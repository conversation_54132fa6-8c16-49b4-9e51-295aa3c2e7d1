{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": false, "es6": false, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "useStaticServer": true, "lazyloadPlaceholderEnable": false, "ignoreUploadUnusedFiles": true, "condition": false, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wx49310464deda8fe4", "projectname": "tihuo", "condition": {"miniprogram": {"list": [{"name": "pages/detail/detail", "pathName": "pages/detail/detail", "query": "", "scene": null}, {"name": "pages/user/user", "pathName": "pages/user/user", "query": "", "scene": null}, {"name": "pages/submit/submit", "pathName": "pages/submit/submit", "query": "member_guid=3cda8ed1-a7b1-825e-19fa-c07a67d15b3d", "scene": 1005}, {"name": "pages/login/login", "pathName": "pages/login/login", "query": "", "scene": null}, {"name": "pages/index/index", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "pages/pay/pay", "pathName": "pages/pay/pay", "query": "", "scene": null}, {"name": "pages/pay/pay_success", "pathName": "pages/pay/pay_success", "query": "", "scene": null}, {"name": "pages/index/index2", "pathName": "pages/index/index2", "query": "", "scene": null}, {"name": "pages/websoket/websoket", "pathName": "pages/websoket/websoket", "query": "", "scene": null}, {"name": "pages/detail/detail_guid", "pathName": "pages/detail/detail", "query": "member_group_guid=1c87d8e8-184d-8615-4293-592f59966704", "scene": null}, {"name": "详情页测试", "pathName": "pages/detail/detail", "query": "member_group_guid=1c87d8e8-184d-8615-4293-592f59966704", "scene": null}]}}, "libVersion": "3.8.12"}