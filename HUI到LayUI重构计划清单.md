# HUI到LayUI重构计划清单

## 项目概述

根据用户需求，需要对member目录下符合以下条件的文件进行LayUI+Vue3重构：

1. 代码行数大于20行且文件名不包含_v1的文件
2. 完全没有`{layout name="layui"/}`的文件
3. 有layui布局但CSS/JS文件没有按照规范的文件
4. 引用了layui但没用Vue3的文件

## 备份目录对比要求

**重要说明**：`D:\Git\yikayi\app\view\member_bak` 目录为备份目录（只读），用于对比重构后功能、样式是否符合预期。

**对比流程**：
- 每次重构一个页面，都要对比备份目录中的原始文件
- 确保重构后的功能完整性与原版一致
- 验证样式效果与用户体验不降级
- 保持业务逻辑和交互行为的一致性

## 第一阶段：文件分析与分类

### 1.1 需要完整分析的目录列表

基于文件列表，需要逐一检查以下目录下的HTML文件：

- [ ] `bind/` - 绑定相关页面
- [ ] `barter/` - 以物换物相关页面  
- [ ] `code/` - 码类相关页面
- [ ] `code_cash/` - 码现金相关页面
- [ ] `coupon_book/` - 优惠券册相关页面
- [ ] `coupon_exchange/` - 优惠券兑换相关页面
- [ ] `coupon_plan/` - 优惠券方案相关页面
- [ ] `device/` - 设备相关页面
- [ ] `email/` - 邮件相关页面
- [ ] `goods/` - 商品相关页面
- [ ] `goods_order/` - 商品订单相关页面
- [ ] `goods_trace/` - 商品溯源相关页面（已完成）
- [ ] `haoping/` - 好评相关页面（已完成）
- [ ] `house/` - 房屋相关页面
- [ ] `kefu/` - 客服相关页面（已完成）
- [ ] `media/` - 媒体相关页面
- [ ] `merchant/` - 商户相关页面
- [ ] `money_exchange/` - 金钱兑换相关页面
- [ ] `pai_mai/` - 拍卖相关页面（部分完成）
- [ ] `passport/` - 通行证相关页面（部分完成）
- [ ] `pay/` - 支付相关页面（已完成）
- [ ] `point_exchange/` - 积分兑换相关页面
- [ ] `questionnaire/` - 问卷相关页面
- [ ] `register/` - 注册相关页面
- [ ] `reward/` - 奖励相关页面
- [ ] `share_activity/` - 分享活动相关页面
- [ ] `store/` - 店铺相关页面
- [ ] `tools/` - 工具相关页面
- [ ] `topic/` - 主题相关页面
- [ ] `transfer/` - 转账相关页面
- [ ] `user/` - 用户相关页面
- [ ] `value_exchange/` - 价值兑换相关页面
- [ ] `wechat/` - 微信相关页面
- [ ] `work_weixin/` - 企业微信相关页面
- [ ] `yikayi/` - 一卡易相关页面
- [ ] `yky/` - YKY相关页面（部分完成）

### 1.2 文件分析标准

对每个HTML文件进行以下检查：

1. **行数统计**：使用工具统计代码行数，筛选出>20行的文件
2. **布局检查**：检查是否包含`{layout name="layui"/}`
3. **CSS/JS规范检查**：检查是否按照以下规范引用文件：
   - CSS: `/static/css/member/目录/文件名.css`
   - JS: `/static/js/member/目录/文件名.js`
4. **Vue3使用检查**：检查是否使用了Vue3框架
5. **LayUI使用检查**：检查是否使用了LayUI组件

### 1.3 分类结果记录

将分析结果按以下类别记录：

#### 类别A：完全没有LayUI布局的文件
- 需要完整重构：从传统HTML转为LayUI+Vue3架构
- 创建对应的CSS和JS文件
- 实现响应式设计和现代化交互

#### 类别B：有LayUI布局但CSS/JS不规范的文件
- 重构CSS/JS文件路径和结构
- 添加Vue3支持
- 优化代码组织

#### 类别C：有LayUI但没有Vue3的文件
- 添加Vue3框架支持
- 重构JavaScript逻辑为Vue3组件
- 保持LayUI样式不变

#### 类别D：需要跳过的文件
- 文件名包含_v1的文件
- 代码行数<=20行的文件
- 布局模板文件（如hui.html, layui.html等）

## 第二阶段：重构策略制定

### 2.1 重构优先级

#### 高优先级（核心业务功能）
1. 支付相关页面（pay/）- 已完成
2. 用户认证页面（passport/）- 部分完成
3. 商品相关页面（goods/, goods_order/）
4. 订单相关页面
5. 用户中心页面（user/）

#### 中优先级（常用功能）
1. 优惠券相关页面（coupon_*/）
2. 积分兑换页面（point_exchange/, value_exchange/）
3. 商户相关页面（merchant/）
4. 工具页面（tools/）

#### 低优先级（辅助功能）
1. 问卷页面（questionnaire/）
2. 媒体页面（media/）
3. 主题页面（topic/）
4. 分享活动页面（share_activity/）

### 2.2 重构技术标准

#### 2.2.1 文件结构标准
```
app/view/member/目录/文件名.html
static/css/member/目录/文件名.css
static/js/member/目录/文件名.js
```

#### 2.2.2 HTML模板标准
```html
{layout name="layui"/}
<!-- 引入页面专用样式文件 -->
<link href="/static/css/member/目录/文件名.css?v=1" rel="stylesheet" type="text/css" />

<!-- 引入Vue.js框架 -->
<script src="//file.yikayi.net/static/js/vue.global.prod.js?v=3.2.47"></script>

<!-- Vue应用容器 -->
<div id="app" v-cloak>
  <!-- 页面内容 -->
</div>

{include file="code/theme" /}
<!-- 引入页面专用JavaScript文件 -->
<script src="/static/js/member/目录/文件名.js?v=1"></script>
```

#### 2.2.3 CSS文件标准
- 使用LayUI样式变量
- 支持主题色变量
- 响应式设计
- 现代化UI组件

#### 2.2.4 JavaScript文件标准
- Vue3 Composition API或Options API
- LayUI组件集成
- 统一的API调用方式（post_layui_member_api_v1）
- 错误处理和加载状态
- 用户体验优化

### 2.3 重构流程标准

#### 2.3.1 单文件重构流程
1. **分析原文件**：理解功能、交互、数据流
2. **设计新架构**：确定Vue组件结构、数据模型
3. **创建HTML模板**：使用LayUI布局和组件
4. **编写CSS样式**：实现响应式和主题支持
5. **开发JavaScript逻辑**：Vue3组件和交互逻辑
6. **测试验证**：功能测试和兼容性测试

#### 2.3.2 批量重构流程
1. **按目录分组**：相同功能模块一起处理
2. **共享组件提取**：识别可复用的组件和样式
3. **统一风格**：保持同一模块内的视觉一致性
4. **渐进式重构**：先完成核心功能，再优化细节

## 第三阶段：执行计划

### 3.1 第一批次重构（高优先级）

#### 3.1.1 用户相关页面
- [ ] `user/index.html` - 用户中心首页
- [ ] `register/index.html` - 用户注册页面
- [ ] `register/success.html` - 注册成功页面

#### 3.1.2 商品相关页面
- [ ] `goods/index.html` - 商品列表页面
- [ ] `goods/detail.html` - 商品详情页面
- [ ] `goods_order/list.html` - 订单列表页面
- [ ] `goods_order/detail.html` - 订单详情页面

#### 3.1.3 绑定相关页面
- [ ] `bind/yky_agent.html` - 代理商绑定页面
- [ ] `bind/yky_user.html` - 用户绑定页面

### 3.2 第二批次重构（中优先级）

#### 3.2.1 优惠券相关页面
- [ ] `coupon_book/index.html` - 优惠券册页面
- [ ] `coupon_plan/add.html` - 添加优惠券方案
- [ ] `coupon_plan/edit.html` - 编辑优惠券方案
- [ ] `coupon_plan/list.html` - 优惠券方案列表
- [ ] `coupon_plan/detail.html` - 优惠券方案详情

#### 3.2.2 兑换相关页面
- [ ] `point_exchange/index.html` - 积分兑换页面
- [ ] `value_exchange/index.html` - 价值兑换页面
- [ ] `money_exchange/index.html` - 金钱兑换页面

#### 3.2.3 商户相关页面
- [ ] `merchant/apply.html` - 商户申请页面
- [ ] `merchant/login.html` - 商户登录页面
- [ ] `merchant/query.html` - 商户查询页面

### 3.3 第三批次重构（低优先级）

#### 3.3.1 工具相关页面
- [ ] `tools/index.html` - 工具首页
- [ ] `tools/express.html` - 快递查询
- [ ] `tools/kuaidi.html` - 快递工具

#### 3.3.2 其他功能页面
- [ ] `questionnaire/epidemic_situation.html` - 疫情问卷
- [ ] `topic/list.html` - 主题列表
- [ ] `topic/detail.html` - 主题详情
- [ ] `share_activity/index.html` - 分享活动

## 第四阶段：质量保证

### 4.1 代码质量标准

#### 4.1.1 代码规范
- [ ] HTML语义化标签使用
- [ ] CSS命名规范（BEM方法论）
- [ ] JavaScript代码规范（ESLint标准）
- [ ] Vue3最佳实践

#### 4.1.2 性能优化
- [ ] 图片懒加载
- [ ] 代码分割和按需加载
- [ ] CSS和JS文件压缩
- [ ] 缓存策略优化

#### 4.1.3 用户体验
- [ ] 加载状态提示
- [ ] 错误处理和提示
- [ ] 响应式设计适配
- [ ] 无障碍访问支持

### 4.2 测试计划

#### 4.2.1 功能测试
- [ ] 核心功能完整性测试
- [ ] 表单验证测试
- [ ] API接口调用测试
- [ ] 数据展示测试

#### 4.2.2 兼容性测试
- [ ] 不同浏览器兼容性
- [ ] 移动端适配测试
- [ ] 不同屏幕尺寸测试
- [ ] 网络环境测试

#### 4.2.3 性能测试
- [ ] 页面加载速度测试
- [ ] 内存使用测试
- [ ] 网络请求优化测试

## 第五阶段：文档和维护

### 5.1 文档更新

#### 5.1.1 技术文档
- [ ] 重构技术规范文档
- [ ] 组件使用说明文档
- [ ] API接口文档更新
- [ ] 部署和配置文档

#### 5.1.2 用户文档
- [ ] 功能使用说明
- [ ] 常见问题解答
- [ ] 操作指南更新

### 5.2 维护计划

#### 5.2.1 监控和优化
- [ ] 性能监控设置
- [ ] 错误日志收集
- [ ] 用户反馈收集
- [ ] 持续优化计划

#### 5.2.2 版本管理
- [ ] 代码版本控制
- [ ] 发布流程规范
- [ ] 回滚策略制定

## 执行时间估算

### 总体时间规划
- **第一阶段（文件分析）**：2-3天
- **第二阶段（策略制定）**：1天
- **第三阶段（执行重构）**：
  - 第一批次：5-7天
  - 第二批次：7-10天
  - 第三批次：3-5天
- **第四阶段（质量保证）**：3-5天
- **第五阶段（文档维护）**：2-3天

**总计预估时间：23-34天**

## 风险评估和应对

### 主要风险
1. **技术风险**：Vue3和LayUI集成复杂度
2. **时间风险**：重构工作量超出预期
3. **质量风险**：重构后功能缺失或bug
4. **兼容性风险**：新架构与现有系统不兼容

### 应对策略
1. **技术风险**：提前进行技术验证和原型开发
2. **时间风险**：分批次执行，优先级管理
3. **质量风险**：完善测试流程，代码审查
4. **兼容性风险**：渐进式重构，保持向后兼容

## 成功标准

### 技术标准
- [ ] 所有目标文件完成LayUI+Vue3重构
- [ ] CSS/JS文件路径符合规范
- [ ] 代码质量达到项目标准
- [ ] 性能指标满足要求

### 业务标准
- [ ] 所有功能正常运行
- [ ] 用户体验得到提升
- [ ] 页面加载速度优化
- [ ] 移动端适配完善

### 维护标准
- [ ] 文档完整准确
- [ ] 代码可维护性强
- [ ] 团队成员能够快速上手
- [ ] 后续扩展性良好

---

**注意事项**：
1. 每完成一个文件的重构，需要进行功能验证
2. 重构过程中保持与原有功能的一致性
3. 遇到复杂问题时及时沟通和调整计划
4. 定期更新进度和风险评估