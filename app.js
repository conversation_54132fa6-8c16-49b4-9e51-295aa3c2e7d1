import websocket from './utils/wechat-websocket.js';
var log, api, utils, http, config, cache, pay, tools;
App({
  api: require('./api.js'),
  config: require("./config.js"),
  utils: require("./utils/utils.js"),
  http: require("./utils/request.js"),
  log: require("./utils/log.js"),
  auth: require("./utils/auth.js"),
  cache: require("./utils/cache.js"),
  pay: require("./utils/pay.js"),
  tools: require("./utils/tools.js"),
  ext_config: wx.getExtConfigSync ? wx.getExtConfigSync() : {},
  onLaunch: function (ops) {
    console.log(ops)
    this.globalData.scene = ops.scene;
    console.log('app - onLaunch');
    // 创建websocket对象
    this.websocket = new websocket({
      // true代表启用心跳检测和断线重连
      url: this.globalData.websocketUrl,
      client_id: 'client_id',
    });
    // 建立连接
    this.linkWebsocket();
    // 监听websocket状态
    this.websocket.onSocketClosed({
      url: this.globalData.websocketUrl,
      success(res) {
        console.log('this.websocket.onSocketClosed success')
        console.log(res)
      },
      fail(err) {
        console.log(err)
      }
    })
    // 监听网络变化
    this.websocket.onNetworkChange({
      url: this.globalData.websocketUrl,
      success(res) {
        console.log('this.websocket.onNetworkChange success')
        console.log(res)
      },
      fail(err) {
        console.log(err)
      }
    })
    // 监听服务器返回
    this.websocket.onReceivedMsg(data => {
      var currentPages = getCurrentPages(),
        that = currentPages[currentPages.length - 1],
        pagePath = that.__route__;
      if (that.websoket_callback) {
        that.websoket_callback(data);
      }
    })
  },
  pageOnShareAppMessage: function (page, options, share_title, share_path, share_img) {
    console.log('onShareAppMessage');
    const promise = new Promise(resolve => {
      let that = this;
      that.request({
        url: that.api.user.info,
        success: function (result) {
          let path = share_path || that.utils.getCurrentPageUrlWithArgsWithOutShareMemberGuid();
          // let user_info = wx.getStorageSync("user_info");
          let user_info = result.data;
          let link = path.indexOf('?') === -1 ? '?' : '&';
          path = path + link + 'share_member_guid=' + user_info.guid;
          // 设置菜单中的转发按钮触发转发事件时的转发内容
          let share_obj = {
            title: share_title || "", // 默认是小程序的名称(可以写slogan等)
            path: path, // 默认是当前页面，必须是以‘/'开头的完整路径
            imageUrl: share_img || '', //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
          };
          // 来自页面内的按钮的转发
          if (options.from == 'button') {
            let eData = options.target.dataset;
            console.log(eData); // shareBtn
          }
          resolve(share_obj);
        }
      });
    })
    return {
      title: '默认分享标题',
      path: '/pages/index/index',
      promise
    }


    console.log(options);
    let path = share_path || this.utils.getCurrentPageUrlWithArgsWithOutShareMemberGuid();
    let user_info = wx.getStorageSync("user_info");
    let link = path.indexOf('?') === -1 ? '?' : '&';
    path = path + link + 'share_member_guid=' + user_info.guid;
    // 设置菜单中的转发按钮触发转发事件时的转发内容
    let share_obj = {
      title: share_title || "", // 默认是小程序的名称(可以写slogan等)
      path: path, // 默认是当前页面，必须是以‘/'开头的完整路径
      imageUrl: share_img || '', //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
    };
    // 来自页面内的按钮的转发
    if (options.from == 'button') {
      let eData = options.target.dataset;
      console.log(eData); // shareBtn
    }
    return share_obj;
  },
  pageOnShareTimeline: function (page, share_title, share_path, share_img) {
    var pages = getCurrentPages() //获取加载的页面
    var currentPage = pages[pages.length - 1] //获取当前页面的对象
    var options = currentPage.options //如果要获取url中所带的参数可以查看options
    console.log(options);
    console.log('OnShareTimeline');
    const promise = new Promise(resolve => {
      let that = this;
      that.request({
        url: that.api.user.info,
        success: function (result) {
          let user_info = result.data;
          var query = ''
          var pages = getCurrentPages() //获取加载的页面
          var currentPage = pages[pages.length - 1] //获取当前页面的对象
          var options = currentPage.options //如果要获取url中所带的参数可以查看options
          for (var key in options) {
            var value = options[key]
            query += key + '=' + value + '&'
          }
          let link = query.indexOf('?') === -1 ? '?' : '&';
          query = query + link + 'share_member_guid=' + user_info.guid;
          // 设置菜单中的转发按钮触发转发事件时的转发内容
          let share_obj = {
            title: share_title || "", // 默认是小程序的名称(可以写slogan等)
            query: query, // 默认是当前页面，必须是以‘/'开头的完整路径
            imageUrl: share_img || '', //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
          };
          console.log(share_obj)
          // 来自页面内的按钮的转发
          if (options.from == 'button') {
            let eData = options.target.dataset;
            console.log(eData); // shareBtn
          }
          resolve(share_obj);
        }
      });
    })
    //拼接url的参数
    var query = ''
    for (var key in options) {
      var value = options[key]
      query += key + '=' + value + '&'
    }
    return {
      title: '默认分享标题',
      query: query,
      promise
    }
  },
  pageOnPullDownRefresh: function (page) {
    console.log('index - onPullDownRefresh');
    wx.showNavigationBarLoading();
    page.onLoad();
    setTimeout(() => {
      wx.hideNavigationBarLoading()
      wx.stopPullDownRefresh()
    }, 3000);
  },
  getConfig: function (page) {
    let that = this;
    that.request({
      url: that.api.config.get,
      success: function (result) {
        page.setData({
          config: result.data
        })
      }
    });
  },
  pageOnLoad: function (page, options) {
    // console.log('--------pageOnLoad----------');
    console.log(options);
    if (options !== undefined && options.share_member_guid !== undefined) {
      wx.setStorageSync('share_member_guid', options.share_member_guid);
    }
    if (options !== undefined && options.share_user_guid !== undefined) {
      wx.setStorageSync('share_user_guid', options.share_user_guid);
    }
    if (options !== undefined && options.show_tab_bar == 1) {
      page.setData({
        show_tab_bar: 1
      })
      this.editTabBar();
    }
  },
  pageOnShow: function (page) {
    console.log('--------pageOnShow----------');
  },
  onShow: function (options) {
    console.log('app - onShow')
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    console.log(extConfig);
    // this.globalData.tabbar = this.config.tabbar;
    this.get_toolbar();
    this.checkUpdate();
    //--------------------this.getSystem();
    // 程序从后台到前台的操作--建立连接
    this.linkWebsocket();
  },

  get_user_info_await() {
    return new Promise((resolve, reject) => {
      let that = this;
      let cache_key = 'user_info';
      let result = that.cache.get(cache_key);
      // if (result) {
      //   resolve(result)
      //   return;
      // }
      that.request({
        url: that.api.user.info,
        success: function (result) {
          that.cache.set(cache_key, result.data, 3600 * 24);
          console.log('结束啦 async_get_user_info');
          resolve(result.data)
        },
        fail: function (result) {
          reject(result)
        }
      });
    })
  },
  async async_get_user_info() {
    console.log('开始异步获取 async_get_user_info');
    return await this.get_user_info_await();
  },
  get_user_info: function (callback) {
    let that = this;
    that.request({
      url: that.api.user.index,
      success: function (result) {
        wx.setStorageSync('user_info', result.data.user_info);
        if (callback) {
          callback;
        }
      }
    });
  },
  get_toolbar: function () {
    let that = this;
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    that.request({
      url: that.api.user.get_weapp_toolbar,
      loading: false,
      data: {
        bid: extConfig.bid,
        appid: wx.getAccountInfoSync().miniProgram.appId,
        mini_program_ext_version: extConfig.version,
        mini_program_env_version: wx.getAccountInfoSync().miniProgram.envVersion,
        mini_program_version: wx.getAccountInfoSync().miniProgram.version,
        from: 'weapp'
      },
      success: function (result) {
        console.log(result);
        that.globalData.tabbar = result.data;
        that.editTabBar();
      },
      fail: function (result) {
        console.log('fail')
        that.globalData.tabbar = that.config.tabbar;
      }
    });
  },
  pageOnUnload: function (page) {
    console.log('pageOnUnload')
  },
  pageOnHide: function (page) {
    console.log('pageOnHide')
  },

  onHide: function () {
    console.log('app - onHide')
    // 程序后台后的操作--关闭websocket连接
    this.websocket.closeWebSocket();
  },
  onError: function (msg) {
    console.log('app - onError')
    console.log(msg)
  },
  onPageNotFound(res) {
    console.log(res.query)
    console.log('app - onPageNotFound');
    let urlWithArgs = '/pages/index/index?';
    for (var key in res.query) {
      var value = res.query[key]
      urlWithArgs += key + '=' + value + '&'
    }
    urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
    wx.reLaunch({
      url: urlWithArgs
    })
  },
  linkWebsocket() {
    // 建立连接
    this.websocket.initWebSocket({
      success(res) {
        console.log(res)
      },
      fail(err) {
        console.log(err)
      }
    })
  },
  getWebSocket() {
    // 向其他页面暴露当前websocket连接
    return this.websocket;
  },
  to_web: function (url) {
    let access_token = wx.getStorageSync("access_token");
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    let protocol = wx.getAccountInfoSync().miniProgram.envVersion == 'develop' ? 'http' : 'https';
    let encode_url = protocol + '://' + extConfig.domain + '/index/plugins/redirect_to_url?url=' + encodeURIComponent(url) + '&access_token=' + access_token;
    console.log(encode_url);
    wx.navigateTo({
      url: '/pages/web/web?url=' + encodeURIComponent(encode_url)
    })
  },
  editTabBar: function () {
    var tabbar = this.globalData.tabbar,
      currentPages = getCurrentPages(),
      that = currentPages[currentPages.length - 1],
      pagePath = that.__route__;
    (pagePath.indexOf('/') != 0) && (pagePath = '/' + pagePath);
    for (var i in tabbar.list) {
      tabbar.list[i].selected = false;
      let tabbar_path = tabbar.list[i].pagePath;
      if (tabbar_path.indexOf('?') != 0) {
        tabbar_path = tabbar_path.split('?')[0];
      }
      (tabbar_path == pagePath) && (tabbar.list[i].selected = true);
    }
    that.setData({
      tabbar: tabbar
    });
  },
  // 检查更新
  checkUpdate() {
    const updateManager = wx.getUpdateManager();
    updateManager.onCheckForUpdate(function (res) {
      if (res.hasUpdate) {
        updateManager.onUpdateReady(function () {
          wx.showModal({
            title: '更新提示',
            content: '新版本已经准备好，是否马上重启小程序？',
            showCancel: true,
            success: function (response) {
              if (response.confirm) {
                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启小程序
                updateManager.applyUpdate();
              }
            }
          })
        })
        updateManager.onUpdateFailed(function () {
          //当新版本下载失败，会进行回调
          wx.showModal({
            title: '更新提示',
            content: '检查到有新版本，但下载失败，请稍后尝试',
            showCancel: false,
          })
        })
      }
    })
  },
  request(object) {
    return this.http.request(object);
  },
  globalData: {
    scene: 1001, // 进入小程序的场景值 默认微信发现栏入
    tabbar: {},
    websocketUrl: 'wss://www.yikayi.net/wss'
  }
})