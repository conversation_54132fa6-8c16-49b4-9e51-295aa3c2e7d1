 function query(bill_no, success_callback, fail_callback) {
   var app = getApp();
   var max_query_times = 30;
   var query_times = 0;
   var lock_query_pay = false;
   var i = setInterval(function () {
     if (query_times > 0) {
       wx.showLoading({
         title: '查单中(' + query_times + '秒)',
       })
     }
     query_times++;
     if (query_times > max_query_times) {
       wx.hideLoading();
       clearInterval(i);
       wx.showModal({
         title: "提示",
         content: '订单查询超时',
         showCancel: false,
         success: function (res) {
           if (res.confirm) {
             if (typeof fail_callback == "function") {
               fail_callback(); //失败后回调函数
             }
           }
         }
       });
     } else {
       //支付中继续查单
       if (lock_query_pay == true) {
         console.log('lock_query_pay 被锁定 直接不查询');
         return;
       }
       lock_query_pay = true;
       console.log('lock_query_pay 锁定 ');
       app.request({
         url: app.api.pay.query,
         loading: false,
         data: {
           bill_no: bill_no
         },
         complete: function (result) {
           console.log('lock_query_pay 解锁 ');
           lock_query_pay = false;
         },
         success: function (result) {
           if (result.data.order_data.status !== 0) {
             //状态不等于0 停止查单
             clearInterval(i);
             wx.hideLoading();
             if (typeof success_callback == "function") {
               success_callback(result); //成功后回调函数
             }
           }
         },
         fail: function (result) {
           clearInterval(i);
           wx.hideLoading();
           wx.showModal({
             title: "提示",
             content: '订单查询失败',
             showCancel: false,
             success: function (res) {
               if (res.confirm) {
                 if (typeof fail_callback == "function") {
                   fail_callback(); //失败后回调函数
                 }
               }
             }
           });
         }
       });
     }
   }, 1000)
 }
 module.exports = {
   query,
 }