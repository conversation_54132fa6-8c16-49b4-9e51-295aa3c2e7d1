function parse_qrcode(page, text, success_callback, failed_callback) {
  let that = this;
  var app = getApp();
  app.request({
    url: app.api.code.parse_qrcode, data: {
      text: text,
    }, success: function (res) {
      page.setData(res.data);
      if (success_callback && typeof success_callback == "function") {
        console.log('success_callback');
        success_callback(res)
      }
    }, fail: function (res) {
      if (failed_callback && typeof failed_callback == "function") {
        console.log('failed_callback');
        failed_callback(res)
      }
    }
  });
}


/**
 * Promise版本的二维码解析
 * @param {Object} page - 页面对象（可选，如果传入会自动设置页面数据）
 * @param {string} text - 二维码文本
 * @return {Promise} 返回Promise对象
 */
function parse_qrcode_promise(page, text) {
  var app = getApp();
  return new Promise((resolve, reject) => {
    app.request({
      url: app.api.code.parse_qrcode, data: {
        text: text,
      }, success: function (res) {
        // 如果传入了page对象，自动设置页面数据
        page.setData(res.data);
        resolve(res.data);
      }, fail: function (res) {
        reject(res);
      }
    });
  });
}


module.exports = {
  parse_qrcode, parse_qrcode_promise
}