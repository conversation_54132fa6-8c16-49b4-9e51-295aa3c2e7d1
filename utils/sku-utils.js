/**
 * SKU工具类
 * 提供SKU选择相关的通用工具函数
 */

const SkuUtils = {
  /**
   * 获取商品当前价格
   * @param {Object} product 商品对象
   * @param {Object} sku SKU对象
   * @returns {Number} 价格
   */
  getPrice(product, sku) {
    if (sku && sku.price !== undefined) {
      return parseFloat(sku.price);
    }
    return parseFloat(product.price) || 0;
  },

  /**
   * 获取商品当前库存
   * @param {Object} product 商品对象
   * @param {Object} sku SKU对象
   * @returns {Number} 库存数量，-1表示无限库存
   */
  getStock(product, sku) {
    // 如果不限制库存，返回-1表示无限库存
    if (product.stock_mode != 1) {
      return -1;
    }

    if (sku && sku.stock !== undefined) {
      return sku.stock;
    }
    return product.stock || 0;
  },

  /**
   * 获取商品规格文本
   * @param {Object} product 商品对象
   * @param {Array} selectedAttrs 选中的属性数组
   * @returns {String} 规格文本
   */
  getSpecsText(product, selectedAttrs) {
    if (product.is_attribute != 1) {
      return product.specs || "";
    }
    
    if (!selectedAttrs || selectedAttrs.length === 0) {
      return "";
    }

    return selectedAttrs.map(attr => attr.attr_name).join(" ");
  },

  /**
   * 获取商品当前图片
   * @param {Object} product 商品对象
   * @param {Object} sku SKU对象
   * @returns {String} 图片URL
   */
  getImage(product, sku) {
    if (sku && sku.picture) {
      return sku.picture;
    }
    return product.pic;
  },

  /**
   * 检查商品是否为多规格商品
   * @param {Object} product 商品对象
   * @returns {Boolean} 是否为多规格
   */
  isMultiSku(product) {
    return product.is_attribute === 1 && 
           product.attr_group_list && 
           product.attr_group_list.length > 0;
  },

  /**
   * 检查SKU选择是否完整
   * @param {Object} product 商品对象
   * @param {Object} selectedAttrs 选中的属性映射
   * @returns {Boolean} 是否选择完整
   */
  isSkuComplete(product, selectedAttrs) {
    if (!this.isMultiSku(product)) {
      return true; // 单规格商品直接完整
    }

    if (!selectedAttrs) {
      return false;
    }

    const selectedCount = Object.keys(selectedAttrs).length;
    return selectedCount === product.attr_group_list.length;
  },

  /**
   * 格式化选中属性为数组格式
   * @param {Object} product 商品对象
   * @param {Object} selectedAttrs 选中的属性映射 {groupId: attrId}
   * @returns {Array} 格式化后的属性数组
   */
  formatSelectedAttrs(product, selectedAttrs) {
    if (!this.isMultiSku(product) || !selectedAttrs) {
      return [];
    }

    let formattedAttrs = [];
    
    for (const group of product.attr_group_list) {
      const selectedAttrId = selectedAttrs[group.attr_group_id];
      if (selectedAttrId) {
        const selectedAttr = group.attr_list.find(attr => attr.attr_id === selectedAttrId);
        if (selectedAttr) {
          formattedAttrs.push({
            attr_group_id: group.attr_group_id,
            attr_group_name: group.attr_group_name,
            attr_id: selectedAttr.attr_id,
            attr_name: selectedAttr.attr_name
          });
        }
      }
    }

    return formattedAttrs;
  },

  /**
   * 解析属性数组为映射格式
   * @param {Array} attrArray 属性数组
   * @returns {Object} 属性映射 {groupId: attrId}
   */
  parseAttrsToMap(attrArray) {
    if (!attrArray || !Array.isArray(attrArray)) {
      return {};
    }

    let attrMap = {};
    attrArray.forEach(attr => {
      if (attr.attr_group_id && attr.attr_id) {
        attrMap[attr.attr_group_id] = attr.attr_id;
      }
    });

    return attrMap;
  },

  /**
   * 生成购物车项唯一ID
   * @param {String} goodsGuid 商品GUID
   * @param {Array} selectedAttrs 选中的属性数组
   * @returns {String} 唯一ID
   */
  generateCartItemId(goodsGuid, selectedAttrs) {
    if (!selectedAttrs || selectedAttrs.length === 0) {
      return goodsGuid; // 无SKU时直接使用商品GUID
    }

    // 将选中的属性ID排序后拼接，确保相同SKU组合生成相同ID
    const attrIds = selectedAttrs.map(attr => attr.attr_id).sort().join('_');
    return `${goodsGuid}_${attrIds}`;
  },

  /**
   * 计算最大可选择数量
   * @param {Object} product 商品对象
   * @param {Number} currentStock 当前库存
   * @returns {Number} 最大可选择数量
   */
  getMaxSelectableQuantity(product, currentStock) {
    let maxByConfig = product.max_choose_num || 999;

    // 如果不限制库存，只受配置限制
    if (product.stock_mode != 1) {
      return maxByConfig;
    }

    // 限制库存时，取配置和库存的最小值
    let maxByStock = currentStock === -1 ? 999 : currentStock;
    return Math.min(maxByConfig, maxByStock);
  },

  /**
   * 验证选择的数量是否合法
   * @param {Object} product 商品对象
   * @param {Number} quantity 选择的数量
   * @param {Number} currentStock 当前库存
   * @returns {Object} 验证结果 {valid: Boolean, message: String, adjustedQuantity: Number}
   */
  validateQuantity(product, quantity, currentStock) {
    const minQuantity = Math.max(product.min_choose_num || 1, 1);
    const maxQuantity = this.getMaxSelectableQuantity(product, currentStock);

    if (quantity < minQuantity) {
      return {
        valid: false,
        message: `最少选择${minQuantity}件`,
        adjustedQuantity: minQuantity
      };
    }

    if (quantity > maxQuantity) {
      let message = '数量超出限制';
      if (product.stock_mode == 1 && currentStock !== -1 && currentStock < (product.max_choose_num || 999)) {
        message = `库存不足，最多选择${maxQuantity}件`;
      } else {
        message = `最多选择${maxQuantity}件`;
      }

      return {
        valid: false,
        message: message,
        adjustedQuantity: maxQuantity
      };
    }

    return {
      valid: true,
      message: '',
      adjustedQuantity: quantity
    };
  },

  /**
   * 深度克隆对象（简单实现）
   * @param {Object} obj 要克隆的对象
   * @returns {Object} 克隆后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  /**
   * 格式化价格显示
   * @param {Number} price 价格
   * @param {Number} precision 小数位数，默认2位
   * @returns {String} 格式化后的价格
   */
  formatPrice(price, precision = 2) {
    const numPrice = parseFloat(price) || 0;
    return numPrice.toFixed(precision);
  },

  /**
   * 检查两个属性数组是否相等
   * @param {Array} attrs1 属性数组1
   * @param {Array} attrs2 属性数组2
   * @returns {Boolean} 是否相等
   */
  isAttrsEqual(attrs1, attrs2) {
    if (!attrs1 && !attrs2) return true;
    if (!attrs1 || !attrs2) return false;
    if (attrs1.length !== attrs2.length) return false;

    // 按attr_id排序后比较
    const sorted1 = attrs1.slice().sort((a, b) => a.attr_id.localeCompare(b.attr_id));
    const sorted2 = attrs2.slice().sort((a, b) => a.attr_id.localeCompare(b.attr_id));

    for (let i = 0; i < sorted1.length; i++) {
      if (sorted1[i].attr_id !== sorted2[i].attr_id) {
        return false;
      }
    }

    return true;
  }
};

module.exports = SkuUtils;
