function formatTime(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()

  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds()


  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

function formatData(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()

  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds()


  return [year, month, day].map(formatNumber).join('-');
}

function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

function objectToUrlParams(obj) {
  var str = "";
  for (var key in obj) {
    str += "&" + key + "=" + obj[key];
  }
  return str.substr(1);
}
/*获取当前页url*/
function getCurrentPageUrl() {
  var pages = getCurrentPages() //获取加载的页面
  var currentPage = pages[pages.length - 1] //获取当前页面的对象
  var url = currentPage.route //当前页面url
  return url
}
/*获取当前页带参数的url*/
function getCurrentPageUrlWithArgsWithOutShareMemberGuid() {
  var pages = getCurrentPages() //获取加载的页面
  var currentPage = pages[pages.length - 1] //获取当前页面的对象
  var url = currentPage.route //当前页面url
  var options = currentPage.options //如果要获取url中所带的参数可以查看options
  //拼接url的参数
  var urlWithArgs = url + '?'
  for (var key in options) {
    var value = options[key]
    if (key != 'share_member_guid') {
      urlWithArgs += key + '=' + value + '&'
    }
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
  return urlWithArgs
}
/*获取当前页带参数的url*/
function getCurrentPageUrlWithArgs() {
  var pages = getCurrentPages() //获取加载的页面
  var currentPage = pages[pages.length - 1] //获取当前页面的对象
  var url = currentPage.route //当前页面url
  var options = currentPage.options //如果要获取url中所带的参数可以查看options
  //拼接url的参数
  var urlWithArgs = url + '?'
  for (var key in options) {
    var value = options[key]
    urlWithArgs += key + '=' + value + '&'
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
  return urlWithArgs
}

function saveImageToPhotosAlbum(image_src) {
  if (!wx.saveImageToPhotosAlbum) {
    // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
    wx.showModal({
      title: '提示',
      content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
      showCancel: false,
    });
    return;
  }

  wx.showLoading({
    title: "正在保存图片",
    mask: false,
  });

  wx.downloadFile({
    url: image_src,
    success: function (e) {
      wx.showLoading({
        title: "正在保存图片",
        mask: false,
      });
      wx.saveImageToPhotosAlbum({
        filePath: e.tempFilePath,
        success: function () {
          wx.showModal({
            title: '提示',
            content: '图片保存成功',
            showCancel: false,
          });
        },
        fail: function (e) {
          wx.showModal({
            title: '图片保存失败',
            content: e.errMsg,
            showCancel: false,
          });
        },
        complete: function (e) {
          console.log(e);
          wx.hideLoading();
        }
      });
    },
    fail: function (e) {
      wx.showModal({
        title: '图片下载失败',
        content: e.errMsg + ";" + image_src,
        showCancel: false,
      });
    },
    complete: function (e) {
      console.log(e);
      wx.hideLoading();
    }
  });
}
const chars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

const random = function generateMixed(n) {
  var res = "";
  for (var i = 0; i < n; i++) {
    var id = Math.ceil(Math.random() * 35);
    res += chars[id];
  }
  return res;
}

module.exports = {
  formatTime: formatTime,
  objectToUrlParams: objectToUrlParams,
  formatData: formatData,
  getCurrentPageUrl: getCurrentPageUrl,
  getCurrentPageUrlWithArgsWithOutShareMemberGuid: getCurrentPageUrlWithArgsWithOutShareMemberGuid,
  getCurrentPageUrlWithArgs: getCurrentPageUrlWithArgs,
  random: random,
  saveImageToPhotosAlbum: saveImageToPhotosAlbum
};