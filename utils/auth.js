 /**
  * 使用promise封装用户信息接口
  */
 function asyncGetUserProfile() {
   console.log('asyncGetUserProfile');
   return new Promise((resolve, reject) => {
     wx.getUserProfile({
       desc: '用户登录', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
       success: (res) => {
         resolve(res)
       },
       fail: (err) => {
         reject(err)
       }
     })
   })
 }

 /**
  * 使用promise封装wx.login接口
  */
 function asyncGetLogin() {
   console.log('asyncGetLogin');
   return new Promise((resolve, reject) => {
     wx.login({
       success(res) {
         resolve(res)
       },
       fail: (err) => {
         reject(err)
       }
     })
   })
 }

 function getUserProfile(success_callback, failed_callback) {
   let that = this;
   let asyncGetLogin = that.asyncGetLogin();
   let asyncGetUserProfile = that.asyncGetUserProfile();
   //使用promise.all()平级调用
   Promise.all([asyncGetLogin, asyncGetUserProfile])
     .then((res) => {
       res[1].code = res[0].code;
       if (success_callback && typeof success_callback == "function") {
         console.log('success_callback')
         let result = res[1];
         result.userInfo = JSON.parse(res[1].rawData);
         success_callback(result)
       }
     }).catch((res) => {
       console.log(res)
       if (failed_callback && typeof failed_callback == "function") {
         console.log('success_callback')
         failed_callback(res[1])
       }
     })
 }

 function getPhoneNumber(e, success_callback, deny_callback, failed_callback) {
   let that = this;
   var app = getApp();
   if (e.detail.errMsg == 'getPhoneNumber:fail user deny') {
     //用户点击拒绝
     wx.showToast({
       title: '您取消了授权',
       duration: 1000
     })
     if (deny_callback && typeof deny_callback == "function") {
       deny_callback(e.detail)
     }
     return;
   }
   app.request({
     url: app.api.passport.get_phone_number,
     data: {
       iv: e.detail.iv,
       encrypted_data: encodeURIComponent(e.detail.encryptedData),
       get_phone_number_code: e.detail.code
     },
     success: function (res) {
       if (success_callback && typeof success_callback == "function") {
         console.log('success_callback');
         success_callback(res)
       }
     },
     fail: function (res) {
       if (failed_callback && typeof failed_callback == "function") {
         console.log('failed_callback');
         failed_callback(res)
       }
     }
   });
 }
 module.exports = {
   asyncGetLogin,
   asyncGetUserProfile,
   getUserProfile,
   getPhoneNumber
 }