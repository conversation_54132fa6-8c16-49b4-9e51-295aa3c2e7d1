export default class websocket {
  constructor({
    url,
    client_id,
  }) {
    // 是否调试
    this._debug = true;
    // 是否连接
    this._isLogin = false;
    // 当前网络状态
    this._netWork = true;
    // 是否正在关闭
    this._isClosing = false;
    // 心跳检测频率
    this._timeout = 8000;
    this._timeoutObj = null;
    // 当前重连次数 
    this._connectNum = 0;
    // 心跳检测开关，true为启用，false为关闭
    this._heartCheck = true;
    //客户端ID
    this._client_id = client_id;
    //websocket 链接URL
    this._url = url;
    //断线重连开关，true为启用，false为关闭
    this._isReconnection = true;
    this._onSocketOpened();
  }
  // 心跳重置
  _reset() {
    clearTimeout(this._timeoutObj);
    return this;
  }
  // 心跳开始
  _start() {
    let that = this;
    this._timeoutObj = setInterval(() => {
      that.sendSocketMessage({
        data: {
          'type': 'ping'
        },
        success(res) {
          //console.log("发送ping成功");
        }
      });
    }, this._timeout);
  }
  // 监听websocket连接关闭
  onSocketClosed(options) {
    wx.onSocketClose(err => {
      console.log('当前websocket连接已关闭,错误信息为:' + JSON.stringify(err));
      // 停止心跳连接
      if (this._heartCheck) {
        this._reset();
      }
      // 关闭已登录开关
      this._isLogin = false;
      if (this._isReconnection) {
        this._reConnect(options)
      }
    })
  }
  // 检测网络变化
  onNetworkChange(options) {
    wx.onNetworkStatusChange(res => {
      console.log('当前网络状态:' + res.isConnected);
      if (!this._netWork) {
        this._isLogin = false;
        // 进行重连
        if (this._isReconnection) {
          this._reConnect(options)
        }
      }
    })
  }
  _onSocketOpened() {
    wx.onSocketOpen(res => {
      console.log('websocket链接已经成功');
      // 打开已登录开关
      this._isLogin = true;
      // 发送心跳
      if (this._heartCheck) {
        this._reset()._start();
      }
      // 发送登录信息
      this.sendSocketMessage({
        data: {
          "type": 'login',
          "client_id": this._client_id,
        }
      })
      // 打开网络开关
      this._netWork = true;
    })
  }
  // 接收服务器返回的消息
  onReceivedMsg(callBack) {
    let that = this;
    wx.onSocketMessage(msg => {
      var data = JSON.parse(msg.data); //由JSON字符串转换为JSON对象
      console.log(data);
      switch (data.type) {
        case 'ping':
          //console.log('收到ping');
          that.sendSocketMessage({
            data: {
              'type': 'pong'
            },
            success(res) {
              //console.log("回应ping成功");
            }
          })
          break;
        case 'pong':
          //console.log('收到pong');
          break;
        default:
          //console.log('其他类型:' + data.type);
          if (callBack) {
            callBack(data)
          }
      }
    })
  }
  // 建立websocket连接
  initWebSocket(options) {
    let that = this;
    if (this._debug) {
      return;
    }
    if (that._isLogin) {
      console.log("当前已经处于登录状态");
      return false;
    }
    // 检查网络
    wx.getNetworkType({
      success(result) {
        if (result.networkType != 'none') {
          // 开始建立连接
          wx.connectSocket({
            url: that._url,
            success(res) {
              if (options.success) {
                that._isLogin = true;
                options.success(res)
              }
            },
            fail(err) {
              if (options.fail) {
                options.fail(res)
              }
            }
          })
        } else {
          console.log('网络已断开');
          that._netWork = false;
          // 网络断开后显示model
          wx.showModal({
            title: '网络错误',
            content: '请重新打开网络',
            showCancel: false
          })
        }
      }
    })
  }
  // 发送websocket消息
  sendSocketMessage(options) {
    let that = this;
    if (!that._isLogin) {
      console.log('已经是关闭状态--无法发送消息');
      return false;
    }
    options.data.time = that.now_time();
    wx.sendSocketMessage({
      data: JSON.stringify(options.data),
      success(res) {
        if (options.success) {
          options.success(res)
        }
      },
      fail(err) {
        that._reset()
        console.log(err);
        if (options.fail) {
          options.fail(err)
        }
      }
    })
  }
  // 重连方法，会根据时间频率越来越慢
  _reConnect(options) {
    let timer, that = this;
    if (this._connectNum < 20) {
      timer = setTimeout(() => {
        this.initWebSocket(options)
      }, 3000)
      this._connectNum += 1;
    } else if (this._connectNum < 50) {
      timer = setTimeout(() => {
        this.initWebSocket(options)
      }, 10000)
      this._connectNum += 1;
    } else {
      timer = setTimeout(() => {
        this.initWebSocket(options)
      }, 450000)
      this._connectNum += 1;
    }
  }
  // 关闭websocket连接
  closeWebSocket() {
    if (this._isClosing) return;
    this._isClosing = true;
    if (!this._isLogin) {
      console.log('已经处于是关闭状态');
      this._isClosing = false;
      return true;
    }
    wx.closeSocket();
    this._isClosing = false;
    this._isLogin = false;
  }
  now_time() {
    let fmt = 'yyyy-MM-dd hh:mm:ss.S';
    var _date = new Date();
    var o = {
      "M+": _date.getMonth() + 1, //月份 
      "d+": _date.getDate(), //日 
      "h+": _date.getHours(), //小时 
      "m+": _date.getMinutes(), //分 
      "s+": _date.getSeconds(), //秒 
      "q+": Math.floor((_date.getMonth() + 3) / 3), //季度 
      "S+": _date.getMilliseconds() //毫秒 
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (_date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      }
    }
    return fmt;
  }
}