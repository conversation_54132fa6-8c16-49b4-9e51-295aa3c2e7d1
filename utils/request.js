var api = require('../api.js');
var config = require("../config.js");
var log = require("./log.js");
var refreshSubscribers = [];
var isRefreshToken = false;
var is_lock = false;
var lock_location_url = false;
var lock_location_path = false;
// 登录获取微信code
function getWxCode() {
  return new Promise((resolve, reject) => {
    wx.login({
      success(res) {
        resolve(res.code)
      }
    })
  })
}
// http状态码异常时候弹出提示框
function httpClipboardData(res, title) {
  wx.showModal({
    title: '系统提示' + "(" + res.statusCode + ")",
    content: title + ",请将错误内容复制发送给我们，以便进行问题追踪。",
    cancelText: "关闭",
    confirmText: "复制",
    success: function (e) {
      e.confirm && wx.setClipboardData({
        data: JSON.stringify({
          data: res.data
        })
      })
    }
  });
}
// 监听接口返回状态
function checkStatus(response, resolve) {
  try {
    log.info(response.data);
  } catch (e) {
    console.log(e)
  }
  switch (response.statusCode) {
    case 200:
      if (response.data.code == undefined) {
        //判断后台数据返回是否包含code字段
        this.httpClipboardData(response, '后台返回格式不正确');
        return false;
      }
      resolve(response.data)
      return true;
      break;
    case 404:
      this.httpClipboardData(response, '您访问的接口不存在');
      return false;
      break;
    case 500:
      this.httpClipboardData(response, '服务器开小差了');
      return false;
      break;
    default:
      this.httpClipboardData(response, '未知的HTTP状态码');
      return false;
  }
}
// 刷新access_token
async function refreshToken() {
  let that = this;
  let code = await that.getWxCode();
  let protocol = wx.getAccountInfoSync().miniProgram.envVersion == 'develop' ? 'http' : 'https';

  wx.showLoading({
    title: '加载中!',
  })
  return new Promise((resolve, reject) => {
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    wx.request({
      method: "POST",
      header: {
        'content-type': 'application/json',
        'client-type': 'weapp_mini_program',
        'mini-program-appid': wx.getAccountInfoSync().miniProgram.appId,
        'mini-program-ext-version': extConfig.version,
        'mini-program-env-version': wx.getAccountInfoSync().miniProgram.envVersion,
        'mini-program-version': wx.getAccountInfoSync().miniProgram.version,
      },
      url: protocol + '://' + extConfig.domain + config.path + api.passport.login,
      data: {
        code: code,
        bid: extConfig.bid,
        mini_program_ext_version: extConfig.version,
        appid: wx.getAccountInfoSync().miniProgram.appId,
        mini_program_env_version: wx.getAccountInfoSync().miniProgram.envVersion,
        mini_program_version: wx.getAccountInfoSync().miniProgram.version,
        share_member_guid: wx.getStorageSync('share_member_guid'),
        share_user_guid: wx.getStorageSync('share_user_guid'),
        drive: 'weapp',
        driver: 'weapp'
      },
      dataType: "json",
      success: function (response) {
        wx.hideLoading();
        if (that.checkStatus(response, resolve) === false) {
          //非200状态码或者返回格式不合法,不做处理
          return;
        }
        if (response.data.code === 0) {
          wx.setStorageSync("access_token", response.data.data.access_token);
          wx.setStorageSync("access_token_expires_time", response.data.data.expires_time);
          wx.setStorageSync("user_info", response.data.data.user_info);
        } else {
          //access_token获取失败
          wx.showModal({
            title: '系统提示',
            content: response.data.msg,
            showCancel: false,
          });
        }
      },
      fail: function (response) {
        wx.hideLoading();
        wx.showModal({
          title: 'access_token获取失败',
          content: response.errMsg,
          showCancel: false,
        });
      },
      complete: function (response) {
        wx.hideNavigationBarLoading();
        wx.stopPullDownRefresh(); //刷新完成后停止下拉刷新动效
      }
    })
  })
}
// 发送请求
function requestModel(object) {
  let that = this;
  let is_pagination = false;
  if ((typeof (object.pagination) !== 'undefined')) {
    console.log('是分页请求');
    var _page = object.page;
    if (typeof (object.data) == 'undefined') {
      object.data = {};
    }
    if (_page.data.has_more === false && object.data.length > 0) {
      console.log('没有更多数据');
      return;
    }
    is_pagination = true; //标记当前是分页请求
    if (is_lock && is_pagination) {
      console.log('正在锁定中');
      return;
    }
    _page.setData({
      has_more: true,
      loading_more: true
    });


    // if (typeof (object.data.page) == 'undefined') {
    //   //请求data中没有指定 page自动获取页面中page赋值
    //   console.log('请求data中没有指定 page自动获取页面中page赋值');
    //   // object.data.page = _page.data[object.page_array_name].length + 1;
    // } else {
    //   object.data.page++;
    // }
    if (typeof (object.data.page) == 'undefined') {

      if (_page.data[object.pagination].length == 0) {
        //如果数组是空的 则从第一页开始获取数据
        // console.log('如果数组是空的 则从第一页开始获取数据');
        console.log(_page.data[object.pagination]);
        object.data.page = 1;
      } else {
        object.data.page = (_page.data.page ? _page.data.page : 0) + 1;
        // console.log(_page.data.page);
        // console.log(object.data.page);
      }
    }


    object.loading = false; //分页请求无需loading,体验更佳
  }

  if (object.loading !== false) {
    wx.showLoading({
      title: '加载中',
    })
  }
  return new Promise((resolve, reject) => {
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    let protocol = wx.getAccountInfoSync().miniProgram.envVersion == 'develop' ? 'http' : 'https';

    if (is_pagination) {
      is_lock = true; //只有分页请求需要锁定,同时只能请求一个分页数据
    }
    wx.request({
      url: protocol + '://' + extConfig.domain + config.path + object.url + '?access_token=' + wx.getStorageSync("access_token"),
      header: object.header || {
        'content-type': 'application/json',
        'client-type': 'weapp_mini_program',
        'mini-program-appid': wx.getAccountInfoSync().miniProgram.appId,
        'mini-program-ext-version': extConfig.version,
        'mini-program-env-version': wx.getAccountInfoSync().miniProgram.envVersion,
        'mini-program-version': wx.getAccountInfoSync().miniProgram.version,
      },
      data: object.data || {},
      method: object.method || "POST",
      dataType: object.dataType || "json",
      success: function (response) {
        if (object.loading !== false) {
          wx.hideLoading();
        }
        if (response.header.LocationPath !== undefined) {
          console.log(response.header.LocationPath);
          var app = getApp();
          if (lock_location_path == false) {
            lock_location_path = true;
            wx.navigateTo({
              url: response.header.LocationPath
            })
            lock_location_path = false;
          }
          return;
        }
        if (response.header.LocationUrl !== undefined) {
          console.log(response.header.LocationUrl);
          var app = getApp();
          if (lock_location_url == false) {
            lock_location_url = true;
            app.to_web(response.header.LocationUrl)
            lock_location_url = false;
          }
          return;
        }

        if (that.checkStatus(response, resolve) === false) {
          //非200状态码或者返回格式不合法,不做处理
          return;
        }
        switch (response.data.code) {
          case 0: //业务成功处理逻辑
            if (is_pagination) {
              //页面自增
              let currentPage = object.data.page; //response.data.current_page
              //动态设置list分页数组
              // if (typeof (object.page_array_name) !== 'undefined') {
              //   //优先取 page_array_name 属性
              //   _page.setData({
              //     [object.page_array_name + '[' + (currentPage - 1) + ']']: response.data.data.data
              //   });
              // }
              let all_data = _page.data[object.pagination].concat(response.data.data.data);
              // console.log(all_data);
              _page.setData({
                // has_more: false,
                loading_more: false,
                page: response.data.data.current_page,
                [object.pagination]: all_data
              });
              if (response.data.data.current_page == response.data.data.last_page) {
                _page.setData({
                  has_more: false
                });
                //说明没有更多数据了
              }
            }
            if (object.success) {
              object.success(response.data);
            }
            break;
          case -1: //业务处理失败逻辑 
            wx.showModal({
              title: '提示',
              content: response.data.msg,
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  if (object.fail) {
                    object.fail(response.data);
                  }
                }
              }
            })
            break;
          case -2: //access_token过期,清空缓存并弹出提示,引导退出小程序重新访问
            wx.removeStorageSync("access_token");
            wx.removeStorageSync("access_token_expires_time");
            wx.showModal({
              title: '身份失效提醒',
              content: 'ACCESS_TOKEN已过期,请退出小程序重试',
              showCancel: false,
            })
            break;
          default: //其他错误码
            wx.showModal({
              title: '系统提示(' + response.data.code + ')',
              content: response.data.msg,
              showCancel: false,
            })
            if (object.fail) {
              object.fail(response.data);
            }
        }
        is_lock = false; //分页请求锁
        console.log('释放锁')
        try {
          log.info({
            url: object.url,
            data: object.data,
            result: response.data
          });
        } catch (e) {
          console.log(e)
        }
      },
      fail: function (response) {
        if (object.loading !== false) {
          wx.hideLoading();
        }
        if (object.fail) {
          object.fail(response);
        } else {
          wx.showModal({
            title: '请求异常!',
            content: response.errMsg,
            showCancel: false,
          });
        }
      },
      complete: function (response) {
        wx.hideNavigationBarLoading();
        wx.stopPullDownRefresh(); //刷新完成后停止下拉刷新动效
        if (object.complete) {
          object.complete(response);
        }
      }
    })
  })
}

/*获取当前页带参数的url*/
function getCurrentPageUrlWithArgs() {
  var pages = getCurrentPages() //获取加载的页面
  var currentPage = pages[pages.length - 1] //获取当前页面的对象
  var url = currentPage.route //当前页面url
  var options = currentPage.options //如果要获取url中所带的参数可以查看options
  //拼接url的参数
  var urlWithArgs = url + '?'
  for (var key in options) {
    var value = options[key]
    urlWithArgs += key + '=' + value + '&'
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
  return urlWithArgs
}

/*发起请求*/
function request(object) {
  var that = this;
  return new Promise(async (resolve, reject) => {
    let access_token = wx.getStorageSync('access_token');
    let access_token_expires_time = wx.getStorageSync('access_token_expires_time');
    let expire = (!access_token || parseInt(access_token_expires_time) < parseInt(Date.parse(new Date()).toString().substr(0, 10))); //access_token是否过期
    let isNoAuth = ['passport/login'].some(v => v === object.url); // 接口是否需要Token验证
    //如果user_info的名字为空 则跳入授权页面
    // let user_info = wx.getStorageSync('user_info');
    // if (!user_info && access_token && object.url != api.passport.get_user_info) {
    //   let url = encodeURIComponent(that.getCurrentPageUrlWithArgs());
    //   wx.redirectTo({
    //     url: "/pages/login/login?url=" + url
    //   });
    //   return;
    // }
    // access_token过期后进入
    if (expire && !isNoAuth) {
      refreshSubscribers.push(object)
      if (!isRefreshToken) {
        // 标记正在刷新access_token
        isRefreshToken = true;
        await that.refreshToken();
        isRefreshToken = false;
        // access_token刷新成功后重新请求
        refreshSubscribers.forEach(async v => {
          resolve(await that.requestModel(v))
        })
        //清空刷新access_token中缓存的请求对象
        refreshSubscribers = [];
      }
    } else {
      //正常发起请求
      resolve(await that.requestModel(object))
    }
  })
}
module.exports = {
  isRefreshToken,
  refreshToken,
  getWxCode,
  refreshSubscribers,
  checkStatus,
  requestModel,
  httpClipboardData,
  getCurrentPageUrlWithArgs,
  request,
};