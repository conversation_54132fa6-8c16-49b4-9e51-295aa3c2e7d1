var expires_time_key_suffix = "_expires_time"

/**
 * 设置
 * k 键key
 * v 值value
 * t 秒
 */
function set(k, v, t) {
  wx.setStorageSync(k, v)
  var seconds = parseInt(t)
  if (seconds > 0) {
    var newtime = Date.parse(new Date())
    newtime = newtime / 1000 + seconds;
    wx.setStorageSync(k + expires_time_key_suffix, newtime + "")
  } else {
    wx.removeStorageSync(k + expires_time_key_suffix)
  }
}
/**
 * 获取
 * k 键key
 */
function get(k) {
  var deadtime = parseInt(wx.getStorageSync(k + expires_time_key_suffix))
  if (deadtime) {
    if (parseInt(deadtime) < Date.parse(new Date()) / 1000) {
      wx.removeStorageSync(k);
      // console.log("过期了")
      return null
    }
  }
  // console.log("没有过期")

  var res = wx.getStorageSync(k)
  if (res) {
    return res
  } else {
    return null
  }
}

/**
 * 删除
 */
function del(k) {
  wx.removeStorageSync(k);
  wx.removeStorageSync(k + expires_time_key_suffix);
}

/**
 * 清除所有key
 */
function clear() {
  wx.clearStorageSync();
}
module.exports = {
  set,
  get,
  del,
  clear
}