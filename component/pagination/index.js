Component({

  properties: {
    name: String,
    key: {
      type: String,
      observer: '_loadMoreData'
    },
    initImmediately: {
      type: Boolean,
      value: true,
      observer: function(val) {
        if (val && !this.data.initState) {
          this.initData()
        }
      }
    },
    limit: {
      type: Number,
      value: 20
    },
    total: Number,
    list: {
      type: Array,
      observer: '_endState'
    }
  },

  data: {
    initState: false, // 是否已经加载过
    page: 0,
    loading: false,
    is_end: false
  },

  lifetimes: {
    attached: function() {
      if (this.data.initImmediately) {
        this.initData()
      }
    }
  },

  methods: {
    initData() {
      console.info(`%c [${this.data.name}]`, "color:red", `start init data `)
      this._loadMoreData()
      this.setData({
        initState: true
      })
    },
    _loadMoreData() {
      const {
        loading,
        is_end,
        limit
      } = this.data
      if (loading) return
      if (is_end) return

      const page = this.data.page + 1
      console.info(`%c [${this.data.name}]`, "color:red", `load page: ${page} data `)
      this.setData({
        loading: true,
        page
      })
      this.triggerEvent('getList', {
        page,
        limit
      })
    },
    _endState(val, oldVal) {
      //console.log(val)
      //console.log(oldVal)
      if (!this.data.initState) return
      // if (val.length === oldVal.length) return
      const {
        total,
        list,
        limit
      } = this.properties
      console.log(`%c [${this.data.name}]`, "color:red;", `total: ${total}`)
      console.log(`%c [${this.data.name}]`, "color:red;", `list: `, list[list.length-1])
      let is_end = false
      if ((list.length * limit) >= total) {
        console.log('已经到底啦')
        is_end = true
      }
      this.setData({
        loading: false,
        is_end: is_end
      })
    }
  }
})