<!--component/area_picker/index.wxml-->
<view class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after">
  <view class="weui-cell__hd">
    <label class="weui-label" style="width: 3em;">地区</label>
  </view>
  <view class="weui-cell__bd" style="padding-left: 30px;">
    <picker mode="multiSelector" value="{{multiIndex}}" bindchange="bindRegionChange" bindcolumnchange="bindRegionColumnChange" range='{{multiArray}}'>
      <view class="weui-select" wx:if="{{addressCity.length>0 && selectProvinceId >0}}">{{addressCity[0]}} - {{addressCity[1]}} - {{addressCity[2]}}</view>
      <view class="weui-select" wx:else>请选择地区</view>
    </picker>
  </view>
</view>