<!--component/uploader/upload.wxml-->
<view class="page__bd">
  <view class="weui-cells">
    <view class="weui-cell">
      <view class="weui-cell__bd">
        <view class="weui-uploader">
          <view class="weui-uploader__hd">
            <view class="weui-uploader__overview">
              <view class="weui-uploader__title">图片</view>
              <view class="weui-uploader__info">{{files.length}}/{{maxFileCount}}</view>
            </view>
            <view class="weui-uploader__tips">
              {{upload_image_example_tips}} (长按可删除)
            </view>
          </view>
          <view class="weui-uploader__bd">
            <view class="weui-uploader__files" id="uploaderFiles">
              <view wx:if="{{upload_image_example_url && files.length==0 }}" class="weui-uploader__file" bindtap="_previewExampleImage" data-src="{{upload_image_example_url}}">
                <image class="weui-uploader__img" src="{{upload_image_example_url}}" mode="aspectFill" />
              </view>
              <block wx:for="{{files}}" wx:key="*this">
                <view class="weui-uploader__file" bindlongpress="_deleteImage" data-index="{{index}}" bindtap="_previewImage" id="{{item}}">
                  <image class="weui-uploader__img" src="{{item}}" mode="aspectFill" />
                </view>
              </block>
            </view>
            <view style='display:{{isCanAddFile?"":"none"}}' class="weui-uploader__input-box">
              <view class="weui-uploader__input" bindtap="_chooseImage"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>