// component/uploader/upload.js
var common = require("../../common/upload.js");
Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    files: {
      type: Array,
      value: []
    },
    upload_image_example_tips: {
      type: String,
      value: ''
    },
    upload_image_example_url: {
      type: String,
      value: ''
    },
    maxFileCount: {
      //允许最多9张图片
      type: Number,
      value: 9
    },
    isCanAddFile: {
      type: Boolean,
      value: true
    }
  },
  /**
   * 组件的初始数据
   */
  data: {

  },
  /*
   *组件生命周期函数，在组件布局完成后执行，此时可以获取节点信息
   */
  ready: function () {},
  /**
   * 组件的方法列表
   */
  methods: {
    /*图片上传 */
    _chooseImage: function (e) {
      var that = this;
      wx.chooseImage({
        count: that.data.maxFileCount - that.data.files.length,
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          // // 返回选定照片的本地文件路径列表，tempFilePath 可以作为img标签的src属性显示图片      
          console.log(res);
          var waitFiles = res.tempFilePaths;
          var allowCount = that.data.maxFileCount - that.data.files.length; //允许上传的文件数          
          if (waitFiles.length >= allowCount) {
            waitFiles = waitFiles.slice(0, allowCount);
          }
          var index = 0; //第几张开始
          var successFiles = []; //成功的文件
          common.uploadFiles(waitFiles, index, successFiles, function (urls) { //此处为抽出的公用方法，便于其它地方调用
            that.data.files = that.data.files.concat(urls);
            if (that.data.files.length >= that.data.maxFileCount) {
              that.data.isCanAddFile = false;
            }
            that.setData({
              files: that.data.files,
              isCanAddFile: that.data.isCanAddFile
            });
          });
        }
      })
    },
    /*图片预览*/
    _previewExampleImage: function (e) {
      var current =e.currentTarget.dataset.src;
      wx.previewImage({
        current: current, // 当前显⽰图⽚的http链接
        urls: [current] // 需要预览的图⽚http链接列表
      })
    },
    /*图片预览*/
    _previewImage: function (e) {
      var preUlrs = [];
      this.data.files.map(
        function (value, index) {
          //preUlrs.push(value.OrigionUrl);
          preUlrs.push(value);
        }
      );
      wx.previewImage({
        current: e.currentTarget.id, // 当前显示图片的http链接
        urls: preUlrs // 需要预览的图片http链接列表
      })
    },
    /*图片删除*/
    _deleteImage: function (e) {
      var that = this;
      var files = that.data.files;
      var index = e.currentTarget.dataset.index; //获取当前长按图片下标
      wx.showModal({
        title: '提示',
        content: '确定要删除此图片吗？',
        success: function (res) {
          if (res.confirm) {
            files.splice(index, 1);
          } else if (res.cancel) {
            return false;
          }
          that.setData({
            files,
            isCanAddFile: true
          });
        }
      })
    },
    /*************供外部调用接口*******************/
    getFiles: function () {
      return this.data.files;
    }
  }
})