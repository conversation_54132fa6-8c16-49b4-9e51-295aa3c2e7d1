// component/copy_right/index.js
var app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {
    copy_right_name: '',
    copy_right_footer: '',
    copy_right_url: ''
  },
  attached() {
    let that = this;
    app.request({
      url: app.api.common.get_copyright,
      data: {
        'from': 'weapp'
      },
      success(res) {
        // console.log(res.data.data)
        that.setData({
          copy_right_name: res.data.copy_right_name,
          copy_right_footer: res.data.copy_right_footer,
          copy_right_url: res.data.copy_right_url
        })
      }
    })
  },
  /**
   * 组件的方法列表
   */
  methods: {
    goto_info: function () {
      console.log('goto_info');
    }
  }
})