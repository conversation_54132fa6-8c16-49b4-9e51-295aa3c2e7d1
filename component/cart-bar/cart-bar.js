// component/cart-bar/cart-bar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 购物车商品列表
    cartGoods: {
      type: Array,
      value: []
    },
    // 总数量
    totalCount: {
      type: Number,
      value: 0
    },
    // 总价格
    totalPrice: {
      type: Number,
      value: 0
    },
    // 确认按钮文字
    confirmButtonText: {
      type: String,
      value: '确认'
    },
    // 确认按钮颜色
    confirmButtonColor: {
      type: String,
      value: '#07c160'
    },
    // 是否显示价格
    showPrice: {
      type: Boolean,
      value: true
    },
    // 购物车图标
    cartIcon: {
      type: String,
      value: '/images/cart.svg'
    },
    // 数量单位
    countUnit: {
      type: String,
      value: '件'
    },
    // 价格单位
    priceUnit: {
      type: String,
      value: '¥'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    showCartDetail: false, // 是否显示购物车详情
    cartDetailVisible: false, // 控制弹窗DOM显示
    cartDetailAnimating: false // 是否正在动画中
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 切换购物车详情显示
     */
    toggleCartDetail: function() {
      if (this.data.totalCount === 0) return;
      
      if (this.data.cartDetailAnimating) return; // 防止动画期间重复点击
      
      if (!this.data.showCartDetail) {
        // 显示弹窗
        this.showCartDetail();
      } else {
        // 隐藏弹窗
        this.hideCartDetail();
      }
    },

    /**
     * 显示购物车详情
     */
    showCartDetail: function() {
      this.setData({
        cartDetailAnimating: true,
        cartDetailVisible: true
      });

      // 下一帧开始动画
      wx.nextTick(() => {
        this.setData({
          showCartDetail: true
        });
        
        // 动画完成后重置状态
        setTimeout(() => {
          this.setData({
            cartDetailAnimating: false
          });
        }, 300);
      });

      // 触发切换事件
      this.triggerEvent('toggleDetail', { show: true });
    },

    /**
     * 隐藏购物车详情
     */
    hideCartDetail: function() {
      this.setData({
        cartDetailAnimating: true,
        showCartDetail: false
      });

      // 动画完成后隐藏DOM
      setTimeout(() => {
        this.setData({
          cartDetailVisible: false,
          cartDetailAnimating: false
        });
      }, 300);

      // 触发切换事件
      this.triggerEvent('toggleDetail', { show: false });
    },

    /**
     * 确认按钮点击
     */
    onConfirm: function() {
      if (this.data.totalCount === 0) {
        wx.showToast({
          title: '请先选择商品',
          icon: 'none'
        });
        return;
      }

      // 触发确认事件
      this.triggerEvent('confirm', {
        cartGoods: this.data.cartGoods,
        totalCount: this.data.totalCount,
        totalPrice: this.data.totalPrice
      });
    },

    /**
     * 购物车商品数量操作
     */
    onCartGoodsCountChange: function(e) {
      const { type, index } = e.currentTarget.dataset;
      
      // 触发数量变化事件
      this.triggerEvent('countChange', {
        type: type,
        index: index,
        goods: this.data.cartGoods[index]
      });
    },

    /**
     * 清空购物车
     */
    onClearCart: function() {
      wx.showModal({
        title: '提示',
        content: '确定要清空购物车吗？',
        success: (res) => {
          if (res.confirm) {
            // 触发清空事件
            this.triggerEvent('clear');
            this.setData({
              showCartDetail: false
            });
          }
        }
      });
    },

    /**
     * 关闭购物车详情
     */
    closeCartDetail: function() {
      this.hideCartDetail();
    },

    /**
     * 阻止事件冒泡
     */
    stopPropagation: function() {
      // 阻止事件冒泡
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'totalCount': function(totalCount) {
      // 当总数量为0时，自动关闭购物车详情
      if (totalCount === 0 && this.data.showCartDetail) {
        this.hideCartDetail();
      }
    }
  }
});