<!--component/cart-bar/cart-bar.wxml-->
<view class="cart-bar-container">
  <!-- 底部购物车栏 -->
  <view class="cart-bar">
    <view class="cart-info" bindtap="toggleCartDetail">
      <view class="cart-icon-wrapper">
        <image class="cart-icon" src="{{cartIcon}}" mode="aspectFit"></image>
        <view wx:if="{{totalCount > 0}}" class="cart-badge">{{totalCount}}</view>
      </view>
      <view class="cart-text">
        <view class="cart-count">已选 {{totalCount}} {{countUnit}}</view>
        <view class="cart-price" wx:if="{{showPrice && totalPrice > 0}}">{{priceUnit}}{{totalPrice}}</view>
      </view>
    </view>
    
    <view 
      class="confirm-btn {{totalCount > 0 ? 'active' : ''}}"
      style="background-color: {{totalCount > 0 ? confirmButtonColor : '#ccc'}}"
      bindtap="onConfirm">
      {{confirmButtonText}}
    </view>
  </view>

  <!-- 购物车详情弹窗遮罩 -->
  <view wx:if="{{cartDetailVisible}}" class="cart-mask {{showCartDetail ? 'show' : ''}}" bindtap="closeCartDetail"></view>
  
  <!-- 购物车详情弹窗 -->
  <view wx:if="{{cartDetailVisible}}" class="cart-detail-modal {{showCartDetail ? 'show' : ''}}">
    <view class="cart-detail-content">
      <view class="cart-detail-header">
        <view class="cart-detail-title">已选商品</view>
        <view class="cart-detail-actions">
          <view class="clear-btn" bindtap="onClearCart">清空</view>
          <view class="close-btn" bindtap="closeCartDetail">
            <text class="icon-close">✖</text>
          </view>
        </view>
      </view>
      
      <scroll-view scroll-y="true" class="cart-detail-list">
        <view wx:for="{{cartGoods}}" wx:key="cart_item_id" class="cart-detail-item">
          <view class="cart-goods-image">
            <image src="{{item.goods_pic}}" mode="aspectFill" />
          </view>

          <view class="cart-goods-info">
            <view class="cart-goods-name">{{item.goods_name}}</view>
            <view wx:if="{{item.sku_text}}" class="cart-goods-sku">{{item.sku_text}}</view>
            <view class="cart-goods-price" wx:if="{{showPrice}}">{{priceUnit}}{{item.goods_price}}</view>
          </view>

          <view class="cart-goods-control">
            <view
              class="cart-count-btn minus"
              data-type="minus"
              data-index="{{index}}"
              catchtap="onCartGoodsCountChange">
              -
            </view>
            <view class="cart-count-text">{{item.count}}</view>
            <view
              class="cart-count-btn plus"
              data-type="plus"
              data-index="{{index}}"
              catchtap="onCartGoodsCountChange">
              +
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view wx:if="{{cartGoods.length === 0}}" class="cart-empty">
          <view class="empty-icon">🛒</view>
          <view class="empty-text">购物车是空的</view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>