# 购物车底栏组件 (cart-bar)

一个可复用的购物车底栏组件，提供购物车展示、商品管理和确认操作功能。

## 功能特性

- ✅ **底部固定展示**：显示购物车图标、商品数量和总价
- ✅ **购物车详情弹窗**：点击购物车区域展开详情
- ✅ **商品数量管理**：支持增减商品数量
- ✅ **清空购物车**：一键清空所有商品
- ✅ **自定义配置**：按钮文字、颜色、图标等可配置
- ✅ **事件丰富**：提供完整的事件回调

## 使用方法

### 1. 在页面JSON中引入组件

```json
{
  "usingComponents": {
    "cart-bar": "/component/cart-bar/cart-bar"
  }
}
```

### 2. 在WXML中使用组件

```xml
<cart-bar
  cart-goods="{{cart_goods}}"
  total-count="{{total_count}}"
  total-price="{{total_price}}"
  confirm-button-text="确认订单"
  confirm-button-color="#07c160"
  show-price="{{true}}"
  cart-icon="🛒"
  count-unit="件"
  price-unit="¥"
  bind:confirm="onConfirm"
  bind:countChange="onCountChange"
  bind:clear="onClear"
  bind:toggleDetail="onToggleDetail"
/>
```

### 3. 在JS中处理事件

```javascript
Page({
  data: {
    cart_goods: [
      {
        cart_item_id: "goods_1",
        goods_name: "商品名称",
        goods_pic: "商品图片URL",
        goods_price: "99.00",
        sku_text: "规格信息",
        count: 2
      }
    ],
    total_count: 2,
    total_price: 198.00
  },

  // 确认按钮点击
  onConfirm: function(e) {
    const { cartGoods, totalCount, totalPrice } = e.detail;
    console.log('确认订单:', { cartGoods, totalCount, totalPrice });
  },

  // 商品数量变化
  onCountChange: function(e) {
    const { type, index, goods } = e.detail;
    // type: 'plus' | 'minus'
    // index: 商品在购物车中的索引
    // goods: 商品信息
    
    let cart_goods = [...this.data.cart_goods];
    if (type === 'plus') {
      cart_goods[index].count++;
    } else if (type === 'minus') {
      if (cart_goods[index].count > 1) {
        cart_goods[index].count--;
      } else {
        cart_goods.splice(index, 1);
      }
    }
    
    this.updateCart(cart_goods);
  },

  // 清空购物车
  onClear: function() {
    this.setData({
      cart_goods: [],
      total_count: 0,
      total_price: 0
    });
  },

  // 购物车详情切换
  onToggleDetail: function(e) {
    const { show } = e.detail;
    console.log('购物车详情显示状态:', show);
  }
});
```

## 属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| cartGoods | Array | [] | 购物车商品列表 |
| totalCount | Number | 0 | 商品总数量 |
| totalPrice | Number | 0 | 商品总价格 |
| confirmButtonText | String | '确认' | 确认按钮文字 |
| confirmButtonColor | String | '#07c160' | 确认按钮颜色 |
| showPrice | Boolean | true | 是否显示价格 |
| cartIcon | String | '🛒' | 购物车图标 |
| countUnit | String | '件' | 数量单位 |
| priceUnit | String | '¥' | 价格单位 |

## 购物车商品数据格式

```javascript
{
  cart_item_id: "唯一标识",     // 必需
  goods_name: "商品名称",      // 必需
  goods_pic: "商品图片URL",    // 必需
  goods_price: "商品价格",     // 必需
  sku_text: "规格信息",        // 可选
  count: 1                     // 必需
}
```

## 事件说明

### confirm
确认按钮点击事件
```javascript
e.detail = {
  cartGoods: Array,    // 购物车商品列表
  totalCount: Number,  // 总数量
  totalPrice: Number   // 总价格
}
```

### countChange
商品数量变化事件
```javascript
e.detail = {
  type: String,       // 'plus' | 'minus'
  index: Number,      // 商品索引
  goods: Object       // 商品信息
}
```

### clear
清空购物车事件（无参数）

### toggleDetail
购物车详情切换事件
```javascript
e.detail = {
  show: Boolean       // 是否显示详情
}
```

## 使用场景

- **商品选择页面**：按钮显示"下一步"
- **礼品选择页面**：按钮显示"送礼"
- **购物车页面**：按钮显示"结算"
- **其他选择场景**：可自定义按钮文字

## 样式定制

组件使用CSS变量，可以通过覆盖样式进行定制：

```css
/* 自定义购物车栏高度 */
.cart-bar {
  height: 120rpx;
}

/* 自定义确认按钮样式 */
.confirm-btn {
  border-radius: 50rpx;
}
```

## 注意事项

1. 购物车商品数据中的 `cart_item_id` 必须唯一
2. 组件会自动处理购物车为空时的状态
3. 购物车详情弹窗支持滚动，适配长列表
4. 组件已适配不同屏幕尺寸