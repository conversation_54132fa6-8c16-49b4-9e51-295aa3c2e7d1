/* component/cart-bar/cart-bar.wxss */

.cart-bar-container {
  position: static;
}

/* 底部购物车栏 */
.cart-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 1000;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.cart-info {
  flex: 1;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  width: 64rpx;
  height: 64rpx;
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  min-width: 36rpx;
  height: 36rpx;
  background-color: #ff4544;
  color: #fff;
  font-size: 20rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-sizing: border-box;
}

.cart-text {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.cart-count {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.cart-price {
  font-size: 32rpx;
  color: #ff4544;
  font-weight: bold;
}

.confirm-btn {
  padding: 0 40rpx;
  height: 70rpx;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  background-color: #ccc;
  transition: all 0.3s ease;
  cursor: pointer;
}

.confirm-btn.active {
  transform: scale(1.02);
}

.confirm-btn:active {
  transform: scale(0.98);
}

/* 购物车详情弹窗遮罩 */
.cart-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0; /* 覆盖整个屏幕 */
  background-color: rgba(0, 0, 0, 0);
  z-index: 550;
  transition: background-color 0.3s ease;
  pointer-events: none; /* 初始状态不接收点击事件 */
}

.cart-mask.show {
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: auto; /* 显示时接收点击事件 */
}


/* 购物车详情弹窗 */
.cart-detail-modal {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0; /* 直接从屏幕底部开始 */
  z-index: 600;
  max-height: 60vh;
  pointer-events: none; /* 初始状态不接收点击事件 */
}

.cart-detail-modal.show {
  pointer-events: auto; /* 显示时接收点击事件 */
}

.cart-detail-content {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  transform: translateY(100%); /* 初始状态完全隐藏在底部 */
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin-bottom: 0; /* 移除底部间隙 */
}

.cart-detail-modal.show .cart-detail-content {
  transform: translateY(0); /* 向上移动到正确位置 */
  border-bottom: 100rpx solid #fff; /* 添加白色底部边框，与购物车栏无缝衔接 */
}

/* 确保购物车栏始终可见且可点击 */


.cart-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.cart-detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.cart-detail-actions {
  display: flex;
  gap: 30rpx;
}

.clear-btn {
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
}

.clear-btn:active {
  color: #333;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.icon-close {
  font-size: 28rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #666;
}

.icon-close:active {
  background-color: #e0e0e0;
}

.cart-detail-list {
  flex: 1;
  padding: 0 30rpx 30rpx;
  overflow-y: auto;
  max-height: 40vh;
  margin-bottom: 0; /* 确保列表不会被购物车栏遮挡 */
}

.cart-detail-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.cart-detail-item:last-child {
  border-bottom: none;
}

.cart-goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.cart-goods-image image {
  width: 100%;
  height: 100%;
}

.cart-goods-info {
  flex: 1;
  margin-right: 20rpx;
}

.cart-goods-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.cart-goods-sku {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.cart-goods-price {
  font-size: 24rpx;
  color: #ff4544;
  font-weight: 500;
}

.cart-goods-control {
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  overflow: hidden;
}

.cart-count-btn {
  width: 60rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cart-count-btn:active {
  background-color: #e8e8e8;
}

.cart-count-text {
  width: 60rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333;
  background-color: #fff;
  border-left: 1rpx solid #e0e0e0;
  border-right: 1rpx solid #e0e0e0;
}

/* 空状态 */
.cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .cart-bar {
    padding: 0 20rpx;
    height: 90rpx;
  }
  
  .cart-icon {
    width: 56rpx;
    height: 56rpx;
  }
  
  .cart-count {
    font-size: 26rpx;
  }
  
  .cart-price {
    font-size: 28rpx;
  }
  
  .confirm-btn {
    padding: 0 30rpx;
    height: 60rpx;
    font-size: 26rpx;
  }
  
  .cart-mask {
    bottom: 0;
  }
  
  .cart-detail-modal {
    bottom: 0;
    max-height: 60vh;
  }
  
  .cart-detail-content {
    margin-bottom: 0;
  }
  
  .cart-detail-modal.show .cart-detail-content {
    transform: translateY(0);
  }
  
  .cart-detail-header,
  .cart-detail-list {
    padding-left: 20rpx;
    padding-right: 20rpx;
  }
  
  .cart-goods-image {
    width: 60rpx;
    height: 60rpx;
  }
  
  .cart-goods-name {
    font-size: 24rpx;
  }
}

/* 动画效果 */
.cart-icon-wrapper:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.cart-count-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}