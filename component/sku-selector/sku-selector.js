// component/sku-selector/sku-selector.js
console.log('=== SKU组件文件加载 ===');
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 显示状态
    visible: {
      type: Boolean,
      value: false
    },
    // 商品数据
    product: {
      type: Object,
      value: {}
    },
    // 初始数量
    initialQuantity: {
      type: Number,
      value: 1
    },
    // 模式：add新增 | edit编辑
    mode: {
      type: String,
      value: 'add'
    },
    // 校验函数名称，父组件中的方法名
    validateFunctionName: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    selectedAttrs: {}, // 用户选择的属性映射 {group_id: attr_id}
    currentSku: null, // 当前匹配的SKU对象
    quantity: 1, // 选择的数量
    currentPrice: 0, // 当前价格
    currentStock: 0, // 当前库存
    maxSelectableQuantity: 1, // 最大可选择数量
    canConfirm: false, // 是否可以确认选择
    showModal: false, // 内部弹窗显示状态
    isInitialized: false // 是否已初始化
  },

  /**
   * 组件的方法列表
   */
  methods: {
  /**
   * 监听属性变化
   */
  _propertyChange: function() {
    console.log('=== SKU组件属性变化 ===');
    console.log('visible:', this.data.visible);
    console.log('product:', this.data.product);

    if (this.data.visible) {
      // 先显示DOM，再触发动画
      this.setData({
        showModal: false
      }, () => {
        // 使用setTimeout确保DOM先渲染，再触发动画
        setTimeout(() => {
          this.setData({
            showModal: true
          });
          
          // 如果有商品数据，则初始化
          if (this.data.product && this.data.product.guid) {
            console.log('初始化SKU数据');
            this.initSkuData();
          }
        }, 50);
      });
    } else {
      // 当弹窗关闭时，先触发动画，再隐藏DOM
      this.setData({
        showModal: false
      });
      
      // 延迟重置初始化状态，确保动画完成
      setTimeout(() => {
        this.setData({
          isInitialized: false
        });
      }, 300);
    }
  },

  /**
   * 初始化SKU数据
   */
  initSkuData: function() {
    console.log('=== 初始化SKU数据 ===');
    const product = this.data.product;
    console.log('商品数据:', product);
    console.log('is_attribute:', product.is_attribute);
    console.log('stock_mode:', product.stock_mode);

    if (!product || !product.guid) {
      console.log('商品数据无效，跳过初始化');
      return;
    }

    // 避免重复初始化
    if (this.data.isInitialized) {
      console.log('已初始化，跳过重复初始化');
      return;
    }

    // 根据商品类型初始化
    let initialStock, maxQuantity, canConfirm;
    
    if (product.is_attribute == 1) {
      // 多规格商品 - 需要选择SKU
      console.log('多规格商品，需要选择SKU');
      initialStock = product.stock || 0;
      maxQuantity = product.max_choose_num || 999;
      canConfirm = false; // 多规格商品需要选择完整SKU才能确认
      
      // 如果限制库存，需要根据SKU库存来计算
      if (product.stock_mode == 1) {
        maxQuantity = Math.min(maxQuantity, initialStock);
      }
    } else {
      // 单规格商品 - 直接可用
      console.log('单规格商品，直接可用');
      initialStock = product.stock || 0;
      maxQuantity = product.max_choose_num || 999;
      canConfirm = true; // 单规格商品直接可确认
      
      // 如果限制库存
      if (product.stock_mode == 1) {
        maxQuantity = Math.min(maxQuantity, initialStock);
      } else {
        // 不限制库存时，库存显示为无限
        initialStock = -1;
      }
    }

    // 确保最大可选数量合理
    maxQuantity = Math.max(maxQuantity, 1);
    console.log('设置最大可选数量为:', maxQuantity);

    // 初始化选择状态
    this.setData({
      selectedAttrs: product.selected_attrs || {},
      quantity: Math.max(this.data.initialQuantity || 1, product.min_choose_num || 1),
      isInitialized: true,
      currentPrice: parseFloat(product.price) || 0,
      currentStock: initialStock,
      maxSelectableQuantity: maxQuantity,
      canConfirm: canConfirm
    });

    console.log('初始化后的状态:', {
      selectedAttrs: this.data.selectedAttrs,
      quantity: this.data.quantity,
      currentPrice: this.data.currentPrice,
      currentStock: this.data.currentStock,
      maxSelectableQuantity: this.data.maxSelectableQuantity,
      canConfirm: this.data.canConfirm
    });

    // 更新SKU状态（仅对多规格商品）
    if (product.is_attribute == 1) {
      this.updateSkuState();
    } else {
      // 单规格商品也需要更新属性可用性（虽然没有属性，但确保逻辑完整）
      console.log('单规格商品，无需更新SKU状态');
    }
  },

    /**
     * 属性选择事件处理
     */
    selectAttr: function(e) {
      console.log('=== 属性选择事件 ===');
      
      const groupId = e.currentTarget.dataset.groupId;
      const attrId = e.currentTarget.dataset.attrId;
      const product = this.data.product;

      console.log('groupId:', groupId);
      console.log('attrId:', attrId);

      if (product.is_attribute != 1) {
        console.log('非多规格商品，跳过属性选择');
        return;
      }

      // 检查属性是否可用
      if (!this.isAttrAvailable(product, groupId, attrId)) {
        console.log('属性不可用，显示提示');
        wx.showToast({
          title: "该规格暂无库存",
          icon: "none",
          duration: 2000
        });
        return;
      }

      console.log('属性可用，更新选择状态');
      
      // 更新选择的属性
      let selectedAttrs = Object.assign({}, this.data.selectedAttrs);
      selectedAttrs[groupId] = attrId;

      console.log('更新后的选择状态:', selectedAttrs);

      this.setData({ selectedAttrs });
      this.updateSkuState();
    },

    /**
     * 数量减少
     */
    decreaseQuantity: function() {
      console.log('减少数量');
      const product = this.data.product;
      const minQuantity = Math.max(product.min_choose_num || 1, 1);
      
      if (this.data.quantity > minQuantity) {
        let newQuantity = this.data.quantity - 1;
        this.setData({
          quantity: newQuantity
        }, () => {
          console.log('数量减少为:', this.data.quantity);
        });
      } else {
        console.log('已达到最小数量:', minQuantity);
      }
    },

    /**
     * 数量增加
     */
    increaseQuantity: function() {
      console.log('增加数量');
      console.log('当前数量:', this.data.quantity);
      console.log('最大可选数量:', this.data.maxSelectableQuantity);
      
      if (this.data.quantity < this.data.maxSelectableQuantity) {
        let newQuantity = this.data.quantity + 1;
        this.setData({
          quantity: newQuantity
        }, () => {
          console.log('数量增加为:', this.data.quantity);
        });
      } else {
        console.log('已达到最大数量:', this.data.maxSelectableQuantity);
        wx.showToast({
          title: '已达到最大可选数量',
          icon: 'none',
          duration: 1500
        });
      }
    },

    /**
     * 确认选择
     */
    confirmSelection: function() {
      if (!this.data.canConfirm) {
        wx.showToast({
          title: "请选择完整规格",
          icon: "none"
        });
        return;
      }

      const product = this.data.product;
      const selectedAttrs = this.data.selectedAttrs;
      const quantity = this.data.quantity;
      const currentSku = this.data.currentSku;

      // 格式化选中的属性为数组格式
      const formattedAttrs = this.formatSelectedAttrs(selectedAttrs);

      // 如果设置了校验函数，先调用校验
      const validateFunctionName = this.data.validateFunctionName;
      if (validateFunctionName) {
        // 获取页面实例
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        
        // 检查页面是否有对应的校验函数
        if (currentPage && typeof currentPage[validateFunctionName] === 'function') {
          // 调用校验函数
          const validateResult = currentPage[validateFunctionName]({
            product: product,
            selectedAttrs: formattedAttrs,
            quantity: quantity,
            sku: currentSku,
            price: this.data.currentPrice
          });
          
          // 如果校验失败，直接返回
          if (validateResult === false) {
            return;
          }
        }
      }

      // 触发确认事件
      this.triggerEvent('confirm', {
        product: product,
        selectedAttrs: formattedAttrs,
        quantity: quantity,
        sku: currentSku,
        price: this.data.currentPrice
      });

      // 关闭弹窗
      this.setData({
        showModal: false
      });
    },

    /**
     * 取消选择
     */
    cancelSelection: function() {
      this.triggerEvent('cancel');
      // 关闭弹窗
      this.setData({
        showModal: false
      });
    },

    /**
     * 关闭弹窗
     */
    closeModal: function() {
      this.setData({
        showModal: false
      });
    },


    /**
     * 更新SKU状态
     */
    updateSkuState: function() {
      const product = this.data.product;
      
      // 更新属性可用性
      this.updateAttrAvailability(product);
      
      // 匹配SKU
      const matchedSku = this.findMatchingSku(product);
      
      // 计算价格和库存
      const currentPrice = this.getCurrentPrice(product, matchedSku);
      const currentStock = this.getCurrentStock(product, matchedSku);
      const maxQuantity = this.getMaxSelectableQuantity(product, currentStock);
      const canConfirm = this.checkCanConfirm(product);

      // 调整数量到合理范围
      let quantity = this.data.quantity;
      const minQuantity = Math.max(product.min_choose_num || 1, 1);
      
      if (quantity > maxQuantity) {
        quantity = maxQuantity;
      }
      if (quantity < minQuantity) {
        quantity = minQuantity;
      }

      this.setData({
        currentSku: matchedSku,
        currentPrice: currentPrice,
        currentStock: currentStock,
        maxSelectableQuantity: maxQuantity,
        canConfirm: canConfirm,
        quantity: quantity
      });
    },

    /**
     * SKU匹配核心算法
     */
    findMatchingSku: function(product) {
      if (product.is_attribute != 1) {
        return null; // 单规格商品无需匹配
      }

      if (!product.attr_group_list || !this.data.selectedAttrs) {
        return null;
      }

      const selectedGroupCount = Object.keys(this.data.selectedAttrs).length;
      if (selectedGroupCount !== product.attr_group_list.length) {
        return null; // 属性选择不完整
      }

      let matchedSkus = null;

      // 遍历每个属性组
      for (const group of product.attr_group_list) {
        const selectedAttrId = this.data.selectedAttrs[group.attr_group_id];
        if (!selectedAttrId) {
          return null;
        }

        // 找到选中的属性
        const selectedAttr = group.attr_list.find(attr => attr.attr_id === selectedAttrId);
        if (!selectedAttr || !selectedAttr.sku_list) {
          return null;
        }

        // 第一次匹配，直接使用该属性的SKU列表
        if (matchedSkus === null) {
          matchedSkus = selectedAttr.sku_list.slice(); // 复制数组
        } else {
          // 后续匹配，取交集
          matchedSkus = matchedSkus.filter(sku1 => 
            selectedAttr.sku_list.some(sku2 => sku1.sku_guid === sku2.sku_guid)
          );
        }

        if (matchedSkus.length === 0) {
          return null; // 没有匹配的SKU
        }
      }

      return matchedSkus && matchedSkus.length > 0 ? matchedSkus[0] : null;
    },

    /**
     * 检查属性是否可用
     */
    isAttrAvailable: function(product, groupId, attrId) {
      // 只有多规格商品才需要检查属性可用性
      if (product.is_attribute != 1) {
        return true;
      }

      if (!product.attr_group_list || product.attr_group_list.length === 0) {
        return true;
      }

      const group = product.attr_group_list.find(g => g.attr_group_id === groupId);
      if (!group) {
        return false;
      }

      const attr = group.attr_list.find(a => a.attr_id === attrId);
      if (!attr || !attr.sku_list) {
        return false;
      }

      // 获取当前已选择的属性（不包括当前要检查的属性组）
      let selectedAttrs = Object.assign({}, this.data.selectedAttrs);

      // 如果没有选择任何其他属性，只需要检查该属性是否有可用的SKU
      let hasOtherSelections = false;
      for (let gId in selectedAttrs) {
        if (gId !== groupId && selectedAttrs[gId]) {
          hasOtherSelections = true;
          break;
        }
      }

      // 根据库存模式检查SKU可用性
      let availableSkus;
      if (product.stock_mode == 0) {
        // stock_mode = 0: 不限制库存，所有SKU都可用
        console.log('不限制库存模式，所有SKU可用');
        availableSkus = attr.sku_list; // 所有SKU都可用
      } else {
        // stock_mode = 1: 限制库存，只有库存大于0的SKU可用
        console.log('限制库存模式，检查SKU库存');
        availableSkus = attr.sku_list.filter(sku => sku.stock > 0);
      }

      if (!hasOtherSelections) {
        // 没有其他选择，只要该属性有可用SKU就行
        console.log(`属性 ${attr.attr_name} 可用SKU数量:`, availableSkus.length);
        return availableSkus.length > 0;
      }

      // 有其他选择，需要检查能否与其他已选属性形成有效组合
      // 创建临时选择状态，但不修改组件状态
      let tempSelectedAttrs = Object.assign({}, selectedAttrs);
      tempSelectedAttrs[groupId] = attrId;

      // 创建临时商品对象进行SKU匹配测试
      let tempProduct = Object.assign({}, product);
      
      // 临时模拟匹配SKU，不修改组件状态
      let matchedSku = this.simulateMatchingSku(tempProduct, tempSelectedAttrs);

      if (!matchedSku) {
        console.log(`属性 ${attr.attr_name} 无法形成有效SKU组合`);
        return false;
      }

      // 根据库存模式检查匹配的SKU是否可用
      if (product.stock_mode == 0) {
        console.log(`属性 ${attr.attr_name} 可用（不限制库存）`);
        return true; // 不限制库存时，有匹配的SKU就可用
      } else {
        let isAvailable = matchedSku.stock > 0;
        console.log(`属性 ${attr.attr_name} ${isAvailable ? '可用' : '不可用'}（库存: ${matchedSku.stock}）`);
        return isAvailable; // 限制库存时，需要库存大于0
      }
    },

    /**
     * 模拟SKU匹配（不修改组件状态）
     */
    simulateMatchingSku: function(product, selectedAttrs) {
      if (product.is_attribute != 1) {
        return null;
      }

      if (!product.attr_group_list || !selectedAttrs) {
        return null;
      }

      const selectedGroupCount = Object.keys(selectedAttrs).length;
      if (selectedGroupCount !== product.attr_group_list.length) {
        return null; // 属性选择不完整
      }

      let matchedSkus = null;

      // 遍历每个属性组
      for (const group of product.attr_group_list) {
        const selectedAttrId = selectedAttrs[group.attr_group_id];
        if (!selectedAttrId) {
          return null;
        }

        // 找到选中的属性
        const selectedAttr = group.attr_list.find(attr => attr.attr_id === selectedAttrId);
        if (!selectedAttr || !selectedAttr.sku_list) {
          return null;
        }

        // 第一次匹配，直接使用该属性的SKU列表
        if (matchedSkus === null) {
          matchedSkus = selectedAttr.sku_list.slice(); // 复制数组
        } else {
          // 后续匹配，取交集
          matchedSkus = matchedSkus.filter(sku1 => 
            selectedAttr.sku_list.some(sku2 => sku1.sku_guid === sku2.sku_guid)
          );
        }

        if (matchedSkus.length === 0) {
          return null; // 没有匹配的SKU
        }
      }

      return matchedSkus && matchedSkus.length > 0 ? matchedSkus[0] : null;
    },

    /**
     * 更新属性的可用性状态
     */
    updateAttrAvailability: function(product) {
      if (product.is_attribute != 1 || !product.attr_group_list) {
        console.log('跳过属性可用性更新：非多规格商品或无属性组');
        return;
      }

      console.log('=== 开始更新属性可用性 ===');
      console.log('商品库存模式 (stock_mode):', product.stock_mode);
      
      for (const group of product.attr_group_list) {
        console.log(`属性组: ${group.attr_group_name}`);
        for (const attr of group.attr_list) {
          attr.available = this.isAttrAvailable(product, group.attr_group_id, attr.attr_id);
          console.log(`  ${attr.attr_name}: ${attr.available ? '可用' : '不可用'}`);
        }
      }
      
      console.log('=== 属性可用性更新完成 ===');
    },

    /**
     * 获取当前价格
     */
    getCurrentPrice: function(product, sku) {
      if (sku && sku.price !== undefined) {
        return parseFloat(sku.price);
      }
      return parseFloat(product.price) || 0;
    },

    /**
     * 获取当前库存
     */
    getCurrentStock: function(product, sku) {
      // stock_mode = 0: 不限制库存，返回-1表示无限库存
      if (product.stock_mode == 0) {
        return -1;
      }

      // stock_mode = 1: 限制库存，需要检查具体库存数量
      if (product.is_attribute == 1) {
        // 多规格商品，使用SKU库存
        if (sku && sku.stock !== undefined) {
          return sku.stock;
        }
        return 0; // 没有匹配的SKU时库存为0
      } else {
        // 单规格商品，使用商品库存
        return product.stock || 0;
      }
    },

    /**
     * 获取最大可选择数量
     */
    getMaxSelectableQuantity: function(product, currentStock) {
      let maxByConfig = product.max_choose_num || 999;

      // stock_mode = 0: 不限制库存，只受配置限制
      if (product.stock_mode == 0) {
        return Math.max(maxByConfig, 1);
      }

      // stock_mode = 1: 限制库存，取配置和库存的最小值
      let maxByStock = currentStock === -1 ? 999 : currentStock;
      let maxQuantity = Math.min(maxByConfig, maxByStock);
      
      // 确保最大可选数量至少为1
      maxQuantity = Math.max(maxQuantity, 1);
      console.log('计算最大可选数量:', maxQuantity);
      
      return maxQuantity;
    },

    /**
     * 检查是否可以确认选择
     */
    checkCanConfirm: function(product) {
      if (product.is_attribute != 1) {
        return true; // 单规格商品直接可确认
      }

      if (!product.attr_group_list || product.attr_group_list.length === 0) {
        return true;
      }

      // 检查是否所有属性组都已选择
      let selectedCount = Object.keys(this.data.selectedAttrs).length;
      return selectedCount === product.attr_group_list.length;
    },

    /**
     * 格式化选中的属性为数组格式
     */
    formatSelectedAttrs: function(selectedAttrs) {
      const product = this.data.product;
      if (!selectedAttrs || !product.attr_group_list) {
        return [];
      }

      let formattedAttrs = [];
      
      for (const group of product.attr_group_list) {
        const selectedAttrId = selectedAttrs[group.attr_group_id];
        if (selectedAttrId) {
          const selectedAttr = group.attr_list.find(attr => attr.attr_id === selectedAttrId);
          if (selectedAttr) {
            formattedAttrs.push({
              attr_group_id: group.attr_group_id,
              attr_group_name: group.attr_group_name,
              attr_id: selectedAttr.attr_id,
              attr_name: selectedAttr.attr_name
            });
          }
        }
      }

      return formattedAttrs;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      console.log('=== SKU组件 attached ===');
      // 组件实例被放入页面节点树后执行
    },
    detached: function() {
      console.log('=== SKU组件 detached ===');
      // 组件实例被从页面节点树移除后执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show: function() {
      // 页面被展示
    },
    hide: function() {
      // 页面被隐藏
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible, product': function(visible, product) {
      console.log('=== observers 触发 ===');
      console.log('visible:', visible);
      console.log('product:', product);
      this._propertyChange();
    },
    'visible': function(visible) {
      console.log('=== visible 单独监听 ===', visible);
      if (visible) {
        // 确保弹窗能显示
        this.setData({
          showModal: true
        });
      }
    }
  }
});