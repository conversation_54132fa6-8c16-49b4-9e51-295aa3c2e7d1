/* component/sku-selector/sku-selector.wxss */

/* 遮罩层 */
.sku-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 1100;
  transition: background-color 0.3s ease;
  visibility: hidden;
}

.sku-mask.show {
  background-color: rgba(0, 0, 0, 0.5);
  visibility: visible;
}

/* 弹窗容器 */
.sku-modal {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1200;
  visibility: hidden;
}

.sku-modal.show {
  visibility: visible;
}

/* 弹窗内容 */
.sku-container {
  position: relative;
  width: 100%;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.sku-modal.show .sku-container {
  transform: translateY(0);
}

/* 商品信息区域 */
.sku-header {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.sku-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-right: 20rpx;
  border: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.sku-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sku-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.sku-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.sku-price {
  font-size: 36rpx;
  color: #ff4544;
  font-weight: bold;
}

.sku-stock {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.sku-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 50%;
  z-index: 10;
}

.sku-close:active {
  background-color: #e0e0e0;
}

/* 属性选择区域 */
.sku-body {
  max-height: 60vh;
  overflow-y: auto;
  padding: 0 30rpx;
}

.attr-group {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.attr-group:last-child {
  border-bottom: none;
}

.attr-group-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.attr-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.attr-item {
  min-width: 120rpx;
  height: 60rpx;
  padding: 0 20rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333;
  background-color: #f5f5f5;
  border: 1rpx solid transparent;
  transition: all 0.2s ease;
}

.attr-item.selected {
  color: #ff4544;
  background-color: #fff0f0;
  border-color: #ff4544;
}

.attr-item.disabled {
  color: #ccc;
  background-color: #f8f8f8;
  cursor: not-allowed;
}

/* 数量选择区域 */
.quantity-section {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #f5f5f5;
}

.quantity-title {
  font-size: 28rpx;
  color: #333;
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1rpx solid #e0e0e0;
  border-radius: 4rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
}

.quantity-btn:active {
  background-color: #e8e8e8;
}

.quantity-btn.disabled {
  color: #ccc;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background-color: #fff;
  border-left: 1rpx solid #e0e0e0;
  border-right: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部确认按钮 */
.sku-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #ff4544;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.confirm-btn:active {
  transform: scale(0.98);
  background-color: #e03e3e;
}

.confirm-btn.disabled {
  background-color: #cccccc;
}