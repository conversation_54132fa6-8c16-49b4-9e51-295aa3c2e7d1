<!--component/sku-selector/sku-selector.wxml-->
<view class="sku-mask {{showModal ? 'show' : ''}}" bindtap="cancelSelection" catchtouchmove="stopPropagation"></view>
<view class="sku-modal {{showModal ? 'show' : ''}}" catchtouchmove="stopPropagation">
  <view class="sku-container" catchtap="stopPropagation">
    <!-- 商品信息 -->
    <view class="sku-header">
      <view class="sku-image">
        <image src="{{product.pic}}" mode="aspectFill"></image>
      </view>
      <view class="sku-info">
        <view class="sku-name">{{product.name}}</view>
        <view class="sku-price">¥{{currentPrice}}</view>
        <view class="sku-stock" wx:if="{{currentStock >= 0}}">库存: {{currentStock}}</view>
        <view class="sku-stock" wx:else>库存: 不限</view>
      </view>
      <view class="sku-close" bindtap="cancelSelection">×</view>
    </view>
    
    <!-- 属性选择 -->
    <view class="sku-body">
      <block wx:if="{{product.is_attribute == 1 && product.attr_group_list && product.attr_group_list.length > 0}}">
        <view class="attr-group" wx:for="{{product.attr_group_list}}" wx:key="attr_group_id" wx:for-item="group">
          <view class="attr-group-name">{{group.attr_group_name}}</view>
          <view class="attr-list">
            <view 
              wx:for="{{group.attr_list}}" 
              wx:key="attr_id" 
              class="attr-item {{selectedAttrs[group.attr_group_id] === item.attr_id ? 'selected' : ''}} {{item.available === false ? 'disabled' : ''}}"
              data-group-id="{{group.attr_group_id}}"
              data-attr-id="{{item.attr_id}}"
              bindtap="selectAttr"
            >
              {{item.attr_name}}
            </view>
          </view>
        </view>
      </block>
      
      <!-- 数量选择 -->
      <view class="quantity-section">
        <view class="quantity-title">数量</view>
        <view class="quantity-control">
          <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" catchtap="decreaseQuantity">-</view>
          <view class="quantity-input">{{quantity}}</view>
          <view class="quantity-btn {{quantity >= maxSelectableQuantity ? 'disabled' : ''}}" catchtap="increaseQuantity">+</view>
        </view>
      </view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="sku-footer">
      <view class="confirm-btn {{!canConfirm ? 'disabled' : ''}}" bindtap="confirmSelection">确定</view>
    </view>
  </view>
</view>