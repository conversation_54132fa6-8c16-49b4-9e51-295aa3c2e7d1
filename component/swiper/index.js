// component/swiper/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // banner_config: {
    //   type: Array,
    //   value: []
    // },
    banner_list: {
      type: Array,
      value: []
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    current: 0,
    imgwidth: 750,
    imgheights: [],
    banner_config: {
      indicatorDots: true, //小点
      indicatorColor: "white", //指示点颜色
      activeColor: "coral", //当前选中的指示点颜色
      autoplay: true, //是否自动轮播
      interval: 3000, //间隔时间
      duration: 500, //滑动时间
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    bindchange: function (e) {
      this.setData({
        current: e.detail.current
      })
    },
    imageLoad: function (e) {
      //获取图片真实宽度
      try {
        var imgwidth = e.detail.width,
          imgheight = e.detail.height,
          src = [],
          ratio = imgwidth / imgheight; //宽高比
        //console.log(e.target.dataset['src']);
        src.push(e.target.dataset['src']);
        //计算的高度值
        var viewHeight = 750 / ratio;
        var imgheight = viewHeight;
        var imgheights = this.data.imgheights;
        //把每一张图片的高度记录到数组里
        imgheights.push(imgheight);
        // console.log(imgheight);
        this.setData({
          imgheights: imgheights
        })
      } catch (e) {
        console.log(e);
        console.log('swiper组件自动高度失败');
      }
    }
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      console.log('在组件实例进入页面节点树时执行');
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
      console.log('在组件实例被从页面节点树移除时执行');
    },
  },
})