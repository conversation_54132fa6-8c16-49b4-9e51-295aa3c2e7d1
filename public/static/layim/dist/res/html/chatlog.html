<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title>演示聊天记录模板</title>
  <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/layui/2.9.18/css/layui.css">
  <style>
    html{background-color: #f5f5f5;}
    body .layim-chat-main{height: auto;}
  </style>
</head>
<body>

<div class="layim-chat-main">
  <ul id="LAY_view"></ul>
</div>

<div id="LAY_page" style="margin: 0 10px;"></div>

<textarea title="消息模版" id="LAY_tpl" style="display:none;">
{{#
var contentParser = layui.layim.callback('contentParser');
layui.each(d.data, function(index, item){
  item.avatar = item.avatar || '../images/default.png';
  if(item.id == layui.layim.cache().user.id) {
}}
  <li class="layim-chat-role-user"><div class="layim-chat-userinfo"><img src="{{= item.avatar }}"><cite><i>{{= layui.util.toDateString(item.timestamp) }}</i>{{= item.username }}</cite></div><div class="layim-chat-text">{{- contentParser(item.content) }}</div></li>
{{# } else { }}
  <li><div class="layim-chat-userinfo"><img src="{{= item.avatar }}"><cite>{{= item.username }}<i>{{= layui.util.toDateString(item.timestamp) }}</i></cite></div><div class="layim-chat-text">{{- contentParser(item.content) }}</div></li>
{{# }
}); }}
</textarea>

<!--
上述模版采用了 laytpl 语法
-->

<script src="//cdnjs.cloudflare.com/ajax/libs/layui/2.9.18/layui.js"></script>
<script>
layui.link('../layim.css', 'skinlayimcss') //加载 css
layui.config({
  layimPath: '../../', // 配置 layim.js 所在目录
  layimResPath: '../' // layim 资源文件所在目录
}).use(['jquery'], function(){
  var layim = parent.layui.layim;
  var laytpl = parent.layui.laytpl;
  var $ = layui.jquery;
  var laypage = parent.layui.laypage;

  //  聊天记录的分页此处不做演示，你可以采用 laypage

  // 开始请求聊天记录
  var param =  location.search; // 获得 URL 参数。该窗口 url 会携带会话 id 和 type，他们是你请求聊天记录的重要凭据

  // 实际使用时，下述的 res 一般是通过Ajax获得，而此处仅仅只是演示数据格式
  var res = {
    "code": 0,
    "msg": "",
    "data": [{
      "username": "我",
      "id": 100000,
      "avatar": "",
      "timestamp": 1480897882000,
      "content": "我方模拟记录 111"
    }, {
      "username": "test123",
      "id": 108101,
      "avatar": "",
      "timestamp": 1480897892000,
      "content": "对方模拟记录 111"
    }, {
      "username": "test123",
      "id": 108101,
      "avatar": "",
      "timestamp": 1480897898000,
      "content": "对方模拟记录 222"
    }, {
      "username": "test123",
      "id": 108101,
      "avatar": "",
      "timestamp": 1480897908000,
      "content": "注意：该页面为 chatLog 参数指向的自定义页面。此页仅为聊天记录的模拟数据，实际使用时请进行相应修改。"
    }]
  }

  // console.log(param)

  var html = laytpl(LAY_tpl.value).render({
    data: res.data
  });
  $('#LAY_view').html(html);

});
</script>
</body>
</html>
