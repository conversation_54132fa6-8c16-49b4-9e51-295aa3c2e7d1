/** The Web UI Theme-v4.1.0 */;layui.define(function(M){R=(i=[]).concat,o=i.filter,l=i.slice,f=window.document,h={},e={},z={"column-count":1,columns:1,"font-weight":1,"line-height":1,opacity:1,"z-index":1,zoom:1},p=/^\s*<(\w+|!)[^>]*>/,Z=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,q=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,H=/^(?:body|html)$/i,I=/([A-Z])/g,V=["val","css","html","text","data","width","height","offset"],t=f.createElement("table"),_=f.createElement("tr"),B={tr:f.createElement("tbody"),tbody:t,thead:t,tfoot:t,td:_,th:_,"*":f.createElement("div")},U=/complete|loaded|interactive/,X=/^[\w-]*$/,W=(J={}).toString,d={},Y=f.createElement("div"),G={tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},m=Array.isArray||function(t){return t instanceof Array},d.matches=function(t,e){var n,r;return!(!e||!t||1!==t.nodeType)&&((n=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.oMatchesSelector||t.matchesSelector)?n.call(t,e):((r=!(n=t.parentNode))&&(n=Y).appendChild(t),n=~d.qsa(n,e).indexOf(t),r&&Y.removeChild(t),n))},s=function(t){return t.replace(/-+(.)?/g,function(t,e){return e?e.toUpperCase():""})},n=function(n){return o.call(n,function(t,e){return n.indexOf(t)==e})},d.fragment=function(t,e,n){var r,i,o;return(r=Z.test(t)?c(f.createElement(RegExp.$1)):r)||(t.replace&&(t=t.replace(q,"<$1></$2>")),e===u&&(e=p.test(t)&&RegExp.$1),(o=B[e=e in B?e:"*"]).innerHTML=""+t,r=c.each(l.call(o.childNodes),function(){o.removeChild(this)})),N(n)&&(i=c(r),c.each(n,function(t,e){-1<V.indexOf(t)?i[t](e):i.attr(t,e)})),r},d.Z=function(t,e){return new Tt(t,e)},d.isZ=function(t){return t instanceof d.Z},d.init=function(t,e){var n,r;if(!t)return d.Z();if("string"==typeof t)if("<"==(t=t.trim())[0]&&p.test(t))n=d.fragment(t,RegExp.$1,e),t=null;else{if(e!==u)return c(e).find(t);n=d.qsa(f,t)}else{if(S(t))return c(f).ready(t);if(d.isZ(t))return t;if(m(t))r=t,n=o.call(r,function(t){return null!=t});else if(C(t))n=[t],t=null;else if(p.test(t))n=d.fragment(t.trim(),RegExp.$1,e),t=null;else{if(e!==u)return c(e).find(t);n=d.qsa(f,t)}}return d.Z(n,t)},(c=function(t,e){return d.init(t,e)}).extend=function(e){var n,t=l.call(arguments,1);return"boolean"==typeof e&&(n=e,e=t.shift()),t.forEach(function(t){!function t(e,n,r){for(a in n)r&&(N(n[a])||m(n[a]))?(N(n[a])&&!N(e[a])&&(e[a]={}),m(n[a])&&!m(e[a])&&(e[a]=[]),t(e[a],n[a],r)):n[a]!==u&&(e[a]=n[a])}(e,t,n)}),e},d.qsa=function(t,e){var n,r="#"==e[0],i=!r&&"."==e[0],o=r||i?e.slice(1):e,a=X.test(o);return t.getElementById&&a&&r?(n=t.getElementById(o))?[n]:[]:1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType?[]:l.call(a&&!r&&t.getElementsByClassName?i?t.getElementsByClassName(o):t.getElementsByTagName(e):t.querySelectorAll(e))},c.contains=f.documentElement.contains?function(t,e){return t!==e&&t.contains(e)}:function(t,e){for(;e=e&&e.parentNode;)if(e===t)return!0;return!1},c.type=T,c.isFunction=S,c.isWindow=yt,c.isArray=m,c.isPlainObject=N,c.isEmptyObject=function(t){for(var e in t)return!1;return!0},c.isNumeric=function(t){var e=Number(t),n=typeof t;return null!=t&&"boolean"!=n&&("string"!=n||t.length)&&!isNaN(e)&&isFinite(e)||!1},c.inArray=function(t,e,n){return i.indexOf.call(e,t,n)},c.camelCase=s,c.trim=function(t){return null==t?"":String.prototype.trim.call(t)},c.uuid=0,c.support={},c.expr={},c.noop=function(){},c.map=function(t,e){var n,r,i,o,a=[];if(bt(t))for(r=0;r<t.length;r++)null!=(n=e(t[r],r))&&a.push(n);else for(i in t)null!=(n=e(t[i],i))&&a.push(n);return 0<(o=a).length?c.fn.concat.apply([],o):o},c.each=function(t,e){var n,r;if(bt(t)){for(n=0;n<t.length;n++)if(!1===e.call(t[n],n,t[n]))return t}else for(r in t)if(!1===e.call(t[r],r,t[r]))return t;return t},c.grep=function(t,e){return o.call(t,e)},window.JSON&&(c.parseJSON=JSON.parse),c.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){J["[object "+e+"]"]=e.toLowerCase()}),c.fn={constructor:d.Z,length:0,forEach:i.forEach,reduce:i.reduce,push:i.push,sort:i.sort,splice:i.splice,indexOf:i.indexOf,concat:function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=d.isZ(t=arguments[n])?t.toArray():t;return R.apply(d.isZ(this)?this.toArray():this,e)},map:function(n){return c(c.map(this,function(t,e){return n.call(t,e,t)}))},slice:function(){return c(l.apply(this,arguments))},ready:function(t){return U.test(f.readyState)&&f.body?t(c):f.addEventListener("DOMContentLoaded",function(){t(c)},!1),this},get:function(t){return t===u?l.call(this):this[0<=t?t:t+this.length]},toArray:function(){return this.get()},size:function(){return this.length},remove:function(){return this.each(function(){null!=this.parentNode&&this.parentNode.removeChild(this)})},each:function(n){return i.every.call(this,function(t,e){return!1!==n.call(t,e,t)}),this},filter:function(e){return S(e)?this.not(this.not(e)):c(o.call(this,function(t){return d.matches(t,e)}))},add:function(t,e){return c(n(this.concat(c(t,e))))},is:function(t){return 0<this.length&&d.matches(this[0],t)},not:function(e){var n,r=[];return S(e)&&e.call!==u?this.each(function(t){e.call(this,t)||r.push(this)}):(n="string"==typeof e?this.filter(e):bt(e)&&S(e.item)?l.call(e):c(e),this.forEach(function(t){n.indexOf(t)<0&&r.push(t)})),c(r)},has:function(t){return this.filter(function(){return C(t)?c.contains(this,t):c(this).find(t).size()})},eq:function(t){return-1===t?this.slice(t):this.slice(t,+t+1)},first:function(){var t=this[0];return t&&!C(t)?t:c(t)},last:function(){var t=this[this.length-1];return t&&!C(t)?t:c(t)},find:function(t){var n=this,e=t?"object"==typeof t?c(t).filter(function(){var e=this;return i.some.call(n,function(t){return c.contains(t,e)})}):1==this.length?c(d.qsa(this[0],t)):this.map(function(){return d.qsa(this,t)}):c();return e},closest:function(n,r){var i=[],o="object"==typeof n&&c(n);return this.each(function(t,e){for(;e&&!(o?0<=o.indexOf(e):d.matches(e,n));)e=e!==r&&!xt(e)&&e.parentNode;e&&i.indexOf(e)<0&&i.push(e)}),c(i)},parents:function(t){for(var e=[],n=this;0<n.length;)n=c.map(n,function(t){if((t=t.parentNode)&&!xt(t)&&e.indexOf(t)<0)return e.push(t),t});return St(e,t)},parent:function(t){return St(n(this.pluck("parentNode")),t)},children:function(t){return St(this.map(function(){return jt(this)}),t)},contents:function(){return this.map(function(){return this.contentDocument||l.call(this.childNodes)})},siblings:function(t){return St(this.map(function(t,e){return o.call(jt(e.parentNode),function(t){return t!==e})}),t)},empty:function(){return this.each(function(){this.innerHTML=""})},pluck:function(e){return c.map(this,function(t){return t[e]})},show:function(){return this.each(function(){var t,e,n;"none"==this.style.display&&(this.style.display=""),"none"==getComputedStyle(this,"").getPropertyValue("display")&&(this.style.display=(t=this.nodeName,h[t]||(e=f.createElement(t),f.body.appendChild(e),n=getComputedStyle(e,"").getPropertyValue("display"),e.parentNode.removeChild(e),h[t]=n="none"==n?"block":n),h[t]))})},replaceWith:function(t){return this.before(t).remove()},wrap:function(e){var n,r,i=S(e);return this[0]&&!i&&(n=c(e).get(0),r=n.parentNode||1<this.length),this.each(function(t){c(this).wrapAll(i?e.call(this,t):r?n.cloneNode(!0):n)})},wrapAll:function(t){if(this[0]){var e;for(c(this[0]).before(t=c(t));(e=t.children()).length;)t=e.first();c(t).append(this)}return this},wrapInner:function(r){var i=S(r);return this.each(function(t){var e=c(this),n=e.contents(),t=i?r.call(this,t):r;n.length?n.wrapAll(t):e.append(t)})},unwrap:function(){return this.parent().each(function(){c(this).replaceWith(c(this).children())}),this},clone:function(){return this.map(function(){return this.cloneNode(!0)})},hide:function(){return this.css("display","none")},toggle:function(e){return this.each(function(){var t=c(this);(e===u?"none"==t.css("display"):e)?t.show():t.hide()})},prev:function(t){return c(this.pluck("previousElementSibling")).filter(t||"*")},next:function(t){return c(this.pluck("nextElementSibling")).filter(t||"*")},html:function(n){return 0 in arguments?this.each(function(t){var e=this.innerHTML;c(this).empty().append(P(this,n,t,e))}):0 in this?this[0].innerHTML:null},text:function(e){return 0 in arguments?this.each(function(t){t=P(this,e,t,this.textContent);this.textContent=null==t?"":""+t}):0 in this?this.pluck("textContent").join(""):null},attr:function(e,n){var t;return"string"!=typeof e||1 in arguments?this.each(function(t){if(1===this.nodeType)if(C(e))for(a in e)Ct(this,a,e[a]);else Ct(this,e,P(this,n,t,this.getAttribute(e)))}):0 in this&&1==this[0].nodeType&&null!=(t=this[0].getAttribute(e))?t:u},removeAttr:function(t){return this.each(function(){1===this.nodeType&&t.split(" ").forEach(function(t){Ct(this,t)},this)})},prop:function(e,n){return e=G[e]||e,1 in arguments?this.each(function(t){this[e]=P(this,n,t,this[e])}):this[0]&&this[0][e]},removeProp:function(t){return t=G[t]||t,this.each(function(){delete this[t]})},data:function(t,e){t="data-"+t.replace(I,"-$1").toLowerCase(),e=1 in arguments?this.attr(t,e):this.attr(t);return null!==e?Nt(e):u},val:function(e){return 0 in arguments?(null==e&&(e=""),this.each(function(t){this.value=P(this,e,t,this.value)})):this[0]&&(this[0].multiple?c(this[0]).find("option").filter(function(){return this.selected}).pluck("value"):this[0].value)},offset:function(r){var t;return r?this.each(function(t){var e=c(this),t=P(this,r,t,e.offset()),n=e.offsetParent().offset(),t={top:t.top-n.top,left:t.left-n.left};"static"==e.css("position")&&(t.position="relative"),e.css(t)}):this.length?f.documentElement===this[0]||c.contains(f.documentElement,this[0])?{left:(t=this[0].getBoundingClientRect()).left+window.pageXOffset,top:t.top+window.pageYOffset,width:Math.round(t.width),height:Math.round(t.height)}:{top:0,left:0}:null},css:function(t,e){if(arguments.length<2){var n,r,i=this[0];if("string"==typeof t)return i?i.style[s(t)]||getComputedStyle(i,"").getPropertyValue(t):void 0;if(m(t))return i?(n={},r=getComputedStyle(i,""),c.each(t,function(t,e){n[e]=i.style[s(e)]||r.getPropertyValue(e)}),n):void 0}var o="";if("string"==T(t))e||0===e?o=O(t)+":"+Et(t,e):this.each(function(){this.style.removeProperty(O(t))});else for(a in t)t[a]||0===t[a]?o+=O(a)+":"+Et(a,t[a])+";":this.each(function(){this.style.removeProperty(O(a))});return this.each(function(){this.style.cssText+=";"+o})},index:function(t){return t?this.indexOf(c(t)[0]):this.parent().children().indexOf(this[0])},hasClass:function(t){return!!t&&i.some.call(this,function(t){return this.test(A(t))},wt(t))},addClass:function(n){return n?this.each(function(t){var e;"className"in this&&(r=[],e=A(this),P(this,n,t,e).split(/\s+/g).forEach(function(t){c(this).hasClass(t)||r.push(t)},this),r.length)&&A(this,e+(e?" ":"")+r.join(" "))}):this},removeClass:function(e){return this.each(function(t){if("className"in this){if(e===u)return A(this,"");r=A(this),P(this,e,t,r).split(/\s+/g).forEach(function(t){r=r.replace(wt(t)," ")}),A(this,r.trim())}})},toggleClass:function(n,r){return n?this.each(function(t){var e=c(this);P(this,n,t,A(this)).split(/\s+/g).forEach(function(t){(r===u?!e.hasClass(t):r)?e.addClass(t):e.removeClass(t)})}):this},scrollTop:function(t){var e;if(this.length)return e="scrollTop"in this[0],t===u?e?this[0].scrollTop:this[0].pageYOffset:this.each(e?function(){this.scrollTop=t}:function(){this.scrollTo(this.scrollX,t)})},scrollLeft:function(t){var e;if(this.length)return e="scrollLeft"in this[0],t===u?e?this[0].scrollLeft:this[0].pageXOffset:this.each(e?function(){this.scrollLeft=t}:function(){this.scrollTo(t,this.scrollY)})},position:function(){var t,e,n,r;if(this.length)return t=this[0],e=this.offsetParent(),n=this.offset(),r=H.test(e[0].nodeName)?{top:0,left:0}:e.offset(),n.top-=parseFloat(c(t).css("margin-top"))||0,n.left-=parseFloat(c(t).css("margin-left"))||0,r.top+=parseFloat(c(e[0]).css("border-top-width"))||0,r.left+=parseFloat(c(e[0]).css("border-left-width"))||0,{top:n.top-r.top,left:n.left-r.left}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||f.body;t&&!H.test(t.nodeName)&&"static"==c(t).css("position");)t=t.offsetParent;return t})}},c.fn.detach=c.fn.remove,["width","height"].forEach(function(r){var i=r.replace(/./,function(t){return t[0].toUpperCase()});c.fn[r]=function(e){var t,n=this[0];return e===u?yt(n)?n["inner"+i]:xt(n)?n.documentElement["scroll"+i]:(t=this.offset())&&t[r]:this.each(function(t){(n=c(this)).css(r,P(this,e,t,n[r]()))})}}),["after","prepend","before","append"].forEach(function(e,a){var s=a%2;c.fn[e]=function(){var n,r,i=c.map(arguments,function(t){var e=[];return"array"==(n=T(t))?(t.forEach(function(t){return t.nodeType!==u?e.push(t):c.zepto.isZ(t)?e=e.concat(t.get()):void(e=e.concat(d.fragment(t)))}),e):"object"==n||null==t?t:d.fragment(t)}),o=1<this.length;return i.length<1?this:this.each(function(t,e){r=s?e:e.parentNode,e=0==a?e.nextSibling:1==a?e.firstChild:2==a?e:null;var n=c.contains(f.documentElement,r);i.forEach(function(t){if(o)t=t.cloneNode(!0);else if(!r)return c(t).remove();r.insertBefore(t,e),n&&function t(e,n){n(e);for(var r=0,i=e.childNodes.length;r<i;r++)t(e.childNodes[r],n)}(t,function(t){var e;null==t.nodeName||"SCRIPT"!==t.nodeName.toUpperCase()||t.type&&"text/javascript"!==t.type||t.src||(e=t.ownerDocument?t.ownerDocument.defaultView:window).eval.call(e,t.innerHTML)})})})},c.fn[s?e+"To":"insert"+(a?"Before":"After")]=function(t){return c(t)[e](this),this}}),d.Z.prototype=Tt.prototype=c.fn,d.uniq=n,d.deserializeValue=Nt,c.zepto=d;var u,a,c,r,s,n,i,R,o,l,f,h,e,z,p,Z,q,H,I,V,_,B,U,X,J,W,d,Y,G,m,g,v,K,Q,tt,y,x,et,nt,rt,it,ot,at,st,b,ut,w,ct,E,lt,ft,ht,pt,dt,mt,gt,vt,j,t=c;function T(t){return null==t?String(t):J[W.call(t)]||"object"}function S(t){return"function"==T(t)}function yt(t){return null!=t&&t==t.window}function xt(t){return null!=t&&t.nodeType==t.DOCUMENT_NODE}function C(t){return"object"==T(t)}function N(t){return C(t)&&!yt(t)&&Object.getPrototypeOf(t)==Object.prototype}function bt(t){var e=!!t&&"length"in t&&t.length,n=c.type(t);return"function"!=n&&!yt(t)&&("array"==n||0===e||"number"==typeof e&&0<e&&e-1 in t)}function O(t){return t.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()}function wt(t){return t in e?e[t]:e[t]=new RegExp("(^|\\s)"+t+"(\\s|$)")}function Et(t,e){return"number"!=typeof e||z[O(t)]?e:e+"px"}function jt(t){return"children"in t?l.call(t.children):c.map(t.childNodes,function(t){if(1==t.nodeType)return t})}function Tt(t,e){for(var n=t?t.length:0,r=0;r<n;r++)this[r]=t[r];this.length=n,this.selector=e||""}function St(t,e){return null==e?c(t):c(t).filter(e)}function P(t,e,n,r){return S(e)?e.call(t,n,r):e}function Ct(t,e,n){null==n?t.removeAttribute(e):t.setAttribute(e,n)}function A(t,e){var n=t.className||"",r=n&&n.baseVal!==u;if(e===u)return r?n.baseVal:n;r?n.baseVal=e:t.className=e}function Nt(e){try{return e&&("true"==e||"false"!=e&&("null"==e?null:+e+""==e?+e:/^[\[\{]/.test(e)?c.parseJSON(e):e))}catch(t){return e}}function D(t){return"string"==typeof t}function L(t){return t._zid||(t._zid=K++)}function Ot(t,e,n,r){var i,o;return(e=Pt(e)).ns&&(o=e.ns,i=new RegExp("(?:^| )"+o.replace(" "," .* ?")+"(?: |$)")),(y[L(t)]||[]).filter(function(t){return t&&(!e.e||t.e==e.e)&&(!e.ns||i.test(t.ns))&&(!n||L(t.fn)===L(n))&&(!r||t.sel==r)})}function Pt(t){t=(""+t).split(".");return{e:t[0],ns:t.slice(1).sort().join(" ")}}function At(t,e){return t.del&&!et&&t.e in nt||!!e}function Dt(t){return rt[t]||et&&nt[t]||t}function Lt(r,t,i,o,a,s,u){var e=L(r),c=y[e]||(y[e]=[]);t.split(/\s/).forEach(function(t){if("ready"==t)return g(document).ready(i);var e=Pt(t),n=(e.fn=i,e.sel=a,e.e in rt&&(i=function(t){t=t.relatedTarget;if(!t||t!==this&&!g.contains(this,t))return e.fn.apply(this,arguments)}),(e.del=s)||i);e.proxy=function(t){var e;if(!(t=Ft(t)).isImmediatePropagationStopped())return t.data=o,!1===(e=n.apply(r,t._args==v?[t]:[t].concat(t._args)))&&(t.preventDefault(),t.stopPropagation()),e},e.i=c.length,c.push(e),"addEventListener"in r&&r.addEventListener(Dt(e.e),e.proxy,At(e,u))})}function $t(e,t,n,r,i){var o=L(e);(t||"").split(/\s/).forEach(function(t){Ot(e,t,n,r).forEach(function(t){delete y[o][t.i],"removeEventListener"in e&&e.removeEventListener(Dt(t.e),t.proxy,At(t,i))})})}function Ft(r,i){return(i||!r.isDefaultPrevented)&&(i=i||r,g.each(st,function(t,e){var n=i[t];r[t]=function(){return this[e]=it,n&&n.apply(i,arguments)},r[e]=ot}),r.timeStamp||(r.timeStamp=Date.now()),i.defaultPrevented!==v?i.defaultPrevented:"returnValue"in i?!1===i.returnValue:i.getPreventDefault&&i.getPreventDefault())&&(r.isDefaultPrevented=it),r}function kt(t){var e,n={originalEvent:t};for(e in t)at.test(e)||t[e]===v||(n[e]=t[e]);return Ft(n,t)}function $(t,e,n,r){if(t.global)return t=e||E,e=n,n=r,e=b.Event(e),b(t).trigger(e,n),!e.isDefaultPrevented()}function Mt(t,e){var n=e.context;if(!1===e.beforeSend.call(n,t,e)||!1===$(e,n,"ajaxBeforeSend",[t,e]))return!1;$(e,n,"ajaxSend",[t,e])}function Rt(t,e,n,r){var i=n.context,o="success";n.success.call(i,t,o,e),r&&r.resolveWith(i,[t,o,e]),$(n,i,"ajaxSuccess",[e,n,t]),zt(o,e,n)}function F(t,e,n,r,i){var o=r.context;r.error.call(o,n,e,t),i&&i.rejectWith(o,[n,e,t]),$(r,o,"ajaxError",[n,r,t||e]),zt(e,n,r)}function zt(t,e,n){var r=n.context;n.complete.call(r,e,t),$(n,r,"ajaxComplete",[e,n]),(t=n).global&&!--b.active&&$(t,null,"ajaxStop")}function k(){}function Zt(t,e){return""==e?t:(t+"&"+e).replace(/[&?]{1,2}/,"?")}function qt(t,e,n,r){return b.isFunction(e)&&(r=n,n=e,e=void 0),b.isFunction(n)||(r=n,n=void 0),{url:t,data:e,success:n,dataType:r}}g=t,K=1,Q=Array.prototype.slice,tt=g.isFunction,y={},x={},et="onfocusin"in window,nt={focus:"focusin",blur:"focusout"},rt={mouseenter:"mouseover",mouseleave:"mouseout"},x.click=x.mousedown=x.mouseup=x.mousemove="MouseEvents",g.event={add:Lt,remove:$t},g.proxy=function(t,e){var n,r=2 in arguments&&Q.call(arguments,2);if(tt(t))return(n=function(){return t.apply(e,r?r.concat(Q.call(arguments)):arguments)})._zid=L(t),n;if(D(e))return r?(r.unshift(t[e],t),g.proxy.apply(null,r)):g.proxy(t[e],t);throw new TypeError("expected function")},g.fn.bind=function(t,e,n){return this.on(t,e,n)},g.fn.unbind=function(t,e){return this.off(t,e)},g.fn.one=function(t,e,n,r){return this.on(t,e,n,r,1)},it=function(){return!0},ot=function(){return!1},at=/^([A-Z]|returnValue$|layer[XY]$|webkitMovement[XY]$)/,st={preventDefault:"isDefaultPrevented",stopImmediatePropagation:"isImmediatePropagationStopped",stopPropagation:"isPropagationStopped"},g.fn.delegate=function(t,e,n){return this.on(e,t,n)},g.fn.undelegate=function(t,e,n){return this.off(e,t,n)},g.fn.live=function(t,e){return g(document.body).delegate(this.selector,t,e),this},g.fn.die=function(t,e){return g(document.body).undelegate(this.selector,t,e),this},g.fn.on=function(e,r,i,o,a){var s,u,n=this;return e&&!D(e)?(g.each(e,function(t,e){n.on(t,r,i,e,a)}),n):(D(r)||tt(o)||!1===o||(o=i,i=r,r=v),o!==v&&!1!==i||(o=i,i=v),!1===o&&(o=ot),n.each(function(t,n){a&&(s=function(t){return $t(n,t.type,o),o.apply(this,arguments)}),Lt(n,e,o,i,r,(u=r?function(t){var e=g(t.target).closest(r,n).get(0);if(e&&e!==n)return t=g.extend(kt(t),{currentTarget:e,liveFired:n}),(s||o).apply(e,[t].concat(Q.call(arguments,1)))}:u)||s)}))},g.fn.off=function(t,n,e){var r=this;return t&&!D(t)?(g.each(t,function(t,e){r.off(t,n,e)}),r):(D(n)||tt(e)||!1===e||(e=n,n=v),!1===e&&(e=ot),r.each(function(){$t(this,t,e,n)}))},g.fn.trigger=function(t,e){return(t=D(t)||g.isPlainObject(t)?g.Event(t):Ft(t))._args=e,this.each(function(){t.type in nt&&"function"==typeof this[t.type]?this[t.type]():"dispatchEvent"in this?this.dispatchEvent(t):g(this).triggerHandler(t,e)})},g.fn.triggerHandler=function(n,r){var i,o;return this.each(function(t,e){(i=kt(D(n)?g.Event(n):n))._args=r,i.target=e,g.each(Ot(e,n.type||n),function(t,e){if(o=e.proxy(i),i.isImmediatePropagationStopped())return!1})}),o},"focusin focusout focus blur load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select keydown keypress keyup error".split(" ").forEach(function(e){g.fn[e]=function(t){return 0 in arguments?this.bind(e,t):this.trigger(e)}}),g.Event=function(t,e){D(t)||(t=(e=t).type);var n=document.createEvent(x[t]||"Events"),r=!0;if(e)for(var i in e)"bubbles"==i?r=!!e[i]:n[i]=e[i];return n.initEvent(t,r,!0),Ft(n)},b=t,ct=+new Date,E=window.document,lt=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,ft=/^(?:text|application)\/javascript/i,ht=/^(?:text|application)\/xml/i,pt="application/json",dt="text/html",mt=/^\s*$/,(gt=E.createElement("a")).href=window.location.href,b.active=0,b.ajaxJSONP=function(n,r){var t,i,o,a,s,e,u,c;return"type"in n?(t=n.jsonpCallback,i=(b.isFunction(t)?t():t)||"Zepto"+ct++,o=E.createElement("script"),a=window[i],u={abort:e=function(t){b(o).triggerHandler("error",t||"abort")}},r&&r.promise(u),b(o).on("load error",function(t,e){clearTimeout(c),b(o).off().remove(),"error"!=t.type&&s?Rt(s[0],u,n,r):F(null,e||"error",u,n,r),window[i]=a,s&&b.isFunction(a)&&a(s[0]),a=s=void 0}),!1===Mt(u,n)?e("abort"):(window[i]=function(){s=arguments},o.src=n.url.replace(/\?(.+)=\?/,"?$1="+i),E.head.appendChild(o),0<n.timeout&&(c=setTimeout(function(){e("timeout")},n.timeout))),u):b.ajax(n)},b.ajaxSettings={type:"GET",beforeSend:k,success:k,error:k,complete:k,context:null,global:!0,xhr:function(){return new window.XMLHttpRequest},accepts:{script:"text/javascript, application/javascript, application/x-javascript",json:pt,xml:"application/xml, text/xml",html:dt,text:"text/plain"},crossDomain:!1,timeout:0,processData:!0,cache:!0,dataFilter:k},b.ajax=function(t){var a=b.extend({},t||{}),s=b.Deferred&&b.Deferred();for(ut in b.ajaxSettings)void 0===a[ut]&&(a[ut]=b.ajaxSettings[ut]);(e=a).global&&0==b.active++&&$(e,null,"ajaxStart"),a.crossDomain||((e=E.createElement("a")).href=a.url,e.href=e.href,a.crossDomain=gt.protocol+"//"+gt.host!=e.protocol+"//"+e.host),a.url||(a.url=window.location.toString()),-1<(e=a.url.indexOf("#"))&&(a.url=a.url.slice(0,e)),(e=a).processData&&e.data&&"string"!=b.type(e.data)&&(e.data=b.param(e.data,e.traditional)),!e.data||e.type&&"GET"!=e.type.toUpperCase()&&"jsonp"!=e.dataType||(e.url=Zt(e.url,e.data),e.data=void 0);var u=a.dataType,e=/\?.+=\?/.test(a.url);if(e&&(u="jsonp"),!1!==a.cache&&(t&&!0===t.cache||"script"!=u&&"jsonp"!=u)||(a.url=Zt(a.url,"_="+Date.now())),"jsonp"==u)return e||(a.url=Zt(a.url,a.jsonp?a.jsonp+"=?":!1===a.jsonp?"":"callback=?")),b.ajaxJSONP(a,s);function n(t,e){r[t.toLowerCase()]=[t,e]}var c,t=a.accepts[u],r={},l=/^([\w-]+:)\/\//.test(a.url)?RegExp.$1:window.location.protocol,f=a.xhr(),i=f.setRequestHeader;if(s&&s.promise(f),a.crossDomain||n("X-Requested-With","XMLHttpRequest"),n("Accept",t||"*/*"),(t=a.mimeType||t)&&(-1<t.indexOf(",")&&(t=t.split(",",2)[0]),f.overrideMimeType)&&f.overrideMimeType(t),(a.contentType||!1!==a.contentType&&a.data&&"GET"!=a.type.toUpperCase())&&n("Content-Type",a.contentType||"application/x-www-form-urlencoded"),a.headers)for(w in a.headers)n(w,a.headers[w]);if(f.setRequestHeader=n,!(f.onreadystatechange=function(){if(4==f.readyState){f.onreadystatechange=k,clearTimeout(c);var t,e=!1;if(200<=f.status&&f.status<300||304==f.status||0==f.status&&"file:"==l){if(u=u||(o=(o=a.mimeType||f.getResponseHeader("content-type"))&&o.split(";",2)[0])&&(o==dt?"html":o==pt?"json":ft.test(o)?"script":ht.test(o)&&"xml")||"text","arraybuffer"==f.responseType||"blob"==f.responseType)t=f.response;else{t=f.responseText;try{n=t,r=u,t=(i=a).dataFilter==k?n:i.dataFilter.call(i.context,n,r),"script"==u?(0,eval)(t):"xml"==u?t=f.responseXML:"json"==u&&(t=mt.test(t)?null:b.parseJSON(t))}catch(t){e=t}if(e)return F(e,"parsererror",f,a,s)}Rt(t,f,a,s)}else F(f.statusText||null,f.status?"error":"abort",f,a,s)}var n,r,i,o})===Mt(f,a))f.abort(),F(null,"abort",f,a,s);else{e=!("async"in a)||a.async;if(f.open(a.type,a.url,e,a.username,a.password),a.xhrFields)for(w in a.xhrFields)f[w]=a.xhrFields[w];for(w in r)i.apply(f,r[w]);0<a.timeout&&(c=setTimeout(function(){f.onreadystatechange=k,f.abort(),F(null,"timeout",f,a,s)},a.timeout)),f.send(a.data||null)}return f},b.get=function(){return b.ajax(qt.apply(null,arguments))},b.post=function(){var t=qt.apply(null,arguments);return t.type="POST",b.ajax(t)},b.getJSON=function(){var t=qt.apply(null,arguments);return t.dataType="json",b.ajax(t)},b.fn.load=function(t,e,n){var r,i,o,a;return this.length&&(r=this,i=t.split(/\s/),t=qt(t,e,n),a=t.success,1<i.length&&(t.url=i[0],o=i[1]),t.success=function(t){r.html(o?b("<div>").html(t.replace(lt,"")).find(o):t),a&&a.apply(r,arguments)},b.ajax(t)),this},vt=encodeURIComponent,b.param=function(t,e){var n=[];return n.add=function(t,e){null==(e=b.isFunction(e)?e():e)&&(e=""),this.push(vt(t)+"="+vt(e))},function n(r,t,i,o){var a,s=b.isArray(t),u=b.isPlainObject(t);b.each(t,function(t,e){a=b.type(e),o&&(t=i?o:o+"["+(u||"object"==a||"array"==a?t:"")+"]"),!o&&s?r.add(e.name,e.value):"array"==a||!i&&"object"==a?n(r,e,i,t):r.add(t,e)})}(n,t,e),n.join("&").replace(/%20/g,"+")},(j=t).fn.serializeArray=function(){function n(t){if(t.forEach)return t.forEach(n);e.push({name:r,value:t})}var r,i,e=[];return this[0]&&j.each(this[0].elements,function(t,e){i=e.type,(r=e.name)&&"fieldset"!=e.nodeName.toLowerCase()&&!e.disabled&&"submit"!=i&&"reset"!=i&&"button"!=i&&"file"!=i&&("radio"!=i&&"checkbox"!=i||e.checked)&&n(j(e).val())}),e},j.fn.serialize=function(){var e=[];return this.serializeArray().forEach(function(t){e.push(encodeURIComponent(t.name)+"="+encodeURIComponent(t.value))}),e.join("&")},j.fn.submit=function(t){return 0 in arguments?this.bind("submit",t):this.length&&(t=j.Event("submit"),this.eq(0).trigger(t),t.isDefaultPrevented()||this.get(0).submit()),this};try{getComputedStyle(void 0)}catch(t){var Ht=getComputedStyle;window.getComputedStyle=function(t,e){try{return Ht(t,e)}catch(t){return null}}}M("zepto",t)});