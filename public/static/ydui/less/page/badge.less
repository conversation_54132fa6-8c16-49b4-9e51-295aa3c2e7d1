.badge {
  color: @badge-default-color;
  font-size: 12px;
  position: relative;
  display: inline-block;
  border-radius: 1000px;
  line-height: 1;
  padding: 3px 6px;
  white-space: nowrap;
  background-color: @badge-default-bg;
  &:after {
    content: '';
    width: 200%;
    height: 200%;
    border: 1px solid @badge-default-bg;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 1rem;
    transform-origin: 0 0;
    transform: scale(.5);
  }
}

.badge-radius {
  border-radius: 2px;
  &:after {
    border-radius: 2px;
  }
}

.badge-primary {
  background-color: @badge-primary-bg;
  color: #FFF;
  &:after {
    border-color: @badge-primary-bg;
  }
}

.badge-danger {
  background-color: @badge-danger-bg;
  color: #FFF;
  &:after {
    border-color: @badge-danger-bg;
  }
}

.badge-warning {
  background-color: @badge-warning-bg;
  color: #FFF;
  &:after {
    border-color: @badge-warning-bg;
  }
}

.badge-hollow {
  background-color: @badge-hollow-bg;
  color: @badge-hollow-color;
  &:after {
    border-color: @badge-hollow-color;
  }
}
