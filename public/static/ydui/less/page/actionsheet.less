.m-actionsheet {
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: @base-zindex * 1000;
  background-color: @actionsheet-bg;
  transform: translate(0, 100%);
  transition: transform .3s;
}

.actionsheet-item {
  display: block;
  position: relative;
  font-size: @actionsheet-item-fontsize;
  color: @actionsheet-item-color;
  height: @actionsheet-item-height;
  line-height: @actionsheet-item-height;
  background-color: @actionsheet-item-bg;
  &:after {
    .bottom-line(@actionsheet-item-border-color, 2);
  }
}

.actionsheet-action {
  display: block;
  margin-top: .15rem;
  font-size: @actionsheet-action-fontsize;
  color: @actionsheet-action-color;
  height: @actionsheet-action-height;
  line-height: @actionsheet-action-height;
  background-color: @actionsheet-action-bg;
}

.actionsheet-toggle {
  transform: translate(0, 0);
}
