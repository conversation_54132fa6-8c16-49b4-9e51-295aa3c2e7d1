.m-spinner {
  border: 1px solid #EAE8E8;
  border-radius: 1px;
  display: inline-block;
  overflow: hidden;
  > a {
    float: left;
    width: .6rem;
    height: .6rem;
    line-height: .62rem;
    text-align: center;
    font-weight: bold;
    color: #666;
    letter-spacing: 0;
    .tap-color(#F8F8F8, .95);
    &:after {
      font-family: @iconfont-inlay;
      color: #777;
      font-size: .18rem;
    }
    &:first-child:after {
      content: '\E60B';
    }
    &:last-child:after {
      content: '\E602'
    }
  }
  > input {
    letter-spacing: 0;
    float: left;
    width: .85rem;
    height: .6rem;
    line-height: 2.7;
    text-align: center;
    color: #666;
    border: none;
    font-size: .26rem;
  }
}
