@pullrefresh: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAABa1BMVEVTfvH///9TfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFAPDq2AAAAeHRSTlMAAAECAwQGBwkLDQ8QERMUFRkiIyYnLS4vMjM1Njc4OTpAQUJDREVGTVJWXmBiZmdoaWpxc3V8f4GCg4SGiIyNj5aXmZqbnJ+go6SlpqmqrK+wub2/wMHCxMXJzM3P0tPU1djd3t/n6Orr7O7w8fP09fb3+Pn6/f5+D/4+AAABqklEQVQ4y43V+T8CQRQA8GklUeQmhESuHBHJUY5EIWeHECGrcpVV++eb2Z3Y2d22eT+9mfm2n880894AQIRpKnDxVCh/sanD+c7/aS8PdDoCOqPfvCRu3c1VKaOOFC+P3LJBlAQ1h3m1uB8UpJRan/FaOX3i39jaj5fw+OeIJ6nzQ1y4crXhGeP43qfk63/UwQnjxDCxS4ufU9DeNzQqLTBAFn1y2vKIBi9WoAivnAZR/tqtJTG1lWFaHNKUmF6i1K0tRWpDWZLRliINoWykjhRo0ztMYqBuQGpHv3FR0TV07q1U9AzSO0BFM5BG6GgO0nU6ikpkhY5y9DQPqY9+W2E6ek7/Z/noj2CM/mCF6xKnouIlHKWiwtW+ZmqjGV8XpmLBLNWUHXm+Ep+VlGHJVkMaYuhLO/WKG4b+GC2yZqJlsCotAxhPhcqaBrJGtKjYW39akAEA6rS3dr/YxCN6CQWTuGkmXBY802gP4bndBiClYKDaiisP0e3NwEGyWG3NqwwgKWzwFbUGn52QnJbms+ExAjWqeIwyHhN5BxRPXLbAc+xNcK5Hdl1+ASkP8ND4fLD1AAAAAElFTkSuQmCC';

@pullrefreshLoading: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAABcVBMVEVTfvH///9TfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvFTfvG7eWgbAAAAenRSTlMAAAECAwQGBwkKCw0PEBETFBUZIiMmJy0uLzIzNTY3ODk6QEFCQ0RFRktNUlZeYGJmZ2hpanFzdXyBgoOEhoiMjY+Wl5mam5yfoKOkpqmqrK+wuLm6vb/AwcLExcnMzc/S09TV2N3e3+fo6uvs7vDx8/T19vf4+fr9/naKfqcAAAGISURBVDjLjZVVW0JBEEBXDLC7FbsVuzDAwO7E7sAORDi/3od7/T72Ajrztjvn4c7dmTNKaZHV7t2+eQ5/BI4Xewq1TFKSdmxZ+yIqTvszEqCNx1jjcTAtDpq9RLy4qIpBK27NXPh81TM+OecPmufvNgva8mYk9ly55o2jafYdYMuuo40hAA5qtCrzPCF20vUPKH0BCPbalCXKfOl6WZnXAPcV6q8wUB/AQ7H6H3WGgc9qJUB3AfqVAHUCHNok6DxArRKg9ldgX0nQegCXCB0FwjkidAM4UyL0CliWoY/AmAz9AoZlaEiOPgFueVlLMnRT/rPc8idokD+s/RXwi1CjCeuUuLWP/mjtTneRNjADCcmCJyL+rqgxDDoTkGn7ANOC4U5ZAQhka8oIxFOGYx2ADquI+mJqKz8HwPuv3vI9hsSXU6Kl2WpK88CVZ96k1s+bdzPJuoorf1UcuVybmvAuHH7+qnnEFiv4SDzB3zVL18aQQ7aMroayEu4tY8XdPRMKnPi6Syzt8gMGxsBO8KgZSQAAAABJRU5ErkJggg==';

.m-list {
  overflow: hidden;
  position: relative;
  .list-item:active {
    background: none; /* for firefox */
  }
}

.list-img {
  height: 0;
  width: 100%;
  padding: 50% 0;
  overflow: hidden;
  img {
    width: 100%;
    margin-top: -50%;
    background-color: #FFF;
    border: none;
  }
}

.list-mes {
  background-color: #FFF;
  .list-title {
    color: #505050;
    font-size: .26rem;
    text-align: justify;
    font-weight: 800;
  }
  .list-mes-item {
    overflow: hidden;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    color: #999;
  }
}

.list-price {
  font-size: .3rem;
  color: #EB5211;
  > em {
    font-size: .22rem;
  }
}

.list-del-price {
  padding-left: .06rem;
  font-size: .2rem;
  margin-left: .02rem;
  position: relative;
  color: #8C8C8C;
  &:after {
    .top-line(#8C8C8C);
    top: auto;
    bottom: 50%;
  }
}

.list-theme1 {
  padding: 0 2px 0;
  .list-item {
    width: 50%;
    float: left;
    padding: 0 2px;
    margin-top: 4px;
    .list-mes {
      padding: .1rem;
      .list-title {
        .text-overflow();
        height: .36rem;
      }
    }
  }
}

.list-theme2 {
  .list-item {
    width: 50%;
    float: left;
    padding-top: 4px;
    &:nth-child(odd) {
      padding-right: 2px;
    }
    &:nth-child(even) {
      padding-left: 2px;
    }
    .list-mes {
      padding: .1rem;
      .list-title {
        .text-overflow();
        height: .36rem;
      }
    }
  }
}

.list-theme3 {
  .list-item {
    width: 50%;
    float: left;
    padding: .2rem;
    position: relative;
    z-index: 0;
    background-color: #FFF;
    &:before {
      .bottom-line(@line-color);
    }
    &:nth-child(odd):after {
      .right-line(@line-color);
    }
    .list-mes {
      padding-top: .1rem;
      box-sizing: content-box;
      .list-title {
        .text-overflow();
        height: .35rem;
      }
    }
    &:active {
      background: #FFF; /* for old android */
    }
  }
}

.list-theme4 {
  padding: 0 7px;
  background-color: #FFF;
  .list-item {
    overflow: hidden;
    display: flex;
    padding: 7px 0 8px 0;
    position: relative;
    &:not(:last-child):after {
      .bottom-line(@line-color);
    }
    .list-img {
      width: 2rem;
      padding: 1rem 0;
    }
    .list-mes {
      flex: 1;
      padding-left: 7px;
      .list-title {
        .line-clamp(3, .38rem);
      }
      .list-mes-item {
        padding-top: .1rem;
      }
    }
  }
}

.list-theme5 {
  background-color: #FFF;
  .list-item {
    display: block;
    position: relative;
    z-index: 1;
    padding: .2rem .2rem 0 .2rem;
    &:after {
      .bottom-line(@line-color);
    }
    .list-mes {
      padding: .2rem 0 .15rem;
      .list-mes-item {
        padding-top: .06rem;
      }
    }
  }
}

@media screen and (min-width: 768px) {
  .list-theme1 {
    padding: 0 4px;
    .list-item {
      padding: 0 4px;
      margin-top: 8px;
    }
  }

  .list-theme2 {
    .list-item {
      padding-top: 8px;
      &:nth-child(odd) {
        padding-right: 4px;
      }
      &:nth-child(even) {
        padding-left: 4px;
      }
    }
  }

  .list-theme4 {
    padding: 0 9px;
    .list-item {
      padding: 9px 0 10px 0;
      .list-mes {
        padding-left: 9px;
      }
    }
  }
}

.list-loading {
  margin-top: .1rem;
  text-align: center;
  font-size: .26rem;
  color: #999;
  height: .66rem;
  line-height: .66rem;
  img {
    height: inherit;
    display: inline-block;
  }
}

.list-donetip {
  font-size: .24rem;
  text-align: center;
  padding: .25rem 0;
  color: #777;
}

.pullrefresh-animation-timing {
  transition: transform 150ms;
}

@keyframes backRotateAnimation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

.pullrefresh-dragtip {
  position: absolute;
  top: -46px;
  left: 50%;
  z-index: 996;
  transform: translate3d(0px, 0px, 0px);
  width: 42px;
  height: 42px;
  line-height: 42px;
  margin-left: -21px;
  border-radius: 50%;
  text-align: center;
  background-color: #FFF;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .25);
  > span {
    display: flex;
    justify-content: center;
    align-items: center;
    height: inherit;
    &:after {
      content: '';
      display: block;
      width: 20px;
      height: 20px;
      background: url(@pullrefresh) no-repeat;
      background-size: 20px 20px;
    }
    &.pullrefresh-loading:after {
      background: url(@pullrefreshLoading) no-repeat;
      background-size: 20px 20px;
      animation: backRotateAnimation .4s linear infinite;
    }
  }
}

.pullrefresh-draghelp {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;
  > div {
    width: 2.3rem;
    height: 2.3rem;
    background-color: rgba(0, 0, 0, .8);
    &:before {
      content: '\e60d';
      font-family: @iconfont-inlay;
      font-size: .88rem;
      text-align: center;
      color: #FFF;
      display: block;
      padding-top: .36rem;
    }
    > span {
      text-align: center;
      color: #FFF;
      font-size: .28rem;
      display: block;
      padding-top: .2rem;
    }
  }
}
