.m-scrolltab {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
}

.scrolltab-nav {
  height: 100%;
  background-color: #F5F5F5;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 1;
  &:after {
    .right-line(#DFDFDF);
  }
}

.scrolltab-item {
  padding: 0 .3rem;
  height: 1rem;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  &.crt {
    background-color: #FFF;
    &:before {
      .right-line(#FFF);
    }
    &:active {
      background-color: #FFF;
    }
  }
  &:after {
    .bottom-line(#DFDFDF);
  }
  &:before {
    .right-line(#DFDFDF);
  }
  &:active {
    background: none; /* for firefox */
  }
}

.scrolltab-icon {
  margin-right: .2rem;
  font-size: .32rem;
  display: flex;
  align-items: center;
  justify-content: center;
  > img {
    height: .4rem;
    display: inline-block;
  }
}

.scrolltab-title {
  font-size: .3rem;
  color: #666;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 1.6rem;
}

.scrolltab-content {
  height: 100%;
  background-color: #FFF;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  flex: 1;
  padding: 0 .24rem .24rem .24rem;
  position: relative;
}

.scrolltab-content-title {
  font-size: .3rem;
  font-weight: normal;
  color: #555;
  display: block;
  padding-bottom: .1rem;
  padding-top: .32rem;
  margin-bottom: .2rem;
  position: relative;
  z-index: 1;
  &:after {
    .bottom-line(@line-high-color);
  }
}
