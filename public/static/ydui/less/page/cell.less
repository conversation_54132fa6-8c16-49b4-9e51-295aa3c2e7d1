.m-celltitle {
  padding: 0 @body-padding-vertical .1rem;
  font-size: @cell-title-fontsize;
  text-align: left;
  color: @cell-title-color;
  position: relative;
  z-index: 1;
  &:after {
    .bottom-line(@line-color);
  }
}

.m-cell {
  background-color: #FFF;
  position: relative;
  z-index: 1;
  margin-bottom: .35rem;
  &:after {
    .bottom-line(@line-high-color);
  }
  a.cell-item,
  label.cell-item {
    .tap-color(#FFF, .96);
  }
}

.cell-item {
  display: flex;
  position: relative;
  padding-left: @body-padding-vertical;
  overflow: hidden;
  &:not(:last-child):after {
    margin-left: @body-padding-vertical;
    .bottom-line(@line-color);
  }
}

.cell-left {
  color: @cell-left-color;
  font-size: @cell-left-fontsize;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.cell-right {
  flex: 1;
  width: 100%;
  min-height: @cell-height;
  color: @cell-right-color;
  text-align: right;
  font-size: @cell-right-fontsize;
  padding-right: @body-padding-vertical;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  input[type="datetime-local"],
  input[type="date"],
  input[type="time"] {
    line-height: @cell-height;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
  input[type="radio"],
  input[type="checkbox"]:not(.m-switch) {
    -webkit-appearance: none;
    -moz-appearance: none;
    position: absolute;
    left: -9999em;
    & + .cell-radio-icon:after,
    & + .cell-checkbox-icon:after {
      font-family: @iconfont-inlay;
      font-size: .44rem;
    }
    & + .cell-radio-icon:after {
      content: '\e600';
      color: #4CD864;
      display: none;
    }
    & + .cell-checkbox-icon:after {
      content: '\e604';
      color: #D9D9D9;
    }
    &:checked {
      & + .cell-radio-icon:after {
        display: inline-block;
      }
      & + .cell-checkbox-icon:after {
        color: #4CD864;
        content: '\e601';
      }
    }
  }
  &:active {
    background: none; /* for firefox */
  }
}

.cell-input {
  flex: 1;
  height: @cell-height;
  border: none;
  font-size: @cell-input-fontsize;
  background: transparent;
  color: @cell-input-color;
  display: flex;
  justify-content: flex-start;
  text-align: left; /* fuck UC */
}

.cell-select {
  flex: 1;
  height: @cell-height;
  border: none;
  display: block;
  color: @cell-select-color;
  font-size: @cell-select-fontsize;
  margin-left: -.08rem; /* 去除select默认缩进 */
}

.cell-multiple-selecet {
  margin-right: 2%;
  flex-grow: 1;
  display: block;
  select {
    width: 100%;
    height: .6rem;
    border: 1px solid @line-color;
    border-radius: 2px;
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 2px;
    color: #A9A9A9;
    &:active {
      border-color: #888;
      background-color: #F2F2F2;
    }
    &:focus {
      border: none;
      background-color: #C00;
    }
  }
  &:last-child {
    margin-right: 0;
  }
}

.cell-icon {
  display: block;
  &:before, &:after {
    color: #A6A5A5;
    font-size: @cell-icon-fontsize !important;
    margin-right: .1rem;
  }
  img {
    height: .4rem;
    margin-right: .1rem;
  }
}

.cell-arrow {
  &:after {
    margin-left: .05rem;
    margin-right: -.08rem;
    display: block;
    font-family: @iconfont-inlay;
    font-size: @cell-arrow-fontsize;
    color: @cell-arrow-color;
    content: '\e608';
  }
}

.cell-textarea {
  width: 100%;
  border: none;
  display: block; /* for old android */
  height: 1.5rem;
  padding: .2rem 0;
}

.m-switch {
  -webkit-appearance: none;
  -moz-appearance: none;
  position: relative;
  display: block;
  width: 52px;
  height: 32px;
  left: 0;
  border: 1px solid #DFDFDF;
  border-radius: 16px;
  background-color: #DFDFDF;
  z-index: 2;
  &:before, &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 30px;
    border-radius: 15px;
    transition: transform .3s;
  }
  &:before {
    width: 50px;
    background-color: #FDFDFD;
  }
  &:after {
    width: 30px;
    background-color: #FFF;
    box-shadow: 0 1px 3px rgba(0, 0, 0, .4);
  }
  &:checked {
    border-color: #4CD864;
    background-color: #4CD864;
    &:before {
      transform: scale(0);
    }
    &:after {
      transform: translateX(20px);
    }
  }

}

.m-switch-old {
  &:checked + .m-switch {
    border-color: #4CD864;
    background-color: #4CD864;
    &:before {
      transform: scale(0);
    }
    &:after {
      transform: translateX(20px);
    }
  }
}
