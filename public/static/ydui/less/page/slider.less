.m-slider {
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

.slider-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transform: translate3d(0px, 0px, 0px);
  position: relative;
  z-index: 1;
  transition-property: transform;
}

.slider-item {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  img {
    width: 100%;
  }
}

.slider-pagination {
  position: absolute;
  width: 100%;
  z-index: 2;
  left: 0;
  bottom: .15rem;
  pointer-events: none;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  > .slider-pagination-item {
    margin: 0 .08rem;
    width: 6px;
    height: 6px;
    display: inline-block;
    border-radius: 100%;
    background-color: #B7D0E1;
    &.slider-pagination-item-active {
      background-color: #FF0005;
    }
  }
}
