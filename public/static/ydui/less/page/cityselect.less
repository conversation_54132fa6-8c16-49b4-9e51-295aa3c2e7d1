.m-cityselect {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 75%;
  z-index: @base-zindex * 1000;
  background-color: #fff;
  transform: translate(0, 100%);
  transition: transform .3s;
  &.brouce-in {
    transform: translate(0, 0);
  }
}

.cityselect-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  &:after {
    .bottom-line(@line-color);
  }
}

.cityselect-title {
  width: 100%;
  font-size: .3rem;
  text-align: center;
  height: 45px;
  line-height: 45px;
  position: relative;
  &:after {
    .bottom-line(@line-high-color);
  }
}

.cityselect-nav {
  width: 100%;
  padding-left: .2rem;
  overflow: hidden;
  display: flex;
  > a {
    font-size: .26rem;
    color: #222;
    display: block;
    height: 40px;
    line-height: 46px;
    padding: 0 .2rem;
    position: relative;
    margin-right: .15rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 40%;
    &.crt {
      color: #F23030;
      &:after {
        content: '';
        width: 100%;
        height: 2px;
        background-color: #F23030;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 2;
      }
    }
  }
}

.cityselect-content {
  height: 100%;
  padding-top: 85px;
  width: 100%;
  display: flex;
  &.cityselect-move-animate {
    transition: transform .3s;
  }
  &.cityselect-next {
    transform: translate(-50%, 0);
  }
  &.cityselect-prev {
    transform: translate(0, 0);
  }
  > .cityselect-item {
    display: block;
    height: inherit;
    width: 50%; /* for old android */
    flex: 0 0 50%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    background-color: #FFF;
    &::-webkit-scrollbar {
      width: 0;
    }
    &:nth-child(2n) {
      background-color: #F5F5F5;
    }
  }
}

.cityselect-item-box {
  width: 100%;
  height: inherit;
  display: block;
  padding: 0 .4rem;
  > a {
    color: #333;
    font-size: .26rem;
    height: 40px;
    line-height: 40px;
    overflow: hidden;
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    z-index: 1;
    &:before {
      .bottom-line(@line-color);
    }
    &:active {
      background: none; /* for firefox */
    }
    span {
      flex: 1;
      display: block;
    }
    &.crt {
      color: #F23030;
      &:after {
        display: block;
        content: '\E600';
        font-family: @iconfont-inlay;
      }
    }
  }
}
