/**
 * Component:	Variables
 * Description:	Define all variables
*/

//======== 【global】 ========
@base-zindex: 1;
@body-bg: #F5F5F5;
@body-padding-vertical: .24rem;
@body-padding-bottom: .5rem;
@line-color: #D9D9D9;
@line-high-color: #B2B2B2;
@max-width: 750px;
@min-width: 300px;

//======== 【button】 ========
@btn-radius: 3px;
@btn-fontsize: .26rem;
@btn-height: .7rem;
@btn-default-color: #FFF;
@btn-block-radius: 3px;
@btn-block-fontsize: .36rem;
@btn-block-height: 1rem;
@btn-block-margin-top: .5rem;
@btn-primary-bg: #04BE02;
@btn-danger-bg: #EF4F4F;
@btn-warning-bg: #FFB400;
@btn-disabled-bg: #CCC;
@btn-disabled-color: #F0F0F0;
@btn-hollow-bg: #FFF;
@btn-hollow-color: #454545;

//======== 【cell】 ========
@cell-title-fontsize: .3rem;
@cell-title-color: #888;
@cell-height: 1rem;
@cell-left-color: #333;
@cell-left-fontsize: .3rem;
@cell-right-color: #525252;
@cell-right-fontsize: .26rem;
@cell-input-color: #555;
@cell-input-fontsize: .3rem;
@cell-icon-fontsize: .42rem;
@cell-select-color: #A9A9A9;
@cell-select-fontsize: @cell-left-fontsize;
@cell-arrow-color: #C9C9C9;
@cell-arrow-fontsize: .34rem;

//======== 【grids】 ========
@grids-fontsize: .28rem;
@grids-color: #333;
@grids-bg: #FFF;
@grids-padding-horizontal: .32rem;
@grids-line-color: @line-color;
@grids-line-high-color: @line-high-color;

//======== 【navbar】 ========
@navbar-height: .9rem;
@navbar-bg: rgba(255, 255, 255, .98);
@navbar-center-width: 50%;
@navbar-center-fontsize: .4rem;
@navbar-center-color: #5C5C5C;
@navbar-item-fontsize: .3rem;
@navbar-item-color: #5C5C5C;
@navbar-icon-color: @navbar-item-color;
@navbar-icon-fontsize: .36rem;
@navbar-border-color: #B2B2B2;

//======== 【tabbar】 ========
@tabbar-bg: rgba(255, 255, 255, .96);
@tabbar-border-color: #B2B2B2;
@tabbar-color: #979797;
@tabbar-color-active: #09BB07;
@tabbar-text-fontsize: .24rem;
@tabbar-icon-color: @tabbar-color;
@tabbar-icon-fontsize: .54rem;

//======== 【actionsheet】 ========
@actionsheet-bg: #EFEFF4;
@actionsheet-item-height: 1rem;
@actionsheet-item-bg: #FFF;
@actionsheet-item-fontsize: .28rem;
@actionsheet-item-color: #555;
@actionsheet-item-border-color: @line-color;
@actionsheet-action-height: 1rem;
@actionsheet-action-bg: @actionsheet-item-bg;
@actionsheet-action-fontsize: @actionsheet-item-fontsize;
@actionsheet-action-color: @actionsheet-item-color;

//======== 【tab】 ========
@tab-bg: #FFF;
@tab-height: .85rem;
@tab-fontsize: .28rem;
@tab-color: #585858;
@tab-color-crt: #FF5E53;
@tab-break-color: @line-color;
@tab-bottom-border-color: @line-high-color;

//======== 【badge】 ========
@badge-default-bg: #D0D0D0;
@badge-default-color: #FFF;
@badge-primary-bg: #04BE02;
@badge-danger-bg: #EF4F4F;
@badge-warning-bg: #FFB400;
@badge-hollow-bg: #FBFBFB;
@badge-hollow-color: #B2B2B2;
