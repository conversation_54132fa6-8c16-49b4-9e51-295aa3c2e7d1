<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>YDUI</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="css/demo.css"/>
    <script src="js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <div class="g-scrollview">

        <h1 class="demo-pagetitle">YDUI</h1>
        <h2 class="demo-detail-title">一只注重审美，且性能高效的移动端&微信UI。</h2>

        <div class="m-grids-4">
            <a class="grids-item" href="html/button.html">
                <div class="grids-icon">
                    <i class="demo-icons-button"></i>
                </div>
                <div class="grids-txt">Button</div>
            </a>
            <a class="grids-item" href="html/dialog.html">
                <div class="grids-icon">
                    <i class="demo-icons-dialog"></i>
                </div>
                <div class="grids-txt">Dialog</div>
            </a>
            <a class="grids-item" href="html/cell.html">
                <div class="grids-icon">
                    <i class="demo-icons-cell"></i>
                </div>
                <div class="grids-txt">Cell</div>
            </a>
            <a class="grids-item" href="html/icons.html">
                <div class="grids-icon">
                    <i class="demo-icons-icons"></i>
                </div>
                <div class="grids-txt">Icons</div>
            </a>
            <a class="grids-item" href="html/grids.html">
                <div class="grids-icon">
                    <i class="demo-icons-grids"></i>
                </div>
                <div class="grids-txt">Grids</div>
            </a>
            <a class="grids-item" href="html/list.html">
                <div class="grids-icon">
                    <i class="demo-icons-list"></i>
                </div>
                <div class="grids-txt">List</div>
            </a>
            <a class="grids-item" href="html/badge.html">
                <div class="grids-icon">
                    <i class="demo-icons-badge"></i>
                </div>
                <div class="grids-txt">Badge</div>
            </a>
            <a class="grids-item" href="html/asidebar.html">
                <div class="grids-icon">
                    <i class="demo-icons-tabbar"></i>
                </div>
                <div class="grids-txt">AsideBar</div>
            </a>
            <a class="grids-item" href="html/tabs.html">
                <div class="grids-icon">
                    <i class="demo-icons-tabs"></i>
                </div>
                <div class="grids-txt">Tabs</div>
            </a>
            <a class="grids-item" href="html/actionsheet.html">
                <div class="grids-icon">
                    <i class="demo-icons-actionsheet"></i>
                </div>
                <div class="grids-txt">ActionSheet</div>
            </a>
            <a class="grids-item" href="html/sendcode.html">
                <div class="grids-icon">
                    <i class="demo-icons-sendcode"></i>
                </div>
                <div class="grids-txt">SendCode</div>
            </a>
            <a class="grids-item" href="html/progressbar.html">
                <div class="grids-icon">
                    <i class="demo-icons-progressbar"></i>
                </div>
                <div class="grids-txt">ProgressBar</div>
            </a>
            <a class="grids-item" href="html/keyboard.html">
                <div class="grids-icon">
                    <i class="demo-icons-keyboard"></i>
                </div>
                <div class="grids-txt">KeyBoard</div>
            </a>
            <a class="grids-item" href="html/slider.html">
                <div class="grids-icon">
                    <i class="demo-icons-slider"></i>
                </div>
                <div class="grids-txt">Slider</div>
            </a>
            <a class="grids-item" href="html/spinner.html">
                <div class="grids-icon">
                    <i class="demo-icons-spinner"></i>
                </div>
                <div class="grids-txt">Spinner</div>
            </a>
            <a class="grids-item" href="html/cityselect.html">
                <div class="grids-icon">
                    <i class="demo-icons-cityselect"></i>
                </div>
                <div class="grids-txt">CitySelect</div>
            </a>
        </div>

    </div>

</section>
<script src="http://www.jq22.com/jquery/jquery-1.10.2.js"></script>
<script src="js/ydui.js"></script>
<script>(!YDUI.device.isMobile && navigator.userAgent.indexOf('Firefox') >= 0) && YDUI.dialog.alert('PC端请使用谷歌内核浏览器查看！');</script>
</body>
</html>
