<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Grids</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">Grids</span></div>
    </header>

    <section class="g-scrollview">

        <div class="m-gridstitle">等分2列(带图标)</div>
        <ul class="m-grids-2">
            <li class="grids-item">
                <div class="grids-icon">
                    <img src="http://static.ydcss.com/ydui/img/logo.png" />
                </div>
                <div class="grids-txt">image</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="demo-icons-cell"></i>
                </div>
                <div class="grids-txt">iconfont</div>
            </li>
        </ul>

        <div class="m-gridstitle">等分3列</div>
        <ul class="m-grids-3">
            <li class="grids-item">
                <div class="grids-txt">grids-3</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-3</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-3</div>
            </li>
        </ul>

        <div class="m-gridstitle">等分4列</div>
        <ul class="m-grids-4">
            <li class="grids-item">
                <div class="grids-txt">grids-4</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-4</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-4</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-4</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-4</div>
            </li>
        </ul>

        <div class="m-gridstitle">等分5列</div>
        <ul class="m-grids-5">
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
            <li class="grids-item">
                <div class="grids-txt">grids-5</div>
            </li>
        </ul>

    </section>

</section>
</body>
</html>
