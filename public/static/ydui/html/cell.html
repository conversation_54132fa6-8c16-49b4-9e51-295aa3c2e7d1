<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Cell</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">Cell</span></div>
    </header>

    <section class="g-scrollview">

        <div class="m-cell demo-small-pitch">
            <div class="cell-item">
                <div class="cell-left">姓名：</div>
                <div class="cell-right"><input type="text" class="cell-input" placeholder="请输入您的姓名" autocomplete="off" /></div>
            </div>
            <div class="cell-item">
                <div class="cell-right cell-arrow"><input type="text" class="cell-input" placeholder="请输入您的姓名" autocomplete="off" /></div>
            </div>
            <div class="cell-item">
                <div class="cell-left">配送方式</div>
                <div class="cell-right">顺丰快递</div>
            </div>
        </div>

        <div class="m-celltitle">带图标的</div>
        <div class="m-cell">
            <div class="cell-item">
                <div class="cell-left"><i class="cell-icon demo-icons-phone"></i></div>
                <div class="cell-right">
                    <input type="number" pattern="[0-9]*" class="cell-input" placeholder="请输入手机号码" autocomplete="off" />
                    <a href="javascript:;" class="btn btn-warning">获取短信验证码</a>
                </div>
            </div>
            <a class="cell-item" href="javascript:;">
                <div class="cell-left"><i class="cell-icon demo-icons-like"></i>我的收藏</div>
                <div class="cell-right"><span class="badge badge-danger">8</span></div>
            </a>
            <a class="cell-item" href="tel:************">
                <div class="cell-left"><i class="cell-icon demo-icons-tel"></i>联系客服</div>
                <div class="cell-right cell-arrow">************</div>
            </a>
            <a class="cell-item" href="javascript:;">
                <div class="cell-left"><i class="cell-icon demo-icons-order"></i>我的订单</div>
                <div class="cell-right cell-arrow">查看全部订单</div>
            </a>
            <a class="cell-item" href="javascript:;">
                <div class="cell-left"><span class="cell-icon"><img src="http://static.ydcss.com/ydui/img/logo.png" /></span>图标</div>
                <div class="cell-right cell-arrow">图标是image</div>
            </a>
        </div>

        <div class="m-celltitle">时间们</div>
        <div class="m-cell">
            <div class="cell-item">
                <div class="cell-left">时间：</div>
                <div class="cell-right"><input class="cell-input" type="datetime-local" value="2016-07-19T08:08" placeholder=""/></div>
            </div>
            <div class="cell-item">
                <div class="cell-left">时间：</div>
                <div class="cell-right"><input class="cell-input" type="date" value="2016-07-19" placeholder=""/></div>
            </div>
            <div class="cell-item">
                <div class="cell-left">时间：</div>
                <div class="cell-right"><input class="cell-input" type="time" value="08:08" placeholder=""/></div>
            </div>
        </div>

        <div class="m-celltitle">下拉框</div>
        <div class="m-cell">
            <div class="cell-item">
                <label class="cell-right cell-arrow">
                    <select class="cell-select">
                        <option value="">支付方式</option>
                        <option value="1">支付宝</option>
                        <option value="2">微信</option>
                        <option value="3">财付通</option>
                    </select>
                </label>
            </div>
            <div class="cell-item">
                <div class="cell-left">性别：</div>
                <label class="cell-right cell-arrow">
                    <select class="cell-select">
                        <option value="">请选择性别</option>
                        <option value="1">男</option>
                        <option value="2">女</option>
                        <option value="3">未知</option>
                    </select>
                </label>
            </div>
        </div>

        <div class="m-celltitle">多选(checkbox改为radio样式通用)</div>
        <div class="m-cell">
            <label class="cell-item">
                <span class="cell-left">多选一</span>
                <label class="cell-right">
                    <input type="checkbox" value="1" name="checkbox"/>
                    <i class="cell-checkbox-icon"></i>
                </label>
            </label>
            <label class="cell-item">
                <span class="cell-left">多选二</span>
                <label class="cell-right">
                    <input type="checkbox" value="2" name="checkbox"/>
                    <i class="cell-checkbox-icon"></i>
                </label>
            </label>
            <label class="cell-item">
                <span class="cell-left">多选三</span>
                <label class="cell-right">
                    <input type="checkbox" value="3" name="checkbox"/>
                    <i class="cell-checkbox-icon"></i>
                </label>
            </label>
        </div>

        <div class="m-celltitle">单选(radio改为checkbox样式通用)</div>
        <div class="m-cell">
            <label class="cell-item">
                <span class="cell-left">单选一</span>
                <label class="cell-right">
                    <input type="radio" value="man" name="radio" checked/>
                    <i class="cell-radio-icon"></i>
                </label>
            </label>
            <label class="cell-item">
                <span class="cell-left">单选二</span>
                <label class="cell-right">
                    <input type="radio" value="woman" name="radio"/>
                    <i class="cell-radio-icon"></i>
                </label>
            </label>
        </div>

        <div class="m-celltitle">复选框（只兼容微信端第一种方法最简单）</div>
        <div class="m-cell">
            <label class="cell-item">
                <span class="cell-left">设为默认地址</span>
                <label class="cell-right">
                    <input type="checkbox" class="m-switch"/>
                </label>
            </label>
            <label class="cell-item">
                <span class="cell-left">兼容火狐写法</span>
                <label class="cell-right">
                    <input type="checkbox" class="m-switch-old" checked/>
                    <span class="m-switch"></span>
                </label>
            </label>
        </div>

        <div class="m-celltitle">文本域</div>
        <div class="m-cell">
            <div class="cell-item">
                <div class="cell-right">
                    <textarea class="cell-textarea" placeholder="请输入您的银行卡卡号和密码"></textarea>
                </div>
            </div>
        </div>

    </section>

</section>
<script src="//file.yikayi.net/static/js/jquery.min.js?v=2.2.4"></script>
<script src="../js/ydui.js"></script>
</body>
</html>
