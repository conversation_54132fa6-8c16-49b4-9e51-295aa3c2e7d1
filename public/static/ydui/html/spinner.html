<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Spinner</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">Spinner</span></div>
    </header>

    <section class="g-scrollview">

        <aside class="demo-tip">
            <p>Spinner 支持 Data API 和 Javascript API方式调用；</p>
            <p>1. 支持长按快速加减；</p>
            <p>2. 目前暂不支持小数加减；</p>
        </aside>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">Javascript API调用（设置最大值为75，累计值为3）</span>
            <span class="m-spinner" id="J_Quantity">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">Data API调用（设置最小值为6，累计值为3）</span>
            <span class="m-spinner" data-ydui-spinner="{input: '.J_Input', add: '.J_Add', minus: '.J_Del', min: 6, unit: 3}">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">不设置最大值和累计值，默认累计值为1</span>
            <span class="m-spinner" data-ydui-spinner="{input: '.J_Input', add: '.J_Add', minus: '.J_Del'}">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">设置默认值为15（累计值为5，默认值与累计值成倍数关系）</span>
            <span class="m-spinner" data-ydui-spinner="{input: '.J_Input', add: '.J_Add', minus: '.J_Del', unit: 5}">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" value="15" placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">禁止修改（设置文本框readonly或者disabled即可）</span>
            <span class="m-spinner" data-ydui-spinner="{input: '.J_Input', add: '.J_Add', minus: '.J_Del'}">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" readonly placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

        <div class="demo-spinner demo-small-pitch">
            <span class="demo-spinner-title">禁用长按快速加减（设置参数longpress为false即可）</span>
            <span class="m-spinner" data-ydui-spinner="{input: '.J_Input', add: '.J_Add', minus: '.J_Del', longpress: false}">
                <a href="javascript:;" class="J_Del"></a>
                <input type="text" class="J_Input" placeholder=""/>
                <a href="javascript:;" class="J_Add"></a>
            </span>
        </div>

    </section>

</section>
<script src="http://static.ydcss.com/libs/jquery/2.1.3/jquery.js"></script>
<script src="../js/ydui.js"></script>
<script>
    !function () {
        $('#J_Quantity').spinner({
            input: '.J_Input',
            add: '.J_Add',
            minus: '.J_Del',
            unit: function () {
                return 1 + 2;
            },
            max: function () {
                return (1 + 2 + 3 + 4 + 5) * 5;
            },
            callback: function (value, $ele) {
                // $ele 当前文本框[jQuery对象]
                // $ele.css('background', '#FF5E53');
                console.log('值：' + value);
            }
        });
    }();
</script>
</body>
</html>
