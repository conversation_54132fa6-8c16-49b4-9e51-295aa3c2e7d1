<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Icons</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">Icons</span></div>
    </header>

    <section class="g-scrollview">

        <div class="m-celltitle"> </div>

        <ul class="m-grids-4 demo-icons">
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-home"></i>
                </div>
                <div class="grids-txt">home</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-home-outline"></i>
                </div>
                <div class="grids-txt">home-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-shopcart"></i>
                </div>
                <div class="grids-txt">shopcart</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-shopcart-outline"></i>
                </div>
                <div class="grids-txt">shopcart-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-ucenter"></i>
                </div>
                <div class="grids-txt">ucenter</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-ucenter-outline"></i>
                </div>
                <div class="grids-txt">ucenter-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-star"></i>
                </div>
                <div class="grids-txt">star</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-star-outline"></i>
                </div>
                <div class="grids-txt">star-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-error"></i>
                </div>
                <div class="grids-txt">error</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-error-outline"></i>
                </div>
                <div class="grids-txt">error-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-warn"></i>
                </div>
                <div class="grids-txt">warn</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-warn-outline"></i>
                </div>
                <div class="grids-txt">warn-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-shield"></i>
                </div>
                <div class="grids-txt">shield</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-shield-outline"></i>
                </div>
                <div class="grids-txt">shield-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-like"></i>
                </div>
                <div class="grids-txt">like</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-like-outline"></i>
                </div>
                <div class="grids-txt">like-outline</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-weibo"></i>
                </div>
                <div class="grids-txt">weibo</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-weixin"></i>
                </div>
                <div class="grids-txt">weixin</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-tencent-weibo"></i>
                </div>
                <div class="grids-txt">tencent-weibo</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-qq"></i>
                </div>
                <div class="grids-txt">qq</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-phone1"></i>
                </div>
                <div class="grids-txt">phone1</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-phone2"></i>
                </div>
                <div class="grids-txt">phone2</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-phone3"></i>
                </div>
                <div class="grids-txt">phone3</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-share1"></i>
                </div>
                <div class="grids-txt">share1</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-share2"></i>
                </div>
                <div class="grids-txt">share2</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-share3"></i>
                </div>
                <div class="grids-txt">share3</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-order"></i>
                </div>
                <div class="grids-txt">order</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-prev"></i>
                </div>
                <div class="grids-txt">prev</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-play"></i>
                </div>
                <div class="grids-txt">play</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-next"></i>
                </div>
                <div class="grids-txt">next</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-good"></i>
                </div>
                <div class="grids-txt">good</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-bad"></i>
                </div>
                <div class="grids-txt">bad</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-feedback"></i>
                </div>
                <div class="grids-txt">feedback</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-compose"></i>
                </div>
                <div class="grids-txt">compose</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-discount"></i>
                </div>
                <div class="grids-txt">discount</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-verifycode"></i>
                </div>
                <div class="grids-txt">verifycode</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-download"></i>
                </div>
                <div class="grids-txt">download</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-footmark"></i>
                </div>
                <div class="grids-txt">footmark</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-refresh"></i>
                </div>
                <div class="grids-txt">refresh</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-search"></i>
                </div>
                <div class="grids-txt">search</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-time"></i>
                </div>
                <div class="grids-txt">time</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-setting"></i>
                </div>
                <div class="grids-txt">setting</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-delete"></i>
                </div>
                <div class="grids-txt">delete</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-more"></i>
                </div>
                <div class="grids-txt">more</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-checkoff"></i>
                </div>
                <div class="grids-txt">checkoff</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-video"></i>
                </div>
                <div class="grids-txt">video</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-clock"></i>
                </div>
                <div class="grids-txt">clock</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-type"></i>
                </div>
                <div class="grids-txt">type</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-discover"></i>
                </div>
                <div class="grids-txt">discover</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-qrscan"></i>
                </div>
                <div class="grids-txt">qrscan</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-location"></i>
                </div>
                <div class="grids-txt">location</div>
            </li>
            <li class="grids-item">
                <div class="grids-icon">
                    <i class="icon-question"></i>
                </div>
                <div class="grids-txt">question</div>
            </li>
        </ul>

    </section>

</section>
</body>
</html>
