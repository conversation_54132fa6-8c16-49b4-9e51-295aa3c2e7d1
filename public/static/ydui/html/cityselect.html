<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CitySelect</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">CitySelect</span></div>
    </header>

    <section class="g-scrollview">

        <aside class="demo-tip">
            <p>CitySelect 仅支持Javascript API方式调用；</p>
            <p>1. 需另引入数据文件：ydui.citys.js；</p>
            <p>2. ydui.citys.js是从京东偷取的数据，点击<a href="http://cityselect.ydui.org" target="_blank">这里</a>可获取最新数据；</p>
        </aside>

        <div class="m-celltitle">默认调用</div>
        <div class="m-cell">
            <div class="cell-item">
                <div class="cell-left">所在地区：</div>
                <div class="cell-right cell-arrow">
                    <input type="text" class="cell-input" readonly id="J_Address" placeholder="请选择收货地址">
                </div>
            </div>
        </div>

        <div class="m-celltitle">设置默认值</div>
        <div class="m-cell">
            <div class="cell-item">
                <div class="cell-left">所在地区：</div>
                <div class="cell-right cell-arrow">
                    <input type="text" class="cell-input" readonly id="J_Address2" value="新疆 乌鲁木齐市 天山区" placeholder="请选择收货地址">
                </div>
            </div>
        </div>

    </section>

</section>
<script src="//file.yikayi.net/static/js/jquery.min.js?v=2.2.4"></script>
<script src="http://static.ydcss.com/uploads/ydui/ydui.citys.js"></script>
<script src="../js/ydui.js"></script>
<script>
    /**
     * 默认调用
     */
    !function () {
        var $target = $('#J_Address');

        $target.citySelect();

        $target.on('click', function (event) {
            event.stopPropagation();
            $target.citySelect('open');
        });

        $target.on('done.ydui.cityselect', function (ret) {
            $(this).val(ret.provance + ' ' + ret.city + ' ' + ret.area);
        });
    }();
    /**
     * 设置默认值
     */
    !function () {
        var $target = $('#J_Address2');

        $target.citySelect({
            provance: '新疆',
            city: '乌鲁木齐市',
            area: '天山区'
        });

        $target.on('click', function (event) {
            event.stopPropagation();
            $target.citySelect('open');
        });

        $target.on('done.ydui.cityselect', function (ret) {
            $(this).val(ret.provance + ' ' + ret.city + ' ' + ret.area);
        });
    }();
</script>
</body>
</html>
