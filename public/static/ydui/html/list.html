<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>List</title>
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <link rel="stylesheet" href="../css/ydui.css?rev=@@hash"/>
    <link rel="stylesheet" href="../css/demo.css"/>
    <script src="../js/ydui.flexible.js"></script>
</head>
<body>
<section class="g-flexview">

    <header class="m-navbar">
        <a href="../index.html" class="navbar-item"><i class="back-ico"></i></a>
        <div class="navbar-center"><span class="navbar-title">List</span></div>
    </header>

    <section class="g-scrollview">

        <div class="m-celltitle demo-small-pitch">排版样式</div>
        <div class="m-cell">
            <a href="list.theme1.html" class="cell-item">
                <div class="cell-left">theme1</div>
                <div class="cell-right cell-arrow"></div>
            </a>
            <a href="list.theme2.html" class="cell-item">
                <div class="cell-left">theme2</div>
                <div class="cell-right cell-arrow"></div>
            </a>
            <a href="list.theme3.html" class="cell-item">
                <div class="cell-left">theme3</div>
                <div class="cell-right cell-arrow"></div>
            </a>
            <a href="list.theme4.html" class="cell-item">
                <div class="cell-left">theme4</div>
                <div class="cell-right cell-arrow"></div>
            </a>
            <a href="list.theme5.html" class="cell-item">
                <div class="cell-left">theme5</div>
                <div class="cell-right cell-arrow"></div>
            </a>
        </div>

        <div class="m-celltitle">异步数据加载</div>
        <div class="m-cell">
            <a href="list.infinitescroll.html" class="cell-item">
                <div class="cell-left">Infinite Scroll</div>
                <div class="cell-right cell-arrow">滚动加载</div>
            </a>
            <a href="list.pullrefresh.html" class="cell-item">
                <div class="cell-left">Pull Refresh</div>
                <div class="cell-right cell-arrow">下拉刷新</div>
            </a>
        </div>

        <div class="m-celltitle">进入详情页后返回列表页，定位之前位置</div>
        <div class="m-cell">
            <a href="list.backposition.html" class="cell-item">
                <div class="cell-left">Back Position</div>
                <div class="cell-right cell-arrow"></div>
            </a>
        </div>

    </section>

</section>
</body>
</html>
