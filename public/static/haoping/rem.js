!(function(doc, win) {
    var docEle = doc.documentElement,
        evt = "onorientationchange" in window ? "orientationchange" : "resize",
        fn = function() {
            var width = docEle.clientWidth > 750? 750:docEle.clientWidth;
            width && (docEle.style.fontSize = 20 * (width / 375) + "px");
        };

   /* win.addEventListener(evt, fn, false);
    doc.addEventListener("DOMContentLoaded", fn, false);*/
    win.addEventListener(evt, function(){
        fn();
        setTimeout(fn,50);
    }, false);
    doc.addEventListener("DOMContentLoaded", function(){
        fn();
        setTimeout(fn,50);
    }, false);

}(document, window));
//发送token

$(document).on('ajaxSend',function (e, xhr, options) {
    xhr.setRequestHeader('xm-token', token);
    var appType = 3;
    xhr.setRequestHeader('app-type', appType);
});

/**
 * 公用的会员协议
 * @returns {*}
 */
function showMemberRule(){
    var index = layer.open({
        type: 1,
        shadeClose: false,
        content: '<div class="memberRuleBox m-rule" id="memberRuleBox">'+
                    '<div class="btn"><i class="iconfont icon-guanbi member-close  j_close"></i></div>'+
                    '<div class="title"><p class="member-title">微信会员协议</p></div>'+
                    '<div class="member-content content">'+
                        '<p class="content-p2 font_size_7">请仔细阅读以下协议条款，如您不同意以或无法清楚理解下任何一条款请勿进行后续操作。您勾选同意本协议即表示您同意本协议的所有内容。</p>'+
                        '<p class="content-p1">1、为进一步享受本公众号给您提供的会员服务，您在此授权：</p>'+
                        '<p class="content-p2">（1）本公众号将您输入的手机号码或订单信息中的手机号码与本店微信公众账户进行绑定；</p>'+
                        '<p class="content-p2">（2）本公众号将您手机号对应的订单、物流等信息传递或同步到本店微信会员管理软件；</p>'+
                        '<p class="content-p2">（3）本公众号将您的订单、物流、积分等部分或全部信息根据您的查询展示在本店微信公众账户。</p>'+
                        '<p class="content-p1">2、您理解并认可，本公众号获取您的信息来自商家，本公众号仅对获取的信息根据您的查询进行展示，如您对展示的信息内容有疑问的，请联系商家，并以商家告知的信息内容为准。本公众号不保证信息的真实性、完整性、准确性、及时性，如您因使用展示的信息而导致了任何损失，本公众号不承担责任。</p>'+
                        '<p class="content-p1">3、您须妥善保管您的手机号码及手机验证码信息，因手机号码或手机验证码信息泄漏导致您信息的泄漏或给您造成其他损失的，本公众号不承担责任。</p>'+
                        '<p class="content-p1">4、本公众号不会将您的信息提供给第三方，并保证您的信息不会用于与公众号运营或店铺经营无关的活动。</p>'+
                    '</div>'+
                 '</div>',
        style:'position: fixed;width: 13.75rem;height:17.3rem;left: 50%; top:50%;margin-top:-8.65rem;margin-left: -6.875rem; border-radius: .25rem; -webkit-animation-duration: .5s;animation-duration: .5s;background-color:#fff'
    });

    // $('#memberRuleBox .member-content').css('height',$(window).height()*0.88+'px');
    $('#memberRuleBox').on('click','.j_close',function(){
        layer.close(index);
    });
    return index;
}
