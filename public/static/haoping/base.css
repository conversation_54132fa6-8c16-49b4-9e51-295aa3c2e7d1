/*CSS reset*/
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td {
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

fieldset, img {
    border: 0;
}

address, caption, cite, code, dfn, em, strong, th, var {
    font-style: normal;
    font-weight: normal;
}

ol, ul {
    list-style: none;
}

caption, th {
    text-align: left;
}

h1, h2, h3, h4, h5, h6 {
    font-size: 100%;
    font-weight: normal;
}

q:before, q:after {
    content: '';
}

abbr, acronym {
    border: 0;
}

a {
    text-decoration: none
}

input {
    box-sizing: border-box;
    border: 1px solid #e4e4e4;
}

/*#memberRuleBox{
    position: relative;
    color: #000;
}
#memberRuleBox .member-title{
    font-size: .9rem;
    text-align: center;
    padding-top: 1.25rem;
}
#memberRuleBox .member-close{
    position: absolute;
    top: .5rem;
    right: 1rem;
    font-size: 2rem;
}
#memberRuleBox .member-content{
    padding: .3rem .8rem;
    line-height: 1rem;
    overflow-y: auto;
}*/
#memberRuleBox .content-p1 {
    font-size: .7rem;
    padding-top: .5rem;
}

#memberRuleBox .content-p2 {
    padding-top: .2rem;
    padding-left: .5rem;
    font-size: .65rem;
    color: #353535;
}

#memberRuleBox .content-p2.font_size_7 {
    font-size: .7rem;
    padding-left: 0;
}

/*活动规则*/
.m-rule {
    position: absolute;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.m-rule .btn {
    padding: .3rem .5rem 0 0;
    text-align: right;
}

.m-rule .btn .iconfont {
    font-size: 1.2rem;
    color: #999;
}

.m-rule .title {
    height: 2.6rem;
    line-height: 2.4rem;
    background: url(./img/bg_title2.png) no-repeat center center;
    background-size: auto 100%;
    font-size: .925rem;
    text-align: center;
    color: #fff;
}

.m-rule .content {
    height: 65%;
    overflow-y: auto;
    margin-top: .7rem;
    padding: 0 .8rem;
    word-wrap: break-word;
    line-height: initial;
}

.m-rule textarea {
    width: 100%;
    height: 100%;
    line-height: 1.2rem;
    border: none;
    resize: none;
    font-size: .7rem;
}
