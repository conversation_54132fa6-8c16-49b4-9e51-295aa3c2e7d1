.wrapper {
    max-width: 750px;
    margin: 0 auto;
    color: #333;
}

.redPage {
    background-color: #332b43;
    /* min-height: 100%;*/
}

.concernTip {
    position: fixed;
    z-index: 999;
    top: 0;
    box-sizing: border-box;
    max-width: 750px;
    width: 100%;
    height: 1.5rem;
    padding: 0 1.2rem 0 .5rem;
    line-height: 1.5rem;
    font-size: .6rem;
    color: #fff;
    text-align: right;
}

.concernTip_bg {
    position: fixed;
    z-index: 99;
    top: 0;
    max-width: 750px;
    width: 100%;
    height: 1.5rem;
    background-color: #000;
    opacity: .4;
}

.concernTip .btn-concern {
    margin-left: .5rem;
    padding: .15rem .4rem;
    color: #fff;
    border: 1px solid #fff;
    border-radius: .1rem;
    font-size: .6rem;
}

.banner {
    height: 14rem;
}

.bannerDecorate {
    background: url(./img/mainBg.png) no-repeat center top;
    -webkit-background-size: 100%;
    background-size: 100%;
    height: 1.8rem;
    position: absolute;
    width: 100%;
    top: -1.8rem;
}

.mainContent {
    position: relative;
}

/*上传图片列表*/
.myImgBox {
    margin: .8rem auto 0;
    text-align: center;
}

.imgList {
    display: inline-block;
}

.imgList li {
    float: left;
    display: inline-block;
    width: 3.8rem;
}

.imgList li img {
    box-sizing: border-box;
    max-width: 100%;
    height: 100%;
    border-radius: .3rem;
}

.imgList .icon_img_close {
    position: absolute;
    top: -0.2rem;
    right: -0.2rem;
    width: .9rem;
    height: .9rem;
    background-color: #fff;
    color: #000;
    border-radius: .9rem;
    font-size: .8rem;
    font-weight: bold;
}

/*上传按钮*/
.aImg, .btn-upload {
    display: inline-block;
    box-sizing: border-box;
    width: 3.4rem;
    height: 3.4rem;
    border: 1px dashed #fff;
    border-radius: .3rem;
    font-size: .6rem;
    color: #fff;
    position: relative;
}

.aImg {
    border-style: solid;
}

.btn-upload {
    position: relative;
}

.btn-upload .iconfont {
    display: block;
    width: 100%;
    position: absolute;
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    padding: .2rem 0 .1rem 0;
}

.upload-text {
    margin-top: 2.2rem;
    display: block;
}

/*输入表单*/
.mainForm {
    margin-top: .5rem;
}

.inputWrapper {
    position: relative;
    font-size: .7rem;
    text-align: center;
}

.inputWrapper input {
    width: 80%;
    height: 2rem;
    font-size: .7rem;
    padding-left: 2.2rem;
    border-radius: 1rem;
    text-align: left;
}

.inputWrapper .iconfont {
    position: absolute;
    top: .35rem;
    left: 2.7rem;
    color: #999;
    font-size: .9rem;
}

.shopWrapper {
    width: 80%;
    height: 2rem;
    font-size: .7rem;
    border-radius: 1rem;
    text-align: center;
    background-color: #fff;
    margin: 0 auto 0.6rem;
    line-height: 2rem;
    color: #919191;
}

.codeWrapper {
    margin-top: .6rem;
}

.codeWrapper .iconfont {
    font-size: 1rem;
}

.codeImg {
    position: absolute;
    right: 10%;
    top: 0;
    height: 100%;
    border-top-right-radius: 1rem;
    border-bottom-right-radius: 1rem;
}

.submitWrapper {
    width: 80%;
    margin: .6rem auto 0;
    text-align: center;
    overflow: hidden;
}

.submitWrapper a {
    display: inline-block;
    width: 100%;
    height: 2rem;
    overflow: hidden;
    line-height: 2rem;
    background-color: #fe654c;
    color: #FFF;
    font-size: .8rem;
    border-radius: 1rem;
}

/*活动规则*/
.m-explain {
    position: absolute;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.m-explain .btn {
    padding: .3rem .5rem 0 0;
    text-align: right;
}

.m-explain .btn .iconfont {
    font-size: 1.2rem;
    color: #999;
}

.m-explain .title {
    height: 2.6rem;
    line-height: 2.4rem;
    background: url(./img/bg_title2.png) no-repeat center center;
    background-size: auto 100%;
    font-size: .925rem;
    text-align: center;
    color: #fff;
}

.m-explain .content {
    height: 60%;
    margin-top: .7rem;
    padding: 0 .8rem;
    word-wrap: break-word;
}

.m-explain textarea {
    width: 100%;
    height: 100%;
    line-height: 1.2rem;
    border: none;
    resize: none;
    font-size: .7rem;
}

/*脚部*/
.protocolDiv {
    margin-bottom: .5rem;
    font-size: .7rem;
}

.protocolDiv .blue {
    color: #8698d5;
}

.protocolDiv input[type=checkbox] {
    vertical-align: baseline;
}

.copyrightWrapper {
    /*position: fixed;
    bottom: 1rem;*/
    max-width: 750px;
    width: 100%;
    padding-bottom: .5rem;
    font-size: .6rem;
    text-align: center;
    color: #fff;
}

.copyright_static {
    position: static;
    /*margin: 1rem 0 1rem;*/
}

.ruleBtn {
    float: left;
    display: inline-block;
    font-size: .65rem;
    color: #fff;
    text-decoration: underline;
}

.lineDecoration {
    display: inline-block;
    width: .3rem;
    height: .1rem;
    background-color: #fff;
    border-radius: .3rem;
    vertical-align: .15rem;
}

.lineDecoration.small {
    height: .075rem;
}

.lineDecoration.middle {
    height: .08rem;
}

/*二维码弹窗*/
.scanDiv {
    margin-top: 3rem;
    text-align: center;
}

.scanDiv img {
    width: 12rem;
}

/*过期页*/
.outDate_wrapper {
    background: url(./img/bg_outdate.png) no-repeat center 70%;
    -webkit-background-size: 50%;
    background-size: 50%;
}

.outDate_tip {
    margin: 0 auto;
    padding-top: 16rem;
    text-align: center;
    font-size: 0.8rem;
    color: #999;
}
