/**
 * 好评返现入口
 * <AUTHOR>
 * @time 20170221
 */
$(initPage);

/**
 * 初始化页面
 */
function initPage() {
    //微信sdk授权
    wx.config({
        appId: jssdk_config.appid,
        timestamp: jssdk_config.timestamp,
        nonceStr: jssdk_config.nonceStr,
        signature: jssdk_config.signature,
        jsApiList: [
            'checkJsApi',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'onMenuShareQQ',
            'onMenuShareWeibo',
            'onMenuShareQZone',
            'hideMenuItems',
            'showMenuItems',
            'hideAllNonBaseMenuItem',
            'showAllNonBaseMenuItem',
            'translateVoice',
            'startRecord',
            'stopRecord',
            'onVoiceRecordEnd',
            'playVoice',
            'onVoicePlayEnd',
            'pauseVoice',
            'stopVoice',
            'uploadVoice',
            'downloadVoice',
            'chooseImage',
            'previewImage',
            'uploadImage',
            'downloadImage',
            'getNetworkType',
            'openLocation',
            'getLocation',
            'hideOptionMenu',
            'showOptionMenu',
            'closeWindow',
            'scanQRCode',
            'chooseWXPay',
            'openProductSpecificView',
            'addCard',
            'chooseCard',
            'openCard'
        ]
    });
    //页面事件
    initEvent();

    //显示规则
    $('.j_explainContent').val($.trim($('.j_explainContent')));
    $('.j_ruleBtn').click();


    //页面显示调整
    adjustLayout();

    //脚部版权调整
    adjustPage();

    //页面移动
    $(window).resize(function () {
        adjustPage();
    });

}

/**
 * 页面事件
 */
function initEvent() {
    var imgUrl = [];
    var images = {
        localId: [],
        serverId: []
    };
    //H5上传图片
    $('#file_upload').uploadifive({
        'auto': true,
        'buttonText': '',
        'height': '3.4rem',
        'width': '3.4rem',
        'buttonClass': 'uploadifive-button',
        //'fileSizeLimit': 1,
        // 'checkScript': 'check-exists.php',
        'formData': {
            'timestamp': '1',
            'token': '2'
        },
        'onCheck': function () {
            // alert('onCheck');
        },
        'queueID': 'queue',
        'uploadScript': "/member_api/v1/file/uploadifive",
        'onUpload': function (file) {
            layer.open({type: 2});

        },
        'onUploadComplete': function (file, data) {
            var obj = eval('(' + data + ')');
            //上传失败则返回原因
            if (obj.error != 0) {
                alert(obj.message);
                return false;
            }
            var url = obj.url;
            imgUrl.push(url);
            $('#uploadBox').before('<li>' +
                '<a href="javascript:void(0)" class="aImg">' +
                '<img src="' + url + '">' +
                '<i class="icon_img_close iconfont icon-guanbi j_removeImg"></i>' +
                '</a>' +
                '</li>');
            adjustPage();
        },
        'onError': function (file, fileType, data) {
            //信息框
            layer.open({
                content: file
                , btn: '我知道了'
            });
            console.log(fileType);
        },
        'onProgress': function (file, event) {
            //setInterval(fn, 100);
        },
        'onSelect': function () {
        },
        'onQueueComplete': function () {
            var loadIndex = layer.open({type: 2});
            layer.close(loadIndex);
        }
    });

    //微信sdk调用
    wx.ready(function () {

        //点击上传打开微信提供的相册
        document.querySelector('#uploadImg').onclick = function () {
            if (imgUrl.length >= 4) {
                alert('最多只能上传4张评价截图！');
                return;
            }

            wx.chooseImage({
                count: 4 - imgUrl.length,
                success: function (res) {
                    images.localId = res.localIds;
                    var i = 0, length = images.localId.length;
                    images.serverId = [];

                    function upload() {
                        //上传图片至微信服务器
                        wx.uploadImage({
                            localId: images.localId[i],
                            success: function (res) {
                                i++;
                                images.serverId.push(res.serverId);
                                if (i < length) {
                                    upload();
                                } else {
                                    //获取上传的图片
                                    var imgStr = '';
                                    $.each(images.serverId, function (i, e) {
                                        imgStr = i == 0 ? imgStr + images.serverId[i] : imgStr + ',' + images.serverId[i];
                                    });

                                    //请求服务器：获取评价截图
                                    $.post(apiUrl + 'upload/getWeChatImages.json', {
                                        company_id: company_id,
                                        mediaId: imgStr
                                    }, function (result) {
                                        if (result.code == 0) {
                                            $.each(result.data, function (i, e) {

                                                var url = result.data[i];

                                                $('#uploadBox').before('<li>' +
                                                    '<a href="javascript:void(0)" class="aImg">' +
                                                    '<img src="' + url + '">' +
                                                    '<i class="icon_img_close iconfont icon-guanbi j_removeImg"></i>' +
                                                    '</a>' +
                                                    '</li>');
                                                imgUrl.push(url);
                                                adjustPage();
                                                if (imgUrl.length >= 4) {
                                                    $('#uploadBox').hide();
                                                }
                                            });
                                        } else {
                                            alert(result.data)
                                        }
                                    });
                                }
                            },
                            fail: function (res) {
                                alert(JSON.stringify(res));
                            }
                        });
                    }

                    upload();
                },
                fail: function (res) {
                    alert(JSON.stringify(res));
                }
            });
        };

    });

    //删除图片
    $('#imgList').off('click').on('click', '.j_removeImg', function () {
        var oLi = $(this).parents('li');
        var currentImg = oLi.find('img').attr('src');
        $.each(imgUrl, function (i, e) {
            if (currentImg.indexOf(e) != -1) {
                imgUrl.splice(i, 1);
                oLi.remove();
                adjustPage();
            }
        });

        if (imgUrl.length < 4) {
            $('#uploadBox').show();
        }
    });
    //订单提交
    $('#submit').on('touchend', function (e) {
        var order = $('#orderDiv').find('.j_orderInput').val();
        var code = $('#codeDiv').find('.j_codeInput').val();
        var isAgree = $('.j_agreeProtocol').is(':checked');
        var shopId = $('#showShop').attr('data-id');

        //截图
        if (is_evaluate_images == 1 && imgUrl.length < 1 && is_need_pic) {
            //信息框
            layer.open({
                content: '请上传评价截图'
                , btn: '我知道了'
            });
            e.preventDefault();//阻止“默认行为”
            return;
        }
        //店铺
        if (is_shop == 1 && shopId == '') {
            layer.open({
                content: '请选择店铺'
                , btn: '我知道了'
            });
            e.preventDefault();//阻止“默认行为”
            return;
        }
        //订单编号
        if (is_order_id == 1 && !$.trim(order)) {
            layer.open({
                content: '请输入有效的订单号!'
                , btn: '我知道了'
            });
            e.preventDefault();//阻止“默认行为”
            return;
        }
        //验证码
        if (open_verify_code == 1 && !$.trim(code)) {
            layer.open({
                content: '请输入验证码!'
                , btn: '我知道了'
            });
            e.preventDefault();//阻止“默认行为”
            return;
        }
        //会员协议
        if (register_user == 1 && !isAgree) {
            layer.open({
                content: '请勾选阅读并同意《会员协议》'
                , btn: '我知道了'
            });
            e.preventDefault();//阻止“默认行为”
            return;
        }

        //领取红包
        ajax_getBouns(imgUrl);
        e.preventDefault();//阻止“默认行为”
    });
    //重新获取验证码
    $('#codeDiv').on('click', '.j_codeImg', function () {
        getCode();
    });

    //显示二维码
    $('#toConcern').on('click', function () {
        var scan = layer.open({
            type: 1,
            content: '<div class="scanDiv"><img src="' + qrcode_url + '"></div>',
            anim: 'up',
            style: 'position:fixed; left:0; bottom:0; width:100%; height:18rem; border: none; -webkit-animation-duration: .5s; animation-duration: .5s;'
        });
    });
    //显示活动规则
    $('.j_ruleBtn').on('click', function () {
        var rule = layer.open({
            type: 1,
            content: $('.j_explainBox').html(),
            shadeClose: false,
            style: 'position: fixed;width: 13.75rem;height:17.3rem;left: 50%; top:50%;margin-top:-8.65rem;margin-left: -6.875rem; border-radius: .25rem; -webkit-animation-duration: .5s;animation-duration: .5s;background-color:#fff'
        });

        $('.j_ruleClose').click(function () {
            layer.close(rule);
        });
    });

}

/**
 * 根据排版内容，对脚部版权的样式调整
 */
function adjustPage() {
    setTimeout(function () {
        var win_h = $(window).height();
        var body_h = $('body').height();
        var foot_h = $('#copyright').height();

        if (win_h - body_h > 0) {
            $('#copyright').css('margin-top', win_h - body_h)
        } else {
            $('#copyright').css('margin-top', '1rem');
        }
    }, 1000);

    //当前布局大于整屏且未调整布局时
    /* if(body_h *1.5 > win_h && $('#copyright').attr('class').indexOf('copyright_static')==  -1){
     //页面布局超过屏幕宽
     $('#copyright').addClass('copyright_static');
     }else{
     $('#copyright').removeClass('copyright_static');
     }*/
}

/**
 * 根据用户设置调整布局
 */
function adjustLayout() {
    //整体样式调整
    if (outDate_status == 1) {
        $('html').addClass('redPage');
        //banner
        $('.j_banner').css({
            'background': 'url(' + top_banner + ')no-repeat center top',
            'background-size': '100% auto'
        });
    } else {
        $('html').removeClass('redPage');
    }
    //TODO
    //截图
    //is_evaluate_images = 0;
    if (is_evaluate_images == 1) {
        $('#myImgBox').show();
    }
    //店铺
    //is_shop = 0;
    if (is_shop == 1) {
        $('#showShop').show();
        ajaxGetShop()
    }
    //订单编号
    // var is_order_id = '<?=$evaluate_rebate_config['data']['is_order_id']?>';
    if (is_order_id == 1) {
        $('#orderDiv').show();
    }
    //验证码
    //var open_verify_code = '<?=$evaluate_rebate_config['data']['open_verify_code']?>';
    if (open_verify_code == 1) {
        $('#codeDiv').show();
        getCode();
    }
    //会员协议
    //var register_user = '<?=$evaluate_rebate_config['data']['register_user']?>';
    if (register_user == 1) {
        $('#protocolDiv').show();
    }

}

/**
 * 获取验证码
 */
function getCode() {
    $('#codeDiv').find('.j_codeImg').attr('src', home_url + 'captcha/index/evaluateRebate?t=' + new Date().getTime());
}

/**
 * 领取红包
 */
function ajax_getBouns(imgUrl) {
    var loadIndex = layer.open({type: 2});
    var order = $('#orderDiv').find('.j_orderInput').val();
    var code = $('#codeDiv').find('.j_codeInput').val();
    var shopId = $('#showShop').attr('data-id');
    var shopName = $('#showShop').text();
    var platFrom = $('#showShop').attr('data-from');

    var url = apiUrl;
    var params = {
        company_id: company_id,
        visit_id: visit_id,
        user_id: user_id,
        order_sn: $.trim(order),
        captcha: $.trim(code),
        evaluate_images: JSON.stringify(imgUrl),
        shop_id: shopId,
        shop_name: shopName,
        plat_from: platFrom,
        openid: openid,
        appid: appid,
        bid: bid,
        is_need_pic: is_need_pic
    };

    $.post(url, params, function (result) {
        layer.close(loadIndex);
        console.log(result);
        if (result.code == 0) {
            layer.open({
                content: result.msg
                , btn: '我知道了'
            });
            return;
            //window.location.href = "../success/?company_id=" + company_id + "&img=" + encodeURI(encodeURI(result.data.img)) + '&img_url=' + result.data.img_url + '&success_content=' + encodeURI(encodeURI(result.data.success_content));
        } else {
            layer.open({
                content: result.msg
                , btn: '我知道了'
            });
        }
    }).error(function () {
        layer.close(loadIndex);
        layer.open({
            content: '网络繁忙'
            , btn: '我知道了'
        });
    });
}

/**
 * 请求服务器：获取店铺列表
 */
function ajaxGetShop() {
    var url = apiUrl + 'shop.json';
    var shopData = [];

    var params = {
        company_id: company_id
    };
    $.post(url, params, function (result) {
        if (result.code == 0) {
            $.each(result.data.data, function (i, e) {
                var aShop = {};
                aShop.value = i;
                aShop.id = e.special_id;
                aShop.label = e.shop_name;
                aShop.plat_from = e.plat_from;
                shopData.push(aShop);
            });

            var firstShop = shopData[0];
            $('#showShop').text(firstShop.label)
                .attr('data-id', firstShop.id)
                .attr('data-from', firstShop.plat_from);

            //店铺选择
            $('#showShop').on('click', function () {
                weui.picker(shopData, {
                    onChange: function (result) {
                        $('#showShop').text(shopData[result].label)
                            .attr('data-id', shopData[result].id)
                            .attr('data-from', shopData[result].plat_from);
                    },
                    onConfirm: function (result) {

                    }
                });
            });
        } else {
            layer.open({
                content: result.data
                , btn: '我知道了'
            });
        }
    });


}
