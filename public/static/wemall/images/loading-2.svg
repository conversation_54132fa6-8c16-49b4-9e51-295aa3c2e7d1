<svg width='100px' height='100px' xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" class="uil-default"><rect x="0" y="0" width="100" height="100" fill="none" class="bk"></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(0 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.8s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(45 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.7000000000000001s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(90 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.6000000000000001s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(135 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.5s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(180 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.4s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(225 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.30000000000000004s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(270 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.2s' repeatCount='indefinite'/></rect><rect  x='45' y='38' width='10' height='24' rx='4' ry='4' fill='#000000' transform='rotate(315 50 50) translate(0 -30)'>  <animate attributeName='opacity' from='1' to='0' dur='0.8s' begin='-0.1s' repeatCount='indefinite'/></rect></svg>