/* 订单列表页面样式 */
[v-cloak] {
  display: none;
}

/* 主容器样式 - 添加底部内边距避免被导航遮住 */
#app {
  padding-bottom: 80px; /* 为底部导航留出空间 */
  min-height: 100vh;
  box-sizing: border-box;
}

/* 搜索框样式 - 暂时隐藏 */
.search-container {
  background: #fff;
  padding: 15px;
  margin-bottom: 0;
  box-shadow: none;
  border-bottom: 1px solid #e5e5e5;
  display: none; /* 暂时隐藏 */
}

.search-input {
  border-radius: 4px;
  padding-left: 40px;
  border: 1px solid #ddd;
  background: #fafafa;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  z-index: 10;
}

/* Tab标签页样式 */
.tab-container {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.layui-tab {
  margin: 0;
}

.layui-tab-title {
  background: #fff;
  border-bottom: none;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: space-around;
}

.layui-tab-title li {
  flex: 1;
  text-align: center;
  font-size: 15px;
  line-height: 48px;
  color: #666;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
  border: none !important;
  outline: none !important;
}

.layui-tab-title li:hover {
  color: var(--theme-primary);
  background: var(--theme-shadow);
}

.layui-tab-title .layui-this {
  color: var(--theme-primary) !important;
  background: var(--theme-shadow);
  font-weight: 600;
  border: none !important;
  outline: none !important;
}

/* 去掉默认的下划线 */
.layui-tab-title .layui-this:after {
  display: none !important;
}

/* 自定义选中指示器 */
.layui-tab-title .layui-this::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: var(--theme-primary);
  border-radius: 2px;
}

.layui-tab-content {
  padding: 0;
}

/* 订单卡片样式 */
.order-card {
  margin: 8px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e5e5;
  background: #fff;
}

.order-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.order-header {
  background: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  color: #999;
  font-size: 14px;
}

/* 订单状态样式 */
.order-status-unpaid {
  color: var(--theme-text);
  font-size: 14px;
  font-weight: normal;
}

.order-status-pending {
  color: var(--theme-primary);
  font-size: 14px;
  font-weight: normal;
}

.order-status-shipped {
  color: var(--theme-primary);
  font-size: 14px;
  font-weight: normal;
}

.order-status-verified {
  color: var(--theme-primary);
  font-size: 14px;
  font-weight: normal;
}

.order-status-completed {
  color: var(--theme-primary);
  font-size: 14px;
  font-weight: normal;
}

/* 商品列表样式 */
.goods-list {
  background: #fff;
  padding: 0;
}

.goods-item {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: flex-start;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  margin-right: 12px;
  flex-shrink: 0;
  object-fit: cover;
  border: 1px solid #f0f0f0;
}

.goods-info {
  flex: 1;
  min-width: 0;
}

.goods-title {
  font-size: 16px;
  color: #000;
  line-height: 1.4;
  margin-bottom: 6px;
  font-weight: bold;
}

.goods-specs {
  background-color: var(--theme-shadow);
  color: var(--theme-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  margin-bottom: 6px;
  vertical-align: middle;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  box-sizing: border-box;
  max-width: 100%;
}

.goods-quantity {
  color: #999;
  font-size: 14px;
}

/* 订单底部样式 */
.order-footer {
  background: #fff;
  padding: 8px 16px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time {
  color: #999;
  font-size: 12px;
}

.view-detail-btn {
  color: #333;
  font-size: 14px;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding-left: 10px;
}

.view-detail-btn:hover {
  color: #333;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 20px;
  color: #999;
  background: #fff;
  margin: 8px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-text {
  margin-left: 8px;
  font-size: 14px;
}

.no-more-text {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
  background: #f8f8f8;
  margin: 0 0 20px 0; /* 添加底部边距 */
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  margin: 8px 8px 28px 8px; /* 添加底部边距 */
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-title {
  color: #999;
  font-size: 16px;
  margin: 0;
}

/* 额外的WeUI风格调整 */
.order-time span:first-child {
  color: #999;
  font-size: 12px;
}

.order-time span:last-child {
  color: #999;
  font-size: 12px;
}

/* 确保卡片间距 */
.order-card + .order-card {
  margin-top: 0;
}

/* 搜索框焦点样式 */
.search-input:focus {
  border-color: var(--theme-primary);
  box-shadow: none;
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-card {
  animation: fadeIn 0.3s ease-out;
}

/* 焦点样式 */
.order-card:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 无障碍访问 */
