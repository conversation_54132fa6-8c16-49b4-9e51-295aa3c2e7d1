/* 全局样式 */
body {
  background-color: #f5f7fa;
  color: #333;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}

/* 页面容器 */
.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 20px;
  opacity: 0;
  animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-hover) 100%);
  color: white;
  padding: 20px;
  text-align: center;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.page-title {
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0;
}

/* 卡片样式 */
.custom-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.custom-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-hover) 100%);
}

.card-body {
  padding: 20px;
}

/* 售后状态样式 */
.after-sale-status {
  text-align: center;
}

.status-badge {
  display: inline-block;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 500;
  margin-bottom: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.status-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.status-message {
  margin: 15px 0;
  font-size: 14px;
  line-height: 1.6;
}

/* 按钮样式 */
.action-buttons {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.custom-btn {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
}

.custom-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 处理记录样式 */
.after-sale-record {
  background-color: #fff;
}

.record-timeline {
  position: relative;
  padding-left: 20px;
}

.record-timeline:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e8e8e8;
}

.record-item {
  position: relative;
  padding: 15px 0 15px 25px;
  margin-bottom: 5px;
  opacity: 0;
  animation: slideInLeft 0.5s ease-in-out forwards;
}

.record-item:nth-child(1) {
  animation-delay: 0.1s;
}
.record-item:nth-child(2) {
  animation-delay: 0.2s;
}
.record-item:nth-child(3) {
  animation-delay: 0.3s;
}
.record-item:nth-child(4) {
  animation-delay: 0.4s;
}
.record-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.record-item:before {
  content: "";
  width: 12px;
  height: 12px;
  background-color: var(--theme-primary);
  border-radius: 50%;
  position: absolute;
  left: -6px;
  top: 20px;
  box-shadow: 0 0 0 4px var(--theme-shadow);
  z-index: 1;
  transition: all 0.3s ease;
}

.record-item:hover:before {
  transform: scale(1.2);
}

.record-item.merchant:before {
  background-color: var(--theme-text);
  box-shadow: 0 0 0 4px var(--theme-shadow);
}

.record-time {
  color: #999;
  font-size: 12px;
  margin-bottom: 8px;
}

.record-content {
  color: #333;
  background-color: #f9f9f9;
  padding: 10px 15px;
  border-radius: 6px;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.record-content:hover {
  background-color: #f0f0f0;
}

.record-author {
  font-weight: 500;
  margin-right: 5px;
}

.customer-author {
  color: var(--theme-primary);
}

.merchant-author {
  color: var(--theme-text);
}

/* 底部按钮 */
.bottom-actions {
  text-align: center;
  padding: 20px 0;
}

.return-btn {
  border-radius: 4px;
  color: #fff;
  transition: all 0.3s ease;
}

.return-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 回复输入框样式 */
.reply-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.reply-section:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.reply-textarea {
  min-height: 100px;
  width: 100%;
  padding: 10px 0 0 10px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  resize: vertical;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.reply-textarea:focus {
  border-color: var(--theme-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--theme-shadow);
}

.reply-textarea:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

/* 响应式调整 */

.layui-form-item {
  margin-bottom: 0;
}

/* 上传按钮样式 */
.custom-upload-button,
.custom-upload-button:hover {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  background: none;
  cursor: pointer;
  transition: all 0.3s;
}

.custom-upload-button:hover {
  border-color: var(--theme-primary);
  background-color: #f8f8f8;
}

.custom-upload-button:after {
  content: "\e67c";
  font-family: "layui-icon";
  font-size: 30px;
  color: #999;
  transition: all 0.3s;
}

.custom-upload-button:hover:after {
  color: var(--theme-primary);
}

.custom-upload-button:before {
  content: "上传图片";
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.uploadifive-button input {
  width: 80px;
  height: 80px;
}

/* 图片相关样式 */
.one_img {
  position: relative;
  display: inline-block;
}

.one_img img {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.one_img img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.remove_img {
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 16px;
  color: #fff;
  background-color: var(--theme-text);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  transition: all 0.3s ease;
}

.remove_img:hover {
  background-color: #ff4757;
  transform: scale(1.1);
}

#queue {
  border: 1px solid #e5e5e5;
  height: 177px;
  overflow: auto;
  margin-bottom: 10px;
  padding: 0 3px 3px;
  width: 300px;
}

.img_list {
  padding: 5px;
  float: left;
}

.note-images img {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.note-images img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 深色模式支持 */

/* 确保有背景色的按钮文字为白色 */
.layui-btn-normal {
  color: white;
}

/* 无障碍访问支持 */
.custom-btn:focus,
.return-btn:focus,
.reply-textarea:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 动画优化 */
