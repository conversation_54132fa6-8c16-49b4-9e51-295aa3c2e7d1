/* 订单详情页面样式 */
body {
  background-color: #f8f8f8;
  color: #333;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Aria<PERSON>, sans-serif;
  line-height: 1.6;
}

/* 页面头部 */
.page-header {
  text-align: center;
  background-image: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-light));
  background-size: cover;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.page-title {
  color: #fff;
  font-size: 1.3rem;
  font-weight: 500;
}

/* 卡片样式 */
.layui-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.layui-card:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.layui-card-header {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding: 4px 15px;
}

.layui-card-body {
  color: #666;
  line-height: 2;
  padding: 15px;
}

/* 卡片头部图标样式 */
.card-header-icon {
  margin-right: 8px;
  color: var(--theme-primary);
}

/* 客户信息样式 */
.customer-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.customer-name {
  font-size: 16px;
  font-weight: 500;
}

.customer-address {
  color: #888;
  margin-top: 5px;
}

.address-icon {
  height: 1.5rem;
  margin-right: 10px;
  vertical-align: middle;
}

/* 物流信息样式 */
.logistics-info {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.info-item {
  margin-bottom: 5px;
}

.info-label {
  color: #666;
  display: inline-block;
  width: 75px;
  text-align: right;
  margin-right: 10px;
}

.info-value {
  color: #333;
  font-weight: 500;
}

/* 商品列表样式 */
.goods_list {
  display: flow-root;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.goods_list:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.goods_list:hover {
  background-color: #f9f9f9;
  border-radius: 4px;
}

.goods-image {
  width: 70px;
  height: 70px;
  border-radius: 4px;
  object-fit: cover;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.goods-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
}

.goods-price {
  color: var(--theme-text);
  font-weight: 500;
}

.goods-meta {
  color: #888;
  font-size: 13px;
}

/* 商品规格标签样式 */
.goods-specs-tag {
  display: inline-block;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 2px 8px;
  margin-left: 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.goods-specs-tag:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 布局相关样式类 */
.flex-1 {
  flex: 1;
}

.flex-container {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-start {
  display: flex;
  align-items: flex-start;
}

.flex-between-start {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

/* 间距相关样式类 */
.margin-left-15 {
  margin-left: 15px;
}

.margin-right-10 {
  margin-right: 10px;
}

.margin-top-5 {
  margin-top: 5px;
}

.margin-top-10 {
  margin-top: 10px;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.margin-top-3 {
  margin-top: 3px;
}

.margin-left-5 {
  margin-left: 5px;
}

/* 图标样式 */
.icon-height {
  height: 1.8rem;
}

.qrcode-icon {
  vertical-align: middle;
  cursor: pointer;
}

/* 文字样式 */
.font-bold-16 {
  font-weight: bold;
  font-size: 16px;
}

.font-size-12 {
  font-size: 12px;
}

.text-gray {
  color: #999;
}

/* 信息标签样式 */
.info-label-left {
  width: auto;
  text-align: left;
}

/* 售后相关样式 */
.after-sale-card-body {
  padding: 0;
}

.after-sale-container {
  padding: 10px;
}

.after-sale-text {
  margin-right: 0;
}

/* 订单信息样式 */
.order-info-item {
  margin-bottom: 8px;
  display: flex;
}

.label {
  display: inline-block;
  width: 100px;
  text-align: right;
  color: #999;
  margin-right: 10px;
}

.value {
  color: #333;
  font-weight: 500;
}

/* 状态标签样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

.layui-badge {
  font-weight: normal;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

/* 按钮样式 */
.layui-btn {
  transition: all 0.3s ease;
}

.layui-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.layui-btn-xs {
  padding: 2px 8px;
  font-size: 12px;
  line-height: 1.5;
  margin-left: 10px;
  vertical-align: middle;
}

.btn-action {
  border-radius: 4px;
  font-size: 13px;
}

/* 修改按钮样式 - 当应用主题色时确保文字可读性 */
#modify_request_send_or_pick_up_time_button.layui-border-blue {
  background-color: transparent;
  color: inherit;
}

/* 售后服务状态样式 */
.after-sale-section {
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 4px;
  margin-top: 15px;
}

.after-sale-status {
  font-size: 14px;
  font-weight: bold;
  background-color: #f9f9f9;
  padding: 4px;
  border-radius: 4px;
  margin-top: 15px;
}

.after-sale-not-started {
  color: #999;
}

.after-sale-pending {
  color: var(--theme-primary);
}

.after-sale-action-required {
  color: var(--theme-text);
}

.after-sale-completed {
  color: var(--theme-primary);
}

.after-sale-btn {
  border-radius: 20px;
  background-color: var(--theme-primary);
  color: white !important;
  transition: all 0.3s;
}

.after-sale-btn:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.after-sale-btn .layui-icon {
  margin-right: 5px;
}

.after-sale-status .layui-badge {
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: normal;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

/* 二维码图标样式 */
.view_qrcode {
  cursor: pointer;
  transition: all 0.3s;
  vertical-align: middle;
}

.view_qrcode:hover {
  transform: scale(1.1);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 响应式设计 */

/* 深色模式支持 */
/*  */

/* 动画效果 */
.layui-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 焦点样式 */
.layui-btn:focus,
.view_qrcode:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 无障碍访问 */
