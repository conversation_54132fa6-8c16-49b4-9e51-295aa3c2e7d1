/**
 * 易卡易积分转换页面样式
 * 支持主题切换和移动端优化
 */

/* 引入主题变量 */
@import url("/static/css/theme/variables.css");

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 应用容器 */
.yikayi-point-app {
  min-height: 100vh;
  background: var(--theme-bg-color);
  color: var(--theme-text-color);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  padding: 0;
  margin: 0;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: var(--theme-text-muted);
}

.loading-state i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary-color);
}

.loading-state p {
  font-size: 14px;
  margin: 0;
}

/* 成功状态 */
.success-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 20px;
}

.success-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--theme-success-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.success-icon i {
  font-size: 40px;
  color: white;
}

.success-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text-color);
  margin-bottom: 8px;
}

.success-desc {
  font-size: 14px;
  color: var(--theme-text-muted);
  margin-bottom: 30px;
}

.success-actions {
  width: 100%;
  max-width: 280px;
}

.retry-btn {
  width: 100%;
  height: 44px;
  background: var(--theme-primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: var(--theme-primary-hover);
  transform: translateY(-1px);
}

.retry-btn:active {
  transform: translateY(0);
}

/* 主要内容 */
.main-content {
  padding: 16px;
  max-width: 480px;
  margin: 0 auto;
}

/* 卡片样式 */
.member-info-card,
.merchant-select-card {
  background: var(--theme-card-bg);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--theme-card-shadow);
  border: 1px solid var(--theme-border-color);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light);
}

/* 信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: var(--theme-text-muted);
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: var(--theme-text-color);
  font-weight: 500;
  text-align: right;
}

.info-value.highlight {
  color: var(--theme-primary-color);
  font-weight: 600;
  font-size: 16px;
}

/* 选择器样式 */
.select-container {
  position: relative;
}

.select-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--theme-input-bg);
  border: 1px solid var(--theme-border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 48px;
}

.select-input:hover {
  border-color: var(--theme-primary-color);
}

.select-input.has-value {
  border-color: var(--theme-primary-color);
}

.select-text {
  font-size: 14px;
  color: var(--theme-text-color);
  flex: 1;
}

.select-placeholder {
  font-size: 14px;
  color: var(--theme-text-muted);
  flex: 1;
}

.select-arrow {
  font-size: 12px;
  color: var(--theme-text-muted);
  transition: transform 0.3s ease;
}

.select-input:hover .select-arrow {
  color: var(--theme-primary-color);
}

/* 操作区域 */
.action-area {
  padding: 20px 0;
}

.convert-btn {
  width: 100%;
  height: 48px;
  background: var(--theme-primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.convert-btn:hover:not(:disabled) {
  background: var(--theme-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.convert-btn:active:not(:disabled) {
  transform: translateY(0);
}

.convert-btn:disabled {
  background: var(--theme-disabled-bg);
  color: var(--theme-disabled-color);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.convert-btn.loading {
  background: var(--theme-primary-color);
  cursor: wait;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 20px;
  color: var(--theme-text-muted);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  opacity: 0.8;
}

/* 选择器弹窗 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.picker-container {
  background: var(--theme-card-bg);
  border-radius: 16px 16px 0 0;
  width: 100%;
  max-width: 480px;
  max-height: 60vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border-light);
  background: var(--theme-card-bg);
}

.picker-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-color);
}

.picker-close {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: var(--theme-text-muted);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.picker-close:hover {
  background: var(--theme-hover-bg);
  color: var(--theme-text-color);
}

.picker-content {
  max-height: 50vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border-bottom: 1px solid var(--theme-border-light);
}

.picker-item:last-child {
  border-bottom: none;
}

.picker-item:hover:not(.disabled) {
  background: var(--theme-hover-bg);
}

.picker-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.merchant-name {
  font-size: 14px;
  color: var(--theme-text-color);
  flex: 1;
}

.selected-icon {
  font-size: 16px;
  color: var(--theme-primary-color);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式设计 */

/* 暗色主题适配 */

/* 打印样式 */
