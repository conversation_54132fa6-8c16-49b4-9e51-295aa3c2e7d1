/* 优惠券领取页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

[v-cloak] {
  display: none;
}

/* 页面背景 */
.page,
body {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  min-height: 100vh;
}

/* 主容器 */
.coupon-container {
  padding: 20px;
  max-width: 400px;
  margin: 0 auto;
}

/* 会员信息卡片 */
.member-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--theme-shadow);
}

.member-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text);
  margin: 0 0 15px 0;
  text-align: center;
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.member-value {
  font-size: 14px;
  color: var(--theme-text);
  font-weight: 600;
}

/* 优惠券卡片 */
.coupon-card {
  background: #fff;
  border-radius: 12px;
  padding: 30px 20px;
  margin-bottom: 20px;
  box-shadow: var(--theme-shadow);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.coupon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--theme-primary), var(--theme-hover));
}

.coupon-icon {
  width: 60px;
  height: 60px;
  background: var(--theme-primary-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 24px;
  color: var(--theme-primary);
}

.coupon-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text);
  margin: 0 0 8px 0;
}

.coupon-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

/* 领取按钮 */
.receive-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-hover));
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.receive-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.receive-btn:active {
  transform: translateY(0);
}

.receive-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 成功状态 */
.success-container {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 40px;
  color: #fff;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #4CAF50;
  margin: 0 0 10px 0;
}

.success-desc {
  font-size: 16px;
  color: #666;
  margin: 0 0 30px 0;
  line-height: 1.5;
}

.success-btn {
  width: 200px;
  height: 44px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 22px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.success-btn:hover {
  background: var(--theme-hover);
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  min-width: 150px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */

/* 深色主题支持 */
[lay-theme="dark"] .member-card,
[lay-theme="dark"] .coupon-card {
  background: #2f2f2f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[lay-theme="dark"] .member-item {
  border-bottom-color: #404040;
}

[lay-theme="dark"] .member-value,
[lay-theme="dark"] .coupon-title {
  color: #fff;
}

[lay-theme="dark"] .loading-content {
  background: #2f2f2f;
}

[lay-theme="dark"] .loading-text {
  color: #ccc;
}
