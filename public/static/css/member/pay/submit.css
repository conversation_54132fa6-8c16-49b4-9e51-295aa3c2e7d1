/* WeUI到LayUI重构 - pay/submit.html 样式文件 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none !important;
}

body {
  font-family: PingFang SC, "Helvetica Neue", Arial, sans-serif;
  background: var(--theme-bg-color, #f8f9fa);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
}

.loading-content {
  text-align: center;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  color: var(--theme-text-secondary, #666);
  font-weight: normal;
  margin: 0;
}

/* 支付容器 */
.payment-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-bg-color, #f8f9fa);
}

/* 支付卡片 */
.payment-card {
  background: var(--theme-card-bg, #fff);
  border-radius: 16px;
  padding: 40px 30px;
  max-width: 400px;
  width: 100%;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--theme-border-color, #e8e8e8);
}

/* 金额区域 */
.amount-section {
  margin-bottom: 30px;
}

.amount {
  font-size: 48px;
  font-weight: 300;
  color: var(--theme-text-color, #333);
  margin: 0;
  line-height: 1.2;
}

/* 描述区域 */
.description-section {
  margin-bottom: 40px;
}

.payment-desc {
  font-size: 16px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  margin: 0;
}

/* 操作区域 */
.action-section {
  margin-bottom: 30px;
}

.pay-button {
  width: 100%;
  height: 50px;
  background: var(--theme-primary, #1890ff);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pay-button:hover:not(:disabled) {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.pay-button:active:not(:disabled) {
  transform: translateY(0);
}

.pay-button:disabled {
  background: var(--theme-disabled, #d9d9d9);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pay-button i {
  font-size: 16px;
}

/* 底部区域 */
.footer-section {
  border-top: 1px solid var(--theme-border-color, #e8e8e8);
  padding-top: 20px;
}

.copyright {
  font-size: 12px;
  color: var(--theme-text-tertiary, #999);
  margin: 0;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-bg-color, #f8f9fa);
}

.error-content {
  text-align: center;
  max-width: 300px;
}

.error-icon {
  font-size: 64px;
  color: var(--theme-error, #ff4d4f);
  margin-bottom: 20px;
}

.error-content h3 {
  font-size: 20px;
  color: var(--theme-text-color, #333);
  margin-bottom: 12px;
  font-weight: 500;
}

.error-content p {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  margin-bottom: 30px;
}

.back-button {
  padding: 10px 24px;
  background: var(--theme-primary, #1890ff);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.payment-card {
  animation: fadeInUp 0.6s ease-out;
}

.error-content {
  animation: fadeInUp 0.6s ease-out;
}

/* 支付按钮脉冲效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.pay-button:not(:disabled) {
  animation: pulse 2s infinite;
}

/* 主题适配 */

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .payment-container,
  .loading-state,
  .error-state {
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
}
