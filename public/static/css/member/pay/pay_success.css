/* WeUI到LayUI重构 - pay/pay_success.html 样式文件 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none !important;
}

body {
  font-family: PingFang SC, "Helvetica Neue", Arial, sans-serif;
  background: var(--theme-bg-color, #f8f9fa);
}

/* 结果容器 */
.result-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--theme-bg-color, #f8f9fa);
}

/* 结果卡片 */
.result-card {
  background: var(--theme-card-bg, #fff);
  border-radius: 16px;
  padding: 40px 30px;
  max-width: 400px;
  width: 100%;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--theme-border-color, #e8e8e8);
}

/* 图标区域 */
.icon-section {
  margin-bottom: 30px;
}

.status-icon {
  font-size: 64px;
  display: inline-block;
}

/* 状态图标颜色 */
.status-icon.waiting {
  color: var(--theme-warning, #faad14);
  animation: spin 1s linear infinite;
}

.status-icon.success {
  color: var(--theme-success, #52c41a);
  animation: bounce 0.6s ease-out;
}

.status-icon.error {
  color: var(--theme-error, #ff4d4f);
  animation: shake 0.6s ease-out;
}

/* 动画效果 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 文本区域 */
.text-section {
  margin-bottom: 40px;
}

.status-title {
  font-size: 24px;
  color: var(--theme-text-color, #333);
  margin-bottom: 12px;
  font-weight: 500;
}

.status-desc {
  font-size: 16px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  margin: 0;
}

/* 操作区域 */
.action-section {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.continue-button {
  width: 100%;
  height: 45px;
  background: var(--theme-primary, #1890ff);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continue-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

.close-button {
  width: 100%;
  height: 45px;
  background: var(--theme-bg-secondary, #f5f5f5);
  color: var(--theme-text-color, #333);
  border: 1px solid var(--theme-border-color, #d9d9d9);
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: var(--theme-bg-hover, #e6f7ff);
  border-color: var(--theme-primary, #1890ff);
  color: var(--theme-primary, #1890ff);
}

/* 底部区域 */
.footer-section {
  border-top: 1px solid var(--theme-border-color, #e8e8e8);
  padding-top: 20px;
}

.footer-links {
  margin-bottom: 8px;
}

.footer-link {
  color: var(--theme-text-tertiary, #999);
  text-decoration: none;
  font-size: 12px;
}

.footer-link:hover {
  color: var(--theme-primary, #1890ff);
}

.copyright {
  font-size: 12px;
  color: var(--theme-text-tertiary, #999);
  margin: 0;
}

/* 响应式设计 */

/* 页面进入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 主题适配 */

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .result-container {
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.processing {
  background: var(--theme-warning, #faad14);
  animation: pulse 1.5s infinite;
}

.status-indicator.success {
  background: var(--theme-success, #52c41a);
}

.status-indicator.failed {
  background: var(--theme-error, #ff4d4f);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
