/* 自助付款页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 隐藏未编译的Vue模板 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #ff7830;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
}

/* 页面容器 */
.page-container {
  flex: 1;
  background: #fff;
  max-width: 640px;
  margin: 0 auto;
  min-height: 100vh;
  position: relative;
}

/* 店铺信息 */
.shop-info {
  text-align: center;
  padding: 30px 20px;
  background: #fff;
}

.shop-logo {
  width: 80px;
  height: 80px;
  background: #f0f0f0;
  border-radius: 50%;
  margin: 0 auto 15px;
  background-image: url("/public/static/images/default-shop-logo.png");
  background-size: cover;
  background-position: center;
}

.shop-name {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

/* 金额输入区域 */
.amount-section {
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.amount-box {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.input-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.label {
  font-size: 16px;
  color: #666;
}

.amount-display {
  display: flex;
  align-items: center;
  font-size: 32px;
  font-weight: 500;
  color: #333;
  position: relative;
}

.amount-text {
  min-width: 60px;
  text-align: right;
}

.cursor-blink {
  width: 2px;
  height: 32px;
  background: #ff7830;
  margin-left: 5px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

.balance-box {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #e9ecef;
}

.balance-box p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.text-orange {
  color: #ff7830 !important;
}

/* 消息提示 */
.message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 16px;
  z-index: 1000;
  max-width: 80%;
  text-align: center;
}

.message-box.error {
  background: rgba(255, 59, 48, 0.9);
}

.message-box.success {
  background: rgba(52, 199, 89, 0.9);
}

/* 数字键盘 */
.keyboard-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #e0e0e0;
  padding: 15px;
  max-width: 640px;
  margin: 0 auto;
}

.brand-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: #999;
  font-size: 14px;
}

.brand-logo {
  width: 20px;
  height: 20px;
  background-image: url("/public/static/images/brand-logo.png");
  background-size: contain;
  margin-right: 5px;
}

.keyboard-grid {
  display: grid;
  gap: 10px;
}

.key-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 10px;
}

.key-row:first-child {
  grid-template-columns: 1fr 1fr 1fr 1fr 2fr;
}

.key-row:last-child {
  grid-template-columns: 2fr 1fr 2fr;
}

.key {
  height: 50px;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.key:hover {
  background: #e9ecef;
}

.key:active {
  background: #dee2e6;
  transform: scale(0.95);
}

.pay-key {
  background: #ff7830;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  grid-row: span 2;
  height: 110px;
}

.pay-key:hover {
  background: #e6691a;
}

.pay-key:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.pay-key.active {
  background: #ff7830;
  color: #fff;
}

.pay-key.active:hover {
  background: #e6691a;
}

.delete-key,
.hide-key {
  background: #6c757d;
  color: #fff;
}

.delete-key:hover,
.hide-key:hover {
  background: #5a6268;
}

.delete-key i,
.hide-key i {
  font-size: 20px;
}

/* 支付订单弹窗 */
.order-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 2000;
}

.order-container {
  background: #fff;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  width: 100%;
  max-width: 640px;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.order-header {
  text-align: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-header h3 {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.order-list,
.balance-section {
  list-style: none;
  margin: 0;
  padding: 0;
}

.order-item,
.balance-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 15px 0;
}

.order-item:last-child,
.balance-item:last-child {
  border-bottom: none;
}

.item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item-left {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
}

.item-right {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.check-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  background-image: url("/public/static/images/check-icon.png");
  background-size: contain;
}

.order-brand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
}

.order-brand .brand-logo {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.pay-button {
  width: 100%;
  height: 50px;
  background: #ff7830;
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 20px;
}

.pay-button:hover {
  background: #e6691a;
}

.pay-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* 响应式设计 */

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

/* 兼容性样式 - 与原版本保持一致 */
.key:active {
  background: #dee2e6;
  transform: scale(0.95);
}

.key.hover {
  background: #e9ecef;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .key:hover {
    background: #f8f9fa;
  }

  .key:active {
    background: #e9ecef;
  }
}

/* 防止用户选择文本 */
body {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}

/* 确保在小屏幕设备上的显示效果 */
@media (max-width: 375px) {
  .shop-name {
    font-size: 18px;
  }

  .amount-display {
    font-size: 28px;
  }

  .key {
    height: 45px;
    font-size: 16px;
  }

  .pay-key {
    height: 100px;
    font-size: 14px;
  }
}
