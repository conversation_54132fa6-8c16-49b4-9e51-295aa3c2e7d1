/* 功能测试导航页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
}

/* 隐藏未编译的Vue模板 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #ff7830;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
}

/* 导航容器 */
.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

/* 页面标题 */
.nav-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* BID信息区域 */
.bid-info {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.bid-info.bid-error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  border-color: #f5c6cb;
}

.bid-header {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
}

.bid-value {
  font-family: "Courier New", monospace;
  font-weight: bold;
  color: #155724;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  margin-left: 8px;
}

.bid-error .bid-value {
  color: #721c24;
}

.bid-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
  font-size: 14px;
}

.stat-item {
  color: #155724;
}

.bid-error .stat-item {
  color: #721c24;
}

.stat-separator {
  color: #6c757d;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover:not(:disabled) {
  background: #c82333;
  transform: translateY(-1px);
}

.clear-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.bid-warning {
  text-align: center;
  margin-top: 15px;
  color: #721c24;
  font-size: 13px;
  background: rgba(255, 255, 255, 0.7);
  padding: 8px;
  border-radius: 4px;
}

/* 搜索容器 */
.search-container {
  margin-bottom: 25px;
}

.search-input {
  font-size: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.layui-input-suffix {
  background: #f8f9fa;
  border-left: 2px solid #e9ecef;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.layui-input-suffix i {
  color: #6c757d;
  font-size: 18px;
}

/* 分类统计 */
.category-stats {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.stat-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.stat-card.collapsed {
  opacity: 0.7;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007bff, #6610f2);
}

.stat-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  font-size: 14px;
}

.stat-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  min-width: 40px;
}

/* 导航列表 */
.nav-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 分类区域 */
.category-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.category-section:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.category-section.collapsed {
  opacity: 0.8;
}

/* 分类标题 */
.category-title {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 15px 20px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  user-select: none;
}

.category-title:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.category-icon {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.category-count {
  margin-left: auto;
  font-size: 14px;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
}

/* 分类内容 */
.category-content {
  padding: 0;
}

/* 导航项 */
.nav-item {
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:last-child {
  border-bottom: none;
}

.nav-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.nav-item.tested {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border-left: 4px solid #28a745;
}

.nav-item.tested:hover {
  background: linear-gradient(135deg, #c3e6cb 0%, #b8dacc 100%);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
}

.nav-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-name {
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

.nav-path {
  font-size: 12px;
  color: #6c757d;
  font-family: "Courier New", monospace;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  align-self: flex-start;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.test-checkbox {
  transform: scale(1.3);
  cursor: pointer;
  accent-color: #28a745;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #dee2e6;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-section {
  animation: fadeInUp 0.3s ease-out;
}

.nav-item {
  animation: fadeInUp 0.2s ease-out;
}

/* 深色模式支持 */

/* 打印样式 */
