/* 注册成功页面样式 */
[v-cloak] {
  display: none !important;
}

/* 成功页面容器 */
.success-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: var(--bg-color, #f8f9fa);
}

/* 成功内容区域 */
.success-content {
  text-align: center;
  background: var(--card-bg, #fff);
  border-radius: 12px;
  padding: 60px 40px 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  max-width: 400px;
  width: 100%;
}

/* 成功图标 */
.success-icon {
  margin-bottom: 20px;
}

.success-icon i {
  font-size: 100px;
  color: #07c160;
  display: block;
  animation: successPulse 1.5s ease-in-out;
}

/* 成功标题 */
.success-title {
  font-size: 24px;
  font-weight: 500;
  color: var(--text-color, #333);
  margin: 0 0 20px 0;
}

/* 成功消息 */
.success-message {
  color: var(--text-secondary, #666);
  line-height: 1.6;
  font-size: 16px;
}

.success-message span {
  display: inline-block;
}

/* 操作按钮容器 */
.action-container {
  width: 100%;
  max-width: 400px;
  padding: 0 10px;
}

/* 关闭按钮 */
.close-btn {
  border: 2px solid #07c160 !important;
  color: #07c160 !important;
  background: transparent !important;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #07c160 !important;
  color: #fff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

.close-btn:active {
  transform: translateY(0);
}

/* 响应式设计 */

/* 深色模式支持 */

/* 动画效果 */
@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-content {
  animation: fadeInUp 0.6s ease-out;
}

.action-container {
  animation: fadeInUp 0.8s ease-out;
}

/* 主题色变量支持 */
.success-icon i {
  color: var(--success-color, #07c160);
}

.close-btn {
  border-color: var(--success-color, #07c160) !important;
  color: var(--success-color, #07c160) !important;
}

.close-btn:hover {
  background: var(--success-color, #07c160) !important;
}

/* 无障碍访问支持 */
.close-btn:focus {
  outline: 2px solid var(--success-color, #07c160);
  outline-offset: 2px;
}

/* 打印样式 */
