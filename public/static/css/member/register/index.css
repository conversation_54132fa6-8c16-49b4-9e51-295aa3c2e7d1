/* 注册页面样式 */
[v-cloak] {
  display: none !important;
}

/* 加载状态样式 */
.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: var(--theme-color, #1e9fff);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 10px;
  display: block;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #666;
}

/* 页面容器 */
.page-container {
  padding: 20px;
  padding-bottom: 54px;
  min-height: 100vh;
  background: var(--bg-color, #f8f9fa);
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: var(--text-color, #333);
  margin: 0;
  padding: 20px 0;
}

/* 表单容器 */
.form-container {
  max-width: 500px;
  margin: 0 auto;
}

/* 注册表单样式 */
.register-form {
  background: var(--card-bg, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.register-form .layui-form-item {
  margin-bottom: 20px;
}

.register-form .layui-form-label {
  width: 90px;
  font-weight: 500;
  color: var(--text-color, #333);
}

.register-form .layui-input-block {
  margin-left: 90px;
}

.register-form .layui-input {
  border: 1px solid var(--border-color, #e6e6e6);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.register-form .layui-input:focus {
  border-color: var(--theme-color, #1e9fff);
  box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
}

/* 提交按钮样式 */
.submit-btn {
  background: var(--theme-color, #1e9fff);
  border: none;
  border-radius: 6px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: var(--theme-hover-color, #0d8ddb);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 159, 255, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn i {
  margin-right: 6px;
}

/* 温馨提示样式 */
.tips-field {
  background: var(--card-bg, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e6e6e6);
}

.tips-field legend {
  color: var(--theme-color, #1e9fff);
  font-weight: 500;
  font-size: 14px;
}

.notice-content {
  color: var(--text-secondary, #666);
  line-height: 1.6;
  font-size: 14px;
  padding: 10px 0;
}

/* 响应式设计 */

/* 深色模式支持 */

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container {
  animation: fadeIn 0.5s ease-out;
}

/* 表单验证错误状态 */
.layui-form-danger {
  border-color: #ff5722 !important;
}

.layui-form-danger:focus {
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.1) !important;
}

/* 成功状态 */
.success-message {
  background: #f0f9ff;
  border: 1px solid #b3e5fc;
  color: #0277bd;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.success-message i {
  margin-right: 8px;
  color: #4caf50;
}
