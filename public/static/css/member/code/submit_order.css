/* 订单提交页面样式 */
/* 基础样式已在common.css中定义 */

/* 表单项样式 */
.layui-form-item {
  margin-bottom: 0;
}

.layui-form-label {
  width: 70px;
}

.layui-form-item .layui-input-inline {
  margin: 0 0 10px 102px;
}

/* 上传组件样式 */
.uploadifive-button,
.uploadifive-button:hover {
  background-image: url("/static/img/add.png");
  z-index: 999;
  background-size: contain;
  border: none;
  background-color: transparent;
  cursor: pointer;
}

.uploadifive-button input {
  width: 80px;
  height: 80px;
}

.one_img {
  position: relative;
  display: inline-block;
}

.one_img img {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  object-fit: cover;
}

.remove_img {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 20px;
  color: var(--theme-text);
  font-weight: bolder;
  z-index: 1;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

#queue {
  border: 1px solid #e5e5e5;
  height: 177px;
  overflow: auto;
  margin-bottom: 10px;
  padding: 0 3px 3px;
  width: 300px;
}

.img_list {
  padding: 5px;
  float: left;
}

/* 图片卡片样式 */
.image-card {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding-top: 4px;
}

.card-header {
  background-color: var(--theme-primary);
  color: #fff;
  padding: 4px 16px;
  font-size: 12px;
  line-height: 1.4;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.card-content {
  display: grid;
}

.multiline-text {
  width: -webkit-fill-available;
  min-height: 80px;
  border: 1px solid #eee;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  outline: none;
}

.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 16px;
}

/* 按钮样式 */
.btn {
  padding: 4px 14px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--theme-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme-hover);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666;
}

.btn-secondary:hover {
  background-color: #e8e8e8;
}

/* 商品规格样式 */
.goods-specs {
  display: inline-block;
  background-color: var(--theme-shadow);
  color: var(--theme-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  vertical-align: middle;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  margin-left: 4px;
  margin-top: 2px;
}

/* 商品列表容器样式 */
.goods-list-container {
  padding: 10px 10px 0 10px;
}

.goods-legend {
  text-align: center;
  margin-left: 0;
}

.goods-field-box {
  padding: 0;
}

.goods-row {
  margin: 0;
}

.goods-card {
  box-shadow: none;
}

.goods-card-body {
  padding: 0;
}

.price-small {
  font-size: 12px;
}

.price-large {
  font-size: 16px;
}

/* 上传相关样式 */
.upload-form-item {
  display: block;
}

.img-box-container {
  padding-top: 10px;
  padding-left: 20px;
  display: block;
}

.img-list-inline {
  display: inline-block;
  margin-right: 10px;
  margin-bottom: 10px;
}

.upload-queue {
  display: none;
}

.file-upload-input {
  display: none;
  height: 80px;
  width: 80px;
}

.wechat-upload-btn {
  display: none; /* 初始隐藏，通过JS控制显示 */
  width: 60px;
  height: 60px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;
  text-align: center;
  /* 当显示时使用flex布局实现完美居中 */
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box; /* 确保边框包含在尺寸内 */
  padding: 4px; /* 内边距确保内容不贴边 */
}

.wechat-upload-btn:hover {
  border-color: var(--theme-primary);
}

.upload-icon {
  font-size: 24px; /* 稍微减小图标尺寸 */
  color: #999;
  transition: all 0.3s;
  margin-bottom: 2px; /* 图标下方间距 */
}

.wechat-upload-btn:hover .upload-icon {
  color: var(--theme-primary);
}

.upload-text {
  font-size: 10px;
  color: #666;
  margin: 0;
  padding: 0;
  line-height: 1;
  white-space: nowrap; /* 防止文字换行 */
}

.pay-info-container {
  text-align: center;
  font-size: 18px;
  color: var(--theme-text);
  font-weight: 500;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 10px 0;
}

/* 商品列表项样式优化 */
.goods-item {
  padding: 8px 5px;
  display: flex;
  align-items: flex-start;
  min-height: 70px;
  border-bottom: 1px solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  flex-shrink: 0;
  margin-right: 10px;
}

.goods-image img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.goods-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 60px;
}

.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.goods-name {
  font-size: 16px;
  margin: 0;
  flex: 1;
  line-height: 1.4;
  padding-right: 10px;
  color: #333;
}

.goods-price {
  color: var(--theme-text);
  white-space: nowrap;
  font-weight: 500;
}

.goods-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.goods-quantity {
  color: #999;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* SKU规格信息样式 */
.goods-specs-info {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin-top: 4px;
}

.goods-specs-item {
  display: inline-block;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 2px 6px;
  margin-left: 8px;
  margin-right: 4px;
  font-size: 11px;
  color: #333;
}

/* 店铺卡片样式 */
.store_card {
  height: 50px;
  color: #333;
  display: list-item;
  background-color: #fff;
  padding-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  padding: 15px;
}

/* Tab标题居中 */
.tab-title-center {
  text-align: center;
}

.tab-title-center .layui-tab-title li {
  margin: 0 10px;
}

/* 内容区域padding */
.content-padding {
  padding-top: 15px;
}

.tab-content-no-padding {
  padding: 0;
}

/* 表单标签优化 - 手机端适配 */
.layui-form-item .layui-form-label {
  width: 120px;
  text-align: right;
  padding-right: 15px;
  position: relative;
}

.layui-form-item .layui-input-inline {
  margin-left: 135px;
  width: calc(100% - 145px);
}

/* 必填项星号样式 - 支持HTML required属性 */
.layui-form-label[required]:after {
  content: " *";
  color: red;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

/* Vue门店信息显示样式 */
.selected-store-info {
  margin-top: 10px;
  padding: 8px 0;
}

.store-info-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid var(--theme-primary);
}

.store-info-card .store-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.store-info-card .store-address {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.no-stores-tip {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 13px;
}

.no-stores-tip .layui-icon {
  margin-right: 5px;
  color: #ffb800;
}

/* Vue门店选择器样式 */
.vue-store-selector {
  position: relative;
  width: 100%;
}

.selected-store-display,
.store-placeholder {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  padding: 12px;
  background: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 50px;
}

.selected-store-display:hover,
.store-placeholder:hover {
  border-color: var(--theme-primary);
}

.store-placeholder {
  color: #999;
}

.store-info .store-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.store-info .store-address {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.store-info .store-business-time {
  font-size: 11px;
  color: #999;
}

.store-list-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.store-list {
  padding: 0;
}

.store-item {
  padding: 12px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.store-item:hover {
  background: #f8f9fa;
}

.store-item.active {
  background: #e8f4fd;
  border-left: 3px solid var(--theme-primary);
}

.store-item:last-child {
  border-bottom: none;
}

.store-main-info .store-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.store-main-info .store-address {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.store-extra-info {
  text-align: right;
  flex-shrink: 0;
  margin-left: 10px;
}

.store-distance {
  font-size: 11px;
  color: var(--theme-primary);
  margin-bottom: 2px;
}

.store-business-time {
  font-size: 11px;
  color: #999;
}

.store-count-tip {
  text-align: center;
  padding: 8px;
  color: #666;
  font-size: 12px;
  background: #f0f9ff;
  border-radius: 4px;
  margin-top: 8px;
}

.store-count-tip .layui-icon {
  margin-right: 4px;
  color: var(--theme-primary);
}

/* 商品列表样式 - 参考订单详情页面 */
.card-header-icon {
  margin-right: 8px;
  color: var(--theme-primary);
}

.goods-item {
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
  flex-shrink: 0;
}

.goods-name {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
  font-weight: 500;
}

.goods-meta {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-top: 4px;
}

.goods-spec-tag {
  display: inline-block;
  background: #f5f5f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-left: 8px;
  vertical-align: middle;
  border: 1px solid #e8e8e8;
}

.goods-price {
  font-size: 16px;
  color: var(--theme-primary);
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
}

.goods-total {
  margin-top: 10px;
}

.no-goods-tip {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.no-goods-tip .layui-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
  display: block;
}

.no-goods-tip p {
  font-size: 14px;
  margin-bottom: 20px;
}

.no-goods-tip .layui-btn {
  border-radius: 20px;
}

/* 地址解析按钮布局 */
.address-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 0 5px;
}

.address-parse-btn {
  background: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  color: white !important;
}

.wechat-address-btn {
  background: #07c160 !important;
  border-color: #07c160 !important;
  color: white !important;
}

.wechat-address-btn .layui-icon {
  margin-right: 4px;
}

/* 商品加载状态 */
.goods-loading {
  padding: 0;
}

.goods-skeleton {
  display: flex;
  align-items: flex-start;
  padding: 15px 0;
  border-bottom: 1px solid #f5f5f5;
}

.goods-skeleton:last-child {
  border-bottom: none;
}

.skeleton-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6px;
  flex-shrink: 0;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-title {
  height: 18px;
  width: 70%;
}

.skeleton-meta {
  height: 14px;
  width: 50%;
}

.skeleton-price {
  width: 60px;
  height: 18px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 图片加载状态 */
.goods-image-container {
  position: relative;
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ccc;
  font-size: 24px;
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
  position: relative;
  z-index: 1;
}

/* 无商品占位 */
.no-goods-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-goods-placeholder .layui-icon {
  font-size: 36px;
  color: #ddd;
  margin-bottom: 12px;
  display: block;
}

.no-goods-placeholder p {
  font-size: 14px;
  margin: 0;
}

/* 提交按钮样式优化 */
.submit-form-item {
  padding: 20px 15px 30px 15px; /* 上20px 左右15px 下30px */
  margin-bottom: 0;
}

.submit-form-item .layui-btn {
  border-radius: 8px; /* 圆角更现代 */
  font-size: 16px;
  font-weight: 500;
  height: 48px; /* 增加按钮高度 */
  line-height: 48px;
}

/* 商品详细信息样式优化 */
.goods-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.goods-specs {
  font-size: 12px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid var(--theme-primary);
}

.goods-specs .layui-icon {
  margin-right: 4px;
  color: var(--theme-primary);
}

.goods-sku {
  font-size: 11px;
  color: #999;
}

.sku-label {
  font-weight: 500;
}

.sku-value {
  font-family: monospace;
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 2px;
}

.goods-quantity {
  font-size: 13px;
  color: #333;
}

.quantity-label, .stock-label, .subtotal-label {
  font-weight: 500;
  margin-right: 4px;
}

.quantity-value {
  font-weight: 600;
  color: var(--theme-primary);
}

.unit-value {
  margin-left: 2px;
  color: #666;
  font-size: 12px;
}

.goods-stock {
  font-size: 12px;
}

.stock-value.unlimited {
  color: #52c41a;
}

.stock-value.limited {
  color: #1890ff;
}

.stock-value.out {
  color: #ff4d4f;
}

.goods-subtotal {
  font-size: 13px;
  font-weight: 500;
  padding-top: 4px;
  border-top: 1px solid #f0f0f0;
}

.subtotal-value {
  color: var(--theme-primary);
  font-weight: 600;
}

/* 省市区Vue化样式 */
.loading-tip {
  font-size: 12px;
  color: #999;
  text-align: center;
  padding: 4px 0;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.layui-input-inline {
  position: relative;
}

.layui-input-inline select:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}
  margin-right: 4px;
  color: var(--theme-primary);
}

/* 手机端优化样式 */
.goods-list-container {
  padding: 8px;
}

.goods-item {
  padding: 8px 5px;
  min-height: 65px;
}

.goods-image {
  margin-right: 8px;
}

.goods-image img {
  width: 55px;
  height: 55px;
}

.goods-name {
  font-size: 15px;
  line-height: 1.3;
}

.goods-price {
  font-size: 14px;
}

.goods-quantity {
  font-size: 13px;
  line-height: 1.6;
}

.goods-specs {
  font-size: 11px;
  padding: 2px 5px;
  margin-left: 4px;
  margin-top: 2px;
  display: inline-block;
  max-width: calc(100% - 80px);
}

.goods-header {
  margin-bottom: 6px;
}

.goods-content {
  min-height: 55px;
}

.image-card {
  margin: 10px 5px;
}

.card-header {
  padding: 8px 12px;
}

.card-footer {
  padding: 8px 12px;
}

.upload-queue {
  width: 100%;
}

/* 移动端上传按钮调整 */
.wechat-upload-btn {
  width: 60px;
  height: 60px;
  /* 移动端保持相同尺寸，显示由JS控制 */
}

/* 移动端提交按钮优化 */
.submit-form-item {
  padding: 20px 15px 40px 15px; /* 移动端底部增加更多间距 */
  /* 支持安全区域 */
  padding-bottom: calc(40px + env(safe-area-inset-bottom));
}

.submit-form-item .layui-btn {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.one_img img {
  width: 70px;
  height: 70px;
}

.pay-info-container {
  font-size: 16px;
  padding: 15px;
}

/* ===== 订单提交页面样式 ===== */
.card-header {
  background-color: var(--theme-primary) !important;
}

.recognize-btn {
  background-color: var(--theme-primary) !important;
  color: #FFF !important;
}
.clear-btn {
  background-color: #999;
  color: #FFF !important;
}
.clear-btn:hover {
  color: #FFF !important;
}
.iPicker-target[data-theme=panel] .iPicker-list{
  width: auto !important;
}
