/* ===================================================================
   商品选择页面样式文件 (choose_goods.css)

   项目：易卡易券卡兑换系统
   功能：用户使用券卡选择兑换商品的页面样式

   ===================================================================

   📋 功能模块说明：
   ✅ 券卡信息展示 - 显示券卡详情、可用次数/金额、有效期等
   ✅ 商品展示系统 - 支持列表布局和网格布局两种模式
   ✅ SKU属性选择 - 多规格商品的属性选择和SKU匹配
   ✅ 数量控制系统 - 商品数量增减控制，支持库存限制
   ✅ 购物车管理 - 购物车模态框，商品管理和总价计算
   ✅ 响应式布局 - 适配移动端和桌面端显示

   ===================================================================

   📂 代码结构：
   1. 🌐 全局样式重置和基础设置
   2. 🎠 轮播图区域样式
   3. 💳 券卡信息展示区域
   4. 📋 商品列表布局（模板1）
   5. 🔲 商品网格布局（模板2）
   6. 🎛️ 商品数量控制按钮系统
   7. 🛒 购物车模态框样式
   8. 🔽 底部导航和提交按钮
   9. 📱 SKU选择弹窗样式
   10. 📐 响应式布局适配

   ===================================================================

   🎨 设计规范：
   - 主色调：#00ae66 (绿色提交按钮)
   - 价格色：#e21323 (红色价格显示)
   - 背景色：#f5f5f5 (页面背景)
   - 边框色：#eee (分割线)
   - 文字色：#333 (主要文字), #666 (次要文字), #999 (辅助文字)

   ================================================================= */

/* ===================================================================
   🌐 1. 全局样式重置和基础设置
   ================================================================= */

/* Vue.js v-cloak 指令样式 - 隐藏未编译的模板 */
[v-cloak] {
  display: none !important;
}

/* 防止Vue编译前显示花括号和闪烁 */
.vue-loading {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.vue-loaded {
  opacity: 1;
}

/* 页面初始化时隐藏内容，防止花括号闪现 */
#app:not(.vue-loaded) {
  visibility: hidden;
}

#app.vue-loaded {
  visibility: visible;
}

/* 📋 HTML元素样式重置
   目的：消除浏览器默认样式差异，确保跨浏览器一致性显示
   影响：所有HTML基础元素的margin、border、padding归零 */
html,
body,
header,
section,
footer,
div,
ul,
ol,
li,
img,
a,
span,
em,
del,
legend,
center,
strong,
var,
fieldset,
form,
label,
dl,
dt,
dd,
cite,
input,
hr,
time,
mark,
code,
figcaption,
figure,
textarea,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0; /* 清除默认外边距 */
  border: 0; /* 清除默认边框 */
  padding: 0; /* 清除默认内边距 */
  font-style: normal; /* 统一字体样式为正常 */
}

/* 全局盒模型和用户交互设置 */
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  outline: none;
}

/* 页面背景色 */
body {
  background-color: #f5f5f5;
}

/* 列表元素重置 */
ul,
ol,
li,
dl,
dd {
  margin: 0;
  padding: 0;
}

/* 移除列表项目符号 */
ul,
ol {
  list-style: none outside none;
}

/* 清除浮动 */
.clear:after,
.clear:before {
  clear: both;
  display: block;
  content: "";
}

/* ===================================================================
   🛠️ 通用工具类 - 减少重复代码
   ================================================================= */

/* 🎨 背景色工具类 */
.bg-white {
  background: #fff !important;
}

.bg-gray {
  background: #f5f5f5 !important;
}

/* 📐 内边距工具类 */
.p-0 {
  padding: 0 !important;
}

.p-4 {
  padding: 0.4rem 0 !important;
}

.p-8 {
  padding: 8px !important;
}

.p-10 {
  padding: 10px !important;
}

.p-20 {
  padding: 20px !important;
}

.px-5 {
  padding: 0 5px !important;
}

.px-10 {
  padding: 0 10px !important;
}

.py-4 {
  padding: 0.4rem 0 !important;
}

.py-12 {
  padding: 12px 0 !important;
}

/* 🔄 弹性布局工具类 */
.flex {
  display: flex !important;
}

.flex-center {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.flex-between {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.flex-end {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
}

.flex-column {
  display: flex !important;
  flex-direction: column !important;
}

/* 🔘 圆角工具类 */
.rounded-4 {
  border-radius: 4px !important;
}

.rounded-6 {
  border-radius: 6px !important;
}

.rounded-8 {
  border-radius: 8px !important;
}

.rounded-12 {
  border-radius: 12px !important;
}

.rounded-16 {
  border-radius: 16px !important;
}

.rounded-20 {
  border-radius: 20px !important;
}

.rounded-full {
  border-radius: 50% !important;
}

/* ===================================================================
   🎠 2. 轮播图区域样式
   ================================================================= */

/* 轮播图容器 */
.banner-section {
  width: 100%;
}

/* 轮播图片 */
.banner-image {
  width: 100%;
  height: auto;
  display: block;
}

/* ===================================================================
   💳 3. 券卡信息展示区域
   ================================================================= */

/* 券卡信息容器 */
.card-info-section {
  margin-bottom: 1px;
}

.card-label {
  font-size: 1rem;
}

/* 可用信息容器 */
.card-usage-info {
  flex: 1;
}

/* 有效期信息行 */
.card-expire-row {
  border-bottom: 1px solid #f8f8f8;
}

/* 使用规则说明 */
.card-rules-row {
  text-align: center;
  display: block;
}

.rule-text {
  color: inherit;
}

.rule-text--value {
  color: var(--theme-primary);
}

/* 券卡详细描述 */
.card-description {
  text-align: left;
  display: block;
}

/* 数字强调色 */
.number_color {
  color: var(--theme-text);
  font-weight: bold;
}

/* 列表元素样式重置 */
ul,
ol,
li,
dl,
dd {
  margin: 0; /* 清除默认外边距 */
  padding: 0; /* 清除默认内边距 */
}

/* 移除列表默认样式 */
ul,
ol {
  list-style: none outside none; /* 移除列表项目符号 */
}

/* 清除浮动的通用类 */
.clear:after,
.clear:before {
  clear: both; /* 清除浮动 */
  display: block; /* 块级元素 */
  content: ""; /* 空内容 */
}

/* ===================================================================
   📋 4. 商品列表布局（模板1）
   ================================================================= */

/* 商品列表区域容器 */
.goods-list-section {
  margin-bottom: 10px;
  padding-top: 2px;
}

/* 商品列表基础容器 */
.goods_list {
  padding-bottom: 5rem;
  display: block;
}

.template-1 .goods-list {
  margin: 0;
}

.template-1 .goods-item {
  position: relative;
  padding: 0.5rem 0.75rem;
  border-bottom: solid 1px #eee;
  align-items: flex-start;
}

.goods-image-container {
  position: relative;
  width: 6rem;
  flex-shrink: 0;
}

.goods-image {
  width: 100px;
  display: block;
}

/* 缺货状态遮罩层 */
.stock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* 缺货遮罩图片 */
.stock-overlay-image {
  opacity: 0.5;
  background-color: #000;
  height: 100%;
  width: 100px;
}

/* 商品信息容器 */
.goods-info {
  margin: 0 0.75rem 0; /* 左右外边距，与图片保持间距 */
  flex: 1; /* 占据剩余空间 */
}

/* 商品标题 */
.goods-title {
  font-size: 1rem; /* 标准字体大小 */
  margin-bottom: 0.5rem; /* 底部外边距 */
  cursor: pointer; /* 鼠标悬停显示手型，表示可点击 */
}

/* 查看详情链接 */
.goods-detail-link {
  color: #999; /* 灰色文字 */
  font-size: 14px; /* 较小字体 */
  padding: 4px 0; /* 上下内边距 */
  display: inline-block; /* 行内块元素 */
  cursor: pointer; /* 鼠标悬停显示手型 */
}

/* 商品价格区域 */
.goods-price-section {
  margin: 0.5rem 0;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 8px;
  position: relative;
}

/* 价格和规格容器 */
.goods-price-specs-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

/* 商品价格显示 */
.goods-price {
  padding: 4px;
  display: inline-block;
  color: var(--theme-text);
  font-weight: normal;
  flex-shrink: 0;
}

/* 价格占位符 */
.goods-price-placeholder {
  padding: 4px;
  display: inline-block;
  flex-shrink: 0;
}

/* 商品规格标签 - 智能布局 */
.goods-specs {
  background-color: #edf1fc;
  color: coral;
  padding: 4px 6px;
  border-radius: 4px;
  line-height: 1.4;
  font-size: 12px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  display: inline-block;
  flex-shrink: 1;
  flex-basis: auto;
  min-width: 0;
  max-width: 100%;
}

/* 短规格 - 与价格同行 */
.goods-specs.short-specs {
  flex-basis: auto;
  width: auto;
  max-width: 100%;
  flex-shrink: 1;
  margin-left: 8px;
}

/* 当短规格容器空间不足时，规格换行显示 */

/* 长规格 - 独立换行，铺满整行 */
.goods-specs.long-specs {
  display: block;
  width: 100%;
  margin-top: 4px;
  position: relative;
  z-index: 1;
}

/* ===== 模板1灵活布局样式 ===== */
/* 模板1灵活布局容器 */
.goods-price-section-flexible {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

/* 模板1第一行：价格、短规格、按钮 */
.price-controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

/* 模板1中的短规格 - 与价格同行 */
.goods-specs.list-specs-short {
  display: inline-block;
  margin-left: 8px;
  flex-shrink: 1;
  max-width: 150px;
}

/* 模板1中的长规格 - 独立第二行 */
.goods-specs.list-specs-long {
  display: block;
  width: 100%;
  margin-top: 0;
  margin-left: 0;
}

/* 模板1灵活布局中的数量控制按钮 */
.goods-price-section-flexible .quantity-controls {
  flex-shrink: 0;
  margin-left: auto;
}

/* 价格行容器 */
.price-row {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  flex-wrap: wrap;
}

/* ===================================================================
   🎛️ 6. 商品数量控制按钮系统
   ================================================================= */

/* 🔢 数量控制按钮组容器
   功能：包含减号按钮、数量显示和加号按钮
   设计：半透明白色背景，圆角边框，居中对齐 */
.quantity-controls {
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  gap: 6px; /* 元素间距 */
  flex-shrink: 0; /* 不收缩 */
  background: rgba(255, 255, 255, 0.9); /* 半透明背景 */
  border-radius: 16px; /* 圆角边框 */
  padding: 2px 6px; /* 内边距 */
  min-width: 80px; /* 最小宽度 */
  justify-content: center; /* 水平居中 */
  margin-left: auto; /* 右对齐 */
}

/* 🔍 短规格布局中的数量控制按钮组
   位置：顶部对齐，避免与规格信息重叠 */
.price-and-controls .quantity-controls {
  align-self: flex-start; /* 顶部对齐 */
  margin-top: 0; /* 移除顶部外边距 */
}

/* 🔘 数量控制按钮基础样式（加号、减号）
   尺寸：22px × 22px 标准尺寸 */
.quantity-btn {
  border: none; /* 无边框 */
  cursor: pointer; /* 鼠标手型 */
  width: 22px; /* 固定宽度 */
  height: 22px; /* 固定高度 */
  flex-shrink: 0; /* 不收缩 */
}

/* 🔢 数量显示元素
   功能：显示当前选择的商品数量
   样式：粗体显示，居中对齐 */
.quantity-display {
  display: inline-block !important; /* 强制行内块显示 */
  min-width: 24px; /* 最小宽度 */
  text-align: center; /* 文字居中 */
  font-style: normal; /* 正常字体样式 */
  line-height: 22px; /* 行高与按钮一致 */
  font-size: 14px; /* 字体大小 */
  font-weight: bold; /* 粗体显示 */
  color: #333; /* 深灰色文字 */
  background: transparent; /* 透明背景 */
}

/* 库存信息显示 */
.goods-stock {
  word-break: break-all; /* 允许单词内换行 */
  color: #999; /* 灰色文字 */
  padding-top: 4px; /* 顶部内边距 */
  margin: 0; /* 清除外边距 */
  font-size: 0.9rem; /* 较小字体 */
}

/* ===================================================================
   🔲 5. 商品网格布局（模板2）
   ================================================================= */

.template-2 .category-group {
  margin-bottom: 1rem;
}

.category-banner {
  padding: 5px 8px;
}

.category-image {
  width: 100%;
  height: auto;
  display: block;
}

.grid-goods-item {
  background: #fff;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.grid-goods-link {
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.grid-image-container {
  position: relative;
}

.grid-goods-image {
  border-radius: 4px;
  width: 100%;
  height: auto;
}

.grid-stock-overlay {
  position: relative;
  margin-top: -47vw;
}

.grid-stock-overlay-image {
  opacity: 0.5;
  background-color: #000;
  height: 100%;
  border-radius: 4px;
  width: 100%;
}

.grid-goods-info {
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.grid-goods-title {
  font-size: 14px;
  color: #454545;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 40px;
  overflow: hidden;
  margin-bottom: 8px;
  cursor: pointer;
}

.grid-price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
  gap: 4px;
}

/* 网格布局自适应价格区域 */
.grid-price-section-adaptive {
  margin-bottom: 6px;
}

/* 网格布局价格信息行 */
.grid-price-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 4px;
}

.grid-price {
  color: var(--theme-text);
  font-size: 14px;
  flex-shrink: 0;
}

.grid-specs {
  background-color: #edf1fc;
  color: coral;
  padding: 3px 5px;
  border-radius: 4px;
  font-size: 11px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.3;
}

/* 短规格 - 与价格同行 */
.grid-specs-short {
  display: inline-block;
  width: auto;
  margin-top: 0;
  flex-shrink: 1;
  max-width: 100px;
}

.grid-stock {
  color: #999;
  font-size: 11px;
  flex-shrink: 0;
}

.grid-quantity-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
  margin-top: auto;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 2px 4px;
  min-width: 70px;
  width: auto;
  align-self: flex-end;
}

.grid-quantity-btn {
  border: none;
  cursor: pointer;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.grid-quantity-display {
  display: inline-block !important;
  min-width: 20px;
  text-align: center;
  font-style: normal;
  line-height: 20px;
  font-size: 13px;
  font-weight: bold;
  color: #333;
}

/*产品列表*/
.wy-pro-list {
  margin: 0;
  background: #f5f5f5;
}

.wy-pro-list li {
  width: 47%;
  float: left;
  margin-bottom: 2%;
  margin-left: 2%;
  position: relative;
  background: #fff;
}

.wy-pro-list li a {
  display: block;
}

.wy-pro-list .proimg {
  height: 47vw;
  position: relative;
}

.wy-pro-list .proimg img {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  height: 100%;
  width: auto;
  max-width: 100%;
  transform-origin: 50% 50% 0px;
  transform: translate3d(-50%, -50%, 0px);
}

.wy-pro-list .protxt {
  margin-top: 5px;
  padding: 5px 8px;
}

.wy-pro-list .protxt .name {
  font-size: 14px;
  color: #454545;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 40px;
  overflow: hidden;
}

.wy-pro-pri {
  color: var(--theme-text);
  font-size: 14px;
  margin-top: 3px;
  line-height: 24px;
}

.wy-pro-pri span {
  /*font-family: Arial, Helvetica, sans-serif;*/
  padding-left: 3px;
  font-size: 14px;
}

/* ===================================================================
   🎛️ 6. 商品数量控制按钮系统 - 统一样式
   ================================================================= */

/* 🔘 加减按钮通用基础样式 */
.add,
.minus,
.add_disable,
.minus_disable {
  border: none !important;
  cursor: pointer !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 22px !important;
  height: 22px !important;
  background-size: 22px 22px !important;
}

/* ➕ 加号按钮（正常状态） - 使用透明SVG图标 */
.add {
  background-image: url(/static/img/add-icon.svg) !important;
  background-color: transparent !important;
}

/* ➖ 减号按钮（正常状态） - 使用透明SVG图标 */
.minus {
  background-image: url(/static/img/minus-icon.svg) !important;
  background-color: transparent !important;
}

/* 🚫 禁用状态按钮（灰度效果） */
.add_disable,
.minus_disable {
  filter: grayscale(100%) opacity(0.5) !important;
  cursor: not-allowed !important;
  background-color: transparent !important;
}

.add_disable {
  background-image: url(/static/img/add-icon.svg) !important;
}

.minus_disable {
  background-image: url(/static/img/minus-icon.svg) !important;
}

/* 🔲 网格布局按钮尺寸调整（20px） */
.grid-quantity-btn.add,
.grid-quantity-btn.minus,
.grid-quantity-btn.add_disable,
.grid-quantity-btn.minus_disable {
  width: 20px !important;
  height: 20px !important;
  background-size: 20px 20px !important;
}

/* 🛒 购物车中的特殊按钮类名映射 */
.ms2 {
  background-image: url(/static/img/minus-icon.svg) !important;
  background-color: transparent !important;
}

.ad2 {
  background-image: url(/static/img/add-icon.svg) !important;
  background-color: transparent !important;
}

.btn button {
  border: none;
}

.btn {
  float: right;
}

/*底部*/

.footer {
  display: block;
  position: fixed;
  width: 100%;
  z-index: 3;
  bottom: 0;
  color: #000;
  background: #fff;
  /*max-width: 640px;*/
}

.footer .left {
  float: left;
  padding: 0 10px;
  height: 3.6rem;
  line-height: 60px;
  width: 60%;
  border-top: 1px solid #e2e2e2;
}

.count_num {
  display: block;
  background: url(/static/img/cart.png) center center no-repeat;
  background-size: contain;
  position: relative;
  height: 50px;
  width: 50px;
  margin: 5px 10px 5px 0;
  float: left;
}

.count_num span {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #000;
  color: #fff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 15px;
  font-size: 12px;
  padding: 0.2rem;
}

.footer .right {
  display: block;
  float: right;
  width: 40%;
  height: 3.6rem;
}

.footer .right .disable {
  background: #dbdbdb !important;
}

.disable {
  background: #dbdbdb !important;
}

.footer .submit {
  display: block;
  text-align: center;
  background-color: var(--theme-primary);
  width: 100%;
  color: #fff;
  font-size: 16px;
  line-height: 3.6rem;
  height: 100%;
  border: none;
}

/*卡信息样式 */

.cardinfo {
  background: #fff;
}

.cardinfo h3.tit {
  border-bottom: 1px solid #999;
  width: 80%;
  margin: 0 auto;
  position: relative;
  height: 2.5rem;
  line-height: 2.5rem;
  /*margin-bottom: 0.5rem;*/
  text-align: center;
}

.lrfrom {
  padding: 10px;
  padding-bottom: 0;
  font-size: 1rem;
}

.titStyle {
  text-align: start;
  width: 30%;
}

.orangeColor {
  color: #f6b20f !important;
}

.lrfrom div em,
.rightTitStyle {
  -ms-flex: 1;
  flex: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -ms-flex-align: center;
  align-items: center;
}

.lrfrom div {
  -webkit-display: flex;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  border-bottom: 1px solid #f8f8f8;
  padding: 0.4rem 0;
}

/*列表购物车*/

.right-con {
  /*display: none;*/
  width: 100%;
  float: left;
  overflow-y: scroll;
  background: #fff;
  position: relative;
  /* height: 35rem; */
}

.right-con li {
  position: relative;
  padding: 0.5rem 0.75rem;
  border-bottom: solid 1px #eee;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.right-con li .menu-img {
  width: 6rem;
}

.right-con li .menu-txt {
  flex: 1;
  min-width: 0;
  margin: 0;
}

.right-con li .menu-txt p.list1 {
  font-size: 0.65rem;
  margin: 0.15rem 0;
}

.right-con li .menu-txt p.list2 b {
  color: var(--theme-text);
  font-weight: normal;
}

.menu-txt h4 {
  font-size: 1rem;
}

.right-con li .btn {
  background-color: transparent;
  cursor: pointer;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 0.25rem;
}

.right-con li .menu-txt p {
  font-style: normal;
  line-height: 0.9rem;
}

.right-con li .menu-txt p.list2 {
  font-size: 0.8rem;
  margin: 0 5px 0 5px;
}

/* 确保数量显示元素可见 */
.quantity-display,
.grid-quantity-display,
.cart-quantity {
  display: inline-block !important;
}

/* ===================================================================
   🛒 7. 购物车模态框样式
   ================================================================= */

/* 购物车模态框容器 */
.shopping-cart-modal {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 10;
}

.shopping-cart-modal .list {
  position: relative;
}

.shopcart-list {
  position: fixed;
  left: 0;
  bottom: 3.6rem;
  width: 100%;
  min-height: 200px;
  display: block;
  z-index: 99999;
  background: #fff;
}

.cart-items-list {
  margin: 0;
  padding: 0;
}

.cart-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.cart-item-name {
  flex: 2;
  text-align: left;
  padding-left: 10px;
}

.cart-item-price {
  flex: 1;
  text-align: center;
}

.price-symbol {
  margin-right: 2px;
}

.cart-item-controls {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cart-minus-btn,
.cart-plus-btn {
  border: none;
  background: transparent;
  cursor: pointer;
}

.cart-quantity {
  display: inline-block;
  min-width: 20px;
  text-align: center;
  font-style: normal;
}

.shopcart-list .list-header {
  height: 40px;
  line-height: 40px;
  padding: 0 10px;
  background: #f3f5f7;
  border-bottom: 1px solid rgba(7, 17, 27, 0.1);
}

.shopcart-list .list-content {
  padding: 0 10px;
  overflow: hidden;
  background: #fff;
}

.shopcart-list .list-header .title {
  float: left;
  font-size: 16px;
  color: #333;
}

.shopcart-list .list-header .empty {
  float: right;
  font-size: 12px;
  color: var(--theme-text);
  padding-left: 17px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA+klEQVQ4T+2TsW3DMBBF3wlIHY+gEQhlAY2gEdwwcBlPYGWCpLYabRCP4AVCcASPoHQGDPACiopjKbYRI21YHj/f3X3eCX88cu692uIVqIDdcJ8jvMnaLaf6HqBzM+MuewHJe4FQomxH4lFMdxzCUlrfJYB92CDqCWH86FJ7WVaiYqR5rxLgsahBVrfZoc+ydvXRgwSBGLwGmurOAtQaI433vTcR2vruGJsk+gHofZCslsaVp9nUFls01MT+Tyr9B8SZG87YsOEXFiZnT/e7X7DFHDDSuKerc5D2xEvj2jT1XxWkfdiA3APdBcgM9INDqGJVI8BtY/yt/gRwL8IRZU00/AAAAABJRU5ErkJggg==) left center no-repeat;
}

.shopcart-list .list-content .food {
  width: 100%;
  height: 40px;
  line-height: 40px;
}

.food div,
.food .btn {
  float: left;
}

.food div {
  width: 25%;
  text-align: center;
}

.food .btn {
  width: 25%;
}

.food div:nth-child(1) {
  width: 50%;
  text-align: left;
}

.food .btn {
  text-align: center;
  height: 40px;
  overflow: hidden;
}

.food .btn button {
  height: 40px;
}

.food .btn button strong {
  display: inline-block;
  width: 22px;
  height: 22px;
}

.nowrap {
  white-space: nowrap;
  -webkit-text-overflow: ellipsis;
  -moz-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 购物车列表按钮样式 */
.list-content .btn button.ms2,
.list-content .btn button.ad2 {
  width: 22px;
  height: 22px;
  overflow: hidden;
}

/*.shopcart-list .list-content .food i {*/
/*    margin-top: 10px;*/
/*}*/

/* ===================================================================
   🔽 8. 底部导航和提交按钮
   ================================================================= */

/* 底部导航栏 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 999;
  background: #fff;
}

.cart-summary {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.cart-icon {
  margin-right: 10px;
}

.cart-count {
  background: #000;
  color: #fff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 15px;
  font-size: 12px;
  padding: 0.2rem;
  position: absolute;
  top: -5px;
  right: -5px;
}

.total-info {
  font-size: 16px;
  display: block;
  height: 3rem;
  max-width: 200px;
  min-width: 100px;
}

.total-price {
  font-size: 18px;
  color: var(--theme-text);
  padding: 0 5px;
}

.submit-section {
  display: flex;
  align-items: center;
}

.submit-btn {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--theme-primary);
  color: #fff;
  font-size: 16px;
  cursor: pointer;
}

.submit-btn.disable {
  background: #dbdbdb !important;
  cursor: not-allowed;
}

.submit-btn-full {
  display: block;
  text-align: center;
  background-color: var(--theme-primary);
  width: 100%;
  color: #fff;
  font-size: 16px;
  line-height: 2.6rem;
  height: 100%;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.submit-btn-full.disable {
  background: #dbdbdb !important;
  cursor: not-allowed;
}

/* Modal Overlay */
.modal-overlay {
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  display: block;
}

/*购物车底部金额合计*/
#cartN {
  font-size: 16px;
  display: block;
  height: 3rem;
  max-width: 200px;
  min-width: 100px;
}

#total_price_show {
  font-size: 18px;
  color: var(--theme-text);
  padding: 0 5px;
}

/* 下一步按钮 */
.footer_full {
  display: block;
  /*position: fixed;*/
  width: 100%;
  z-index: 3;
  /*bottom: 0;*/
  color: #000;
  background: #fff;
  /*max-width: 640px;*/
  padding: 0 4px;
  padding-bottom: 20px;
}

.footer_full .submit_full {
  display: block;
  text-align: center;
  background-color: var(--theme-primary);
  width: 100%;
  color: #fff;
  font-size: 16px;
  line-height: 2.6rem;
  height: 100%;
  border: none;
  border-radius: 4px;
}

.submit_button_div {
  position: fixed;
  bottom: 0;
}

/* 购物车超过文字换行*/
.accountName {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.goods-num-box {
  margin-left: auto;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 16px;
  padding: 2px 6px;
  flex-shrink: 0;
}

/* ===================================================================
   📱 9. SKU选择弹窗样式
   ================================================================= */

/* SKU选择器 */
.sku-selector {
  margin: 8px 0;
  font-size: 12px;
}

.attr-group {
  margin-bottom: 6px;
}

.attr-group-name {
  color: #666;
  margin-right: 6px;
  font-size: 11px;
}

.attr-options {
  display: inline-block;
}

.attr-option {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 6px;
  margin-bottom: 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f8f8;
  color: #333;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
  line-height: 1.4;
}

.attr-option.selected {
  border-color: var(--theme-primary);
  background: var(--theme-shadow);
  color: var(--theme-primary);
}

.attr-option.disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #f0f0f0;
}

.attr-option:hover:not(.disabled) {
  border-color: var(--theme-primary);
}

/* 网格布局模板的SKU选择器样式调整 */
.template-2 .sku-selector {
  margin: 4px 0;
}

.template-2 .attr-group {
  margin-bottom: 4px;
}

.template-2 .attr-option {
  padding: 1px 6px;
  margin-right: 4px;
  margin-bottom: 2px;
  font-size: 10px;
}

/* 购物车中的规格显示 */
.cart-item-specs {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

/* SKU弹窗样式 */
.sku-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.sku-modal {
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.sku-modal-header {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.sku-modal-product {
  display: flex;
  flex: 1;
}

.sku-modal-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
}

.sku-modal-info {
  flex: 1;
}

.sku-modal-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  margin-bottom: 8px;
}

.sku-modal-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text);
  margin-bottom: 4px;
}

.sku-modal-stock {
  font-size: 14px;
  color: #666;
}

.sku-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
  cursor: pointer;
}

.sku-modal-content {
  padding: 20px;
}

.sku-attr-group {
  margin-bottom: 20px;
}

.sku-attr-group-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 500;
}

.sku-attr-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.sku-attr-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.sku-attr-option.selected {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary);
  color: white;
}

.sku-attr-option.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #eee;
}

.sku-modal-footer {
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sku-quantity-selector {
  display: flex;
  align-items: center;
}

.sku-quantity-label {
  font-size: 14px;
  color: #333;
  margin-right: 12px;
}

.sku-quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.sku-quantity-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sku-quantity-btn:disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.sku-quantity-display {
  min-width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
}

.sku-confirm-btn {
  padding: 12px 24px;
  background-color: var(--theme-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sku-confirm-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 已选择SKU信息显示 */
.selected-sku-info {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.sku-info-label {
  margin-right: 4px;
}

.sku-info-text {
  color: var(--theme-text);
}

/* 可点击规格样式 - 仿照规格选择器样式 */
.clickable-specs {
  display: inline-block;
  padding: 2px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f8f8;
  color: #333;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;
  line-height: 1.4;
  margin: 0 2px;
}

.clickable-specs:hover {
  border-color: var(--theme-primary);
  background: var(--theme-shadow);
  color: var(--theme-primary);
}

.clickable-specs:active {
  border-color: var(--theme-hover);
  background: var(--theme-shadow);
  color: var(--theme-hover);
}

/* 网格布局中的可点击规格样式调整 */
.template-2 .clickable-specs {
  padding: 1px 6px;
  margin: 0 2px;
  font-size: 10px;
}

/* 购物车中的可点击规格样式调整 */
.cart-item-specs.clickable-specs {
  margin-top: 2px;
  font-size: 10px;
}

/* ===== 购物车弹层样式重构 ===== */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 58px;
  background: rgba(0, 0, 0, 0);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.cart-overlay.cart-show {
  background: rgba(0, 0, 0, 0.5);
}

.cart-container {
  background: #fff;
  width: 100%;
  max-width: 500px;
  height: calc(100vh - 120px);
  max-height: 60vh;
  border-radius: 16px 16px 0 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 0;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.cart-overlay.cart-show .cart-container {
  transform: translateY(0);
}

.cart-overlay.cart-hide .cart-container {
  transform: translateY(100%);
}

/* 优化动画性能 */
.cart-container {
  will-change: transform;
  backface-visibility: hidden;
}

.cart-overlay {
  will-change: background-color;
}

.cart-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  flex-shrink: 0;
}

.cart-header-actions {
  display: flex;
  align-items: center;
}

.cart-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.cart-clear-btn {
  color: #999;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.cart-clear-btn:hover {
  color: var(--theme-text);
  background: #fff5f5;
}

.cart-close-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  color: #666;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  margin-left: 8px;
  line-height: 1;
}

.cart-close-btn:hover {
  background: #e8e8e8;
  border-color: #d0d0d0;
  color: #333;
  transform: scale(1.05);
}

.cart-close-btn:active {
  transform: scale(0.95);
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px 20px 20px;
}

.cart-product-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.cart-product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 60px;
}

.product-name {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 6px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-specs {
  margin-bottom: 8px;
}

.specs-tag {
  display: inline-block;
  background: var(--theme-shadow);
  color: var(--theme-primary);
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid var(--theme-primary);
  cursor: pointer;
  transition: all 0.2s;
}

.specs-tag:hover {
  background: var(--theme-shadow);
  border-color: var(--theme-hover);
}

.product-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  color: var(--theme-text);
  font-size: 14px;
  margin-right: 2px;
}

.price-amount {
  color: var(--theme-text);
  font-size: 16px;
  font-weight: 600;
}

.price-placeholder {
  color: #ccc;
  font-size: 14px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 20px;
  padding: 2px;
}

.qty-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.qty-btn:hover {
  background: #f0f0f0;
  color: #333;
}

.qty-btn:active {
  transform: scale(0.95);
}

.qty-minus {
  color: #ff6b35;
}

.qty-plus {
  color: #52c41a;
}

.qty-number {
  min-width: 32px;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  padding: 0 4px;
}

/* ===================================================================
   📐 10. 响应式布局适配
   ================================================================= */

/* 移动端适配 */

.custom-category-tabs {
  margin-bottom: 15px;
  padding: 0 15px;
}

.tab-container {
  display: flex;
  flex-wrap: wrap; /* 允许换行 */
  gap: 8px; /* 标签之间的间距 */
  padding: 10px 0;
  border-bottom: 1px solid #e6e6e6;
}

.tab-item {
  display: inline-block;
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  border: 1px solid #e6e6e6;
  border-radius: 20px; /* 圆角标签 */
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap; /* 防止文字换行 */
  user-select: none; /* 防止选中文字 */
}

.tab-item:hover {
  color: var(--theme-primary);
  border-color: var(--theme-primary);
  background: var(--theme-shadow);
}

.tab-item.active {
  color: #fff;
  background: var(--theme-primary);
  border-color: var(--theme-primary);
  box-shadow: 0 2px 4px var(--theme-shadow);
}

/* 响应式设计 */

/* 券卡简介容器样式 */
.card-info-container {
  background: #ffffff;
  border-radius: 12px;
  margin: 5px; /* 与商品卡片边距保持一致 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 5px;
}

/* 紧凑型信息列表布局 */
.card-info-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 信息条目样式 */
.info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.info-card:hover {
  background: var(--theme-shadow);
  border-color: var(--theme-primary);
}

/* 左侧：图标+标签 */
.info-left {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 小图标样式 */
.info-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: var(--theme-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.info-icon i {
  font-size: 16px;
  color: #ffffff;
}

/* 标签样式 */
.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 右侧：值 */
.info-value {
  font-size: 16px;
  color: var(--theme-primary);
  font-weight: 600;
  text-align: right;
}

.info-unit {
  font-size: 14px;
  color: #999;
  font-weight: 400;
}

/* 规则卡片特殊样式 */
.rule-card {
  background: #fff7e6;
  border-color: #ffd591;
}

.rule-card .info-icon {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.rule-card .info-value {
  color: #fa8c16;
}

/* 自定义卡号图标样式 */
.card-number-icon {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  font-family: "Courier New", monospace;
}

/* 响应式设计 */

/* ===================================================================
   提取的内联样式 - detail.html 页面专用
   ================================================================= */

/* 券卡图片样式 */
.coupon-image {
  width: 100%;
}

/* 券卡简介容器样式 */
.coupon-description {
  padding: 10px 15px;
}
