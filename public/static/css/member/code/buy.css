/* 卡券商城页面样式 */

/* v-cloak 防止模板闪烁 */
[v-cloak] {
  display: none !important;
}

/* 页面基础样式 */
body {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow-x: hidden; /* 防止水平滚动条 */
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: 100vw; /* 防止超出视口宽度 */
}

/* 防止所有元素超出容器宽度 */
* {
  box-sizing: border-box;
}

#app {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #999;
}

/* 页面容器 */
.page-container {
  min-height: auto;
  padding: 0;
  padding-bottom: 80px; /* 为底部导航留出空间 */
  max-width: 100%;
  box-sizing: border-box;
}

/* 页面标题 - 手机端优化 */
.page-header {
  background: #fff;
  padding: 12px 16px; /* 手机端紧凑间距 */
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-title {
  margin: 0;
  font-size: 16px; /* 手机端适中字体 */
  font-weight: 600;
  color: var(--theme-text, #333);
  text-align: center;
}

/* 卡券列表 - 手机端优化 */
.coupon-list {
  padding: 12px;
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 手机端固定2列 */
  gap: 8px;
  max-width: 100%;
}

/* 卡券项目 */
.coupon-item {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.coupon-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.coupon-item:active {
  transform: translateY(0);
}

/* 卡券图片 - 手机端优化 */
.coupon-image {
  width: 100%;
  height: 120px; /* 手机端适中高度 */
  overflow: hidden;
  position: relative;
}

.coupon-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.coupon-item:hover .coupon-image img {
  transform: scale(1.05);
}

/* 卡券信息 - 手机端优化 */
.coupon-info {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-name {
  font-size: 14px; /* 手机端适中字体 */
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coupon-price {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.current-price {
  color: var(--theme-primary, #ff4d4f);
  font-size: 14px; /* 手机端适中字体 */
  font-weight: 600;
}

.price-number {
  font-size: 18px; /* 手机端适中字体 */
  font-weight: 700;
  font-style: normal;
}

.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 12px; /* 手机端适中字体 */
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-content i {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #666;
}

.empty-content p {
  margin: 0;
  font-size: 14px;
  color: #999;
}

/* 移除响应式设计 - 专注手机端 */

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-container {
  animation: fadeIn 0.3s ease-out;
}

.coupon-item {
  animation: fadeIn 0.3s ease-out;
}

/* 主题适配 - 使用CSS变量，由theme.js动态设置 */
.coupon-item {
  border: 1px solid transparent;
  transition: all 0.3s ease, border-color 0.3s ease;
}

/* 主题色适配 - 使用正确的CSS变量名 */
.coupon-item:hover {
  border-color: var(--theme-primary, #1890ff);
  box-shadow: 0 4px 16px var(--theme-shadow, rgba(24, 144, 255, 0.15));
}

/* 空状态图标主题适配 */
.empty-content i {
  color: var(--theme-primary, #d9d9d9);
  opacity: 0.3;
}

/* 响应主题变化的动画 */
.coupon-item,
.current-price,
.loading-content i,
.page-title,
.coupon-name {
  transition: color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
}
