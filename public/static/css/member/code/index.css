/* 礼品兑换页面样式 */
img {
  max-width: 100%;
}

.swiper {
  width: 100%;
}

.scan_qrcode {
  width: 30px;
  float: right;
  margin-top: -34px;
  margin-right: 10px;
}

.has_notice {
  margin-bottom: 60px;
}

.empty_notice {
  position: absolute;
  text-align: center;
  width: 100%;
  bottom: 80px;
}

/* 提取的内联样式 - CSS类 */
.slide-image {
  width: 100%;
}

.main-container {
  padding: 10px 10px 10px 10px;
}

.scan-image {
  display: block;
}

.code-input-container {
  margin-left: 10px;
}

/* 弹窗样式 */
.notice-popup-content {
  padding: 20px;
}

.notice-popup-title {
  text-align: center;
  padding-bottom: 10px;
}

.notice-popup-footer {
  text-align: center;
  padding-top: 20px;
}

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 40px 20px;
}

.loading-content {
  display: inline-block;
}

.loading-content i {
  font-size: 24px;
  color: var(--theme-primary);
  margin-bottom: 10px;
}

.loading-content h4 {
  margin: 0;
  color: var(--theme-text);
  font-weight: normal;
}

/* 主题适配 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 输入框聚焦动画 */
.layui-input {
  transition: all 0.2s ease;
}

.layui-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px var(--theme-shadow);
  outline: none;
}

/* 输入框悬停效果 */
.layui-input:hover:not(:focus) {
  border-color: #c0c4cc;
}

/* Tab页样式优化 */
.layui-tab-brief {
  margin-top: 0;
}

.layui-tab-brief > .layui-tab-title {
  text-align: center;
}

.layui-tab-brief > .layui-tab-title .layui-this {
  color: var(--theme-primary);
}

.layui-tab-content {
  padding: 20px 0;
}

/* 发送验证码按钮样式 */
.code-input-container .layui-btn-primary {
  color: white !important;
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
}

.code-input-container .layui-btn-primary:hover:not(.layui-btn-disabled) {
  color: white !important;
  background-color: var(--theme-hover) !important;
  border-color: var(--theme-hover) !important;
}

.code-input-container .layui-btn-primary:disabled,
.code-input-container .layui-btn-primary.layui-btn-disabled {
  color: white !important;
  background-color: #d9d9d9 !important;
  border-color: #d9d9d9 !important;
  cursor: not-allowed;
}

/* 按钮loading效果 */
.layui-btn-loading {
  pointer-events: none;
  opacity: 0.8;
}

.layui-btn-loading .loading-icon {
  margin-right: 5px;
}

/* 提交按钮loading状态 */
.layui-btn.layui-btn-loading {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  color: white !important;
}

/* 发送按钮loading状态 */
.code-input-container .layui-btn-primary.layui-btn-loading {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
  color: white !important;
}
