/* 卡券分类页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 隐藏未编译的Vue模板 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: rgba(255, 255, 255, 0.9);
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #ff7830;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
}

/* 页面容器 */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 640px;
  margin: 0 auto;
  background: #fff;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  width: 8.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-right: 1px solid #eee;
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff7830;
  margin: 0;
  text-align: center;
}

.header-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.page-subtitle {
  font-size: 1rem;
  color: #ff7830;
  margin: 0;
  text-align: center;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* 左侧分类菜单 */
.category-sidebar {
  width: 8.6rem;
  background: #f8f9fa;
  border-right: 1px solid #eee;
  overflow-y: auto;
}

.menu-left {
  height: 100%;
}

.category-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.category-item {
  padding: 1rem 0.5rem;
  text-align: center;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  font-size: 0.9rem;
  color: #666;
  transition: all 0.3s ease;
  word-break: break-all;
  line-height: 1.4;
}

.category-item:hover {
  background: #e9ecef;
  color: #ff7830;
}

.category-item.active {
  background: #ff7830;
  color: #fff;
  font-weight: bold;
}

/* 右侧卡券内容 */
.coupon-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.content-section {
  height: 100%;
}

.section-title {
  font-size: 1.2rem;
  color: #333;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ff7830;
}

/* 卡券列表 */
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.coupon-item {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #eee;
}

.coupon-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.coupon-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.coupon-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.coupon-item:hover .coupon-image img {
  transform: scale(1.05);
}

.coupon-info {
  padding: 0.75rem;
}

.coupon-name {
  font-size: 1rem;
  color: #ff8527;
  margin: 0;
  font-weight: 500;
  line-height: 1.4;
}

.coupon-action {
  padding: 0 0.75rem 0.75rem;
}

.experience-btn {
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, #ff7830, #ff8527);
  color: #fff;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.experience-btn:hover {
  background: linear-gradient(135deg, #e66a2a, #e67621);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 120, 48, 0.3);
}

.experience-btn:active {
  transform: translateY(0);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #999;
  min-height: 200px;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #ddd;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #666;
}

.empty-state p {
  margin: 0;
  font-size: 0.9rem;
  color: #999;
}

/* 成功提示 */
.success-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 1rem 2rem;
  border-radius: 8px;
  z-index: 9999;
  animation: fadeInOut 2s ease-in-out;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toast-content i {
  font-size: 1.2rem;
  color: #52c41a;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  20%,
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 响应式设计 */

/* 滚动条样式 */
.category-sidebar::-webkit-scrollbar,
.coupon-content::-webkit-scrollbar {
  width: 4px;
}

.category-sidebar::-webkit-scrollbar-track,
.coupon-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.category-sidebar::-webkit-scrollbar-thumb,
.coupon-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.category-sidebar::-webkit-scrollbar-thumb:hover,
.coupon-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 主题适配 */
.theme-transition {
  transition: all 0.3s ease;
}

/* 深色模式支持 */
