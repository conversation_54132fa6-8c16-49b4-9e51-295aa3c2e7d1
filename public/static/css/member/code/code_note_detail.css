/* 券卡详情页面专用样式文件 */

/* Vue.js v-cloak 指令样式 - 防止模板闪烁 */
[v-cloak] {
  display: none !important;
}

/* 页面容器样式 */
.page-container {
  padding: 15px;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 80px;
}

/* 卡片容器 */
.card-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 15px;
}

/* 轮播图样式 */
.swiper {
  width: 100%;
  height: 250px;
  border-radius: 12px 12px 0 0;
}

.swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 券卡信息区域 */
.coupon-info {
  padding: 20px;
}

.coupon-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 15px;
  line-height: 1.4;
}

.coupon-code {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.coupon-code .label {
  color: #666;
  margin-right: 8px;
}

.coupon-code .code {
  color: var(--theme-primary);
  font-weight: 600;
  font-size: 16px;
}

/* 信息卡片网格 */
.info-grid {
  display: grid;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--theme-primary);
}

.info-left {
  display: flex;
  align-items: center;
}

.info-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-light));
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.info-icon i {
  color: #fff;
  font-size: 16px;
}

.info-label {
  color: #666;
  font-size: 14px;
}

.info-value {
  color: var(--theme-text);
  font-weight: 600;
  font-size: 15px;
}

/* 描述区域 */
.description-card {
  padding: 20px;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.description-title i {
  margin-right: 8px;
  color: var(--theme-primary);
}

.description-content {
  color: #666;
  line-height: 1.6;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 15px;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 10px;
  z-index: 999;
}

.action-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  color: #fff;
}

.btn-primary {
  background: var(--theme-primary);
}

.btn-primary:hover {
  background: var(--theme-hover);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--theme-primary, #01ba79);
}

.btn-success:hover {
  background: var(--theme-hover, #019963);
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--theme-primary, #ff7830);
}

.btn-warning:hover {
  background: var(--theme-hover, #e6692a);
  transform: translateY(-1px);
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 24px;
  margin-bottom: 10px;
}

/* 移除响应式设计 - 专注手机端，直接应用手机端样式 */
.page-container {
  padding: 10px;
}

.coupon-info {
  padding: 15px;
}

.bottom-actions {
  padding: 10px;
}

/* 响应主题变化的动画 */
.coupon-title,
.description-title,
.coupon-code .code,
.info-item,
.info-icon,
.btn-primary,
.btn-success,
.btn-warning,
.loading-content i {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
