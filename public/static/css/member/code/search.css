/*过渡时间*/
.ts-1{-webkit-transition: all .1s; -moz-transition: all .1s; -o-transition: all .1s; transition: all .1s;}
.ts-2{-webkit-transition: all .2s; -moz-transition: all .2s; -o-transition: all .2s; transition: all .2s;}
.ts-3{-webkit-transition: all .3s; -moz-transition: all .3s; -o-transition: all .3s; transition: all .3s;}
.ts-5{-webkit-transition: all .5s; -moz-transition: all .5s; -o-transition: all .5s; transition: all .5s;}

/*旋转*/
.tf-180{ -moz-transform: rotate(-180deg);-webkit-transform: rotate(-180deg);-ms-transform: rotate(-180deg);-o-transform: rotate(-180deg);transform: rotate(-180deg);}

/*促销图标*/
.em-promotion{font-size:1.2rem; vertical-align: middle;  padding:.1rem .4rem;  background:#1CBB7F; border-radius: 1.2rem;color:#fff;}
.em-p-center{background:#f447c9;}
.em-p-low{background:#13ab53;}
/*display盒子*/
.dis-box {display: -webkit-box;display: -moz-box;display: -ms-box;display: box;}
.box-flex {-webkit-box-flex: 1;-moz-box-flex: 1;-ms-box-flex: 1;box-flex: 1; display: block; width:100%;}

/*字体位置*/
.text-left{text-align: left;}
.text-center{text-align: center;}
.text-right{text-align: right;}

/*多个一行三个*/
.w-3{width:33.33%;  float:left; padding:.6rem .4rem; box-sizing: border-box; position: relative;}
.w-3:nth-child(3n+1){padding-left:0; padding-right:.8rem}
.w-3:nth-child(3n){ padding-left: .8rem;padding-right:0;}
.xiangqing{
    padding-bottom: 1rem;
    line-height: 2rem;
    background-color: #FFFFFF;
    margin-top: 0.5rem;
    margin-bottom: 5.5rem;
}
/*按钮色调*/
.btn-submit{font-size: 1.6rem;color: #fff;border: 0;text-align: center;padding: .84rem 0;border-radius: 4px;}
a.btn-submit,
a.btn-disab,
a.btn-cart{color: #fff;}
a.btn-reset,a.btn-default{color:#555;}
.btn-submit {background: #1CBB7F;border:1px solid #13AB53}/*主提交按钮*/
.btn-submit:active,
.btn-submit:hover {background: #1CBB7F;border:1px solid #13AB53}/*主提交按钮按下颜色13AB53*/
.btn-cart {background: #f4a213;border:1px solid #e19511}/*加入购物车*/
.btn-cart:active,
.btn-cart:hover {background: #e19511;border:1px solid #e19511}/*按下颜色*/
.btn-reset{background:#fff;color:#555;border:1px solid #efefef}/*清空按钮*/
.btn-reset:active,
.btn-reset:hover {background:#fff;border:1px solid #efefef}/*按下颜色*/
.btn-default{background:#fff;color:#555; border:1px solid #efefef}/*默认*/
.btn-default:active,
.btn-default:hover {color:#666}/*按下颜色*/
.btn-alipay {background: #43afea;border:1px solid #35a0db}/*支付宝按钮*/
.btn-alipay:active,
.btn-wechat:hover {background: #35a0db;border:1px solid #1dbc20}/*支付宝按钮按下颜色*/
.btn-wechat {background: #1dbc20;border:1px solid #1dbc20}/*微信按钮*/
.btn-wechat:active,
.btn-wechat:hover {background: #35a0db;border:1px solid #35a0db}/*微信按钮按下颜色*/
.btn-disab {background: #bbb;border:1px solid #1CBB7F}/*禁用按钮*/
.ect-button-more a,.ect-button-more button{margin:0 .65rem;}
.ect-button-more a:first-child,.ect-button-more button:first-child{margin-left:0;}
.ect-button-more a:last-child,.ect-button-more button:last-child{margin-right:0;}

/*文本框*/
.text-all {border-bottom: 1px solid #F6F6F9;padding: 1rem 0;width: 100%;overflow: hidden;}
.text-all.active{border-bottom:1px solid #1CBB7F;}
.text-all label {font-size: 1.65rem;display: block;height: 3rem;line-height: 3rem;margin-right: 0.8rem;vertical-align: middle;}
.text-all .text-all-span{height:3rem; line-height: 3rem; font-size:1.6rem; color:#666;}
.text-all span.t-jiantou{margin-top:.8rem;}
.input-texts {position: relative;}
.input-texts input {border: 0;height: 3.6rem;line-height: 3.6rem;padding: .5rem 0;box-sizing: border-box;width: 100%;color: #555;font-size: 1.57rem;padding-right: 3rem;}
.text-area1{width:100%; border:0; font-size:1.6rem; min-height: 3rem; line-height:2rem; padding: .5rem 0;  box-sizing:border-box; border-bottom:1px solid #F6F6F9; color:#555;}
/*文本下拉*/
.text-all-selec{position:relative;}
.text-all-select-div{background:#fafafa; position: absolute; left:0; right:0; display: none; font-size:1.5rem;}
.text-all-select-div ul li{border-bottom:1px solid #efefef; padding:1.3rem 0; color:#444;}
.text-all-select-div ul li:first-of-type{padding-top:0;}
.text-all-select-div ul li:last-of-type{border-bottom:none; padding-bottom:0;}
/*清空图标*/
.is-null {font-size: 2.1rem;color: #ddd;top: 50%;
	transition: all 0.2s; margin-top: -1.05rem;z-index: 10;position: absolute;right: 0.2rem; visibility: hidden; opacity: 0;-webkit-transition: all 0.1s;
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;}
.is-null.active {visibility:visible; opacity: 1;}
/*后面带按钮文本框*/
.ipt-check-btn:link{padding:0 1.4rem; height:2rem; line-height:2rem; margin: .5rem 0; text-align: center; color: #555; display: block; border-left:1px solid #F3F4F9; margin-left:1.2rem}
.ipt-check-btn:visited,.ip-check-btn:active,.ip-check-btn:hover{color:#555; border-left:1px solid #F3F4F9;}
.ipt-check-btn:link.disabled{color:#999}
.ipt-check-btn:visited.disabled,.ip-check-btn:active.disabled,.ip-check-btn:hover.disabled{color:#999}
/*后带显示隐藏密码按钮*/
.is-yanjing{font-size:2.4rem; padding:0 .2rem; color:#1CBB7F; height: 3rem; line-height: 3rem; margin-left:1rem;-webkit-transition: all 0.1s;
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;}
.is-yanjing.disabled{color:#ddd;}
/*搜索框*/
.search{padding:0 1rem; position:relative;}
.search a.a-search-input{display:block; position: absolute; left:0; bottom:0; right:0; top:0; z-index: 2;}
.search .text-all{border-bottom:0;}
.search .input-texts{padding:.3rem 0; border-bottom:0; background:#fff; background:#FFFFFF; border-radius: 4px; overflow: hidden;  position:relative;}
.search .input-texts input{padding-left:1rem;}
.search .is-null{right:.6rem;}
.search .search-check{ position: absolute; box-sizing: border-box; line-height: 2rem; padding:.5rem 0; padding-left:1rem;  color:#666;}
.search-check i.icon-xiajiantou{ position:absolute; font-size:1.2rem;}
a.s-filter{display:block; font-size:1.6rem; text-align:center; padding:.8rem 0; line-height:2rem; box-sizing: border-box; padding-left:1.2rem;}/*搜索筛选*/
.search .btn-submit{display: block; width:6rem; height:4rem;box-sizing: border-box; border-radius: 0px 4px 4px 0;    line-height: 1rem;}

/*span靠边对齐带箭头*/
span.t-jiantou{position: relative; font-size:1.4rem;margin-top: 0.1rem; color:#555;}
span.t-jiantou.active{color:#1CBB7F;}
span.t-jiantou i.icon-jiantou{position: absolute;}
span.t-jiantou i.icon-jiantou:before{font-size: 1.4rem; color: #888; }
span.t-jiantou em{width:10rem; box-sizing: border-box; text-align:right; overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}
/*title-hrbg*/
h4.title-hrbg {font-size: 1.3rem;position: relative; z-index:1; height: 4rem;line-height: 4rem;overflow: hidden;color: #888;text-align: center;margin-top: 4rem;}
h4.title-hrbg span {background: #fff;padding: 1rem .6rem;font-size: 1.4rem; z-index: 10;}
h4.title-hrbg hr {background: #f6f6f9;height: 1px;border: 0;position: absolute;left: 0;right: 0;top: 50%;margin-top: 1px;z-index: -1;}

/*
 * a链接样式1
 */
.a-text-more a{display:block; width:100%; text-align: center; box-sizing: border-box;}
.a-text-more span{height:2.6rem; line-height: 2.6rem; font-size:1.5rem; display: block; border:1px solid #efefef;  border-radius: 4px;}
/*a链接样式2-横向，一行一个*/
.a-text-one a{text-align: left;}
.a-text-one span{border:0; border-radius: 0; border-bottom:1px solid #efefef; padding:.8rem 0;}
.a-text-one li:last-child span{border-bottom:0;}
/*点击滑动进入筛选*/
.show-filter-div .mask-filter-div,.show-city-div .mask-filter-div{display: inherit;}
.show-filter-div .filter-div,.show-city-div .filter-city-div{left:0;}
.show-filter-div .close-filter-div,.show-city-div .close-filter-div{left: 0;-webkit-transition: all .2s;
	-moz-transition: all .2s;
	-o-transition: all .2s;
	transition: all .2s;transition-delay: .2s;
	-moz-transition-delay: .2s;
	-webkit-transition-delay: .2s;
	-o-transition-delay: .2s;}
.show-city-div .cate-filter-city{left:3.8rem; right:0; width:inherit;}
.show-city-div .cate-filter-city .con-filter-div{left:0;}
/*
 * 弹出搜索框
*/
.show-search-div .search-div{top:0;opacity:1;}
.show-search-div .mask-search-div{ display: inherit;}
.search-div{background:#fff; position:fixed; height:100%; width:100%; left: 0; top: 100%; right: 0; bottom:0; z-index: 112;opacity:0;}
.search-div .search{background:#F6F6F9;}
.search-con{padding:2rem 1rem; padding-bottom:0; background:#fff;}
.search-con ul{overflow: hidden; color:#555}
.search-con ul a{color:#666;}
.search-con p{overflow:hidden; font-size:1.6rem; padding:.8rem 0; padding-top:0; clear: both; color:#999;}
.search-con p.hos-search{margin-top:2rem; }
.search-con p.hos-search i.icon-xiao10{font-size:1.8rem;}
.close-search{height:4.6rem; line-height: 4.6rem; color:#999; position: absolute; bottom:0; font-size:1.6rem; text-align: center; width:100%;}
.history-search{position: absolute !important; bottom: 5rem;overflow: hidden;left:0;top: 6.8rem; right: 0; padding:0 1.3rem;}

/*弹出层中关闭*/
.show-div-guanbi{font-size:2.1rem; color:#999; margin-left:1rem;}

.mask-filter-div.show {
    display: inherit;
}
.icon-write .ect-icon-history {
    background-position: -5.4em -2.7em;
}
/*积分商城样式添加  robot*/
.heart{padding-left:0.6em;padding-right:0.6em;width:5rem;}
.heart i{display:block; width: 2.8rem; height:2.8rem; margin:0 auto; background:url(../img/heart_03.png) 0 100% no-repeat; background-size:2.7rem 5.4rem;}
.heart.active i{ background:url(../img/heart_03.png) 0 0 no-repeat; background-size:2.7rem 5.4rem;}

.heart em{font-size:1.3rem; display:block; text-align: center; margin-top:.2rem; color:#555;}
.heart.active em{color:#1CBB7F}

address, cite, dfn, em, var {
	font-style: normal;
}
.swiper-slide-active p label{font-weight:normal;    margin-bottom: 5px;}
/*new hooter*/
.new-footer-box{padding:0.2rem 0;}
.new-footer-box i{font-size:1.7rem;color:#D7D7D7;display:block;text-align:center}
.new-footer-box span{color:#D7D7D7;font-size:1rem;display:block;text-align:center;padding-top:0.1rem;}
.new-index-footer{ background-color: rgba(50, 50, 50, 0.96);   left: 0;
    right: 0;
    position: fixed;
    z-index: 4;
    overflow: hidden;
    margin: 0 auto;
    bottom: 0;
    z-index: 4;}
