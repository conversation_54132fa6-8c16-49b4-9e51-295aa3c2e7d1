/* 券卡详情页面样式 */

/* v-cloak 防止模板闪烁 */
[v-cloak] {
  display: none !important;
}

/* 页面基础样式 */
body {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow-x: hidden; /* 防止水平滚动条 */
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: 100vw; /* 防止超出视口宽度 */
}

/* 防止所有元素超出容器宽度 */
* {
  box-sizing: border-box;
}

#app {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #999;
}

/* 页面容器 */
.page-container {
  min-height: auto; /* 改为自动高度 */
  padding: 0;
  max-width: 100%;
  box-sizing: border-box;
}

/* 券卡信息卡片 */
.coupon-card {
  background: #fff;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  box-sizing: border-box;
}

/* 轮播图样式 */
.swiper-container {
  width: 100%;
  height: auto;
  min-height: 200px;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.swiper-slide {
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.swiper-slide img {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
  display: block;
}

.swiper-pagination {
  bottom: 10px !important;
}

.swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
}

.swiper-pagination-bullet-active {
  background: var(--theme-primary, #fff);
}

/* 券卡信息区域 */
.coupon-info {
  padding: 20px;
}

.coupon-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.coupon-price {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 16px;
}

.current-price {
  color: var(--theme-primary, #ff4d4f);
  font-size: 16px;
  font-weight: 600;
}

.price-number {
  font-size: 24px;
  font-weight: 700;
  font-style: normal;
}

.price-number-small {
  font-size: 16px;
  font-weight: 400;
  font-style: normal;
}

.original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
}

/* 券卡描述 - 独立卡片 */
.coupon-description {
  background: #fff;
  margin: 16px 0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  box-sizing: border-box;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0 0 16px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.description-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.description-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 8px 0;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  z-index: 1000;
}

.action-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: var(--theme-primary, #1890ff);
  border: none;
  transition: all 0.3s ease;
  color: #fff;
}

.action-btn:hover:not(:disabled) {
  background: var(--theme-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--theme-shadow, rgba(24, 144, 255, 0.3));
}

.action-btn:disabled {
  background: #d9d9d9;
  color: #fff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.action-btn i {
  margin-right: 8px;
  font-size: 18px;
}

/* 移除响应式设计 - 专注手机端，直接应用手机端样式 */
.coupon-info {
  padding: 16px;
}

.coupon-title {
  font-size: 16px;
}

.price-number {
  font-size: 20px;
}

.bottom-actions {
  padding: 8px 12px;
}

.action-btn {
  height: 44px;
  font-size: 15px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-container {
  animation: fadeIn 0.3s ease-out;
}

/* LayUI 样式覆盖 */
.layui-btn-fluid {
  width: 100%;
}

.layui-btn-normal {
  background-color: var(--theme-primary, #1890ff);
  border-color: var(--theme-primary, #1890ff);
}

.layui-btn-normal:hover {
  background-color: var(--theme-hover, #40a9ff);
  border-color: var(--theme-hover, #40a9ff);
}

/* 响应主题变化的动画 */
.action-btn,
.layui-btn-normal,
.current-price,
.coupon-title,
.description-title,
.loading-content i,
.swiper-pagination-bullet-active {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
