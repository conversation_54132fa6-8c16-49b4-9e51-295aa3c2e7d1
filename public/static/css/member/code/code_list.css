* {
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none;
  text-decoration: none;
  color: inherit;
  font-weight: normal;
  font-family: "微软雅黑";
  box-sizing: border-box;
  font-style: normal;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  width: 100%;
  overflow-x: hidden;
  background: #eaeaea;
}

img {
  vertical-align: middle;
  max-width: 100%;
}

.container {
  width: 100%;
  padding: 0 0.4rem;
}

/*顶部标题返回*/
.top-bar {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 0.9rem;
  padding: 0 0.2rem;
  background: #fff;
  z-index: 99;
}

.top-bar i {
  display: inline-block;
  width: 0.5rem;
  height: 0.9rem;
  background: url(/static/img/member/code/images/arrow-left.png) left center no-repeat;
  background-size: 0.18rem 0.31rem;
  position: relative;
  z-index: 3;
}

.top-bar .title {
  position: absolute;
  height: 0.4rem;
  width: 100%;
  text-align: center;
  left: 0;
  font-size: 0.34rem;
  z-index: 2;
}

.category {
  font-size: 0.3rem;
  color: #666666;
  position: fixed;
  top: 0;
  width: 100%;
  padding: 0 0.4rem;
  background: #fff;
  z-index: 99;
}

.category ul {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

.category ul li {
  padding: 0.1rem 0 0.3rem 0;
  position: relative;
}

.category ul li.active {
  color: var(--theme-primary);
}

.category ul li.active::after {
  position: absolute;
  content: "";
  display: inline-block;
  bottom: 0.16rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.5rem;
  height: 0.06rem;
  border-radius: 0.06rem;
  background: var(--theme-primary);
}

.container {
  width: 100%;
  padding: 0.35rem 0.3rem;
  position: relative;
  padding-top: 1.15rem;
  padding-bottom: 2.5rem;
}

.container ul {
  display: flex;
  flex-direction: column;
  display: none;
}

.container ul.active {
  display: block;
}

.container ul li {
  width: 100%;
  height: 2.18rem;
  background: url(/static/img/member/code/images/item-bg.png) center center no-repeat;
  background-size: 100%;
  margin-bottom: 0.15rem;
  display: flex;
  position: relative;
}

.container ul li .l {
  flex-shrink: 0;
  width: 2.2rem;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container ul li .l p {
  font-size: 0.51rem;
  display: flex;
  align-items: baseline;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  font-weight: bold;
}

.container ul li .l p i {
  font-size: 0.24rem;
}

.container ul li .l span {
  font-size: 0.3rem;
  color: var(--theme-text);
}

.container ul li .r {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 0.45rem;
  padding-right: 0.15rem;
}

.container ul li .r h3 {
  font-size: 0.32rem;
  color: var(--theme-primary);
}

.container ul li .r .use {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: #666666;
  margin-top: 0.03rem;
}

.container ul li .r .use button:not(.share-button) {
  flex-shrink: 0;
  width: 1.2rem;
  height: 0.52rem;
  line-height: 0.52rem;
  font-size: 0.24rem;
  color: #fff;
  background: var(--theme-primary);
  border-radius: 0.05rem;
}

.container ul li .r .use button[disabled] {
  background-color: #bbb;
  pointer-events: none;
  cursor: not-allowed;
}

.container ul li .r .status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: #666666;
  margin-top: 0.15rem;
}

.container ul li .r .status span {
  flex-shrink: 0;
  padding: 0.05rem 0.1rem;
  background: #f6e5dc;
  font-size: 0.16rem;
  color: var(--theme-text);
  border-radius: 0.05rem;
}

/* 底部按钮 - 参考recommend页面样式 */
.bottom-btn {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 750px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  background: transparent;
  box-shadow: none;
  border: none;
}

.bottom-btn .layui-btn {
  width: 100%;
  height: 45px;
  box-shadow: none !important;
  border: none !important;
  background: var(--theme-primary) !important;
  font-size: 16px;
}

/* 兑换优惠卷 - 保留兼容性 */
.exchange {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: auto;
  padding: 0.2rem 0.35rem 0.3rem 0.35rem;
  background: #fff;
  z-index: 99;
}

.exchange button {
  width: 100%;
  height: 0.96rem;
  line-height: 0.96rem;
  background: var(--theme-primary);
  font-size: 0.36rem;
  color: #fff;
  border-radius: 0.15rem;
}

/* 特定按钮样式 */
.use-button {
  height: 0.42rem;
  line-height: 0.42rem;
  width: auto;
  padding: 0 4px 0 4px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 0.05rem;
  font-size: 0.24rem;
}

/* 转赠按钮特殊样式 - 红色 */
.container ul li .r .use button.share-button,
button.share-button {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  margin-right: 0.1rem;
  height: 0.42rem;
  line-height: 0.42rem;
  width: auto;
  padding: 0 4px 0 4px;
  color: #fff !important;
  border: none !important;
  border-radius: 0.05rem;
  font-size: 0.24rem;
}

/* Vue相关样式 */
.main-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 0.1rem;
  align-items: center;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-content {
  text-align: center;
}

.loading-content i {
  font-size: 40px;
  color: #999;
  margin-bottom: 10px;
}

.loading-content h4 {
  color: #999;
  margin: 0;
  font-size: 16px;
}

/* 列表样式调整 */
.coupon-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 确保列表项正确显示 */
.coupon-list li {
  width: 100%;
  height: 2.18rem;
  background: url(/static/img/member/code/images/item-bg.png) center center no-repeat;
  background-size: 100%;
  margin-bottom: 0.15rem;
  display: flex;
  position: relative;
}

/* 复制原有的li样式到coupon-list */
.coupon-list li .l {
  flex-shrink: 0;
  width: 2.2rem;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.coupon-list li .l p {
  font-size: 0.51rem;
  display: flex;
  align-items: baseline;
  padding-top: 0.5rem;
  padding-bottom: 0.3rem;
  font-weight: bold;
}

.coupon-list li .l p i {
  font-size: 0.24rem;
}

.coupon-list li .l span {
  font-size: 0.3rem;
  color: var(--theme-text);
}

.coupon-list li .r {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 0.45rem;
  padding-right: 0.15rem;
}

.coupon-list li .r h3 {
  font-size: 0.32rem;
  color: #000;
}

.coupon-list li .r .use {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: #666666;
  margin-top: 0.03rem;
}

.coupon-list li .r .use button:not(.share-button) {
  flex-shrink: 0;
  width: 1.2rem;
  height: 0.52rem;
  line-height: 0.52rem;
  font-size: 0.24rem;
  color: #fff;
  background: var(--theme-primary);
  border-radius: 0.05rem;
}

.coupon-list li .r .use button[disabled] {
  background-color: #bbb;
  pointer-events: none;
  cursor: not-allowed;
}

.coupon-list li .r .status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: #666666;
  margin-top: 0.15rem;
}

.coupon-list li .r .status span {
  flex-shrink: 0;
  padding: 0.05rem 0.1rem;
  background: #f6e5dc;
  font-size: 0.16rem;
  color: var(--theme-text);
  border-radius: 0.05rem;
}

.coupon-list li .r .use button.share-button {
  background: #ff4757 !important;
  background-color: #ff4757 !important;
  margin-right: 0.1rem;
  height: 0.42rem;
}

/* 空状态样式优化 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
}

.empty-state-content {
  text-align: center;
  padding: 40px 20px;
}

.empty-state-icon {
  font-size: 80px;
  color: #999999;
  display: block;
  margin-bottom: 15px;
}

.empty-state-text {
  font-size: 16px;
  color: #999999;
  margin: 0;
  font-weight: normal;
}

.layui-layer-btn0 {
  font-size: 14px !important;
}

/* 显示块样式 */
.display-block {
  display: block;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
}

.empty-state-content {
  padding-top: 80px;
}

.empty-state-icon {
  font-size: 80px;
  color: #999999;
}

.empty-state-text {
  font-size: 16px;
  color: #999999;
}

/* 卡券标题样式 */
.coupon-title {
  font-size: 16px;
}

/* 二维码内容样式 */
.qrcode-content {
  text-align: center;
}

/* 绑定表单样式 */
.bind-form {
  font-size: 18px;
  padding: 10px;
}

.layui-layer-btn0 {
  font-size: 14px !important;
}
