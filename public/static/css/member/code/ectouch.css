/*初始样式*/

html {font-size: 18px; font-family: Helvetica, Tahoma, Arial, "Hiragino Sans GB", "Hiragino Sans GB W3", STXihei, STHeiti, "Microsoft YaHei", <PERSON><PERSON>, <PERSON>m<PERSON><PERSON>, sans-serif;
-webkit-font-smoothing: antialiased;}
body,
ul,
ol,
li,
p,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
table,
td,
img,
div,
dl,
dt,
dd {margin: 0;padding: 0;border: 0;list-style: none;font-weight: normal;}
body {background: #F6F6F9;color: #333;margin: 0 auto;font-size: 18px;font-weight: normal;}
a,
a:link {color: #333;text-decoration: none;}
a:active,
a:hover{text-decoration: underline;}

* {-webkit-appearance: none;-webkit-tap-highlight-color: rgba(255, 255, 255, 0);}
input:required,
input:valid,
input:invalid {border: 0 none;outline: 0 none;-webkit-box-shadow: none;-moz-box-shadow: none;-ms-box-shadow: none;-o-box-shadow: none;box-shadow: none;}/* 重置文本格式元素 */

input[type="number" ]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button{
    -webkit-appearance: none !important;
    margin: 0; 
}

input[type="number"]{-moz-appearance:textfield;}

address,
cite,
dfn,
em,
var {font-style: normal;}/* 将斜体扶正 */

code,
kbd,
pre,
samp {}/* 统一等宽字体 */
del {color: #aaa; font-size:1.2rem;}

/*动画*/

/*=======================================
   		共用样式
  =======================================*/
 #loading{ position: fixed; left:0; top: 0; right: 0; bottom: 0; background:rgba(255,255,255,1) ; margin-left:-2.5rem;  margin-top:-1rem;z-index: 111;}
 #loading img{ position:absolute; width:5rem; height: auto; left:50%; top: 50%; margin-left:-2rem; margin-top:-3rem;}

/*通用*/
a:hover{
	text-decoration: none;
}
/*垂直水平居中*/
.tb-lr-center{  display: -webkit-box; display: -ms-flexbox;  display: -webkit-flex; display: flex !important;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;}
 /*文字超出一行隐藏*/
.onelist-hidden{white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}
.clear{clear:both}
.of-hidden{overflow: hidden;}
.div-messages{background:rgba(0,0,0,0.6); position: fixed; -webkit-transition: all .5s; -moz-transition: all .5s; -o-transition: all .5s; transition: all .5s; visibility: hidden; opacity: 0; padding:0 1.8rem; box-sizing:content-box; left:50%; bottom:1.6rem; height:3.6rem; line-height: 3.6rem; font-size:1.6rem; color:#fff; border-radius:1.8rem; z-index: 100000;}/*此为弹出层消息并且层级应最高，内容js动态替换，默认无内容，左右居中js*/
.div-messages.active{visibility: visible; opacity: 1; -webkit-transition: opacity .5s; -moz-transition: opacity .5s; -o-transition: opacity .5s; transition: opacity .5s;}
.fl {float: left;}
.fr {float: right;}
.m-top04{margin-top:.4rem;}
.m-top06{margin-top:.6rem;}
.m-top08{margin-top:.8rem;}
.m-top10{margin-top:1rem;}
.m-top12{margin-top:1.2rem;}
.m-top1px{margin-top:1px;}
.margin-lr {margin: 0 1.3rem;}
.padding-lr {padding: 0 1.3rem; box-sizing: content-box;}
.padding-all{padding:1.3rem}
.bodyfff {background: #fff;}
.ellipsis-one{white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
/*滚动条样式*/
.scrollbar-none::-webkit-scrollbar {/*滚动条宽度设置*/
    width: 0px;height: 0;
}
/*并排不换行*/
.w-space{white-space:nowrap}
.dis-i-block{display:inline-block}
/*dis-flex*/
.dis-flex{display:flex; display: -webkit-flex;}}
.dis-flex:before{display:flex; display: -webkit-flex;}
.dis-block{display: block;}
.dis-i-flex{display:inline-flex; display: -webkit-inline-flex;}
/*字体颜色*/
label.admin-text{color:#999;}
.t-first {color: #1CBB7F}/*主字体颜色*/
.t-two{color: #f4a213}/*黄色 － 与购物车按钮背景色一致*/
.t-remark,
.t-remark:link {color: #777;font-size: 1.5rem; }/*文本备注文字颜色*/
.t-remark2 {color: #444;font-size: 1.6rem;}/*主要文本连接*/
.a-first:link {color: #1CBB7F}/*主连接*/
.a-first:visited {color: #1CBB7F}
.t-center{color:#f447c9;}
.t-low{color:#13ab53;}
.t-remark3{font-size:1.3rem; color:#888;}
/*背景颜色*/
.b-color{background:#1CBB7F}/*主要背景颜色*/
.b-color-f{background:#fff;}
.b-color-1{background:#F6F6F9}

/*过渡时间*/
.ts-1{-webkit-transition: all .1s; -moz-transition: all .1s; -o-transition: all .1s; transition: all .1s;}
.ts-2{-webkit-transition: all .2s; -moz-transition: all .2s; -o-transition: all .2s; transition: all .2s;}
.ts-3{-webkit-transition: all .3s; -moz-transition: all .3s; -o-transition: all .3s; transition: all .3s;}
.ts-5{-webkit-transition: all .5s; -moz-transition: all .5s; -o-transition: all .5s; transition: all .5s;}

/*旋转*/
.tf-180{ -moz-transform: rotate(-180deg);-webkit-transform: rotate(-180deg);-ms-transform: rotate(-180deg);-o-transform: rotate(-180deg);transform: rotate(-180deg);}

/*促销图标*/
.em-promotion{font-size:1.2rem; vertical-align: middle;  padding:.1rem .4rem;  background:#1CBB7F; border-radius: 1.2rem;color:#fff;}
.em-p-center{background:#f447c9;}
.em-p-low{background:#13ab53;}

/*display盒子*/
.dis-box {display: -webkit-box;display: -moz-box;display: -ms-box;display: box;}
.box-flex {-webkit-box-flex: 1;-moz-box-flex: 1;-ms-box-flex: 1;box-flex: 1; display: block; width:100%;}

/*字体位置*/
.text-left{text-align: left;}
.text-center{text-align: center;}
.text-right{text-align: right;}

/*多个一行三个*/
.w-3{width:33.33%;  float:left; padding:.6rem .4rem; box-sizing: border-box; position: relative;}
.w-3:nth-child(3n+1){padding-left:0; padding-right:.8rem}
.w-3:nth-child(3n){ padding-left: .8rem;padding-right:0;}
.xiangqing{
    padding-bottom: 1rem;
    line-height: 2rem;
    background-color: #FFFFFF;
    margin-top: 0.5rem;
    margin-bottom: 5.5rem;
}
.xiangqing-1{
    padding-bottom: 1rem;
    line-height: 2rem;
    background-color: #FFFFFF;
    margin-top: 0.5rem;
    margin-bottom: 6.5rem;
}
/*按钮色调*/
.btn-submit,
.btn-disab,
.btn-cart,
.btn-reset,
.btn-default,
.btn-alipay,
.btn-wechat{font-size: 1.7rem;color: #fff;border: 0;text-align: center;padding: .84rem 0;border-radius: 4px;width: 100%;}
a.btn-submit,
a.btn-disab,
a.btn-cart{color: #fff;}
a.btn-reset,a.btn-default{color:#555;}

.btn-submit {background: #1CBB7F;border:1px solid #13AB53}/*主提交按钮*/
.btn-submit:active,
.btn-submit:hover {background: #1CBB7F;border:1px solid #13AB53}/*主提交按钮按下颜色13AB53*/
.btn-cart {background: #f4a213;border:1px solid #e19511}/*加入购物车*/
.btn-cart:active,
.btn-cart:hover {background: #e19511;border:1px solid #e19511}/*按下颜色*/
.btn-reset{background:#fff;color:#555;border:1px solid #efefef}/*清空按钮*/
.btn-reset:active,
.btn-reset:hover {background:#fff;border:1px solid #efefef}/*按下颜色*/
.btn-default{background:#fff;color:#555; border:1px solid #efefef}/*默认*/
.btn-default:active,
.btn-default:hover {color:#666}/*按下颜色*/
.btn-alipay {background: #43afea;border:1px solid #35a0db}/*支付宝按钮*/
.btn-alipay:active,
.btn-wechat:hover {background: #35a0db;border:1px solid #1dbc20}/*支付宝按钮按下颜色*/
.btn-wechat {background: #1dbc20;border:1px solid #1dbc20}/*微信按钮*/
.btn-wechat:active,
.btn-wechat:hover {background: #35a0db;border:1px solid #35a0db}/*微信按钮按下颜色*/

.btn-disab {background: #bbb;border:1px solid #1CBB7F}/*禁用按钮*/

.ect-button-more a,.ect-button-more button{margin:0 .65rem;}
.ect-button-more a:first-child,.ect-button-more button:first-child{margin-left:0;}
.ect-button-more a:last-child,.ect-button-more button:last-child{margin-right:0;}

/*文本框*/
.text-all {border-bottom: 1px solid #F6F6F9;padding: 1rem 0;width: 100%;overflow: hidden;}
.text-all.active{border-bottom:1px solid #1CBB7F;}
.text-all label {font-size: 1.65rem;display: block;height: 3rem;line-height: 3rem;margin-right: 0.8rem;vertical-align: middle;}
.text-all .text-all-span{height:3rem; line-height: 3rem; font-size:1.6rem; color:#666;}
.text-all span.t-jiantou{margin-top:.8rem;}
.input-text {position: relative;}
.input-text input {border: 0;height: 3rem;line-height: 2rem;padding: .5rem 0;box-sizing: border-box;width: 100%;color: #555;font-size: 1.6rem;padding-right: 3rem;}
.text-area1{width:100%; border:0; font-size:1.6rem; min-height: 3rem; line-height:2rem; padding: .5rem 0;  box-sizing:border-box; border-bottom:1px solid #F6F6F9; color:#555;}
/*文本下拉*/
.text-all-selec{position:relative;}
.text-all-select-div{background:#fafafa; position: absolute; left:0; right:0; display: none; font-size:1.5rem;}
.text-all-select-div ul li{border-bottom:1px solid #efefef; padding:1.3rem 0; color:#444;}
.text-all-select-div ul li:first-of-type{padding-top:0;}
.text-all-select-div ul li:last-of-type{border-bottom:none; padding-bottom:0;}
/*清空图标*/
.is-null {font-size: 2.1rem;color: #ddd;top: 50%;
	transition: all 0.2s; margin-top: -1.05rem;z-index: 10;position: absolute;right: 0.2rem; visibility: hidden; opacity: 0;-webkit-transition: all 0.1s;
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;}
.is-null.active {visibility:visible; opacity: 1;}
/*后面带按钮文本框*/
.ipt-check-btn:link{padding:0 1.4rem; height:2rem; line-height:2rem; margin: .5rem 0; text-align: center; color: #555; display: block; border-left:1px solid #F3F4F9; margin-left:1.2rem}
.ipt-check-btn:visited,.ip-check-btn:active,.ip-check-btn:hover{color:#555; border-left:1px solid #F3F4F9;}
.ipt-check-btn:link.disabled{color:#999}
.ipt-check-btn:visited.disabled,.ip-check-btn:active.disabled,.ip-check-btn:hover.disabled{color:#999}
/*后带显示隐藏密码按钮*/
.is-yanjing{font-size:2.4rem; padding:0 .2rem; color:#1CBB7F; height: 3rem; line-height: 3rem; margin-left:1rem;-webkit-transition: all 0.1s;
	-moz-transition: all 0.1s;
	-o-transition: all 0.1s;
	transition: all 0.1s;}
.is-yanjing.disabled{color:#ddd;}
/*搜索框*/
.search{padding:0 1rem; position:relative;}
.search a.a-search-input{display:block; position: absolute; left:0; bottom:0; right:0; top:0; z-index: 2;}
.search .text-all{border-bottom:0;}
.search .input-text{padding:.3rem 0; border-bottom:0; background:#fff; background:#FFFFFF; border-radius: 4px; overflow: hidden;  position:relative;}
.search .input-text input{padding-left:1rem;}
.search .is-null{right:.6rem;}
.search .search-check{ position: absolute; box-sizing: border-box; line-height: 2rem; padding:.5rem 0; padding-left:1rem;  color:#666;}
.search-check i.icon-xiajiantou{ position:absolute; font-size:1.2rem;}
a.s-filter{display:block; font-size:1.6rem; text-align:center; padding:.8rem 0; line-height:2rem; box-sizing: border-box; padding-left:1.2rem;}/*搜索筛选*/
.search .btn-submit{display: block; width:6rem; height:3.6rem;box-sizing: border-box; border-radius: 0px 4px 4px 0;    line-height: 1rem;}

/*span靠边对齐带箭头*/
span.t-jiantou{position: relative; font-size:1.4rem;margin-top: 0.1rem; color:#555;}
span.t-jiantou.active{color:#1CBB7F;}
span.t-jiantou i.icon-jiantou{position: absolute;}
span.t-jiantou i.icon-jiantou:before{font-size: 1.4rem; color: #888; }
span.t-jiantou em{width:10rem; box-sizing: border-box; text-align:right; overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}
/*title-hrbg*/
h4.title-hrbg {font-size: 1.3rem;position: relative; z-index:1; height: 4rem;line-height: 4rem;overflow: hidden;color: #888;text-align: center;margin-top: 4rem;}
h4.title-hrbg span {background: #fff;padding: 1rem .6rem;font-size: 1.4rem; z-index: 10;}
h4.title-hrbg hr {background: #f6f6f9;height: 1px;border: 0;position: absolute;left: 0;right: 0;top: 50%;margin-top: 1px;z-index: -1;}
/*加减文本*/
.div-num{border:1px solid #ededed; border-radius: 4px; width:12rem;}
.div-num input{border:0;border-left:1px solid #ededed; border-right: 1px solid #ededed; border-radius: 0; height:2rem; padding:.5rem; font-size:1.2rem; text-align: center;}
.div-num a{display:block; width:3rem; height:3rem; position: relative;}
.div-num a.num-less:before,.div-num a.num-plus:before,.div-num a.num-plus:after{content: " "; display: block; border-bottom:1px solid #888; width:1.6rem; position: absolute; left:50%; margin-left:-.8rem; top:50%; margin-top:-1px;}
.div-num a.num-plus:after{ -moz-transform: rotate(-90deg);-webkit-transform: rotate(-90deg);-ms-transform: rotate(-90deg);-o-transform: rotate(-90deg);transform: rotate(-90deg);}
.div-num.div-num-disabled{border:1px solid #f6f6f6;}
.div-num.div-num-disabled input{color:#999; border-color:#f6f6f6;}
.div-num.div-num-disabled a.num-less:before,.div-num.div-num-disabled a.num-plus:before,.div-num.div-num-disabled a.num-plus:after{border-color:#f1f1f1}
/*
 * 单选复选通用  
 *
 */

/*单选复选通用样式 － 1 方形显示*/
.select-one{background:#fff; overflow: hidden;}
.select-one .ect-select{font-size:1.5rem; margin:0 .6rem;}
.select-one .ect-select:first-child{margin-left:0;}
.select-one .ect-select:last-child{margin-right:0;}
.select-one .ect-select label{ padding:.6rem 1rem;  display: block; text-align: center;  border:1px solid #efefef; border-radius: 4px; color:#666;}
.select-one .ect-select label.active{border-color:#1CBB7F; color:#1CBB7F;}
.select-one .ect-select input[type="radio"]{display: none;}

/*单选复选通用样式 － 2 列表打勾显示*/
.select-two{background:#fff; overflow: hidden; padding-top:0; padding-bottom:0;}
.select-two .select-title{overflow:hidden;display: block; font-size:1.7rem;  border-top:1px solid #e7e8ef;}
.select-two .select-title:first-child{border-top:0;}
.select-two .select-title span{margin-top:.2rem;}

.select-two .select-title.active i{-webkit-transform: rotate(-90deg);
	-moz-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	-o-transform: rotate(-90deg);
	transform: rotate(-90deg);}

.select-two ul{padding-top:0; padding-bottom:0;border-top:1px solid #e7e8ef;}
.select-two .ect-select{font-size:1.5rem;  border-bottom:1px solid #F6F6F9;}
.select-two .ect-select:last-child{border-bottom:none;}
.select-two .ect-select label{ padding:1.6rem 0;  text-align: left; display: block; color:#666;}
.select-two .ect-select label i.icon-gou{visibility:hidden; color:#1CBB7F; opacity: 0; font-size:2.2rem}
.select-two .ect-select label.active{color:#1CBB7F;}
.select-two .ect-select label.active i.icon-gou{visibility: visible; opacity: 1;}

/*单选复选按钮 － 3 前置圆形按钮*/
.select-three .ect-select label{height:2.2rem; line-height:2.2rem; font-size:1.6rem;}
.select-three .ect-select label span{font-size:1.7rem; margin-left:.6rem;  margin-top:.1rem;}
.select-three .ect-select i{display:block; width:2.1rem; height:2.1rem; border:1px solid #ddd; border-radius: 100%;}
.select-three .ect-select label.active i{border:1px solid #1CBB7F; color:#fff; text-align: center; background:#1CBB7F url(../img/iconfont-gou.png) center center no-repeat; background-size:70%;}

/*
 * a链接样式1
 */
.a-text-more a{display:block; width:100%; text-align: center; box-sizing: border-box;}
.a-text-more span{height:2.6rem; line-height: 2.6rem; font-size:1.5rem; display: block; border:1px solid #efefef;  border-radius: 4px;}
/*a链接样式2-横向，一行一个*/
.a-text-one a{text-align: left;}
.a-text-one span{border:0; border-radius: 0; border-bottom:1px solid #efefef; padding:.8rem 0;}
.a-text-one li:last-child span{border-bottom:0;}

/*价格区间*/
.price-range{font-size:1.6rem; background:#fff;}
.price-slider{padding:1rem 1.3rem; margin-top:3.6rem; position: relative;}
.slider-info{position: absolute; z-index: 2; background:#fff; font-size:1.4rem; left:34%; top:-3.6rem; border:1px solid #efefef; color: #666; padding:.6rem 1.2rem; border-radius: 4px;}
.slider-info:before{content:" "; width:1rem; height:1rem; display: block; position: absolute; border-top:1px solid #efefef; border-right:1px solid #efefef;
-webkit-transform: rotate(135deg);
-moz-transform: rotate(135deg);
-ms-transform: rotate(135deg);
-o-transform: rotate(135deg);
transform: rotate(135deg);background:#fff; bottom:-.6rem; z-index: -1; left:50%; margin-left:-.5em;}

.ui-slider .ui-slider-handle{height:2.6rem; width: 2.6rem; top:-1rem; margin-left:-1.3rem; border-radius: 50%; border: 1px solid #efefef; background:#fff}

.ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus{border:1px solid #1CBB7F; background:#CFFFED}
.ui-slider-range{background:#1CBB7F}
.ui-widget-content{background:#efefef; border-radius: 0;  height:.8rem; border:0;}
/*
 * 侧滑弹出层 － 商品筛选
 * 
 */
.show-filter-div,.show-city-div
{overflow: hidden; position: fixed; top: 0; left: 0; right:0;bototm:0;}
.show-filter-div .blur-div,
.show-city-div .blur-div,
.show-search-div .blur-div,
.show-attr-div .blur-div,
.show-coupon-div .blur-div,
.show-service-div .blur-div,
.show-dist-div .blur-div,
.show-time-div .blur-div
{-webkit-filter: blur(30px); filter: blur(30px);}/*背景虚化*/

.filter-div,.filter-city-div,.mask-filter-div,.mask-search-div{position:fixed; width:100%; left:100%; right:0; top: 0; bottom: 0; z-index: 12; background:rgba(0,0,0,0.4) }

.mask-filter-div,.mask-search-div{z-index: 10; display: none; left: 0;}

.close-filter-div,.con-filter-div{position:absolute; top: 0; bottom: 0; z-index: 11;}
.close-filter-div{ width:3.8rem; left: 3.9rem; background:rgba(0,0,0,0.6);}
.con-filter-div{left:3.8rem; top:0; right:0; background:rgba(246,246,249,1); overflow-y: scroll;}
.con-filter-div .select-two .ect-select{font-size:1.4rem;}
.show-city-div .cate-filter-city{left:3.8rem;}

.close-filter-div{color:#fff; font-size:1.4rem; text-align: center;}
.close-filter-div i.icon-fanhui{font-size:2rem; display: block;
	-webkit-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg); margin-bottom:.4rem; }
.close-f-btn{position: absolute; top:50%; left:50%; margin-top:-2rem; margin-left:-1.4rem;}
/*点击滑动进入筛选*/
.show-filter-div .mask-filter-div,.show-city-div .mask-filter-div{display: inherit;}
.show-filter-div .filter-div,.show-city-div .filter-city-div{left:0;}
.show-filter-div .close-filter-div,.show-city-div .close-filter-div{left: 0;-webkit-transition: all .2s;
	-moz-transition: all .2s;
	-o-transition: all .2s;
	transition: all .2s;transition-delay: .2s;
	-moz-transition-delay: .2s; 
	-webkit-transition-delay: .2s;
	-o-transition-delay: .2s;}
.show-city-div .cate-filter-city{left:3.8rem; right:0; width:inherit;}
.show-city-div .cate-filter-city .con-filter-div{left:0;}
/*
 * 弹出搜索框
*/
.show-search-div .search-div{top:0; visibility:visible;}
.show-search-div .mask-search-div{ display: inherit;}
.search-div{background:#fff; position:fixed; height:100%; width:100%; left: 0; top: 100%; right: 0; visibility:hidden; bottom:0; z-index: 112;}
.search-div .search{background:#F6F6F9;}
.search-con{padding:2rem 1rem; padding-bottom:0; background:#fff;}
.search-con ul{overflow: hidden; color:#555}
.search-con ul a{color:#666;}
.search-con p{overflow:hidden; font-size:1.5rem; padding:.8rem 0; padding-top:0; clear: both; color:#999;}
.search-con p.hos-search{margin-top:1.3rem; }
.search-con p.hos-search i.icon-xiao10{font-size:1.8rem;}
.close-search{height:4.6rem; line-height: 4.6rem; color:#999; position: absolute; bottom:0; font-size:1.6rem; text-align: center; width:100%;}
.history-search{position: absolute !important; bottom: 5rem;overflow: hidden;left:0;top: 6.8rem; right: 0; padding:0 1.3rem;}

/*
 * 切换样式
 */
.radio-switching{ font-size:1.6rem; overflow: hidden; background:#fff;}
.radio-switching span{display:inline-block; width:5.4rem; position: relative; margin-top:.23rem;}
.radio-switching em{width:2.7rem; height:2.7rem; right:50%; border-radius: 50%; display: block; top:50%; margin-top:-1.3rem; background:#e7e8ef; position: absolute;}
.radio-switching hr{background:#e7e8ef;height: 1px;border: 0;}

.radio-switching.active em{background:#1CBB7F; right:0}
.radio-switching.active hr{background:#1CBB7F; height:1px; border:0;}

/*
 * 城市筛选链接样式
 */
.filter-city{background:#fff; overflow: hidden; font-size: 1.6rem;}
.filter-city span{}

/*
 * 三种模式商品列表
 * [备注：]库存销量,加入购物车按钮，市场价默认全部显示，如需隐藏/显示在对应图片列表代码中设置即可
 */
.product-list{}
.product-list ul{ overflow: hidden; width:100%;}
.product-list ul li{box-sizing: border-box;}

.product-div{background:#fff;  position: relative;  overflow: hidden;}
.product-div-link{ position: absolute;  left: 0; right: 0; top: 0; bottom: 0; z-index: 1;}
/*商品列表购物车按钮*/
.icon-flow-cart{ background:#1CBB7F; border-radius: 50%;  text-align: center; position: absolute; z-index: 3;}
.icon-flow-cart i.icon-gouwuche{color:#fff; }
.product-div img{width:100%; display: block;    max-width: 294px;
    max-height: 294px;}
.product-text h4{ display: block;overflow: hidden;}
.product-text p{padding-top:.6rem; padding-bottom:.4rem;}
.product-text .p-t-remark{color:#999;padding-bottom:0rem;}

/*
 * 商品列表1－最大化图片浏览
 * 在.product-list处加入.product-list-big即可实现
 */

.product-list-big{padding:0.5rem 1rem;}
.product-list-big ul li{width:100%; padding:.5rem 0;}

.product-list-big .product-text{padding:1.4rem;}
.product-list-big h4{font-size:1.8rem; height:4.4rem; line-height: 2.3rem;}

.product-list-big .p-t-remark{padding-top:.8rem;font-size:1.5rem; display: none;/*隐藏库存销量*/}

.product-list-big .p-price{font-size:2.3rem;}
.product-list-big .p-price del{font-size:1.6rem; margin-left:1rem}
.product-list-big .icon-flow-cart{ right:1.4rem; bottom:1.4rem; width:3.6rem; height:3.6rem; line-height:3.6rem;}
.product-list-big .icon-flow-cart i.icon-gouwuche{font-size:2.4rem;}
/*small与medium h4参数一致*/
.product-list-medium h4,.product-list-small h4{font-size:1.65rem; height:3.8rem; line-height: 1.9rem;}

/*商品列表2－中等图片浏览默认*/
.product-list-medium{padding:.2rem .4rem;}
.product-list-medium ul li{float:left; width:50%; padding:.2rem;}
.product-list-medium ul li:nth-child(2n-1){padding-right:0.2rem;}
.product-list-medium ul li:nth-child(2n){padding-left:0.2rem;}

.product-list-medium .product-text{padding:.8rem;}

.product-list-medium .p-t-remark{font-size:1.2rem; padding-top:.2rem; display: none;/*隐藏库存销量*/}

.product-list-medium .p-price{font-size:1.8rem;}
.product-list-medium .p-price small{margin-left:.6rem; display:none;/*隐藏市场价*/}
.product-list-medium .icon-flow-cart{ right:.6rem; bottom:.6rem;width:2.8rem; height:2.8rem; line-height:2.8rem; display: none;/*隐藏商品列表购物按钮*/}
.product-list-medium .icon-flow-cart i.icon-gouwuche{font-size:1.8rem;}

/*
 * 商品列表3－最小化图片浏览
 * 在.product-list处加入.product-list-small即可实现
 */
.product-list-small{padding:0 1px; margin-top:1px;}
.product-list-small ul li{width:100%; padding-bottom:1px;}

.product-list-small .product-div{padding:.8rem;}
.product-list-small .product-div img{ border:1px solid #F6F6F9;}
.product-list-small .product-list-img{width:9.6rem; height: auto; float:left;}
.product-list-small .product-text{margin-left:11rem; height:9.2rem;  padding:.2rem 0;}
.product-list-small .p-t-remark{padding-top:.6rem; font-size:1.3rem; height: 1.6rem;}
.product-list-small .p-price{font-size:2rem;}
.product-list-small .p-price small{margin-left:.6rem; font-size:1.4rem;}
.product-list-small .icon-flow-cart{ right:1.3rem; bottom:1.3rem;width:2.8rem; height:2.8rem; line-height:2.8rem; }
.product-list-small .icon-flow-cart i.icon-gouwuche{font-size:1.8rem;}

/* 
 * 商品列表一行多列显示
 */
.product-one-list{ overflow-x: scroll; width:100%; position: relative;}
.product-one-list li{width:36%; margin-right:.8rem; float:left;}
.product-one-list li:last-of-type{margin-right:0;}
.product-one-list li h4{font-size:1.4rem; height:2.9rem; line-height: 1.5rem; overflow: hidden;}
.product-one-list li .p-price{font-size:1.5rem;}

/*排序栏*/
.product-sequence{text-align: center; height:4.6rem; line-height:4.6rem; font-size:1.5rem; background:#fff; width: 100%;}
.product-sequence a:hover,.product-sequence a{text-decoration: none; color:#444}
.product-sequence a.active{color:#1CBB7F;}
.product-sequence .icon-xiajiantou{margin-left: .1rem;  position: relative; font-size:1.2rem;  transition: 0.2s ease;}
.product-sequence .icon-xiajiantou:before{position:absolute }
.product-sequence .a-change .icon-xiajiantou:before{-webkit-transform: rotate(-180deg);
	-moz-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	-o-transform: rotate(-180deg);
	transform: rotate(-180deg);}

a.a-sequence{width:6.6rem; height:4.6rem; line-height: 4.6rem; display: block;color:#666;position:relative;}
a.a-sequence .iconfont:before{font-size:2.8rem; color: #888;}
a.a-sequence .iconfont.icon-icon-square:before{font-size:3.2rem;}

/*
 * 商品详情页 － 共用样式
 * */
/*共用*/
.goods,.flow-have-cart,.flow-checkout,.flow-consignee-list,.flow-receipt,.flow-site{margin-bottom:5rem;}
.show-goods-img{position:fixed; left:0; top:0; right:0; bottom:0;}
.show-goods-img .j-show-goods-img{background:rgba(0,0,0,1); z-index: 111; left:0; right:0; top:0; bottom:0; position:fixed !important;}
.goods-min-icon{font-size:1.5rem; color:#bbb;   padding-left:.4rem; }
.g-t-temark{ margin:0; margin-right:1.2rem; display: inherit;}
.remark-all{font-size:1.4rem; padding:.6rem; text-align: center; color:#fff; display: block; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;
}
.big-remark-all{margin-bottom:1.3rem;}
.big-remark-all .remark-all{padding:1.3rem;box-sizing:border-box;}
.big-remark-all p{overflow: hidden; clear: both; position: relative;}
.big-remark-all .b-r-a-price{font-size:3.4rem;}
.big-remark-all .b-r-a-price sup{font-size:1.5rem;}
.big-remark-all .b-r-a-con{margin-left:1rem; margin-top:.4rem; font-size:1.1rem;}
.big-remark-all .b-r-a-con em{display:block;}
.big-remark-all .b-r-a-con em:first-of-type{font-size:1.2rem; margin-bottom:.3rem;}
.big-remark-all .b-r-a-time{font-size:1.1rem;}
.big-remark-all .b-r-a-btn{font-size:1.8rem; width:7rem; text-align: center; }
.temark-1{background:#f47575;}
.temark-2{background:#4fd2e5;}
.temark-3{background:#ffab3e;}
.temark-4{background:#ddd;}
.temark-1-text{color:#f47575;}
.temark-2-text{color:#4fd2e5;}
.temark-3-text{color:#ffab3e;}
.temark-4-text{color:#aaa;}
.t-goods1{font-size:1.5rem;}
.goods .j-goodsinfo-div{color:#999;}
/*弹出层中关闭*/
.show-div-guanbi{font-size:2.1rem; color:#999; margin-left:1rem;}

/*评价列表*/
.evaluation-list .t-remark{font-size:1.3rem;}
.evaluation-list em.t-remark{margin-left:.8rem; display:inline-block; width:7rem; overflow: hidden; white-space: nowrap;overflow: hidden; text-overflow: ellipsis;}
.evaluation-list .t-goods1{font-size:1.3rem;} 
.grade-star{display:inline-block; position: relative; width:9rem; height:1.3rem; background:url(../img/grade_star.png)  no-repeat; background-position-y: 100%; background-size:9rem auto;}
.grade-star:before{position:absolute; content: " "; display:block; left:0; top:0; right:0;bottom:0; background:url(../img/grade_star.png)  no-repeat;  background-size:9rem auto;}
.grade-star.g-star-1:before{left:-7.6rem;}
.grade-star.g-star-2:before{left:-5.7rem;}
.grade-star.g-star-3:before{left:-3.8rem;}
.grade-star.g-star-4:before{left:-1.9rem;}
.grade-star.g-star-5:before{left:0;}
/*店铺信息*/
.g-s-i-img {width:7rem; height:7rem; vertical-align: middle; padding:.2rem; border:1px solid #efefef;}
.g-s-i-img img{width:100%;}
.g-s-i-title{margin-left:1.3rem; margin-top:1.6rem;}
.g-s-i-title h3{font-size:1.7rem;}
.goods-shop-score{font-size:1.36rem; color:#666}
.goods-shop-score .margin-lr{margin:0 .2rem;}
.goods-shop-score .em-promotion{font-size:1rem; padding:.1rem .4rem }
.goods-shop-pic .title-hrbg{margin-top:.4rem;}

.goods-shop-btn i.iconfont{font-size:1.9rem; margin-right:.6rem;}

/*商品图片滑动*/
.goods-photo {position: relative;  width: 100%; height: 100%; overflow: hidden;}
.goods-photo img{width:100%; height:auto; display: block;}
.goods-photo .goods-num{position: absolute; font-size:1.5rem; color:#fff; height:2.6rem; line-height:2.6rem; padding:0 1rem; border-radius: 1.3rem; right:1.3rem; bottom:1.3rem; background:rgba(0,0,0,0.5); z-index: 5;}
.goods-photo li{width:3rem;}
/*标题*/
.goods-title{padding-bottom:.8rem;}
.goods-title h3{font-size:1.8rem; height:4.3rem; overflow: hidden; line-height: 1.3; padding-right:1.3rem; border-right:1px solid #efefef;}
.heart{display:block; width:4rem; height:4rem; padding-left:1.3rem;}
.heart i{display:block; width: 2.8rem; height:2.8rem; margin:0 auto; background:url(../img/heart_03.png) 0 100% no-repeat; background-size:2.7rem 5.4rem;}
.heart em{font-size:1.3rem; display:block; text-align: center; margin-top:.2rem; color:#555;}
.heart.active em{color:#1CBB7F}
.heart.active i{ background:url(../img/heart_03.png) 0 0 no-repeat; background-size:2.7rem 5.4rem;}
/*价格*/
.goods-price{padding-top:0; }
.g-p-tthree{font-size:1.4rem; color:#999;}
.p-price{clear: both; overflow: hidden;}
.p-price span{font-size:2.1rem;  vertical-align: middle;}
.p-price .em-promotion{ margin-left:.6rem; margin-top:.2rem}
.p-market{color:#888; font-size:1.6rem; clear: both; margin-top:.6rem;}
.p-market del{font-size:1.6rem; margin-left:.6rem;}
/*促销*/
.g-promotion-con p{margin-bottom:.6rem; clear: both; padding:.3rem 0; font-size:1.4rem; color:#777;}
.g-promotion-con p:first-of-type{padding-top:0;}
.g-promotion-con p:last-of-type{margin-bottom:0;}
.g-promotion-con p span{font-size:1.4rem; vertical-align: middle;}
.g-promotion-con .em-promotion{margin-right:.6rem}
/*优惠券*/
.g-coupon-con li{padding:0 .2rem;}
.goods-coupon .g-t-temark{padding-top:.6rem;}
/*服务信息*/
.goods-service .goods-min-icon{display:box; display:-webkit-box;  margin-top:.2rem;}
.g-r-rule .em-promotion{border-radius: 100%; vertical-align: middle; padding: 0; width:1.8rem; height:1.8rem; line-height: 1.8rem; text-align: center; margin-right:.4rem;}
.g-r-rule .em-promotion i{font-size:1.2rem;}
.g-r-rule span{display:inline-block; font-size:1.4rem; height:1.8rem; line-height: 2rem;}
/*评价*/
.g-evaluation-title .t-first{margin-left:.6rem; margin-right:.6rem;}
/*评价 － 店铺信息button*/
.goods-evaluation .btn-default,.goods-shop .btn-default{font-size:1.5rem;}
/*商品上滑弹出层*/
.show-goods-attr,
.show-goods-coupon,
.show-goods-service,
.show-goods-dist,
.show-time-con{position:fixed; left: 0; bottom:-100%; right:0; z-index: 13; visibility: hidden;}
.show-goods-attr .product-div{padding:1.3rem;}
.show-attr-div .mask-filter-div,
.show-coupon-div .mask-filter-div,
.show-service-div .mask-filter-div,
.show-dist-div .mask-filter-div,
.show-time-div .mask-filter-div{display: inherit;}
.show-attr-div .show-goods-attr,
.show-coupon-div .show-goods-coupon,
.show-service-div .show-goods-service,
.show-dist-div .show-goods-dist,
.show-time-div .show-time-con{bottom:0; visibility: visible;}
.s-g-attr-con{max-height: 14rem; overflow:hidden;}
.s-g-attr-con .ect-select{margin: 0; margin-right:1rem; margin-bottom:1rem;}

.goods-big-service em.em-promotion{width:4rem; height:4rem; display: block; padding:0; line-height: 4rem; text-align: center; border-radius: 2rem; margin-right:1rem;}
.goods-big-service em.em-promotion i{ font-size:2.2rem;}
.goods-big-service span{font-size:1.7rem; color:#444; line-height:4rem;}
.g-b-s-con{font-size:1.4rem; color:#777;}
/*优惠券上滑动*/
.goods-show-title{height:2.2rem; line-height: 2.2rem;}
.g-c-title-h3{font-size:1.6rem;}
.goods-show-con{max-height:26rem; min-height: 10rem; overflow-y: scroll;}

/*商品详情页 － tab*/
.goods-info .tab-title,.flow-coupon .tab-title{font-size:1.5rem; position: relative; text-align: center; border-bottom:1px solid #F3F4F9;}
.goods-info .tab-title,.flow-coupon .tab-title{padding:1.4rem 0;}
.goods-info .tab-title li,.flow-coupon .tab-title li{height:1.8rem; line-height:1.8rem; box-sizing: border-box;}
.goods-info .tab-title .active,.flow-coupon .tab-title .active{color:#1CBB7F;}
.goods-info .tab-title li:first-of-type,.flow-coupon .tab-title li:first-child{border-right:1px solid #F3F4F9;}
.goods-info img{max-width: 100%; height:auto}
.goods-info table{max-width:100%;}
.goods-info-attr li{margin:0 1.3rem; padding:1.5rem 0; border-bottom:1px solid #f3f4f9}
.goods-info-attr li:last-of-type{border-bottom:0;}
.ect-tab.active .tab-title{position: fixed; z-index: 11; border-bottom:1px solid #F6F6FF; top:0; left:0; right:0;}
/*评价页*/
.goods-evaluation-page .tab-title{border-bottom:1px solid #F3F4F9; position: fixed; left: 0; right: 0; top:0; z-index: 11;}
.goods-evaluation-page .tab-title ul li{padding:1.2rem 0; padding-bottom:.8rem;  font-size:1.5rem; text-align: center;}
.goods-evaluation-page .tab-title ul li em{font-size:1.2rem;}
.goods-evaluation-page .tab-title .active{color:#1CBB7F;}
.goods-evaluation-page .tab-con{margin-top:9.8rem;}
.goods-evaluation-page .evaluation-list{border-bottom:1px solid #F6F6F9; box-sizing: border-box; width:100%;}
.goods-evaluation-page .g-e-p-pic li{width:30%; margin-right:.4rem;}
.goods-evaluation-page .g-e-p-pic li:last-of-type{margin-right:0;}
.goods-evaluation-page .g-e-p-pic img{width:100%;}
/*悬浮btn*/
.filter-btn{position: fixed; left: 0; right:0; bottom:0; padding:.8rem; z-index: 6; background:#fafafc; border-top:1px solid #efefef;}
.filter-btn .filter-btn-a{padding:0 .5rem;  display: block; text-align: center; position: relative;}
.filter-btn .filter-btn-a i{font-size:2.2rem; color:#777; display:block; color:#636363}
.filter-btn .filter-btn-flow sup{position:absolute; top:-.3rem; right:.4rem; height:1.4rem; min-width:1.4rem; line-height: 1.4rem;  padding:0 .2rem; box-sizing:border-box; font-size:1.2rem; color:#fff; border-radius: .7rem;}
.filter-btn .filter-btn-a i.icon-gouwuche em{position:absolute; top: 0; right:0; display: block; min-width: 1.3rem; height:1.3rem; line-height:1.3rem; padding:.1rem .3rem; font-size:1.1rem; border-radius: .8rem; box-sizing: border-box; color:#fff;}
.filter-btn .filter-btn-a em{display:block; font-size:1.1rem; margin-top:.1rem; color:#666;}
.filter-btn a.box-flex{margin:0 .8rem; }
.filter-btn a.box-flex:last-of-type{margin-right:0; margin-left:0;}

/*============================================================================================================================================================*/

/*=======================================
		用户登录页
  =======================================*/
.user-center {margin-top: 2rem; font-size:1.6rem;}
.user-center .t-remark{margin-top:1.6rem;}
.user-center .btn-submit {margin-top: 2.2rem}
.u-l-register {font-size: 1.6rem;text-align: center;margin-top: 2.2rem;display: block;}
/*第三方登录*/
.other-login ul {margin: 1rem;margin-top: 2rem;}
.other-login ul li {text-align: center;}
.other-login ul li a {font-size: 1.4rem;color: #888}
.other-login ul li span {display: block;width: 5rem;height: 5rem;line-height: 5rem;border-radius: 50%;margin: 0 auto;margin-bottom: .4rem;}
.other-login ul li span.qq {background: #36aaf4}
.other-login ul li span.weixin {background: #2cbf19;}
.other-login ul li span.xinlang {background: #ec4353;}
.other-login ul li i {display: block;color: #fff;font-size: 3.4rem;}
.other-login ul li a:link {text-decoration: none !important;}

/*=======================================
		用户密码找回页
  =======================================*/
.user-forget-tel .t-remark2,.user-forget-email .t-remark2 {margin-bottom: .4rem}

/*=======================================
		用户注册页
  =======================================*/
.user-register .swiper-slide{padding:0 1.3rem; box-sizing: border-box;}
.user-register .hd{padding:0 3rem; margin-bottom:1rem;  font-size:1.7rem; text-align: center; border-bottom:1px solid #F3F4F9;}
.user-register .hd li{ padding:1rem 0; height:2.6rem;  line-height:2.6rem; display: block;  margin-bottom:-1px;  position: relative;}
.user-register .hd .active{color:#1CBB7F; border-bottom:1px solid #1CBB7F;}
.user-register .hd .active:after{content: " "; position: absolute; display: block; width:1rem; height:1rem; border:1px solid #1CBB7F; border-right: 0; border-bottom:0; -webkit-transform: rotate(45deg);
	-moz-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	-o-transform: rotate(45deg);
	transform: rotate(45deg);  background:#fff; left: 50%; margin-left:-.5rem; top:50%; margin-top:1.8rem;}
.user-register .bd{padding-bottom:1rem !important;}

/*=======================================
		商品列表页
  =======================================*/
/*弹出层部分共用样式重写*/
.c-filter-div .filter-city{margin-top:1px;}
.c-filter-div .select-one,.c-filter-div .select-two{margin-top:.7rem;}
/*商品分类页*/
.category-top .search{border-bottom:1px solid #e8e8e8;position:fixed; left:0; top:0; right:0; z-index: 1; background:#F6F6F9}

.menu-left, .menu-right {
	position: fixed;
	left: 0;
	top: 5.7rem;
	bottom: 0;
	overflow-y: scroll;
}

.menu-right #loading{left:11rem; top:6.7rem;}

.menu-left{background:#F6F6F9;}
.menu-left ul li{padding:1.3rem .8rem; box-sizing: border-box;  font-size:1.5rem; width:8.6rem; text-align: center;}
.menu-left ul li.active{background:#fff; position:relative;}
.menu-left ul li.active:before{content: " "; position: absolute;display: block; width:2px; height:100%; background:#1CBB7F; top:0; left:0;}
.menu-right{background:#fff; position:inherit; margin-left:8.6rem; margin-top: 0; right:0; bottom:0; left: 8.6rem}
.menu-right h5{font-size:1.3rem; color:#666; margin-top:1rem; margin-bottom:.4rem;}
.menu-right h5:first-child{margin-top:0;}
.menu-right ul{overflow: hidden;}
.menu-right ul li{text-align: center;}
.menu-right ul li a{display:block; position: absolute; left:.3rem; top:.7rem; bottom:.7rem; right:.3rem;}
.menu-right ul li:nth-child(3n+1) a{left:0; right:.7rem;}
.menu-right ul li:nth-child(3n) a{right:0; left: .7rem;}
.menu-right ul li span{display:block;height:2.6rem; line-height: 2.6rem; overflow: hidden; text-align: center; font-size:1.4rem}
.menu-right ul li img{width:5rem; height: auto;	}
.mune-no-img img{display:none}
.mune-no-img span{border:1px solid #efefef; color:#555; border-radius: 4px;}

/*=======================================
		购物车页
  =======================================*/
 .flow-no-cart .gwc-bg{height:16rem; width:16rem; line-height:16rem;  text-align: center; display: block; background:#e7e8ef; border-radius: 100%; margin: 0 auto; margin-top:6rem}
 .flow-no-cart i{font-size:10rem; color:#fff;}
 .flow-no-cart p.t-remark{margin-top:1.6rem;}
 .flow-no-cart a.btn-default{width:30%; display:block; margin: 0 auto; margin-top: 1.6rem;}
 .flow-no-cart .f-n-c-prolist{margin-top:6rem; }
 .flow-no-cart .f-n-c-prolist .swiper-slide{padding-top:.8rem;}
 .flow-no-cart .f-n-c-prolist .swiper-slide:first-of-type{padding-left:.8rem}
 .flow-no-cart .f-n-c-prolist .swiper-slide:last-of-type{padding-right:.8rem}
 .flow-no-cart .f-n-c-prolist h3.g-c-title-h3{width:100%;background:#fff; border-bottom:1px solid #F6F6F9}
 .flow-have-cart .product-list-medium a.icon-flow-cart{display:block;}
 .flow-have-cart header em{font-size:1.7rem; margin-top:.2rem; display: block; padding-left:1rem;}
 .flow-have-cart .product-list-small{padding:0 1.3rem;}
 .flow-have-cart .product-list-small .p-t-remark{padding-top:.4rem}
 .flow-have-cart .product-list-small li{box-sizing: border-box; border-bottom:1px solid #F6F6F9}
 .flow-have-cart .product-list-small li:last-of-type{border-bottom:none;}
 .flow-have-cart .product-list-small li>.dis-box{width:100%;}
 .flow-have-cart .product-list-small .ect-select{padding:1.3rem padding-left:0;  padding-top:1.8rem;}
 .flow-have-cart .product-list-small .product-div{padding:1.3rem 1rem; padding-top:0;  padding-right: 0;}
 .flow-have-cart .product-list-small .product-div i.icon-xiao10{position:absolute; font-size:1.8rem; color:#888; right:-.1rem; bottom:1.3rem;}
 .flow-have-cart .product-list-small .product-div:first-of-type{padding-top:1.3rem;} 
 .flow-have-cart .product-list-small .product-div .p-d-img{position: relative; overflow: hidden; width:9rem; height:9rem;}
 .flow-have-cart .product-list-small .product-div .p-d-img span{position:absolute; font-size:1.4rem; left:1px; right:0; bottom:0; padding:.4rem 0; text-align: center;  display: block; background:rgba(0,0,0,0.6); color:#fff;} 
 .flow-have-cart .product-list-small .product-div img{width:100%; height:auto;}
 .flow-have-cart .product-list-small .product-text{margin-left:10rem; height:auto; padding-top:0;}
 .flow-have-cart .product-list-small .product-text h4{position:relative; height:2rem; line-height: 2rem; display:block;}
 .flow-have-cart .product-list-small .product-text .t-first{font-size:1.5rem; display: block; margin-top:.2rem;}
 .flow-have-cart .product-list-small .product-div-link{left:1.3rem}
 .flow-have-cart .product-list-small .div-num{position:absolute; z-index: 4; width:8.6rem;}
 .flow-have-cart .product-list-small .div-num a{width:2.6rem; height:2.6rem;}
 .flow-have-cart .product-list-small .div-num input{height:1.6rem;}
 .flow-have-cart .div-num a.num-less:before, .flow-have-cart .div-num a.num-plus:before, .flow-have-cart .div-num a.num-plus:after{width:1.2rem; margin-left:-.65rem;}
 .flow-have-cart .g-promotion-con{padding-left:3.4rem; clear: both; padding-top:1.3rem; margin-bottom:.4rem; box-sizing: border-box;}
 .flow-have-cart .g-promotion-con p span{line-height:1.1; color:#555}
 .flow-have-cart .g-promotion-con .g-promotion-con-sh p{float:left; clear: inherit; display: inline-block;}
 .flow-have-cart .g-promotion-con .g-promotion-con-sh p:first-of-type{padding:.3rem 0;}
 .flow-have-cart .g-promotion-con .g-promotion-con-sh span{display:none;}
 .flow-have-cart .g-promotion-con p{margin-bottom:0;}
 .flow-have-cart .g-promotion-con i.icon-jiantou{margin-bottom:.4rem;}
 .flow-have-cart .g-promotion-con span.g-p-c-promotion{display: none; font-size:1.6rem;}
 .flow-have-cart .g-promotion-con .g-promotion-con-sh p:first-of-type{display:none;}
 .flow-have-cart .g-promotion-con.active span.g-p-c-promotion{display:inherit} 
 .flow-have-cart .g-promotion-con.active em.ec-promotion1,.flow-have-cart .g-promotion-con.active span.g-p-c-text{display:none}
 .flow-have-cart .g-promotion-con.active .g-promotion-con-sh p{float:inherit; display:inherit;}
 .flow-have-cart .g-promotion-con.active .g-promotion-con-sh p:first-of-type{display: inherit;}
 .flow-have-cart .g-promotion-con.active .g-promotion-con-sh span{display:initial;}
 .flow-have-cart .g-promotion-con.active .g-promotion-con-sh i.icon-jiantou{-webkit-transform: rotate(-180deg);
	 -moz-transform: rotate(-180deg);
 	-ms-transform: rotate(-180deg);
 	-o-transform: rotate(-180deg);
 	transform: rotate(-180deg);}
 .flow-have-cart .g-promotion-con.active i.icon-jiantou{-webkit-transform: rotate(-90deg);
 	-moz-transform: rotate(-90deg);
	-ms-transform: rotate(-90deg);
	-o-transform: rotate(-90deg);
	transform: rotate(-90deg);}
 .a-accessories{padding:.6rem .8rem; margin-top:1.3rem; border: 1px solid #efefef; color: #666; font-size:1.6rem; display: block; border-radius: 4px;}
 .f-cart-filter-btn{font-size:1.4rem;color:#666;}
 .f-cart-filter-btn .btn-submit, .f-cart-filter-btn .btn-default{padding:.84rem 1.2rem; width: auto;}
 .f-cart-filter-btn .ect-select{position:absolute; top:50%; margin-top:-1.1rem;}
 .f-cart-filter-btn .heart{margin-right:1.8rem; position: absolute; top:50%; margin-top:-1.8rem; right:16rem;  width:auto; display: initial;}
 .f-cart-filter-btn .heart i{width:2.2rem; height:2.2rem;}
 .f-cart-filter-btn .heart i{background-size:100% 4.4rem;}
 .f-cart-filter-btn .heart em{font-size:1.2rem; margin-top:0; color:#999;}
 .f-cart-filter-btn .heart.active em{color:#1CBB7F}
 .g-cart-filter-price{left:7rem; right:13rem; font-size:1.5rem; top:50%; margin-top:-1.6rem; position: absolute;}
 .g-cart-filter-price em, .g-cart-filter-price .t-first{line-height:2rem;}
 .g-cart-filter-price .t-remark{font-size:1.3rem;}
 .f-cart-filter-btn .ect-select label span{font-size:1.4rem; margin-left:.4rem}
 .f-cart-filter-btn .span-bianji{width:3rem; color:#999; display: block; text-align: center; margin-right:.9rem; margin-top:.2rem;}
 .f-cart-filter-btn .span-bianji i.icon-bianji1:before{font-size:1.8rem;}
 .f-cart-filter-btn .span-bianji em{display:block; font-size:1.2rem; margin-top:.2rem;}
 .f-cart-filter-btn .g-cart-filter-bj{display:none;}
 .f-cart-filter-btn.active .g-cart-filter-price,
 .f-cart-filter-btn.active .g-cart-filter-sb{display: none;}
 .f-cart-filter-btn.active .g-cart-filter-bj{display:initial}
 .f-cart-filter-btn.active .btn-submit,
 .f-cart-filter-btn.active .btn-default{padding:.84rem 2rem; width: auto;}
 .f-cart-filter-btn.active .btn-default{margin-right:1rem;}
 /*=======================================
		订单提交页
  =======================================*/
 .flow-checkout-adr{background:#fff url(../img/flow_check_03.png) bottom left repeat-x; background-size:8rem auto; font-size:1.7rem; position: relative; padding-top:1.6rem; padding-bottom:2rem; padding-right:3rem; position: relative;}
 .flow-checkout-adr a{position:absolute; left:0; top:0; right:0; bottom:0;}
 .flow-checkout-adr .t-jiantou{position:absolute; right:1.3rem; top:50%; margin-top:-.8rem;}
 .flow-no-adr i.icon-dingwei{margin-right:.6rem; font-size:1.8rem;}
 .flow-have-adr .f-h-adr-title label{margin-right:1rem;}
 .flow-checkout-pro .product-list-small .p-t-remark{margin-top:0; padding-top:0;}
 .flow-checkout-pro .product-list-small .p-price{font-size:1.5rem;}
 .flow-checkout-pro .product-text p{padding:0;}
 .flow-checkout-pro .g-r-rule{margin-top:.4rem;}
 .flow-checkout-pro header{font-size:1.7rem;}
 .flow-checkout-pro .product-list-small{padding:0 1.3rem;}
 .flow-checkout-pro .product-list-small ul li{padding-bottom:0;}
 .flow-checkout-pro .product-list-small .product-div{padding:1.3rem 0; border-bottom:1px solid #F6F6F9}
 .flow-checkout-pro .product-list-small ul li:last-of-type .product-div{border-bottom:0;}
 .flow-checkout-smallpic{overflow:hidden; padding-top:1.3rem; margin-bottom:1.3rem}
 .flow-checkout-smallpic li{margin-left:1rem; float:left; width:29% !important; text-align: center;}
 .flow-checkout-smallpic li img{width:100% !important;}
 .flow-checkout-smallpic li:first-of-type{margin-left:0;}
 .f-c-sp-more{font-size:1.4rem; position: relative; box-sizing: border-box;}
 .flow-checkout-pro .flow-checkout-bigpic{display:none;}
 .flow-checkout-pro span.t-jiantou{display:block; padding-left:1rem; margin-top:3.3rem;}
 .flow-checkout-pro.active span.t-jiantou  i{  -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg);}
 .flow-checkout-pro.active .f-c-a-count{display:none;}
 .flow-checkout-pro.active .flow-checkout-smallpic{display:none;}
 .flow-checkout-pro.active .flow-checkout-bigpic{display:block}
 /*配送时间*/
 .distribution-time{padding:0;}
 .distribution-time-con{font-size:1.4rem;}
 .distribution-time-con i{font-size:2.4rem;}
 .distribution-time label,.distribution-time span{margin-right:.6rem;}
 /*选择*/
 .flow-checkout-select{padding-top:0; padding:0 1.3rem;}
 .flow-checkout-select ul{border-top:1px solid #F6F6F9;}
 .flow-checkout-select ul li{padding:1.3rem 0; border-bottom:1px solid #F6F6F9;}
 .flow-checkout-select ul li.f-c-select-msg{border-bottom:0; padding-bottom:0;}
 .f-c-select-price{padding-top:.6rem; padding-bottom:1.5rem; font-size:1.6rem;}
 
 /*总价*/
 .flow-checkout-tprice ul li{padding-bottom:0; font-size:1.5rem;}
 .flow-checkout-tprice ul li:last-of-type{padding-bottom:1.3rem;}
 .flow-checkout-tprice header{font-size:1.7rem;}
 /*悬浮菜单*/ 
 .f-checkout-filter-btn span{margin-right:1rem; font-size:1.6rem; box-sizing: border-box; padding-top:.8rem;}
 .f-checkout-filter-btn span em{font-size:1.9rem;}
 .f-checkout-filter-btn .btn-submit{width:auto; padding:.84rem 1.2rem; display:block;}

 .s-g-list-con{overflow:hidden; max-height: 20rem;}
 .s-g-list-con ul li{padding:0;}
 .swiper-scroll{position:relative;}
 .swiper-wrapper,.swiper-slide{height:inherit !important;}
 .f-c-s-coupon{position:relative;}
 .f-c-s-coupon a{position:absolute; left: 0; top:0; bottom:0; right:0;}
 /*弹出时间层*/
 .show-time-con .ect-select{margin-right:1rem;}
 .show-time-con .ect-select label span{font-size:1.5rem;}
 .s-time-con-btn a{margin-left:1rem; font-size:1.5rem;}
 .s-time-con-btn a.s-time-clsoe{color:#777;}
 /*订单提交优惠券页面*/
 .flow-coupon{overflow:hidden}
 .flow-coupon .big-remark-all{position:relative;}
 .flow-coupon .big-remark-all h4{font-size:1.3rem;}
 .flow-coupon .big-remark-all p{font-size:1.1rem;}
 .flow-coupon .big-remark-all .remark-all{width:10rem;}
 .flow-coupon .big-remark-all .b-r-a-price{font-size:2.6rem;}
 .flow-coupon .big-remark-all .ect-select{position:absolute; right:1rem; top:50%; margin-top:-1.2rem;}
 /*收货地址列表*/
 .flow-consignee-list .flow-checkout-adr{background:#fff; padding-bottom:1.6rem;}
 .flow-consignee-list .flow-checkout-adr{padding:0;}
 .flow-consignee-list .flow-checkout-adr a{position:static; font-size:1.5rem; color:#777; margin-left:1rem;}
 .flow-consignee-list .flow-checkout-adr a i{font-size:1.9rem; margin-right:.8rem;}
 .flow-consignee-list .flow-have-adr{padding:2rem 1.6rem;}
 .flow-set-adr{border-bottom:1px solid #F6F6F9; font-size:1.3rem;}
 .flow-set-adr .ect-select label span{font-size:1.5rem; color:#666}
 /*填写－修改收货人信息*/
 .flow-consignee{margin-top:2rem;}
 
 .c-city-div .con-filter-div .filter-btn{left:100%;}
 .show-city-div .c-city-div .con-filter-div .filter-btn{left:0;}
 .c-city-div .menu-left{position:absolute; top:0; overflow-y: scroll;}
 .c-city-div .menu-right{position:absolute; left:8.6rem; top:0; right:0; bottom:0; overflow-y: scroll; margin:0;}
 .c-city-div .menu-right .select-title{font-size:1.5rem;}
 .c-city-div .menu-right .ect-select label{padding:1.2rem 0;}
 .c-city-div .menu-right .j-menu-select{border-top:1px solid #F6F6F9;}
 .j-get-city-one{border-bottom:1px solid #F6F6F9;margin-top:0 !important;}
/*发票*/
 .flow-receipt-type header,.flow-receipt-title header,.flow-receipt-cont header{font-size:1.7rem;}
 .flow-receipt-type-con,.flow-receipt-cont-con{padding:0 1.3rem;  font-size:1.5rem;}
 .flow-receipt-type-con ul,.flow-receipt-cont-con ul{border-top:0;}
 .flow-receipt-type-con ul li{background:#fff; border-bottom:1px solid #F6F6F9; padding-left:0; padding-right:0;}
 .flow-receipt-title header{padding-bottom:0;}
 .flow-receipt-title-con{padding:0 1.3rem;}
 .flow-receipt-title-con .text-all{border-bottom:0;}
 .f-c-receipt .t-jiantou{margin-top:1rem; display: block;}
 .f-c-receipt label{padding-top:.8rem;}
 .f-c-receipt  p.receipt-title{font-size:1.5rem;}
 .f-c-receipt  p.receipt-name{font-size:1.4rem; color:#666;}
/*自提点*/
 .flow-site ul li label{position:relative;}
 .flow-site ul li h4{font-size:1.8rem; color:#444;}
 .flow-site .active h4{color:#1CBB7F}
 .flow-site ul li label i.icon-gou{position:absolute; right:0; top:50%; margin-top:-.7rem;}
/**/
 .flow-done-con{ margin-top:5rem;  text-align: center;}
 .flow-done-con i.icon-qian{font-size:12rem; color: #EFCE0C;}
 .flow-done-con{color:#555; font-size:1.7rem;}
 .flow-done-con .flow-done-price{font-size:3rem;}
 .flow-done-id{margin-top:2.2rem}
 .flow-done-id label, .flow-done-id span{height:2rem; line-height:2rem; font-size:1.6rem;}
 .flow-done-other{text-align: center; }
 .flow-done-other a{color:#888; font-size:1.6rem;}

 /*悬浮菜单*/
 .filter-menu,.filter-top{position:fixed; right:1.6rem; bottom:1.6rem; left: 1.6rem; z-index: 11;}
 .filter-menu-title{width:4.6rem; height:4.6rem; border-radius: 100%; background:#1CBB7F; position:relative; float:right;}
 .filter-menu-title:before,.filter-menu-title:after{content:" "; display:block; width:1px; height:2.6rem; background:#fff; position:absolute; left:50%; margin-left:-1px; top:50%; margin-top:-1.3rem;-webkit-transition: all .5s; -moz-transition: all .5s; -o-transition: all .5s; transition: all .5s;} 
 .filter-menu-title:after{-moz-transform: rotate(-90deg);-webkit-transform: rotate(-90deg);-ms-transform: rotate(-90deg);-o-transform: rotate(-90deg);transform: rotate(-90deg);}
 .filter-menu-list{background:rgba(0,0,0,0.8); height:4.6rem; border-radius: 2.3rem; position:absolute; right:0; width:0; opacity: 0;}
 .filter-menu-list ul{height:4.6rem; margin-left:.8rem;}
 .filter-menu-list ul li{margin:0 .2rem; padding:0 .4rem; color:#fff; text-align: center; padding:.3rem 0;}
 .filter-menu-list ul li i{font-size:1.8rem;}
 .filter-menu-list ul li em{display:block; font-size:1.2rem;}
 .filter-menu-list ul li.w{width:4.6rem;}
 .filter-menu.active .filter-menu-list {left:0; right:0; width:inherit; opacity: 1;}
 .filter-menu.active  .filter-menu-title:before{-moz-transform: rotate(-135deg);-webkit-transform: rotate(-45deg);-ms-transform: rotate(-45deg);-o-transform: rotate(-45deg);transform: rotate(-45deg);}
 .filter-menu.active  .filter-menu-title:after{-moz-transform: rotate(-135deg);-webkit-transform: rotate(-135deg);-ms-transform: rotate(-135deg);-o-transform: rotate(-135deg);transform: rotate(-135deg);}
 .filter-top{width:4.6rem; height:4.6rem;  text-align:center; line-height:4.6rem; background:rgba(0,0,0,0.5); border-radius: 100%; bottom:7.1rem; right:1.6rem; left:inherit}
 .filter-top i.icon-jiantou{font-size:2.4rem; color:#fff;}
 .filter-top i.icon-jiantou:before{ position: absolute; left:50%; margin-left:-1.3rem; margin-top:.3rem; -moz-transform: rotate(90deg);-webkit-transform: rotate(90deg);-ms-transform: rotate(90deg);-o-transform: rotate(90deg);transform: rotate(90deg);}
 
/*1-7*/
.pull-left{
	float: left;
}
.pull-right{
	float: right;
}
/*颜色版本*/
.ect-bg{  background: #1CBB7F !important;color: #fff !important;height: auto;overflow: hidden;}}
/*顶部高度*/
.ect-header{height:2.5em; position:relative;}
.ect-header span{font-size: 1.4em;position: absolute;left: 2.4em;right: 3em;top: 0.64em;}
.ect-header a{  width: 2.5em;height: 2.5em;}
.ect-text-left{text-align:left;}
.ect-border-none{border:none;}
.ect-margin-lr{margin-left:0.6em; margin-right:0.6em;}
.ect-margin-tb{  margin-top: 0.6em;margin-bottom: 0.6em;}
.ect-padding-lr{padding-left:0.6em; padding-right:0.6em;}
.ect-padding-tb{padding-top:0.6em; padding-bottom:0.6em;}
.ect-border-radius100{ border-radius: 100%; -moz-border-radius: 100%; -webkit-border-radius: 100%; -o-border-radius: 100%;}
.ect-border-radius0{ border-radius:0; -moz-border-radius:0; -webkit-border-radius:0; -o-border-radius:0;}
.ect-icon-color{color:silver;}
.ect-diaplay-box{display: -webkit-box;display: -moz-box;display: -ms-box;display: box;}
.ect-box-flex{-webkit-box-flex: 1;-moz-box-flex: 1;-ms-box-flex: 1;box-flex: 1; display:block; width:100%;}
.ect-margin-bottom0{margin-bottom:0 !important;}
/*icon灰色*/
/*icon灰色*/
.ect-icon{background-repeat:no-repeat; background-position:center center; background:url(../../images/iconall.png) no-repeat; background-size:15em;}
.ect-icon1{background-size:18em;}
.ect-icon-cate3{background-position:-0.1em 0em; }
.ect-icon-home{background-position:-6.45em -0.2em;}
.ect-icon-search{background-position:-10.8em -0.2em;}
.ect-icon-search1{background-position:-12.85em 0em;}
.ect-icon-cate{background-position:-0.1em 0em; background-size:13em;}
.ect-icon-flow{background-position:-2.7em -0.15em;}
.ect-icon-user{background-position:-12.98em -0.15em;}
.ect-icon-history{background-position:-5.4em -0.1em;}
/*ico白色*/
.icon-write .ect-icon-mune{background-position:-10.36em -2.6em;}
/*导航*/
nav.ect-nav{color:#999; background:#fcfcfc; height:auto; width:100%; max-width:640px; border-top:1px solid #e3e3e3;}
footer nav.ect-nav{position:fixed; bottom:0;}
nav.ect-nav a{color:#999;  font-size: 1.5rem;}
nav.ect-nav ul li{padding:0.3em 0; width:20%; }
nav.ect-nav ul li i{display:block; width:1.9em; height:1.9em; margin:0.1em auto;}
header nav.ect-nav{top:0; border-top:none; z-index:100; position:fixed;}
header nav.ect-nav a{color:#fff;}
/*内页导航*/
nav.ect-nav-list{border-top:none; border-bottom:1px solid #e3e3e3; position:relative;}
.ect-diaplay-box{display: -webkit-box;display: -moz-box;display: -ms-box;display: box;}
.ect-box-flex{-webkit-box-flex: 1;-moz-box-flex: 1;-ms-box-flex: 1;box-flex: 1; display:block; width:100%;}
/*banner效果*/
.focus { width: 100%; height: auto; position: relative; overflow: hidden;}
.focus .hd { width: 100%; height: 0.8em; position: absolute; z-index: 1; text-align: center; bottom:0.5em;}
.focus .hd ul { display: inline-block; height: 0.8em;}
.focus .hd ul li { display: inline-block; width: 0.6em; height: 0.6em; text-indent:-999em; background: rgba(255, 255, 255, 0.8); margin: 0 0.3em; vertical-align: top; overflow: hidden; border-radius: 100%; -moz-border-radius: 100%; -webkit-border-radius: 100%; -o-border-radius: 100%; }
.focus .hd ul .on { background: #1CBB7F; }
.focus .bd { position: relative; z-index: 0;}
.focus .bd li { height: auto; overflow: hidden; }
.focus .bd li a img { width: 100%; height: auto; display: block; margin: 0; padding: 0; }
.focus .bd li a { -webkit-tap-highlight-color: rgba(0, 0, 0, 0);/* 取消链接高亮 */ }
/*========================
        商品详情页面
=============*/
.goods-info{border-top:1px #e3e3e3 solid;  width:100%; padding-bottom:0; border-bottom:0;}
.goods-info p{overflow:hidden;}
.goods-info section{position:relative; overflow:hidden;}
.tab-pane img{width:100% ; height: auto;}
.goods-info section.goods-title h4{position:absolute; right:3em; left:0em;}
.goods-info section.goods-title span{font-size:1em; border-left:1px solid #e3e3e3;}
.goods-info section.goods-title span i.fa{font-size:1.5em;}

.show-goods-coupon.show, .show-goods-attr.show, .show-goods-service.show, .show-goods-dist.show, .show-time-con.show{
	bottom:0 ;
}
.mask-filter-div.show {
    display: inherit;
}
.icon-write .ect-icon-history {
    background-position: -5.4em -2.7em;
}
/*comment*/
.goods-evaluation-page .tab-title {
    border-bottom: 1px solid #F3F4F9;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 11;
}
.evaluation-page{
	border-bottom: 1px solid #F3F4F9;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 11;
}
.evaluation-page ul {
    padding: 1.2rem 0;
}
.comment-header{
	position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 75;
}

footer.logo {
    text-align: center;
    border-top: 1px solid #e3e3e3;
}
footer.logo img {
    width: 8em;
    height: auto;
    display: block;
    margin: 0.8em auto;
}
.goods-info-attr li span:first-of-type{max-width: 30%;}
.goods-info-attr li span:last-of-type{max-width: 66%;}
/*.goods{margin-bottom: 0;}*/

.jian001{
        margin-right: 1.5rem;
}
.no-div-message{text-align: center; padding-top:6rem; color:#999}
.no-div-message .icon-biaoqingleiben{font-size:7rem; color:#a6a6a6;}
.no-div-message p{margin-top:1.8rem; font-size:1.6rem;}
.no-div-message a{font-size:1.5rem; margin-top:.5rem; display:block; color:#EC5151}
.padding-all-1{padding:1rem 1.3rem;}
.n-list-pl{border-bottom:1px solid #f6f6f9;}
/*搜索未商品提示内容*/
.n-cate-box-1{margin:0 auto;padding: 4rem 0;}
.n-cate-box-1 p{font-size:1.55rem; color:#888;text-align:center;}
.n-cate-box-1 span{background:#fff; padding:0.8rem;border:1px solid #efefef;text-align: center;display:block;margin:0 auto;font-size:1.5rem;color:#555;border-radius:0.5rem;    width: 45%;margin-top: 0.8rem;}
