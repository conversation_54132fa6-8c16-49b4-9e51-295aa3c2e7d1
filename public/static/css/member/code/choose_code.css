/* 选择卡券页面样式 */

/* Vue.js隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Segoe UI, Arial, Roboto, PingFang SC, miui, Hiragino Sans GB, Microsoft Yahei, sans-serif;
}

p {
  margin: 0;
  padding: 0;
}

img {
  max-width: 100% !important;
}

/* 页面容器样式 */
.page-container {
  padding: 15px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 卡券项样式 */
.box {
  width: 48%;
  float: left;
  margin-right: 2%;
  margin-bottom: 2%;
  background: #fff;
  border-radius: 8px;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.box:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: var(--theme-primary);
}

.box img {
  width: 100%;
  height: auto;
  border-radius: 4px;
  background-color: #f5f5f5;
}

/* 标题样式 */
.title {
  color: #333;
  font-size: 14px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 600;
  margin-top: 8px;
}

/* 提交按钮区域 */
.submit_button_div {
  position: sticky;
  bottom: 0;
}

/* 价格样式 */
.price {
  color: var(--theme-text);
  font-size: 18px;
}

.yuan {
  color: var(--theme-text);
}

/* 空状态样式 */
.empty-container {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 80px;
  color: #d2d2d2;
  margin-bottom: 20px;
  display: block;
}

.empty-title {
  color: #666;
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 500;
}

.empty-desc {
  color: #999;
  font-size: 14px;
  line-height: 1.5;
}

/* 加载状态样式 */
.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loading-container i {
  font-size: 24px;
  margin-right: 10px;
}

/* 响应式设计 */

/* Macy瀑布流布局相关样式 */
#macy-container {
  width: 100%;
}

#macy-container .box {
  width: calc(50% - 5px);
  margin: 0 0 10px 0;
}

#macy-container .box:nth-child(odd) {
  margin-right: 10px;
}
