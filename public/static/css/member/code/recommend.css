* {
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none;
  text-decoration: none;
  color: inherit;
  font-weight: normal;
  font-family: "微软雅黑";
  /*box-sizing: border-box;*/
  font-style: normal;
  outline: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  width: 100%;
  overflow-x: hidden;
  background: var(--theme-bg-color, #ffffff);
}

img {
  vertical-align: middle;
  max-width: 100%;
}

.container {
  width: 100%;
  padding: 0 0.4rem;
}

/*顶部标题返回*/
.top-bar {
  position: fixed;
  width: 100%;
  max-width: 750px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  height: 0.9rem;
  padding: 0 0.2rem;
  background: var(--theme-card-bg, #fff);
  z-index: 99;
}

.top-bar i {
  display: inline-block;
  width: 0.5rem;
  height: 0.9rem;
  background: url(/static/img/member/code/images/arrow-left.png) left center no-repeat;
  background-size: 0.18rem 0.31rem;
  position: relative;
  z-index: 3;
}

.top-bar .title {
  position: absolute;
  height: 0.4rem;
  width: 100%;
  text-align: center;
  left: 0;
  font-size: 0.34rem;
  z-index: 2;
}

/* Layui tabs 自定义样式 */
.layui-tab-brief {
  position: fixed;
  top: 0;
  width: 100%;
  max-width: 750px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--theme-card-bg, #fff);
  z-index: 100;
  margin: 0;
}

/* 移除tab的默认边距 */
.layui-tab-brief > .layui-tab-title {
  margin: 0;
  padding: 0 0.4rem;
}

.layui-tab-brief > .layui-tab-title {
  border-bottom: 1px solid var(--theme-border-color, #e2e2e2);
  background: var(--theme-card-bg, #fff);
  padding: 0 0.4rem;
}

.layui-tab-brief > .layui-tab-title .layui-this {
  color: var(--theme-primary);
}

.layui-tab-brief > .layui-tab-title .layui-this:after {
  border-bottom: 2px solid var(--theme-primary);
}

.layui-tab-content {
  padding: 0;
  height: calc(100vh - 1.5rem - 60px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0.2rem 0 1rem 0;
  position: relative;
}

.container ul {
  display: flex;
  flex-direction: column;
  display: none;
}

.container ul.active {
  display: block;
}

.container ul li {
  width: 100%;
  height: 2.18rem;
  background: url(/static/img/member/code/images/item-bg.png) center center no-repeat;
  background-size: 100%;
  margin-bottom: 0.15rem;
  display: flex;
  position: relative;
}

.container ul li .l {
  flex-shrink: 0;
  width: 2.2rem;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container ul li .l p {
  font-size: 0.51rem;
  display: flex;
  align-items: baseline;
  padding-top: 0.5rem;
  padding-bottom: 0.48rem;
  font-weight: bold;
}

.container ul li .l p i {
  font-size: 0.24rem;
}

.container ul li .l span {
  font-size: 0.3rem;
  color: #a63a08;
}

.container ul li .r {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-left: 0.45rem;
  padding-right: 0.15rem;
}

.container ul li .r h3 {
  font-size: 0.32rem;
  color: var(--theme-text-color, #000);
}

.container ul li .r .use {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: var(--theme-text-secondary, #666666);
  margin-top: 0.03rem;
}

.container ul li .r .use button {
  flex-shrink: 0;
  width: 1.2rem;
  height: 0.52rem;
  line-height: 0.52rem;
  font-size: 0.24rem;
  color: #fff;
  background: var(--theme-success-color, #01bb75);
  border-radius: 0.05rem;
}

.container ul li .r .use button[disabled] {
  background-color: var(--theme-disabled-color, #bbb);
  pointer-events: none;
  cursor: not-allowed;
}

.container ul li .r .status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.26rem;
  color: var(--theme-text-secondary, #666666);
  margin-top: 0.15rem;
}

.container ul li .r .status span {
  flex-shrink: 0;
  padding: 0.05rem 0.1rem;
  background: var(--theme-warning-bg, #f6e5dc);
  font-size: 0.16rem;
  color: var(--theme-warning-color, #dd3f14);
  border-radius: 0.05rem;
}

/* 兑换优惠卷 */
.exchange {
  position: fixed;
  left: 0;
  bottom: 100px;
  width: 100%;
  height: auto;
  padding: 0.2rem 0.35rem 0.2rem 0.35rem;
  background: var(--theme-card-bg, #fff);
  z-index: 99;
}

.exchange button {
  width: 100%;
  height: 0.96rem;
  line-height: 0.96rem;
  background: var(--theme-success-color, #01ba79);
  font-size: 0.36rem;
  color: #fff;
  border-radius: 0.15rem;
}

/* ===== 新的卡片样式 ===== */

/* 推荐卡片容器 */
.recommend-cards-container {
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.16rem;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 2rem 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.loading-content i {
  font-size: 2rem;
  color: var(--theme-primary-color, #1890ff);
}

.loading-content h4 {
  font-size: 0.32rem;
  color: var(--theme-text-secondary, #666666);
  margin: 0;
  font-weight: 400;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 2rem 0;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.empty-content i {
  font-size: 2rem;
  color: var(--theme-text-light, #999999);
}

.empty-content h4 {
  font-size: 0.32rem;
  color: var(--theme-text-light, #999999);
  margin: 0;
}

/* 卡片包装器 */
.recommend-card-wrapper {
  margin-bottom: 0.2rem;
  width: 100%;
  display: flex;
  justify-content: center;
}

/* 推荐卡片主体 */
.recommend-card {
  background: linear-gradient(135deg, #3f51b5 0%, #5e35b1 100%);
  border-radius: 0.24rem;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  width: 100%;
  min-width: 280px;
}

/* 已发放奖励卡片 */
.recommend-card.rewarded {
  background: linear-gradient(135deg, #2e7d32 0%, #388e3c 50%, #43a047 100%);
}

/* 待发放奖励卡片 */
.recommend-card.pending {
  background: linear-gradient(135deg, #e65100 0%, #ff6f00 50%, #ff8f00 100%);
}

/* 已兑换卡片 */
.recommend-card.used {
  background: linear-gradient(135deg, #424242 0%, #616161 50%, #757575 100%);
}

.recommend-card:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 科技感背景装饰 */
.recommend-card::before {
  content: "";
  position: absolute;
  top: -0.6rem;
  right: -0.6rem;
  width: 2rem;
  height: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.recommend-card::after {
  content: "";
  position: absolute;
  bottom: -0.4rem;
  left: -0.4rem;
  width: 1.2rem;
  height: 1.2rem;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  z-index: 0;
}

/* 卡片装饰条纹 */
.recommend-card .card-stripe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.04rem;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

/* 卡片主要内容 */
.recommend-card .card-content {
  padding: 0.24rem;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 0.16rem;
  min-height: 2.4rem;
}

/* 卡片标题区域 */
.card-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-title-left {
  flex: 1;
}

.card-title {
  font-size: 0.22rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  letter-spacing: 0.01rem;
  margin: 0 0 0.04rem 0;
}

.card-subtitle {
  font-size: 0.28rem;
  color: #ffffff;
  font-weight: 700;
  letter-spacing: 0.02rem;
}

.card-chip {
  width: 0.52rem;
  height: 0.28rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 0.14rem;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.card-chip::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.16rem;
  height: 0.16rem;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 卡片状态区域 */
.card-status-section {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  padding: 0.08rem 0.16rem;
  border-radius: 0.2rem;
  font-size: 0.22rem;
  font-weight: 500;
  border: 0.01rem solid rgba(255, 255, 255, 0.2);
}

.status-badge.success {
  background: rgba(76, 175, 80, 0.2);
  color: #ffffff;
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffffff;
}

.status-badge.used {
  background: rgba(158, 158, 158, 0.2);
  color: #ffffff;
}

/* 卡片信息区域 */
.card-info-section {
  display: flex;
  flex-direction: column;
  gap: 0.12rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.22rem;
  margin-bottom: 0.08rem;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.info-value {
  color: #ffffff;
  font-weight: 500;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
}

.info-value.reward-code {
  color: #ffeb3b;
  font-weight: 600;
}

.goto-use-btn {
  background: rgba(255, 255, 255, 0.9);
  color: var(--theme-success-color, #00ae66);
  border: none;
  padding: 0.12rem 0.24rem;
  border-radius: 0.2rem;
  font-size: 0.24rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.goto-use-btn:hover {
  background: #ffffff;
}

.goto-use-btn:active {
  background: rgba(255, 255, 255, 0.8);
}

/* 右上角去兑换按钮 */
.goto-use-btn-corner {
  position: absolute;
  top: 0.16rem;
  right: 0.16rem;
  background: rgba(255, 255, 255, 0.95);
  color: var(--theme-success-color, #00ae66);
  border: none;
  padding: 0.08rem 0.16rem;
  border-radius: 0.12rem;
  font-size: 0.2rem;
  font-weight: 600;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.goto-use-btn-corner:hover {
  background: #ffffff;
  transform: scale(1.05);
}

.goto-use-btn-corner:active {
  transform: scale(0.95);
}

/* 科技感装饰圆点 */
.recommend-card .card-dots {
  position: absolute;
  bottom: 0.2rem;
  left: 0.28rem;
  display: flex;
  gap: 0.12rem;
  z-index: 1;
}

.recommend-card .card-dot {
  width: 0.12rem;
  height: 0.12rem;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  position: relative;
}

.recommend-card .card-dot::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.06rem;
  height: 0.06rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.recommend-card .card-dot:nth-child(2) {
  background: rgba(255, 255, 255, 0.6);
}

.recommend-card .card-dot:nth-child(3) {
  background: rgba(255, 255, 255, 0.3);
}

/* 隐藏原有的列表样式 */
.container ul {
  display: none !important;
}

.layui-tab-title {
  text-align: center;
}

body,
html {
  background-color: var(--theme-bg-color, #ffffff) !important;
}

#app {
  background-color: var(--theme-bg-color, #ffffff);
}

.layui-layer-btn0 {
  font-size: 14px !important;
}

.bottom-btn {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 750px;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  background: transparent;
  box-shadow: none;
  border: none;
}

.bottom-btn .layui-btn {
  width: 100%;
  height: 45px;
  box-shadow: none !important;
  border: none !important;
}

.bind {
  top: 10% !important;
}

/* ===================================================================
   提取的内联样式 - recommend.html 页面专用
   ================================================================= */

/* 主容器显示样式 */
.main-container {
  display: block;
}

/* 弹窗内容样式 */
.popup-content {
  font-size: 18px;
  padding: 10px;
}

.layui-layer-setwin .layui-layer-close {
  color: #FFF;
}
