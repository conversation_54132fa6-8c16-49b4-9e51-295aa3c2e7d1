/* WeUI到LayUI重构 - code/tips.html 样式文件 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

[v-cloak] {
  display: none !important;
}

/* 主要内容区域样式 */
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  text-align: center;
  background: var(--theme-bg-color, #f8f9fa);
}

/* 图标区域 */
.icon-area {
  margin-bottom: 30px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 标题样式 */
.main-title {
  font-size: 24px;
  color: var(--theme-text-color, #333);
  margin-bottom: 16px;
  font-weight: 600;
}

.sub-title {
  font-size: 16px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 40px;
  line-height: 1.5;
}

/* 提示卡片 */
.tip-card {
  background: var(--theme-card-bg, #fff);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 40px;
  max-width: 320px;
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--theme-border-color, #e8e8e8);
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.tip-indicator {
  width: 4px;
  height: 20px;
  background: var(--theme-primary, #1890ff);
  border-radius: 2px;
  margin-right: 12px;
}

.tip-title {
  font-size: 16px;
  color: var(--theme-text-color, #333);
  font-weight: 500;
  margin: 0;
}

.tip-content {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.6;
  margin: 0;
}

/* 操作指南 */
.guide-container {
  max-width: 320px;
  width: 100%;
}

.guide-card {
  background: var(--theme-card-bg, #fff);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--theme-border-color, #e8e8e8);
}

.guide-title {
  font-size: 16px;
  color: var(--theme-text-color, #333);
  margin-bottom: 16px;
  font-weight: 500;
}

.guide-content {
  text-align: left;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.guide-step:last-child {
  margin-bottom: 0;
}

.step-number {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: var(--theme-success, #52c41a);
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
}

/* 底部说明 */
.bottom-note {
  font-size: 12px;
  color: var(--theme-text-tertiary, #999);
  margin-top: 40px;
  line-height: 1.4;
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content > * {
  animation: fadeInUp 0.6s ease-out;
}

.main-content > *:nth-child(2) {
  animation-delay: 0.1s;
}

.main-content > *:nth-child(3) {
  animation-delay: 0.2s;
}

.main-content > *:nth-child(4) {
  animation-delay: 0.3s;
}

.main-content > *:nth-child(5) {
  animation-delay: 0.4s;
}

/* 悬停效果 */
.tip-card:hover,
.guide-card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 主题适配 */

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
}

.loading-content {
  text-align: center;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  color: var(--theme-text-secondary, #666);
  font-weight: normal;
  margin: 0;
}
