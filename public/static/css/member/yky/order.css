/* 易卡易订单页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 页面标题 */
.page-title {
  text-align: center;
  padding: 20px;
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.page-title h1 {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.page-title i {
  font-size: 20px;
  color: #ff6037;
}

/* 标签页容器 */
.tab-container {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 标签页头部 */
.tab-header {
  display: flex;
  background: var(--theme-background-light, #fafafa);
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
}

.tab-item {
  flex: 1;
  padding: 16px 20px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-secondary, #666);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item.active {
  color: #ff6037;
  background: #fff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: #ff6037;
  border-radius: 2px;
}

.tab-item:hover {
  color: #ff6037;
  background: rgba(255, 96, 55, 0.05);
}

/* 标签页内容 */
.tab-content {
  min-height: 400px;
}

.tab-pane {
  display: none;
  padding: 0;
}

.tab-pane.active {
  display: block;
}

/* 订单列表 */
.order-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-item {
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border-light, #f0f0f0);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.order-item:hover {
  background: var(--theme-hover, #f8f9fa);
}

.order-item:last-child {
  border-bottom: none;
}

.order-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.order-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
}

.order-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.order-icon i {
  font-size: 24px;
  color: var(--theme-primary, #1890ff);
}

/* 订单类型图标 */
.order-icon.oil {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.order-icon.oil i {
  color: #fff;
}

.order-icon.mobile {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.order-icon.mobile i {
  color: #fff;
}

.order-icon.money {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
}

.order-icon.money i {
  color: #fff;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-time {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 2px;
}

.order-memo {
  font-size: 12px;
  color: var(--theme-text-placeholder, #999);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-amount {
  text-align: right;
  flex-shrink: 0;
  margin-left: 12px;
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: #ff6037;
  margin-bottom: 4px;
}

.order-status {
  font-size: 12px;
  color: var(--theme-success, #52c41a);
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme-text-placeholder, #ccc);
  margin-bottom: 16px;
  display: block;
}

.empty-state h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--theme-text-secondary, #666);
}

.empty-state p {
  font-size: 14px;
  color: var(--theme-text-placeholder, #999);
  margin: 0;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border, #e5e5e5);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 易卡易品牌色彩 */
.yky-theme {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
}

.yky-icon {
  color: #ff6037;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.order-item:active {
  background: var(--theme-active, #e6f7ff);
}

.tab-item:active {
  background: rgba(255, 96, 55, 0.1);
}

/* 订单项悬停效果 */
.order-item:hover .order-title {
  color: #ff6037;
}

.order-item:hover .amount-value {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 刷新按钮 */
.refresh-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: #ff6037;
  border: none;
  border-radius: 50%;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(255, 96, 55, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.refresh-button:hover {
  background: #e55a32;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 96, 55, 0.4);
}

.refresh-button:active {
  transform: translateY(0);
}

.refresh-button.loading {
  pointer-events: none;
}

.refresh-button.loading i {
  animation: spin 1s linear infinite;
}

/* 标签页统计 */
.tab-stats {
  padding: 12px 20px;
  background: var(--theme-background-light, #fafafa);
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-label {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 4px;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: #ff6037;
}

/* 响应式优化 */

/* 滚动优化 */
.tab-content {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-content::-webkit-scrollbar {
  width: 4px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--theme-background-light, #f5f5f5);
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--theme-border, #e5e5e5);
  border-radius: 2px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-placeholder, #ccc);
}
