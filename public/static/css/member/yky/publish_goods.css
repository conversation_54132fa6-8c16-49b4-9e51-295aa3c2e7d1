/* 商品发布页面样式 */

/* 页面基础样式 */
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--theme-primary-light), #f8f9fa);
  padding: 20px 15px;
}

/* 表单容器 */
.form-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: var(--theme-shadow);
  overflow: hidden;
  margin-bottom: 20px;
}

/* 表单头部 */
.form-header {
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-light));
  color: #fff;
  padding: 25px 20px;
  text-align: center;
}

.form-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.form-desc {
  font-size: 14px;
  opacity: 0.9;
  margin: 8px 0 0 0;
}

/* 表单内容区域 */
.form-content {
  padding: 25px 20px;
}

/* 表单组 */
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text);
  margin-bottom: 8px;
}

.form-label.required::after {
  content: "*";
  color: #ff4757;
  margin-left: 4px;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #fff;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.1);
}

.form-input::placeholder {
  color: #adb5bd;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: all 0.3s ease;
  font-family: inherit;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(var(--theme-primary-rgb), 0.1);
}

/* 字符计数器 */
.char-counter {
  text-align: right;
  font-size: 12px;
  color: #6c757d;
  margin-top: 5px;
}

/* 图片上传区域 */
.upload-container {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.upload-container:hover {
  border-color: var(--theme-primary);
  background: rgba(var(--theme-primary-rgb), 0.05);
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.upload-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text);
  margin: 0;
}

.upload-info {
  font-size: 12px;
  color: #6c757d;
}

/* 图片预览区域 */
.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.preview-item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e9ecef;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.preview-delete {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  background: #ff4757;
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.preview-delete:hover {
  background: #ff3742;
  transform: scale(1.1);
}

/* 上传按钮区域 */
.upload-input-area {
  position: relative;
  display: inline-block;
}

.upload-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-button:hover {
  background: var(--theme-hover);
  transform: translateY(-1px);
}

.upload-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
}

/* 上传提示 */
.upload-tips {
  font-size: 12px;
  color: #6c757d;
  margin-top: 10px;
  line-height: 1.4;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-hover));
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--theme-primary-rgb), 0.3);
}

.submit-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  min-width: 120px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 14px;
  color: var(--theme-text);
  margin: 0;
}

/* 成功状态 */
.success-container {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: #2ed573;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: #fff;
  font-size: 24px;
}

.success-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--theme-text);
  margin: 0 0 10px 0;
}

.success-desc {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 20px 0;
}

.success-button {
  padding: 10px 20px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-button:hover {
  background: var(--theme-hover);
}

/* 响应式设计 */

/* Vue过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 隐藏元素 */
[v-cloak] {
  display: none !important;
}
