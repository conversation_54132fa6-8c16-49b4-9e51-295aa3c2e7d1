/* 储值充值页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 页面标题 */
.page-title {
  text-align: center;
  padding: 20px;
  background: #fff;
  margin: 16px;
  margin-top: 35px;
  border-radius: 16px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.page-title h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.page-title i {
  font-size: 24px;
  color: #ff6037;
}

/* 充值表单区域 */
.recharge-form {
  background: #fff;
  margin: 20px 10px 15px;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 表单项 */
.form-item {
  margin-bottom: 24px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text, #333);
  background: var(--theme-background-light, #fafafa);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  background: #fff;
  box-shadow: 0 0 0 2px var(--theme-primary-light, rgba(24, 144, 255, 0.2));
}

.form-input.error {
  border-color: var(--theme-error, #ff4d4f);
  background: var(--theme-error-light, rgba(255, 77, 79, 0.1));
}

/* 充值按钮 */
.recharge-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 10px 0;
}

.recharge-button:hover {
  background: linear-gradient(135deg, #e55a32, #ff7a56);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 96, 55, 0.3);
}

.recharge-button:active {
  transform: translateY(0);
}

.recharge-button:disabled {
  background: var(--theme-background-disabled, #f5f5f5);
  color: var(--theme-text-disabled, #999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.recharge-button.loading {
  pointer-events: none;
  position: relative;
}

.recharge-button.loading::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 错误提示 */
.error-message {
  color: var(--theme-error, #ff4d4f);
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message i {
  font-size: 14px;
}

/* 充值说明 */
.recharge-info {
  background: var(--theme-info-light, rgba(24, 144, 255, 0.1));
  border: 1px solid var(--theme-info, #1890ff);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.recharge-info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recharge-info li {
  font-size: 14px;
  color: var(--theme-info, #1890ff);
  line-height: 1.6;
  margin-bottom: 8px;
  position: relative;
  padding-left: 20px;
}

.recharge-info li:last-child {
  margin-bottom: 0;
}

.recharge-info li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--theme-info, #1890ff);
  font-weight: bold;
}

/* 金额输入框特殊样式 */
.amount-input {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #ff6037;
}

.amount-input:focus {
  color: var(--theme-text, #333);
}

/* 金额显示区域 */
.amount-display {
  background: var(--theme-background-light, #fafafa);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  text-align: center;
}

.amount-label {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 8px;
}

.amount-value {
  font-size: 24px;
  font-weight: 700;
  color: #ff6037;
  margin-bottom: 4px;
}

.amount-unit {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
}

.conversion-rate {
  font-size: 12px;
  color: var(--theme-text-placeholder, #999);
  margin-top: 8px;
}

/* 易卡易品牌色彩 */
.yky-theme {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
}

.yky-icon {
  color: #ff6037;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.form-input:active {
  transform: scale(0.99);
}

.recharge-button:active {
  transform: scale(0.98);
}

/* 表单验证状态 */
.form-item.success .form-input {
  border-color: var(--theme-success, #52c41a);
  background: var(--theme-success-light, rgba(82, 196, 26, 0.1));
}

.form-item.success .form-label::after {
  content: '✓';
  color: var(--theme-success, #52c41a);
  margin-left: 8px;
  font-weight: bold;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border, #e5e5e5);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

/* 快捷金额选择 */
.quick-amounts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.quick-amount-item {
  padding: 12px 8px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--theme-background-light, #fafafa);
}

.quick-amount-item:hover {
  border-color: #ff6037;
  background: rgba(255, 96, 55, 0.1);
}

.quick-amount-item.active {
  border-color: #ff6037;
  background: rgba(255, 96, 55, 0.1);
  color: #ff6037;
}

.quick-amount-value {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.quick-amount-label {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

/* 充值流程说明 */
.process-steps {
  background: var(--theme-background-light, #fafafa);
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.process-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.process-title i {
  color: #ff6037;
}

.process-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.process-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 13px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
}

.process-item:last-child {
  margin-bottom: 0;
}

.process-number {
  width: 20px;
  height: 20px;
  background: #ff6037;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.process-text {
  flex: 1;
}

/* 汇率显示 */
.exchange-rate {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  text-align: center;
}

.exchange-rate-title {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.exchange-rate-value {
  font-size: 16px;
  font-weight: 600;
}

/* 响应式优化 */

/* 滚动优化 */
.page-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .page-container {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
    padding-bottom: max(60px, env(safe-area-inset-bottom));
  }
}
