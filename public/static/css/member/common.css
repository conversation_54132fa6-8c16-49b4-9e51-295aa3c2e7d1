/* 通用主题样式文件 - 所有主题共用 */
/* 通过CSS变量控制颜色，由JavaScript动态设置变量值 */

/* 默认变量定义（防止未加载主题时的fallback） - 完整版 */
:root {
  /* ===== 核心主题变量 - 由JavaScript动态控制 ===== */
  --theme-primary: #00a95b; /* 主色调 */
  --theme-primary-light: #00f584; /* 渐变浅色 (亮度+15%) */
  --theme-hover: #00d271; /* 悬浮效果 (亮度+8%) */
  --theme-shadow: rgba(0, 169, 91, 0.08); /* 阴影 (透明度0.08) */
  --theme-text: #00a95b; /* 文字色 (与主色相同) */

  /* ===== 扩展的通用变量 - 固定值 ===== */
  --theme-secondary: #f8f9fa;
  --theme-text-light: #666;
  --theme-border: #e0e0e0;
  --theme-background: #fff;

  /* ===== 语义化状态颜色 ===== */
  --theme-success: #52c41a;
  --theme-warning: #faad14;
  --theme-danger: #ff4d4f;
  --theme-error: #ff4d4f;
  --theme-info: #1890ff;

  /* ===== 文字颜色系统 ===== */
  --theme-text-primary: #333;
  --theme-text-secondary: #666;
  --theme-text-muted: #999;
  --theme-text-disabled: #ccc;

  /* ===== 背景颜色系统 ===== */
  --theme-bg-color: #f5f5f5;
  --theme-bg-secondary: #ffffff;
  --theme-card-bg: #fff;
  --theme-light: #f8f9fa;
  --theme-dark: #343a40;
  --theme-white: #ffffff;

  /* ===== 边框和阴影 ===== */
  --theme-border-color: #e0e0e0;
  --theme-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --theme-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --theme-shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);

  /* ===== 布局和交互 ===== */
  --theme-radius: 6px;
  --theme-radius-small: 4px;
  --theme-radius-large: 12px;
  --theme-transition: all 0.3s ease;
  --theme-transition-fast: all 0.2s ease;
  --theme-transition-slow: all 0.5s ease;
}

/* ===== 全局基础样式重置 ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--theme-background);
  color: #333;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100% !important;
}

[v-cloak] {
  display: none !important;
}

.layui-form-label.required:after {
  content: " *";
  color: red;
  position: absolute;
  right: 8px;
  top: 12px;
}

/* ===== 通用组件样式 ===== */
.layui-btn {
  background-color: var(--theme-primary);
}

.layui-border-green {
  border-width: 1px;
  border-style: solid;
  border-color: #fff !important;
  color: #fff !important;
}

.layui-layer-btn .layui-layer-btn0 {
  background-color: var(--theme-primary);
}

.layui-tab-brief > .layui-tab-title .layui-this {
  color: var(--theme-text);
}

.layui-tab-brief > .layui-tab-more li.layui-this:after,
.layui-tab-brief > .layui-tab-title .layui-this:after {
  border-bottom: 2px solid var(--theme-primary);
}

.footer_full .submit_full {
  background-color: var(--theme-primary) !important;
}

.layui-icon-ok-circle {
  color: var(--theme-text) !important;
}

.number_color {
  color: var(--theme-text) !important;
}

.submit {
  background-color: var(--theme-primary) !important;
}

.layui-input:focus,
.layui-textarea:focus {
  border-color: var(--theme-hover) !important;
  box-shadow: 0 0 0 3px var(--theme-shadow);
}

.layui-icon-rate,
.layui-icon-rmb,
.layui-icon-date,
.layui-icon-tips {
  color: #ffffff !important;
}

.card-number-icon {
  color: #ffffff !important;
  font-weight: bold !important;
}

.info-label {
  color: var(--theme-text) !important;
  font-weight: 500 !important;
}

.info-value {
  color: var(--theme-text) !important;
  font-weight: 600 !important;
}

/* ===== 更多通用样式可以继续添加 ===== */
.theme-primary-bg {
  background-color: var(--theme-primary) !important;
}

.theme-primary-text {
  color: var(--theme-primary) !important;
}

.theme-primary-border {
  border-color: var(--theme-primary) !important;
}

.theme-hover:hover {
  background-color: var(--theme-hover) !important;
}

/* ===== 订单详情页主题适配 ===== */

/* 修改按钮边框 */
.layui-border-blue {
  border-color: var(--theme-primary) !important;
  color: var(--theme-primary) !important;
}

/* ===== LayUI日期选择器主题适配 ===== */
/* 使用正确的LayUI类名 */

/* 选中日期背景色 - 使用正确的LayUI类名 */
.layui-laydate .layui-this,
.layui-laydate .layui-this > div {
  background-color: var(--theme-primary) !important;
  color: #fff !important;
}

/* 今天的日期颜色 */
.layui-laydate td.layui-this {
  background-color: var(--theme-primary) !important;
}

/* 头部年月切换按钮悬浮效果 */
.layui-laydate .layui-laydate-header i:hover,
.layui-laydate .layui-laydate-header span:hover {
  color: var(--theme-primary) !important;
}

/* 确定按钮 */
.layui-laydate .layui-laydate-btns span[lay-type="confirm"] {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
}

/* 月份/年份选择悬浮效果 */
.layui-laydate .layui-laydate-list li:hover {
  background-color: var(--theme-primary) !important;
  color: #fff !important;
}

/* 时间选择器 */
.layui-laydate .layui-laydate-time .layui-this {
  background-color: var(--theme-primary) !important;
}

/* ===== Layer弹窗主题适配 ===== */

/* Layer loading图标颜色适配 */
.layui-layer-loading .layui-layer-ico {
  border-color: var(--theme-primary) transparent transparent transparent !important;
}

/* Layer loading1 样式 (默认loading) */
.layui-layer-ico0 {
  border-color: var(--theme-primary) transparent transparent transparent !important;
}

/* Layer loading2 样式 */
.layui-layer-ico1 {
  background-color: var(--theme-primary) !important;
}

/* Layer loading3 样式 */
.layui-layer-ico2 {
  border-color: var(--theme-primary) !important;
}

/* Layer消息框图标颜色 */
.layui-layer-dialog .layui-layer-ico1 {
  background-color: var(--theme-primary) !important;
}

/* Layer确认按钮 */
.layui-layer-btn .layui-layer-btn0 {
  background-color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
}

.layui-layer-btn .layui-layer-btn0:hover {
  background-color: var(--theme-hover) !important;
  border-color: var(--theme-hover) !important;
}

/* Layer取消按钮悬浮效果 */
.layui-layer-btn .layui-layer-btn1:hover {
  color: var(--theme-primary) !important;
  border-color: var(--theme-primary) !important;
}

/* Layer标题栏 */
.layui-layer-title {
  background-color: var(--theme-primary) !important;
  color: #fff !important;
}

/* Layer关闭按钮悬浮效果 */
.layui-layer-setwin .layui-layer-close:hover {
  color: var(--theme-primary) !important;
}

/* ===== 更详细的Layer Loading适配 ===== */

/* Layer loading动画的所有变体 */
.layui-layer-loading .layui-layer-ico,
.layui-layer-loading1 .layui-layer-ico,
.layui-layer-loading2 .layui-layer-ico {
  border-top-color: var(--theme-primary) !important;
  border-left-color: var(--theme-primary) !important;
}

/* Layer loading圆点样式 */
.layui-layer-loading .layui-layer-ico:after,
.layui-layer-loading .layui-layer-ico:before {
  background-color: var(--theme-primary) !important;
}

/* Layer msg类型的loading */
.layui-layer-msg .layui-layer-ico16 {
  background-color: var(--theme-primary) !important;
}

/* 自定义loading动画颜色 */
@keyframes layui-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.layui-layer-loading .layui-layer-ico {
  animation: layui-rotate 1s linear infinite;
  border: 2px solid transparent;
  border-top-color: var(--theme-primary) !important;
  border-radius: 50%;
}

.rich_img {
  max-width: 100% !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block;
}
