/* 商家账号绑定页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
  background: var(--theme-bg-color, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 隐藏未初始化的Vue内容 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: var(--theme-primary-color, #1e9fff);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 10px;
  display: block;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #666;
}

/* 页面容器 */
.page-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2rem;
  color: var(--theme-text-color, #333);
  margin: 0;
  padding: 1rem 0;
  font-weight: 500;
}

/* 表单容器 */
.form-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 表单样式 */
.bind-form {
  max-width: 100%;
}

.layui-form-item {
  margin-bottom: 25px;
}

.layui-form-label {
  width: 100px;
  font-weight: 500;
  color: var(--theme-text-color, #333);
}

.layui-input-block {
  margin-left: 110px;
}

.layui-input {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 14px;
  padding: 10px 15px;
}

.layui-input:focus {
  border-color: var(--theme-primary-color, #1e9fff);
  box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.1);
}

.layui-input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* 绑定按钮 */
.bind-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 4px;
  background: var(--theme-primary-color, #1e9fff);
  border: none;
  transition: all 0.3s ease;
}

.bind-btn:hover:not(:disabled) {
  background: var(--theme-primary-hover-color, #009688);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 159, 255, 0.3);
}

.bind-btn:disabled {
  background: #d2d2d2;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.bind-btn i {
  margin-right: 5px;
}

/* 提示卡片 */
.tips-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.tips-card .layui-card-header {
  background: var(--theme-primary-color, #1e9fff);
  color: #fff;
  font-weight: 500;
  border-radius: 8px 8px 0 0;
}

.tips-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.tips-list li {
  padding: 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.tips-list li:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

/* 页脚 */
.page-footer {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 12px;
  margin-top: auto;
}

.page-footer p {
  margin: 0;
}

/* 响应式设计 */

/* 主题色变量支持 */

/* 深色主题支持 */

/* 动画效果 */
.page-container {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证错误状态 */
.layui-form-danger .layui-input {
  border-color: #ff5722;
}

.layui-form-danger .layui-input:focus {
  box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.1);
}

/* 成功状态 */
.layui-form-pane .layui-input:valid {
  border-color: #4caf50;
}

/* 加载按钮动画 */
.bind-btn:disabled i {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
