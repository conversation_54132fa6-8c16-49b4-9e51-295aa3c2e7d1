/* 二维码绑定页面样式 */

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主题支持的CSS变量 */

/* 暗色主题 */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.qrcode-bind-app {
  min-height: 100vh;
  background-color: var(--theme-bg-color);
  color: var(--theme-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  padding: 20px;
}

/* 表单容器 */
.bind-form-container {
  max-width: 400px;
  margin: 0 auto;
  background-color: var(--theme-bg-secondary);
  border-radius: 12px;
  box-shadow: var(--theme-shadow);
  overflow: hidden;
}

/* 表单头部 */
.form-header {
  padding: 30px 20px 20px;
  text-align: center;
  background: linear-gradient(135deg, var(--theme-primary-color), #ffb366);
  color: white;
}

.form-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.form-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 表单内容区域 */
.form-content {
  padding: 30px 20px;
}

/* 表单组 */
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 8px;
}

.form-label.required::after {
  content: " *";
  color: var(--theme-error-color);
}

/* 表单输入框 */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--theme-border-color);
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text-primary);
  background-color: var(--theme-bg-secondary);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary-color);
  box-shadow: 0 0 0 3px rgba(255, 150, 0, 0.1);
}

.form-input::placeholder {
  color: var(--theme-text-muted);
}

/* 输入框错误状态 */
.form-input.error {
  border-color: var(--theme-error-color);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.1);
}

/* 错误提示 */
.error-message {
  color: var(--theme-error-color);
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, var(--theme-primary-color), #ffb366);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  margin-top: 20px;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 150, 0, 0.3);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  background: var(--theme-text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 加载状态 */
.submit-btn.loading {
  position: relative;
  color: transparent;
}

.submit-btn.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页脚 */
.form-footer {
  padding: 20px;
  text-align: center;
  background-color: var(--theme-bg-color);
  border-top: 1px solid var(--theme-border-color);
}

.footer-text {
  font-size: 12px;
  color: var(--theme-text-muted);
  margin: 0;
}

.footer-link {
  color: var(--theme-primary-color);
  text-decoration: none;
  margin: 0 8px;
}

.footer-link:hover {
  text-decoration: underline;
}

/* 成功状态 */
.success-state {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  font-size: 48px;
  color: var(--theme-success-color);
  margin-bottom: 16px;
}

.success-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 8px;
}

.success-message {
  font-size: 14px;
  color: var(--theme-text-secondary);
  margin-bottom: 20px;
}

.success-btn {
  background-color: var(--theme-success-color);
  border-color: var(--theme-success-color);
}

.success-btn:hover {
  background-color: #45b317;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 响应式设计 */

/* 动画效果 */
.bind-form-container {
  animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证动画 */
.form-input.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 无障碍访问 */
