/* 积分兑换记录页面样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--theme-text);
}

[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding: 20px;
  padding-bottom: 80px;
}

/* 页面头部 */
.page-header {
  background: var(--theme-card-bg);
  border-radius: 20px 20px 0 0;
  padding: 20px;
  margin-bottom: 2px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--theme-secondary);
  font-size: 16px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: var(--theme-bg);
  color: var(--theme-primary);
}

.back-btn i {
  margin-right: 5px;
  font-size: 18px;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--theme-text);
}

.refresh-btn {
  background: none;
  border: none;
  color: var(--theme-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: var(--theme-bg);
  color: var(--theme-primary);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 加载状态 */
.loading-container {
  background: var(--theme-card-bg);
  border-radius: 0 0 20px 20px;
  padding: 60px 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  color: var(--theme-primary);
}

.loading-spinner i {
  font-size: 32px;
  margin-bottom: 15px;
  display: block;
}

.loading-spinner p {
  font-size: 16px;
  color: var(--theme-text-light);
}

/* 记录列表 */
.records-container {
  background: var(--theme-card-bg);
  border-radius: 0 0 20px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.record-card {
  border-bottom: 1px solid var(--theme-border);
  padding: 20px;
  transition: all 0.3s ease;
}

.record-card:last-child {
  border-bottom: none;
}

.record-card:hover {
  background: var(--theme-bg);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.record-info h3 {
  font-size: 16px;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 5px;
}

.record-time {
  font-size: 14px;
  color: var(--theme-text-light);
}

.record-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.status-success {
  color: var(--theme-success);
}

.status-pending {
  color: var(--theme-warning);
}

.status-failed {
  color: var(--theme-danger);
}

.record-body {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-details {
  flex: 1;
}

.detail-label {
  font-size: 14px;
  color: var(--theme-text-light);
  margin-right: 10px;
}

.detail-value {
  font-size: 14px;
  color: var(--theme-text);
}

.record-amount {
  text-align: right;
}

.amount-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--theme-primary);
}

.amount-label {
  font-size: 12px;
  color: var(--theme-text-light);
  margin-top: 2px;
}

/* 空状态 */
.empty-container {
  background: var(--theme-card-bg);
  border-radius: 0 0 20px 20px;
  padding: 80px 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.empty-content {
  max-width: 300px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  color: var(--theme-text-light);
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--theme-text);
  margin-bottom: 10px;
}

.empty-desc {
  font-size: 14px;
  color: var(--theme-text-light);
  margin-bottom: 30px;
  line-height: 1.5;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--theme-border);
  display: flex;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  text-decoration: none;
  color: var(--theme-text-light);
  transition: all 0.3s ease;
}

.nav-item.active {
  color: var(--theme-primary);
}

.nav-item:hover {
  color: var(--theme-primary);
  background: rgba(4, 190, 2, 0.05);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
  display: block;
}

.nav-text {
  font-size: 12px;
  display: block;
}

/* 下拉刷新 */
.pull-refresh {
  position: relative;
  overflow: hidden;
}

.pull-refresh-indicator {
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-card-bg);
  color: var(--theme-text-light);
  font-size: 14px;
  transition: all 0.3s ease;
}

.pull-refresh-indicator.show {
  top: 0;
}

.pull-refresh-indicator i {
  margin-right: 8px;
  font-size: 16px;
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.record-card {
  animation: fadeInUp 0.6s ease-out;
}

.record-card:nth-child(2) { animation-delay: 0.1s; }
.record-card:nth-child(3) { animation-delay: 0.2s; }
.record-card:nth-child(4) { animation-delay: 0.3s; }
.record-card:nth-child(5) { animation-delay: 0.4s; }
