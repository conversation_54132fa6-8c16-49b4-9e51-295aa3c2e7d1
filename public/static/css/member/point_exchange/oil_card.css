/* 积分兑换油卡页面样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 60px;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

/* 输入项 */
.input-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.input-item:last-child {
  border-bottom: none;
}

.input-label {
  width: 80px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #333;
  background: transparent;
}

.input-field:disabled {
  color: var(--theme-primary);
  font-weight: 600;
}

.input-field::placeholder {
  color: #999;
}

/* 金额选择区域 */
.amount-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 15px;
}

.amount-item {
  height: 45px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.amount-item:hover {
  border-color: var(--theme-hover);
  color: var(--theme-hover);
}

.amount-item.selected {
  border-color: var(--theme-primary);
  background: var(--theme-primary);
  color: #ffffff;
  position: relative;
}

.amount-item.selected::after {
  content: "✓";
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  font-weight: bold;
}

/* 充值按钮 */
.submit-section {
  padding: 0 15px;
  margin-top: 20px;
}

.submit-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, var(--theme-primary), var(--theme-primary-light));
  border: none;
  border-radius: 25px;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px var(--theme-shadow);
}

.submit-btn:hover {
  background: linear-gradient(135deg, var(--theme-hover), var(--theme-primary));
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--theme-shadow);
}

.submit-btn:active {
  transform: translateY(0);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 提示信息 */
.tips-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.tips-title {
  font-size: 14px;
  color: #333;
  font-weight: 600;
  margin-bottom: 10px;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-item {
  font-size: 12px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 15px;
  position: relative;
}

.tips-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--theme-primary);
  font-weight: bold;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #ffffff;
  border-top: 1px solid #e5e5e5;
  display: flex;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: #666;
  transition: all 0.3s ease;
}

.nav-item.active {
  color: var(--theme-primary);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 11px;
}

/* 响应式设计 */

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 页面进入动画 */
.page-container {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
