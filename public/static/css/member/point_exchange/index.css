/* 积分兑换页面样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: var(--theme-text);
}

[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding: 20px;
  padding-bottom: 80px;
}

/* 主卡片 */
.exchange-card {
  background: var(--theme-card-bg);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

/* 卡片头部 */
.card-header {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  color: white;
  padding: 30px 20px;
  text-align: center;
}

.card-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.balance-info {
  font-size: 16px;
  opacity: 0.9;
}

.balance-amount {
  font-size: 20px;
  font-weight: bold;
  margin: 0 5px;
}

/* 卡片内容 */
.card-content {
  padding: 30px 20px;
}

/* 金额输入区域 */
.amount-section {
  margin-bottom: 30px;
}

.amount-label {
  font-size: 16px;
  color: var(--theme-text);
  margin-bottom: 15px;
  display: block;
}

.amount-input-wrapper {
  position: relative;
  background: var(--theme-bg);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.amount-input-wrapper:focus-within {
  border-color: var(--theme-primary);
  background: white;
  box-shadow: 0 0 0 3px rgba(4, 190, 2, 0.1);
}

.currency-symbol {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: var(--theme-secondary);
  font-weight: bold;
}

.amount-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 28px;
  font-weight: bold;
  color: var(--theme-text);
  padding-left: 40px;
  outline: none;
}

.amount-input::placeholder {
  color: var(--theme-text-light);
  font-weight: normal;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.quick-btn {
  flex: 1;
  padding: 10px;
  border: 1px solid var(--theme-border);
  background: white;
  border-radius: 8px;
  font-size: 14px;
  color: var(--theme-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  background: rgba(4, 190, 2, 0.05);
}

/* 提现方式选择 */
.exchange-type-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  color: var(--theme-text);
  margin-bottom: 15px;
  display: block;
}

.type-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.type-option {
  display: flex;
  align-items: center;
  padding: 15px;
  background: var(--theme-bg);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.type-option:hover {
  background: white;
  border-color: var(--theme-primary);
}

.type-option.selected {
  background: rgba(4, 190, 2, 0.1);
  border-color: var(--theme-primary);
}

.type-option input[type="radio"] {
  margin-right: 12px;
  transform: scale(1.2);
}

.type-option-label {
  font-size: 16px;
  color: var(--theme-text);
}

/* 提交按钮 */
.submit-section {
  text-align: center;
}

.submit-btn {
  width: 100%;
  padding: 18px;
  background: var(--theme-primary-light);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 10px;
}

.submit-btn:hover {
  background: var(--theme-primary);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(4, 190, 2, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-tips {
  font-size: 14px;
  color: var(--theme-text-light);
  margin-top: 10px;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid var(--theme-border);
  display: flex;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  text-decoration: none;
  color: var(--theme-text-light);
  transition: all 0.3s ease;
}

.nav-item.active {
  color: var(--theme-primary);
}

.nav-item:hover {
  color: var(--theme-primary);
  background: rgba(4, 190, 2, 0.05);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
  display: block;
}

.nav-text {
  font-size: 12px;
  display: block;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
}

.loading-spinner {
  font-size: 24px;
  color: var(--theme-primary);
  margin-bottom: 10px;
}

.loading-text {
  font-size: 16px;
  color: var(--theme-text);
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.exchange-card {
  animation: fadeInUp 0.6s ease-out;
}

/* 输入验证状态 */
.amount-input-wrapper.error {
  border-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.amount-input-wrapper.success {
  border-color: var(--theme-primary);
  background: rgba(4, 190, 2, 0.05);
}

/* 错误提示 */
.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
  display: none;
}

.error-message.show {
  display: block;
}
