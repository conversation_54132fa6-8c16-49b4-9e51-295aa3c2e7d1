/* 商品溯源页面样式 */
[v-cloak] {
  display: none !important;
}

.goods-trace-container {
  min-height: 100vh;
  background: #f4f4f4;
  padding-bottom: 120px;
}

/* 商品内容区域 */
.goods-content {
  background: white;
}

/* 图片轮播区域 */
.image-carousel {
  position: relative;
  width: 100%;
  background: white;
}

.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-wrapper {
  display: flex;
  transition: transform 0.3s ease;
  width: 100%;
}

.carousel-slide {
  flex: 0 0 100%;
  width: 100%;
}

.product-image {
  width: 100%;
  height: auto;
  display: block;
  object-fit: cover;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

/* 轮播控制按钮 */
.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  pointer-events: none;
  z-index: 10;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.control-btn i {
  font-size: 18px;
}

/* 商品信息卡片 */
.goods-info-card {
  background: white;
  margin: 8px 4px;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.goods-title {
  font-size: 18px;
  color: #02a066;
  margin: 0 0 15px 0;
  line-height: 1.4;
  font-weight: 500;
}

.price-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label {
  color: #a6a6a6;
  font-size: 14px;
}

.price-value {
  color: #e21323;
  font-size: 16px;
  font-weight: 500;
  font-family: Arial, Helvetica, sans-serif;
}

/* 商品描述 */
.goods-description {
  background: white;
  margin: 8px 4px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.description-content {
  padding: 15px;
  line-height: 1.6;
  color: #333;
}

.description-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 10px 0;
}

.description-content p {
  margin: 0 0 15px 0;
}

.description-content h1,
.description-content h2,
.description-content h3,
.description-content h4,
.description-content h5,
.description-content h6 {
  margin: 20px 0 15px 0;
  color: #333;
  font-weight: 500;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 操作按钮 */
.action-buttons {
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.action-btn {
  width: 100%;
  height: 45px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-btn {
  background: var(--theme-primary, #1890ff);
  color: white;
}

.primary-btn:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

.primary-btn:active {
  transform: translateY(0);
}

/* 小程序跳转按钮 */
.miniprogram-launch {
  position: relative;
  padding: 10px 15px;
}

.launch-button {
  width: 100%;
  height: 45px;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.launch-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.launch-button:active {
  transform: translateY(0);
}

.launch-button i {
  font-size: 18px;
}

/* 微信小程序跳转组件 */
.weapp-launcher {
  position: absolute;
  top: 10px;
  left: 15px;
  right: 15px;
  bottom: 10px;
  border: none;
  background: transparent;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 10px;
  display: block;
  color: var(--theme-primary, #1890ff);
}

.loading-content p {
  margin: 0;
  font-size: 14px;
}

/* 错误状态 */
.error-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  color: #666;
  padding: 20px;
}

.error-content i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
  color: #ff4d4f;
}

.error-content p {
  margin: 0 0 20px 0;
  font-size: 16px;
  line-height: 1.5;
}

.retry-btn {
  background: var(--theme-primary, #1890ff);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: var(--theme-primary-hover, #40a9ff);
}

/* 响应式设计 */

/* 图片懒加载样式 */
.product-image[data-src] {
  background: #f5f5f5 url('/static/img/loading.gif') center center no-repeat;
  background-size: 40px 40px;
}

/* 富文本图片懒加载 */
.description-content img[data-src] {
  background: #f5f5f5 url('/static/img/loading.gif') center center no-repeat;
  background-size: 30px 30px;
  min-height: 100px;
}

/* 主题色变量支持 */

/* 深色主题支持 */
