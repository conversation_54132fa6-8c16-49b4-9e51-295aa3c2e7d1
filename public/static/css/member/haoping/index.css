/* 好评页面样式 */
[v-cloak] {
  display: none !important;
}

.evaluation-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 关注提示 */
.concern-tip {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.concern-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
}

.concern-content {
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
  font-size: 14px;
}

.rule-button,
.concern-button {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rule-button:hover,
.concern-button:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

/* 横幅区域 */
.banner-section {
  padding: 20px;
  text-align: center;
}

.banner-image {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 主要内容 */
.main-content {
  background: white;
  margin: 0 15px;
  border-radius: 15px 15px 0 0;
  padding: 30px 20px;
  min-height: calc(100vh - 200px);
  position: relative;
}

.banner-decoration {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #ddd;
  border-radius: 2px;
}

/* 图片上传区域 */
.image-upload-section {
  margin-bottom: 30px;
}

.upload-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.upload-item {
  width: 80px;
  height: 80px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.upload-item:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 5px;
}

.upload-text {
  font-size: 12px;
  color: #666;
}

.uploaded-image {
  position: relative;
  width: 80px;
  height: 80px;
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.remove-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

/* 表单样式 */
.evaluation-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  position: relative;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 10px;
  padding: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  cursor: pointer;
}

.input-wrapper:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.form-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;
  outline: none;
}

.form-input::placeholder {
  color: #999;
}

.input-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 18px;
}

.shop-name {
  color: #333;
  font-size: 16px;
}

.placeholder {
  color: #999;
  font-size: 16px;
}

/* 验证码组 */
.verify-group .input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.verify-input {
  flex: 1;
}

.verify-image {
  width: 80px;
  height: 40px;
  border-radius: 5px;
  cursor: pointer;
  border: 1px solid #ddd;
}

/* 提交按钮 */
.submit-section {
  margin-top: 30px;
}

.submit-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 版权区域 */
.copyright-section {
  padding: 30px 20px;
  text-align: center;
}

.protocol-section {
  margin-bottom: 20px;
}

.protocol-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.protocol-checkbox {
  margin-right: 5px;
}

.protocol-link {
  background: none;
  border: none;
  color: #4fc3f7;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
}

.copyright-content {
  color: rgba(255, 255, 255, 0.6);
}

.decoration-line {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.line {
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.line.small {
  width: 20px;
}
.line.medium {
  width: 30px;
}
.line.large {
  width: 40px;
}

.support-text {
  font-size: 12px;
  padding: 0 10px;
}

/* 规则弹窗 */
.rules-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.rules-content p {
  margin: 0 0 15px 0;
  line-height: 1.6;
  color: #666;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 10px;
  display: block;
}

/* 响应式设计 */
