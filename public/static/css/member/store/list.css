/* 店铺列表页面样式 */
@charset "UTF-8";

/* CSS Reset */
body, div, span, h1, h2, h3, h4, h5, h6, header, footer, section, article, aside, details, figcaption, figure, hgroup, nav, menu, address, time, canvas, audio, video, p, pre, sup, sub, ul, ol, li, dl, dt, dd, form, input, button, textarea, select, iframe, img, a {
  padding: 0;
  margin: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

header, footer, section, article, aside, details, figcaption, figure, hgroup, nav, menu {
  display: block;
}

html, body {
  font-size: 20px;
  -webkit-overflow-scrolling: touch;
}

body {
  background-color: var(--theme-bg-color, #eee);
  color: var(--theme-text-primary, #333);
  font-family: "Helvetica Neue", "Microsoft Yahei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Droid Sans Fallback", "Heiti SC", sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
}

ul, ol {
  list-style-type: none;
}

a {
  color: var(--theme-text-primary, #333);
  text-decoration: none;
  outline: none;
}

a:link, a:visited, a:hover, a:active {
  color: var(--theme-text-primary, #333);
  outline: none;
}

/* 清除浮动 */
.clearfix:after {
  clear: both;
  content: "";
  display: table;
}

/* 头部样式 */
.wap-header {
  position: relative;
  height: 44px;
  line-height: 44px;
  background-color: var(--theme-primary-color, #ff9600);
  color: var(--theme-primary-text, #fff);
  text-align: center;
  font-size: 18px;
}

.wap-header-back {
  position: absolute;
  left: 10px;
  top: 0;
  color: var(--theme-primary-text, #fff);
  font-size: 14px;
  text-decoration: none;
}

.wap-header-l {
  left: 10px;
}

.wap-search {
  position: relative;
  padding: 0 60px;
}

.wap-search input {
  width: 100%;
  height: 30px;
  line-height: 30px;
  border: none;
  border-radius: 15px;
  padding: 0 15px;
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--theme-primary-text, #fff);
}

.wap-search input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.wap-search-btn {
  position: absolute;
  right: 10px;
  top: 7px;
  width: 30px;
  height: 30px;
  border: none;
  background: transparent;
  color: var(--theme-primary-text, #fff);
  font-size: 14px;
  cursor: pointer;
}

/* 筛选面板样式 */
.filtrate {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.filtrate-main {
  position: absolute;
  left: 0;
  top: 0;
  width: 80%;
  height: 100%;
  background-color: var(--theme-bg-color, #fff);
  overflow-y: auto;
}

.filtrate-header {
  height: 44px;
  line-height: 44px;
  background-color: var(--theme-primary-color, #ff9600);
  color: var(--theme-primary-text, #fff);
  text-align: center;
  font-size: 16px;
  position: relative;
}

.filtrate-close {
  position: absolute;
  right: 15px;
  top: 0;
  color: var(--theme-primary-text, #fff);
  font-size: 14px;
  cursor: pointer;
}

.filtrate-content {
  padding: 20px;
}

.filtrate-item {
  margin-bottom: 20px;
}

.filtrate-item h3 {
  font-size: 16px;
  color: var(--theme-text-primary, #333);
  margin-bottom: 10px;
  font-weight: bold;
}

.filtrate-item ul {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filtrate-item li {
  padding: 8px 15px;
  border: 1px solid var(--theme-border, #ddd);
  border-radius: 20px;
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  cursor: pointer;
  transition: all 0.3s ease;
}

.filtrate-item li:hover,
.filtrate-item li.active {
  background-color: var(--theme-primary-color, #ff9600);
  color: var(--theme-primary-text, #fff);
  border-color: var(--theme-primary-color, #ff9600);
}

.filtrate-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--theme-bg-color, #fff);
  border-top: 1px solid var(--theme-border, #ddd);
}

.filtrate-btn {
  display: flex;
  height: 50px;
}

.filtrate-btn li {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filtrate-btn input {
  width: 90%;
  height: 36px;
  border: none;
  border-radius: 18px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

input.filtrate-reset {
  background-color: var(--theme-bg-secondary, #f5f5f5);
  color: var(--theme-text-secondary, #666);
  border: 1px solid var(--theme-border, #ddd);
}

input.filtrate-submit {
  border: 1px solid var(--theme-primary-color, #ff9600);
  background-color: var(--theme-primary-color, #ff9600);
  color: var(--theme-primary-text, #fff);
}

/* 店铺列表样式 */
.store-list-container {
  padding: 10px;
  background-color: var(--theme-bg-color, #eee);
  min-height: calc(100vh - 44px);
}

.store-item {
  background-color: var(--theme-card-bg, #fff);
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 15px;
  transition: all 0.3s ease;
}

.store-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.store-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.store-info {
  flex: 1;
  min-width: 0;
}

.store-name {
  font-size: 16px;
  font-weight: bold;
  color: var(--theme-text-primary, #333);
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-detail {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  margin-bottom: 4px;
}

.store-distance {
  color: var(--theme-primary-color, #ff9600);
  font-weight: bold;
}

.store-address {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 搜索栏样式 */
.search-container {
  padding: 10px;
  background-color: var(--theme-card-bg, #fff);
  border-bottom: 1px solid var(--theme-border, #eee);
}

.search-input {
  width: 100%;
  height: 36px;
  border: 1px solid var(--theme-border, #ddd);
  border-radius: 18px;
  padding: 0 15px;
  font-size: 14px;
  background-color: var(--theme-input-bg, #f5f5f5);
  color: var(--theme-text-primary, #333);
}

.search-input:focus {
  outline: none;
  border-color: var(--theme-primary-color, #ff9600);
  background-color: var(--theme-card-bg, #fff);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #999);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 20px;
  color: var(--theme-text-secondary, #999);
}

/* 暗色主题支持 */

/* 响应式设计 */
