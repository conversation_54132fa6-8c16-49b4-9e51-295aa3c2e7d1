/* 店铺详情页面样式 */

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主题支持的CSS变量 */

/* 暗色主题 */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.store-detail-app {
  min-height: 100vh;
  background-color: var(--theme-bg-color);
  color: var(--theme-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 店铺详情容器 */
.store-detail-container {
  max-width: 100%;
  margin: 0 auto;
  background-color: var(--theme-bg-secondary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--theme-shadow);
}

/* 店铺图片 */
.store-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
  background-color: var(--theme-border-color);
}

.store-image.error {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f0f0f0'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='0.3em' font-family='Arial' font-size='12' fill='%23999'%3E暂无图片%3C/text%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100px 100px;
}

/* 店铺信息区域 */
.store-info {
  padding: 20px;
}

.store-name {
  font-size: 20px;
  font-weight: bold;
  color: var(--theme-text-primary);
  margin-bottom: 15px;
  line-height: 1.4;
}

.store-detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--theme-border-color);
}

.store-detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.store-detail-label {
  font-size: 14px;
  color: var(--theme-text-secondary);
  min-width: 60px;
  margin-right: 10px;
  flex-shrink: 0;
}

.store-detail-value {
  font-size: 14px;
  color: var(--theme-text-primary);
  flex: 1;
  line-height: 1.5;
}

/* 电话号码特殊样式 */
.store-phone {
  color: var(--theme-primary-color);
  text-decoration: none;
}

.store-phone:hover {
  text-decoration: underline;
}

/* 地址信息 */
.store-address {
  word-break: break-all;
  line-height: 1.6;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-state .layui-icon {
  font-size: 32px;
  color: var(--theme-primary-color);
  margin-bottom: 10px;
}

.loading-state p {
  color: var(--theme-text-secondary);
  font-size: 14px;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-state .layui-icon {
  font-size: 48px;
  color: var(--theme-text-muted);
  margin-bottom: 15px;
}

.error-state p {
  color: var(--theme-text-secondary);
  font-size: 16px;
  margin-bottom: 20px;
}

.error-state .layui-btn {
  background-color: var(--theme-primary-color);
  border-color: var(--theme-primary-color);
}

/* 操作按钮区域 */
.store-actions {
  padding: 20px;
  border-top: 1px solid var(--theme-border-color);
  background-color: var(--theme-bg-color);
}

.action-btn {
  width: 100%;
  padding: 12px;
  background-color: var(--theme-primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-btn:hover {
  background-color: #e6850a;
}

.action-btn:active {
  background-color: #cc7700;
}

/* 响应式设计 */

/* 动画效果 */
.store-detail-container {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图片加载动画 */
.store-image {
  transition: opacity 0.3s ease;
}

.store-image.loading {
  opacity: 0.7;
}

/* 无障碍访问 */
