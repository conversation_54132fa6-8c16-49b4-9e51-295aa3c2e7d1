/* 推荐记录页面样式 */
[v-cloak] {
  display: none !important;
}

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 20px;
}

/* 记录列表 */
.record-list {
  margin: 15px;
}

.record-item {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-left {
  flex: 1;
}

.record-date {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.record-member {
  font-size: 14px;
  color: #999;
}

.record-right {
  flex-shrink: 0;
  text-align: right;
}

.record-reward {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-primary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #999;
}

.empty-icon {
  font-size: 60px;
  color: #ddd;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 16px;
  line-height: 1.5;
}

/* 下拉刷新 */
.refresh-container {
  position: relative;
  overflow: hidden;
}

.refresh-indicator {
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.refresh-indicator.pulling {
  transform: translateY(60px);
}

.refresh-indicator .loading {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-top-color: var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

.load-more.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.load-more .loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-top-color: var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
