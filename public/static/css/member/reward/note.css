/* 奖励记录页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 奖励记录区域 */
.reward-section {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
  background: var(--theme-background-light, #fafafa);
}

.section-title {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  font-size: 16px;
  color: var(--theme-success, #52c41a);
}

/* 奖励记录列表 */
.reward-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.reward-item {
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border-light, #f0f0f0);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.reward-item:hover {
  background: var(--theme-hover, #f8f9fa);
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reward-info {
  flex: 1;
  min-width: 0;
}

.reward-date {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reward-store {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reward-time {
  font-size: 12px;
  color: var(--theme-text-placeholder, #999);
}

.reward-amount {
  text-align: right;
  flex-shrink: 0;
  margin-left: 12px;
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--theme-success, #52c41a);
  margin-bottom: 2px;
}

.reward-badge {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme-text-placeholder, #ccc);
  margin-bottom: 16px;
  display: block;
}

.empty-state h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--theme-text-secondary, #666);
}

.empty-state p {
  font-size: 14px;
  color: var(--theme-text-placeholder, #999);
  margin: 0;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.reward-item:active {
  background: var(--theme-active, #e6f7ff);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border, #e5e5e5);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 奖励特色样式 */
.reward-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #52c41a, #73d13d);
  border-radius: 50%;
  margin-right: 6px;
}

/* 日期标签样式 */
.date-badge {
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
  color: var(--theme-primary, #1890ff);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  margin-bottom: 4px;
}

/* 商店名称样式 */
.store-name {
  font-weight: 500;
  color: var(--theme-text, #333);
}

/* 奖励金额突出显示 */
.reward-highlight {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* 列表项悬停效果 */
.reward-item:hover .reward-date {
  color: var(--theme-primary, #1890ff);
}

.reward-item:hover .amount-value {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 响应式网格布局 */

/* 滚动优化 */
.reward-list {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.reward-list::-webkit-scrollbar {
  width: 4px;
}

.reward-list::-webkit-scrollbar-track {
  background: var(--theme-background-light, #f5f5f5);
}

.reward-list::-webkit-scrollbar-thumb {
  background: var(--theme-border, #e5e5e5);
  border-radius: 2px;
}

.reward-list::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-placeholder, #ccc);
}

/* 数据统计样式 */
.stats-summary {
  padding: 16px 20px;
  background: var(--theme-background-light, #fafafa);
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-label {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 4px;
}

.stats-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-success, #52c41a);
}

/* 刷新按钮 */
.refresh-button {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: var(--theme-primary, #1890ff);
  border: none;
  border-radius: 50%;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px var(--theme-shadow, rgba(24, 144, 255, 0.3));
  transition: all 0.3s ease;
  z-index: 1000;
}

.refresh-button:hover {
  background: var(--theme-primary-dark, #0050b3);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px var(--theme-shadow-hover, rgba(24, 144, 255, 0.4));
}

.refresh-button:active {
  transform: translateY(0);
}

.refresh-button.loading {
  pointer-events: none;
}

.refresh-button.loading i {
  animation: spin 1s linear infinite;
}
