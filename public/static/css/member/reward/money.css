/* 奖励金额页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 会员信息卡片 */
.member-card {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.member-profile {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
}

.member-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  margin-right: 16px;
  flex-shrink: 0;
  overflow: hidden;
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-avatar i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
  line-height: 1.4;
}

.member-level {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-card {
  background: linear-gradient(135deg, var(--theme-primary, #1890ff), var(--theme-primary-dark, #0050b3));
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  text-decoration: none;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--theme-shadow-hover, rgba(24, 144, 255, 0.3));
}

.stat-card.token {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
}

.stat-card.contribution {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-card.balance {
  background: linear-gradient(135deg, #faad14, #ffc53d);
}

.stat-title {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Token币明细区域 */
.token-section {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
  background: var(--theme-background-light, #fafafa);
}

.section-title {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  font-size: 16px;
  color: var(--theme-primary, #1890ff);
}

/* 交易记录列表 */
.transaction-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.transaction-item {
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme-border-light, #f0f0f0);
  transition: background-color 0.3s ease;
}

.transaction-item:hover {
  background: var(--theme-hover, #f8f9fa);
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.transaction-info {
  flex: 1;
  min-width: 0;
}

.transaction-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.transaction-time {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

.transaction-amount {
  text-align: right;
  flex-shrink: 0;
  margin-left: 12px;
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 2px;
}

.amount-value.positive {
  color: var(--theme-success, #52c41a);
}

.amount-value.negative {
  color: var(--theme-error, #ff4d4f);
}

.amount-value.neutral {
  color: var(--theme-text, #333);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme-text-placeholder, #ccc);
  margin-bottom: 16px;
  display: block;
}

.empty-state h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--theme-text-secondary, #666);
}

.empty-state p {
  font-size: 14px;
  color: var(--theme-text-placeholder, #999);
  margin: 0;
}

/* 确认对话框样式 */
.confirm-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.confirm-content {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin: 20px;
  max-width: 320px;
  width: 100%;
  text-align: center;
}

.confirm-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 16px;
}

.confirm-buttons {
  display: flex;
  gap: 12px;
}

.confirm-button {
  flex: 1;
  height: 40px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  background: #fff;
  color: var(--theme-text, #333);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-button.primary {
  background: var(--theme-primary, #1890ff);
  border-color: var(--theme-primary, #1890ff);
  color: #fff;
}

.confirm-button:hover {
  border-color: var(--theme-primary, #1890ff);
  color: var(--theme-primary, #1890ff);
}

.confirm-button.primary:hover {
  background: var(--theme-primary-dark, #0050b3);
  border-color: var(--theme-primary-dark, #0050b3);
  color: #fff;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.stat-card:active {
  transform: scale(0.98);
}

.transaction-item:active {
  background: var(--theme-active, #e6f7ff);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border, #e5e5e5);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 奖励特色样式 */
.reward-badge {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.token-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  border-radius: 50%;
  margin-right: 6px;
}
