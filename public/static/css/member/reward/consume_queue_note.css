/* 奖励消费队列记录页面样式 */

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主题支持的CSS变量 */

/* 暗色主题 */

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.reward-queue-app {
  min-height: 100vh;
  background-color: var(--theme-bg-color);
  color: var(--theme-text-primary);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 会员信息卡片 */
.member-card {
  margin: 10px;
  background: var(--theme-gradient-primary);
  border-radius: 10px;
  padding: 16px;
  color: white;
  box-shadow: var(--theme-shadow);
}

.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  margin-right: 12px;
  object-fit: cover;
  background-color: rgba(255, 255, 255, 0.2);
}

.member-details h3 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.member-details p {
  font-size: 13px;
  opacity: 0.9;
}

/* 统计卡片区域 */
.stats-container {
  padding: 0 10px;
  margin-bottom: 20px;
}

.stats-grid {
  display: flex;
  gap: 8px;
  height: 80px;
}

.stat-item {
  flex: 1;
  border-radius: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--theme-text-primary);
  text-decoration: none;
  transition: transform 0.2s ease;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-item:active {
  transform: translateY(0);
}

.stat-item.ranking {
  background-image: url('/static/img/1.png');
  margin-left: -5px;
  margin-right: 10px;
}

.stat-item.pending {
  background-image: url('/static/img/2.png');
  margin-right: 10px;
}

.stat-item.advance {
  background-image: url('/static/img/3.png');
  margin-right: 10px;
}

.stat-item.help {
  background-image: url('/static/img/4.png');
  margin-right: -5px;
}

.stat-title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 12px;
  color: var(--theme-text-muted);
}

.stat-highlight {
  color: var(--theme-error-color);
  font-weight: bold;
}

/* 列表容器 */
.list-container {
  margin: 0 10px 10px;
  background-color: var(--theme-bg-secondary);
  border-radius: 10px;
  box-shadow: var(--theme-shadow);
  overflow: hidden;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid var(--theme-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-title {
  font-size: 16px;
  font-weight: 500;
}

.list-title.completed {
  color: var(--theme-success-color);
}

.list-title.pending {
  color: var(--theme-warning-color);
}

.list-subtitle {
  color: var(--theme-text-muted);
  font-size: 12px;
  font-weight: normal;
}

/* 滚动容器 */
.scroll-container {
  overflow: hidden;
  position: relative;
}

.scroll-container.completed {
  height: 320px;
}

.scroll-container.pending {
  height: auto;
  max-height: 400px;
}

.scroll-content {
  transition: transform 0.05s linear;
}

/* 列表项 */
.reward-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme-border-color);
  min-height: 60px;
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-rank {
  font-size: 12px;
  font-weight: bold;
  min-width: 30px;
  text-align: right;
  margin-right: 12px;
}

.reward-rank.completed {
  color: var(--theme-success-color);
}

.reward-rank.pending {
  color: var(--theme-warning-color);
  font-size: 14px;
}

.reward-avatar {
  width: 20px;
  height: 20px;
  border-radius: 20px;
  margin-right: 12px;
  object-fit: cover;
  background-color: var(--theme-border-color);
}

.reward-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reward-name {
  font-size: 12px;
  color: var(--theme-text-primary);
}

.reward-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.reward-amount {
  color: #ff8527;
  font-size: 12px;
  font-weight: 500;
}

.reward-amount.pending {
  font-size: 14px;
}

.reward-type {
  font-size: 12px;
}

.reward-type.advance {
  color: var(--theme-error-color);
}

.reward-type.system {
  color: var(--theme-text-muted);
}

.reward-time {
  color: var(--theme-text-muted);
  font-size: 12px;
  margin-left: 8px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-state .layui-icon {
  font-size: 32px;
  color: var(--theme-primary-color);
  margin-bottom: 10px;
}

.loading-state p {
  color: var(--theme-text-secondary);
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-state .layui-icon {
  font-size: 64px;
  color: var(--theme-text-muted);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 18px;
  color: var(--theme-text-muted);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: var(--theme-text-muted);
}

/* 响应式设计 */

/* 动画效果 */
.member-card, .list-container {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动动画 */
@keyframes scrollUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}

.scroll-animation {
  animation: scrollUp 10s linear infinite;
}

/* 无障碍访问 */
