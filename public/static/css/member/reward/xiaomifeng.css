/* 小蜜蜂Token币兑换页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 页面标题 */
.page-title {
  text-align: center;
  padding: 20px;
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.page-title h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.page-title i {
  font-size: 24px;
  color: #ff6037;
}

/* 兑换表单区域 */
.exchange-form {
  background: #fff;
  margin: 16px;
  border-radius: 16px;
  padding: 28px 20px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 表单项 */
.form-item {
  margin-bottom: 24px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin-bottom: 8px;
  display: block;
}

.form-input {
  width: 100%;
  height: 48px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text, #333);
  background: var(--theme-background-light, #fafafa);
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  background: #fff;
  box-shadow: 0 0 0 2px var(--theme-primary-light, rgba(24, 144, 255, 0.2));
}

.form-input:disabled {
  background: var(--theme-background-disabled, #f5f5f5);
  color: var(--theme-text-disabled, #999);
  cursor: not-allowed;
}

.form-input.error {
  border-color: var(--theme-error, #ff4d4f);
  background: var(--theme-error-light, rgba(255, 77, 79, 0.1));
}

/* 选择框 */
.form-select {
  position: relative;
}

.form-select select {
  width: 100%;
  height: 48px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text, #333);
  background: var(--theme-background-light, #fafafa);
  appearance: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-select select:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  background: #fff;
  box-shadow: 0 0 0 2px var(--theme-primary-light, rgba(24, 144, 255, 0.2));
}

.form-select::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--theme-text-secondary, #666);
  pointer-events: none;
}

/* 余额显示特殊样式 */
.balance-input {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
  font-weight: 600;
  text-align: center;
  border: none;
}

.balance-input::placeholder {
  color: rgba(255, 255, 255, 0.8);
}

/* 兑换按钮 */
.exchange-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  border: none;
  border-radius: 8px;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.exchange-button:hover {
  background: linear-gradient(135deg, #e55a32, #ff7a56);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 96, 55, 0.3);
}

.exchange-button:active {
  transform: translateY(0);
}

.exchange-button:disabled {
  background: var(--theme-background-disabled, #f5f5f5);
  color: var(--theme-text-disabled, #999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.exchange-button.loading {
  pointer-events: none;
  position: relative;
}

.exchange-button.loading::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 错误提示 */
.error-message {
  color: var(--theme-error, #ff4d4f);
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message i {
  font-size: 14px;
}

/* 提示信息 */
.info-box {
  background: var(--theme-info-light, rgba(24, 144, 255, 0.1));
  border: 1px solid var(--theme-info, #1890ff);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
}

.info-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-icon {
  font-size: 16px;
  color: var(--theme-info, #1890ff);
  margin-top: 2px;
  flex-shrink: 0;
}

.info-text {
  font-size: 14px;
  color: var(--theme-info, #1890ff);
  line-height: 1.5;
}

/* 小蜜蜂主题色彩 */
.xiaomifeng-theme {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
}

.xiaomifeng-icon {
  color: #ff6037;
}

/* 币种标签 */
.coin-badge {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  margin-left: 8px;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.form-input:active,
.form-select select:active {
  transform: scale(0.99);
}

.exchange-button:active {
  transform: scale(0.98);
}

/* 表单验证状态 */
.form-item.success .form-input {
  border-color: var(--theme-success, #52c41a);
  background: var(--theme-success-light, rgba(82, 196, 26, 0.1));
}

.form-item.success .form-label::after {
  content: '✓';
  color: var(--theme-success, #52c41a);
  margin-left: 8px;
  font-weight: bold;
}

/* 数字输入框特殊样式 */
.number-input {
  text-align: right;
  font-weight: 600;
  color: var(--theme-primary, #1890ff);
}

.number-input:focus {
  color: var(--theme-text, #333);
}

/* 兑换说明 */
.exchange-note {
  background: var(--theme-warning-light, rgba(250, 173, 20, 0.1));
  border: 1px solid var(--theme-warning, #faad14);
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 16px;
}

.exchange-note-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.exchange-note-icon {
  font-size: 16px;
  color: var(--theme-warning, #faad14);
  margin-top: 2px;
  flex-shrink: 0;
}

.exchange-note-text {
  font-size: 12px;
  color: var(--theme-warning-dark, #d48806);
  line-height: 1.5;
}

/* 成功状态样式 */
.success-state {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  font-size: 48px;
  color: var(--theme-success, #52c41a);
  margin-bottom: 16px;
}

.success-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 8px;
}

.success-message {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 24px;
}

.success-button {
  background: var(--theme-success, #52c41a);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-button:hover {
  background: var(--theme-success-dark, #389e0d);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}
