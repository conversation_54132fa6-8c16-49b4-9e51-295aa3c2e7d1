/**
 * WeUI到LayUI重构 - passport/user_login.html 样式文件
 * 用户登录页面样式
 */

/* Vue.js隐藏未渲染内容 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.login-container {
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

/* 登录表单 */
.login-form {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 横幅区域 */
.banner-section {
  text-align: center;
  padding: 0;
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
}

/* 表单区域 */
.form-section {
  padding: 30px 20px;
}

.form-group {
  margin-bottom: 20px;
}

/* 输入组 */
.input-group {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.verification-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

/* 表单输入 */
.form-input {
  width: 100%;
  height: 48px;
  padding: 0 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px; /* 防止iOS自动缩放 */
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
  -webkit-appearance: none; /* 移除iOS默认样式 */
  -webkit-tap-highlight-color: transparent; /* 移除点击高亮 */
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-input::placeholder {
  color: #999;
}

/* 验证码输入 */
.verification-input {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

/* 验证码按钮 */
.verification-button {
  height: 48px;
  padding: 0 16px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 90px;
  flex-shrink: 0; /* 防止按钮被压缩 */
  text-align: center;
}

.verification-button:hover:not(:disabled) {
  background: var(--theme-primary-hover, #40a9ff);
}

.verification-button:active:not(:disabled) {
  background: var(--theme-primary-active, #096dd9);
  transform: scale(0.98);
}

.verification-button:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

/* 触摸设备优化 */

/* 按钮区域 */
.button-section {
  margin-top: 30px;
}

/* 登录按钮 */
.login-button {
  width: 100%;
  height: 50px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.login-button:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 版权区域 */
.copyright-section {
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

/* 成功提示 */
.success-toast {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.toast-content {
  position: relative;
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 200px;
}

.success-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 15px;
}

.toast-text {
  font-size: 16px;
  color: #333;
  margin: 0;
}

/* 手机端优化设计 - 专注移动端体验 */

/* 超小屏幕优化 (iPhone SE等) */

/* 横屏模式优化 */

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-form {
  animation: fadeIn 0.5s ease-out;
}

/* LayUI图标动画 */
.layui-anim-rotate {
  animation: layui-rotate 2s linear infinite;
}

@keyframes layui-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 主题适配 */
