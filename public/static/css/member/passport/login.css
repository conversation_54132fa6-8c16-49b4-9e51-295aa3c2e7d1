/* 会员登录页面样式 */

/* 深色模式支持 */

/* 全局样式 */
* {
  box-sizing: border-box;
}

#LAY_app,
body,
html {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

html {
  background-color: #f2f2f2;
  color: var(--theme-text-light);
}

/* Vue应用容器 */
#app {
  height: 100vh;
  background-color: #f2f2f2;
}

[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f2f2f2;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-light);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 15px;
  color: var(--theme-primary);
}

.loading-content h4 {
  margin: 0;
  font-weight: 400;
  font-size: 16px;
}

/* LayUI布局样式 */
.layui-layout-body {
  overflow: auto;
}

#LAY-user-login,
.layadmin-user-display-show {
  display: block !important;
}

/* 登录容器 */
.layadmin-user-login {
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  box-sizing: border-box;
  top: 50%;
  left: 50%;
  margin-left: -187.5px;
  margin-top: -200px;
  animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%) translateY(30px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) translateY(0);
  }
}

/* 登录主体 */
.layadmin-user-login-main {
  width: 375px;
  margin: 0 auto;
  box-sizing: border-box;
  border: 1px solid #dddddd;
  background: var(--theme-white);
  padding: 20px;
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
  transition: var(--theme-transition);
}

.layadmin-user-login-main:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.layadmin-user-login-box {
  padding: 20px;
}

/* 登录头部 */
.layadmin-user-login-header {
  text-align: center;
  margin-bottom: 20px;
}

.layadmin-user-login-header h2 {
  margin-bottom: 10px;
  font-weight: 300;
  font-size: 30px;
  color: var(--theme-text);
  animation: slideInDown 0.6s ease 0.2s both;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.layadmin-user-login-header p {
  font-weight: 300;
  color: var(--theme-text-light);
}

/* 表单样式 */
.layadmin-user-login-body .layui-form-item {
  position: relative;
  margin-bottom: 20px;
  animation: slideInLeft 0.6s ease calc(0.1s * var(--item-index, 1)) both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 输入框图标 */
.layadmin-user-login-icon {
  position: absolute;
  left: 1px;
  top: 1px;
  width: 38px;
  line-height: 36px;
  text-align: center;
  color: #d2d2d2;
  transition: var(--theme-transition);
  z-index: 10;
}

.layui-form-item:focus-within .layadmin-user-login-icon {
  color: var(--theme-primary);
}

/* 输入框样式 */
.layadmin-user-login-body .layui-form-item .layui-input {
  padding-left: 38px;
  border-radius: var(--theme-radius);
  border: 1px solid var(--theme-border);
  transition: var(--theme-transition);
  font-size: 14px;
}

.layadmin-user-login-body .layui-form-item .layui-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.2);
}

.layadmin-user-login-body .layui-form-item .layui-input:hover {
  border-color: var(--theme-primary);
}

/* 验证码图片 */
.layadmin-user-login-codeimg {
  max-height: 38px;
  width: 100%;
  cursor: pointer;
  box-sizing: border-box;
  border-radius: var(--theme-radius);
  transition: var(--theme-transition);
}

.layadmin-user-login-codeimg:hover {
  opacity: 0.8;
}

/* 滑动验证码容器 */
#slider {
  border-radius: var(--theme-radius);
  overflow: hidden;
  border: 1px solid var(--theme-border);
}

/* 登录按钮 */
.layui-btn-fluid {
  border-radius: var(--theme-radius);
  font-size: 16px;
  font-weight: 500;
  transition: var(--theme-transition);
  position: relative;
  overflow: hidden;
}

.layui-btn-fluid:before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.layui-btn-fluid:hover:before {
  left: 100%;
}

.layui-btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 底部链接 */
.login-links {
  margin-top: 20px;
  animation: fadeIn 0.6s ease 0.8s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.font-set {
  color: var(--theme-primary);
  text-decoration: none;
  font-size: 14px;
  transition: var(--theme-transition);
}

.font-set:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 其他登录方式 */
.layadmin-user-login-other {
  position: relative;
  font-size: 0;
  line-height: 38px;
  padding-top: 20px;
}

.layadmin-user-login-other > * {
  display: inline-block;
  vertical-align: middle;
  margin-right: 10px;
  font-size: 14px;
}

.layadmin-user-login-other .layui-icon {
  position: relative;
  top: 2px;
  font-size: 26px;
}

.layadmin-user-login-other a:hover {
  opacity: 0.8;
}

.layadmin-user-jump-change {
  float: right;
}

/* 页脚 */
.layadmin-user-login-footer {
  left: 0;
  bottom: 0;
  width: 100%;
  line-height: 30px;
  padding: 20px;
  text-align: center;
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.6s ease 1s both;
}

.layadmin-user-login-footer span {
  padding: 0 5px;
}

.layadmin-user-login-footer a {
  padding: 0 5px;
  color: rgba(0, 0, 0, 0.5);
  transition: var(--theme-transition);
}

.layadmin-user-login-footer a:hover {
  color: rgba(0, 0, 0, 1);
}

/* 背景图片支持 */
.layadmin-user-login-main[bgimg] {
  background-color: var(--theme-white);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.05);
}

/* 主题切换 */
.ladmin-user-login-theme {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
}

.ladmin-user-login-theme ul {
  display: inline-block;
  padding: 5px;
  background-color: var(--theme-white);
  border-radius: var(--theme-radius);
}

.ladmin-user-login-theme ul li {
  display: inline-block;
  vertical-align: top;
  width: 64px;
  height: 43px;
  cursor: pointer;
  transition: var(--theme-transition);
  background-color: #f2f2f2;
  border-radius: var(--theme-radius);
  margin: 2px;
}

.ladmin-user-login-theme ul li:hover {
  opacity: 0.9;
  transform: scale(1.05);
}

/* 响应式设计 */

/* 滑动验证码样式增强 */
.pro_name a {
  color: #4183c4;
}

.osc_git_title {
  background-color: var(--theme-white);
}

.osc_git_box {
  background-color: var(--theme-white);
  border-color: #e3e9ed;
}

.osc_git_info {
  color: var(--theme-text-light);
}

.osc_git_main a {
  color: #9b9b9b;
}

/* 表单验证状态 */
.layui-form-item.error .layui-input {
  border-color: var(--theme-danger);
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.layui-form-item.success .layui-input {
  border-color: var(--theme-success);
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.layui-form-item.error .layadmin-user-login-icon {
  color: var(--theme-danger);
}

.layui-form-item.success .layadmin-user-login-icon {
  color: var(--theme-success);
}

/* 加载动画 */
.layui-anim-rotate {
  animation: layui-rotate 2s linear infinite;
}

@keyframes layui-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 无障碍访问 */
.layui-input:focus {
  outline: none;
}

.layui-btn:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 过渡动画 */
.theme-transition {
  transition: var(--theme-transition);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

/* 深色模式适配 */
