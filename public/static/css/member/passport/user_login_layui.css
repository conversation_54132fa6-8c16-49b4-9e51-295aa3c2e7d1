/* 用户登录页面样式 */

/* 深色模式支持 */

/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 隐藏Vue未编译内容 */
[v-cloak] {
  display: none !important;
}

/* 登录容器样式增强 */
.layui-container {
  max-width: 400px;
  width: 100%;
}

.layadmin-user-login-main {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 卡片样式增强 */
.layui-card {
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
  border: none;
  background: var(--theme-white);
  overflow: hidden;
}

.layui-card-header {
  background: linear-gradient(135deg, var(--theme-primary) 0%, #4facfe 100%);
  color: var(--theme-white);
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  padding: 20px;
  border-bottom: none;
}

.layui-card-body {
  padding: 30px;
}

/* 表单样式增强 */
.layui-form-item {
  margin-bottom: 20px;
  position: relative;
}

.layui-input {
  height: 45px;
  line-height: 45px;
  border: 2px solid var(--theme-border);
  border-radius: var(--theme-radius);
  padding: 0 15px;
  font-size: 14px;
  transition: var(--theme-transition);
  background: var(--theme-white);
}

.layui-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px rgba(30, 159, 255, 0.1);
  outline: none;
}

.layui-input.error {
  border-color: var(--theme-danger);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* 错误信息样式 */
.error-message {
  color: var(--theme-danger);
  font-size: 12px;
  margin-top: 5px;
  padding-left: 5px;
  display: flex;
  align-items: center;
}

.error-message::before {
  content: "⚠";
  margin-right: 5px;
}

/* 按钮样式增强 */
.layui-btn {
  height: 45px;
  line-height: 45px;
  border-radius: var(--theme-radius);
  font-size: 14px;
  font-weight: 500;
  transition: var(--theme-transition);
  border: none;
  cursor: pointer;
}

.layui-btn-fluid {
  width: 100%;
}

.layui-btn:not(.layui-btn-primary) {
  background: linear-gradient(135deg, var(--theme-primary) 0%, #4facfe 100%);
  color: var(--theme-white);
}

.layui-btn:not(.layui-btn-primary):hover:not(.disabled) {
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 159, 255, 0.3);
}

.layui-btn-primary {
  background: var(--theme-white);
  color: var(--theme-primary);
  border: 2px solid var(--theme-primary);
}

.layui-btn-primary:hover:not(.disabled) {
  background: var(--theme-primary);
  color: var(--theme-white);
  transform: translateY(-1px);
}

.layui-btn.disabled,
.layui-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 验证码按钮特殊样式 */
.layui-col-xs5 .layui-btn {
  font-size: 12px;
  padding: 0 8px;
}

/* 加载动画 */
.layui-icon-loading {
  margin-right: 5px;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background: var(--theme-white);
  padding: 30px;
  border-radius: var(--theme-radius);
  text-align: center;
  box-shadow: var(--theme-shadow);
}

.loading-spinner i {
  font-size: 24px;
  color: var(--theme-primary);
  margin-bottom: 10px;
}

.loading-spinner p {
  margin: 0;
  color: var(--theme-text);
  font-size: 14px;
}

/* 成功提示 */
.success-toast {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.toast-content {
  background: var(--theme-white);
  padding: 40px;
  border-radius: var(--theme-radius);
  text-align: center;
  box-shadow: var(--theme-shadow);
  position: relative;
  z-index: 1;
  animation: toastFadeIn 0.3s ease;
}

.success-icon {
  font-size: 48px;
  color: var(--theme-success);
  margin-bottom: 15px;
}

.toast-text {
  margin: 0;
  color: var(--theme-text);
  font-size: 16px;
  font-weight: 500;
}

@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */

/* 无障碍访问 */
.layui-input:focus,
.layui-btn:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 打印样式 */

/* 高对比度模式 */

/* 减少动画模式 */

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-light);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-light);
}

/* 选择文本样式 */
::selection {
  background: rgba(30, 159, 255, 0.2);
  color: var(--theme-text);
}

/* 占位符样式 */
.layui-input::placeholder {
  color: var(--theme-text-light);
  opacity: 0.8;
}

/* 表单验证状态 */
.layui-form-item.success .layui-input {
  border-color: var(--theme-success);
}

.layui-form-item.warning .layui-input {
  border-color: var(--theme-warning);
}

/* 链接样式 */
.layadmin-link {
  color: var(--theme-white);
  text-decoration: none;
  transition: var(--theme-transition);
}

.layadmin-link:hover {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}

/* 特殊样式保持 */
.captcha-blood {
  position: absolute;
  top: 0px;
  left: 125px;
  background-color: antiquewhite;
}
