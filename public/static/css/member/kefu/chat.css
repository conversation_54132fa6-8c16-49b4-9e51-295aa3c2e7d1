/* 客服聊天页面样式 */

/* 深色模式支持 */

/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  background-color: var(--theme-secondary);
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--theme-secondary);
}

/* 聊天头部 */
.chat-header {
  height: 60px;
  background: var(--theme-primary);
  color: var(--theme-white);
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: var(--theme-shadow);
  position: relative;
  z-index: 100;
}

.chat-header-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.chat-header-title {
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  line-height: 60px;
}

.chat-header-tool {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
}

.sound-toggle,
.websocket-status {
  background: none;
  border: none;
  color: var(--theme-white);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: var(--theme-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sound-toggle:hover,
.websocket-status:hover {
  background: rgba(255, 255, 255, 0.2);
}

.websocket-status.connected {
  color: var(--theme-success);
}

.websocket-status.disconnected {
  color: var(--theme-danger);
}

.websocket-status.connecting {
  color: var(--theme-warning);
}

/* 聊天主体 */
.chat-body {
  flex: 1;
  overflow: hidden;
  background: var(--theme-secondary);
}

.chat-box {
  height: 100%;
  overflow-y: auto;
  padding: 15px;
  background: var(--theme-secondary);
}

.chat-message {
  margin-bottom: 15px;
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 系统消息 */
.system-msg {
  text-align: center;
  color: var(--theme-text-light);
  font-size: 12px;
  padding: 8px 15px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--theme-radius);
  margin: 10px auto;
  max-width: 300px;
}

/* 消息行 */
.msg-row {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
}

.msg-row.msg-right {
  flex-direction: row-reverse;
}

.msg-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

/* 消息气泡 */
.msg-bubble {
  max-width: 70%;
  position: relative;
}

.msg-content {
  background: var(--theme-white);
  padding: 12px 15px;
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
  word-wrap: break-word;
  line-height: 1.5;
}

.msg-right .msg-content {
  background: var(--theme-primary);
  color: var(--theme-white);
}

/* 消息信息 */
.msg-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 5px;
  font-size: 12px;
  color: var(--theme-text-light);
}

.msg-right .msg-info {
  justify-content: flex-end;
}

.msg-time {
  color: var(--theme-text-light);
}

.msg-status {
  font-size: 11px;
}

.msg-status.read {
  color: var(--theme-success);
}

.msg-status.unread {
  color: var(--theme-text-light);
}

.recall-btn {
  background: none;
  border: 1px solid var(--theme-border);
  color: var(--theme-text-light);
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  transition: var(--theme-transition);
}

.recall-btn:hover {
  background: var(--theme-danger);
  color: var(--theme-white);
  border-color: var(--theme-danger);
}

/* 图片消息 */
.msg-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: var(--theme-radius);
  cursor: pointer;
  transition: var(--theme-transition);
}

.msg-image:hover {
  transform: scale(1.02);
}

/* 视频消息 */
.video-player {
  position: relative;
  cursor: pointer;
  border-radius: var(--theme-radius);
  overflow: hidden;
}

.video-player video {
  width: 100%;
  max-width: 300px;
  border-radius: var(--theme-radius);
  display: block;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--theme-radius);
  transition: var(--theme-transition);
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #333;
  cursor: pointer;
  transition: var(--theme-transition);
}

.play-button:hover {
  background: var(--theme-white);
  transform: scale(1.1);
}

.video-info {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

/* 机器人快捷回复 */
.robot-quick-replies {
  background: var(--theme-light);
  border-radius: var(--theme-radius);
  padding: 15px;
}

.quick-reply-title {
  font-weight: 500;
  margin-bottom: 10px;
  color: var(--theme-text);
}

.quick-reply-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-reply-btn {
  background: var(--theme-white);
  border: 1px solid var(--theme-border);
  padding: 8px 12px;
  border-radius: var(--theme-radius);
  cursor: pointer;
  transition: var(--theme-transition);
  text-align: left;
}

.quick-reply-btn:hover {
  background: var(--theme-primary);
  color: var(--theme-white);
  border-color: var(--theme-primary);
}

/* 聊天底部 */
.chat-footer {
  background: var(--theme-white);
  border-top: 1px solid var(--theme-border);
  padding: 15px;
}

.text-holder {
  margin-bottom: 10px;
}

.text-holder textarea {
  width: 100%;
  min-height: 60px;
  max-height: 120px;
  border: 1px solid var(--theme-border);
  border-radius: var(--theme-radius);
  padding: 10px;
  font-size: 14px;
  resize: vertical;
  font-family: inherit;
  transition: var(--theme-transition);
}

.text-holder textarea:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(30, 159, 255, 0.2);
}

.send-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tool-box {
  display: flex;
  align-items: center;
  gap: 15px;
}

.tool-box i {
  font-size: 20px;
  color: var(--theme-text-light);
  cursor: pointer;
  transition: var(--theme-transition);
}

.tool-box i:hover {
  color: var(--theme-primary);
}

.staff-service {
  background: var(--theme-warning);
  color: var(--theme-white);
  padding: 6px 12px;
  border-radius: var(--theme-radius);
  font-size: 12px;
  cursor: pointer;
  transition: var(--theme-transition);
}

.staff-service:hover {
  background: #e0a800;
}

.send-btn-div {
  margin-left: 15px;
}

.send-input {
  background: var(--theme-primary);
  color: var(--theme-white);
  border: none;
  padding: 10px 20px;
  border-radius: var(--theme-radius);
  cursor: pointer;
  font-size: 14px;
  transition: var(--theme-transition);
}

.send-input:hover:not(:disabled) {
  background: #1890ff;
}

.send-input:disabled {
  background: var(--theme-border);
  cursor: not-allowed;
}

/* 图片预览 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;
}

.image-preview-content {
  max-width: 90%;
  max-height: 90%;
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
}

/* 常见问题 */
.common-questions {
  position: absolute;
  top: 62px;
  right: 0;
  height: calc(100% - 62px);
  width: 230px;
  background: var(--theme-white);
  border-left: 1px solid var(--theme-border);
  display: flex;
  flex-direction: column;
}

.common-questions .layui-tab {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.common-questions .layui-tab-content {
  flex: 1;
  overflow-y: auto;
}

.common-questions .info-msg p {
  margin: 8px 0;
}

.common-questions .info-msg a {
  color: var(--theme-text);
  text-decoration: none;
  font-size: 14px;
  transition: var(--theme-transition);
}

.common-questions .info-msg a:hover {
  color: var(--theme-primary);
}

.powered-by {
  flex-shrink: 0;
  height: 27px;
  background: #e9f0ef;
  margin-bottom: 0;
  font-size: 12px;
  text-align: center;
  line-height: 27px;
}

.powered-by a {
  color: var(--theme-primary);
  text-decoration: none;
}

/* 响应式设计 */

/* 无障碍访问 */
button:focus,
input:focus,
textarea:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading::after {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme-border);
  border-top: 2px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
