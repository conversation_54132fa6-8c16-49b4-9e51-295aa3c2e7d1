html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 防止body滚动 */
}

#app {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  flex-shrink: 0; /* 防止头部被压缩 */
  height: 60px;
  border-bottom: 1.5px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-header .chat-header-avatar .agent-avatar {
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chat-header .chat-header-title {
  font-weight: 500;
  font-size: 18px;
  line-height: 60px;
  color: #fff;
  padding-left: 50px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-header-tool {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.chat-header-tool .iconfont {
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: color 0.2s;
  padding: 4px;
  border-radius: 4px;
}

.chat-header-tool .iconfont:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.1);
}

.chat-body {
  position: absolute;
  top: 60px;
  bottom: 130px;
  left: 0;
  right: 0;
  overflow-y: auto;
  padding: 18px 16px 12px 16px;
  background: #f9f9fb;
  display: flex;
  flex-direction: column;
}

.chat-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 130px;
  background-color: #fff;
  box-shadow: 0 -1px 0 0 rgba(0, 0, 0, 0.04), 0 -2px 0 0 rgba(0, 0, 0, 0.01);
  border-bottom: 1px solid #e2e2e2;
  z-index: 10;
  padding-bottom: 12px;
}

.chat-footer .text-holder {
  height: 90px;
  padding-top: 8px;
  padding-left: 10px;
}

.chat-footer .text-holder textarea {
  display: block;
  width: 92%;
  height: 75px;
  padding: 8px 10px;
  overflow-x: hidden;
  overflow-y: auto;
  resize: none;
  outline: 0;
  background-color: #fff;
  border: 0;
  word-break: break-all;
  font-size: 13px;
  line-height: 17px;
  -webkit-appearance: none;
  border-radius: 4px;
  box-sizing: border-box;
}

.chat-footer .send-bar {
  height: 40px;
  width: 100%;
  overflow: hidden;
  /* 不用flex，直接用float布局 */
}

.chat-footer .send-bar .tool-box {
  float: left;
  width: 80%;
  height: 100%;
  line-height: 40px;
  text-align: left;
  position: relative;
  left: 10px;
  display: block;
}

.chat-footer .send-bar .tool-box i {
  margin-left: 10px;
  font-size: 20px;
  color: #c2c2c2;
  cursor: pointer;
}

.chat-footer .send-bar .staff-service {
  font-size: 12px;
  padding: 3px 10px 3px 10px;
  margin-left: 10px;
  cursor: pointer;
  margin-top: -5px;
  top: -3px;
  position: relative;
  color: #fff;
  background: #1e9fff;
  border-radius: 1px;
}

.chat-footer .send-bar .send-btn-div {
  float: right;
  width: 20%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 16px;
  box-sizing: border-box;
}

.chat-footer .send-bar .send-btn-div .send-input {
  width: 100%;
  font-size: 13px;
  border: 0;
  outline: none;
  background-color: #1e9fff;
  color: #fff;
  cursor: pointer;
  padding: 5px 0;
  border-radius: 4px;
  transition: background 0.2s;
  box-sizing: border-box;
}

.chat-footer .send-bar .send-input:enabled {
  background-color: #1e9fff;
  color: #fff;
}

.chat-message {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  outline: none;
}

.chat-message:focus,
.chat-message:active {
  outline: none;
  background: none;
}

.msg-bubble:focus,
.msg-bubble:active {
  outline: none;
  background: inherit;
}

.msg-row {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
}

.msg-left {
  flex-direction: row;
}

.msg-right {
  flex-direction: row-reverse;
}

.msg-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  margin: 0 10px;
  border: 1.5px solid #e0e0e0;
  background: #fff;
}

.msg-bubble {
  max-width: 60%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  padding: 10px 16px;
  position: relative;
  word-break: break-all;
  font-size: 15px;
  color: #222;
  margin-bottom: 2px;
}

.msg-right .msg-bubble {
  background: #e6f7ff;
  color: #222;
}

.msg-content {
  word-break: break-all;
  line-height: 1.5;
  margin-bottom: 4px;
}

.msg-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
}

.msg-time {
  font-size: 12px;
  color: #aaa;
  text-align: right;
}

.msg-status {
  font-size: 12px;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.msg-status.read {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.msg-status.unread {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

.msg-right .msg-status {
  margin-right: 6px;
}

.recall-btn {
  font-size: 11px;
  color: #1e9fff;
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.recall-btn:hover {
  background: rgba(30, 159, 255, 0.1);
}

/* 系统消息样式 */
.system-msg {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin: 8px 0;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  max-width: 200px;
  margin: 8px auto;
}

/* 聊天框滚动条样式 */
.chat-body::-webkit-scrollbar {
  width: 4px;
}

.chat-body::-webkit-scrollbar-track {
  background: transparent;
}

.chat-body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 消息动画效果 */
.chat-message {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recall-btn:hover {
  background: rgba(30, 159, 255, 0.1);
}

.msg-right .recall-btn {
  color: #1e9fff;
}

.system-msg {
  text-align: center;
  color: #666;
  font-size: 12px;
  margin: 15px auto;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  display: block;
  width: 100%;
  padding: 6px 12px;
  max-width: 200px;
  word-break: break-all;
  box-sizing: border-box;
}

/* 图片消息样式 */
.msg-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: block;
}

.msg-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片预览遮罩 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
}

.image-preview-content {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.send-bar {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
  box-sizing: border-box;
}

.tool-box {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 12px;
}

.text-holder {
  flex: 1;
  margin: 0 12px;
  display: flex;
  align-items: center;
}

.text-holder textarea {
  width: 100%;
  height: 40px;
  min-height: 40px;
  max-height: 120px;
  resize: none;
  border-radius: 6px;
  border: 1px solid #ddd;
  padding: 8px 12px;
  font-size: 16px;
  line-height: 1.5;
  background: #fff;
  box-sizing: border-box;
}

.send-btn-div {
  flex-shrink: 0;
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.send-input {
  height: 40px;
  min-width: 68px;
  font-size: 16px;
  border-radius: 6px;
  background: #4ea1ff;
  color: #fff;
  border: none;
  cursor: pointer;
}

.send-input:disabled {
  background: #ccc;
  color: #fff;
  cursor: not-allowed;
}

.chat-message.system {
  display: flex;
  justify-content: center;
}

/* 优化聊天框整体布局 */
.chat-box {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 10px;
}

/* 机器人快捷回复样式 */
.robot-quick-replies {
  max-width: 100%;
  margin: 5px 0;
}

.quick-reply-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.quick-reply-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.quick-reply-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  text-align: center;
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.quick-reply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.quick-reply-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.quick-reply-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quick-reply-btn:hover::before {
  left: 100%;
}

/* 机器人消息特殊样式 */
.msg-row .msg-bubble:has(.robot-quick-replies) {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border: 1px solid #e1e8ff;
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.1);
}

/* 机器人头像特殊效果 */
.msg-avatar[src*="robot"] {
  border: 2px solid #667eea;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
  animation: robotPulse 2s infinite;
}

@keyframes robotPulse {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
  }
}

/* 优化输入框样式 */
.text-holder textarea:focus {
  border-color: #4ea1ff;
  box-shadow: 0 0 0 2px rgba(78, 161, 255, 0.2);
  outline: none;
}

/* 工具栏图标优化 */
.tool-box i {
  transition: color 0.2s;
}

.tool-box i:hover {
  color: #4ea1ff;
}

/* 发送按钮优化 */
.send-input:hover:not(:disabled) {
  background: #3d8bff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(78, 161, 255, 0.3);
}

.send-input:active:not(:disabled) {
  transform: translateY(0);
}

/* 兼容老系统消息气泡系统消息样式 */
.chat-system {
  padding: 8px 15px;
  background-color: #e2e2e2;
  border-radius: 6px;
  color: #333;
  word-break: break-all;
  font-size: 13px;
  cursor: pointer;
  text-align: center;
  margin: 8px auto;
  max-width: 220px;
}

/* 聊天消息已读/未读小圆点 */
.no-read,
.check-read {
  color: #ff5722;
  font-size: 12px;
  top: 5px;
  left: -5px;
  position: relative;
}

.already-read,
.complete-read {
  color: #c2c2c2;
  font-size: 12px;
  top: 5px;
  left: -5px;
  position: relative;
}

/* 兼容表情弹窗样式 */
body .layui-whisper-face {
  border: none;
  background: none;
}

body .layui-whisper-face .layui-layer-content {
  padding: 0;
  background-color: #fff;
  color: #666;
  box-shadow: none;
}

.layui-whisper-face .layui-layer-TipsG {
  display: none;
}

.layui-whisper-face ul {
  position: relative;
  width: 372px;
  padding: 10px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}

.layui-whisper-face ul li {
  cursor: pointer;
  float: left;
  border: 1px solid #e8e8e8;
  height: 22px;
  width: 26px;
  overflow: hidden;
  margin: -1px 0 0 -1px;
  padding: 4px 2px;
  text-align: center;
}

.layui-whisper-face ul li:hover {
  position: relative;
  z-index: 2;
  border: 1px solid #eb7350;
  background: #fff9ec;
}

.layui-unselect .close {
  position: relative;
  top: -24px;
  left: 36px;
  color: #2f4056;
}

/* 兼容右侧气泡内链接色 */
.right .chat-message a {
  color: #ff5722 !important;
}

/* 返回按钮（如页面用到） */
.chat-back {
  top: 18px;
  position: absolute;
  color: #fff;
  left: 10px;
  height: 40px;
  width: 40px;
}

/* 兼容历史消息/更多记录等老结构 */
.clearfloat {
  zoom: 1;
  margin: 10px 10px;
}

.author-name {
  text-align: center;
  margin: 15px 0 5px 0;
  color: #888;
}

.small-chat-box .chat-date {
  opacity: 0.6;
  font-size: 10px;
  font-weight: 400;
}

.clearfloat .chat-avatars {
  display: inline-block;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #eee;
  vertical-align: top;
  overflow: hidden;
}

.clearfloat .chat-avatars > img {
  width: 35px;
  height: 35px;
}

.sound-toggle {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sound-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sound-toggle i {
  font-size: 16px;
}

/* WebSocket状态指示器样式 */
.websocket-status {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.websocket-status:hover {
  background: rgba(255, 255, 255, 0.1);
}

.websocket-status i {
  font-size: 16px;
}

/* 不同状态的颜色 */
.websocket-status.connected {
  color: #52c41a;
}

.websocket-status.connecting {
  color: #faad14;
  animation: rotate 1s linear infinite;
}

.websocket-status.disconnected {
  color: #ff4d4f;
}

.websocket-status.reconnecting {
  color: #fa8c16;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 动画效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}
