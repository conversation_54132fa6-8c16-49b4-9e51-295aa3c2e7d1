/* 电子卡册页面样式 */

/* 基础样式重置 */
html,
body {
  color: #333;
  margin: 0;
  height: 100%;
  font-family: "Myriad Set Pro", "Helvetica Neue", Helvetica, Arial, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: normal;
  background: #fff;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

a {
  text-decoration: none;
  color: #666;
}

a,
label,
button,
input,
select {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

img {
  border: 0;
}

/* Vue.js防闪烁 */
[v-cloak] {
  display: none;
}

/* 轮播图样式 */
.swiper.top {
  width: 100%;
  height: auto;
}

.swiper-slide img {
  width: 100%;
  height: auto;
  display: block;
}

/* 联系信息栏 */
.contact-bar {
  display: flex;
  line-height: 30px;
  background-color: rgb(235, 234, 234);
}

.contact-bar .company {
  flex: 1;
  text-align: center;
}

.contact-bar .phone-link {
  flex: 1;
  display: flex;
  align-items: center;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.contact-bar .phone-icon {
  width: 20px;
  height: auto;
  flex: 0 0 20px;
  margin-left: 5px;
}

/* 分类导航样式 */
.cat_box {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}

.cat {
  display: inline-block;
  padding: 10px 20px;
}

/* 主要内容区域 */
.bottom {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

/* 左侧分类菜单 */
.left {
  height: 100%;
  width: 35%;
  overflow: hidden;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 1;
  padding: 5px 5px 0px 5px;
}

.scrolltab-item {
  position: relative;
  z-index: 1;
  width: 100%;
  display: block;
  text-align: center;
  background: #ee7341;
  border-radius: 8px;
  margin-bottom: 2px;
  padding: 6px 4px;
  min-height: 32px;
  box-sizing: border-box;
}

.scrolltab-item.crt {
  background-color: #f5c853;
  position: relative;
}

.scrolltab-title {
  font-size: 13px;
  color: #fff;
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.3;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 右侧商品列表 */
.right {
  height: 100%;
  background-color: #ffffff;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  position: relative;
  padding: 0 0 60px;
}

/* 商品卡片样式 */
.box {
  width: 48%;
  float: left;
  margin-right: 2%;
  margin-bottom: 2%;
  background: #fff;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 2px 17px 1px rgb(0 0 0 / 5%);
  padding: 10px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.box:hover {
  transform: translateY(-2px);
  box-shadow: 0px 4px 20px 2px rgb(0 0 0 / 10%);
}

.box:nth-child(2n) {
  margin-right: 0;
}

/* 商品图片容器 */
.goods-image-container {
  height: 28vw;
  width: 28vw;
  max-height: 120px;
  max-width: 120px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 2px;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品标题 */
.title {
  color: #656161;
  font-size: 12px;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: bold;
  margin-top: 5px;
  height: 36px;
}

/* 价格样式 */
.price-container {
  margin-top: 5px;
}

.price {
  color: #fe654c;
  font-size: 18px;
  font-weight: bold;
}

.yuan {
  color: #fe654c;
  font-size: 14px;
}

.red {
  color: #fc8080;
}

/* 底部按钮样式 */
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  padding: 10px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.bottom-button .layui-btn {
  width: 100%;
  background: #4caf50;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
}

.bottom-button .layui-btn:hover {
  background: #45a049;
}

/* 底部间距 */
.bottom-spacer {
  height: 60px;
  display: block;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 40px;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-container .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-container .empty-text {
  font-size: 14px;
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .bottom {
    height: calc(100vh - 150px);
  }

  .left {
    width: 25%;
  }

  .scrolltab-item {
    padding: 12px 8px;
  }

  .scrolltab-title {
    font-size: 12px;
  }

  .box {
    width: 48%;
    padding: 8px;
  }

  .goods-image-container {
    height: 25vw;
    width: 25vw;
  }

  .title {
    font-size: 11px;
    line-height: 16px;
  }

  .price {
    font-size: 16px;
  }
}

/* 主题变量支持 */
.scrolltab-item.crt {
  background-color: var(--theme-primary, #1890ff);
  color: white;
}

.goods-image-container {
  border: 1px solid var(--theme-primary, #1890ff);
  outline: 1px solid var(--theme-primary, #1890ff);
  outline-offset: 2px;
}

.bottom-button .layui-btn {
  background-color: var(--theme-primary, #1890ff);
  border-color: var(--theme-primary, #1890ff);
}
