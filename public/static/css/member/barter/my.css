/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面基础样式 */
body {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow-x: hidden; /* 防止水平滚动条 */
}

html,
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #999;
}

/* 页面容器 */
.page-container {
  min-height: auto;
  padding: 12px;
  max-width: 100vw;
  box-sizing: border-box;
}

/* 会员信息卡片 */
.member-card {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.member-profile {
  display: flex;
  align-items: center;
  padding: 20px;
  text-decoration: none;
  color: inherit;
}

.member-avatar {
  width: 66px;
  height: 66px;
  margin-right: 16px;
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 8px;
}

.member-level {
  font-size: 12px;
  color: #999;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  background: #f0f0f0;
}

.stat-card {
  background: #fff;
  text-align: center;
  padding: 20px;
}

.stat-card a {
  text-decoration: none;
  color: inherit;
  display: block;
}

.stat-title {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-primary, #ff6037);
  margin: 0;
}

/* 订单列表区域 */
.orders-section {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
}

/* LayUI标签页样式覆盖 */
.layui-tab-title {
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.layui-tab-title li {
  color: #666;
  font-size: 14px;
}

.layui-tab-title .layui-this {
  color: var(--theme-primary, #1890ff);
  border-bottom-color: var(--theme-primary, #1890ff);
}

.layui-tab-content {
  padding: 0;
}

.layui-tab-item {
  padding: 16px;
}

/* 订单列表 */
.order-list {
  min-height: 200px;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item:hover {
  background-color: #f8f9fa;
}

.order-icon {
  width: 50px;
  height: 50px;
  margin-right: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
}

.order-icon img {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.order-icon i {
  font-size: 24px;
  color: var(--theme-primary, #1890ff);
}

.order-content {
  flex: 1;
  min-width: 0;
}

.order-number {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
}

.order-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.order-memo {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-amount {
  text-align: right;
  flex-shrink: 0;
}

.amount {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-primary, #ff6037);
  display: block;
}

.status {
  font-size: 12px;
  color: #52c41a;
  margin-top: 4px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 响应主题变化的动画 */
.member-name,
.section-title h2,
.order-number,
.stat-value,
.amount,
.layui-tab-title .layui-this,
.order-icon i {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}
