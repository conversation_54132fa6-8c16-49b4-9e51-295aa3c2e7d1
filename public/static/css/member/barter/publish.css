/* 商品发布页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
}

/* 页面标题 */
.page-title {
  background: var(--theme-primary, #1890ff);
  color: #fff;
  text-align: center;
  padding: 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 表单容器 */
.form-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: var(--theme-text, #333);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
}

.form-input::placeholder {
  color: var(--theme-text-placeholder, #999);
}

.form-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: var(--theme-text, #333);
  resize: vertical;
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
}

.form-select {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: var(--theme-text, #333);
  transition: border-color 0.3s ease;
}

.form-select:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
}

/* 图片上传区域 */
.image-upload-section {
  margin: 24px 0;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--theme-border, #e5e5e5);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-remove {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  background: #ff4d4f;
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  width: 80px;
  height: 80px;
  border: 2px dashed var(--theme-border, #e5e5e5);
  border-radius: 8px;
  background: var(--theme-background-light, #fafafa);
  color: var(--theme-text-secondary, #666);
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-button:hover {
  border-color: var(--theme-primary, #1890ff);
  color: var(--theme-primary, #1890ff);
}

.upload-input {
  display: none;
}

/* 上传进度 */
.upload-progress {
  margin-top: 8px;
  height: 4px;
  background: var(--theme-background-light, #f0f0f0);
  border-radius: 2px;
  overflow: hidden;
}

.upload-progress-bar {
  height: 100%;
  background: var(--theme-primary, #1890ff);
  transition: width 0.3s ease;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 32px;
  padding: 0 8px;
}

.publish-button {
  flex: 1;
  height: 48px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.publish-button:hover {
  background: var(--theme-primary-dark, #1677cc);
}

.publish-button:disabled {
  background: var(--theme-disabled, #ccc);
  cursor: not-allowed;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 错误状态 */
.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: #ff4d4f;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--theme-text-placeholder, #ccc);
}

/* 触摸友好 */
.upload-button:active,
.publish-button:active {
  transform: scale(0.98);
}
