/* 商品列表页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 80px; /* 为底部按钮留空间 */
  background: var(--theme-background, #f5f5f5);
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 页面标题 */
.page-header {
  background: #fff;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
  margin-bottom: 16px;
}

.page-header h1 {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-header h1 i {
  color: var(--theme-primary, #1890ff);
}

/* 商品统计 */
.goods-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.stat-label {
  color: var(--theme-text-secondary, #666);
}

.stat-value {
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.stat-value.online {
  color: var(--theme-success, #52c41a);
  background: var(--theme-success-light, rgba(82, 196, 26, 0.1));
}

.stat-value.offline {
  color: var(--theme-error, #ff4d4f);
  background: var(--theme-error-light, rgba(255, 77, 79, 0.1));
}

/* 商品网格 */
.goods-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 0 12px;
}

/* 商品项 */
.goods-item {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.goods-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--theme-shadow, rgba(0, 0, 0, 0.15));
}

.goods-item:active {
  transform: translateY(0);
}

/* 商品图片 */
.goods-image {
  position: relative;
  width: 100%;
  height: 140px;
  overflow: hidden;
  background: var(--theme-background-light, #fafafa);
}

.goods-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.goods-item:hover .goods-image img {
  transform: scale(1.05);
}

/* 状态标识 */
.status-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
}

.status-badge.offline {
  background: var(--theme-error, #ff4d4f);
}

.status-badge.online {
  background: var(--theme-success, #52c41a);
}

/* 商品信息 */
.goods-info {
  padding: 12px;
}

.goods-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 38px;
}

.goods-price {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.price {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-primary, #1890ff);
}

.currency {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme-text-placeholder, #ccc);
  margin-bottom: 16px;
  display: block;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

/* 空状态操作按钮 */
.empty-action-btn {
  background: linear-gradient(135deg, var(--theme-primary, #1890ff), var(--theme-primary-dark, #096dd9));
  color: #fff;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 8px var(--theme-primary-light, rgba(24, 144, 255, 0.3));
  transition: all 0.3s ease;
}

.empty-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--theme-primary-light, rgba(24, 144, 255, 0.4));
}

.empty-action-btn:active {
  transform: translateY(0);
}

.empty-action-btn i {
  font-size: 16px;
}

/* 发布商品按钮容器 */
.publish-btn-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

/* 发布商品按钮 */
.publish-btn {
  background: linear-gradient(135deg, var(--theme-primary, #1890ff), var(--theme-primary-dark, #096dd9));
  color: #fff;
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 16px var(--theme-primary-light, rgba(24, 144, 255, 0.3));
  transition: all 0.3s ease;
}

.publish-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--theme-primary-light, rgba(24, 144, 255, 0.4));
}

.publish-btn:active {
  transform: translateY(0);
}

.publish-btn i {
  font-size: 18px;
}

/* 刷新按钮 */
.refresh-button {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--theme-border, #e8e8e8);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
  z-index: 999;
  backdrop-filter: blur(10px);
}

.refresh-button:hover {
  background: #fff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--theme-shadow, rgba(0, 0, 0, 0.15));
}

.refresh-button:active {
  transform: scale(0.95);
}

.refresh-button i {
  font-size: 18px;
  color: var(--theme-primary, #1890ff);
  transition: transform 0.3s ease;
}

.refresh-button:hover i {
  transform: rotate(180deg);
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 大屏幕优化 */

/* 触摸友好 */
.goods-item:active {
  transform: scale(0.98);
}

.publish-btn:active {
  transform: scale(0.95);
}

/* 商品加载骨架屏 */
.goods-skeleton {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.skeleton-image {
  width: 100%;
  height: 140px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-info {
  padding: 12px;
}

.skeleton-title {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-price {
  height: 14px;
  width: 60%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 商品状态指示器 */
.goods-status {
  position: absolute;
  bottom: 8px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--theme-success, #52c41a);
}

.goods-status.offline {
  background: var(--theme-error, #ff4d4f);
}

/* 商品操作菜单 */
.goods-menu {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.goods-item:hover .goods-menu {
  opacity: 1;
}

.goods-menu i {
  color: #fff;
  font-size: 12px;
}

/* 响应式优化 */

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .page-container {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-bottom: max(80px, env(safe-area-inset-bottom));
  }

  .publish-btn-container {
    bottom: max(20px, env(safe-area-inset-bottom));
  }
}

/* 滚动优化 */
.page-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
