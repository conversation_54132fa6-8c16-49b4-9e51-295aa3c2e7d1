/* 积分转账页面样式 - 使用项目主题变量 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

[v-cloak] {
  display: none !important;
}

/* 容器样式 */
.transfer-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 16px;
}

/* 卡片基础样式 */
.member-info-card,
.transfer-form-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

/* 卡片标题 */
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding: 16px 20px 12px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

/* 会员信息样式 */
.info-list {
  padding: 0 20px 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-value.highlight {
  color: var(--theme-primary);
  font-weight: 600;
}

/* 表单样式 */
.transfer-form {
  padding: 0 20px 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background: #fff;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px var(--theme-shadow);
}

.form-input::placeholder {
  color: #999;
}

/* 会员姓名显示 */
.member-name {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #666;
}

.member-name.checking {
  color: var(--theme-primary);
}

/* 按钮组 */
.button-group {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.submit-button {
  width: 100%;
  height: 48px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-button:hover {
  background: var(--theme-hover);
}

.submit-button:active {
  transform: translateY(1px);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.contact-button {
  width: 100%;
  height: 48px;
  background: transparent;
  color: var(--theme-primary);
  border: 1px solid var(--theme-primary);
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.contact-button:hover {
  background: var(--theme-primary);
  color: #fff;
}

/* 成功状态样式 */
.success-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.success-content {
  text-align: center;
  max-width: 320px;
  width: 100%;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: var(--theme-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  color: #fff;
  margin: 0 auto 24px;
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-title {
  font-size: 20px;
  color: #333;
  margin: 0 0 12px;
  font-weight: 600;
}

.success-description {
  font-size: 14px;
  color: #666;
  margin: 0 0 32px;
  line-height: 1.5;
}

.success-button {
  width: 100%;
  height: 48px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-button:hover {
  background: var(--theme-hover);
}

/* 响应式设计 */

/* 动画效果 */
.transfer-container > * {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单验证状态 */
.form-input.error {
  border-color: #ff4757;
  box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.1);
}

.form-input.success {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px var(--theme-shadow);
}

/* 加载状态 */
.layui-icon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
