/* 话题详情页面样式 */

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: var(--theme-bg-color, #f5f5f5);
  color: var(--theme-text-color, #333);
  line-height: 1.6;
  min-height: 100vh;
}

/* Vue.js 隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 话题详情应用容器 */
.topic-detail-app {
  min-height: 100vh;
  background: var(--theme-bg-color, #f5f5f5);
}

/* 话题容器 */
.topic-container {
  background: var(--theme-card-bg, #fff);
  min-height: 100vh;
}

/* 话题标题区域 */
.topic-header {
  padding: 20px 15px;
  border-bottom: 1px solid var(--theme-border-color, #eee);
  background: var(--theme-card-bg, #fff);
}

.topic-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--theme-text-color, #333);
  line-height: 1.4;
  margin-bottom: 10px;
  word-wrap: break-word;
}

.topic-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  color: var(--theme-text-secondary, #666);
  font-size: 14px;
}

.topic-time {
  display: flex;
  align-items: center;
  gap: 5px;
}

.topic-time::before {
  content: '📅';
  font-size: 12px;
}

/* 话题内容区域 */
.topic-content {
  padding: 20px 15px;
  background: var(--theme-card-bg, #fff);
}

.content-html {
  font-size: 16px;
  line-height: 1.8;
  color: var(--theme-text-color, #333);
  word-wrap: break-word;
}

/* 内容中的元素样式 */
.content-html h1,
.content-html h2,
.content-html h3,
.content-html h4,
.content-html h5,
.content-html h6 {
  margin: 20px 0 15px 0;
  font-weight: 600;
  color: var(--theme-text-color, #333);
}

.content-html h1 { font-size: 24px; }
.content-html h2 { font-size: 22px; }
.content-html h3 { font-size: 20px; }
.content-html h4 { font-size: 18px; }
.content-html h5 { font-size: 16px; }
.content-html h6 { font-size: 14px; }

.content-html p {
  margin: 15px 0;
  text-align: justify;
}

.content-html img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 15px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-html img:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.content-html ul,
.content-html ol {
  margin: 15px 0;
  padding-left: 30px;
}

.content-html li {
  margin: 8px 0;
}

.content-html blockquote {
  margin: 20px 0;
  padding: 15px 20px;
  background: var(--theme-bg-light, #f8f9fa);
  border-left: 4px solid var(--theme-primary-color, #1890ff);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--theme-text-secondary, #666);
}

.content-html code {
  background: var(--theme-bg-light, #f8f9fa);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: var(--theme-primary-color, #1890ff);
}

.content-html pre {
  background: var(--theme-bg-dark, #2f3349);
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 20px 0;
}

.content-html pre code {
  background: none;
  padding: 0;
  color: inherit;
}

.content-html a {
  color: var(--theme-primary-color, #1890ff);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.content-html a:hover {
  border-bottom-color: var(--theme-primary-color, #1890ff);
}

/* 操作按钮区域 */
.topic-actions {
  padding: 20px 15px;
  background: var(--theme-card-bg, #fff);
  border-top: 1px solid var(--theme-border-color, #eee);
  display: flex;
  justify-content: center;
  gap: 15px;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.action-btn.secondary {
  background: var(--theme-bg-light, #f8f9fa);
  color: var(--theme-text-color, #333);
  border: 1px solid var(--theme-border-color, #eee);
}

.action-btn.secondary:hover {
  background: var(--theme-primary-color, #1890ff);
  color: #fff;
  border-color: var(--theme-primary-color, #1890ff);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: var(--theme-text-secondary, #666);
}

.loading-state i {
  font-size: 40px;
  margin-bottom: 15px;
  color: var(--theme-primary-color, #1890ff);
}

.loading-state p {
  font-size: 16px;
  margin: 0;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  padding: 40px 20px;
  text-align: center;
}

.empty-state i {
  font-size: 80px;
  color: var(--theme-text-light, #ccc);
  margin-bottom: 20px;
}

.empty-state .empty-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme-text-color, #333);
  margin-bottom: 10px;
}

.empty-state .empty-desc {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 30px;
  line-height: 1.6;
}

.empty-btn {
  padding: 12px 30px;
  background: var(--theme-primary-color, #1890ff);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.empty-btn:hover {
  background: var(--theme-primary-dark, #096dd9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 响应式设计 */

/* 暗色主题适配 */
