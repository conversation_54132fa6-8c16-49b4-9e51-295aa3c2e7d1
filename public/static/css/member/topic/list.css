/* 话题列表页面样式 - LayUI版本 */

/* Vue.js隐藏未编译模板 */
[v-cloak] {
  display: none !important;
}

/* 页面基础样式 */
.background {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 话题项样式 */
.topic-item {
  margin-bottom: 15px;
  transition: all 0.3s ease;
  border: 1px solid #e6e6e6;
}

.topic-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.topic-item:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 缩略图容器样式 */
.topic-thumb-container {
  width: 100%;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
}

/* 缩略图样式 */
.topic-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #f0f0f0;
}

/* 图片占位符样式 */
.topic-thumb-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 20px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.topic-item:hover .topic-thumb-placeholder {
  background: linear-gradient(135deg, #e8e8e8 0%, #ddd 100%);
  color: #999;
}

/* 内容区域样式 */
.topic-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 60px;
}

/* 标题样式 */
.topic-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 元信息样式 */
.topic-meta {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: auto;
}

.topic-meta .layui-badge {
  font-size: 12px;
  padding: 2px 6px;
}

/* 搜索框样式 */
.search-box {
  background: white;
  padding: 15px;
  margin-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.search-box .layui-input-group {
  border-radius: 20px;
  overflow: hidden;
}

.search-box .layui-input {
  border: none;
  background: #f5f5f5;
}

.search-box .layui-btn {
  border: none;
  background: #1e9fff;
  color: white;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 20px;
  color: #666;
}

.loading-state i {
  font-size: 20px;
  margin-right: 8px;
}

/* 没有更多数据样式 */
.no-more-data {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 14px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 50px 20px;
}

.empty-state i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 20px;
}

.empty-state h2 {
  color: #999;
  font-size: 18px;
  margin-bottom: 10px;
}

.empty-state p {
  color: #ccc;
  font-size: 14px;
}

/* 响应式设计 */

/* 无限滚动相关样式 */
.load-more-trigger {
  height: 1px;
  opacity: 0;
}

/* LayUI卡片样式微调 */
.layui-card {
  border-radius: 8px;
  overflow: hidden;
}

.layui-card-body {
  padding: 15px;
}

/* 容器样式 */
.layui-container {
  max-width: 100%;
  padding: 10px;
}

/* 修复LayUI在移动端的一些样式问题 */
