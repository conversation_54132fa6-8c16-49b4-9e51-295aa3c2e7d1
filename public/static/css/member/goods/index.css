/* 商品详情页面样式 */
[v-cloak] {
  display: none !important;
}

/* 基础样式重置 */
* {
  padding: 0;
  margin: 0;
  list-style: none;
  border: 0;
  font-weight: 400;
  font-family: -apple-system-font, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
}

body {
  background: #f8f8f8;
  font-size: 14px;
  max-width: 640px;
  margin: 0 auto;
  position: relative;
}

/* 欢迎页面样式 */
.welcome-container {
  height: 100vh;
  background: #f8f8f8;
}

.welcome-box {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  height: 20%;
}

.welcome-text {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  line-height: 0.6rem;
  font-size: 1rem;
  padding: 10% 0;
  color: white;
  font-weight: bold;
  background-color: var(--theme-primary);
  width: 80%;
  border-radius: 0.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.welcome-text:hover {
  background-color: var(--theme-hover);
  transform: scale(1.05);
}

/* 商品详情页面样式 */
.goods-container {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 80px;
}

/* 商品图片 */
.goods-image {
  width: 100%;
  background: #fff;
}

.goods-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* 商品信息头部 */
.goods-header {
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.goods-title {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 10px;
}

.goods-price {
  margin-bottom: 10px;
}

.current-price {
  color: var(--theme-primary);
  font-size: 24px;
  font-weight: bold;
}

.current-price small {
  font-size: 14px;
}

.goods-sales {
  background: #fafafa;
  padding: 10px;
  font-size: 12px;
  color: #666;
  border-radius: 4px;
}

/* 商品规格信息 */
.goods-specs {
  background: #fff;
  margin-bottom: 10px;
}

.spec-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  color: #999;
  width: 60px;
  flex-shrink: 0;
}

.spec-value {
  color: #333;
  flex: 1;
}

/* 广告区域 */
.goods-ad {
  background: #fff;
  padding: 15px;
  text-align: center;
  margin-bottom: 10px;
}

.ad-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--theme-primary);
}

/* 商品详情 */
.goods-content {
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
}

.content-nav {
  font-size: 14px;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.nav-title {
  color: var(--theme-primary);
  font-weight: bold;
}

.goods-description {
  color: #333;
  line-height: 1.6;
}

.goods-description img {
  max-width: 100% !important;
  height: auto;
}

/* 底部按钮 */
.goods-footer {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 640px;
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0 15px;
  z-index: 1000;
}

.footer-button {
  flex: 1;
  height: 40px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.footer-button:hover {
  background: var(--theme-hover);
  transform: translateY(-2px);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8f8f8;
}

.loading-text {
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #f8f8f8;
  padding: 20px;
}

.error-text {
  color: #999;
  font-size: 14px;
  text-align: center;
  margin-bottom: 20px;
}

.retry-button {
  padding: 10px 20px;
  background: var(--theme-primary);
  color: #fff;
  border: none;
  border-radius: 20px;
  cursor: pointer;
}
