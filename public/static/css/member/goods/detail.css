/* 商品详情页面样式 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Aria<PERSON>, sans-serif;
  background: #f5f5f5;
  min-height: 100vh;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 隐藏未编译的Vue模板 */
[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--theme-primary, #ff7830);
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
}

/* 页面容器 */
.page-container {
  flex: 1;
  background: #fff;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 80px;
}

/* 轮播图样式 */
.swiper {
  width: 100%;
  background: #f8f9fa;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swiper-pagination {
  bottom: 10px !important;
}

.swiper-pagination-bullet {
  background: rgba(255, 255, 255, 0.5);
  opacity: 1;
}

.swiper-pagination-bullet-active {
  background: #fff;
}

/* 商品信息区域 */
.goods-info {
  padding: 0;
}

/* 商品名称区域 */
.name-box {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.goods-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

/* SKU已选规格显示 */
.selected-sku-info {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  cursor: pointer;
  color: #666;
}

.sku-info-label {
  font-size: 0.9rem;
  margin-right: 0.5rem;
}

.goods-specs {
  flex: 1;
  font-size: 0.9rem;
  color: var(--theme-primary, #ff7830);
  background: rgba(255, 120, 48, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.selected-sku-info i {
  margin-left: 0.5rem;
  color: #999;
}

/* 价格区域 */
.price-box {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label {
  font-size: 0.9rem;
  color: #666;
}

.price-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--theme-primary, #ff7830);
}

/* 库存区域 */
.stock-box {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-label {
  font-size: 0.9rem;
  color: #666;
}

.stock-value {
  font-size: 1rem;
  color: #333;
}

/* 规格区域 */
.specs-box {
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.specs-item {
  display: flex;
  align-items: center;
}

.specs-label {
  font-size: 0.9rem;
  color: #666;
  margin-right: 0.5rem;
}

.specs-value {
  background: rgba(255, 120, 48, 0.1);
  color: #ff7830;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* 商品描述区域 */
.description-box {
  padding: 1rem;
}
.description-content {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #666;
}

.description-content img {
  max-width: 100%;
  height: auto;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 640px;
  background: #fff;
  padding: 1rem;
  border-top: 1px solid #eee;
  z-index: 100;
}

.action-btn {
  width: 100%;
  padding: 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.action-btn.primary {
  background: var(--theme-primary, #ff7830);
  color: #fff;
}

.action-btn.primary:hover {
  background: var(--theme-hover, #e66a2a);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--theme-shadow, rgba(255, 120, 48, 0.3));
}

.action-btn.disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
}

.action-btn:disabled {
  background: #ccc;
  color: #999;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 固定工具栏 */
.fixed-toolbar {
  position: fixed;
  right: 1rem;
  bottom: 100px;
  z-index: 99;
}

.toolbar-item {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toolbar-item:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.toolbar-item i {
  font-size: 1.2rem;
}

/* SKU选择弹窗 */
.sku-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.sku-modal {
  background: #fff;
  width: 100%;
  max-width: 640px;
  max-height: 80vh;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* SKU弹窗头部 */
.sku-modal-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
  position: relative;
}

.sku-modal-product {
  display: flex;
  align-items: center;
  flex: 1;
}

.sku-modal-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 1rem;
}

.sku-modal-info {
  flex: 1;
}

.sku-modal-name {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.sku-modal-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ff7830;
  margin-bottom: 0.25rem;
}

.sku-modal-stock {
  font-size: 0.8rem;
  color: #666;
}

.sku-modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.5rem;
  color: #999;
}

/* SKU属性选择区域 */
.sku-modal-content {
  padding: 1rem;
  max-height: 40vh;
  overflow-y: auto;
}

.sku-attr-group {
  margin-bottom: 1.5rem;
}

.sku-attr-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.75rem;
}

.sku-attr-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sku-attr-option {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.sku-attr-option:hover {
  border-color: var(--theme-primary, #ff7830);
  color: var(--theme-primary, #ff7830);
}

.sku-attr-option.selected {
  border-color: var(--theme-primary, #ff7830);
  background: var(--theme-primary, #ff7830);
  color: #fff;
}

.sku-attr-option.disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
  border-color: #eee;
}

.sku-attr-option.disabled:hover {
  border-color: #eee;
  color: #ccc;
}

/* SKU弹窗底部 */
.sku-modal-footer {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-top: 1px solid #eee;
  background: #f8f9fa;
}

.sku-quantity-selector {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.sku-quantity-label {
  font-size: 0.9rem;
  color: #666;
  margin-right: 0.75rem;
}

.sku-quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
}

.sku-quantity-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #fff;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sku-quantity-btn:hover {
  background: #f5f5f5;
}

.sku-quantity-btn.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.sku-quantity-btn.disabled:hover {
  background: #fff;
}

.sku-quantity-display {
  width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  font-size: 0.9rem;
  color: #333;
}

.sku-confirm-btn {
  flex: 1;
  padding: 0.75rem;
  background: var(--theme-primary, #ff7830);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sku-confirm-btn:hover {
  background: var(--theme-hover, #e66a2a);
}

.sku-confirm-btn.disabled {
  background: #ccc;
  cursor: not-allowed;
}

.sku-confirm-btn.disabled:hover {
  background: #ccc;
}

/* 错误状态 */
.error-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #fff;
}

.error-content {
  text-align: center;
  color: #666;
  padding: 2rem;
}

.error-content i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #ddd;
}

.error-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #666;
}

.error-content p {
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
  color: #999;
}

.retry-btn {
  padding: 0.75rem 2rem;
  background: #ff7830;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e66a2a;
}

/* 响应式设计 */

/* 滚动条样式 */
.sku-modal-content::-webkit-scrollbar {
  width: 4px;
}

.sku-modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sku-modal-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.sku-modal-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 主题适配 */
.theme-transition {
  transition: all 0.3s ease;
}

/* 深色模式支持 */
