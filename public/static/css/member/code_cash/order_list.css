/**
 * WeUI到LayUI重构 - code_cash/order_list.html 样式文件
 * 现金订单列表页面样式
 */

/* Vue.js隐藏未渲染内容 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.order-list-container {
  min-height: 100vh;
  background: var(--theme-bg-color, #ededed);
}

/* 搜索框样式 */
.search-section {
  background: #f2f2f2;
  padding: 9px 15px;
  position: relative;
  z-index: 3;
  border-bottom: 1px solid #e2e2e2;
}

.search-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  height: 35px;
  padding: 6px 15px 6px 35px;
  background: #fefefe;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  font-size: 14px;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #999;
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

.search-button {
  height: 35px;
  padding: 0 15px;
  background: var(--theme-primary, #07c160);
  color: #fff;
  border: none;
  border-radius: 3px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.search-button:hover {
  background: var(--theme-primary-hover, #06ad56);
}

/* 订单列表 */
.order-list {
  padding: 0;
}

/* 订单卡片 */
.order-card {
  background: #fff;
  margin-bottom: 10px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  color: #999;
  font-size: 14px;
}

.order-status {
  font-size: 14px;
  font-weight: 500;
}

.order-status.rejected {
  color: #ff5722;
}

.order-status.pending {
  color: #1e9fff;
}

.order-status.approved {
  color: #07c160;
}

.order-status.paid {
  color: #07c160;
}

/* 订单内容 */
.order-content {
  padding: 15px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-info-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.order-info-label {
  font-weight: 500;
  color: #333;
  min-width: 60px;
}

.order-info-value {
  color: #666;
  flex: 1;
}

/* 订单底部 */
.order-footer {
  padding: 12px 15px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-time-label {
  font-size: 12px;
  color: #999;
}

.order-time-value {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

/* 加载状态 */
.loading-section {
  padding: 20px;
  text-align: center;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: var(--theme-primary, #07c160);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--theme-primary, #07c160);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
}

/* 无更多数据 */
.no-more-section {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
  position: relative;
}

.no-more-section::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 20px;
  right: 20px;
  height: 1px;
  background: #e0e0e0;
  z-index: 1;
}

.no-more-text {
  background: var(--theme-bg-color, #ededed);
  padding: 0 15px;
  position: relative;
  z-index: 2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 80px;
  color: #d9d9d9;
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  color: #999;
  margin: 0 0 10px 0;
}

.empty-description {
  font-size: 14px;
  color: #ccc;
  margin: 0;
}

/* 响应式设计 */

/* 动画效果 */
.order-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* LayUI图标支持 */
.layui-icon {
  font-family: layui-icon !important;
  font-size: inherit;
  font-style: normal;
}

/* 主题适配 */
