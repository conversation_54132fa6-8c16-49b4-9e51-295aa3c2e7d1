/* 用户中心页面样式 - 重新设计 */

/* 主题色变量 */

/* 基础重置和全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  border: none;
  outline: none;
  cursor: pointer;
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
}

[v-cloak] {
  display: none;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 16px;
  color: var(--theme-primary);
}

.loading-content h4 {
  font-weight: normal;
  font-size: 16px;
}

/* 主容器 */
.flex-view {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.scroll-view {
  padding-bottom: 20px;
}

/* 用户头部区域 */
.take-head {
  position: relative;
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  padding: 20px 16px 60px;
  color: white;
  overflow: hidden;
}

.take-head img {
  position: absolute;
  right: -20px;
  bottom: -10px;
  width: 120px;
  height: auto;
  opacity: 0.3;
}

.flex-one {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 12px;
}

.take-user {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
  background-color: rgba(255, 255, 255, 0.1);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.flex-box {
  flex: 1;
}

.flex-box span {
  display: block;
  color: white;
}

.flex-box span:first-child {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 4px;
}

.flex-box span:last-child {
  font-size: 14px;
  opacity: 0.9;
}

.icon-phone::before {
  content: "📱";
  margin-right: 4px;
}

.icon::before {
  margin-right: 4px;
}

/* 统计卡片 */
.flex-three {
  margin: -30px 16px 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.flex-three a {
  display: block;
  padding: 24px;
  text-align: center;
  transition: background-color 0.2s;
}

.flex-three a:hover {
  background-color: #f8f9fa;
}

.flex-box-info span {
  display: block;
  color: #999;
  font-size: 14px;
  margin-bottom: 8px;
}

.flex-box-info h4 {
  font-size: 36px;
  font-weight: 600;
  color: var(--theme-text);
  margin: 0;
}

/* 内容卡片 */
.take-item {
  margin: 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--theme-shadow);
  overflow: hidden;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.flex h1 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.arrow a {
  color: #999;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s;
}

.arrow a:hover {
  color: var(--theme-hover);
}

.arrow a::after {
  content: "›";
  font-size: 16px;
  color: #ccc;
}

/* 宫格布局 */
.palace {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.palace-grid {
  flex: 1;
  text-align: center;
  padding: 16px 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.palace-grid:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
}

.palace-grid-icon {
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
}

.palace-grid-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.palace-grid-text h2 {
  font-size: 14px;
  font-weight: normal;
  color: #666;
  transition: color 0.2s;
}

.palace-grid:hover .palace-grid-text h2 {
  color: var(--theme-text);
}

/* 按钮样式 */
.take-button button {
  background: var(--theme-primary);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  transition: background-color 0.2s;
}

.take-button button:hover {
  background: var(--theme-hover);
}

/* 响应式设计 */

/* 隐藏元素的通用样式 */
[style*="display: none"] {
  display: none !important;
}

/* 链接悬浮效果 */
a:hover {
  color: var(--theme-hover);
}

/* 统计卡片悬浮效果增强 */
.flex-three a:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--theme-shadow);
}

/* 修复内联样式覆盖问题 */
.flex-three[style*="margin"] {
  margin: -30px 16px 16px !important;
}

.flex-three[style*="display: flex"] {
  display: block !important;
}

/* 确保卡片阴影使用主题色 */
.flex-three {
  box-shadow: 0 2px 12px var(--theme-shadow);
}

.take-item {
  box-shadow: 0 2px 8px var(--theme-shadow);
}

/* 头像样式增强 - 背景图片方式 */
.take-user {
  /* 防止被选中 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 添加过渡效果 */
  transition: transform 0.2s ease;
}

.take-user:hover {
  transform: scale(1.05);
}

/* 优化移动端显示 */
