/* 兑换记录页面样式 */
[v-cloak] {
  display: none !important;
}

.exchange-records-container {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--theme-primary-light) 0%, var(--theme-primary) 100%);
  padding-bottom: 60px;
}

/* 头部标题区域 */
.header-section {
  background: var(--theme-primary);
  color: white;
  padding: 20px;
  text-align: center;
  box-shadow: var(--theme-shadow);
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 5px;
}

/* 记录列表区域 */
.records-section {
  padding: 20px;
}

.records-list {
  background: white;
  border-radius: 12px;
  box-shadow: var(--theme-shadow);
  overflow: hidden;
}

/* 记录项 */
.record-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-item:hover {
  background-color: #f8f9fa;
}

.record-left {
  flex: 1;
}

.record-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.record-time {
  font-size: 13px;
  color: #999;
}

.record-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-amount {
  font-size: 18px;
  font-weight: bold;
  color: var(--theme-primary);
}

.record-status {
  font-size: 20px;
}

.status-pending {
  color: #ffa500;
}

.status-success {
  color: #28a745;
}

.status-failed {
  color: #dc3545;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: var(--theme-shadow);
  margin: 20px;
}

.empty-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: var(--theme-shadow);
  margin: 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-left: 12px;
  color: #666;
  font-size: 14px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 下拉刷新 */
.refresh-container {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
}

.refresh-icon {
  font-size: 18px;
  margin-bottom: 8px;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  z-index: 1000;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  color: #999;
  text-decoration: none;
  transition: color 0.3s ease;
  cursor: pointer;
}

.nav-item.active {
  color: var(--theme-primary);
}

.nav-item:hover {
  color: var(--theme-hover);
}

.nav-item i {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-item span {
  font-size: 12px;
}

/* 响应式设计 */

/* 深色主题支持 */
