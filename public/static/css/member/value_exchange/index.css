/* 储值提现页面样式 */

[v-cloak] {
  display: none !important;
}

.value-exchange-app {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 主容器 */
.main-container {
  padding: 20px;
  max-width: 500px;
  margin: 0 auto;
}

/* 表单卡片 */
.form-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 20px;
}

.form-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  padding: 24px;
  text-align: center;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.form-content {
  padding: 24px;
}

/* 金额输入区域 */
.amount-section {
  margin-bottom: 24px;
}

.amount-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 12px;
  display: block;
}

.amount-input-container {
  position: relative;
  margin-bottom: 12px;
}

.currency-symbol {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24px;
  color: var(--primary-color);
  font-weight: 600;
}

.amount-input {
  width: 100%;
  height: 56px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 0 16px 0 48px;
  font-size: 24px;
  font-weight: 500;
  color: var(--text-color);
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.amount-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(87, 107, 158, 0.1);
}

.balance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.balance-text {
  color: var(--text-color);
}

.balance-amount {
  color: var(--primary-light);
  font-weight: 500;
}

.withdraw-all-btn {
  color: var(--primary-color);
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.withdraw-all-btn:hover {
  background: rgba(87, 107, 158, 0.1);
}

/* 提现方式选择 */
.withdraw-method-section {
  margin-bottom: 24px;
}

.method-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 12px;
  display: block;
}

.method-options {
  space-y: 8px;
}

.method-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.method-option:hover {
  border-color: var(--primary-color);
  background: rgba(87, 107, 158, 0.05);
}

.method-option.selected {
  border-color: var(--primary-color);
  background: rgba(87, 107, 158, 0.1);
}

.method-text {
  font-size: 15px;
  color: var(--text-color);
  font-weight: 500;
}

.method-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s ease;
}

.method-option.selected .method-radio {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.method-option.selected .method-radio::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

/* 提交按钮 */
.submit-section {
  margin-bottom: 16px;
}

.submit-btn {
  width: 100%;
  height: 52px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: var(--success-light);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-btn:not(:disabled):hover {
  background: var(--success-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(4, 190, 2, 0.3);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn.loading {
  background: var(--success-light);
}

.submit-btn .loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.submit-tips {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e1e5e9;
  display: flex;
  z-index: 100;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  text-decoration: none;
  color: #666;
  transition: color 0.2s ease;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
}

/* 成功状态 */
.success-container {
  text-align: center;
  padding: 40px 24px;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: var(--success-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  animation: successPulse 0.6s ease-out;
}

.success-icon i {
  font-size: 40px;
  color: white;
}

@keyframes successPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 12px 0;
}

.success-desc {
  font-size: 16px;
  color: #666;
  margin: 0 0 32px 0;
}

.success-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-btn:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 32px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-spinner-large {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.loading-text {
  font-size: 16px;
  color: var(--text-color);
  margin: 0;
}

/* 响应式设计 */
