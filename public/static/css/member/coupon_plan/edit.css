/**
 * WeUI到LayUI重构 - coupon_plan/edit.html 样式文件
 * 优惠券方案编辑页面
 */

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none;
}

/* 页面容器 */
.coupon-plan-edit-container {
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
  padding: 0;
}

/* 表单区域 */
.form-section {
  background: #fff;
  margin: 0;
  padding: 20px;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--theme-text-color, #333);
  margin: 0;
}

.form-description {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin-top: 8px;
  line-height: 1.5;
}

/* 表单控件 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-color, #333);
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text-color, #333);
  background: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-input::placeholder {
  color: #999;
}

.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  color: var(--theme-text-color, #333);
  background: #fff;
  resize: vertical;
  min-height: 120px;
  line-height: 1.5;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-textarea::placeholder {
  color: #999;
}

/* 操作按钮区域 */
.action-section {
  padding: 20px;
  background: #fff;
  border-top: 1px solid #eee;
  position: sticky;
  bottom: 0;
  z-index: 10;
  display: flex;
  gap: 12px;
}

.submit-button {
  flex: 1;
  padding: 14px 20px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  line-height: 1.4;
}

.submit-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.submit-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.submit-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancel-button {
  flex: 1;
  padding: 14px 20px;
  background: #fff;
  color: var(--theme-text-color, #333);
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  line-height: 1.4;
}

.cancel-button:hover {
  background: #f5f5f5;
  border-color: #ccc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cancel-button:active {
  transform: translateY(0);
  box-shadow: none;
}

/* 加载状态 */
.loading-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  background: #fff;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */

/* 主题适配 */
