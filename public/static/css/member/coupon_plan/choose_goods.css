/**
 * WeUI到LayUI重构 - coupon_plan/choose_goods.html 样式文件
 * 选择商品页面样式
 */

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page,
body {
  background-color: var(--theme-bg-color, #f8f9fa);
}

/* 分类选择框 */
.cat_box {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 10px 0;
  background: var(--theme-card-bg, #fff);
}

.cat {
  display: inline-block;
  padding: 8px 16px;
  margin: 0 5px;
  border-radius: 20px;
  background: var(--theme-bg-secondary, #f5f5f5);
  color: var(--theme-text-primary, #333);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.cat:hover {
  background: var(--theme-primary-light, #e6f7ff);
  border-color: var(--theme-primary, #1890ff);
}

/* 价格筛选框 */
.price_box {
  display: flex;
  overflow: auto;
  background-color: var(--theme-card-bg, #fff);
  z-index: 1;
  padding: 10px 0;
  border-bottom: 1px solid var(--theme-border, #e8e8e8);
}

.price {
  flex-shrink: 0;
  padding: 8px 16px;
  margin: 0 5px;
  border-radius: 20px;
  background: var(--theme-bg-secondary, #f5f5f5);
  color: var(--theme-text-primary, #333);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.price:hover {
  background: var(--theme-primary-light, #e6f7ff);
  border-color: var(--theme-primary, #1890ff);
}

/* 选中状态 */
.red {
  color: var(--theme-danger, #ff4d4f) !important;
  background: var(--theme-danger-light, #fff2f0) !important;
  border-color: var(--theme-danger, #ff4d4f) !important;
}

/* 搜索按钮 */
.search-btn {
  background: var(--theme-primary, #1890ff);
  border: 0 none;
  border-radius: 4px;
  color: #ffffff;
  cursor: pointer;
  line-height: 36px;
  padding: 0 16px;
  text-align: center;
  white-space: nowrap;
  font-weight: 500;
  font-size: 14px;
  display: inline-block;
  transition: all 0.3s ease;
  min-width: 80px;
}

.search-btn:hover {
  background: var(--theme-primary-dark, #096dd9);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

/* 搜索框样式 */
.search-box {
  background: var(--theme-card-bg, #fff);
  padding: 15px;
  border-bottom: 1px solid var(--theme-border, #e8e8e8);
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-box input {
  flex: 1;
  background: var(--theme-bg-secondary, #f5f5f5);
  border-radius: 20px;
  border: 1px solid var(--theme-border, #d9d9d9);
  padding: 8px 16px;
  font-size: 14px;
  color: var(--theme-text-primary, #333);
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  background: var(--theme-card-bg, #fff);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-box input::placeholder {
  color: var(--theme-text-secondary, #999);
}

/* 滚动标签页 */
.m-scrolltab {
  background: var(--theme-card-bg, #fff);
}

.scrolltab-nav {
  display: flex;
  overflow-x: auto;
  background: var(--theme-card-bg, #fff);
  border-bottom: 1px solid var(--theme-border, #e8e8e8);
  padding: 0 15px;
}

.scrolltab-item {
  flex-shrink: 0;
  padding: 15px 20px;
  color: var(--theme-text-secondary, #666);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.scrolltab-item:hover {
  color: var(--theme-primary, #1890ff);
}

.scrolltab-item.crt {
  color: var(--theme-primary, #1890ff);
  border-bottom-color: var(--theme-primary, #1890ff);
}

.scrolltab-title {
  white-space: nowrap;
}

/* 商品列表 */
.scrolltab-content {
  background: var(--theme-bg-color, #f8f9fa);
}

.scrolltab-content-item {
  padding: 15px;
  background: var(--theme-card-bg, #fff);
  margin-bottom: 10px;
}

.sit-list {
  margin-top: 15px;
}

/* 商品项 */
.flex {
  display: flex;
  align-items: center;
  padding: 15px;
  background: var(--theme-card-bg, #fff);
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.flex:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.comm-user {
  flex-shrink: 0;
  margin-right: 15px;
}

.comm-user img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid var(--theme-border, #e8e8e8);
}

.flex-box {
  flex: 1;
  min-width: 0;
}

.flex-box h2 {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-primary, #333);
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-box p {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-danger, #ff4d4f);
  margin: 0;
}

/* 操作按钮 */
.follow {
  flex-shrink: 0;
  margin-left: 15px;
}

.follow span {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
  background: transparent;
}

.follow span i {
  margin-right: 2px;
  font-style: normal;
}

/* 添加按钮 */
.follow span[style*="color: #0BB20C"] {
  color: var(--theme-success, #52c41a) !important;
  border-color: var(--theme-success, #52c41a) !important;
}

.follow span[style*="color: #0BB20C"]:hover {
  background: var(--theme-success, #52c41a) !important;
  color: #fff !important;
}

/* 移除按钮 */
.follow span[style*="color: red"] {
  color: var(--theme-danger, #ff4d4f) !important;
  border-color: var(--theme-danger, #ff4d4f) !important;
}

.follow span[style*="color: red"]:hover {
  background: var(--theme-danger, #ff4d4f) !important;
  color: #fff !important;
}

/* 底部边线 */
.b-line {
  position: relative;
}

.b-line:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 1px;
  background: var(--theme-border, #e8e8e8);
  transform: scaleY(0.5);
}

/* 响应式设计 */

/* 暗色主题支持 */
