/**
 * WeUI到LayUI重构 - coupon_plan/list.html 样式文件
 * 优惠券方案列表页面样式
 */

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.coupon-plan-list-container {
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
  padding: 0;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  background: #fff;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: var(--theme-text-secondary, #666);
  font-size: 14px;
}

/* 搜索区域 */
.search-section {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
  background: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 16px;
}

.search-button {
  padding: 10px 20px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.search-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

.search-button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
  transform: none;
}

/* 列表区域 */
.list-section {
  padding: 12px;
}

.plan-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.plan-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.plan-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.plan-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--theme-primary, #1890ff), #40a9ff);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-id {
  font-size: 12px;
  opacity: 0.9;
  margin: 0;
}

.plan-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.plan-content {
  padding: 20px;
}

.plan-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary, #333);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.plan-description {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.plan-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.goods-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--theme-primary, #1890ff);
  font-weight: 600;
}

.create-time {
  font-size: 12px;
  color: var(--theme-text-tertiary, #999);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #fff;
  margin: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 20px;
}

.empty-title {
  font-size: 18px;
  color: var(--theme-text-secondary, #666);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--theme-text-tertiary, #999);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.empty-button {
  padding: 12px 24px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.empty-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.add-button {
  width: 100%;
  padding: 12px;
  background: var(--theme-success, #52c41a);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: block;
  text-align: center;
}

.add-button:hover {
  background: var(--theme-success-hover, #73d13d);
  transform: translateY(-1px);
}

/* 响应式设计 */

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.list-enter-active, .list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from, .list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 主题适配 */

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* box-sizing: border-box; */
}

[v-cloak] {
  display: none;
}

.weui-wa-hotarea {
  margin: 0 10px 0 10px;
  width: 25%;
}

.page,
body {
  background-color: var(--weui-BG-0);
}

.icon {
  width: 20px;
  height: 20px;
  display: block;
  border: none;
  float: left;
  background-size: 20px;
  background-repeat: no-repeat;
}

.search-box {
  background: #f2f2f2;
  padding: 9px 15px;
  position: relative;
  z-index: 3;
}

.search-box input {
  background: #fefefe;
  border-radius: 3px;
  border: 1px solid #dcdcdc;
  width: 100%;
  padding: 6px 25px;
  font-size: 0.85rem;
}

.icon-search {
  position: absolute;
  left: 20px;
  background-image: url("data:image/png;base64,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");
  top: 18px;
  background-size: 18px;
}

.search-btn {
  background: none repeat scroll 0 0 #07c160;
  border: 0 none;
  border-radius: 0;
  color: #ffffff;
  cursor: pointer;
  /*height: 35px;*/
  line-height: 33px;
  padding: 0;
  vertical-align: baseline !important;
  width: 18%;
  margin-left: 2%;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  margin-bottom: 0;
  font-weight: normal;
  font-size: 14px;
  display: inline-block;
  position: relative;
}

.b-line {
  position: relative;
}

.b-line:after {
  content: "";
  position: absolute;
  z-index: 2;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  border-bottom: 1px solid #e2e2e2;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
}
