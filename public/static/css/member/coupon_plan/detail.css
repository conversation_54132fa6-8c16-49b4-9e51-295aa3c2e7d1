/**
 * WeUI到LayUI重构 - coupon_plan/detail.html 样式文件
 * 优惠券方案详情页面样式
 */

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.coupon-plan-detail-container {
  min-height: 100vh;
  background: var(--theme-bg-color, #f8f9fa);
  padding: 0;
  padding-bottom: 80px; /* 为底部按钮留出空间 */
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  background: #fff;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--theme-primary, #1890ff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--theme-text-secondary, #666);
  font-size: 14px;
}

/* 信息卡片 */
.info-card {
  background: #fff;
  margin: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.header-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.header-button.danger {
  background: #ff4d4f;
  border-color: #ff4d4f;
}

.header-button.danger:hover {
  background: #ff7875;
}

.card-content {
  padding: 20px;
}

/* 基本信息 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: var(--theme-text-primary, #333);
  min-width: 80px;
  font-size: 14px;
}

.info-value {
  flex: 1;
  text-align: right;
  color: var(--theme-text-secondary, #666);
  font-size: 14px;
  word-break: break-all;
}

.info-value.primary {
  color: var(--theme-primary, #1890ff);
  font-weight: 600;
}

.info-value.description {
  text-align: left;
  margin-left: 12px;
  line-height: 1.5;
}

/* 商品列表 */
.goods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.goods-count {
  color: #ff4d4f;
  font-size: 18px;
  font-weight: 600;
}

.goods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.goods-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.goods-item:hover {
  background: #f0f0f0;
  transform: translateY(-1px);
}

.goods-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text-primary, #333);
  margin: 0;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: #fff;
  margin: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  color: var(--theme-text-secondary, #666);
  margin: 0 0 20px 0;
}

.empty-button {
  padding: 10px 24px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.empty-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.back-button {
  width: 100%;
  padding: 12px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: block;
  text-align: center;
}

.back-button:hover {
  background: var(--theme-primary-hover, #40a9ff);
  transform: translateY(-1px);
}

/* 响应式设计 */

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 主题适配 */
