/* 余额兑换页面样式 */
[v-cloak] {
  display: none !important;
}

.page-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

/* 表单区域 */
.form-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out;
}

.input-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.input-item:last-child {
  margin-bottom: 0;
}

.input-label {
  width: 80px;
  font-size: 16px;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
}

.input-field {
  flex: 1;
  height: 45px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 16px;
  color: #333;
  background: #ffffff;
  transition: all 0.3s ease;
}

.input-field:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px var(--theme-shadow);
  outline: none;
}

.input-field:disabled {
  background: #f8f9fa;
  color: #666;
  cursor: not-allowed;
}

.input-field::placeholder {
  color: #999;
}

/* 金额选择区域 */
.amount-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.amount-item {
  height: 45px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: #333;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
}

.amount-item:hover {
  border-color: var(--theme-hover);
  background: rgba(24, 144, 255, 0.05);
}

.amount-item.selected {
  border-color: var(--theme-primary);
  background: var(--theme-primary);
  color: #ffffff;
  position: relative;
}

.amount-item.selected::after {
  content: "✓";
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  font-weight: bold;
}

/* 充值按钮 */
.submit-section {
  margin: 15px;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.submit-btn {
  width: 100%;
  height: 50px;
  background: var(--theme-primary);
  color: #ffffff;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:hover:not(:disabled) {
  background: var(--theme-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--theme-shadow);
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-btn .loading {
  width: 18px;
  height: 18px;
  border: 2px solid #ffffff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 提示信息 */
.tips-section {
  background: #ffffff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.tips-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-item {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 15px;
  position: relative;
}

.tips-item:last-child {
  margin-bottom: 0;
}

.tips-item::before {
  content: "•";
  position: absolute;
  left: 0;
  color: var(--theme-primary);
  font-weight: bold;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background: #ffffff;
  border-top: 1px solid #e0e0e0;
  display: flex;
  z-index: 100;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  color: #666;
  transition: all 0.3s ease;
}

.nav-item.active {
  color: var(--theme-primary);
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
