/* 优惠券兑换页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
}

/* 页面标题 */
.page-title {
  background: var(--theme-primary, #1890ff);
  color: #fff;
  text-align: center;
  padding: 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 表单容器 */
.form-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: var(--theme-text, #333);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
}

.form-input::placeholder {
  color: var(--theme-text-placeholder, #999);
}

/* 金额选择器 */
.amount-selector {
  margin: 24px 0;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.amount-option {
  height: 44px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  background: #fff;
  color: var(--theme-text, #333);
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.amount-option:hover {
  border-color: var(--theme-primary, #1890ff);
}

.amount-option.selected {
  border-color: var(--theme-primary, #1890ff);
  background: var(--theme-primary-light, #e6f7ff);
  color: var(--theme-primary, #1890ff);
}

.amount-option.selected::after {
  content: '✓';
  position: absolute;
  right: 8px;
  bottom: 4px;
  font-size: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 48px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 24px;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background: var(--theme-primary-dark, #1677cc);
}

.submit-button:disabled {
  background: var(--theme-disabled, #ccc);
  cursor: not-allowed;
}

/* 注意事项 */
.notice-section {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.notice-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 16px;
}

.notice-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notice-item {
  font-size: 13px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 0;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.amount-option:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.submit-button:active {
  transform: scale(0.98);
}

/* 错误状态 */
.form-input.error {
  border-color: #ff4d4f;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--theme-text-placeholder, #ccc);
}
