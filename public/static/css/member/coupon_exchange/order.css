/* 订单页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
}

/* 页面标题 */
.page-title {
  background: var(--theme-primary, #1890ff);
  color: #fff;
  text-align: center;
  padding: 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 标签页 */
.tab-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.tab-header {
  display: flex;
  background: var(--theme-background-light, #f8f9fa);
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
}

.tab-item {
  flex: 1;
  padding: 16px 12px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-secondary, #666);
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-item.active {
  color: var(--theme-primary, #1890ff);
  background: #fff;
  border-bottom-color: var(--theme-primary, #1890ff);
}

.tab-item:hover {
  color: var(--theme-primary, #1890ff);
}

/* 标签页内容 */
.tab-content {
  padding: 20px;
  min-height: 400px;
}

/* 订单列表 */
.order-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-item {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  transition: box-shadow 0.3s ease;
}

.order-item:hover {
  box-shadow: 0 4px 12px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme-border-light, #f0f0f0);
}

.order-id {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
}

.order-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.order-status.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.order-status.pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.order-status.failed {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.order-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.order-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--theme-primary-light, #e6f7ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme-primary, #1890ff);
  font-size: 20px;
}

.order-info {
  flex: 1;
}

.order-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
}

.order-desc {
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  margin-bottom: 8px;
}

.order-time {
  font-size: 12px;
  color: var(--theme-text-placeholder, #999);
}

.order-amount {
  text-align: right;
}

.order-price {
  font-size: 18px;
  font-weight: 600;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 4px;
}

.order-type {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 16px;
  color: var(--theme-text-placeholder, #ccc);
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: var(--theme-text-secondary, #666);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
  color: var(--theme-text-placeholder, #999);
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.tab-item:active {
  transform: scale(0.98);
}

.order-item:active {
  transform: scale(0.98);
}

/* 加载更多按钮 */
.load-more-button {
  width: 100%;
  height: 44px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.3s ease;
}

.load-more-button:hover {
  background: var(--theme-primary-dark, #1677cc);
}

.load-more-button:disabled {
  background: var(--theme-disabled, #ccc);
  cursor: not-allowed;
}
