/* 储值卡兑换页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
}

/* 页面标题 */
.page-title {
  background: var(--theme-primary, #1890ff);
  color: #fff;
  text-align: center;
  padding: 16px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 公告区域 */
.notice-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
  display: flex;
  align-items: center;
  gap: 12px;
}

.notice-icon {
  width: 20px;
  height: 20px;
  color: var(--theme-primary, #1890ff);
  font-size: 16px;
}

.notice-content {
  flex: 1;
  font-size: 14px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
}

/* 表单容器 */
.form-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

/* 表单项 */
.form-item {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text, #333);
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 16px;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  font-size: 16px;
  background: #fff;
  color: var(--theme-text, #333);
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-primary, #1890ff);
}

.form-input::placeholder {
  color: var(--theme-text-placeholder, #999);
}

/* 密码输入框 */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--theme-text-secondary, #666);
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
}

.password-toggle:hover {
  color: var(--theme-primary, #1890ff);
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 48px;
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 24px;
  transition: background-color 0.3s ease;
}

.submit-button:hover {
  background: var(--theme-primary-dark, #1677cc);
}

.submit-button:disabled {
  background: var(--theme-disabled, #ccc);
  cursor: not-allowed;
}

/* 温馨提醒区域 */
.tips-container {
  background: #fff;
  margin: 16px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
}

.tips-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  position: relative;
}

.tips-title::before,
.tips-title::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--theme-border, #e5e5e5);
}

.tips-title span {
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-secondary, #666);
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-item {
  padding: 8px 0;
  font-size: 13px;
  color: var(--theme-text-secondary, #666);
  line-height: 1.5;
  position: relative;
  padding-left: 16px;
}

.tips-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--theme-primary, #1890ff);
  font-weight: bold;
}

/* 错误状态 */
.form-input.error {
  border-color: #ff4d4f;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

/* 成功状态 */
.success-message {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.submit-button:active {
  transform: scale(0.98);
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* 输入框聚焦动画 */
.form-input:focus {
  box-shadow: 0 0 0 2px var(--theme-primary-light, rgba(24, 144, 255, 0.2));
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
