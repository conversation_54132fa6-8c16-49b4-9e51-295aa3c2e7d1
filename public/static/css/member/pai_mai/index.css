/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面基础样式 */
body {
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

html,
body {
  box-sizing: border-box;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #fff;
}

.loading-content {
  text-align: center;
  color: #666;
}

.loading-content i {
  font-size: 32px;
  color: var(--theme-primary, #1890ff);
  margin-bottom: 16px;
}

.loading-content h4 {
  margin: 0;
  font-weight: normal;
  color: #999;
}

/* 拍卖容器 */
.auction-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* 左侧分类栏 */
.category-sidebar {
  width: 25%;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  background: #fff;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
}

.category-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.category-item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme-primary, #0a6aa1);
  border-bottom: 1px solid #e9ecef;
  transition: all 0.3s ease;
  text-align: center;
}

.category-item:hover {
  background-color: #e8f4fd;
}

.category-item.active {
  background-color: #e8e8e8;
  color: #000;
  border-left: 3px solid var(--theme-primary, #3399ff);
  font-weight: 600;
}

/* 右侧商品内容 */
.goods-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 活动信息头部 */
.activity-header {
  background: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.end-time {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.time-value {
  font-size: 14px;
  font-weight: 600;
  color: #ff4d4f;
}

.activity-image img {
  height: 30px;
  object-fit: contain;
}

/* 商品表格容器 */
.goods-table-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.goods-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.goods-table thead {
  background: #f8f9fa;
}

.goods-table th {
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: var(--theme-text, #333);
  border-bottom: 1px solid #e9ecef;
}

.goods-table th:nth-child(1) { width: 15%; }
.goods-table th:nth-child(2) { width: 50%; }
.goods-table th:nth-child(3) { width: 15%; }
.goods-table th:nth-child(4) { width: 20%; }

.goods-row {
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.goods-row:hover {
  background-color: #f8f9fa;
}

.goods-row:last-child {
  border-bottom: none;
}

.goods-table td {
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.goods-title {
  text-align: left !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 0;
}

/* 出价按钮 */
.offer-btn {
  background: var(--theme-primary, #ff4d4f);
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.offer-btn:hover {
  background: var(--theme-hover, #ff7875);
  transform: translateY(-1px);
}

.offer-btn.has-offer {
  background: var(--theme-primary, #52c41a);
}

.offer-btn.has-offer:hover {
  background: var(--theme-hover, #73d13d);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state i {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 加载更多 */
.load-more {
  padding: 16px;
  text-align: center;
}

.load-more-btn {
  background: var(--theme-primary, #1890ff);
  color: #fff;
  border: none;
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: var(--theme-hover, #40a9ff);
}

.load-more-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

/* 响应主题变化的动画 */
.sidebar-header h3,
.category-item,
.time-value,
.goods-table th,
.offer-btn,
.load-more-btn {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端适配 */
