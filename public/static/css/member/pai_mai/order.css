/* 拍卖订单页面样式 - LayUI + Vue.js 3 */

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--theme-background, #f5f5f5);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: var(--theme-text, #333);
}

/* Vue.js v-cloak 指令样式 */
[v-cloak] {
  display: none !important;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  padding-bottom: 60px; /* 为底部导航留空间 */
  background: #f0eff5;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
  color: var(--theme-text-secondary, #666);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 12px;
  color: var(--theme-primary, #1890ff);
}

/* 标签页容器 */
.tab-container {
  background: #fff;
  margin: 0;
  border-radius: 0;
}

/* 标签页头部 */
.tab-header {
  display: flex;
  background: #fff;
  border-bottom: 1px solid var(--theme-border, #e5e5e5);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16px 12px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-text-secondary, #666);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: var(--theme-primary, #1890ff);
  background: #fff;
  border-bottom-color: var(--theme-primary, #1890ff);
}

.tab-item:hover {
  color: var(--theme-primary, #1890ff);
  background: var(--theme-hover, #f8f9fa);
}

/* 标签页内容 */
.tab-content {
  background: #f0eff5;
  min-height: 60vh;
  padding: 16px;
}

/* 订单列表 */
.order-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.order-item {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--theme-shadow, rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.order-item:hover {
  box-shadow: 0 4px 16px var(--theme-shadow-hover, rgba(0, 0, 0, 0.15));
  transform: translateY(-2px);
}

.order-item:last-child {
  margin-bottom: 0;
}

/* 订单内容 */
.order-content {
  display: flex;
  align-items: center;
  padding: 16px;
  text-decoration: none;
  color: inherit;
}

.order-content:hover {
  text-decoration: none;
  color: inherit;
}

/* 订单图标 */
.order-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.order-icon i {
  font-size: 24px;
  color: var(--theme-primary, #1890ff);
}

/* 订单信息 */
.order-info {
  flex: 1;
  min-width: 0; /* 防止文本溢出 */
}

.order-category {
  font-size: 12px;
  color: var(--theme-primary, #1890ff);
  background: var(--theme-primary-light, rgba(24, 144, 255, 0.1));
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 4px;
}

.order-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text, #333);
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-express {
  font-size: 14px;
  color: var(--theme-success, #52c41a);
  margin-bottom: 4px;
  font-weight: 500;
}

.order-time {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

/* 订单价格和状态 */
.order-right {
  text-align: right;
  flex-shrink: 0;
  margin-left: 12px;
}

.order-price {
  font-size: 20px;
  font-weight: 700;
  color: #ff6037;
  margin-bottom: 4px;
}

.order-status {
  font-size: 12px;
  color: var(--theme-text-secondary, #666);
}

.order-status.success {
  color: var(--theme-success, #52c41a);
}

.order-status.failed {
  color: var(--theme-error, #ff4d4f);
}

.order-status.pending {
  color: var(--theme-warning, #faad14);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--theme-text-secondary, #666);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme-text-placeholder, #ccc);
  margin-bottom: 16px;
  display: block;
}

.empty-state h3 {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--theme-text-secondary, #666);
}

.empty-state p {
  font-size: 14px;
  color: var(--theme-text-placeholder, #999);
  margin: 0;
}

/* 加载更多按钮 */
.load-more-button {
  width: 100%;
  height: 44px;
  background: #fff;
  border: 1px solid var(--theme-border, #e5e5e5);
  border-radius: 8px;
  color: var(--theme-text, #333);
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
}

.load-more-button:hover {
  border-color: var(--theme-primary, #1890ff);
  color: var(--theme-primary, #1890ff);
}

.load-more-button:disabled {
  background: var(--theme-disabled-bg, #f5f5f5);
  color: var(--theme-disabled, #ccc);
  cursor: not-allowed;
}

/* 主题过渡动画 */
.theme-transition {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* 移动端优化 */

/* 触摸友好 */
.order-item:active {
  transform: scale(0.98);
}

.tab-item:active {
  background: var(--theme-active, #e6f7ff);
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.pending {
  background: var(--theme-warning, #faad14);
}

.status-indicator.success {
  background: var(--theme-success, #52c41a);
}

.status-indicator.failed {
  background: var(--theme-error, #ff4d4f);
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border, #e5e5e5);
  border-radius: 50%;
  border-top-color: var(--theme-primary, #1890ff);
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 拍卖特色样式 */
.auction-badge {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bid-price {
  background: linear-gradient(135deg, #ff6037, #ff8c69);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}
