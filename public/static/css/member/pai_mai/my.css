/* 拍卖个人中心页面样式 */

/* 深色模式支持 */

/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--theme-secondary);
  color: var(--theme-text);
}

/* Vue应用容器 */
#app {
  min-height: 100vh;
  background-color: var(--theme-secondary);
}

[v-cloak] {
  display: none !important;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--theme-secondary);
}

.loading-content {
  text-align: center;
  color: var(--theme-text-light);
}

.loading-content i {
  font-size: 32px;
  margin-bottom: 15px;
  color: var(--theme-primary);
}

.loading-content h4 {
  margin: 0;
  font-weight: 400;
  font-size: 16px;
}

/* 页面容器 */
.page-container {
  padding: 15px;
  max-width: 500px;
  margin: 0 auto;
}

/* 会员信息卡片 */
.member-card {
  background: var(--theme-white);
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
  margin-bottom: 20px;
  overflow: hidden;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-profile {
  display: flex;
  align-items: center;
  padding: 20px;
  text-decoration: none;
  color: var(--theme-text);
  transition: var(--theme-transition);
  border-bottom: 1px solid var(--theme-border);
}

.member-profile:hover {
  background-color: rgba(30, 159, 255, 0.05);
}

.member-avatar {
  width: 66px;
  height: 66px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
  border: 3px solid var(--theme-border);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text);
  margin-bottom: 5px;
}

/* 账户余额 */
.balance-section {
  padding: 15px 20px;
  background: linear-gradient(135deg, var(--theme-primary), #40a9ff);
  color: var(--theme-white);
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 14px;
  opacity: 0.9;
}

.balance-value {
  font-size: 20px;
  font-weight: 600;
}

/* 功能菜单卡片 */
.menu-card {
  background: var(--theme-white);
  border-radius: var(--theme-radius);
  box-shadow: var(--theme-shadow);
  overflow: hidden;
  animation: fadeInUp 0.5s ease 0.1s both;
}

.menu-list {
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 18px 20px;
  cursor: pointer;
  transition: var(--theme-transition);
  border-bottom: 1px solid var(--theme-border);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: rgba(30, 159, 255, 0.05);
}

.menu-item:active {
  background-color: rgba(30, 159, 255, 0.1);
}

.menu-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--theme-primary), #40a9ff);
  border-radius: 50%;
  margin-right: 15px;
  flex-shrink: 0;
}

.menu-icon i {
  font-size: 18px;
  color: var(--theme-white);
}

.menu-text {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  color: var(--theme-text);
}

.menu-text span {
  font-weight: 400;
}

.menu-text i {
  font-size: 14px;
  color: var(--theme-text-light);
}

/* 响应式设计 */

/* 无障碍访问 */
.menu-item:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 加载动画 */
.layui-anim-rotate {
  animation: layui-rotate 2s linear infinite;
}

@keyframes layui-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 主题色调整 */
.theme-primary {
  color: var(--theme-primary);
}

.theme-success {
  color: var(--theme-success);
}

.theme-warning {
  color: var(--theme-warning);
}

.theme-danger {
  color: var(--theme-danger);
}

/* 卡片悬停效果 */
.member-card:hover,
.menu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--theme-primary), #40a9ff);
}

/* 文本省略 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 圆角边框 */
.rounded {
  border-radius: var(--theme-radius);
}

.rounded-full {
  border-radius: 50%;
}

/* 阴影效果 */
.shadow {
  box-shadow: var(--theme-shadow);
}

.shadow-lg {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* 过渡动画 */
.transition {
  transition: var(--theme-transition);
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 间距工具类 */
.m-0 {
  margin: 0;
}
.m-1 {
  margin: 5px;
}
.m-2 {
  margin: 10px;
}
.m-3 {
  margin: 15px;
}
.m-4 {
  margin: 20px;
}

.p-0 {
  padding: 0;
}
.p-1 {
  padding: 5px;
}
.p-2 {
  padding: 10px;
}
.p-3 {
  padding: 15px;
}
.p-4 {
  padding: 20px;
}

/* 文本对齐 */
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

/* 字体大小 */
.text-xs {
  font-size: 12px;
}
.text-sm {
  font-size: 14px;
}
.text-base {
  font-size: 16px;
}
.text-lg {
  font-size: 18px;
}
.text-xl {
  font-size: 20px;
}

/* 字体粗细 */
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
