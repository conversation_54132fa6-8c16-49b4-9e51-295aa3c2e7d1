@charset "utf-8";
/* CSS by feng*/
* {
    margin: 0;
    padding: 0;
    border: 0;
    -webkit-touch-callout: none;
    -webkit-text-size-adjust: none;
    outline: none;
    font-style: normal;
    font-size: 14px;
    font-family: Helve<PERSON>, "STHeiti STXihei", "Microsoft YaHei", Tohoma, Arial;
}

span {
    word-break: normal;
    width: auto;
    /*display: block;*/
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block;
}

input, select, textarea {
    font-size: 100%;
}

body {
    /*font: normal 15px <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", sans-self;*/
    -webkit-user-select: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    color: #222222;
    background-color: #EFEFF4;
    position: absolute;
    min-height: 100%;
    width: 100%;
}

img, a img {
    border: 0;
    vertical-align: middle;
    width: 100%;
}

ol, ul {
    list-style: none;
}

a {
    outline: 0 none;
    text-decoration: none;
    color: #333536;
}

em {
    font-style: normal;
    font-weight: normal;
}

/*空状态*/
.empty {
    position: fixed;
    top: 30%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    text-align: center;
    margin-left: -1rem;
    margin-top: -1rem;
    background-image: url("../img/empty.png");
    background-size: 2rem 2rem;
    background-repeat: no-repeat;
}

.empty span {
    color: #999;
    position: fixed;
    margin-top: 2rem;
    margin-left: -1rem;
    width: 2rem;
}