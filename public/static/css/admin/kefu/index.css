/* kefu 客服端自定义样式，后续可补充 */
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: #f5f6fa;
  height: 100vh;
}

.kefu-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-width: 320px;
}

.kefu-header {
  display: flex;
  align-items: center;
  background: #263445; /* 深蓝色 */
  color: #fff;
  height: 56px;
  padding: 0 20px;
  flex-shrink: 0;
  border-bottom: 2px solid #009688;
}

.kefu-header .logo {
  height: 36px;
  margin-right: 16px;
}

.kefu-header .title {
  font-size: 20px;
  font-weight: bold;
  flex: 1;
}

.kefu-header .user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sound-toggle {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.sound-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sound-toggle i {
  font-size: 16px;
}

/* WebSocket状态指示器样式 */
.websocket-status {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.websocket-status:hover {
  background: rgba(255, 255, 255, 0.1);
}

.websocket-status i {
  font-size: 16px;
}

/* 不同状态的颜色 */
.websocket-status.connected {
  color: #52c41a;
}

.websocket-status.connecting {
  color: #faad14;
  animation: rotate 1s linear infinite;
}

.websocket-status.disconnected {
  color: #ff4d4f;
}

.websocket-status.reconnecting {
  color: #fa8c16;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 动画效果 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.kefu-header .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
}

.kefu-header .user-name {
  margin-right: 16px;
}

.kefu-header .logout {
  cursor: pointer;
  color: #ffe082;
}

.kefu-main {
  display: flex;
  flex: 1;
  min-height: 0;
  background: #f5f6fa;
  overflow: hidden;
}

.kefu-sidebar {
  width: 60px;
  background: #23262e;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 12px;
  flex-shrink: 0;
  border-right: 1.5px solid #e0e0e0;
}

.kefu-sidebar .menu-item {
  width: 44px;
  height: 44px;
  margin-bottom: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  font-size: 22px;
  transition: background 0.2s;
}

.kefu-sidebar .menu-item.active,
.kefu-sidebar .menu-item:hover {
  background: #009688;
  color: #fff;
}

.kefu-session-list {
  width: 220px;
  background: #f7f8fa;
  border-right: 2px solid #e0e0e0;
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.2s;
}

.kefu-session-list .session-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: #fff;
  transition: background 0.2s, box-shadow 0.2s;
  position: relative;
}

.kefu-session-list .session-item.active {
  background: #b2ebf2;
  border-left: 4px solid #009688;
  box-shadow: 0 2px 8px rgba(0, 150, 136, 0.1);
}

.kefu-session-list .session-item.active .name {
  color: #009688;
  font-weight: bold;
}

.kefu-session-list .session-item.active .avatar {
  border: 2.5px solid #009688;
  box-shadow: 0 0 6px #00968855;
}

.kefu-session-list .session-item:hover:not(.active) {
  background: #e0f2f1;
}

.kefu-session-list .session-item .name {
  font-weight: 500;
  font-size: 15px;
  color: #263445;
}

.kefu-session-list .avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
  border: 1.5px solid #e0e0e0;
}

.kefu-session-list .info {
  flex: 1;
}

.kefu-session-list .name {
  font-weight: bold;
  font-size: 15px;
}

.kefu-session-list .last-msg {
  color: #888;
  font-size: 13px;
  margin-top: 2px;
}

.kefu-session-list .unread {
  background: #ff5252;
  color: #fff;
  border-radius: 10px;
  padding: 0 6px;
  font-size: 12px;
  margin-left: 6px;
  position: absolute;
  right: 18px;
  top: 16px;
  min-width: 18px;
  text-align: center;
  box-shadow: 0 2px 6px rgba(255, 82, 82, 0.15);
  font-weight: bold;
}

.kefu-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: #f9f9fb;
}

.kefu-chat-header {
  height: 48px;
  background: #fff;
  border-bottom: 1.5px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 18px;
  flex-shrink: 0;
}

.kefu-chat-header .chat-title {
  font-size: 16px;
  font-weight: bold;
  flex: 1;
}

.kefu-chat-header .chat-actions i {
  margin-left: 16px;
  cursor: pointer;
  color: #888;
}

.kefu-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 18px 16px 12px 16px;
  background: #f9f9fb;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 消息气泡优化 */
.msg-row {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
}

.msg-row.msg-right {
  flex-direction: row-reverse;
}

.msg-avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  margin: 0 10px;
  border: 1.5px solid #e0e0e0;
  background: #fff;
}

.msg-bubble {
  max-width: 60%;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  padding: 10px 16px;
  position: relative;
  word-break: break-all;
  font-size: 15px;
  color: #222;
  margin-bottom: 2px;
}

.msg-row.msg-right .msg-bubble {
  background: #e6f7ff;
  color: #222;
}

.msg-content {
  margin-bottom: 4px;
}

.msg-time {
  font-size: 12px;
  color: #aaa;
  text-align: right;
}

.msg-status {
  font-size: 12px;
  margin-left: 6px;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 500;
}

.msg-status-read {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.msg-status-unread {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

.msg-row.msg-right .msg-status {
  margin-right: 6px;
  margin-left: 0;
}

/* 图片消息样式 */
.msg-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.msg-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片预览遮罩 */
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
}

.image-preview-content {
  max-width: 90%;
  max-height: 90%;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.kefu-chat-input {
  background: #fff;
  border-top: 1.5px solid #e0e0e0;
  padding: 10px 16px;
  display: flex;
  align-items: flex-end;
  flex-shrink: 0;
}

/* 桌面端工具栏图标样式 */
.kefu-chat-input .input-area > div:first-child {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-right: 12px;
  margin-bottom: 8px;
}

.kefu-chat-input .input-area > div:first-child i {
  font-size: 24px;
  color: #666;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  background: transparent;
}

.kefu-chat-input .input-area > div:first-child i:hover {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

.kefu-chat-input .input-area > div:first-child i:active {
  transform: scale(0.95);
}

/* 桌面端输入区域布局 */
.kefu-chat-input .input-area {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.kefu-chat-input textarea {
  flex: 1;
  min-height: 36px;
  max-height: 90px;
  border: 1.5px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 10px;
  font-size: 15px;
  resize: none;
  outline: none;
  background: #fafbfc;
}

.kefu-chat-input .send-btn {
  margin-left: 12px;
  background: #009688;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 8px 22px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 6px rgba(0, 150, 136, 0.08);
}

.kefu-chat-input .send-btn:disabled {
  background: #ccc;
  color: #fff;
  cursor: not-allowed;
}

.kefu-info-bar {
  width: 320px;
  background: #f7f8fa;
  border-left: 2px solid #e0e0e0;
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.2s;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 大屏幕到中等屏幕的过渡 */
@media (max-width: 1200px) {
  .kefu-info-bar {
    width: 280px;
  }

  .kefu-session-list {
    width: 240px;
  }
}

@media (max-width: 1100px) {
  .kefu-info-bar {
    display: none;
  }

  .kefu-session-list {
    width: 260px;
  }
}

/* 平板端适配 */
@media (max-width: 900px) {
  .kefu-sidebar {
    width: 50px;
  }

  .kefu-session-list {
    width: 220px;
  }

  .kefu-chat-header {
    padding: 0 12px;
  }

  .kefu-chat-messages {
    padding: 12px 10px;
  }

  .msg-bubble {
    max-width: 70%;
    font-size: 14px;
  }
}

@media (max-width: 800px) {
  .kefu-sidebar {
    width: 44px;
  }

  .kefu-session-list {
    width: 200px;
    min-width: 180px;
    max-width: 220px;
  }

  .kefu-info-bar {
    display: none;
  }

  /* 平板端会话项优化 */
  .kefu-session-list .session-item {
    padding: 14px 12px;
  }

  .kefu-session-list .avatar {
    width: 40px;
    height: 40px;
  }

  .kefu-session-list .name {
    font-size: 14px;
  }

  .kefu-session-list .last-msg {
    font-size: 12px;
  }
}

/* 小平板到大手机的过渡 */
@media (max-width: 700px) {
  .kefu-header {
    padding: 0 10px;
  }

  .kefu-header .title {
    font-size: 18px;
  }

  .kefu-header .user-info {
    gap: 8px;
  }

  .kefu-session-list {
    width: 180px;
  }

  .kefu-chat-input {
    padding: 8px 10px;
  }

  .kefu-chat-input textarea {
    font-size: 14px;
    padding: 6px 10px;
  }
}

/* 桌面端输入框保持原有样式 */
@media (min-width: 601px) {
  .kefu-chat-input {
    position: static !important;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0 !important;
    min-height: auto !important;
    box-shadow: none !important;
  }

  .kefu-chat-input .input-area {
    display: contents !important;
  }

  .kefu-chat-input textarea {
    margin-right: 12px !important;
  }
}

/* 手机端布局重构 - 采用底部导航+抽屉式设计 */
@media (max-width: 600px) {
  .kefu-layout {
    position: relative;
  }

  .kefu-header {
    height: 50px;
    padding: 0 12px;
    position: relative;
    z-index: 100;
  }

  .kefu-header .title {
    font-size: 16px;
  }

  .kefu-header .user-info {
    gap: 8px;
  }

  .kefu-header .user-name {
    display: none; /* 手机端隐藏用户名节省空间 */
  }

  .kefu-main {
    position: relative;
    height: calc(100vh - 50px); /* 只减去头部高度，底部导航是fixed定位 */
    padding-bottom: 60px; /* 为底部导航留出空间 */
  }

  /* 手机端侧边栏隐藏 */
  .kefu-sidebar {
    display: none;
  }

  /* 手机端会话列表改为抽屉式 */
  .kefu-session-list {
    position: fixed;
    top: 50px;
    left: -100%;
    width: 85vw;
    max-width: 320px;
    height: calc(100vh - 50px - 60px);
    background: #fff;
    z-index: 200;
    transition: left 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    border-right: none;
  }

  .kefu-session-list.mobile-open {
    left: 0;
  }

  /* 手机端聊天区域 */
  .kefu-chat-area {
    width: 100%;
    height: 100%;
    position: relative;
  }

  .kefu-chat-header {
    padding: 8px 12px;
    height: 56px;
    position: relative;
  }

  .kefu-chat-messages {
    padding: 12px 8px;
    height: calc(100% - 56px - 80px); /* 减去头部和输入框高度，适配新的输入框高度 */
    padding-bottom: 140px; /* 增加底部间距，确保最后一条消息完全可见 */
    box-sizing: border-box;
  }

  .kefu-chat-input {
    padding: 12px 16px;
    min-height: 80px;
    position: fixed;
    bottom: 60px; /* 在底部导航上方 */
    left: 0;
    right: 0;
    background: #fff;
    border-top: 1px solid #e0e0e0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 50;
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  .kefu-chat-input textarea {
    min-height: 40px;
    max-height: 60px;
  }

  /* 手机端访客信息栏隐藏，通过底部导航访问 */
  .kefu-info-bar {
    display: none;
  }

  /* 消息气泡手机端优化 */
  .msg-bubble {
    max-width: 75%;
    font-size: 14px;
    padding: 8px 12px;
  }

  .msg-avatar {
    width: 32px;
    height: 32px;
    margin: 0 6px;
  }
}

/* 手机端底部导航栏 */
.mobile-bottom-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: #fff;
  border-top: 1px solid #e0e0e0;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 600px) {
  .mobile-bottom-nav {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 20px;
  }
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  position: relative;
  min-width: 60px;
  height: 48px; /* 固定高度确保对齐 */
}

.mobile-nav-item:hover {
  background: rgba(0, 150, 136, 0.1);
}

.mobile-nav-item.active {
  background: rgba(0, 150, 136, 0.15);
  color: #009688;
}

.mobile-nav-item i {
  font-size: 22px;
  margin-bottom: 3px;
  color: #666;
  transition: color 0.2s ease;
  line-height: 1;
}

.mobile-nav-item.active i {
  color: #009688;
}

.mobile-nav-item span {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  transition: color 0.2s ease;
  line-height: 1;
  text-align: center;
}

.mobile-nav-item.active span {
  color: #009688;
  font-weight: 600;
}

/* 导航项上的徽章 */
.mobile-nav-badge {
  position: absolute;
  top: 4px;
  right: 8px;
  background: #ff5252;
  color: #fff;
  border-radius: 10px;
  padding: 0 5px;
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 手机端遮罩层 */
.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 150;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-overlay.show {
  display: block;
  opacity: 1;
}

/* 手机端会话列表头部 */
.mobile-session-header {
  display: none;
  padding: 12px 16px;
  background: #009688;
  color: #fff;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

@media (max-width: 600px) {
  .mobile-session-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.mobile-session-close {
  background: none;
  border: none;
  color: #fff;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.mobile-session-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 手机端聊天头部优化 */
@media (max-width: 600px) {
  .kefu-chat-header {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .kefu-chat-header .mobile-menu-btn {
    background: none;
    border: none;
    color: #009688;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .kefu-chat-header .mobile-menu-btn:hover {
    background: rgba(0, 150, 136, 0.1);
  }

  .kefu-chat-header .mobile-back-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.2s ease;
    display: none;
  }

  .kefu-chat-header .mobile-back-btn:hover {
    background: rgba(0, 0, 0, 0.05);
  }

  /* 当显示访客信息时显示返回按钮 */
  .mobile-visitor-info-active .mobile-back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mobile-visitor-info-active .mobile-menu-btn {
    display: none;
  }
}

/* 手机端访客信息全屏模式 */
.mobile-visitor-info {
  display: none;
  position: fixed;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 60px;
  background: #f7f8fa;
  z-index: 300;
  overflow-y: auto;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  -webkit-overflow-scrolling: touch;
}

.mobile-visitor-info.show {
  transform: translateX(0);
}

@media (max-width: 600px) {
  .mobile-visitor-info {
    display: block;
  }

  .mobile-visitor-info .visitor-info-card,
  .mobile-visitor-info .common-words-card {
    margin: 12px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }

  /* 手机端访客信息卡片优化 */
  .mobile-visitor-info .visitor-info-header,
  .mobile-visitor-info .common-words-header {
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 600;
  }

  .mobile-visitor-info .visitor-info-content,
  .mobile-visitor-info .common-words-content {
    padding: 20px;
  }

  .mobile-visitor-info .visitor-avatar {
    width: 60px;
    height: 60px;
  }

  .mobile-visitor-info .visitor-name {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .mobile-visitor-info .detail-item {
    padding: 14px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .mobile-visitor-info .detail-label {
    font-size: 15px;
    margin-bottom: 4px;
  }

  .mobile-visitor-info .detail-value {
    font-size: 15px;
    color: #333;
    font-weight: 500;
  }

  .mobile-visitor-info .remark-textarea {
    font-size: 16px;
    padding: 14px;
    border-radius: 12px;
    border: 2px solid #e8f5e8;
    min-height: 100px;
  }

  .mobile-visitor-info .common-word-item {
    padding: 14px 16px;
    margin-bottom: 10px;
    border-radius: 12px;
    font-size: 15px;
    line-height: 1.4;
  }

  .mobile-visitor-info .common-tab-btn {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 12px;
  }
}

/* 手机端输入工具栏优化 */
@media (max-width: 600px) {
  .kefu-chat-input {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
    padding: 12px;
  }

  .kefu-chat-input > div:first-child {
    order: 2;
    justify-content: center;
    margin-right: 0;
    margin-bottom: 8px;
  }

  .kefu-chat-input textarea {
    order: 1;
    margin-bottom: 8px;
  }

  .kefu-chat-input .send-btn {
    order: 3;
    margin-left: 0;
    align-self: flex-end;
    min-width: 80px;
  }

  /* 手机端会话列表优化 */
  .kefu-session-list .session-item {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
    touch-action: manipulation;
  }

  .kefu-session-list .session-item:active {
    background: #e8f5e8;
    transform: scale(0.98);
  }

  .kefu-session-list .session-item .avatar {
    width: 44px;
    height: 44px;
    margin-right: 12px;
  }

  .kefu-session-list .session-item .name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .kefu-session-list .session-item .last-msg {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
  }

  .kefu-session-list .session-item .unread {
    top: 20px;
    right: 20px;
    min-width: 20px;
    height: 20px;
    font-size: 12px;
  }

  /* 手机端会话操作按钮优化 */
  .kefu-session-list .session-item.queue .session-actions {
    flex-direction: column;
    gap: 6px;
    margin-left: 8px;
  }

  .kefu-session-list .session-item.queue .accept-btn,
  .kefu-session-list .session-item.queue .reject-btn {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 50px;
    border-radius: 6px;
  }

  /* 手机端会话分组标题 */
  .session-group-title {
    padding: 12px 16px 8px 16px;
    font-size: 14px;
    font-weight: 600;
    background: #f7f8fa;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 5;
  }

  /* 手机端聊天界面优化 */
  .kefu-chat-header {
    height: 60px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .kefu-chat-header img {
    width: 36px !important;
    height: 36px !important;
    margin-right: 8px !important;
  }

  .kefu-chat-header > div {
    font-size: 16px !important;
    font-weight: 600;
  }

  .kefu-chat-header .layui-btn {
    font-size: 12px;
    margin-left: 8px !important;
  }

  /* 手机端消息区域优化 */
  .kefu-chat-messages {
    padding: 12px 8px;
    padding-bottom: 140px; /* 确保最后一条消息完全可见 */
    background: #f5f6fa;
    box-sizing: border-box;
  }

  .msg-row {
    margin-bottom: 12px;
  }

  /* 确保最后一条消息有足够的底部空间 */
  .msg-row:last-child {
    margin-bottom: 24px;
  }

  .msg-bubble {
    max-width: 85%;
    font-size: 15px;
    line-height: 1.4;
    padding: 10px 14px;
    border-radius: 16px;
    word-break: break-word;
    box-sizing: border-box;
  }

  .msg-row.msg-left .msg-bubble {
    background: #fff;
    border-bottom-left-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .msg-row.msg-right .msg-bubble {
    background: #007aff;
    color: #fff;
    border-bottom-right-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
  }

  .msg-time {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
  }

  .msg-status {
    font-size: 11px;
    margin-left: 4px;
  }

  /* 手机端输入区域进一步优化 */
  .kefu-chat-input {
    background: #fff;
    border-top: 1px solid #e0e0e0;
    padding: 12px;
    min-height: 100px;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .kefu-chat-input textarea {
    border: none;
    border-radius: 0;
    padding: 8px 12px;
    font-size: 16px;
    line-height: 1.4;
    resize: none;
    background: transparent;
    min-height: 24px;
    max-height: 80px;
    width: 100%;
    box-sizing: border-box;
    vertical-align: middle;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  }

  .kefu-chat-input textarea:focus {
    border: none;
    background: transparent;
    outline: none;
    box-shadow: none;
  }

  .kefu-chat-input .send-btn {
    background: #007aff;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    min-width: 60px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    align-self: flex-end;
    flex-shrink: 0;
    margin-left: 8px;
  }

  .kefu-chat-input .send-btn:disabled {
    background: #ccc;
    color: #999;
  }

  /* 手机端工具栏图标优化 - 移到输入框左下角 */
  .kefu-chat-input .input-area > div:first-child {
    position: absolute;
    bottom: 8px;
    left: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: transparent;
    padding: 0;
    margin: 0;
    z-index: 10;
  }

  .kefu-chat-input .input-area > div:first-child i {
    font-size: 20px;
    color: #999;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    background: transparent;
  }

  .kefu-chat-input .input-area > div:first-child i:active {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
    transform: scale(0.9);
  }

  /* 手机端输入区域布局 */
  .kefu-chat-input .input-area {
    display: flex;
    align-items: flex-end;
    gap: 0;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    position: relative;
    min-height: 48px;
  }

  .kefu-chat-input .input-area textarea {
    flex: 1;
    border: none;
    background: transparent;
    padding: 8px 12px;
    padding-left: 12px; /* 正常左边距 */
    padding-right: 80px; /* 为发送按钮留出空间 */
    padding-bottom: 36px; /* 为工具栏留出底部空间 */
    margin: 0;
    min-height: 24px;
    resize: none;
  }

  .kefu-chat-input .input-area textarea:focus {
    border: none;
    background: transparent;
    box-shadow: none;
    outline: none;
  }

  .kefu-chat-input .input-area .send-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    margin: 0;
    height: 32px;
    min-width: 60px;
    border-radius: 6px;
  }
}

[v-cloak] {
  display: none;
}

.status-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  vertical-align: middle;
}

.session-group-title {
  font-weight: bold;
  color: #263445;
  font-size: 15px;
  padding: 10px 16px 4px 16px;
  background: #f7f8fa;
}

.session-group-divider {
  border-bottom: 1.5px solid #e0e0e0;
  margin-bottom: 6px;
}

.status-dot.queue {
  background: #ff9800;
}

.status-dot.active {
  background: #4caf50;
}

.status-dot.history {
  background: #bdbdbd;
}

.session-actions {
  margin-left: auto;
  display: flex;
  gap: 4px;
}

.accept-btn,
.reject-btn,
.view-btn {
  background: #009688;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.accept-btn {
  background: #ff9800;
  transition: all 0.2s;
  position: relative;
}

.accept-btn:hover {
  background: #f57c00;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.accept-btn:active {
  transform: translateY(0);
}

.accept-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reject-btn {
  background: #f44336;
}

.reject-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.reject-btn:active {
  transform: translateY(0);
}

.reject-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.view-btn {
  background: #bdbdbd;
}

.queue-count,
.active-count {
  color: #ff9800;
  font-size: 13px;
  margin-left: 6px;
}

/* 访客信息卡片样式 */
.visitor-info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin: 18px 20px 0 20px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.visitor-info-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.visitor-info-header {
  background: linear-gradient(135deg, #009688 0%, #00796b 100%);
  color: #fff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.visitor-info-title {
  font-size: 16px;
  font-weight: 600;
}

.visitor-info-content {
  padding: 20px;
}

/* 访客基本信息区域 */
.visitor-basic-info {
  margin-bottom: 24px;
}

.visitor-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.visitor-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 3px solid #e8f5e8;
  box-shadow: 0 2px 8px rgba(0, 150, 136, 0.15);
  object-fit: cover;
}

.visitor-name-section {
  flex: 1;
}

.visitor-name {
  font-size: 18px;
  font-weight: 600;
  color: #263445;
  margin-bottom: 6px;
}

.visitor-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.visitor-status.online .status-dot {
  width: 8px;
  height: 8px;
  background: #4caf50;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.visitor-status .status-text {
  font-size: 13px;
  color: #4caf50;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* 详细信息区域 */
.visitor-details {
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
}

.detail-label i {
  color: #009688;
  font-size: 16px;
}

.detail-value {
  color: #333;
  font-size: 14px;
  text-align: right;
  max-width: 160px;
  word-break: break-all;
}

/* 备注区域 */
.visitor-remark-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.remark-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  color: #666;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}

.remark-label i {
  color: #009688;
  font-size: 16px;
}

.remark-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  min-width: 60px;
  text-align: center;
}

.remark-status.saving {
  background-color: #f0f9ff;
  color: #0369a1;
  opacity: 1;
}

.remark-status.saved {
  background-color: #f0fdf4;
  color: #15803d;
  opacity: 1;
}

.remark-status.error {
  background-color: #fef2f2;
  color: #dc2626;
  opacity: 1;
}

.remark-input-wrapper {
  position: relative;
}

.remark-textarea {
  width: 100%;
  border: 2px solid #e8f5e8;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  font-family: inherit;
  box-sizing: border-box;
}

.remark-textarea:focus {
  outline: none;
  border-color: #009688;
  box-shadow: 0 0 0 3px rgba(0, 150, 136, 0.1);
  transform: translateY(-1px);
}

/* 保存成功动画效果 */
.remark-textarea.saving {
  border-color: #ff9800;
  box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.2);
}

.remark-textarea.saved {
  border-color: #4caf50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
  animation: saveSuccess 0.6s ease;
}

@keyframes saveSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.remark-textarea::placeholder {
  color: #bbb;
}

.remark-tip {
  font-size: 12px;
  color: #999;
  margin-top: 6px;
  text-align: right;
}

/* 空状态样式 */
.visitor-info-empty {
  padding: 60px 20px;
  text-align: center;
  color: #999;
}

.empty-text {
  margin-top: 16px;
  font-size: 14px;
  color: #999;
}

/* 常用语卡片样式 */
.common-words-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin: 18px 20px 0 20px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.common-words-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.common-words-header {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: #fff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.common-words-title {
  font-size: 16px;
  font-weight: 600;
}

.common-words-content {
  padding: 20px;
}

/* 常用语标签页 */
.common-words-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.common-tab-btn {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #f0f0f0;
  background: #fafafa;
  color: #666;
  border-radius: 8px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: 500;
}

.common-tab-btn:hover {
  border-color: #ff9800;
  background: #fff8e1;
  color: #ff9800;
}

.common-tab-btn.active {
  border-color: #ff9800;
  background: #ff9800;
  color: #fff;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.common-tab-btn i {
  font-size: 14px;
}

/* 常用语列表 */
.common-words-list {
  max-height: 300px;
  overflow-y: auto;
}

.common-word-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.common-word-item:hover {
  background: #fff3e0;
  border-color: #ff9800;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.15);
}

.common-word-item:active {
  transform: translateY(0);
}

.word-text {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.word-arrow {
  color: #ccc;
  font-size: 12px;
  transition: all 0.3s ease;
}

.common-word-item:hover .word-arrow {
  color: #ff9800;
  transform: translateX(2px);
}

/* 滚动条美化 */
.common-words-list::-webkit-scrollbar {
  width: 4px;
}

.common-words-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.common-words-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.common-words-list::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .visitor-info-card,
  .common-words-card {
    margin: 12px 16px 0 16px;
  }

  .visitor-info-content,
  .common-words-content {
    padding: 16px;
  }

  .visitor-avatar {
    width: 48px;
    height: 48px;
  }

  .visitor-name {
    font-size: 16px;
  }
}

@media (max-width: 800px) {
  .visitor-info-card,
  .common-words-card {
    margin: 8px;
    border-radius: 8px;
  }

  .visitor-info-header,
  .common-words-header {
    padding: 12px 16px;
    font-size: 14px;
  }

  .visitor-info-content,
  .common-words-content {
    padding: 12px;
  }

  .visitor-avatar-section {
    gap: 12px;
  }

  .visitor-avatar {
    width: 40px;
    height: 40px;
  }

  .visitor-name {
    font-size: 15px;
  }

  .detail-item {
    padding: 8px 0;
  }

  .detail-label,
  .detail-value {
    font-size: 13px;
  }

  .common-tab-btn {
    padding: 8px 10px;
    font-size: 12px;
  }

  .common-word-item {
    padding: 10px 12px;
  }

  .word-text {
    font-size: 13px;
  }
}

/* 输入预览动画 */
@keyframes typingDot {
  0%,
  20% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  80%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

.typing-preview {
  transition: all 0.3s ease;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  position: relative;
}

.typing-indicator::before {
  content: "";
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(to bottom, #009688, #4caf50);
  border-radius: 2px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
