.layui-breadcrumb {
    display: block;
    border-bottom: 1px #f0f0f0 solid;
    height: 50px;
    line-height: 50px;
    font-size: 12px;
    color: #999;
}

body,
button,
input,
select,
textarea,
h1,
h2,
h3,
h4,
h5,
h6,
a {
    font-family: 'Microsoft YaHei', 'simhei', Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
}

.searchbox {
    border-bottom: 1px #f0f0f0 solid;
    padding: 20px;
}

.class {
    display: block;
    line-height: 30px;
    width: 100%;
    overflow: hidden;
    padding: 5px 0;
    border-bottom: 1px #f9f9f9 solid;
}

.class div {
    float: left;
    width: 80px;
    text-align: right;
    padding-right: 20px;
    font-weight: bold;
}

.class ul {
    width: 1100px;
    margin-left: 80px;
}

.class ul li {
    float: left;
    padding: 0 10px;
}

.class ul li a:link {
    padding: 2px 5px;
    border-radius: 2px;
}

.class ul li a:hover {
    color: #fff;
    background: #ed5f5b;
}

/*caigou*/

.caigouBox {
    margin: 0px 0;
    overflow: hidden;
    padding: 10px 0px
}

.caigouBox > .col-lg-3 {
    padding: 0
}

.caigouBox li {
    width: 280px;
    float: left;
    margin-top: 10px;
    margin-bottom: 10px;
    background: #fff;
    padding: 5px;
}

.caigouBox li > .border {
    border: 1px #eee solid;
    border-radius: 0px;
}

.caigouBox li > .border:hover {
    border: 1px #c4dcee solid;

}

.caigouBox li a {
}

.caigouBox li a:hover {
    color: #000;
}

.caigouBox li a > .title {
    font-size: 14px;
    line-height: 22px;
    padding: 15px 15px 10px 15px;
    font-weight: bold;
    height: 50px;
}

.caigouBox li a .money {
    font-size: 14px;
    overflow: hidden;
    padding: 0px 15px;
}

.caigouBox li a .money b {
    font-size: 12px;
    color: #999;
    padding-right: 10px;
    font-weight: normal;
}

.caigouBox li a .money span {
    color: #f73063;
    font-size: 20px;
}

.caigouBox li a .money label {
    color: #999;
    font-size: 12px;
    float: right;
    padding-top: 8px;
}

.caigouBox li a .time {
    font-size: 12px;
    font-weight: normal;
    padding: 0px 15px 20px 15px;
    color: #999;
}

.caigouBox li a .time b {
    padding-right: 10px;
    font-weight: normal;
}

.tbtime {
    background: #e7ecf2;
    padding: 15px 10px;
    position: relative;
    width: 275px \0;

}

.tbtime .tag {
    font-weight: normal;
}

.tbtime .tag .ing {
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    background: #19aa6b;
    position: absolute;
    right: 0;
    top: 5px;
    padding: 5px 15px;
}

.tbtime .tag .begin {
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    background: #fd4f3e;
    position: absolute;
    right: 0;
    top: 5px;
    padding: 5px 15px;
}

.tbtime .tag .stop {
    font-size: 14px;
    font-weight: normal;
    color: #fff;
    background: #989898;
    position: absolute;
    right: 0;
    top: 5px;
    padding: 5px 15px;
}

.tbtime .text {
    font-size: 14px;
    padding-bottom: 20px;
}

.tbtime .day {
    font-size: 14px;
    text-align: center;
    padding: 10px;
    overflow: hidden;
}

.tbtime .day b {
    background: #19a3e0;
    border-radius: 3px;
    padding: 3px 3px;
    color: #fff;
    margin: 5px;
    font-weight: normal;
    min-width: 20px;
    display: inline-block;
}

.topTitle {
}

.topTitle h3 {
    padding: 20px 0;
    line-height: 36px;
    font-size: 24px;
}

.topTitle h3 b {
    color: #333;
    font-weight: normal;
    padding-right: 10px;
}

#container {
    padding: 10px;
    border: 1px #eee solid;
    border-top: 2px #0075e2 solid;
    overflow: auto;
}

#main {

}

#main h3 {
    border-bottom: 1px #eee solid;
    padding-bottom: 14px;
}

.search_hover {
    color: #fff;
    background: #ed5f5b;
}