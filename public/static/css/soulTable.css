/** 自定义字体 **/
/** 这里使用在线字体，如果需要离线包，请看 font/README.md **/
@font-face {
    font-family: 'soul-icon';  /* project id 677836 */
    src: url('//at.alicdn.com/t/font_677836_jwq362m0tt.eot');
    src: url('//at.alicdn.com/t/font_677836_jwq362m0tt.eot?#iefix') format('embedded-opentype'),
    url('//at.alicdn.com/t/font_677836_jwq362m0tt.woff2') format('woff2'),
    url('//at.alicdn.com/t/font_677836_jwq362m0tt.woff') format('woff'),
    url('//at.alicdn.com/t/font_677836_jwq362m0tt.ttf') format('truetype'),
    url('//at.alicdn.com/t/font_677836_jwq362m0tt.svg#iconfont') format('svg');
}

.soul-icon {
    font-family:"soul-icon" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.soul-icon-filter:before { content: "\e60b"; }
.soul-icon-filter-asc:before { content: "\e768"; }
.soul-icon-filter-desc:before { content: "\e767"; }
.soul-icon-asc:before { content: "\e6af"; }
.soul-icon-desc:before { content: "\e6ae"; }
.soul-icon-all-check:before { content: "\e670"; }
.soul-icon-invert-check:before { content: "\e614"; }
.soul-icon-fold:before { content: "\e760"; }
.soul-icon-unfold:before { content: "\e611"; }
.soul-icon-delete:before { content: "\e600"; }
.soul-icon-download:before { content: "\e601"; }
.soul-icon-drop-list:before { content: "\e6a3"; }
.soul-icon-query:before { content: "\e66d"; }
/* 全选*/
.soul-icon-quanxuan:before { content: "\e623"; }
.soul-icon-qingkong:before { content: "\e63e"; }
.soul-icon-autoColumnWidth
:before { content: "\e614"; }

/*最大化*/
.soul-icon-min:before { content: "\e656"; }
.soul-icon-max:before { content: "\e61b"; }


/* 配色方案*/
.layui-red {
    color:  #FF5722
}
.layui-orange {
    color:  #FFB800
}
.layui-green {
    color:  #009688
}
.layui-cyan {
    color: #2F4056
}
.layui-blue {
    color:  #1E9FFF
}
.layui-black {
    color:  #393D49
}
.layui-gray {
    color:  #eee
}
.layui-firebrick {
    color: firebrick;
}
.layui-deeppink {
    color: deeppink;
}
.layui-blueviolet {
    color: blueviolet;
}

.soul-condition [class*="layui-col-"] {
    margin-top: 10px;
}
/* 表格排序样式*/
.soul-edge {
    display: inline-block;
    width: 0;
    height: 0;
    border-width: 6px;
    border-style: dashed;
    border-color: transparent;
    overflow: hidden
}
.soul-table-sort {
    width: 10px;
    height: 20px;
    margin-left: 5px;
    cursor: pointer !important;
    position: relative;
    display: inline-block;
}

.soul-table-sort .soul-edge {
    position: absolute;
    left: 5px;
    border-width: 5px
}

.soul-table-sort .soul-table-sort-asc {
    top: 10px;
    border-top: none;
    border-bottom-style: solid;
    border-bottom-color: #b2b2b2
}

.soul-table-sort .soul-table-sort-asc:hover {
    border-bottom-color: #666
}

.soul-table-sort .soul-table-sort-desc {
    bottom: -2px;
    border-bottom: none;
    border-top-style: solid;
    border-top-color: #b2b2b2
}

.soul-table-sort .soul-table-sort-desc:hover {
    border-top-color: #666
}

.soul-table-sort[soul-sort=asc] .soul-table-sort-asc {
    border-bottom-color: #000
}

.soul-table-sort[soul-sort=desc] .soul-table-sort-desc {
    border-top-color: #000
}

.multiOption {
    display: inline-block;
    padding: 0 5px;
    cursor: pointer;
    color: #999;
}
/*表格筛选*/
.soul-table-filter {
    line-height: 20px;
    color: #b2b2b2;
    cursor: pointer;
    margin-left: 5px;
}
.soul-table-filter .soul-icon-filter-asc,.soul-table-filter .soul-icon-filter-desc {
    display: none;
}
.soul-table-filter[lay-sort="asc"] .soul-icon-filter-asc{
    display: block;
    color: #000000;
}
.soul-table-filter[lay-sort="asc"] .soul-icon-filter,.soul-table-filter[lay-sort="asc"] .soul-icon-filter-desc{
    display: none;
}

.soul-table-filter[lay-sort="desc"] .soul-icon-filter-desc{
    display: block;
    color: #000000;
}
.soul-table-filter[lay-sort="desc"] .soul-icon-filter,.soul-table-filter[lay-sort="desc"] .soul-icon-filter-asc{
    display: none;
}
.soul-table-filter[soul-filter="true"] i {
    color: #009688!important;
}


[id^=main-list], [id^=soul-columns], [id^=soul-dropList], [id^=soul-condition], [id^=soul-bf-prefix], [id^=soul-bf-column], [id^=soul-bf-type], [id^=soul-bf-cond2] {
    display: inline-block;
    position: absolute;
    z-index: 2147483647;
    background-color: white;
    max-height: 200px;
    min-width: 160px;
    max-width: 300px;
    overflow-y: auto;
    border: 1px solid #e6e6e6;
    border-radius: 5px;
    box-shadow: 2px 2px 4px -2px rgba(0,0,0,.2);
}
[id^=main-list] {
    max-height: initial;
}
[id^=soul-condition] {
    overflow-y: visible;
    max-height: initial;
    min-width: 285px;
    padding: 5px;
}
[id^=soul-condition] .layui-laydate-header {
    padding: 4px 70px 5px
}
[id^=soul-condition] hr{
    margin: 5px 0;
}
[id^=soul-condition].soul-bf{
    min-width: 150px;
}
[id^=soul-filter-list] ul li {
    padding: 3px 10px;
    cursor: pointer;
}
[id^=soul-filter-list] ul li:hover {
    background-color: deepskyblue;
}
[id^=soul-filter-list] i.layui-icon {
    display: inline-block;
    width: 16px;
}
[id^=soul-dropList] ul {
    border: 0;
    max-height: 116px;
    overflow-y: auto;
}
[id^=soul-dropList] ul li, [id^=soul-filter-list] [id^=soul-columns]>li{
    padding: 2px 10px;
}
[id^=soul-dropList] .check {
    padding: 5px 10px;
}
.filter-search {
    padding: 5px 10px 0 10px;
}
[id^=soul-condition] .layui-inline {
    width: 100px;
}
[id^=soul-condition] table.condition-table tr>td {
    padding: 0 3px;
}
[id^=soul-condition] table.condition-table tr>td:first-child {
    min-width: 60px;
}
[id^=soul-condition] .layui-form-switch {
    background-color: #1E9FFF;
    border: 1px solid #1E9FFF;
    width: 35px;
    margin-top: 0px;
}
[id^=soul-condition] .layui-form-switch.layui-form-onswitch {
    background-color: #5FB878;
    border: 1px solid #5FB878;
}
[id^=soul-condition] .layui-form-switch em {
    color: #fff!important;
}
[id^=soul-condition] .layui-form-switch i {
    background-color: #fff;
}
[data-type^=date][class$=Condition] {
    width: 273px;
}
/*表格筛选*/
[id^=soul-condition]>div{
    width: 270px;
}

.soul-condition-title {
    text-align: center;
    font-weight: bolder;
}

/*底部筛选*/
.soul-bottom-contion {
    height: 31px;
    /*line-height: 29px;*/
    border-top: solid 1px #e6e6e6;
}
.soul-bottom-contion .condition-items {
    display: inline-block;
    width: calc(100vw - 100px);
    height: 30px;
    float: left;
    overflow: hidden;
    white-space: nowrap;
}
.soul-bottom-contion .condition-item>div {
    display: inline-block;
    height: 28px;
    line-height: 28px;
    cursor: pointer;
}
.soul-bottom-contion .condition-items .condition-item>div[class^='item-']:hover{
    text-decoration: underline;
}
.soul-bottom-contion .condition-items .condition-item{
    padding: 0 10px;
    margin: 0 2px;
    font-weight: bold;
    border: solid 1px darkslateblue;
    border-radius: 10px;
    display: inline-block;
    height: 28px;
    position: relative;
}
.soul-bottom-contion .editCondtion {
    height: 30px;
    float: right;
}
.soul-bottom-contion .item-value {
    min-width: 20px;
    display: inline-block;
}
.soul-bottom-contion .editCondtion a {
    border: hidden;
    border-left: solid 1px #e6e6e6;
    height: 28px;
    line-height: 29px;
}
.soul-bottom-contion .condition-items .condition-item .condition-item-close {
    position: absolute;
    cursor: pointer;
    margin-top: -8px;
}
.soul-bottom-contion .condition-items>.condition-item>.condition-item-close {
    margin-top: -2px;
}
.soul-bottom-contion .condition-items .condition-item .condition-item-close:hover{
    color: red
}
.soul-bottom-contion .condition-items .condition-item .condition-item-close:before {
    background: white;
    border-radius: 10px;
}
.soul-edit-out {
    padding: 10px;
}
[id^=soul-bf] li {
    padding: 0px 10px;
    height: 22px;
    line-height: 22px;
    color: #000;
    cursor: pointer;
}
[id^=soul-bf] li.soul-bf-selected {
    background-color: deepskyblue;
}
[id^=soul-bf] li:hover {
    background-color: deepskyblue;
}
.soul-edit-out .tempValue {
    height: 25px;
}
.soul-bf-condition-value {
    display: inline;
    width: 100px;
}
/*子表格*/
.layui-table tbody tr.noHover:hover {
    background-color: white;
}

/*编辑筛选*/
.soul-edit-out .layui-form-radio {
    margin: 0;
}
.soul-edit-out ul li > div {
    display: inline-block;
    margin-right: 10px;
    height: 25px;
    vertical-align: top;
    cursor: pointer;
}
.soul-edit-out ul.group {
    padding-left: 50px;
}
.soul-edit-out ul.group.line {
    border-left: 1px dashed grey;
}
.soul-edit-out ul li {
    line-height: 25px;
}
.soul-edit-out table {
    display: inline-block;
}
.soul-edit-out table td[data-type='top'] {
    width: 12px;
    height: 12px;
    border-left: dashed 1px grey;
    border-bottom: dashed 1px grey;
}
.soul-edit-out table td[data-type='bottom'] {
    width: 12px;
    height: 12px;
    border-left: dashed 1px grey;
}
.soul-edit-out li.last>div>table td[data-type='bottom'] {
    border-left: none;
}
.soul-edit-out .layui-form-switch {
    background-color: #1E9FFF;
    border: 1px solid #1E9FFF;
    width: 35px;
    margin-top: 0px;
}
.soul-edit-out .layui-form-switch em {
    color: #fff!important;
}
.soul-edit-out .layui-form-switch i {
    background-color: #fff;
}
.soul-edit-out .layui-form-switch.layui-form-onswitch {
    background-color: #5FB878;
    border: 1px solid #5FB878;
}
.soul-edit-out .delete-item {
    display: none;
}
.soul-edit-out li:hover>.delete-item {
    display: inline-block;
}

/* 拖拽相关 */
#column-remove {
    position: absolute;
    z-index: 2147483647;
}
.layui-table-box.no-left-border td.isDrag, .layui-table-box.no-left-border th.isDrag {
    border-left: inherit!important;
}
.soul-drag-bar {
    position: absolute;
    top: 100px;
    z-index: 200;
    left: 50%;
    font-weight: 900;
    color: white;
    box-shadow: 0 1px 20px rgba(0,0,0,.15);
    text-align: center;
    transform: translateX(100vw);
    /*transition: transform .3s;*/
}
.soul-drag-bar.active {
    transform: translateX(-98px);
}
.soul-drag-bar > div {
    display: inline-block;
    padding: 10px;
    cursor: crosshair;
    width: 62px;
    background-color: rgba(0, 150, 136, 0.5);
}
.soul-drag-bar > div.active, .soul-drag-bar[data-type='left']>div[data-type='left'], .soul-drag-bar[data-type='right']>div[data-type='right'], .soul-drag-bar[data-type='none']>div[data-type='none']  {
    background-color: rgb(0, 150, 136);
}

/* 动画 */
.animated {
    -webkit-animation-duration: 1s;
    -moz-animation-duration: 1s;
    -o-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;
}
@-moz-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -moz-transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}

@-o-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -o-transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        -o-transform: translateX(0);
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.animated.fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    -moz-animation-name: fadeInLeft;
    -o-animation-name: fadeInLeft;
    animation-name: fadeInLeft;
}

@-webkit-keyframes fadeOutLeft {
    0% {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
    100% {
        opacity: 0;
        -webkit-transform: translateX(-20px);
    }
}

@-moz-keyframes fadeOutLeft {
    0% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
    100% {
        opacity: 0;
        -moz-transform: translateX(-20px);
    }
}

@-o-keyframes fadeOutLeft {
    0% {
        opacity: 1;
        -o-transform: translateX(0);
    }
    100% {
        opacity: 0;
        -o-transform: translateX(-20px);
    }
}

@keyframes fadeOutLeft {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-20px);
    }
}

.animated.fadeOutLeft {
    -webkit-animation-name: fadeOutLeft;
    -moz-animation-name: fadeOutLeft;
    -o-animation-name: fadeOutLeft;
    animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
}

@-moz-keyframes fadeInRight {
    0% {
        opacity: 0;
        -moz-transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}

@-o-keyframes fadeInRight {
    0% {
        opacity: 0;
        -o-transform: translateX(20px);
    }
    100% {
        opacity: 1;
        -o-transform: translateX(0);
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        transform: translateX(20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.animated.fadeInRight {
    -webkit-animation-name: fadeInRight;
    -moz-animation-name: fadeInRight;
    -o-animation-name: fadeInRight;
    animation-name: fadeInRight;
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
}

@-moz-keyframes fadeInUp {
    0% {
        opacity: 0;
        -moz-transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -moz-transform: translateY(0);
    }
}

@-o-keyframes fadeInUp {
    0% {
        opacity: 0;
        -o-transform: translateY(20px);
    }
    100% {
        opacity: 1;
        -o-transform: translateY(0);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated.fadeInUp {
    -webkit-animation-name: fadeInUp;
    -moz-animation-name: fadeInUp;
    -o-animation-name: fadeInUp;
    animation-name: fadeInUp;
}

@-webkit-keyframes fadeOutDown {
    0% {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
    100% {
        opacity: 0;
        -webkit-transform: translateY(20px);
    }
}

@-moz-keyframes fadeOutDown {
    0% {
        opacity: 1;
        -moz-transform: translateY(0);
    }
    100% {
        opacity: 0;
        -moz-transform: translateY(20px);
    }
}

@-o-keyframes fadeOutDown {
    0% {
        opacity: 1;
        -o-transform: translateY(0);
    }
    100% {
        opacity: 0;
        -o-transform: translateY(20px);
    }
}

@keyframes fadeOutDown {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateY(20px);
    }
}

.animated.fadeOutDown {
    -webkit-animation-name: fadeOutDown;
    -moz-animation-name: fadeOutDown;
    -o-animation-name: fadeOutDown;
    animation-name: fadeOutDown;
}

#soul-table-contextmenu-wrapper {
    width: 0;
}
.soul-table-contextmenu {
    position: absolute;
    z-index: 2147483647;
    list-style: none;
    margin: 0;
    padding: 0;
    border: 1px solid #ebeef5;
    box-shadow: 2px 2px 4px -2px rgba(0,0,0,.2);
    background: white;
}
.soul-table-contextmenu li {
    line-height: 26px;
    padding: 0 30px;
    cursor: pointer;
    word-break: keep-all;
}
.soul-table-contextmenu li:hover {
    background: #c5c5c5;
}
.soul-table-contextmenu li i.prefixIcon{
    position: absolute;
    left: 8px;
}

.soul-table-contextmenu li i.endIcon{
    position: absolute;
    right: 8px;
}

/*拖拽相关*/
.layui-table-sort-invalid {
    width: 10px;
    height: 20px;
    margin-left: 5px;
    cursor: pointer!important;
}
.layui-table-sort-invalid .layui-table-sort-asc {
    top: 3px;
    border-top: none;
    border-bottom-style: solid;
    border-bottom-color: #b2b2b2;
}
.layui-table-sort-invalid .layui-edge {
    position: absolute;
    left: 5px;
    border-width: 5px;
}
.layui-table-sort-invalid .layui-table-sort-desc {
    bottom: 5px;
    border-bottom: none;
    border-top-style: solid;
    border-top-color: #b2b2b2;
}
.layui-table-sort-invalid .layui-edge {
    position: absolute;
    left: 5px;
    border-width: 5px;
}
.noselect {

    -webkit-touch-callout: none; /* iOS Safari */

    -webkit-user-select: none; /* Chrome/Safari/Opera */

    -khtml-user-select: none; /* Konqueror */

    -moz-user-select: none; /* Firefox */

    -ms-user-select: none; /* Internet Explorer/Edge */

    user-select: none; /* Non-prefixed version, currently */

}
/* 固定列滚动 */
.soul-fixed-scroll::-webkit-scrollbar{
    display: none
}
.soul-fixed-scroll{
    overflow-y: auto!important;
    -ms-overflow-style:none;
    overflow:-moz-scrollbars-none;
}
