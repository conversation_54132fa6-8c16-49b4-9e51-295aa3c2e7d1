<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 机器人头部背景 -->
  <circle cx="32" cy="32" r="30" fill="url(#robotGradient)" stroke="#667eea" stroke-width="2"/>
  
  <!-- 机器人眼睛 -->
  <circle cx="24" cy="26" r="4" fill="#667eea"/>
  <circle cx="40" cy="26" r="4" fill="#667eea"/>
  <circle cx="24" cy="26" r="2" fill="white"/>
  <circle cx="40" cy="26" r="2" fill="white"/>
  
  <!-- 机器人嘴巴 -->
  <rect x="26" y="36" width="12" height="4" rx="2" fill="#667eea"/>
  
  <!-- 机器人天线 -->
  <line x1="32" y1="8" x2="32" y2="2" stroke="#667eea" stroke-width="2" stroke-linecap="round"/>
  <circle cx="32" cy="2" r="2" fill="#764ba2"/>
  
  <!-- 机器人耳朵 -->
  <rect x="8" y="28" width="6" height="8" rx="3" fill="#667eea"/>
  <rect x="50" y="28" width="6" height="8" rx="3" fill="#667eea"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="robotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e8f2ff;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
