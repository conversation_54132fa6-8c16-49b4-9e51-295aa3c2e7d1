/* !
 * iPicker v4.0.4
 * Copyright (C) 2020-present, ZG
 * Released under the MIT license.
 */
!(function(){"use strict";function _slicedToArray(e,i){return _arrayWithHoles(e)||_iterableToArrayLimit(e,i)||_unsupportedIterableToArray(e,i)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,i){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,n,a,o,c=[],l=!0,s=!1;try{if(a=(t=t.call(e)).next,0===i){if(Object(t)!==t)return;l=!1}else for(;!(l=(r=a.call(t)).done)&&(c.push(r.value),c.length!==i);l=!0);}catch(e){s=!0,n=e}finally{try{if(!l&&null!=t.return&&(o=t.return(),Object(o)!==o))return}finally{if(s)throw n}}return c}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _createForOfIteratorHelper(e,i){var t,r,n,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(t=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return t=e.done,e},e:function(e){r=!0,n=e},f:function(){try{t||null==o.return||o.return()}finally{if(r)throw n}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray(e))||i&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:i=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:i};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,i){var t;if(e)return"string"==typeof e?_arrayLikeToArray(e,i):"Map"===(t="Object"===(t=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:t)||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,i):void 0}function _arrayLikeToArray(e,i){(null==i||i>e.length)&&(i=e.length);for(var t=0,r=new Array(i);t<i;t++)r[t]=e[t];return r}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,i){"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(e=e||self).iPicker=i()}("undefined"!=typeof window?window:void 0,function(){var I={type:function(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()},isNotEmptyPlainObject:function(e){return!("object"!==I.type(e)||!Object.keys(e).length)},isCorrectNumber:function(e,i){return!(!Number.isSafeInteger(e)||!(i?0<=e:0<e))},isFunction:function(e){return"function"===I.type(e)},isPromise:function(e){return"promise"===I.type(e)},uid:function(e){var i=Math.random().toString(36).slice(2,10);return e?Symbol(i):i},delay:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;return new Promise(function(e){var i=window.setTimeout(function(){window.clearTimeout(i),i=null,e()},t)})},mergeParam:function(){var e,i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=1<arguments.length?arguments[1]:void 0,r={};for(e in t){var n=i[e];"object"===I.type(n)?r[e]=I.mergeParam(n,t[e]):r[e]=0===n?n:n||t[e]}return r}},z=function(e){var i=[];if("string"==typeof e&&(i=document.querySelectorAll(e)),e.nodeType||e===document)i.push(e);else if(0<e.length&&e[0]&&e[0].nodeType)for(var t=0,r=e.length;t<r;t++)i.push(e[t]);return new n(i)};function n(e){var i=e[0]?e.length:0;this.length=i;for(var t=0;t<i;t++)this[t]=e[t];return this}function j(e,i){if(e&&i&&"string"==typeof e&&e.trim()&&I.isNotEmptyPlainObject(i)&&I.isNotEmptyPlainObject(i.data)&&i.data.source&&(I.isFunction(i.data.source)||I.isPromise(i.data.source))){var a=z(e),n=a.get();if(n){var o=I.mergeParam(i,L),c=((!I.isCorrectNumber(o.level)||o.level<1||3<o.level)&&(o.level=3),"select"===o.theme),l="cascader"===o.theme,s="panel"===o.theme,t=I.isPromise(o.data.source),r=I.isFunction(o.onClear),d=I.isFunction(o.onSelect),u=n.iPickerID||I.uid(!0),h=(A.originalElem.set(n,e),A.options.set(n,o),A.target.set(u,n),A.id.set(n,u),n.iPickerID=u,document.getElementById(C)||document.head.insertAdjacentHTML("afterbegin",'\n <style id="'.concat(C,'">.iPicker-target{position:relative;height:34px}.iPicker-container,.iPicker-target *{box-sizing:border-box;font-size:14px;margin:0;padding:0}.iPicker-container{height:34px;float:left;position:relative}.iPicker-container:not(:first-of-type){margin-left:10px}.iPicker-result{display:flex;align-items:center;position:relative;background:#fff;border:#d6d6d6 solid 1px;min-width:100px;height:34px;border-radius:2px;cursor:pointer;user-select:none}.iPicker-result.iPicker-result-active:not(.iPicker-disabled),.iPicker-result.iPicker-result-active:not(.iPicker-disabled):hover{border:#00b8ff solid 1px}.iPicker-result.iPicker-result-active i::before{transform:scale(.55) rotate(180deg)}.iPicker-result.iPicker-result-active i.arrow-outline::before{transform:scale(.72) rotate(180deg)}.iPicker-result i{position:absolute;top:0;right:0;display:block;width:30px;height:34px}.iPicker-result i::before{position:absolute;top:0;right:2px;display:block;width:28px;height:100%;background-position:center;background-repeat:no-repeat;content:"";opacity:.5;transition:transform .2s;transform:scale(.55)}.iPicker-result i.arrow-icon::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTc2OTk1MjQ3Njc4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjI2NTAiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUzNS40NjY2NjcgODEyLjhsNDUwLjEzMzMzMy01NjMuMmMxNC45MzMzMzMtMTkuMiAyLjEzMzMzMy00OS4wNjY2NjctMjMuNDY2NjY3LTQ5LjA2NjY2N0g2MS44NjY2NjdjLTI1LjYgMC0zOC40IDI5Ljg2NjY2Ny0yMy40NjY2NjcgNDkuMDY2NjY3bDQ1MC4xMzMzMzMgNTYzLjJjMTIuOCAxNC45MzMzMzMgMzQuMTMzMzMzIDE0LjkzMzMzMyA0Ni45MzMzMzQgMHoiIHAtaWQ9IjI2NTEiIGZpbGw9IiMwMDAwMDAiPjwvcGF0aD48L3N2Zz4=)}.iPicker-result i.clear-icon::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjA3Njc2MTg3NDk0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjMzNDIiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45MTg2NDcgMTYzLjE1NDkxN2MtMTkzLjQzODY0MSAwLTM1MS42OTcwMzcgMTU4LjI1NDMwNC0zNTEuNjk3MDM3IDM1MS42OTkwODQgMCAxOTMuNDM5NjY0IDE1OC4yNTczNzQgMzUxLjY5NzAzNyAzNTEuNjk3MDM3IDM1MS42OTcwMzcgMTkzLjM5NDYzOCAwIDM1MS42NTMwMzUtMTU4LjI1NjM1IDM1MS42NTMwMzUtMzUxLjY5NzAzN0M4NjMuNTcwNjU5IDMyMS40MDkyMjEgNzA1LjMxMzI4NiAxNjMuMTU0OTE3IDUxMS45MTg2NDcgMTYzLjE1NDkxN002ODcuNzM2OTc4IDY0MS40NTIzMjdsLTQ5LjE5ODUxNSA0OS4yMjQwOTgtMTI2LjYxOTgxNi0xMjYuNjAwMzczLTEyNi41NzM3NjcgMTI2LjYwMDM3My00OS4zMDQ5MzktNDkuMjI0MDk4IDEyNi42MzYxODktMTI2LjU5ODMyNi0xMjYuNjM2MTg5LTEyNi42MDAzNzMgNDkuMzA0OTM5LTQ5LjIyNDA5OCAxMjYuNTczNzY3IDEyNi42MDAzNzMgMTI2LjYxOTgxNi0xMjYuNjAwMzczIDQ5LjE5ODUxNSA0OS4yMjQwOTgtMTI2LjU3Mzc2NyAxMjYuNjAwMzczTDY4Ny43MzY5NzggNjQxLjQ1MjMyN3oiIHAtaWQ9IjMzNDMiIGZpbGw9IiMwMDAwMDAiPjwvcGF0aD48L3N2Zz4=);transform:scale(1);z-index:10}.iPicker-result i.arrow-outline::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjA1MDYzNzA0MzAzIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NDMiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMiA3MzBjLTYuNCAwLTEyLjgtMi40LTE3LjctNy4zbC0zODYtMzg2Yy05LjgtOS44LTkuOC0yNS42IDAtMzUuNCA5LjgtOS44IDI1LjYtOS44IDM1LjQgMEw1MTIgNjY5LjZsMzY4LjMtMzY4LjNjOS44LTkuOCAyNS42LTkuOCAzNS40IDAgOS44IDkuOCA5LjggMjUuNiAwIDM1LjRsLTM4NiAzODZjLTQuOSA0LjktMTEuMyA3LjMtMTcuNyA3LjN6IiBmaWxsPSIjMzMzMzMzIiBwLWlkPSIzNDQ0Ij48L3BhdGg+PC9zdmc+);transform:scale(.72);opacity:.9}.iPicker-result.iPicker-disabled{cursor:not-allowed;background:#f2f5fa;color:#898989;border-color:#dfdfdf}.iPicker-result.iPicker-disabled input{cursor:not-allowed;background:#f2f5fa;color:#898989}.iPicker-input{display:block;width:100%;width:calc(100% - 23px);height:32px;padding:0 10px;outline:0;border:0;cursor:pointer;user-select:none}.iPicker-result input::selection{background:#fff}.iPicker-list.iPicker-list-ontop{transform-origin:center bottom}.iPicker-list-show-temporary{display:block!important;opacity:0!important;pointer-events:none!important}.iPicker-input::-webkit-input-placeholder{color:#aaa}.iPicker-input::-moz-placeholder{color:#aaa}.iPicker-target[data-theme=panel] .iPicker-list{width:370px}.iPicker-list{position:relative;z-index:10;display:none;overflow-x:hidden;overflow-y:auto;overscroll-behavior:contain;user-select:none;background:#fff;border:#ddd solid 1px;box-shadow:rgba(0,0,0,.12) 0 2px 6px;transform-origin:center top;transform:scaleY(0);transition-property:transform,opacity;transition-duration:.2s}.iPicker-list ul{display:block;overflow:hidden}.iPicker-list li{cursor:pointer;transition:.2s}.iPicker-list li span{pointer-events:none}.iPicker-list li.iPicker-list-active,.iPicker-list li:hover{color:#00b8ff;background:rgba(0,184,255,.1)}.iPicker-list li.iPicker-list-disabled{cursor:not-allowed;background:#f2f5fa;color:#b6b6b6}.iPicker-panel-tab{display:flex;align-items:center;justify-content:flex-start;height:36px;background-color:#f5f5f5;border-bottom:#ddd solid 1px}.iPicker-panel-tab>div{cursor:pointer;padding:0 20px;height:36px;line-height:36px}.iPicker-panel-tab>div:last-child.iPicker-panel-tab-active,.iPicker-panel-tab>div:not(:last-child){border-right:#d3d3d3 solid 1px}.iPicker-panel-tab>div.iPicker-panel-tab-active{background:#fff;cursor:default;position:relative;height:37px;border-bottom:#fff solid 1px;color:#00b8ff}.iPicker-panel-content{padding:10px 0;min-height:50px;overflow-x:hidden;overflow-y:auto}.iPicker-panel-content ul{display:none}.iPicker-panel li{float:left;display:block;margin:2px 4px;padding:4px 10px;border-radius:2px}.iPicker-panel li span{font-size:13px}.iPicker-cascader li,.iPicker-select li{position:relative;display:block;padding:6px 12px;list-style:none;transition:.25s;overflow:hidden;clear:both;word-break:break-all}.iPicker-cascader li:first-child,.iPicker-select li:first-child{margin-top:8px}.iPicker-cascader li:last-child,.iPicker-select li:last-child{margin-bottom:8px}.iPicker-list.iPicker-cascader{overflow:hidden}.iPicker-cascader ul{position:relative;z-index:4;display:none;width:200px;float:left;overflow-y:auto;overscroll-behavior:contain}.iPicker-cascader ul:nth-child(2){z-index:3;margin-left:-2px}.iPicker-cascader ul:nth-child(3){z-index:2;margin-left:0}.iPicker-cascader ul:nth-child(4){z-index:1}.iPicker-cascader ul:not(:last-child){border-right:#dfdfdf solid 1px}.iPicker-cascader li i{display:block;position:absolute;top:50%;right:10px;width:8px;height:8px;margin-top:-4px;border-top:#6f6f6f solid 1px;border-right:#6f6f6f solid 1px;transform:scale(.8) rotate(45deg)}.iPicker-cascader li.iPicker-list-disabled i{opacity:.4}.iPicker-cascader li.iPicker-list-active i{border-top-color:#00b8ff;border-right-color:#00b8ff}.iPicker-cascader ul:last-child li i{display:none}.iPicker-list.iPicker-list-show{display:block;transform:scaleY(1)}.iPicker-list.iPicker-list-hide{transform:scaleY(0);opacity:0}</style>\n')),T(a,o,u),a.find(".iPicker-container")),f=a.find(".iPicker-result"),p=a.find(".iPicker-input"),g=a.find(".iPicker-list"),b=g.find("ul");if(b.each(function(e){z(this).data("level",++e)}),I.isCorrectNumber(o.maxHeight)&&100<=o.maxHeight&&(g.css("maxHeight","".concat(o.maxHeight,"px")),l&&b.css("maxHeight","".concat(o.maxHeight,"px")),s)&&g.find(".iPicker-panel-content").css("height","".concat(o.maxHeight-38,"px")),I.isCorrectNumber(o.width)&&100<=o.width&&(f.css("width","".concat(o.width,"px")),c)&&g.css("width","".concat(o.width,"px")),"string"==typeof o.width&&o.width.trim().endsWith("%")&&(f.css("width",o.width),(c?g:h).css("width",o.width)),I.isCorrectNumber(o.height)&&20<=o.height&&(f.css("height","".concat(o.height,"px")),p.css("height","".concat(o.height-2,"px")),p.next().css("height","".concat(o.height-2,"px"))),!0===o.disabledItem&&new MutationObserver(function(){a.find("li").addClass("iPicker-list-disabled")}).observe(n,{childList:!0,subtree:!0}),Array.isArray(o.disabledItem)&&o.disabledItem.length)for(var v=0,y=_toConsumableArray(new Set(o.disabledItem));v<y.length;v++)!function(){var i=y[v];new MutationObserver(function(){var e=n.querySelector('[data-code="'.concat(i,'"]:not(.iPicker-list-disabled)'));e&&e.classList.add("iPicker-list-disabled")}).observe(n,{childList:!0,subtree:!0})}();if(!0===o.disabled&&(o.disabled=[1,2,3].slice(0,o.level)),I.isCorrectNumber(o.disabled)&&(o.disabled=[o.disabled]),Array.isArray(o.disabled)&&o.disabled.length)for(var m=0,k=_toConsumableArray(new Set(o.disabled));m<k.length;m++){var M=k[m];I.isCorrectNumber(M)&&1<=M&&M<=3&&f.eq(M-1).addClass("iPicker-disabled")}c&&Array.isArray(o.placeholder)&&o.placeholder.forEach(function(e,i){var t=p.eq(i).get();t&&t.setAttribute("placeholder",o.placeholder[i]||L.placeholder[i])}),(l||s)&&("string"==typeof o.placeholder&&o.placeholder.trim()||(o.placeholder="请选择地区"),p.eq(0).get().setAttribute("placeholder",o.placeholder)),I.isCorrectNumber(o.radius,!0)&&f.add(p).css("borderRadius","".concat(o.radius,"px")),f.find(".clear-icon").hide(),f.each(function(){var i=this,t=z(this).get();t.addEventListener("mouseenter",function(){var e=t.querySelector("input");e&&e.value&&!t.classList.contains("iPicker-disabled")&&z(i).find(".clear-icon").show().prev().hide()}),t.addEventListener("mouseleave",function(){z(i).find(".clear-icon").hide().prev().show()})});var P=a.find(".clear-icon");return c?P.each(function(){var t=z(this);t.click(function(){t.hide().prev().show();var e=t.parent(),i=e.next().find("ul");i.data("index");e.find("input").val(""),i.find(".iPicker-list-active").removeClass("iPicker-list-active"),e.parent().nextAll().find("input").val("").parent().next().find("ul").html(""),N(),x(e.next()),r&&o.onClear()})}):P.click(function(){P.hide().prev().show(),j.clear(u),r&&o.onClear()}),f.each(function(){z(this).find("input, .arrow-icon").click(function(){var i,t,r=z(this).parent(),n=r.next(),e=r.parent().parent().data("id"),e=z('.iPicker-target:not([data-id="'.concat(e,'"]) .iPicker-list'));e.length&&x(e),r.hasClass("iPicker-disabled")||n.find("li").length&&(r.toggleClass("iPicker-result-active"),n.hasClass("iPicker-list-show")?x(n):(s&&(a.find(".iPicker-panel-tab > div:first-child").addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active"),a.find(".iPicker-panel-content > ul:first-child").show().siblings().hide()),i=!1,t=parseInt(r.css("height")),(e=function(){var e;i||(i=!0,n.addClass("iPicker-list-show-temporary"),document.documentElement.clientHeight-r.get().getBoundingClientRect().bottom<(e=parseInt(n.css("height")))?n.css("marginTop","-".concat(e+t,"px")).addClass("iPicker-list-ontop"):n.css("marginTop","0px").removeClass("iPicker-list-ontop"),i=!1,n.removeClass("iPicker-list-show-temporary"))})(),window.addEventListener("scroll",e),window.addEventListener("resize",e),n.addClass("iPicker-list-show").removeClass("iPicker-list-hide")))})}),w(t?"86":null,1).then(function(){if(n.dataset.promise="true",Array.isArray(o.selected)&&o.selected.length){o.selected=_toConsumableArray(new Set(o.selected));var e,i=_createForOfIteratorHelper(o.selected);try{for(i.s();!(e=i.n()).done;){var t=e.value;if(o.disabledItem.includes(t))return}}catch(e){i.e(e)}finally{i.f()}!function e(i){w(o.selected[i-1],i+1).then(function(){++i<o.level?e(i):(o.selected.forEach(function(e){a.find('li[data-code="'.concat(e,'"]')).addClass("iPicker-list-active")}),N(),I.isFunction(o.selectedCallback)&&o.selectedCallback())})}(1)}}),a.click(function(e){var i;"li"!==e.target.nodeName.toLowerCase()||(i=(e=z(e.target)).parent(),e.hasClass("iPicker-list-disabled"))||(e.addClass("iPicker-list-active").siblings().removeClass("iPicker-list-active"),w(e.data("code"),+i.data("level")+1),c&&(x(i.parent()),i.parent().parent().nextAll().find(".iPicker-result input").val("")),i.index()===o.level-1&&(l&&x(i.parent()),s)&&x(i.parent().parent()))}),l&&b.css({minHeight:"".concat(o.maxHeight,"px"),maxHeight:"".concat(o.maxHeight,"px")}),s&&a.find(".iPicker-panel-tab > div").click(function(){var e=z(this).index();z(this).parent().next().find("ul").eq(e).find("li").length&&(z(this).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active"),a.find(".iPicker-panel-content ul").eq(z(this).index()).show().siblings().hide())}),z(document).click(function(i){h.each(function(e){i.target===this||this.contains(i.target)||x(g.eq(e))})}),u}}function x(e){var t,r;e.hasClass("iPicker-list-show")&&(e.addClass("iPicker-list-hide").removeClass("iPicker-list-show").prev().removeClass("iPicker-result-active"),e.show(),I.delay(200).then(function(){e.get().style.removeProperty("display")}),c||N(),t=e.parent().parent(),(r=A.options.get(t.get())).strict)&&I.delay(200).then(function(){var i=_slicedToArray(A.value.get(t.get()),1)[0].length;i&&i!==r.level&&new Promise(function(e){1!==i||2===r.level?e():w(b.eq(1).find("li:first-child").data("code"),3).then(function(){e()})}).then(function(){b.each(function(){z(this).find(".iPicker-list-active").length||z(this).find("li:first-child").addClass("iPicker-list-active")}),N()})})}function w(e,n){return new Promise(function(r){D(e,c&&3<n?3:n,o,t).then(function(e){S(e,o,t).then(function(e){var i,t=b.eq(n-1);t.html(e).nextAll().html(""),c&&t.parent().parent().nextAll().find("ul").html(""),l&&(i=0,b.each(function(){this.innerHTML&&i++}),g.css("width","".concat(200*i,"px")),b.eq(n-1).show().nextAll().hide()),s&&(b.eq(n-1).show().siblings().hide(),a.find(".iPicker-panel-tab > div:nth-child(".concat(n,")")).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active")),(c||n<=o.level)&&N(),r()})})})}function N(){I.delay(10).then(function(){var e,i=E(a),t=(O(n,i),o.separator.trim().charAt(0));function r(e){return e=e&&(l||s)&&o.onlyShowLastLevel?e.split(t).slice(-1)[0].trim():e}c?i[1].forEach(function(e,i){p.eq(i).val(r(e))}):(e=i[1].join(" ".concat(t," ")),p.eq(0).val(r(e))),d&&i[1].length&&o.onSelect.apply(o,_toConsumableArray(A.value.get(n)))})}}function a(e){return e.filter(function(e){return"string"==typeof e&&e.match(/^\d{6,12}$/)})}var L={theme:"select",data:{props:{code:"code",name:"name"},source:null,when:null},level:3,radius:2,width:200,height:34,maxHeight:300,disabled:[],disabledItem:[],selected:[],selectedCallback:function(){},placeholder:["省","市","区"],separator:"/",clearable:!(n.prototype={each:function(e){for(var i=0,t=this.length;i<t;i++)e.call(this[i],i,this[i]);return this},get:function(){return this[0<arguments.length&&void 0!==arguments[0]?arguments[0]:0]},click:function(i){return this.each(function(){this.addEventListener("click",function(e){i.call(this,e)})})},hasClass:function(e){return this[0].classList.contains(e)},addClass:function(r){return this.each(function(){var e,i=_createForOfIteratorHelper(r.split(" "));try{for(i.s();!(e=i.n()).done;){var t=e.value;this.classList.add(t)}}catch(e){i.e(e)}finally{i.f()}})},removeClass:function(r){return this.each(function(){var e,i=_createForOfIteratorHelper(r.split(" "));try{for(i.s();!(e=i.n()).done;){var t=e.value;this.classList.remove(t)}}catch(e){i.e(e)}finally{i.f()}})},toggleClass:function(r){return this.each(function(){var e,i=_createForOfIteratorHelper(r.split(" "));try{for(i.s();!(e=i.n()).done;){var t=e.value;this.classList.toggle(t)}}catch(e){i.e(e)}finally{i.f()}})},css:function(i,t){function r(e,i,t){e.style[i]=t}return"string"!=typeof i||t?this.each(function(){if(i&&t&&r(this,i,t),I.isNotEmptyPlainObject(i)&&!t)for(var e in i)r(this,e,i[e])}):(e=this[0],n=i,document.defaultView.getComputedStyle(e,null).getPropertyValue(n));var e,n},html:function(e){return this.each(function(){this.innerHTML=e})},text:function(){return this[0].textContent},val:function(e){return this.each(function(){this.value=e})},eq:function(e){var i;if("number"==typeof e)return i=[],e<this.length&&i.push(this[e]),z(i)},index:function(){if(this[0]){for(var e=this[0],i=0;null!==(e=e.previousSibling);)1===e.nodeType&&i++;return i}},prev:function(){var i=[];return this.each(function(){var e=this.previousElementSibling;e&&i.push(e)}),z(i)},next:function(){var i=[];return this.each(function(){var e=this.nextElementSibling;e&&i.push(e)}),z(i)},nextAll:function(){var t=[];return this.each(function(){var i=this.nextElementSibling;!function e(){i&&(t.push(i),i=z(i).get().nextElementSibling,e())}()}),z(t)},parent:function(){var e=[];return this.each(function(){e.push(this.parentNode)}),z(e)},find:function(r){var n=[];return this.each(function(){for(var e=this.querySelectorAll(r),i=0,t=e.length;i<t;i++)1===e[i].nodeType&&n.push(e[i])}),z(n)},siblings:function(){var r=[];return this.each(function(){for(var e=this.parentNode.children,i=0,t=e.length;i<t;i++)e[i]!==this&&r.push(e[i])}),z(r)},add:function(e){for(var i=z(e),t=0,r=i.length;t<r;t++)this[this.length]=i[t],this.length++;return this},data:function(i,t){return"string"!=typeof i||t?this.each(function(){if(i&&t)this.dataset[i]=t;else for(var e in i)this.dataset[e]=i[e]}):this[0].dataset[i]},remove:function(){return this.each(function(){this.parentNode&&this.parentNode.removeChild(this)})},show:function(){return this.each(function(){this.style.display="block"})},hide:function(){return this.each(function(){this.style.display="","none"!==z(this).css("display")&&(this.style.display="none")})}}),strict:!1,onlyShowLastLevel:!1,icon:"arrow",onClear:function(){},onSelect:function(){}},A={originalElem:new WeakMap,options:new WeakMap,value:new WeakMap,id:new WeakMap,target:new Map},c=new Map,C="iPicker-default-style",T=function(e,i,t){var r=i.theme,n=i.level,a=i.icon,i=i.clearable,o='\n <div class="iPicker-container">\n  <div class="iPicker-result">\n<input \n type="text" \n autocomplete="off" \n spellcheck="false"\n class="iPicker-input"\n readonly\n> \n<i class="arrow-icon '.concat("arrow-outline"===a?"arrow-outline":"arrow-triangle",'"></i>\n').concat(i?'<i class="clear-icon"></i>':"",'\n  </div>\n  <div class="iPicker-list iPicker-').concat(r,'">___</div>\n </div>\n');switch(r){case"select":o=o.replace("___","<ul></ul>").repeat(n);break;case"cascader":o=o.replace("___","<ul></ul>".repeat(n));break;case"panel":o=o.replace("___",'\n<div class="iPicker-panel-tab">\n <div class="iPicker-panel-tab-active">省份</div>\n '.concat(1<n?"<div>城市</div>":"","\n ").concat(2<n?"<div>区县</div>":"",'\n</div>\n<div class="iPicker-panel-content">').concat("<ul></ul>".repeat(n),"</div>\n  "))}e.addClass("iPicker-target").html(o).data({theme:r,id:t.toString().replace(/(\(|\))/g,"")})},S=function(c,l,s){return new Promise(function(e){var i="",t="cascader"===l.theme;if(s)for(var r in c)i+='\n <li data-code="'.concat(r,'" data-name="').concat(c[r],'">\n  <span>').concat(c[r],"</span>\n  ").concat(t?"<i></i>":"","\n </li>\n");else{var n=l.data.props||{},a=n.code,o=n.name;c.forEach(function(e){i+='\n <li data-code="'.concat(e[a],'" data-name="').concat(e[o],'">\n  <span>').concat(e[o],"</span>\n  ").concat(t?"<i></i>":"","\n </li>\n")})}e(i)})},D=function(r,n,a,o){return new Promise(function(i){function t(e,i){return I.isFunction(a.data.when)?a.data.when(e,i):e}var e;o?a.data.source.then(function(e){i(t(e[r],n))}):(e=c.get(r))?i(t(e,n)):(e=a.data.source(r,n),"object"===I.type(e)&&I.isFunction(e.then)?e.then(function(e){c.set(r,e),i(t(e,n))}):i(t(e,n)))})},E=function(e){var e=e.find(".iPicker-list-active"),i=e.length,t=[],r=[],n=[];return i&&e.each(function(){var e=z(this).data("code"),i=z(this).data("name");t.push(e),r.push(i),n.push({code:e,name:i})}),[t,r,n]},O=function(e,i){A.value.set(e,i)};(j.create=j).set=function(e,n){var a=A.target.get(e);function i(){var r=a.querySelectorAll("ul");!function e(i){var t=a.querySelector('[data-code="'.concat(n[i],'"]'));r[i+1]?(new MutationObserver(function(){++i<n.length&&e(i)}).observe(r[i+1],{childList:!0}),t.click()):t&&t.click()}(0)}e&&a&&n&&Array.isArray(n)&&n.length&&(j.clear(e),a.dataset.promise?i():new MutationObserver(function(){i()}).observe(a,{attributes:!0}))},j.get=function(e,i){var t=A.target.get(e);if(e&&t)return e=A.value.get(t),"code"===i||void 0===i?e[0]:"name"===i?e[1]:"all"===i?e[2]:void 0},j.clear=function(e){var i,t,r=A.target.get(e);if(e&&r)return i=z(r),t=A.options.get(r),A.value.set(r,[[],[],[]]),i.find("input").val(""),i.find("li").removeClass("iPicker-list-active"),i.find("ul").each(function(e){var i=z(this);e&&(i.html(""),"cascader"===t.theme)&&(i.parent().css("width","200px"),i.get().style.removeProperty("display"))}),"panel"===t.theme&&(i.find(".iPicker-panel-tab > div").eq(0).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active"),i.find(".iPicker-panel-content > ul").eq(0).show().siblings().hide()),i.find(".iPicker-list").get().scrollTop=0,i.find("ul").get().scrollTop=0,e},j.reset=function(e){var i=A.target.get(e);if(e&&i)return j(A.originalElem.get(i),A.options.get(i))},j.destroy=function(e){var i=A.target.get(e);e&&i&&(A.originalElem.delete(i),A.value.delete(i),A.options.delete(i),A.id.delete(i),A.target.delete(i.iPickerID),delete i.iPickerID,i.innerHTML="",document.querySelector(".iPicker-container")||z("#".concat(C)).remove())},j.enabled=function(e,i){var t,r=A.target.get(e);if(e&&r&&i)return t=z(r).find(".iPicker-result"),!0===i&&t.removeClass("iPicker-disabled"),I.isCorrectNumber(i)&&(i=[i]),Array.isArray(i)&&i.length&&i.forEach(function(e){I.isCorrectNumber(e)&&1<=e&&e<=3&&t.eq(e-1).removeClass("iPicker-disabled")}),e},j.disabled=function(e,i){var t,r=A.target.get(e);if(e&&r&&i)return t=z(r).find(".iPicker-result"),!0===i&&t.addClass("iPicker-disabled"),I.isCorrectNumber(i)&&(i=[i]),Array.isArray(i)&&i.length&&i.forEach(function(e){I.isCorrectNumber(e)&&1<=e&&e<=3&&t.eq(e-1).addClass("iPicker-disabled")}),e};return j.enabledItem=function(e,t){var i,r,n=A.target.get(e);if(e&&n&&t)return i={childList:!0,subtree:!0},!0===t&&new MutationObserver(function(){z(n).find("li").removeClass("iPicker-list-disabled")}).observe(n,i),Array.isArray(t)&&t.length&&(r=(t=a(_toConsumableArray(new Set(t)))).length,new MutationObserver(function(){for(var e=0;e<r;e++){var i=n.querySelector('[data-code="'.concat(t[e],'"]'));i&&i.classList.remove("iPicker-list-disabled")}}).observe(n,i)),e},j.disabledItem=function(e,t){var i,r,n=A.target.get(e);if(e&&n&&t)return i={childList:!0,subtree:!0},!0===t&&new MutationObserver(function(){z(n).find("li").addClass("iPicker-list-disabled")}).observe(n,i),Array.isArray(t)&&t.length&&(r=(t=a(_toConsumableArray(new Set(t)))).length,new MutationObserver(function(){for(var e=0;e<r;e++){var i=n.querySelector('[data-code="'.concat(t[e],'"]'));i&&i.classList.add("iPicker-list-disabled")}}).observe(n,i)),e},j});})();