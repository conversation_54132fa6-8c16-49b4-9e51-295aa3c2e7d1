var t = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (t) {
    return typeof t
} : function (t) {
    return t && "function" == typeof Symbol && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t
};
(global.webpackJsonp = global.webpackJsonp || []).push([["common/vendor"], {
    "0194": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1c51")).default)
        }).call(this, n("543d").createPage)
    }, "0202": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a250")).default)
        }).call(this, n("543d").createPage)
    }, "023f": function (t, e, n) {
        function i(t) {
            for (var e = {}, n = t.split(","), i = 0; i < n.length; i += 1) e[n[i]] = !0;
            return e
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = /^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z0-9_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,
            r = /^<\/([-A-Za-z0-9_]+)[^>]*>/,
            o = /([a-zA-Z0-9_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,
            c = i("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),
            l = i("address,code,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,ins,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),
            s = i("a,abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),
            u = i("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),
            d = i("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected");
        e.default = function (t, e) {
            function n(t, n) {
                var i;
                if (n) for (n = n.toLowerCase(), i = g.length - 1; i >= 0 && g[i] !== n; i -= 1) ; else i = 0;
                if (i >= 0) {
                    for (var a = g.length - 1; a >= i; a -= 1) e.end && e.end(g[a]);
                    g.length = i
                }
            }

            var i, f, p, h = t, g = [];
            for (g.last = function () {
                return g[g.length - 1]
            }; t;) {
                if (f = !0, 0 === t.indexOf("</") ? (p = t.match(r)) && (t = t.substring(p[0].length), p[0].replace(r, n), f = !1) : 0 === t.indexOf("<") && (p = t.match(a)) && (t = t.substring(p[0].length), p[0].replace(a, function (t, i, a, r) {
                    if (i = i.toLowerCase(), l[i]) for (; g.last() && s[g.last()];) n("", g.last());
                    if (u[i] && g.last() === i && n("", i), (r = c[i] || !!r) || g.push(i), e.start) {
                        var f = [];
                        a.replace(o, function (t, e) {
                            var n = arguments[2] || arguments[3] || arguments[4] || (d[e] ? e : "");
                            f.push({name: e, value: n, escaped: n.replace(/(^|[^\\])"/g, '$1\\"')})
                        }), e.start && e.start(i, f, r)
                    }
                }), f = !1), f) {
                    i = t.indexOf("<");
                    for (var v = ""; 0 === i;) v += "<", i = (t = t.substring(1)).indexOf("<");
                    v += i < 0 ? t : t.substring(0, i), t = i < 0 ? "" : t.substring(i), e.chars && e.chars(v)
                }
                if (t === h) throw new Error("Parse Error: ".concat(t));
                h = t
            }
            n()
        }
    }, "02d7": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c3ea")).default)
        }).call(this, n("543d").createPage)
    }, "030b": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b933")).default)
        }).call(this, n("543d").createPage)
    }, "0333": function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = i(n("9dc18")), r = i(n("27f5")), o = {
            getAward: function (t, e) {
                var n = this;
                return new Promise(function (i, o) {
                    (0, a.default)({
                        url: r.default.check_in.sign_in,
                        data: {status: t, day: e || 1}
                    }).then(function (t) {
                        if (0 != t.code) return o(t.msg);
                        n.checkInResult(t.data.queueId, t.data.token).then(function (t) {
                            return i(t)
                        }).catch(function (t) {
                            return o(t)
                        })
                    }).catch(function (t) {
                        return o(t)
                    })
                })
            }, checkInResult: function (t, e) {
                var n = this;
                return new Promise(function (i, o) {
                    (0, a.default)({
                        url: r.default.check_in.sign_in_result,
                        data: {queueId: t, token: e}
                    }).then(function (a) {
                        return 0 != a.code ? o(a.msg) : 1 != a.data.retry ? i(a.data) : void n.checkInResult(t, e).then(function (t) {
                            return i(t)
                        }).catch(function (t) {
                            return o(t)
                        })
                    }).catch(function (t) {
                        return o(t)
                    })
                })
            }
        };
        e.default = o
    }, "046e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5ac6")).default)
        }).call(this, n("543d").createPage)
    }, "04881": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("6cd3")).default)
        }).call(this, n("543d").createPage)
    }, "04883": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4120")).default)
        }).call(this, n("543d").createPage)
    }, "0752": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e6ce")).default)
        }).call(this, n("543d").createPage)
    }, "0773": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e568")).default)
        }).call(this, n("543d").createPage)
    }, "0ae7": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4bc6")).default)
        }).call(this, n("543d").createPage)
    }, "0b14": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("ccf9")).default)
        }).call(this, n("543d").createPage)
    }, "0be2": function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0,
            state: {
                reportAndError: {boolean: !1, content: "网络开了会儿小差， 请刷新重试下哦~"},
                tabBarBoolean: !1,
                systemInfo: {
                    SDKVersion: "",
                    batteryLevel: 0,
                    brand: "",
                    errMsg: "",
                    fontSizeSetting: 0,
                    language: "0",
                    model: "",
                    pixelRatio: 0,
                    platform: "",
                    safeArea: {bottom: 0, height: 0, left: 0, right: 0, top: 0, width: 0},
                    screenHeight: 0,
                    screenWidth: 0,
                    statusBarHeight: 0,
                    system: "",
                    version: "",
                    windowHeight: 0,
                    windowWidth: 0
                },
                tabBarHeight: 0,
                iphone: !1,
                iphoneHeight: 0,
                promptBox: {text: "", show: !1, call: -1},
                imageWidth: 0
            },
            getters: {
                reportAndErrorObj: function (t) {
                    return t.reportAndError
                }
            },
            mutations: {
                reportAndErrorObj: function (t, e) {
                    t.reportAndError = e
                }, reportAndErrorB: function (t, e) {
                    t.reportAndError.boolean = e
                }, setTabBarBoolean: function (t, e) {
                    var n = getCurrentPages(), i = null;
                    n.length && (i = n[n.length - 1]);
                    var a = void 0;
                    a = "/".concat(i.route.split("?")[0]);
                    for (var r = 0; r < e.length; r++) if (a.includes(e[r].url.split("?")[0])) return t.tabBarBoolean = !0;
                    return t.tabBarBoolean = !1
                }, setSystemInfo: function (t, e) {
                    t.systemInfo = e, t.imageWidth = e.windowWidth
                }, setPromptBox: function (t, e) {
                    t.promptBox = e
                }, setPromptBoxCall: function (t, e) {
                    t.promptBox.call = e
                }, setHeight: function (t, e) {
                    t.tabBarHeight = e
                }, setiPhoneHeight: function (t, e) {
                    t.iphoneHeight = e
                }, setiPhoneBoolean: function (t, e) {
                    t.iphone = e
                }, setImageWidth: function (t, e) {
                    t.imageWidth = t.systemInfo.windowWidth - t.systemInfo.windowWidth / 750 * e
                }
            },
            actions: {
                setImageWidth: function (t, e) {
                    t.commit("setImageWidth", e)
                }, reportAndErrorObj: function (t, e) {
                    t.commit("reportAndErrorObj", e)
                }, reportAndErrorB: function (t, e) {
                    t.commit("reportAndErrorB", e)
                }, setTabBarBoolean: function (t, e) {
                    t.commit("setTabBarBoolean", e)
                }, setSystemInfo: function (t, e) {
                    t.commit("setSystemInfo", e)
                }, setHeight: function (t, e) {
                    t.commit("setHeight", e)
                }, setiPhoneBoolean: function (t, e) {
                    t.commit("setiPhoneBoolean", e)
                }, setPromptBox: function (t, e) {
                    t.commit("setPromptBox", e)
                }, setPromptBoxCall: function (t, e) {
                    t.commit("setPromptBoxCall", e)
                }, setiPhoneHeight: function (t, e) {
                    t.commit("setiPhoneHeight", e)
                }
            }
        };
        e.default = i
    }, "0d73": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("0cca")).default)
        }).call(this, n("543d").createPage)
    }, "0f9a": function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = function (t) {
            return t && t.__esModule ? t : {default: t}
        }(n("816e")), a = {
            namespaced: !0,
            state: {accessToken: null, info: null, showLoginModal: !1, tempParentId: 0},
            getters: {
                accessToken: function (t) {
                    return t.accessToken
                }, info: function (t) {
                    return t.info
                }, showLoginModal: function (t) {
                    return t.showLoginModal
                }, tempParentId: function (t) {
                    return t.tempParentId
                }, is_vip: function (t) {
                    return t.is_vip_card_user
                }
            },
            mutations: {
                accessToken: function (t, e) {
                    t.accessToken = e
                }, info: function (t, e) {
                    t.info = e
                }, showLoginModal: function (t, e) {
                    t.showLoginModal = e
                }, tempParentId: function (t, e) {
                    t.tempParentId = e
                }
            },
            actions: {
                accessToken: function (t) {
                    i.default.isLogin() || t.commit("accessToken", null), i.default.getAccessToken().then(function (e) {
                        t.commit("accessToken", e)
                    }).catch(function (t) {
                    })
                }, info: function (t) {
                    i.default.isLogin() || t.commit("accessToken", null), i.default.getAccessToken().then(function (e) {
                        t.commit("accessToken", e), i.default.getInfo().then(function (e) {
                            t.commit("info", e)
                        }).catch(function (t) {
                        })
                    }).catch(function (t) {
                    })
                }, refreshInfo: function (t) {
                    i.default.isLogin() || t.commit("accessToken", null), i.default.getAccessToken().then(function (e) {
                        t.commit("accessToken", e), i.default.getInfo({refresh: !0}).then(function (e) {
                            t.commit("info", e)
                        }).catch(function (t) {
                        })
                    }).catch(function (t) {
                    })
                }, setTempParentId: function (t, e) {
                    t.commit("tempParentId", e)
                }, loadAccessTokenFormCache: function (t) {
                    t.accessToken || i.default.getAccessToken({cacheOnly: !0}).then(function (e) {
                        e && t.commit("accessToken", e)
                    }).catch(function (t) {
                    })
                }, logout: function (t) {
                    t.commit("accessToken", null), i.default.loginByToken(null)
                }
            }
        };
        e.default = a
    }, "100e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("85c6")).default)
        }).call(this, n("543d").createPage)
    }, 1081: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4b5c")).default)
        }).call(this, n("543d").createPage)
    }, 1155: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a574")).default)
        }).call(this, n("543d").createPage)
    }, "121d": function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        function a(t, e, n, i, a, r, o) {
            try {
                var c = t[r](o), l = c.value
            } catch (t) {
                return void n(t)
            }
            c.done ? e(l) : Promise.resolve(l).then(i, a)
        }

        function r(t) {
            return function () {
                var e = this, n = arguments;
                return new Promise(function (i, r) {
                    function o(t) {
                        a(l, i, r, o, c, "next", t)
                    }

                    function c(t) {
                        a(l, i, r, o, c, "throw", t)
                    }

                    var l = t.apply(e, n);
                    o(void 0)
                })
            }
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var o = i(n("a34a")), c = i(n("d3b5"));
        i(n("816e"));
        var l = {
            actionGetLoading: function (t, e) {
                t.commit("mutSetLoading", e)
            }
        }, s = {
            namespaced: !0,
            state: {type: "global", text: "加载中", color: "#ffffff", backgroundImage: "", isShow: !1},
            getters: {
                getType: function (t) {
                    return t.type
                }, getText: function (t) {
                    return t.text
                }, getColor: function (t) {
                    return t.color
                }, getBackgroundImage: function () {
                    var t = r(o.default.mark(function t(e) {
                        return o.default.wrap(function (t) {
                            for (; ;) switch (t.prev = t.next) {
                                case 0:
                                    return t.abrupt("return", e.backgroundImage);
                                case 1:
                                case"end":
                                    return t.stop()
                            }
                        }, t)
                    }));
                    return function (e) {
                        return t.apply(this, arguments)
                    }
                }(), getIsShow: function (t) {
                    return t.isShow
                }
            },
            mutations: {
                mutSetLoading: function (t, e) {
                    for (var n in e) t[n] = e[n];
                    t.backgroundImage || c.default.getConfig().then(function (e) {
                        t.backgroundImage = e.__wxapp_img.mall.loading
                    })
                }
            },
            actions: l
        };
        e.default = s
    }, 1310: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2c78")).default)
        }).call(this, n("543d").createPage)
    }, 1464: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c180")).default)
        }).call(this, n("543d").createPage)
    }, "15b5": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a4eb")).default)
        }).call(this, n("543d").createPage)
    }, 1621: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("dfe1")).default)
        }).call(this, n("543d").createPage)
    }, 1633: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d80a")).default)
        }).call(this, n("543d").createPage)
    }, 1745: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("dcd9")).default)
        }).call(this, n("543d").createPage)
    }, "18a7": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("528b")).default)
        }).call(this, n("543d").createPage)
    }, "198e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3353")).default)
        }).call(this, n("543d").createPage)
    }, "1b6e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("bd29")).default)
        }).call(this, n("543d").createPage)
    }, "1b7b": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2846")).default)
        }).call(this, n("543d").createPage)
    }, "1eab": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("073a")).default)
        }).call(this, n("543d").createPage)
    }, "1eb5": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f61e")).default)
        }).call(this, n("543d").createPage)
    }, "1ef2": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("395b")).default)
        }).call(this, n("543d").createPage)
    }, "20aa": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1b3d")).default)
        }).call(this, n("543d").createPage)
    }, "219d": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b616")).default)
        }).call(this, n("543d").createPage)
    }, "246c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f442")).default)
        }).call(this, n("543d").createPage)
    }, 2749: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            EVENT_USER_LOGIN: "event_user_login",
            EVENT_USER_REGISTER: "event_user_register",
            EVENT_VIDEO_END: "event_video_end",
            EVENT_POPUP: "popUpEvent"
        };
        e.default = i
    }, "27f5": function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        function a(t, e, n) {
            return e in t ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : t[e] = n, t
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var r = i(n("36e8")), o = function (t, e) {
            var n = {};
            for (var i in e) {
                var r = a({}, i, {});
                for (var o in e[i]) r[i][o] = "".concat(t).concat(e[i][o]);
                n[i] = r[i]
            }
            return n
        }, c = function (t) {
            var e = "";
            return e = t.acid > 0 ? t.siteroot.substr(0, t.siteroot.indexOf("app/index.php")) + "addons/zjhj_bd/web/index.php?_acid=" + t.acid + "&r=" : t.apiroot + "&r=", o(e, r.default)
        }(i(n("ae58")).default);
        e.default = c
    }, 2877: function (t, e, n) {
        function i(t, e, n, i, a, r, o, c) {
            var l, s = "function" == typeof t ? t.options : t;
            if (e && (s.render = e, s.staticRenderFns = n, s._compiled = !0), i && (s.functional = !0), r && (s._scopeId = "data-v-" + r), o ? (l = function (t) {
                (t = t || this.$vnode && this.$vnode.ssrContext || this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) || "undefined" == typeof __VUE_SSR_CONTEXT__ || (t = __VUE_SSR_CONTEXT__), a && a.call(this, t), t && t._registeredComponents && t._registeredComponents.add(o)
            }, s._ssrRegister = l) : a && (l = c ? function () {
                a.call(this, this.$root.$options.shadowRoot)
            } : a), l) if (s.functional) {
                s._injectStyles = l;
                var u = s.render;
                s.render = function (t, e) {
                    return l.call(e), u(t, e)
                }
            } else {
                var d = s.beforeCreate;
                s.beforeCreate = d ? [].concat(d, l) : [l]
            }
            return {exports: t, options: s}
        }

        n.d(e, "a", function () {
            return i
        })
    }, "2a24": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d2c1")).default)
        }).call(this, n("543d").createPage)
    }, "2a71": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("854e")).default)
        }).call(this, n("543d").createPage)
    }, "2a74": function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = i(n("27f5")), r = i(n("9dc18")), o = {
            namespaced: !0,
            state: {home_pages: {}, type: ""},
            getters: {},
            mutations: {
                getHomePages: function (t, e) {
                    console.table(e), t.type = e.type, t.home_pages = e.home_pages
                }
            },
            actions: {
                getHomePages: function (t) {
                    (0, r.default)({url: "".concat(a.default.index.index, "&page_id=0&longitude=&latitude=")}).then(function (e) {
                        t.commit("getHomePages", e.data)
                    }).catch(function (t) {
                        console.log(t)
                    })
                }
            }
        };
        e.default = o
    }, "2adc": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("958d")).default)
        }).call(this, n("543d").createPage)
    }, "2d5c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e254")).default)
        }).call(this, n("543d").createPage)
    }, "2f62": function (e, n, i) {
        i.r(n), function (e) {
            function a(t) {
                function e() {
                    var t = this.$options;
                    t.store ? this.$store = "function" == typeof t.store ? t.store() : t.store : t.parent && t.parent.$store && (this.$store = t.parent.$store)
                }

                if (Number(t.version.split(".")[0]) >= 2) t.mixin({beforeCreate: e}); else {
                    var n = t.prototype._init;
                    t.prototype._init = function (t) {
                        void 0 === t && (t = {}), t.init = t.init ? [e].concat(t.init) : e, n.call(this, t)
                    }
                }
            }

            function r(t) {
                k && (t._devtoolHook = k, k.emit("vuex:init", t), k.on("vuex:travel-to-state", function (e) {
                    t.replaceState(e)
                }), t.subscribe(function (t, e) {
                    k.emit("vuex:mutation", t, e)
                }))
            }

            function o(t, e) {
                Object.keys(t).forEach(function (n) {
                    return e(t[n], n)
                })
            }

            function c(e) {
                return null !== e && "object" === (void 0 === e ? "undefined" : t(e))
            }

            function l(t) {
                return t && "function" == typeof t.then
            }

            function s(t, e) {
                return function () {
                    return t(e)
                }
            }

            function u(t, e, n) {
                if (e.update(n), n.modules) for (var i in n.modules) {
                    if (!e.getChild(i)) return;
                    u(t.concat(i), e.getChild(i), n.modules[i])
                }
            }

            function d(t, e) {
                return e.indexOf(t) < 0 && e.push(t), function () {
                    var n = e.indexOf(t);
                    n > -1 && e.splice(n, 1)
                }
            }

            function f(t, e) {
                t._actions = Object.create(null), t._mutations = Object.create(null), t._wrappedGetters = Object.create(null), t._modulesNamespaceMap = Object.create(null);
                var n = t.state;
                h(t, n, [], t._modules.root, !0), p(t, n, e)
            }

            function p(t, e, n) {
                var i = t._vm;
                t.getters = {};
                var a = {};
                o(t._wrappedGetters, function (e, n) {
                    a[n] = s(e, t), Object.defineProperty(t.getters, n, {
                        get: function () {
                            return t._vm[n]
                        }, enumerable: !0
                    })
                });
                var r = L.config.silent;
                L.config.silent = !0, t._vm = new L({
                    data: {$$state: e},
                    computed: a
                }), L.config.silent = r, t.strict && x(t), i && (n && t._withCommit(function () {
                    i._data.$$state = null
                }), L.nextTick(function () {
                    return i.$destroy()
                }))
            }

            function h(t, e, n, i, a) {
                var r = !n.length, o = t._modules.getNamespace(n);
                if (i.namespaced && (t._modulesNamespaceMap[o] = i), !r && !a) {
                    var c = b(e, n.slice(0, -1)), l = n[n.length - 1];
                    t._withCommit(function () {
                        L.set(c, l, i.state)
                    })
                }
                var s = i.context = g(t, o, n);
                i.forEachMutation(function (e, n) {
                    y(t, o + n, e, s)
                }), i.forEachAction(function (e, n) {
                    var i = e.root ? n : o + n, a = e.handler || e;
                    m(t, i, a, s)
                }), i.forEachGetter(function (e, n) {
                    _(t, o + n, e, s)
                }), i.forEachChild(function (i, r) {
                    h(t, e, n.concat(r), i, a)
                })
            }

            function g(t, e, n) {
                var i = "" === e, a = {
                    dispatch: i ? t.dispatch : function (n, i, a) {
                        var r = P(n, i, a), o = r.payload, c = r.options, l = r.type;
                        return c && c.root || (l = e + l), t.dispatch(l, o)
                    }, commit: i ? t.commit : function (n, i, a) {
                        var r = P(n, i, a), o = r.payload, c = r.options, l = r.type;
                        c && c.root || (l = e + l), t.commit(l, o, c)
                    }
                };
                return Object.defineProperties(a, {
                    getters: {
                        get: i ? function () {
                            return t.getters
                        } : function () {
                            return v(t, e)
                        }
                    }, state: {
                        get: function () {
                            return b(t.state, n)
                        }
                    }
                }), a
            }

            function v(t, e) {
                var n = {}, i = e.length;
                return Object.keys(t.getters).forEach(function (a) {
                    if (a.slice(0, i) === e) {
                        var r = a.slice(i);
                        Object.defineProperty(n, r, {
                            get: function () {
                                return t.getters[a]
                            }, enumerable: !0
                        })
                    }
                }), n
            }

            function y(t, e, n, i) {
                (t._mutations[e] || (t._mutations[e] = [])).push(function (e) {
                    n.call(t, i.state, e)
                })
            }

            function m(t, e, n, i) {
                (t._actions[e] || (t._actions[e] = [])).push(function (e, a) {
                    var r = n.call(t, {
                        dispatch: i.dispatch,
                        commit: i.commit,
                        getters: i.getters,
                        state: i.state,
                        rootGetters: t.getters,
                        rootState: t.state
                    }, e, a);
                    return l(r) || (r = Promise.resolve(r)), t._devtoolHook ? r.catch(function (e) {
                        throw t._devtoolHook.emit("vuex:error", e), e
                    }) : r
                })
            }

            function _(t, e, n, i) {
                t._wrappedGetters[e] || (t._wrappedGetters[e] = function (t) {
                    return n(i.state, i.getters, t.state, t.getters)
                })
            }

            function x(t) {
                t._vm.$watch(function () {
                    return this._data.$$state
                }, function () {
                }, {deep: !0, sync: !0})
            }

            function b(t, e) {
                return e.length ? e.reduce(function (t, e) {
                    return t[e]
                }, t) : t
            }

            function P(t, e, n) {
                return c(t) && t.type && (n = e, e = t, t = t.type), {type: t, payload: e, options: n}
            }

            function w(t) {
                L && t === L || (L = t, a(L))
            }

            function S(t) {
                return Array.isArray(t) ? t.map(function (t) {
                    return {key: t, val: t}
                }) : Object.keys(t).map(function (e) {
                    return {key: e, val: t[e]}
                })
            }

            function A(t) {
                return function (e, n) {
                    return "string" != typeof e ? (n = e, e = "") : "/" !== e.charAt(e.length - 1) && (e += "/"), t(e, n)
                }
            }

            function T(t, e, n) {
                return t._modulesNamespaceMap[n]
            }

            i.d(n, "Store", function () {
                return D
            }), i.d(n, "install", function () {
                return w
            }), i.d(n, "mapState", function () {
                return j
            }), i.d(n, "mapMutations", function () {
                return F
            }), i.d(n, "mapGetters", function () {
                return R
            }), i.d(n, "mapActions", function () {
                return E
            }), i.d(n, "createNamespacedHelpers", function () {
                return I
            });
            var k = ("undefined" != typeof window ? window : void 0 !== e ? e : {}).__VUE_DEVTOOLS_GLOBAL_HOOK__,
                M = function (t, e) {
                    this.runtime = e, this._children = Object.create(null), this._rawModule = t;
                    var n = t.state;
                    this.state = ("function" == typeof n ? n() : n) || {}
                }, O = {namespaced: {configurable: !0}};
            O.namespaced.get = function () {
                return !!this._rawModule.namespaced
            }, M.prototype.addChild = function (t, e) {
                this._children[t] = e
            }, M.prototype.removeChild = function (t) {
                delete this._children[t]
            }, M.prototype.getChild = function (t) {
                return this._children[t]
            }, M.prototype.update = function (t) {
                this._rawModule.namespaced = t.namespaced, t.actions && (this._rawModule.actions = t.actions), t.mutations && (this._rawModule.mutations = t.mutations), t.getters && (this._rawModule.getters = t.getters)
            }, M.prototype.forEachChild = function (t) {
                o(this._children, t)
            }, M.prototype.forEachGetter = function (t) {
                this._rawModule.getters && o(this._rawModule.getters, t)
            }, M.prototype.forEachAction = function (t) {
                this._rawModule.actions && o(this._rawModule.actions, t)
            }, M.prototype.forEachMutation = function (t) {
                this._rawModule.mutations && o(this._rawModule.mutations, t)
            }, Object.defineProperties(M.prototype, O);
            var C = function (t) {
                this.register([], t, !1)
            };
            C.prototype.get = function (t) {
                return t.reduce(function (t, e) {
                    return t.getChild(e)
                }, this.root)
            }, C.prototype.getNamespace = function (t) {
                var e = this.root;
                return t.reduce(function (t, n) {
                    return e = e.getChild(n), t + (e.namespaced ? n + "/" : "")
                }, "")
            }, C.prototype.update = function (t) {
                u([], this.root, t)
            }, C.prototype.register = function (t, e, n) {
                var i = this;
                void 0 === n && (n = !0);
                var a = new M(e, n);
                0 === t.length ? this.root = a : this.get(t.slice(0, -1)).addChild(t[t.length - 1], a), e.modules && o(e.modules, function (e, a) {
                    i.register(t.concat(a), e, n)
                })
            }, C.prototype.unregister = function (t) {
                var e = this.get(t.slice(0, -1)), n = t[t.length - 1];
                e.getChild(n).runtime && e.removeChild(n)
            };
            var L, D = function (t) {
                var e = this;
                void 0 === t && (t = {}), !L && "undefined" != typeof window && window.Vue && w(window.Vue);
                var n = t.plugins;
                void 0 === n && (n = []);
                var i = t.strict;
                void 0 === i && (i = !1), this._committing = !1, this._actions = Object.create(null), this._actionSubscribers = [], this._mutations = Object.create(null), this._wrappedGetters = Object.create(null), this._modules = new C(t), this._modulesNamespaceMap = Object.create(null), this._subscribers = [], this._watcherVM = new L;
                var a = this, o = this, c = o.dispatch, l = o.commit;
                this.dispatch = function (t, e) {
                    return c.call(a, t, e)
                }, this.commit = function (t, e, n) {
                    return l.call(a, t, e, n)
                }, this.strict = i;
                var s = this._modules.root.state;
                h(this, s, [], this._modules.root), p(this, s), n.forEach(function (t) {
                    return t(e)
                }), (void 0 !== t.devtools ? t.devtools : L.config.devtools) && r(this)
            }, $ = {state: {configurable: !0}};
            $.state.get = function () {
                return this._vm._data.$$state
            }, $.state.set = function (t) {
            }, D.prototype.commit = function (t, e, n) {
                var i = this, a = P(t, e, n), r = a.type, o = a.payload, c = (a.options, {type: r, payload: o}),
                    l = this._mutations[r];
                l && (this._withCommit(function () {
                    l.forEach(function (t) {
                        t(o)
                    })
                }), this._subscribers.forEach(function (t) {
                    return t(c, i.state)
                }))
            }, D.prototype.dispatch = function (t, e) {
                var n = this, i = P(t, e), a = i.type, r = i.payload, o = {type: a, payload: r}, c = this._actions[a];
                if (c) {
                    try {
                        this._actionSubscribers.filter(function (t) {
                            return t.before
                        }).forEach(function (t) {
                            return t.before(o, n.state)
                        })
                    } catch (t) {
                    }
                    return (c.length > 1 ? Promise.all(c.map(function (t) {
                        return t(r)
                    })) : c[0](r)).then(function (t) {
                        try {
                            n._actionSubscribers.filter(function (t) {
                                return t.after
                            }).forEach(function (t) {
                                return t.after(o, n.state)
                            })
                        } catch (t) {
                        }
                        return t
                    })
                }
            }, D.prototype.subscribe = function (t) {
                return d(t, this._subscribers)
            }, D.prototype.subscribeAction = function (t) {
                return d("function" == typeof t ? {before: t} : t, this._actionSubscribers)
            }, D.prototype.watch = function (t, e, n) {
                var i = this;
                return this._watcherVM.$watch(function () {
                    return t(i.state, i.getters)
                }, e, n)
            }, D.prototype.replaceState = function (t) {
                var e = this;
                this._withCommit(function () {
                    e._vm._data.$$state = t
                })
            }, D.prototype.registerModule = function (t, e, n) {
                void 0 === n && (n = {}), "string" == typeof t && (t = [t]), this._modules.register(t, e), h(this, this.state, t, this._modules.get(t), n.preserveState), p(this, this.state)
            }, D.prototype.unregisterModule = function (t) {
                var e = this;
                "string" == typeof t && (t = [t]), this._modules.unregister(t), this._withCommit(function () {
                    var n = b(e.state, t.slice(0, -1));
                    L.delete(n, t[t.length - 1])
                }), f(this)
            }, D.prototype.hotUpdate = function (t) {
                this._modules.update(t), f(this, !0)
            }, D.prototype._withCommit = function (t) {
                var e = this._committing;
                this._committing = !0, t(), this._committing = e
            }, Object.defineProperties(D.prototype, $);
            var j = A(function (t, e) {
                var n = {};
                return S(e).forEach(function (e) {
                    var i = e.key, a = e.val;
                    n[i] = function () {
                        var e = this.$store.state, n = this.$store.getters;
                        if (t) {
                            var i = T(this.$store, 0, t);
                            if (!i) return;
                            e = i.context.state, n = i.context.getters
                        }
                        return "function" == typeof a ? a.call(this, e, n) : e[a]
                    }, n[i].vuex = !0
                }), n
            }), F = A(function (t, e) {
                var n = {};
                return S(e).forEach(function (e) {
                    var i = e.key, a = e.val;
                    n[i] = function () {
                        for (var e = [], n = arguments.length; n--;) e[n] = arguments[n];
                        var i = this.$store.commit;
                        if (t) {
                            var r = T(this.$store, 0, t);
                            if (!r) return;
                            i = r.context.commit
                        }
                        return "function" == typeof a ? a.apply(this, [i].concat(e)) : i.apply(this.$store, [a].concat(e))
                    }
                }), n
            }), R = A(function (t, e) {
                var n = {};
                return S(e).forEach(function (e) {
                    var i = e.key, a = e.val;
                    a = t + a, n[i] = function () {
                        if (!t || T(this.$store, 0, t)) return this.$store.getters[a]
                    }, n[i].vuex = !0
                }), n
            }), E = A(function (t, e) {
                var n = {};
                return S(e).forEach(function (e) {
                    var i = e.key, a = e.val;
                    n[i] = function () {
                        for (var e = [], n = arguments.length; n--;) e[n] = arguments[n];
                        var i = this.$store.dispatch;
                        if (t) {
                            var r = T(this.$store, 0, t);
                            if (!r) return;
                            i = r.context.dispatch
                        }
                        return "function" == typeof a ? a.apply(this, [i].concat(e)) : i.apply(this.$store, [a].concat(e))
                    }
                }), n
            }), I = function (t) {
                return {
                    mapState: j.bind(null, t),
                    mapGetters: R.bind(null, t),
                    mapMutations: F.bind(null, t),
                    mapActions: E.bind(null, t)
                }
            }, z = {
                Store: D,
                install: w,
                version: "3.1.1",
                mapState: j,
                mapMutations: F,
                mapGetters: R,
                mapActions: E,
                createNamespacedHelpers: I
            };
            n.default = z
        }.call(this, i("c8ba"))
    }, "2ff6": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1734")).default)
        }).call(this, n("543d").createPage)
    }, 3111: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5381")).default)
        }).call(this, n("543d").createPage)
    }, 3241: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("11f2")).default)
        }).call(this, n("543d").createPage)
    }, 3460: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("057d")).default)
        }).call(this, n("543d").createPage)
    }, "346d": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("fa97")).default)
        }).call(this, n("543d").createPage)
    }, "34d1": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d1ab")).default)
        }).call(this, n("543d").createPage)
    }, "35d5": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e3bc")).default)
        }).call(this, n("543d").createPage)
    }, "36e8": function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            index: {config: "api/index/config", index: "api/index/index", buy_data: "api/index/purchase"},
            payment: {
                get_payments: "api/payment/get-payments",
                pay_data: "api/payment/pay-data",
                pay_buy_balance: "api/payment/pay-buy-balance",
                pay_buy_huodao: "api/payment/pay-buy-huodao"
            },
            upload: {file: "api/attachment/upload"},
            default: {
                district: "api/default/district",
                goods_list: "api/default/goods-list",
                search_list: "api/default/search-list",
                cart_list: "api/default/cats-list&cat_id=",
                qrcode_parameter: "api/default/qr-code-parameter"
            },
            passport: {login: "api/passport/login"},
            user: {
                user_info: "api/user/user-info",
                address: "api/user/address",
                wechat_district: "api/user/wechat-district",
                address_save: "api/user/address-save",
                address_detail: "api/user/address-detail",
                address_default: "api/user/address-default",
                address_destroy: "api/user/address-destroy",
                my_favorite_goods: "api/user/my-favorite-goods",
                my_favorite_topic: "api/user/my-favorite-topic",
                favorite_add: "api/user/favorite-add",
                favorite_remove: "api/user/favorite-remove",
                config: "api/user/config"
            },
            article: {list: "api/default/article-list", detail: "api/default/article"},
            coupon: {
                list: "api/coupon/list",
                detail: "api/coupon/detail",
                receive: "api/coupon/receive",
                user_coupon: "api/coupon/user-coupon",
                user_coupon_detail: "api/coupon/user-coupon-detail",
                share_coupon: "api/coupon/share-coupon"
            },
            city: {cityName: "api/default/district", goodsList: "api/default/goods-list"},
            balance: {index: "api/balance/index", logs: "api/balance/logs", log_detail: "api/balance/log-detail"},
            order: {
                preview: "api/order/preview",
                submit: "api/order/submit",
                pay_data: "api/order/pay-data",
                usable_coupon_list: "api/order/usable-coupon-list",
                store_list: "api/order/store-list",
                list: "api/order/list",
                detail: "api/order/detail",
                appraise: "api/order/appraise",
                express_detail: "api/order/express-detail",
                clerk_affirm_pay: "api/order/clerk-affirm-pay",
                order_clerk: "api/order/order-clerk",
                clerk_qr_code: "api/order/clerk-qr-code",
                apply_refund: "api/order/apply-refund",
                refund_submit: "api/order/refund-submit",
                refund_detail: "api/order/refund-detail",
                refund_send: "api/order/refund-send",
                confirm: "api/order/confirm",
                cancel: "api/order/cancel",
                list_pay_data: "api/order/list-pay-data",
                pay_result: "api/order/pay-result",
                customer: "api/express/get-customer",
                delivery: "api/express/delivery-config",
                order_express_list: "api/express/order-express-list"
            },
            video: {index: "api/video/index"},
            topic: {
                type: "api/topic/type",
                list: "api/topic/list",
                detail: "api/topic/detail",
                favorite: "api/topic/favorite",
                qrcode: "api/qrcode/topic"
            },
            member: {
                index: "api/mall-member/index",
                all: "api/mall-member/all-member",
                coupon: "api/mall-member/member-coupon",
                goods: "api/mall-member/member-goods",
                cats: "api/mall-member/goods-cats",
                purchase: "api/mall-member/purchase-member"
            },
            recharge: {
                index: "api/recharge/index",
                setting: "api/recharge/setting",
                balance_recharge: "api/recharge/balance-recharge"
            },
            store: {list: "api/store/list", detail: "api/store/detail"},
            goods: {
                detail: "api/goods/detail",
                attr: "api/goods/attr",
                comments_list: "api/goods/comments-list",
                recommend: "api/goods/recommend",
                new_recommend: "api/goods/new-recommend",
                cat_style: "api/goods/cat-style"
            },
            card: {
                index: "api/card/user-card",
                detail: "api/card/user-card-detail",
                qrcode: "api/card/card-qrcode",
                clerk: "api/card/card-clerk"
            },
            share: {
                apply: "api/share/apply",
                index: "api/share/index",
                setting: "api/share/setting",
                customize: "api/share/customize",
                brokerage: "api/share/brokerage",
                team: "api/share/team",
                cash: "api/share/cash",
                cash_list: "api/share/cash-list",
                apply_status: "api/share/apply-status",
                share_order: "api/share/share-order",
                bind_parent: "api/share/bind-parent",
                poster: "api/qrcode/share"
            },
            pond: {
                index: "plugin/pond/api/pond/index",
                lottery: "plugin/pond/api/pond/lottery",
                prize: "plugin/pond/api/pond/prize",
                send: "plugin/pond/api/pond/send",
                setting: "plugin/pond/api/pond/setting",
                order_preview: "plugin/pond/api/pond/order-preview",
                order_submit: "plugin/pond/api/pond/order-submit",
                poster: "plugin/pond/api/pond/poster"
            },
            scratch: {
                index: "plugin/scratch/api/scratch/index",
                receive: "plugin/scratch/api/scratch/receive",
                setting: "plugin/scratch/api/scratch/setting",
                prize: "plugin/scratch/api/scratch/prize",
                record: "plugin/scratch/api/scratch/record",
                order_preview: "plugin/scratch/api/scratch/order-preview",
                order_submit: "plugin/scratch/api/scratch/order-submit",
                poster: "plugin/scratch/api/scratch/poster"
            },
            bonus: {
                order: "plugin/bonus/api/order/index",
                index: "plugin/bonus/api/index/index",
                apply: "plugin/bonus/api/index/apply",
                status: "plugin/bonus/api/index/apply-status",
                team: "plugin/bonus/api/order/team-bonus",
                setting: "plugin/bonus/api/index/setting",
                clear: "plugin/bonus/api/index/clear-apply",
                cash: "plugin/bonus/api/index/cash",
                detail: "plugin/bonus/api/cash/index",
                member: "plugin/bonus/api/index/all-member",
                data: "plugin/bonus/api/order/data"
            },
            lottery: {
                index: "plugin/lottery/api/lottery/index",
                setting: "plugin/lottery/api/lottery/setting",
                detail: "plugin/lottery/api/lottery/detail",
                prize: "plugin/lottery/api/lottery/prize",
                clerk: "plugin/lottery/api/lottery/clerk",
                code: "plugin/lottery/api/lottery/code",
                order_preview: "plugin/lottery/api/lottery/order-preview",
                order_submit: "plugin/lottery/api/lottery/order-submit",
                goods: "plugin/lottery/api/lottery/goods",
                poster: "plugin/lottery/api/lottery/poster"
            },
            check_in: {
                index: "plugin/check_in/api/index/index",
                user: "plugin/check_in/api/index/user",
                customize: "plugin/check_in/api/index/customize",
                sign_in: "plugin/check_in/api/index/sign-in",
                sign_in_result: "plugin/check_in/api/index/sign-in-result",
                sign_in_day: "plugin/check_in/api/index/sign-in-day"
            },
            quick: {index: "api/quick/index", goods_list: "api/quick/goods-list", cart: "api/quick/cart"},
            step: {
                index: "plugin/step/api/step/index",
                setting: "plugin/step/api/step/setting",
                ranking: "plugin/step/api/step/ranking",
                goods: "plugin/step/api/step/goods",
                activity_detail: "plugin/step/api/step/activity-detail",
                activity: "plugin/step/api/step/activity",
                activity_log: "plugin/step/api/step/activity-log",
                activity_join: "plugin/step/api/step/activity-join",
                activity_submit: "plugin/step/api/step/activity-submit",
                invite_list: "plugin/step/api/step/invite-list",
                convert: "plugin/step/api/step/convert",
                log: "plugin/step/api/step/log",
                step_convert: "plugin/step/api/step/step-convert",
                remind: "plugin/step/api/step/remind",
                order_preview: "plugin/step/api/step/order-preview",
                order_submit: "plugin/step/api/step/order-submit",
                goods_detail: "plugin/step/api/step/goods-detail",
                poster: "plugin/step/api/step/poster",
                goods_poster: "plugin/step/api/step/goods-poster"
            },
            cart: {edit: "api/cart/edit", delete: "api/cart/delete", list: "api/cart/list", add: "api/cart/add"},
            fxhb: {
                index: "plugin/fxhb/api/index/index",
                join: "plugin/fxhb/api/index/join",
                join_result: "plugin/fxhb/api/index/join-result",
                detail: "plugin/fxhb/api/index/detail",
                recommend: "plugin/fxhb/api/index/recommend"
            },
            scan_code_pay: {
                index: "plugin/scan_code_pay/api/index/index",
                preview: "plugin/scan_code_pay/api/order/preview",
                submit: "plugin/scan_code_pay/api/order/submit",
                qr_code: "plugin/scan_code_pay/api/index/qr-code",
                coupons: "plugin/scan_code_pay/api/order/coupons",
                cancel: "plugin/scan_code_pay/api/order/cancel"
            },
            phone: {binding: "api/phone/binding", code: "api/user/phone-code"},
            book: {
                cats: "plugin/booking/api/booking/cats",
                clerk_code: "plugin/booking/api/order-list/clerk-code",
                order_submit: "plugin/booking/api/order/order-submit",
                order_preview: "plugin/booking/api/order/order-preview",
                detail: "plugin/booking/api/goods/detail",
                list: "plugin/booking/api/goods/list",
                setting: "plugin/booking/api/booking/setting",
                order_list: "plugin/booking/api/order-list/index",
                order_detail: "plugin/booking/api/order-list/detail",
                store_list: "plugin/booking/api/booking/store-list",
                poster: "plugin/booking/api/booking/poster"
            },
            poster: {share: "api/qrcode/share", goods: "api/qrcode/goods", topic: "api/qrcode/topic"},
            bargain: {
                banner: "plugin/bargain/api/index/banner",
                goods_list: "plugin/bargain/api/goods/list",
                goods_detail: "plugin/bargain/api/goods/detail",
                list: "plugin/bargain/api/order/bargain-list",
                bargain_submit: "plugin/bargain/api/order/bargain-submit",
                bargain_result: "plugin/bargain/api/order/bargain-result",
                order_preview: "plugin/bargain/api/order/order-preview",
                order_submit: "plugin/bargain/api/order/order-submit",
                user_join_bargain: "plugin/bargain/api/order/user-join-bargain",
                user_join_bargain_result: "plugin/bargain/api/order/user-join-bargain-result",
                activity: "plugin/bargain/api/order/activity",
                setting: "plugin/bargain/api/index/index",
                poster: "plugin/bargain/api/index/poster"
            },
            integral_mall: {
                index: "plugin/integral_mall/api/index/index",
                coupon: "plugin/integral_mall/api/coupon/index",
                cats: "plugin/integral_mall/api/goods/cats",
                detail: "plugin/integral_mall/api/coupon/detail",
                goods_detail: "plugin/integral_mall/api/goods/detail",
                coupon_submit: "plugin/integral_mall/api/coupon-order/order-submit",
                goods: "plugin/integral_mall/api/goods/index",
                log: "api/integral-log/index",
                order: "plugin/integral_mall/api/order/index",
                coupon_order: "plugin/integral_mall/api/coupon-order/index",
                order_preview: "plugin/integral_mall/api/order/order-preview",
                order_submit: "plugin/integral_mall/api/order/order-submit",
                coupon_pay: "plugin/integral_mall/api/coupon-order/order-pay-data",
                poster: "plugin/integral_mall/api/index/poster"
            },
            pt: {
                index: "plugin/pintuan/api/index/index",
                goods: "plugin/pintuan/api/goods/index",
                detail: "plugin/pintuan/api/goods/detail",
                cats: "plugin/pintuan/api/goods/cats",
                order_preview: "plugin/pintuan/api/order/order-preview",
                order_submit: "plugin/pintuan/api/order/submit",
                list: "plugin/pintuan/api/order/pintuan-list",
                pt_detail: "plugin/pintuan/api/order/pintuan-detail",
                order: "plugin/pintuan/api/order/list",
                poster: "plugin/pintuan/api/index/poster",
                order_poster: "plugin/pintuan/api/order/poster"
            },
            mch: {
                index: "plugin/mch/api/mch/index",
                detail: "plugin/mch/api/mch/detail",
                category: "plugin/mch/api/mch/category",
                goods: "plugin/mch/api/goods/index",
                goods_detail: "plugin/mch/api/goods/detail",
                cat_style: "plugin/mch/api/goods/cat-style",
                cats_list: "api/default/cats-list",
                setting: "plugin/mch/api/mch/setting",
                visit: "plugin/mch/api/mch/add-visit",
                order_list: "plugin/mch/api/order/index",
                order_detail: "plugin/mch/api/order/detail",
                manage_index: "plugin/mch/api/mch/manage-index",
                qr_code: "plugin/mch/api/mch/qr-code",
                qr_code_parameter: "api/default/qr-code-parameter",
                statistic: "plugin/mch/api/mch/statistic",
                year_list: "plugin/mch/api/mch/year-list",
                property: "plugin/mch/api/property/index",
                account_log: "plugin/mch/api/property/account-log",
                cash_log: "plugin/mch/api/property/cash-log",
                order_close_log: "plugin/mch/api/property/order-close-log",
                cash_submit: "plugin/mch/api/property/cash-submit",
                order_send: "plugin/mch/api/order/order-send",
                express_list: "api/order/express-list",
                refund_detail: "plugin/mch/api/order/refund-detail",
                mch_status: "plugin/mch/api/mch/mch-status",
                apply: "plugin/mch/api/mch/apply",
                login: "plugin/mch/api/mch/login",
                order_print: "plugin/mch/api/order/print",
                update_total_price: "plugin/mch/api/order/update-total-price",
                update_price: "plugin/mch/api/order/update-price",
                refund_handle: "plugin/mch/api/order/refund-handle",
                update_password: "plugin/mch/api/mch/update-password",
                switch_status: "plugin/mch/api/goods/switch-status",
                destroy: "plugin/mch/api/goods/destroy",
                cancel: "plugin/mch/api/order/cancel",
                order_preview: "plugin/mch/api/order/preview",
                order_submit: "plugin/mch/api/order/submit",
                apply_status: "plugin/mch/api/goods/apply-status",
                qr_code_login: "plugin/mch/api/mch/qr-code-login",
                poster: "plugin/mch/api/mch/poster",
                edit: "plugin/mch/api/goods/edit",
                cat: "plugin/mch/api/goods/cats",
                postage: "plugin/mch/api/goods/rules",
                service: "plugin/mch/api/goods/services",
                mch_cat: "plugin/mch/api/goods/mch-cats"
            },
            app_admin: {
                index: "api/admin/data-statistics/all_data",
                send: "api/admin/order/send",
                goods: "api/admin/goods/index",
                table: "api/admin/data-statistics/table",
                express: "api/order/express-list",
                goods_switch: "api/admin/goods/switch-status",
                goods_destroy: "api/admin/goods/destroy",
                comments: "api/admin/order-comments/index",
                comments_reply: "api/admin/order-comments/reply",
                comments_show: "api/admin/order-comments/show",
                user: "api/admin/user/index",
                share: "api/admin/share/index",
                clerk: "api/admin/user/clerk",
                integral: "api/admin/user/integral",
                balance: "api/admin/user/balance",
                setting: "api/admin/mall/setting",
                review: "api/admin/review/index",
                tabs: "api/admin/review/tabs",
                review_detail: "api/admin/review/detail",
                review_switch: "api/admin/review/switch-status",
                order: "api/admin/order/index",
                update_price: "api/admin/order/update-total-price",
                update_address: "api/admin/order/update-address",
                address_list: "api/admin/order/address-list",
                express_detail: "api/order/express-detail",
                cancel: "api/admin/order/cancel",
                detail: "api/admin/order/detail",
                refund: "api/admin/order/refund",
                refund_handle: "api/admin/order/refund-handle",
                edit: "api/admin/goods/edit",
                service: "api/admin/service/options",
                card: "api/admin/card/options",
                cat: "api/admin/cat/options",
                order_num: "api/admin/order/order-num",
                print: "api/admin/order/print",
                refund_address: "api/admin/refund-address/index",
                refund_address_edit: "api/admin/refund-address/edit",
                clerk_destroy: "api/admin/user/clerk-destroy",
                address_destroy: "api/admin/refund-address/destroy",
                clerk_edit: "api/admin/user/clerk-edit",
                cash: "api/admin/cash/index",
                verify: "api/admin/cash/verify",
                user_cash: "api/admin/cash/cash",
                postage: "api/admin/postage-rule/all-list",
                shou_huo: "api/admin/order/shou-huo",
                delivery: "api/admin/order/delivery",
                confirm: "api/admin/order/confirm"
            },
            clerk: {
                info: "plugin/clerk/api/index/clerk-info",
                order: "plugin/clerk/api/index/order",
                my: "plugin/clerk/api/index/my-order",
                detail: "plugin/clerk/api/index/detail",
                card: "plugin/clerk/api/index/card",
                my_card: "plugin/clerk/api/index/my-card",
                card_detail: "api/card/user-card-detail",
                statistics: "plugin/clerk/api/index/statistics",
                qrcode_parameter: "api/default/qr-code-parameter"
            },
            miaosha: {
                goods: "plugin/miaosha/api/goods/index",
                estimate: "plugin/miaosha/api/goods/estimate",
                goods_detail: "plugin/miaosha/api/goods/detail",
                cats: "plugin/miaosha/api/goods/cats",
                goods_info: "plugin/miaosha/api/goods/miaosha",
                order_preview: "plugin/miaosha/api/order/order-preview",
                order_submit: "plugin/miaosha/api/order/submit",
                today_goods: "plugin/miaosha/api/goods/today-miaosha",
                time_list: "plugin/miaosha/api/goods/time-list",
                add_cart: "plugin/miaosha/api/index/add-cart",
                cart_edit: "plugin/miaosha/api/index/cart-edit",
                poster: "plugin/miaosha/api/index/poster"
            },
            diy: {page_store: "plugin/diy/api/page/store"},
            vip_card: {
                index: "plugin/vip_card/api/index/index",
                card: "plugin/vip_card/api/index/card",
                order_preview: "plugin/vip_card/api/order/preview",
                card_detail: "plugin/vip_card/api/index/card-detail",
                setting: "plugin/vip_card/api/index/setting",
                right: "plugin/vip_card/api/index/right",
                order_submit: "plugin/vip_card/api/order/submit",
                pay_data: "plugin/vip_card/api/order/pay-data"
            },
            advance: {
                goods: "plugin/advance/api/goods/index",
                banner: "plugin/advance/api/goods/banner",
                detail: "plugin/advance/api/goods/detail",
                poster: "plugin/advance/api/goods/poster",
                order_submit: "plugin/advance/api/order/advance",
                order: "plugin/advance/api/order/my-advance",
                order_preview: "plugin/advance/api/order/order-preview",
                order_sub: "plugin/advance/api/order/order-submit",
                order_detail: "plugin/advance/api/order/detail",
                pay_data: "plugin/advance/api/order/pay-data",
                get_payments: "plugin/advance/api/payment/get-payments",
                goods_list: "plugin/advance/api/goods/index"
            },
            gift: {
                order_submit: "/plugin/gift/api/gift-order/order-submit",
                order_preview: "/plugin/gift/api/gift-order/order-preview",
                config: "plugin/gift/api/gift-index/config",
                pay_data: "plugin/gift/api/gift-order/pay-data",
                gift: "plugin/gift/api/gift-index/gift",
                poster: "plugin/gift/api/gift-index/poster",
                send_list: "plugin/gift/api/order-list/send-list",
                send_detail: "plugin/gift/api/order-list/send-detail",
                turn: "plugin/gift/api/gift-order/turn",
                get_turn: "plugin/gift/api/gift-order/get-turn",
                my_join: "plugin/gift/api/gift-join/my-join",
                my_win: "plugin/gift/api/gift-join/my-win",
                win_detail: "plugin/gift/api/gift-join/win-detail",
                join: "plugin/gift/api/gift-join/join",
                join_status: "plugin/gift/api/gift-join/join-status",
                join_detail: "plugin/gift/api/gift-join/join-detail",
                preview: "plugin/gift/api/gift-order/gift-convert-preview",
                convert: "plugin/gift/api/gift-order/gift-convert",
                list: "plugin/gift/api/goods/goods-list",
                goods: "plugin/gift/api/goods/detail",
                cancel: "plugin/gift/api/gift-order/order-cancel"
            },
            quick_share: {
                poster_list: "plugin/quick_share/api/goods/poster-list",
                goods: "plugin/quick_share/api/goods/index",
                poster: "plugin/quick_share/api/goods/poster",
                setting: "plugin/quick_share/api/setting/index"
            }
        };
        e.default = i
    }, "37f8": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("05c9")).default)
        }).call(this, n("543d").createPage)
    }, 3813: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9e51")).default)
        }).call(this, n("543d").createPage)
    }, "3a3f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3818")).default)
        }).call(this, n("543d").createPage)
    }, "3a5f": function (t, e, n) {
        t.exports = {
            acid: -1,
            version: "1.0.0",
            siteroot: "http://localhost/app/index.php",
            apiroot: "http://localhost/web/index.php?_mall_id=1"
        }
    }, "3d1c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("39fe")).default)
        }).call(this, n("543d").createPage)
    }, "3e8b": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("87b8")).default)
        }).call(this, n("543d").createPage)
    }, "3ecd": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("15e9")).default)
        }).call(this, n("543d").createPage)
    }, 4151: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b359")).default)
        }).call(this, n("543d").createPage)
    }, "41ea": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("795e")).default)
        }).call(this, n("543d").createPage)
    }, "42a8": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("301c")).default)
        }).call(this, n("543d").createPage)
    }, "42de": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("460f")).default)
        }).call(this, n("543d").createPage)
    }, 4360: function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = i(n("66fd")), r = i(n("2f62")), o = i(n("d30e")), c = i(n("0f9a")), l = i(n("0be2")), s = i(n("2a74")),
            u = i(n("dcdb")), d = i(n("d1e4")), f = i(n("121d")), p = i(n("9465")), h = i(n("5152")), g = i(n("c3b2")),
            v = i(n("ce85")), y = i(n("e1fb")), m = i(n("ffa5"));
        a.default.use(r.default);
        var _ = new r.default.Store({
            modules: {
                mallConfig: o.default,
                user: c.default,
                gConfig: l.default,
                index: s.default,
                orderSubmit: u.default,
                pagination: d.default,
                loading: f.default,
                payment: p.default,
                scanCode: h.default,
                page: g.default,
                userCenter: v.default,
                iPhoneX: y.default,
                gift: m.default
            }
        }), x = _;
        e.default = x
    }, "44c4": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2c8d")).default)
        }).call(this, n("543d").createPage)
    }, "4af9": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c581")).default)
        }).call(this, n("543d").createPage)
    }, "4bca": function (t, e, n) {
        function i(t, e, n, i, a, r, o) {
            try {
                var c = t[r](o), l = c.value
            } catch (t) {
                return void n(t)
            }
            c.done ? e(l) : Promise.resolve(l).then(i, a)
        }

        function a(t) {
            return function () {
                var e = this, n = arguments;
                return new Promise(function (a, r) {
                    function o(t) {
                        i(l, a, r, o, c, "next", t)
                    }

                    function c(t) {
                        i(l, a, r, o, c, "throw", t)
                    }

                    var l = t.apply(e, n);
                    o(void 0)
                })
            }
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var r = function (t) {
            return t && t.__esModule ? t : {default: t}
        }(n("a34a")), o = function () {
            var t = a(r.default.mark(function t(e) {
                var n;
                return r.default.wrap(function (t) {
                    for (; ;) switch (t.prev = t.next) {
                        case 0:
                            n = {
                                type: "global",
                                text: "加载中",
                                color: "#ffffff",
                                backgroundImage: ""
                            }, (e = Object.assign(n, e)).isShow = !0, this.$store.dispatch("loading/actionGetLoading", e);
                        case 4:
                        case"end":
                            return t.stop()
                    }
                }, t, this)
            }));
            return function (e) {
                return t.apply(this, arguments)
            }
        }();
        e.default = o
    }, "4c2a2": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e8d2")).default)
        }).call(this, n("543d").createPage)
    }, "4c94": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b46e")).default)
        }).call(this, n("543d").createPage)
    }, "4dbd": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d5d4")).default)
        }).call(this, n("543d").createPage)
    }, "4dca": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5fce")).default)
        }).call(this, n("543d").createPage)
    }, "4eb9": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("581e")).default)
        }).call(this, n("543d").createPage)
    }, "4f83": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1aec")).default)
        }).call(this, n("543d").createPage)
    }, 5152: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0, state: {userCoupon: null}, mutations: {
                mutSetUserCoupon: function (t, e) {
                    t.userCoupon = e
                }
            }, actions: {}
        };
        e.default = i
    }, "537e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7a22")).default)
        }).call(this, n("543d").createPage)
    }, "543d": function (e, n, i) {
        function a(t, e) {
            return c(t) || o(t, e) || r()
        }

        function r() {
            throw new TypeError("Invalid attempt to destructure non-iterable instance")
        }

        function o(t, e) {
            if (Symbol.iterator in Object(t) || "[object Arguments]" === Object.prototype.toString.call(t)) {
                var n = [], i = !0, a = !1, r = void 0;
                try {
                    for (var o, c = t[Symbol.iterator](); !(i = (o = c.next()).done) && (n.push(o.value), !e || n.length !== e); i = !0) ;
                } catch (t) {
                    a = !0, r = t
                } finally {
                    try {
                        i || null == c.return || c.return()
                    } finally {
                        if (a) throw r
                    }
                }
                return n
            }
        }

        function c(t) {
            if (Array.isArray(t)) return t
        }

        function l(t, e, n) {
            return e in t ? Object.defineProperty(t, e, {
                value: n,
                enumerable: !0,
                configurable: !0,
                writable: !0
            }) : t[e] = n, t
        }

        function s(t) {
            return f(t) || d(t) || u()
        }

        function u() {
            throw new TypeError("Invalid attempt to spread non-iterable instance")
        }

        function d(t) {
            if (Symbol.iterator in Object(t) || "[object Arguments]" === Object.prototype.toString.call(t)) return Array.from(t)
        }

        function f(t) {
            if (Array.isArray(t)) {
                for (var e = 0, n = new Array(t.length); e < t.length; e++) n[e] = t[e];
                return n
            }
        }

        function p(e) {
            return (p = "function" == typeof Symbol && "symbol" === t(Symbol.iterator) ? function (e) {
                return void 0 === e ? "undefined" : t(e)
            } : function (e) {
                return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : void 0 === e ? "undefined" : t(e)
            })(e)
        }

        function h(t) {
            return "function" == typeof t
        }

        function g(t) {
            return "string" == typeof t
        }

        function v(t) {
            return "[object Object]" === kt.call(t)
        }

        function y(t, e) {
            return Mt.call(t, e)
        }

        function m() {
        }

        function _(t) {
            var e = Object.create(null);
            return function (n) {
                return e[n] || (e[n] = t(n))
            }
        }

        function x(t, e) {
            var n = e ? t ? t.concat(e) : Array.isArray(e) ? e : [e] : t;
            return n ? b(n) : n
        }

        function b(t) {
            for (var e = [], n = 0; n < t.length; n++) -1 === e.indexOf(t[n]) && e.push(t[n]);
            return e
        }

        function P(t, e) {
            var n = t.indexOf(e);
            -1 !== n && t.splice(n, 1)
        }

        function w(t, e) {
            Object.keys(e).forEach(function (n) {
                -1 !== Lt.indexOf(n) && h(e[n]) && (t[n] = x(t[n], e[n]))
            })
        }

        function S(t, e) {
            t && e && Object.keys(e).forEach(function (n) {
                -1 !== Lt.indexOf(n) && h(e[n]) && P(t[n], e[n])
            })
        }

        function A(t) {
            return function (e) {
                return t(e) || e
            }
        }

        function T(t) {
            return !!t && ("object" === p(t) || "function" == typeof t) && "function" == typeof t.then
        }

        function k(t, e) {
            for (var n = !1, i = 0; i < t.length; i++) {
                var a = t[i];
                if (n) n = Promise.then(A(a)); else {
                    var r = a(e);
                    if (T(r) && (n = Promise.resolve(r)), !1 === r) return {
                        then: function () {
                        }
                    }
                }
            }
            return n || {
                then: function (t) {
                    return t(e)
                }
            }
        }

        function M(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
            return ["success", "fail", "complete"].forEach(function (n) {
                if (Array.isArray(t[n])) {
                    var i = e[n];
                    e[n] = function (e) {
                        k(t[n], e).then(function (t) {
                            return h(i) && i(t) || t
                        })
                    }
                }
            }), e
        }

        function O(t, e) {
            var n = [];
            Array.isArray(Dt.returnValue) && n.push.apply(n, s(Dt.returnValue));
            var i = $t[t];
            return i && Array.isArray(i.returnValue) && n.push.apply(n, s(i.returnValue)), n.forEach(function (t) {
                e = t(e) || e
            }), e
        }

        function C(t) {
            var e = Object.create(null);
            Object.keys(Dt).forEach(function (t) {
                "returnValue" !== t && (e[t] = Dt[t].slice())
            });
            var n = $t[t];
            return n && Object.keys(n).forEach(function (t) {
                "returnValue" !== t && (e[t] = (e[t] || []).concat(n[t]))
            }), e
        }

        function L(t, e, n) {
            for (var i = arguments.length, a = new Array(i > 3 ? i - 3 : 0), r = 3; r < i; r++) a[r - 3] = arguments[r];
            var o = C(t);
            return o && Object.keys(o).length ? Array.isArray(o.invoke) ? k(o.invoke, n).then(function (t) {
                return e.apply(void 0, [M(o, t)].concat(a))
            }) : e.apply(void 0, [M(o, n)].concat(a)) : e.apply(void 0, [n].concat(a))
        }

        function D(t) {
            return Ft.test(t)
        }

        function $(t) {
            return jt.test(t)
        }

        function j(t) {
            return Rt.test(t)
        }

        function F(t) {
            return t.then(function (t) {
                return [null, t]
            }).catch(function (t) {
                return [t]
            })
        }

        function R(t) {
            return !(D(t) || $(t) || j(t))
        }

        function E(t, e) {
            return R(t) ? function () {
                for (var n = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, i = arguments.length, a = new Array(i > 1 ? i - 1 : 0), r = 1; r < i; r++) a[r - 1] = arguments[r];
                return h(n.success) || h(n.fail) || h(n.complete) ? O(t, L.apply(void 0, [t, e, n].concat(a))) : O(t, F(new Promise(function (i, r) {
                    L.apply(void 0, [t, e, Object.assign({}, n, {
                        success: i,
                        fail: r
                    })].concat(a)), Promise.prototype.finally || (Promise.prototype.finally = function (t) {
                        var e = this.constructor;
                        return this.then(function (n) {
                            return e.resolve(t()).then(function () {
                                return n
                            })
                        }, function (n) {
                            return e.resolve(t()).then(function () {
                                throw n
                            })
                        })
                    })
                })))
            } : e
        }

        function I() {
            var t = wx.getSystemInfoSync(), e = t.platform, n = t.pixelRatio, i = t.windowWidth;
            Bt = i, Nt = n, zt = "ios" === e
        }

        function z(t, e, n) {
            return function (i) {
                return e(N(t, i, n))
            }
        }

        function B(t, e) {
            var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {},
                i = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {},
                a = arguments.length > 4 && void 0 !== arguments[4] && arguments[4];
            if (v(e)) {
                var r = !0 === a ? e : {};
                for (var o in h(n) && (n = n(e, r) || {}), e) if (y(n, o)) {
                    var c = n[o];
                    h(c) && (c = c(e[o], e, r)), c ? g(c) ? r[c] = e[o] : v(c) && (r[c.name ? c.name : o] = c.value) : console.warn("微信小程序 ".concat(t, "暂不支持").concat(o))
                } else -1 !== Gt.indexOf(o) ? r[o] = z(t, e[o], i) : a || (r[o] = e[o]);
                return r
            }
            return h(e) && (e = z(t, e, i)), e
        }

        function N(t, e, n) {
            var i = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
            return h(Ut.returnValue) && (e = Ut.returnValue(t, e)), B(t, e, n, {}, i)
        }

        function W(t, e) {
            if (y(Ut, t)) {
                var n = Ut[t];
                return n ? function (e, i) {
                    var a = n;
                    h(n) && (a = n(e));
                    var r = [e = B(t, e, a.args, a.returnValue)];
                    void 0 !== i && r.push(i);
                    var o = wx[a.name || t].apply(wx, r);
                    return $(t) ? N(t, o, a.returnValue, D(t)) : o
                } : function () {
                    console.error("微信小程序 暂不支持".concat(t))
                }
            }
            return e
        }

        function H(t) {
            return function (e) {
                var n = e.fail, i = e.complete, a = {errMsg: "".concat(t, ":fail:暂不支持 ").concat(t, " 方法")};
                h(n) && n(a), h(i) && i(a)
            }
        }

        function U(t, e, n) {
            return t[e].apply(t, n)
        }

        function V(t) {
            if (wx.canIUse("nextTick")) {
                var e = t.triggerEvent;
                t.triggerEvent = function (n) {
                    for (var i = arguments.length, a = new Array(i > 1 ? i - 1 : 0), r = 1; r < i; r++) a[r - 1] = arguments[r];
                    return e.apply(t, [ie(n)].concat(a))
                }
            }
        }

        function q(t, e) {
            var n = e[t];
            e[t] = n ? function () {
                V(this);
                for (var t = arguments.length, e = new Array(t), i = 0; i < t; i++) e[i] = arguments[i];
                return n.apply(this, e)
            } : function () {
                V(this)
            }
        }

        function G(t, e) {
            var n = t.$mp[t.mpType];
            e.forEach(function (e) {
                y(n, e) && (t[e] = n[e])
            })
        }

        function X(t, e) {
            if (!e) return !0;
            if (Tt.default.options && Array.isArray(Tt.default.options[t])) return !0;
            if (e = e.default || e, h(e)) return !!h(e.extendOptions[t]) || !!(e.super && e.super.options && Array.isArray(e.super.options[t]));
            if (h(e[t])) return !0;
            var n = e.mixins;
            return Array.isArray(n) ? !!n.find(function (e) {
                return X(t, e)
            }) : void 0
        }

        function J(t, e, n) {
            e.forEach(function (e) {
                X(e, n) && (t[e] = function (t) {
                    return this.$vm && this.$vm.__call_hook(e, t)
                })
            })
        }

        function Q(t, e) {
            var n;
            return e = e.default || e, h(e) ? (n = e, e = n.extendOptions) : n = t.extend(e), [n, e]
        }

        function K(t, e) {
            if (Array.isArray(e) && e.length) {
                var n = Object.create(null);
                e.forEach(function (t) {
                    n[t] = !0
                }), t.$scopedSlots = t.$slots = n
            }
        }

        function Y(t, e) {
            var n = (t = (t || "").split(",")).length;
            1 === n ? e._$vueId = t[0] : 2 === n && (e._$vueId = t[0], e._$vuePid = t[1])
        }

        function Z(t, e) {
            var n = t.data || {}, i = t.methods || {};
            if ("function" == typeof n) try {
                n = n.call(e)
            } catch (t) {
                Object({
                    NODE_ENV: "production",
                    VUE_APP_PLATFORM: "mp-weixin",
                    BASE_URL: "/"
                }).VUE_APP_DEBUG && console.warn("根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。", n)
            } else try {
                n = JSON.parse(JSON.stringify(n))
            } catch (t) {
            }
            return v(n) || (n = {}), Object.keys(i).forEach(function (t) {
                -1 !== e.__lifecycle_hooks__.indexOf(t) || y(n, t) || (n[t] = i[t])
            }), n
        }

        function tt(t) {
            return function (e, n) {
                this.$vm && (this.$vm[t] = e)
            }
        }

        function et(t, e) {
            var n = t.behaviors, i = t.extends, a = t.mixins, r = t.props;
            r || (t.props = r = []);
            var o = [];
            return Array.isArray(n) && n.forEach(function (t) {
                o.push(t.replace("uni://", "wx".concat("://"))), "uni://form-field" === t && (Array.isArray(r) ? (r.push("name"), r.push("value")) : (r.name = {
                    type: String,
                    default: ""
                }, r.value = {type: [String, Number, Boolean, Array, Object, Date], default: ""}))
            }), v(i) && i.props && o.push(e({properties: it(i.props, !0)})), Array.isArray(a) && a.forEach(function (t) {
                v(t) && t.props && o.push(e({properties: it(t.props, !0)}))
            }), o
        }

        function nt(t, e, n, i) {
            return Array.isArray(e) && 1 === e.length ? e[0] : e
        }

        function it(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
                n = (arguments.length > 2 && void 0 !== arguments[2] && arguments[2], {});
            return e || (n.vueId = {type: String, value: ""}, n.vueSlots = {
                type: null,
                value: [],
                observer: function (t, e) {
                    var n = Object.create(null);
                    t.forEach(function (t) {
                        n[t] = !0
                    }), this.setData({$slots: n})
                }
            }), Array.isArray(t) ? t.forEach(function (t) {
                n[t] = {type: null, observer: tt(t)}
            }) : v(t) && Object.keys(t).forEach(function (e) {
                var i = t[e];
                if (v(i)) {
                    var a = i.default;
                    h(a) && (a = a()), i.type = nt(e, i.type), n[e] = {
                        type: -1 !== re.indexOf(i.type) ? i.type : null,
                        value: a,
                        observer: tt(e)
                    }
                } else {
                    var r = nt(e, i);
                    n[e] = {type: -1 !== re.indexOf(r) ? r : null, observer: tt(e)}
                }
            }), n
        }

        function at(t) {
            try {
                t.mp = JSON.parse(JSON.stringify(t))
            } catch (t) {
            }
            return t.stopPropagation = m, t.preventDefault = m, t.target = t.target || {}, y(t, "detail") || (t.detail = {}), v(t.detail) && (t.target = Object.assign({}, t.target, t.detail)), t
        }

        function rt(t, e) {
            var n = t;
            return e.forEach(function (e) {
                var i = e[0], a = e[2];
                if (i || void 0 !== a) {
                    var r = e[1], o = e[3], c = i ? t.__get_value(i, n) : n;
                    Number.isInteger(c) ? n = a : r ? Array.isArray(c) ? n = c.find(function (e) {
                        return t.__get_value(r, e) === a
                    }) : v(c) ? n = Object.keys(c).find(function (e) {
                        return t.__get_value(r, c[e]) === a
                    }) : console.error("v-for 暂不支持循环数据：", c) : n = c[a], o && (n = t.__get_value(o, n))
                }
            }), n
        }

        function ot(t, e, n) {
            var i = {};
            return Array.isArray(e) && e.length && e.forEach(function (e, a) {
                "string" == typeof e ? e ? "$event" === e ? i["$" + a] = n : 0 === e.indexOf("$event.") ? i["$" + a] = t.__get_value(e.replace("$event.", ""), n) : i["$" + a] = t.__get_value(e) : i["$" + a] = t : i["$" + a] = rt(t, e)
            }), i
        }

        function ct(t) {
            for (var e = {}, n = 1; n < t.length; n++) {
                var i = t[n];
                e[i[0]] = i[1]
            }
            return e
        }

        function lt(t, e) {
            var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : [],
                i = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : [],
                a = arguments.length > 4 ? arguments[4] : void 0, r = arguments.length > 5 ? arguments[5] : void 0,
                o = !1;
            if (a && (o = e.currentTarget && e.currentTarget.dataset && "wx" === e.currentTarget.dataset.comType, !n.length)) return o ? [e] : e.detail.__args__ || e.detail;
            var c = ot(t, i, e), l = [];
            return n.forEach(function (t) {
                "$event" === t ? "__set_model" !== r || a ? a && !o ? l.push(e.detail.__args__[0]) : l.push(e) : l.push(e.target.value) : Array.isArray(t) && "o" === t[0] ? l.push(ct(t)) : "string" == typeof t && y(c, t) ? l.push(c[t]) : l.push(t)
            }), l
        }

        function st(t, e) {
            return t === e || "regionchange" === e && ("begin" === t || "end" === t)
        }

        function ut(t) {
            var e = this, n = ((t = at(t)).currentTarget || t.target).dataset;
            if (!n) return console.warn("事件信息不存在");
            var i = n.eventOpts || n["event-opts"];
            if (!i) return console.warn("事件信息不存在");
            var a = t.type, r = [];
            return i.forEach(function (n) {
                var i = n[0], o = n[1], c = i.charAt(0) === ce, l = (i = c ? i.slice(1) : i).charAt(0) === oe;
                i = l ? i.slice(1) : i, o && st(a, i) && o.forEach(function (n) {
                    var i = n[0];
                    if (i) {
                        var a = e.$vm;
                        a.$options.generic && a.$parent && a.$parent.$parent && (a = a.$parent.$parent);
                        var o = a[i];
                        if (!h(o)) throw new Error(" _vm.".concat(i, " is not a function"));
                        if (l) {
                            if (o.once) return;
                            o.once = !0
                        }
                        r.push(o.apply(a, lt(e.$vm, t, n[1], n[2], c, i)))
                    }
                })
            }), "input" === a && 1 === r.length && void 0 !== r[0] ? r[0] : void 0
        }

        function dt(t, e) {
            var n = e.mocks, i = e.initRefs;
            t.$options.store && (Tt.default.prototype.$store = t.$options.store), Tt.default.prototype.mpHost = "mp-weixin", Tt.default.mixin({
                beforeCreate: function () {
                    this.$options.mpType && (this.mpType = this.$options.mpType, this.$mp = l({data: {}}, this.mpType, this.$options.mpInstance), this.$scope = this.$options.mpInstance, delete this.$options.mpType, delete this.$options.mpInstance, "app" !== this.mpType && (i(this), G(this, n)))
                }
            });
            var a = {
                onLaunch: function (e) {
                    this.$vm || (wx.canIUse("nextTick") || console.error("当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上"), this.$vm = t, this.$vm.$mp = {app: this}, this.$vm.$scope = this, this.$vm._isMounted = !0, this.$vm.__call_hook("mounted", e), this.$vm.__call_hook("onLaunch", e))
                }
            };
            return a.globalData = t.$options.globalData || {}, J(a, le), a
        }

        function ft(t, e) {
            var n = t.$children, i = n.find(function (t) {
                return t.$scope._$vueId === e
            });
            if (i) return i;
            for (var a = n.length - 1; a >= 0; a--) if (i = ft(n[a], e)) return i
        }

        function pt(t) {
            return Behavior(t)
        }

        function ht() {
            return !!this.route
        }

        function gt(t) {
            this.triggerEvent("__l", t)
        }

        function vt(t) {
            var e = t.$scope;
            Object.defineProperty(t, "$refs", {
                get: function () {
                    var t = {};
                    return e.selectAllComponents(".vue-ref").forEach(function (e) {
                        var n = e.dataset.ref;
                        t[n] = e.$vm || e
                    }), e.selectAllComponents(".vue-ref-in-for").forEach(function (e) {
                        var n = e.dataset.ref;
                        t[n] || (t[n] = []), t[n].push(e.$vm || e)
                    }), t
                }
            })
        }

        function yt(t) {
            var e, n = t.detail || t.value, i = n.vuePid, a = n.vueOptions;
            i && (e = ft(this.$vm, i)), e || (e = this.$vm), a.parent = e
        }

        function mt(t) {
            return dt(t, {mocks: se, initRefs: vt})
        }

        function _t(t) {
            return App(mt(t)), t
        }

        function xt(t) {
            var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, n = e.isPage,
                i = e.initRelation, r = a(Q(Tt.default, t), 2), o = r[0], c = r[1], l = {
                    options: {multipleSlots: !0, addGlobalClass: !0},
                    data: Z(c, Tt.default.prototype),
                    behaviors: et(c, pt),
                    properties: it(c.props, !1, c.__file),
                    lifetimes: {
                        attached: function () {
                            var t = this.properties,
                                e = {mpType: n.call(this) ? "page" : "component", mpInstance: this, propsData: t};
                            Y(t.vueId, this), i.call(this, {
                                vuePid: this._$vuePid,
                                vueOptions: e
                            }), this.$vm = new o(e), K(this.$vm, t.vueSlots), this.$vm.$mount()
                        }, ready: function () {
                            this.$vm && (this.$vm._isMounted = !0, this.$vm.__call_hook("mounted"), this.$vm.__call_hook("onReady"))
                        }, detached: function () {
                            this.$vm.$destroy()
                        }
                    },
                    pageLifetimes: {
                        show: function (t) {
                            this.$vm && this.$vm.__call_hook("onPageShow", t)
                        }, hide: function () {
                            this.$vm && this.$vm.__call_hook("onPageHide")
                        }, resize: function (t) {
                            this.$vm && this.$vm.__call_hook("onPageResize", t)
                        }
                    },
                    methods: {__l: yt, __e: ut}
                };
            return Array.isArray(c.wxsCallMethods) && c.wxsCallMethods.forEach(function (t) {
                l.methods[t] = function (e) {
                    return this.$vm[t](e)
                }
            }), n ? l : [l, o]
        }

        function bt(t) {
            return xt(t, {isPage: ht, initRelation: gt})
        }

        function Pt(t, e) {
            e.isPage, e.initRelation;
            var n = bt(t);
            return J(n.methods, ue, t), n.methods.onLoad = function (t) {
                this.$vm.$mp.query = t, this.$vm.__call_hook("onLoad", t)
            }, n
        }

        function wt(t) {
            return Pt(t, {isPage: ht, initRelation: gt})
        }

        function St(t) {
            return Component(wt(t))
        }

        function At(t) {
            return Component(bt(t))
        }

        Object.defineProperty(n, "__esModule", {value: !0}), n.createApp = _t, n.createComponent = At, n.createPage = St, n.default = void 0;
        var Tt = function (t) {
                return t && t.__esModule ? t : {default: t}
            }(i("66fd")), kt = Object.prototype.toString, Mt = Object.prototype.hasOwnProperty, Ot = /-(\w)/g,
            Ct = _(function (t) {
                return t.replace(Ot, function (t, e) {
                    return e ? e.toUpperCase() : ""
                })
            }), Lt = ["invoke", "success", "fail", "complete", "returnValue"], Dt = {}, $t = {},
            jt = /^\$|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64/,
            Ft = /^create|Manager$/, Rt = /^on/, Et = 1e-4, It = 750, zt = !1, Bt = 0, Nt = 0, Wt = {
                promiseInterceptor: {
                    returnValue: function (t) {
                        return T(t) ? t.then(function (t) {
                            return t[1]
                        }).catch(function (t) {
                            return t[0]
                        }) : t
                    }
                }
            }, Ht = Object.freeze({
                upx2px: function (t, e) {
                    if (0 === Bt && I(), 0 === (t = Number(t))) return 0;
                    var n = t / It * (e || Bt);
                    return n < 0 && (n = -n), 0 === (n = Math.floor(n + Et)) ? 1 !== Nt && zt ? .5 : 1 : t < 0 ? -n : n
                }, interceptors: Wt, addInterceptor: function (t, e) {
                    "string" == typeof t && v(e) ? w($t[t] || ($t[t] = {}), e) : v(t) && w(Dt, t)
                }, removeInterceptor: function (t, e) {
                    "string" == typeof t ? v(e) ? S($t[t], e) : delete $t[t] : v(t) && S(Dt, t)
                }
            }), Ut = {
                previewImage: {
                    args: function (t) {
                        var e = parseInt(t.current);
                        if (!isNaN(e)) {
                            var n = t.urls;
                            if (Array.isArray(n)) {
                                var i = n.length;
                                if (i) return e < 0 ? e = 0 : e >= i && (e = i - 1), e > 0 ? (t.current = n[e], t.urls = n.filter(function (t, i) {
                                    return !(i < e) || t !== n[e]
                                })) : t.current = n[0], {indicator: !1, loop: !1}
                            }
                        }
                    }
                }
            }, Vt = ["vibrate"], qt = [], Gt = ["success", "fail", "cancel", "complete"], Xt = Object.create(null);
        ["subscribePush", "unsubscribePush", "onPush", "offPush", "share"].forEach(function (t) {
            Xt[t] = H(t)
        });
        var Jt = {oauth: ["weixin"], share: ["weixin"], payment: ["wxpay"], push: ["weixin"]}, Qt = Object.freeze({
            getProvider: function (t) {
                var e = t.service, n = t.success, i = t.fail, a = t.complete, r = !1;
                Jt[e] ? (r = {
                    errMsg: "getProvider:ok",
                    service: e,
                    provider: Jt[e]
                }, h(n) && n(r)) : (r = {errMsg: "getProvider:fail:服务[" + e + "]不存在"}, h(i) && i(r)), h(a) && a(r)
            }
        }), Kt = function () {
            return "function" == typeof getUniEmitter ? getUniEmitter : function () {
                return t || (t = new Tt.default), t
            };
            var t
        }(), Yt = Object.freeze({
            $on: function () {
                return U(Kt(), "$on", Array.prototype.slice.call(arguments))
            }, $off: function () {
                return U(Kt(), "$off", Array.prototype.slice.call(arguments))
            }, $once: function () {
                return U(Kt(), "$once", Array.prototype.slice.call(arguments))
            }, $emit: function () {
                return U(Kt(), "$emit", Array.prototype.slice.call(arguments))
            }
        }), Zt = Object.freeze({}), te = Page, ee = Component, ne = /:/g, ie = _(function (t) {
            return Ct(t.replace(ne, "-"))
        });
        Page = function () {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
            return q("onLoad", t), te(t)
        }, Component = function () {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
            return q("created", t), ee(t)
        };
        var ae = ["onPullDownRefresh", "onReachBottom", "onShareAppMessage", "onPageScroll", "onResize", "onTabItemTap"],
            re = [String, Number, Boolean, Object, Array, null], oe = "~", ce = "^",
            le = ["onShow", "onHide", "onError", "onPageNotFound"],
            se = ["__route__", "__wxExparserNodeId__", "__wxWebviewId__"], ue = ["onShow", "onHide", "onUnload"];
        ue.push.apply(ue, ae), Vt.forEach(function (t) {
            Ut[t] = !1
        }), qt.forEach(function (t) {
            var e = Ut[t] && Ut[t].name ? Ut[t].name : t;
            wx.canIUse(e) || (Ut[t] = !1)
        });
        var de = {};
        "undefined" != typeof Proxy ? de = new Proxy({}, {
            get: function (t, e) {
                return t[e] ? t[e] : Ht[e] ? Ht[e] : Zt[e] ? E(e, Zt[e]) : Qt[e] ? E(e, Qt[e]) : Xt[e] ? E(e, Xt[e]) : Yt[e] ? Yt[e] : y(wx, e) || y(Ut, e) ? E(e, W(e, wx[e])) : void 0
            }, set: function (t, e, n) {
                return t[e] = n, !0
            }
        }) : (Object.keys(Ht).forEach(function (t) {
            de[t] = Ht[t]
        }), Object.keys(Xt).forEach(function (t) {
            de[t] = E(t, Xt[t])
        }), Object.keys(Qt).forEach(function (t) {
            de[t] = E(t, Xt[t])
        }), Object.keys(Yt).forEach(function (t) {
            de[t] = Yt[t]
        }), Object.keys(Zt).forEach(function (t) {
            de[t] = E(t, Zt[t])
        }), Object.keys(wx).forEach(function (t) {
            (y(wx, t) || y(Ut, t)) && (de[t] = E(t, W(t, wx[t])))
        })), wx.createApp = _t, wx.createPage = St, wx.createComponent = At;
        var fe = de;
        n.default = fe
    }, "54bf": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f4ec")).default)
        }).call(this, n("543d").createPage)
    }, "552b": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1deb")).default)
        }).call(this, n("543d").createPage)
    }, "55ab": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3bcc")).default)
        }).call(this, n("543d").createPage)
    }, "56d7": function (t, e, n) {
        (function (t) {
            function e() {
                if ("function" != typeof WeakMap) return null;
                var t = new WeakMap;
                return e = function () {
                    return t
                }, t
            }

            function i(t) {
                if (t && t.__esModule) return t;
                var n = e();
                if (n && n.has(t)) return n.get(t);
                var i = {};
                if (null != t) {
                    var a = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) {
                        var o = a ? Object.getOwnPropertyDescriptor(t, r) : null;
                        o && (o.get || o.set) ? Object.defineProperty(i, r, o) : i[r] = t[r]
                    }
                }
                return i.default = t, n && n.set(t, i), i
            }

            function a(t) {
                return t && t.__esModule ? t : {default: t}
            }

            function r(t, e) {
                var n = Object.keys(t);
                if (Object.getOwnPropertySymbols) {
                    var i = Object.getOwnPropertySymbols(t);
                    e && (i = i.filter(function (e) {
                        return Object.getOwnPropertyDescriptor(t, e).enumerable
                    })), n.push.apply(n, i)
                }
                return n
            }

            function o(t, e, n) {
                return e in t ? Object.defineProperty(t, e, {
                    value: n,
                    enumerable: !0,
                    configurable: !0,
                    writable: !0
                }) : t[e] = n, t
            }

            n("6cdc");
            var c = a(n("66fd")), l = a(n("3dfd")), s = a(n("4360")), u = a(n("3a5f")), d = a(n("58b6"));
            n("89f3");
            var f = a(n("9dc18")), p = a(n("816e")), h = a(n("27f5")), g = n("63ad"), v = a(n("d3b5")),
                y = i(n("ac6b")), m = i(n("b1c7")), _ = a(n("2749")), x = a(n("9d0f")), b = a(n("4bca")),
                P = a(n("b7dd")), w = a(n("dd00")), S = a(n("7319")), A = a(n("972f")), T = a(n("ab4f")),
                k = a(n("f169"));
            c.default.component("app-button", function () {
                return n.e("components/basic-component/app-button/app-button").then(n.bind(null, "aec9"))
            }), c.default.component("app-form-id", function () {
                return Promise.all([n.e("common/vendor"), n.e("components/basic-component/app-form-id/app-form-id")]).then(n.bind(null, "8ee9"))
            }), c.default.component("app-layout", function () {
                return Promise.all([n.e("common/vendor"), n.e("components/basic-component/app-layout/app-layout")]).then(n.bind(null, "0b17"))
            }), c.default.component("app-input", function () {
                return n.e("components/basic-component/app-input/app-input").then(n.bind(null, "75e2"))
            }), c.default.component("app-jump-button", function () {
                return Promise.all([n.e("common/vendor"), n.e("components/basic-component/app-jump-button/app-jump-button")]).then(n.bind(null, "b362"))
            }), c.default.component("app-load-text", function () {
                return n.e("components/basic-component/app-load-text/app-load-text").then(n.bind(null, "cae6"))
            }), c.default.component("app-image", function () {
                return n.e("components/basic-component/app-image/app-image").then(n.bind(null, "f54c"))
            }), c.default.use({
                install: function (t, e) {
                    t.prototype.$appVersion = d.default, t.prototype.$store = s.default, t.prototype.$platform = g.platform, t.prototype.$siteInfo = u.default, t.prototype.$api = h.default, t.prototype.$request = f.default, t.prototype.$storage = y, t.prototype.$user = p.default, t.prototype.$mallConfig = v.default, t.prototype.$utils = m, t.prototype.$const = _.default, t.prototype.$event = x.default, t.prototype.$showLoading = b.default, t.prototype.$hideLoading = P.default, t.prototype.$platDiff = w.default, t.prototype.$lazyLoadingData = S.default, t.prototype.$jump = A.default, t.prototype.$popupAd = T.default, t.prototype.$shareAppMessage = k.default
                }
            }), c.default.config.productionTip = !1, l.default.mpType = "app", t(new c.default(function (t) {
                for (var e = 1; e < arguments.length; e++) {
                    var n = null != arguments[e] ? arguments[e] : {};
                    e % 2 ? r(n, !0).forEach(function (e) {
                        o(t, e, n[e])
                    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : r(n).forEach(function (e) {
                        Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e))
                    })
                }
                return t
            }({store: s.default}, l.default))).$mount()
        }).call(this, n("543d").createApp)
    }, "587c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("bf12")).default)
        }).call(this, n("543d").createPage)
    }, "58ca": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9964")).default)
        }).call(this, n("543d").createPage)
    }, "598e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9e2c")).default)
        }).call(this, n("543d").createPage)
    }, "5b0c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("deb4")).default)
        }).call(this, n("543d").createPage)
    }, "5b3c": function (t, e, n) {
        function i(t) {
            return t = t.replace(/&forall;/g, "∀"), t = t.replace(/&part;/g, "∂"), t = t.replace(/&exist;/g, "∃"), t = t.replace(/&empty;/g, "∅"), t = t.replace(/&nabla;/g, "∇"), t = t.replace(/&isin;/g, "∈"), t = t.replace(/&notin;/g, "∉"), t = t.replace(/&ni;/g, "∋"), t = t.replace(/&prod;/g, "∏"), t = t.replace(/&sum;/g, "∑"), t = t.replace(/&minus;/g, "−"), t = t.replace(/&lowast;/g, "∗"), t = t.replace(/&radic;/g, "√"), t = t.replace(/&prop;/g, "∝"), t = t.replace(/&infin;/g, "∞"), t = t.replace(/&ang;/g, "∠"), t = t.replace(/&and;/g, "∧"), t = t.replace(/&or;/g, "∨"), t = t.replace(/&cap;/g, "∩"), t = t.replace(/&cup;/g, "∪"), t = t.replace(/&int;/g, "∫"), t = t.replace(/&there4;/g, "∴"), t = t.replace(/&sim;/g, "∼"), t = t.replace(/&cong;/g, "≅"), t = t.replace(/&asymp;/g, "≈"), t = t.replace(/&ne;/g, "≠"), t = t.replace(/&le;/g, "≤"), t = t.replace(/&ge;/g, "≥"), t = t.replace(/&sub;/g, "⊂"), t = t.replace(/&sup;/g, "⊃"), t = t.replace(/&nsub;/g, "⊄"), t = t.replace(/&sube;/g, "⊆"), t = t.replace(/&supe;/g, "⊇"), t = t.replace(/&oplus;/g, "⊕"), t = t.replace(/&otimes;/g, "⊗"), t = t.replace(/&perp;/g, "⊥"), t = t.replace(/&sdot;/g, "⋅")
        }

        function a(t) {
            return t = t.replace(/&Alpha;/g, "Α"), t = t.replace(/&Beta;/g, "Β"), t = t.replace(/&Gamma;/g, "Γ"), t = t.replace(/&Delta;/g, "Δ"), t = t.replace(/&Epsilon;/g, "Ε"), t = t.replace(/&Zeta;/g, "Ζ"), t = t.replace(/&Eta;/g, "Η"), t = t.replace(/&Theta;/g, "Θ"), t = t.replace(/&Iota;/g, "Ι"), t = t.replace(/&Kappa;/g, "Κ"), t = t.replace(/&Lambda;/g, "Λ"), t = t.replace(/&Mu;/g, "Μ"), t = t.replace(/&Nu;/g, "Ν"), t = t.replace(/&Xi;/g, "Ν"), t = t.replace(/&Omicron;/g, "Ο"), t = t.replace(/&Pi;/g, "Π"), t = t.replace(/&Rho;/g, "Ρ"), t = t.replace(/&Sigma;/g, "Σ"), t = t.replace(/&Tau;/g, "Τ"), t = t.replace(/&Upsilon;/g, "Υ"), t = t.replace(/&Phi;/g, "Φ"), t = t.replace(/&Chi;/g, "Χ"), t = t.replace(/&Psi;/g, "Ψ"), t = t.replace(/&Omega;/g, "Ω"), t = t.replace(/&alpha;/g, "α"), t = t.replace(/&beta;/g, "β"), t = t.replace(/&gamma;/g, "γ"), t = t.replace(/&delta;/g, "δ"), t = t.replace(/&epsilon;/g, "ε"), t = t.replace(/&zeta;/g, "ζ"), t = t.replace(/&eta;/g, "η"), t = t.replace(/&theta;/g, "θ"), t = t.replace(/&iota;/g, "ι"), t = t.replace(/&kappa;/g, "κ"), t = t.replace(/&lambda;/g, "λ"), t = t.replace(/&mu;/g, "μ"), t = t.replace(/&nu;/g, "ν"), t = t.replace(/&xi;/g, "ξ"), t = t.replace(/&omicron;/g, "ο"), t = t.replace(/&pi;/g, "π"), t = t.replace(/&rho;/g, "ρ"), t = t.replace(/&sigmaf;/g, "ς"), t = t.replace(/&sigma;/g, "σ"), t = t.replace(/&tau;/g, "τ"), t = t.replace(/&upsilon;/g, "υ"), t = t.replace(/&phi;/g, "φ"), t = t.replace(/&chi;/g, "χ"), t = t.replace(/&psi;/g, "ψ"), t = t.replace(/&omega;/g, "ω"), t = t.replace(/&thetasym;/g, "ϑ"), t = t.replace(/&upsih;/g, "ϒ"), t = t.replace(/&piv;/g, "ϖ"), t = t.replace(/&middot;/g, "·")
        }

        function r(t) {
            return t = t.replace(/&nbsp;/g, " "), t = t.replace(/&ensp;/g, " "), t = t.replace(/&emsp;/g, "　"), t = t.replace(/&quot;/g, "'"), t = t.replace(/&amp;/g, "&"), t = t.replace(/&lt;/g, "<"), t = t.replace(/&gt;/g, ">"), t = t.replace(/&#8226;/g, "•")
        }

        function o(t) {
            return t = t.replace(/&OElig;/g, "Œ"), t = t.replace(/&oelig;/g, "œ"), t = t.replace(/&Scaron;/g, "Š"), t = t.replace(/&scaron;/g, "š"), t = t.replace(/&Yuml;/g, "Ÿ"), t = t.replace(/&fnof;/g, "ƒ"), t = t.replace(/&circ;/g, "ˆ"), t = t.replace(/&tilde;/g, "˜"), t = t.replace(/&ensp;/g, ""), t = t.replace(/&emsp;/g, ""), t = t.replace(/&thinsp;/g, ""), t = t.replace(/&zwnj;/g, ""), t = t.replace(/&zwj;/g, ""), t = t.replace(/&lrm;/g, ""), t = t.replace(/&rlm;/g, ""), t = t.replace(/&ndash;/g, "–"), t = t.replace(/&mdash;/g, "—"), t = t.replace(/&lsquo;/g, "‘"), t = t.replace(/&rsquo;/g, "’"), t = t.replace(/&sbquo;/g, "‚"), t = t.replace(/&ldquo;/g, "“"), t = t.replace(/&rdquo;/g, "”"), t = t.replace(/&bdquo;/g, "„"), t = t.replace(/&dagger;/g, "†"), t = t.replace(/&Dagger;/g, "‡"), t = t.replace(/&bull;/g, "•"), t = t.replace(/&hellip;/g, "…"), t = t.replace(/&permil;/g, "‰"), t = t.replace(/&prime;/g, "′"), t = t.replace(/&Prime;/g, "″"), t = t.replace(/&lsaquo;/g, "‹"), t = t.replace(/&rsaquo;/g, "›"), t = t.replace(/&oline;/g, "‾"), t = t.replace(/&euro;/g, "€"), t = t.replace(/&trade;/g, "™"), t = t.replace(/&larr;/g, "←"), t = t.replace(/&uarr;/g, "↑"), t = t.replace(/&rarr;/g, "→"), t = t.replace(/&darr;/g, "↓"), t = t.replace(/&harr;/g, "↔"), t = t.replace(/&crarr;/g, "↵"), t = t.replace(/&lceil;/g, "⌈"), t = t.replace(/&rceil;/g, "⌉"), t = t.replace(/&lfloor;/g, "⌊"), t = t.replace(/&rfloor;/g, "⌋"), t = t.replace(/&loz;/g, "◊"), t = t.replace(/&spades;/g, "♠"), t = t.replace(/&clubs;/g, "♣"), t = t.replace(/&hearts;/g, "♥"), t = t.replace(/&diams;/g, "♦"), t = t.replace(/&#39;/g, "'")
        }

        function c(t, e) {
            for (var n = 0; n < t.length; n++) if ("" !== t[n]) return /^\/\//.test(t[n]) ? "https:".concat(t[n]) : /^\//.test(t[n]) ? "https://".concat(e).concat(t[n]) : t[n]
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var l = {
            strDiscode: function (t) {
                return t = i(t), t = a(t), t = r(t), t = o(t)
            }, urlToHttpUrl: function (t, e) {
                return /^\/\//.test(t) ? "https:".concat(t) : /^\//.test(t) ? "https://".concat(e).concat(t) : Array.isArray(t) ? c(t, e) : t
            }
        };
        e.default = l
    }, "5baa": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1596")).default)
        }).call(this, n("543d").createPage)
    }, "5bb4": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c7fe")).default)
        }).call(this, n("543d").createPage)
    }, "5c35": function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        function a(t) {
            for (var e = {}, n = t.split(","), i = 0; i < n.length; i += 1) e[n[i]] = !0;
            return e
        }

        function r(t) {
            return /<body.*>([^]*)<\/body>/.test(t) ? RegExp.$1 : t
        }

        function o(t) {
            return t.replace(/<!--.*?-->/gi, "").replace(/\/\*.*?\*\//gi, "").replace(/[ ]+</gi, "<").replace(/<script[^]*<\/script>/gi, "").replace(/<style[^]*<\/style>/gi, "")
        }

        function c() {
            var t = {};
            return wx.getSystemInfo({
                success: function (e) {
                    t.width = e.windowWidth, t.height = e.windowHeight
                }
            }), t
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var l = i(n("5b3c")), s = i(n("023f")),
            u = a("br,code,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,ins,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),
            d = a("a,abbr,acronym,applet,b,basefont,bdo,big,button,cite,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),
            f = a("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr");
        e.default = function (t, e, n, i) {
            function a(t) {
                this.node = "element", this.tag = t, this.$screen = g
            }

            t = o(t = r(t)), t = l.default.strDiscode(t);
            var p = [], h = {nodes: [], imageUrls: []}, g = c();
            return (0, s.default)(t, {
                start: function (t, i, r) {
                    var o = new a(t);
                    if (0 !== p.length) {
                        var c = p[0];
                        void 0 === c.nodes && (c.nodes = [])
                    }
                    if (u[t] ? o.tagType = "block" : d[t] ? o.tagType = "inline" : f[t] && (o.tagType = "closeSelf"), o.attr = i.reduce(function (t, e) {
                        var n = e.name, i = e.value;
                        return "class" === n && (o.classStr = i), "style" === n && (o.styleStr = i), i.match(/ /) && (i = i.split(" ")), t[n] ? Array.isArray(t[n]) ? t[n].push(i) : t[n] = [t[n], i] : t[n] = i, t
                    }, {}), o.classStr ? o.classStr += " ".concat(o.tag) : o.classStr = o.tag, "inline" === o.tagType && (o.classStr += " inline"), "img" === o.tag) {
                        var s = o.attr.src;
                        s = l.default.urlToHttpUrl(s, n.domain), Object.assign(o.attr, n, {src: s || ""}), s && h.imageUrls.push(s)
                    }
                    if ("a" === o.tag && (o.attr.href = o.attr.href || ""), "font" === o.tag) {
                        var g = ["x-small", "small", "medium", "large", "x-large", "xx-large", "-webkit-xxx-large"],
                            v = {color: "color", face: "font-family", size: "font-size"};
                        o.styleStr || (o.styleStr = ""), Object.keys(v).forEach(function (t) {
                            if (o.attr[t]) {
                                var e = "size" === t ? g[o.attr[t] - 1] : o.attr[t];
                                o.styleStr += "".concat(v[t], ": ").concat(e, ";")
                            }
                        })
                    }
                    if ("source" === o.tag && (h.source = o.attr.src), e.start && e.start(o, h), r) {
                        var y = p[0] || h;
                        void 0 === y.nodes && (y.nodes = []), y.nodes.push(o)
                    } else p.unshift(o)
                }, end: function (t) {
                    var n = p.shift();
                    if (n.tag !== t && console.error("invalid state: mismatch end tag"), "video" === n.tag && h.source && (n.attr.src = h.source, delete h.source), e.end && e.end(n, h), 0 === p.length) h.nodes.push(n); else {
                        var i = p[0];
                        i.nodes || (i.nodes = []), i.nodes.push(n)
                    }
                }, chars: function (t) {
                    if (t.trim()) {
                        var n = {node: "text", text: t};
                        if (e.chars && e.chars(n, h), 0 === p.length) h.nodes.push(n); else {
                            var i = p[0];
                            void 0 === i.nodes && (i.nodes = []), i.nodes.push(n)
                        }
                    }
                }
            }), h
        }
    }, "5ca3": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7320")).default)
        }).call(this, n("543d").createPage)
    }, "5cdf": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b3cd")).default)
        }).call(this, n("543d").createPage)
    }, 6015: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1186")).default)
        }).call(this, n("543d").createPage)
    }, "617f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("0fa7")).default)
        }).call(this, n("543d").createPage)
    }, 6182: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4093")).default)
        }).call(this, n("543d").createPage)
    }, "61e8": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("daa9")).default)
        }).call(this, n("543d").createPage)
    }, "624e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("80ff")).default)
        }).call(this, n("543d").createPage)
    }, "62e4": function (t, e) {
        t.exports = function (t) {
            return t.webpackPolyfill || (t.deprecate = function () {
            }, t.paths = [], t.children || (t.children = []), Object.defineProperty(t, "loaded", {
                enumerable: !0,
                get: function () {
                    return t.l
                }
            }), Object.defineProperty(t, "id", {
                enumerable: !0, get: function () {
                    return t.i
                }
            }), t.webpackPolyfill = 1), t
        }
    }, 6327: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3e82")).default)
        }).call(this, n("543d").createPage)
    }, "63ad": function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.platform = void 0;
        e.platform = "wxapp"
    }, 6448: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b56c")).default)
        }).call(this, n("543d").createPage)
    }, 6577: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("91f4")).default)
        }).call(this, n("543d").createPage)
    }, "66d5": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("24c6")).default)
        }).call(this, n("543d").createPage)
    }, "66fd": function (e, n, i) {
        i.r(n), function (e) {
            function i(t) {
                return void 0 === t || null === t
            }

            function a(t) {
                return void 0 !== t && null !== t
            }

            function r(t) {
                return !0 === t
            }

            function o(t) {
                return !1 === t
            }

            function c(e) {
                return "string" == typeof e || "number" == typeof e || "symbol" === (void 0 === e ? "undefined" : t(e)) || "boolean" == typeof e
            }

            function l(e) {
                return null !== e && "object" === (void 0 === e ? "undefined" : t(e))
            }

            function s(t) {
                return "[object Object]" === yn.call(t)
            }

            function u(t) {
                return "[object RegExp]" === yn.call(t)
            }

            function d(t) {
                var e = parseFloat(String(t));
                return e >= 0 && Math.floor(e) === e && isFinite(t)
            }

            function f(t) {
                return a(t) && "function" == typeof t.then && "function" == typeof t.catch
            }

            function p(t) {
                return null == t ? "" : Array.isArray(t) || s(t) && t.toString === yn ? JSON.stringify(t, null, 2) : String(t)
            }

            function h(t) {
                var e = parseFloat(t);
                return isNaN(e) ? t : e
            }

            function g(t, e) {
                for (var n = Object.create(null), i = t.split(","), a = 0; a < i.length; a++) n[i[a]] = !0;
                return e ? function (t) {
                    return n[t.toLowerCase()]
                } : function (t) {
                    return n[t]
                }
            }

            function v(t, e) {
                if (t.length) {
                    var n = t.indexOf(e);
                    if (n > -1) return t.splice(n, 1)
                }
            }

            function y(t, e) {
                return xn.call(t, e)
            }

            function m(t) {
                var e = Object.create(null);
                return function (n) {
                    return e[n] || (e[n] = t(n))
                }
            }

            function _(t, e) {
                e = e || 0;
                for (var n = t.length - e, i = new Array(n); n--;) i[n] = t[n + e];
                return i
            }

            function x(t, e) {
                for (var n in e) t[n] = e[n];
                return t
            }

            function b(t) {
                for (var e = {}, n = 0; n < t.length; n++) t[n] && x(e, t[n]);
                return e
            }

            function P(t, e, n) {
            }

            function w(t, e) {
                if (t === e) return !0;
                var n = l(t), i = l(e);
                if (!n || !i) return !n && !i && String(t) === String(e);
                try {
                    var a = Array.isArray(t), r = Array.isArray(e);
                    if (a && r) return t.length === e.length && t.every(function (t, n) {
                        return w(t, e[n])
                    });
                    if (t instanceof Date && e instanceof Date) return t.getTime() === e.getTime();
                    if (a || r) return !1;
                    var o = Object.keys(t), c = Object.keys(e);
                    return o.length === c.length && o.every(function (n) {
                        return w(t[n], e[n])
                    })
                } catch (t) {
                    return !1
                }
            }

            function S(t, e) {
                for (var n = 0; n < t.length; n++) if (w(t[n], e)) return n;
                return -1
            }

            function A(t) {
                var e = !1;
                return function () {
                    e || (e = !0, t.apply(this, arguments))
                }
            }

            function T(t) {
                var e = (t + "").charCodeAt(0);
                return 36 === e || 95 === e
            }

            function k(t, e, n, i) {
                Object.defineProperty(t, e, {value: n, enumerable: !!i, writable: !0, configurable: !0})
            }

            function M(t) {
                if (!$n.test(t)) {
                    var e = t.split(".");
                    return function (t) {
                        for (var n = 0; n < e.length; n++) {
                            if (!t) return;
                            t = t[e[n]]
                        }
                        return t
                    }
                }
            }

            function O(t) {
                return "function" == typeof t && /native code/.test(t.toString())
            }

            function C(t) {
                Qn.push(t), Jn.target = t
            }

            function L() {
                Qn.pop(), Jn.target = Qn[Qn.length - 1]
            }

            function D(t) {
                return new Kn(void 0, void 0, void 0, String(t))
            }

            function $(t) {
                var e = new Kn(t.tag, t.data, t.children && t.children.slice(), t.text, t.elm, t.context, t.componentOptions, t.asyncFactory);
                return e.ns = t.ns, e.isStatic = t.isStatic, e.key = t.key, e.isComment = t.isComment, e.fnContext = t.fnContext, e.fnOptions = t.fnOptions, e.fnScopeId = t.fnScopeId, e.asyncMeta = t.asyncMeta, e.isCloned = !0, e
            }

            function j(t) {
                ii = t
            }

            function F(t, e) {
                t.__proto__ = e
            }

            function R(t, e, n) {
                for (var i = 0, a = n.length; i < a; i++) {
                    var r = n[i];
                    k(t, r, e[r])
                }
            }

            function E(t, e) {
                var n;
                if (l(t) && !(t instanceof Kn)) return y(t, "__ob__") && t.__ob__ instanceof ai ? n = t.__ob__ : ii && !Un() && (Array.isArray(t) || s(t)) && Object.isExtensible(t) && !t._isVue && (n = new ai(t)), e && n && n.vmCount++, n
            }

            function I(t, e, n, i, a) {
                var r = new Jn, o = Object.getOwnPropertyDescriptor(t, e);
                if (!o || !1 !== o.configurable) {
                    var c = o && o.get, l = o && o.set;
                    c && !l || 2 !== arguments.length || (n = t[e]);
                    var s = !a && E(n);
                    Object.defineProperty(t, e, {
                        enumerable: !0, configurable: !0, get: function () {
                            var e = c ? c.call(t) : n;
                            return Jn.target && (r.depend(), s && (s.dep.depend(), Array.isArray(e) && N(e))), e
                        }, set: function (e) {
                            var i = c ? c.call(t) : n;
                            e === i || e !== e && i !== i || c && !l || (l ? l.call(t, e) : n = e, s = !a && E(e), r.notify())
                        }
                    })
                }
            }

            function z(t, e, n) {
                if (Array.isArray(t) && d(e)) return t.length = Math.max(t.length, e), t.splice(e, 1, n), n;
                if (e in t && !(e in Object.prototype)) return t[e] = n, n;
                var i = t.__ob__;
                return t._isVue || i && i.vmCount ? n : i ? (I(i.value, e, n), i.dep.notify(), n) : (t[e] = n, n)
            }

            function B(t, e) {
                if (Array.isArray(t) && d(e)) t.splice(e, 1); else {
                    var n = t.__ob__;
                    t._isVue || n && n.vmCount || y(t, e) && (delete t[e], n && n.dep.notify())
                }
            }

            function N(t) {
                for (var e = void 0, n = 0, i = t.length; n < i; n++) (e = t[n]) && e.__ob__ && e.__ob__.dep.depend(), Array.isArray(e) && N(e)
            }

            function W(t, e) {
                if (!e) return t;
                for (var n, i, a, r = qn ? Reflect.ownKeys(e) : Object.keys(e), o = 0; o < r.length; o++) "__ob__" !== (n = r[o]) && (i = t[n], a = e[n], y(t, n) ? i !== a && s(i) && s(a) && W(i, a) : z(t, n, a));
                return t
            }

            function H(t, e, n) {
                return n ? function () {
                    var i = "function" == typeof e ? e.call(n, n) : e, a = "function" == typeof t ? t.call(n, n) : t;
                    return i ? W(i, a) : a
                } : e ? t ? function () {
                    return W("function" == typeof e ? e.call(this, this) : e, "function" == typeof t ? t.call(this, this) : t)
                } : e : t
            }

            function U(t, e) {
                var n = e ? t ? t.concat(e) : Array.isArray(e) ? e : [e] : t;
                return n ? V(n) : n
            }

            function V(t) {
                for (var e = [], n = 0; n < t.length; n++) -1 === e.indexOf(t[n]) && e.push(t[n]);
                return e
            }

            function q(t, e, n, i) {
                var a = Object.create(t || null);
                return e ? x(a, e) : a
            }

            function G(t, e) {
                var n = t.props;
                if (n) {
                    var i, a, r, o = {};
                    if (Array.isArray(n)) for (i = n.length; i--;) "string" == typeof (a = n[i]) && (r = Pn(a), o[r] = {type: null}); else if (s(n)) for (var c in n) a = n[c], o[r = Pn(c)] = s(a) ? a : {type: a};
                    t.props = o
                }
            }

            function X(t, e) {
                var n = t.inject;
                if (n) {
                    var i = t.inject = {};
                    if (Array.isArray(n)) for (var a = 0; a < n.length; a++) i[n[a]] = {from: n[a]}; else if (s(n)) for (var r in n) {
                        var o = n[r];
                        i[r] = s(o) ? x({from: r}, o) : {from: o}
                    }
                }
            }

            function J(t) {
                var e = t.directives;
                if (e) for (var n in e) {
                    var i = e[n];
                    "function" == typeof i && (e[n] = {bind: i, update: i})
                }
            }

            function Q(t, e, n) {
                function i(i) {
                    var a = ri[i] || ci;
                    c[i] = a(t[i], e[i], n, i)
                }

                if ("function" == typeof e && (e = e.options), G(e, n), X(e, n), J(e), !e._base && (e.extends && (t = Q(t, e.extends, n)), e.mixins)) for (var a = 0, r = e.mixins.length; a < r; a++) t = Q(t, e.mixins[a], n);
                var o, c = {};
                for (o in t) i(o);
                for (o in e) y(t, o) || i(o);
                return c
            }

            function K(t, e, n, i) {
                if ("string" == typeof n) {
                    var a = t[e];
                    if (y(a, n)) return a[n];
                    var r = Pn(n);
                    if (y(a, r)) return a[r];
                    var o = wn(r);
                    return y(a, o) ? a[o] : a[n] || a[r] || a[o]
                }
            }

            function Y(t, e, n, i) {
                var a = e[t], r = !y(n, t), o = n[t], c = nt(Boolean, a.type);
                if (c > -1) if (r && !y(a, "default")) o = !1; else if ("" === o || o === An(t)) {
                    var l = nt(String, a.type);
                    (l < 0 || c < l) && (o = !0)
                }
                if (void 0 === o) {
                    o = Z(i, a, t);
                    var s = ii;
                    j(!0), E(o), j(s)
                }
                return o
            }

            function Z(t, e, n) {
                if (y(e, "default")) {
                    var i = e.default;
                    return t && t.$options.propsData && void 0 === t.$options.propsData[n] && void 0 !== t._props[n] ? t._props[n] : "function" == typeof i && "Function" !== tt(e.type) ? i.call(t) : i
                }
            }

            function tt(t) {
                var e = t && t.toString().match(/^\s*function (\w+)/);
                return e ? e[1] : ""
            }

            function et(t, e) {
                return tt(t) === tt(e)
            }

            function nt(t, e) {
                if (!Array.isArray(e)) return et(e, t) ? 0 : -1;
                for (var n = 0, i = e.length; n < i; n++) if (et(e[n], t)) return n;
                return -1
            }

            function it(t, e, n) {
                C();
                try {
                    if (e) for (var i = e; i = i.$parent;) {
                        var a = i.$options.errorCaptured;
                        if (a) for (var r = 0; r < a.length; r++) try {
                            if (!1 === a[r].call(i, t, e, n)) return
                        } catch (t) {
                            rt(t, i, "errorCaptured hook")
                        }
                    }
                    rt(t, e, n)
                } finally {
                    L()
                }
            }

            function at(t, e, n, i, a) {
                var r;
                try {
                    (r = n ? t.apply(e, n) : t.call(e)) && !r._isVue && f(r) && !r._handled && (r.catch(function (t) {
                        return it(t, i, a + " (Promise/async)")
                    }), r._handled = !0)
                } catch (t) {
                    it(t, i, a)
                }
                return r
            }

            function rt(t, e, n) {
                if (Ln.errorHandler) try {
                    return Ln.errorHandler.call(null, t, e, n)
                } catch (e) {
                    e !== t && ot(e, null, "config.errorHandler")
                }
                ot(t, e, n)
            }

            function ot(t, e, n) {
                if (!Fn && !Rn || "undefined" == typeof console) throw t;
                console.error(t)
            }

            function ct() {
                si = !1;
                var t = li.slice(0);
                li.length = 0;
                for (var e = 0; e < t.length; e++) t[e]()
            }

            function lt(t, e) {
                var n;
                if (li.push(function () {
                    if (t) try {
                        t.call(e)
                    } catch (t) {
                        it(t, e, "nextTick")
                    } else n && n(e)
                }), si || (si = !0, oi()), !t && "undefined" != typeof Promise) return new Promise(function (t) {
                    n = t
                })
            }

            function st(t) {
                ut(t, hi), hi.clear()
            }

            function ut(t, e) {
                var n, i, a = Array.isArray(t);
                if (!(!a && !l(t) || Object.isFrozen(t) || t instanceof Kn)) {
                    if (t.__ob__) {
                        var r = t.__ob__.dep.id;
                        if (e.has(r)) return;
                        e.add(r)
                    }
                    if (a) for (n = t.length; n--;) ut(t[n], e); else for (n = (i = Object.keys(t)).length; n--;) ut(t[i[n]], e)
                }
            }

            function dt(t, e) {
                function n() {
                    var t = arguments, i = n.fns;
                    if (!Array.isArray(i)) return at(i, null, arguments, e, "v-on handler");
                    for (var a = i.slice(), r = 0; r < a.length; r++) at(a[r], null, t, e, "v-on handler")
                }

                return n.fns = t, n
            }

            function ft(t, e, n, a, o, c) {
                var l, s, u, d;
                for (l in t) s = t[l], u = e[l], d = gi(l), i(s) || (i(u) ? (i(s.fns) && (s = t[l] = dt(s, c)), r(d.once) && (s = t[l] = o(d.name, s, d.capture)), n(d.name, s, d.capture, d.passive, d.params)) : s !== u && (u.fns = s, t[l] = u));
                for (l in e) i(t[l]) && (d = gi(l), a(d.name, e[l], d.capture))
            }

            function pt(t, e, n) {
                var r = e.options.props;
                if (!i(r)) {
                    var o = {}, c = t.attrs, l = t.props;
                    if (a(c) || a(l)) for (var s in r) {
                        var u = An(s);
                        ht(o, l, s, u, !0) || ht(o, c, s, u, !1)
                    }
                    return o
                }
            }

            function ht(t, e, n, i, r) {
                if (a(e)) {
                    if (y(e, n)) return t[n] = e[n], r || delete e[n], !0;
                    if (y(e, i)) return t[n] = e[i], r || delete e[i], !0
                }
                return !1
            }

            function gt(t) {
                for (var e = 0; e < t.length; e++) if (Array.isArray(t[e])) return Array.prototype.concat.apply([], t);
                return t
            }

            function vt(t) {
                return c(t) ? [D(t)] : Array.isArray(t) ? mt(t) : void 0
            }

            function yt(t) {
                return a(t) && a(t.text) && o(t.isComment)
            }

            function mt(t, e) {
                var n, o, l, s, u = [];
                for (n = 0; n < t.length; n++) i(o = t[n]) || "boolean" == typeof o || (l = u.length - 1, s = u[l], Array.isArray(o) ? o.length > 0 && (o = mt(o, (e || "") + "_" + n), yt(o[0]) && yt(s) && (u[l] = D(s.text + o[0].text), o.shift()), u.push.apply(u, o)) : c(o) ? yt(s) ? u[l] = D(s.text + o) : "" !== o && u.push(D(o)) : yt(o) && yt(s) ? u[l] = D(s.text + o.text) : (r(t._isVList) && a(o.tag) && i(o.key) && a(e) && (o.key = "__vlist" + e + "_" + n + "__"), u.push(o)));
                return u
            }

            function _t(t) {
                var e = t.$options.provide;
                e && (t._provided = "function" == typeof e ? e.call(t) : e)
            }

            function xt(t) {
                var e = bt(t.$options.inject, t);
                e && (j(!1), Object.keys(e).forEach(function (n) {
                    I(t, n, e[n])
                }), j(!0))
            }

            function bt(t, e) {
                if (t) {
                    for (var n = Object.create(null), i = qn ? Reflect.ownKeys(t) : Object.keys(t), a = 0; a < i.length; a++) {
                        var r = i[a];
                        if ("__ob__" !== r) {
                            for (var o = t[r].from, c = e; c;) {
                                if (c._provided && y(c._provided, o)) {
                                    n[r] = c._provided[o];
                                    break
                                }
                                c = c.$parent
                            }
                            if (!c && "default" in t[r]) {
                                var l = t[r].default;
                                n[r] = "function" == typeof l ? l.call(e) : l
                            }
                        }
                    }
                    return n
                }
            }

            function Pt(t, e) {
                if (!t || !t.length) return {};
                for (var n = {}, i = 0, a = t.length; i < a; i++) {
                    var r = t[i], o = r.data;
                    if (o && o.attrs && o.attrs.slot && delete o.attrs.slot, r.context !== e && r.fnContext !== e || !o || null == o.slot) r.asyncMeta && r.asyncMeta.data && "page" === r.asyncMeta.data.slot ? (n.page || (n.page = [])).push(r) : (n.default || (n.default = [])).push(r); else {
                        var c = o.slot, l = n[c] || (n[c] = []);
                        "template" === r.tag ? l.push.apply(l, r.children || []) : l.push(r)
                    }
                }
                for (var s in n) n[s].every(wt) && delete n[s];
                return n
            }

            function wt(t) {
                return t.isComment && !t.asyncFactory || " " === t.text
            }

            function St(t, e, n) {
                var i, a = Object.keys(e).length > 0, r = t ? !!t.$stable : !a, o = t && t.$key;
                if (t) {
                    if (t._normalized) return t._normalized;
                    if (r && n && n !== vn && o === n.$key && !a && !n.$hasNormal) return n;
                    for (var c in i = {}, t) t[c] && "$" !== c[0] && (i[c] = At(e, c, t[c]))
                } else i = {};
                for (var l in e) l in i || (i[l] = Tt(e, l));
                return t && Object.isExtensible(t) && (t._normalized = i), k(i, "$stable", r), k(i, "$key", o), k(i, "$hasNormal", a), i
            }

            function At(e, n, i) {
                var a = function () {
                    var e = arguments.length ? i.apply(null, arguments) : i({});
                    return (e = e && "object" === (void 0 === e ? "undefined" : t(e)) && !Array.isArray(e) ? [e] : vt(e)) && (0 === e.length || 1 === e.length && e[0].isComment) ? void 0 : e
                };
                return i.proxy && Object.defineProperty(e, n, {get: a, enumerable: !0, configurable: !0}), a
            }

            function Tt(t, e) {
                return function () {
                    return t[e]
                }
            }

            function kt(t, e) {
                var n, i, r, o, c;
                if (Array.isArray(t) || "string" == typeof t) for (n = new Array(t.length), i = 0, r = t.length; i < r; i++) n[i] = e(t[i], i); else if ("number" == typeof t) for (n = new Array(t), i = 0; i < t; i++) n[i] = e(i + 1, i); else if (l(t)) if (qn && t[Symbol.iterator]) {
                    n = [];
                    for (var s = t[Symbol.iterator](), u = s.next(); !u.done;) n.push(e(u.value, n.length)), u = s.next()
                } else for (o = Object.keys(t), n = new Array(o.length), i = 0, r = o.length; i < r; i++) c = o[i], n[i] = e(t[c], c, i);
                return a(n) || (n = []), n._isVList = !0, n
            }

            function Mt(t, e, n, i) {
                var a, r = this.$scopedSlots[t];
                r ? (n = n || {}, i && (n = x(x({}, i), n)), a = r(n) || e) : a = this.$slots[t] || e;
                var o = n && n.slot;
                return o ? this.$createElement("template", {slot: o}, a) : a
            }

            function Ot(t) {
                return K(this.$options, "filters", t, !0) || Mn
            }

            function Ct(t, e) {
                return Array.isArray(t) ? -1 === t.indexOf(e) : t !== e
            }

            function Lt(t, e, n, i, a) {
                var r = Ln.keyCodes[e] || n;
                return a && i && !Ln.keyCodes[e] ? Ct(a, i) : r ? Ct(r, t) : i ? An(i) !== e : void 0
            }

            function Dt(t, e, n, i, a) {
                if (n && l(n)) {
                    var r;
                    Array.isArray(n) && (n = b(n));
                    for (var o in n) !function (o) {
                        if ("class" === o || "style" === o || _n(o)) r = t; else {
                            var c = t.attrs && t.attrs.type;
                            r = i || Ln.mustUseProp(e, c, o) ? t.domProps || (t.domProps = {}) : t.attrs || (t.attrs = {})
                        }
                        var l = Pn(o), s = An(o);
                        l in r || s in r || (r[o] = n[o], !a) || ((t.on || (t.on = {}))["update:" + o] = function (t) {
                            n[o] = t
                        })
                    }(o)
                }
                return t
            }

            function $t(t, e) {
                var n = this._staticTrees || (this._staticTrees = []), i = n[t];
                return i && !e ? i : (i = n[t] = this.$options.staticRenderFns[t].call(this._renderProxy, null, this), Ft(i, "__static__" + t, !1), i)
            }

            function jt(t, e, n) {
                return Ft(t, "__once__" + e + (n ? "_" + n : ""), !0), t
            }

            function Ft(t, e, n) {
                if (Array.isArray(t)) for (var i = 0; i < t.length; i++) t[i] && "string" != typeof t[i] && Rt(t[i], e + "_" + i, n); else Rt(t, e, n)
            }

            function Rt(t, e, n) {
                t.isStatic = !0, t.key = e, t.isOnce = n
            }

            function Et(t, e) {
                if (e && s(e)) {
                    var n = t.on = t.on ? x({}, t.on) : {};
                    for (var i in e) {
                        var a = n[i], r = e[i];
                        n[i] = a ? [].concat(a, r) : r
                    }
                }
                return t
            }

            function It(t, e, n, i) {
                e = e || {$stable: !n};
                for (var a = 0; a < t.length; a++) {
                    var r = t[a];
                    Array.isArray(r) ? It(r, e, n) : r && (r.proxy && (r.fn.proxy = !0), e[r.key] = r.fn)
                }
                return i && (e.$key = i), e
            }

            function zt(t, e) {
                for (var n = 0; n < e.length; n += 2) {
                    var i = e[n];
                    "string" == typeof i && i && (t[e[n]] = e[n + 1])
                }
                return t
            }

            function Bt(t, e) {
                return "string" == typeof t ? e + t : t
            }

            function Nt(t) {
                t._o = jt, t._n = h, t._s = p, t._l = kt, t._t = Mt, t._q = w, t._i = S, t._m = $t, t._f = Ot, t._k = Lt, t._b = Dt, t._v = D, t._e = Zn, t._u = It, t._g = Et, t._d = zt, t._p = Bt
            }

            function Wt(t, e, n, i, a) {
                var o, c = this, l = a.options;
                y(i, "_uid") ? (o = Object.create(i), o._original = i) : (o = i, i = i._original);
                var s = r(l._compiled), u = !s;
                this.data = t, this.props = e, this.children = n, this.parent = i, this.listeners = t.on || vn, this.injections = bt(l.inject, i), this.slots = function () {
                    return c.$slots || St(t.scopedSlots, c.$slots = Pt(n, i)), c.$slots
                }, Object.defineProperty(this, "scopedSlots", {
                    enumerable: !0, get: function () {
                        return St(t.scopedSlots, this.slots())
                    }
                }), s && (this.$options = l, this.$slots = this.slots(), this.$scopedSlots = St(t.scopedSlots, this.$slots)), l._scopeId ? this._c = function (t, e, n, a) {
                    var r = Kt(o, t, e, n, a, u);
                    return r && !Array.isArray(r) && (r.fnScopeId = l._scopeId, r.fnContext = i), r
                } : this._c = function (t, e, n, i) {
                    return Kt(o, t, e, n, i, u)
                }
            }

            function Ht(t, e, n, i, r) {
                var o = t.options, c = {}, l = o.props;
                if (a(l)) for (var s in l) c[s] = Y(s, l, e || vn); else a(n.attrs) && Vt(c, n.attrs), a(n.props) && Vt(c, n.props);
                var u = new Wt(n, c, r, i, t), d = o.render.call(null, u._c, u);
                if (d instanceof Kn) return Ut(d, n, u.parent, o, u);
                if (Array.isArray(d)) {
                    for (var f = vt(d) || [], p = new Array(f.length), h = 0; h < f.length; h++) p[h] = Ut(f[h], n, u.parent, o, u);
                    return p
                }
            }

            function Ut(t, e, n, i, a) {
                var r = $(t);
                return r.fnContext = n, r.fnOptions = i, e.slot && ((r.data || (r.data = {})).slot = e.slot), r
            }

            function Vt(t, e) {
                for (var n in e) t[Pn(n)] = e[n]
            }

            function qt(t, e, n, o, c) {
                if (!i(t)) {
                    var s = n.$options._base;
                    if (l(t) && (t = s.extend(t)), "function" == typeof t) {
                        var u;
                        if (i(t.cid) && (u = t, void 0 === (t = ae(u, s)))) return ie(u, e, n, o, c);
                        e = e || {}, Ie(t), a(e.model) && Qt(t.options, e);
                        var d = pt(e, t, c);
                        if (r(t.options.functional)) return Ht(t, d, e, n, o);
                        var f = e.on;
                        if (e.on = e.nativeOn, r(t.options.abstract)) {
                            var p = e.slot;
                            e = {}, p && (e.slot = p)
                        }
                        Xt(e);
                        var h = t.options.name || c;
                        return new Kn("vue-component-" + t.cid + (h ? "-" + h : ""), e, void 0, void 0, void 0, n, {
                            Ctor: t,
                            propsData: d,
                            listeners: f,
                            tag: c,
                            children: o
                        }, u)
                    }
                }
            }

            function Gt(t, e) {
                var n = {_isComponent: !0, _parentVnode: t, parent: e}, i = t.data.inlineTemplate;
                return a(i) && (n.render = i.render, n.staticRenderFns = i.staticRenderFns), new t.componentOptions.Ctor(n)
            }

            function Xt(t) {
                for (var e = t.hook || (t.hook = {}), n = 0; n < mi.length; n++) {
                    var i = mi[n], a = e[i], r = yi[i];
                    a === r || a && a._merged || (e[i] = a ? Jt(r, a) : r)
                }
            }

            function Jt(t, e) {
                var n = function (n, i) {
                    t(n, i), e(n, i)
                };
                return n._merged = !0, n
            }

            function Qt(t, e) {
                var n = t.model && t.model.prop || "value", i = t.model && t.model.event || "input";
                (e.attrs || (e.attrs = {}))[n] = e.model.value;
                var r = e.on || (e.on = {}), o = r[i], c = e.model.callback;
                a(o) ? (Array.isArray(o) ? -1 === o.indexOf(c) : o !== c) && (r[i] = [c].concat(o)) : r[i] = c
            }

            function Kt(t, e, n, i, a, o) {
                return (Array.isArray(n) || c(n)) && (a = i, i = n, n = void 0), r(o) && (a = xi), Yt(t, e, n, i, a)
            }

            function Yt(t, e, n, i, r) {
                if (a(n) && a(n.__ob__)) return Zn();
                if (a(n) && a(n.is) && (e = n.is), !e) return Zn();
                var o, c, l;
                return Array.isArray(i) && "function" == typeof i[0] && (n = n || {}, n.scopedSlots = {default: i[0]}, i.length = 0), r === xi ? i = vt(i) : r === _i && (i = gt(i)), "string" == typeof e ? (c = t.$vnode && t.$vnode.ns || Ln.getTagNamespace(e), o = Ln.isReservedTag(e) ? new Kn(Ln.parsePlatformTagName(e), n, i, void 0, void 0, t) : n && n.pre || !a(l = K(t.$options, "components", e)) ? new Kn(e, n, i, void 0, void 0, t) : qt(l, n, t, i, e)) : o = qt(e, n, t, i), Array.isArray(o) ? o : a(o) ? (a(c) && Zt(o, c), a(n) && te(n), o) : Zn()
            }

            function Zt(t, e, n) {
                if (t.ns = e, "foreignObject" === t.tag && (e = void 0, n = !0), a(t.children)) for (var o = 0, c = t.children.length; o < c; o++) {
                    var l = t.children[o];
                    a(l.tag) && (i(l.ns) || r(n) && "svg" !== l.tag) && Zt(l, e, n)
                }
            }

            function te(t) {
                l(t.style) && st(t.style), l(t.class) && st(t.class)
            }

            function ee(t) {
                t._vnode = null, t._staticTrees = null;
                var e = t.$options, n = t.$vnode = e._parentVnode, i = n && n.context;
                t.$slots = Pt(e._renderChildren, i), t.$scopedSlots = vn, t._c = function (e, n, i, a) {
                    return Kt(t, e, n, i, a, !1)
                }, t.$createElement = function (e, n, i, a) {
                    return Kt(t, e, n, i, a, !0)
                };
                var a = n && n.data;
                I(t, "$attrs", a && a.attrs || vn, null, !0), I(t, "$listeners", e._parentListeners || vn, null, !0)
            }

            function ne(t, e) {
                return (t.__esModule || qn && "Module" === t[Symbol.toStringTag]) && (t = t.default), l(t) ? e.extend(t) : t
            }

            function ie(t, e, n, i, a) {
                var r = Zn();
                return r.asyncFactory = t, r.asyncMeta = {data: e, context: n, children: i, tag: a}, r
            }

            function ae(t, e) {
                if (r(t.error) && a(t.errorComp)) return t.errorComp;
                if (a(t.resolved)) return t.resolved;
                var n = bi;
                if (n && a(t.owners) && -1 === t.owners.indexOf(n) && t.owners.push(n), r(t.loading) && a(t.loadingComp)) return t.loadingComp;
                if (n && !a(t.owners)) {
                    var o = t.owners = [n], c = !0, s = null, u = null;
                    n.$on("hook:destroyed", function () {
                        return v(o, n)
                    });
                    var d = function (t) {
                        for (var e = 0, n = o.length; e < n; e++) o[e].$forceUpdate();
                        t && (o.length = 0, null !== s && (clearTimeout(s), s = null), null !== u && (clearTimeout(u), u = null))
                    }, p = A(function (n) {
                        t.resolved = ne(n, e), c ? o.length = 0 : d(!0)
                    }), h = A(function (e) {
                        a(t.errorComp) && (t.error = !0, d(!0))
                    }), g = t(p, h);
                    return l(g) && (f(g) ? i(t.resolved) && g.then(p, h) : f(g.component) && (g.component.then(p, h), a(g.error) && (t.errorComp = ne(g.error, e)), a(g.loading) && (t.loadingComp = ne(g.loading, e), 0 === g.delay ? t.loading = !0 : s = setTimeout(function () {
                        s = null, i(t.resolved) && i(t.error) && (t.loading = !0, d(!1))
                    }, g.delay || 200)), a(g.timeout) && (u = setTimeout(function () {
                        u = null, i(t.resolved) && h(null)
                    }, g.timeout)))), c = !1, t.loading ? t.loadingComp : t.resolved
                }
            }

            function re(t) {
                return t.isComment && t.asyncFactory
            }

            function oe(t) {
                if (Array.isArray(t)) for (var e = 0; e < t.length; e++) {
                    var n = t[e];
                    if (a(n) && (a(n.componentOptions) || re(n))) return n
                }
            }

            function ce(t) {
                t._events = Object.create(null), t._hasHookEvent = !1;
                var e = t.$options._parentListeners;
                e && de(t, e)
            }

            function le(t, e) {
                vi.$on(t, e)
            }

            function se(t, e) {
                vi.$off(t, e)
            }

            function ue(t, e) {
                var n = vi;
                return function i() {
                    null !== e.apply(null, arguments) && n.$off(t, i)
                }
            }

            function de(t, e, n) {
                vi = t, ft(e, n || {}, le, se, ue, t), vi = void 0
            }

            function fe(t) {
                var e = Pi;
                return Pi = t, function () {
                    Pi = e
                }
            }

            function pe(t) {
                var e = t.$options, n = e.parent;
                if (n && !e.abstract) {
                    for (; n.$options.abstract && n.$parent;) n = n.$parent;
                    n.$children.push(t)
                }
                t.$parent = n, t.$root = n ? n.$root : t, t.$children = [], t.$refs = {}, t._watcher = null, t._inactive = null, t._directInactive = !1, t._isMounted = !1, t._isDestroyed = !1, t._isBeingDestroyed = !1
            }

            function he(t, e, n, i, a) {
                var r = i.data.scopedSlots, o = t.$scopedSlots,
                    c = !!(r && !r.$stable || o !== vn && !o.$stable || r && t.$scopedSlots.$key !== r.$key),
                    l = !!(a || t.$options._renderChildren || c);
                if (t.$options._parentVnode = i, t.$vnode = i, t._vnode && (t._vnode.parent = i), t.$options._renderChildren = a, t.$attrs = i.data.attrs || vn, t.$listeners = n || vn, e && t.$options.props) {
                    j(!1);
                    for (var s = t._props, u = t.$options._propKeys || [], d = 0; d < u.length; d++) {
                        var f = u[d], p = t.$options.props;
                        s[f] = Y(f, p, e, t)
                    }
                    j(!0), t.$options.propsData = e
                }
                n = n || vn;
                var h = t.$options._parentListeners;
                t.$options._parentListeners = n, de(t, n, h), l && (t.$slots = Pt(a, i.context), t.$forceUpdate())
            }

            function ge(t) {
                for (; t && (t = t.$parent);) if (t._inactive) return !0;
                return !1
            }

            function ve(t, e) {
                if (e) {
                    if (t._directInactive = !1, ge(t)) return
                } else if (t._directInactive) return;
                if (t._inactive || null === t._inactive) {
                    t._inactive = !1;
                    for (var n = 0; n < t.$children.length; n++) ve(t.$children[n]);
                    me(t, "activated")
                }
            }

            function ye(t, e) {
                if (!(e && (t._directInactive = !0, ge(t)) || t._inactive)) {
                    t._inactive = !0;
                    for (var n = 0; n < t.$children.length; n++) ye(t.$children[n]);
                    me(t, "deactivated")
                }
            }

            function me(t, e) {
                C();
                var n = t.$options[e], i = e + " hook";
                if (n) for (var a = 0, r = n.length; a < r; a++) at(n[a], t, null, t, i);
                t._hasHookEvent && t.$emit("hook:" + e), L()
            }

            function _e() {
                Mi = wi.length = Si.length = 0, Ai = {}, Ti = ki = !1
            }

            function xe() {
                var t, e;
                for (Oi(), ki = !0, wi.sort(function (t, e) {
                    return t.id - e.id
                }), Mi = 0; Mi < wi.length; Mi++) (t = wi[Mi]).before && t.before(), e = t.id, Ai[e] = null, t.run();
                var n = Si.slice(), i = wi.slice();
                _e(), we(n), be(i), Vn && Ln.devtools && Vn.emit("flush")
            }

            function be(t) {
                for (var e = t.length; e--;) {
                    var n = t[e], i = n.vm;
                    i._watcher === n && i._isMounted && !i._isDestroyed && me(i, "updated")
                }
            }

            function Pe(t) {
                t._inactive = !1, Si.push(t)
            }

            function we(t) {
                for (var e = 0; e < t.length; e++) t[e]._inactive = !0, ve(t[e], !0)
            }

            function Se(t) {
                var e = t.id;
                if (null == Ai[e]) {
                    if (Ai[e] = !0, ki) {
                        for (var n = wi.length - 1; n > Mi && wi[n].id > t.id;) n--;
                        wi.splice(n + 1, 0, t)
                    } else wi.push(t);
                    Ti || (Ti = !0, lt(xe))
                }
            }

            function Ae(t, e, n) {
                $i.get = function () {
                    return this[e][n]
                }, $i.set = function (t) {
                    this[e][n] = t
                }, Object.defineProperty(t, n, $i)
            }

            function Te(t) {
                t._watchers = [];
                var e = t.$options;
                e.props && ke(t, e.props), e.methods && je(t, e.methods), e.data ? Me(t) : E(t._data = {}, !0), e.computed && Ce(t, e.computed), e.watch && e.watch !== Nn && Fe(t, e.watch)
            }

            function ke(t, e) {
                var n = t.$options.propsData || {}, i = t._props = {}, a = t.$options._propKeys = [];
                !t.$parent || j(!1);
                for (var r in e) !function (r) {
                    a.push(r);
                    var o = Y(r, e, n, t);
                    I(i, r, o), r in t || Ae(t, "_props", r)
                }(r);
                j(!0)
            }

            function Me(t) {
                var e = t.$options.data;
                s(e = t._data = "function" == typeof e ? Oe(e, t) : e || {}) || (e = {});
                for (var n = Object.keys(e), i = t.$options.props, a = (t.$options.methods, n.length); a--;) {
                    var r = n[a];
                    i && y(i, r) || T(r) || Ae(t, "_data", r)
                }
                E(e, !0)
            }

            function Oe(t, e) {
                C();
                try {
                    return t.call(e, e)
                } catch (t) {
                    return it(t, e, "data()"), {}
                } finally {
                    L()
                }
            }

            function Ce(t, e) {
                var n = t._computedWatchers = Object.create(null), i = Un();
                for (var a in e) {
                    var r = e[a], o = "function" == typeof r ? r : r.get;
                    i || (n[a] = new Di(t, o || P, P, ji)), a in t || Le(t, a, r)
                }
            }

            function Le(t, e, n) {
                var i = !Un();
                "function" == typeof n ? ($i.get = i ? De(e) : $e(n), $i.set = P) : ($i.get = n.get ? i && !1 !== n.cache ? De(e) : $e(n.get) : P, $i.set = n.set || P), Object.defineProperty(t, e, $i)
            }

            function De(t) {
                return function () {
                    var e = this._computedWatchers && this._computedWatchers[t];
                    if (e) return e.dirty && e.evaluate(), Jn.target && e.depend(), e.value
                }
            }

            function $e(t) {
                return function () {
                    return t.call(this, this)
                }
            }

            function je(t, e) {
                t.$options.props;
                for (var n in e) t[n] = "function" != typeof e[n] ? P : Tn(e[n], t)
            }

            function Fe(t, e) {
                for (var n in e) {
                    var i = e[n];
                    if (Array.isArray(i)) for (var a = 0; a < i.length; a++) Re(t, n, i[a]); else Re(t, n, i)
                }
            }

            function Re(t, e, n, i) {
                return s(n) && (i = n, n = n.handler), "string" == typeof n && (n = t[n]), t.$watch(e, n, i)
            }

            function Ee(t, e) {
                var n = t.$options = Object.create(t.constructor.options), i = e._parentVnode;
                n.parent = e.parent, n._parentVnode = i;
                var a = i.componentOptions;
                n.propsData = a.propsData, n._parentListeners = a.listeners, n._renderChildren = a.children, n._componentTag = a.tag, e.render && (n.render = e.render, n.staticRenderFns = e.staticRenderFns)
            }

            function Ie(t) {
                var e = t.options;
                if (t.super) {
                    var n = Ie(t.super);
                    if (n !== t.superOptions) {
                        t.superOptions = n;
                        var i = ze(t);
                        i && x(t.extendOptions, i), (e = t.options = Q(n, t.extendOptions)).name && (e.components[e.name] = t)
                    }
                }
                return e
            }

            function ze(t) {
                var e, n = t.options, i = t.sealedOptions;
                for (var a in n) n[a] !== i[a] && (e || (e = {}), e[a] = n[a]);
                return e
            }

            function Be(t) {
                this._init(t)
            }

            function Ne(t) {
                t.use = function (t) {
                    var e = this._installedPlugins || (this._installedPlugins = []);
                    if (e.indexOf(t) > -1) return this;
                    var n = _(arguments, 1);
                    return n.unshift(this), "function" == typeof t.install ? t.install.apply(t, n) : "function" == typeof t && t.apply(null, n), e.push(t), this
                }
            }

            function We(t) {
                t.mixin = function (t) {
                    return this.options = Q(this.options, t), this
                }
            }

            function He(t) {
                t.cid = 0;
                var e = 1;
                t.extend = function (t) {
                    t = t || {};
                    var n = this, i = n.cid, a = t._Ctor || (t._Ctor = {});
                    if (a[i]) return a[i];
                    var r = t.name || n.options.name, o = function (t) {
                        this._init(t)
                    };
                    return o.prototype = Object.create(n.prototype), o.prototype.constructor = o, o.cid = e++, o.options = Q(n.options, t), o.super = n, o.options.props && Ue(o), o.options.computed && Ve(o), o.extend = n.extend, o.mixin = n.mixin, o.use = n.use, On.forEach(function (t) {
                        o[t] = n[t]
                    }), r && (o.options.components[r] = o), o.superOptions = n.options, o.extendOptions = t, o.sealedOptions = x({}, o.options), a[i] = o, o
                }
            }

            function Ue(t) {
                var e = t.options.props;
                for (var n in e) Ae(t.prototype, "_props", n)
            }

            function Ve(t) {
                var e = t.options.computed;
                for (var n in e) Le(t.prototype, n, e[n])
            }

            function qe(t) {
                On.forEach(function (e) {
                    t[e] = function (t, n) {
                        return n ? ("component" === e && s(n) && (n.name = n.name || t, n = this.options._base.extend(n)), "directive" === e && "function" == typeof n && (n = {
                            bind: n,
                            update: n
                        }), this.options[e + "s"][t] = n, n) : this.options[e + "s"][t]
                    }
                })
            }

            function Ge(t) {
                return t && (t.Ctor.options.name || t.tag)
            }

            function Xe(t, e) {
                return Array.isArray(t) ? t.indexOf(e) > -1 : "string" == typeof t ? t.split(",").indexOf(e) > -1 : !!u(t) && t.test(e)
            }

            function Je(t, e) {
                var n = t.cache, i = t.keys, a = t._vnode;
                for (var r in n) {
                    var o = n[r];
                    if (o) {
                        var c = Ge(o.componentOptions);
                        c && !e(c) && Qe(n, r, i, a)
                    }
                }
            }

            function Qe(t, e, n, i) {
                var a = t[e];
                !a || i && a.tag === i.tag || a.componentInstance.$destroy(), t[e] = null, v(n, e)
            }

            function Ke(t, e) {
                var n = {};
                return Ye(t, e), Ze(t, e, "", n), n
            }

            function Ye(t, e) {
                if (t !== e) {
                    var n = en(t), i = en(e);
                    if (n == zi && i == zi) {
                        if (Object.keys(t).length >= Object.keys(e).length) for (var a in e) {
                            var r = t[a];
                            void 0 === r ? t[a] = null : Ye(r, e[a])
                        }
                    } else n == Ii && i == Ii && t.length >= e.length && e.forEach(function (e, n) {
                        Ye(t[n], e)
                    })
                }
            }

            function Ze(t, e, n, i) {
                if (t !== e) {
                    var a = en(t), r = en(e);
                    if (a == zi) if (r != zi || Object.keys(t).length < Object.keys(e).length) tn(i, n, t); else {
                        for (var o in t) !function (a) {
                            var r = t[a], o = e[a], c = en(r), l = en(o);
                            if (c != Ii && c != zi) r != e[a] && tn(i, ("" == n ? "" : n + ".") + a, r); else if (c == Ii) l != Ii ? tn(i, ("" == n ? "" : n + ".") + a, r) : r.length < o.length ? tn(i, ("" == n ? "" : n + ".") + a, r) : r.forEach(function (t, e) {
                                Ze(t, o[e], ("" == n ? "" : n + ".") + a + "[" + e + "]", i)
                            }); else if (c == zi) if (l != zi || Object.keys(r).length < Object.keys(o).length) tn(i, ("" == n ? "" : n + ".") + a, r); else for (var s in r) Ze(r[s], o[s], ("" == n ? "" : n + ".") + a + "." + s, i)
                        }(o)
                    } else a == Ii ? r != Ii ? tn(i, n, t) : t.length < e.length ? tn(i, n, t) : t.forEach(function (t, a) {
                        Ze(t, e[a], n + "[" + a + "]", i)
                    }) : tn(i, n, t)
                }
            }

            function tn(t, e, n) {
                t[e] = n
            }

            function en(t) {
                return Object.prototype.toString.call(t)
            }

            function nn(t) {
                if (t.__next_tick_callbacks && t.__next_tick_callbacks.length) {
                    if (Object({NODE_ENV: "production", VUE_APP_PLATFORM: "mp-weixin", BASE_URL: "/"}).VUE_APP_DEBUG) {
                        var e = t.$scope;
                        console.log("[" + +new Date + "][" + (e.is || e.route) + "][" + t._uid + "]:flushCallbacks[" + t.__next_tick_callbacks.length + "]")
                    }
                    var n = t.__next_tick_callbacks.slice(0);
                    t.__next_tick_callbacks.length = 0;
                    for (var i = 0; i < n.length; i++) n[i]()
                }
            }

            function an(t) {
                return wi.find(function (e) {
                    return t._watcher === e
                })
            }

            function rn(t, e) {
                if (!t.__next_tick_pending && !an(t)) {
                    if (Object({NODE_ENV: "production", VUE_APP_PLATFORM: "mp-weixin", BASE_URL: "/"}).VUE_APP_DEBUG) {
                        var n = t.$scope;
                        console.log("[" + +new Date + "][" + (n.is || n.route) + "][" + t._uid + "]:nextVueTick")
                    }
                    return lt(e, t)
                }
                if (Object({NODE_ENV: "production", VUE_APP_PLATFORM: "mp-weixin", BASE_URL: "/"}).VUE_APP_DEBUG) {
                    var i = t.$scope;
                    console.log("[" + +new Date + "][" + (i.is || i.route) + "][" + t._uid + "]:nextMPTick")
                }
                var a;
                if (t.__next_tick_callbacks || (t.__next_tick_callbacks = []), t.__next_tick_callbacks.push(function () {
                    if (e) try {
                        e.call(t)
                    } catch (e) {
                        it(e, t, "nextTick")
                    } else a && a(t)
                }), !e && "undefined" != typeof Promise) return new Promise(function (t) {
                    a = t
                })
            }

            function on(t) {
                var e = Object.create(null);
                return [].concat(Object.keys(t._data || {}), Object.keys(t._computedWatchers || {})).reduce(function (e, n) {
                    return e[n] = t[n], e
                }, e), Object.assign(e, t.$mp.data || {}), Array.isArray(t.$options.behaviors) && -1 !== t.$options.behaviors.indexOf("uni://form-field") && (e.name = t.name, e.value = t.value), JSON.parse(JSON.stringify(e))
            }

            function cn() {
            }

            function ln(t, e, n) {
                if (!t.mpType) return t;
                "app" === t.mpType && (t.$options.render = cn), t.$options.render || (t.$options.render = cn), "mp-toutiao" !== t.mpHost && me(t, "beforeMount");
                return new Di(t, function () {
                    t._update(t._render(), n)
                }, P, {
                    before: function () {
                        t._isMounted && !t._isDestroyed && me(t, "beforeUpdate")
                    }
                }, !0), n = !1, t
            }

            function sn(t, e) {
                return a(t) || a(e) ? un(t, dn(e)) : ""
            }

            function un(t, e) {
                return t ? e ? t + " " + e : t : e || ""
            }

            function dn(t) {
                return Array.isArray(t) ? fn(t) : l(t) ? pn(t) : "string" == typeof t ? t : ""
            }

            function fn(t) {
                for (var e, n = "", i = 0, r = t.length; i < r; i++) a(e = dn(t[i])) && "" !== e && (n && (n += " "), n += e);
                return n
            }

            function pn(t) {
                var e = "";
                for (var n in t) t[n] && (e && (e += " "), e += n);
                return e
            }

            function hn(t) {
                return Array.isArray(t) ? b(t) : "string" == typeof t ? Bi(t) : t
            }

            function gn(t, e) {
                var n = e.split("."), i = n[0];
                return 0 === i.indexOf("__$n") && (i = parseInt(i.replace("__$n", ""))), 1 === n.length ? t[i] : gn(t[i], n.slice(1).join("."))
            }

            var vn = Object.freeze({}), yn = Object.prototype.toString;
            g("slot,component", !0);
            var mn, _n = g("key,ref,slot,slot-scope,is"), xn = Object.prototype.hasOwnProperty, bn = /-(\w)/g,
                Pn = m(function (t) {
                    return t.replace(bn, function (t, e) {
                        return e ? e.toUpperCase() : ""
                    })
                }), wn = m(function (t) {
                    return t.charAt(0).toUpperCase() + t.slice(1)
                }), Sn = /\B([A-Z])/g, An = m(function (t) {
                    return t.replace(Sn, "-$1").toLowerCase()
                }), Tn = Function.prototype.bind ? function (t, e) {
                    return t.bind(e)
                } : function (t, e) {
                    function n(n) {
                        var i = arguments.length;
                        return i ? i > 1 ? t.apply(e, arguments) : t.call(e, n) : t.call(e)
                    }

                    return n._length = t.length, n
                }, kn = function (t, e, n) {
                    return !1
                }, Mn = function (t) {
                    return t
                }, On = ["component", "directive", "filter"],
                Cn = ["beforeCreate", "created", "beforeMount", "mounted", "beforeUpdate", "updated", "beforeDestroy", "destroyed", "activated", "deactivated", "errorCaptured", "serverPrefetch"],
                Ln = {
                    optionMergeStrategies: Object.create(null),
                    silent: !1,
                    productionTip: !1,
                    devtools: !1,
                    performance: !1,
                    errorHandler: null,
                    warnHandler: null,
                    ignoredElements: [],
                    keyCodes: Object.create(null),
                    isReservedTag: kn,
                    isReservedAttr: kn,
                    isUnknownElement: kn,
                    getTagNamespace: P,
                    parsePlatformTagName: Mn,
                    mustUseProp: kn,
                    async: !0,
                    _lifecycleHooks: Cn
                },
                Dn = /a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/,
                $n = new RegExp("[^" + Dn.source + ".$_\\d]"), jn = "__proto__" in {},
                Fn = "undefined" != typeof window, Rn = "undefined" != typeof WXEnvironment && !!WXEnvironment.platform,
                En = Rn && WXEnvironment.platform.toLowerCase(), In = Fn && window.navigator.userAgent.toLowerCase(),
                zn = In && /msie|trident/.test(In),
                Bn = (In && In.indexOf("msie 9.0"), In && In.indexOf("edge/"), In && In.indexOf("android"), In && /iphone|ipad|ipod|ios/.test(In) || "ios" === En),
                Nn = (In && /chrome\/\d+/.test(In), In && /phantomjs/.test(In), In && In.match(/firefox\/(\d+)/), {}.watch);
            if (Fn) try {
                var Wn = {};
                Object.defineProperty(Wn, "passive", {
                    get: function () {
                    }
                }), window.addEventListener("test-passive", null, Wn)
            } catch (t) {
            }
            var Hn, Un = function () {
                    return void 0 === mn && (mn = !Fn && !Rn && void 0 !== e && e.process && "server" === e.process.env.VUE_ENV), mn
                }, Vn = Fn && window.__VUE_DEVTOOLS_GLOBAL_HOOK__,
                qn = "undefined" != typeof Symbol && O(Symbol) && "undefined" != typeof Reflect && O(Reflect.ownKeys);
            Hn = "undefined" != typeof Set && O(Set) ? Set : function () {
                function t() {
                    this.set = Object.create(null)
                }

                return t.prototype.has = function (t) {
                    return !0 === this.set[t]
                }, t.prototype.add = function (t) {
                    this.set[t] = !0
                }, t.prototype.clear = function () {
                    this.set = Object.create(null)
                }, t
            }();
            var Gn = P, Xn = 0, Jn = function () {
                this.id = Xn++, this.subs = []
            };
            Jn.prototype.addSub = function (t) {
                this.subs.push(t)
            }, Jn.prototype.removeSub = function (t) {
                v(this.subs, t)
            }, Jn.prototype.depend = function () {
                Jn.target && Jn.target.addDep(this)
            }, Jn.prototype.notify = function () {
                for (var t = this.subs.slice(), e = 0, n = t.length; e < n; e++) t[e].update()
            }, Jn.target = null;
            var Qn = [], Kn = function (t, e, n, i, a, r, o, c) {
                this.tag = t, this.data = e, this.children = n, this.text = i, this.elm = a, this.ns = void 0, this.context = r, this.fnContext = void 0, this.fnOptions = void 0, this.fnScopeId = void 0, this.key = e && e.key, this.componentOptions = o, this.componentInstance = void 0, this.parent = void 0, this.raw = !1, this.isStatic = !1, this.isRootInsert = !0, this.isComment = !1, this.isCloned = !1, this.isOnce = !1, this.asyncFactory = c, this.asyncMeta = void 0, this.isAsyncPlaceholder = !1
            }, Yn = {child: {configurable: !0}};
            Yn.child.get = function () {
                return this.componentInstance
            }, Object.defineProperties(Kn.prototype, Yn);
            var Zn = function (t) {
                void 0 === t && (t = "");
                var e = new Kn;
                return e.text = t, e.isComment = !0, e
            }, ti = Array.prototype, ei = Object.create(ti);
            ["push", "pop", "shift", "unshift", "splice", "sort", "reverse"].forEach(function (t) {
                var e = ti[t];
                k(ei, t, function () {
                    for (var n = [], i = arguments.length; i--;) n[i] = arguments[i];
                    var a, r = e.apply(this, n), o = this.__ob__;
                    switch (t) {
                        case"push":
                        case"unshift":
                            a = n;
                            break;
                        case"splice":
                            a = n.slice(2)
                    }
                    return a && o.observeArray(a), o.dep.notify(), r
                })
            });
            var ni = Object.getOwnPropertyNames(ei), ii = !0, ai = function (t) {
                this.value = t, this.dep = new Jn, this.vmCount = 0, k(t, "__ob__", this), Array.isArray(t) ? (jn ? t.push !== t.__proto__.push ? R(t, ei, ni) : F(t, ei) : R(t, ei, ni), this.observeArray(t)) : this.walk(t)
            };
            ai.prototype.walk = function (t) {
                for (var e = Object.keys(t), n = 0; n < e.length; n++) I(t, e[n])
            }, ai.prototype.observeArray = function (t) {
                for (var e = 0, n = t.length; e < n; e++) E(t[e])
            };
            var ri = Ln.optionMergeStrategies;
            ri.data = function (t, e, n) {
                return n ? H(t, e, n) : e && "function" != typeof e ? t : H(t, e)
            }, Cn.forEach(function (t) {
                ri[t] = U
            }), On.forEach(function (t) {
                ri[t + "s"] = q
            }), ri.watch = function (t, e, n, i) {
                if (t === Nn && (t = void 0), e === Nn && (e = void 0), !e) return Object.create(t || null);
                if (!t) return e;
                var a = {};
                for (var r in x(a, t), e) {
                    var o = a[r], c = e[r];
                    o && !Array.isArray(o) && (o = [o]), a[r] = o ? o.concat(c) : Array.isArray(c) ? c : [c]
                }
                return a
            }, ri.props = ri.methods = ri.inject = ri.computed = function (t, e, n, i) {
                if (!t) return e;
                var a = Object.create(null);
                return x(a, t), e && x(a, e), a
            }, ri.provide = H;
            var oi, ci = function (t, e) {
                return void 0 === e ? t : e
            }, li = [], si = !1;
            if ("undefined" != typeof Promise && O(Promise)) {
                var ui = Promise.resolve();
                oi = function () {
                    ui.then(ct), Bn && setTimeout(P)
                }
            } else if (zn || "undefined" == typeof MutationObserver || !O(MutationObserver) && "[object MutationObserverConstructor]" !== MutationObserver.toString()) oi = "undefined" != typeof setImmediate && O(setImmediate) ? function () {
                setImmediate(ct)
            } : function () {
                setTimeout(ct, 0)
            }; else {
                var di = 1, fi = new MutationObserver(ct), pi = document.createTextNode(String(di));
                fi.observe(pi, {characterData: !0}), oi = function () {
                    di = (di + 1) % 2, pi.data = String(di)
                }
            }
            var hi = new Hn, gi = m(function (t) {
                var e = "&" === t.charAt(0), n = "~" === (t = e ? t.slice(1) : t).charAt(0),
                    i = "!" === (t = n ? t.slice(1) : t).charAt(0);
                return t = i ? t.slice(1) : t, {name: t, once: n, capture: i, passive: e}
            });
            Nt(Wt.prototype);
            var vi, yi = {
                    init: function (t, e) {
                        if (t.componentInstance && !t.componentInstance._isDestroyed && t.data.keepAlive) {
                            var n = t;
                            yi.prepatch(n, n)
                        } else (t.componentInstance = Gt(t, Pi)).$mount(e ? t.elm : void 0, e)
                    }, prepatch: function (t, e) {
                        var n = e.componentOptions;
                        he(e.componentInstance = t.componentInstance, n.propsData, n.listeners, e, n.children)
                    }, insert: function (t) {
                        var e = t.context, n = t.componentInstance;
                        n._isMounted || (n._isMounted = !0, me(n, "mounted")), t.data.keepAlive && (e._isMounted ? Pe(n) : ve(n, !0))
                    }, destroy: function (t) {
                        var e = t.componentInstance;
                        e._isDestroyed || (t.data.keepAlive ? ye(e, !0) : e.$destroy())
                    }
                }, mi = Object.keys(yi), _i = 1, xi = 2, bi = null, Pi = null, wi = [], Si = [], Ai = {}, Ti = !1, ki = !1,
                Mi = 0, Oi = Date.now;
            if (Fn && !zn) {
                var Ci = window.performance;
                Ci && "function" == typeof Ci.now && Oi() > document.createEvent("Event").timeStamp && (Oi = function () {
                    return Ci.now()
                })
            }
            var Li = 0, Di = function (t, e, n, i, a) {
                this.vm = t, a && (t._watcher = this), t._watchers.push(this), i ? (this.deep = !!i.deep, this.user = !!i.user, this.lazy = !!i.lazy, this.sync = !!i.sync, this.before = i.before) : this.deep = this.user = this.lazy = this.sync = !1, this.cb = n, this.id = ++Li, this.active = !0, this.dirty = this.lazy, this.deps = [], this.newDeps = [], this.depIds = new Hn, this.newDepIds = new Hn, this.expression = "", "function" == typeof e ? this.getter = e : (this.getter = M(e), this.getter || (this.getter = P)), this.value = this.lazy ? void 0 : this.get()
            };
            Di.prototype.get = function () {
                var t;
                C(this);
                var e = this.vm;
                try {
                    t = this.getter.call(e, e)
                } catch (t) {
                    if (!this.user) throw t;
                    it(t, e, 'getter for watcher "' + this.expression + '"')
                } finally {
                    this.deep && st(t), L(), this.cleanupDeps()
                }
                return t
            }, Di.prototype.addDep = function (t) {
                var e = t.id;
                this.newDepIds.has(e) || (this.newDepIds.add(e), this.newDeps.push(t), this.depIds.has(e) || t.addSub(this))
            }, Di.prototype.cleanupDeps = function () {
                for (var t = this.deps.length; t--;) {
                    var e = this.deps[t];
                    this.newDepIds.has(e.id) || e.removeSub(this)
                }
                var n = this.depIds;
                this.depIds = this.newDepIds, this.newDepIds = n, this.newDepIds.clear(), n = this.deps, this.deps = this.newDeps, this.newDeps = n, this.newDeps.length = 0
            }, Di.prototype.update = function () {
                this.lazy ? this.dirty = !0 : this.sync ? this.run() : Se(this)
            }, Di.prototype.run = function () {
                if (this.active) {
                    var t = this.get();
                    if (t !== this.value || l(t) || this.deep) {
                        var e = this.value;
                        if (this.value = t, this.user) try {
                            this.cb.call(this.vm, t, e)
                        } catch (t) {
                            it(t, this.vm, 'callback for watcher "' + this.expression + '"')
                        } else this.cb.call(this.vm, t, e)
                    }
                }
            }, Di.prototype.evaluate = function () {
                this.value = this.get(), this.dirty = !1
            }, Di.prototype.depend = function () {
                for (var t = this.deps.length; t--;) this.deps[t].depend()
            }, Di.prototype.teardown = function () {
                if (this.active) {
                    this.vm._isBeingDestroyed || v(this.vm._watchers, this);
                    for (var t = this.deps.length; t--;) this.deps[t].removeSub(this);
                    this.active = !1
                }
            };
            var $i = {enumerable: !0, configurable: !0, get: P, set: P}, ji = {lazy: !0}, Fi = 0;
            (function (t) {
                t.prototype._init = function (t) {
                    var e = this;
                    e._uid = Fi++, e._isVue = !0, t && t._isComponent ? Ee(e, t) : e.$options = Q(Ie(e.constructor), t || {}, e), e._renderProxy = e, e._self = e, pe(e), ce(e), ee(e), me(e, "beforeCreate"), "mp-toutiao" !== e.mpHost && xt(e), Te(e), "mp-toutiao" !== e.mpHost && _t(e), "mp-toutiao" !== e.mpHost && me(e, "created"), e.$options.el && e.$mount(e.$options.el)
                }
            })(Be), function (t) {
                var e = {
                    get: function () {
                        return this._data
                    }
                }, n = {
                    get: function () {
                        return this._props
                    }
                };
                Object.defineProperty(t.prototype, "$data", e), Object.defineProperty(t.prototype, "$props", n), t.prototype.$set = z, t.prototype.$delete = B, t.prototype.$watch = function (t, e, n) {
                    var i = this;
                    if (s(e)) return Re(i, t, e, n);
                    (n = n || {}).user = !0;
                    var a = new Di(i, t, e, n);
                    if (n.immediate) try {
                        e.call(i, a.value)
                    } catch (t) {
                        it(t, i, 'callback for immediate watcher "' + a.expression + '"')
                    }
                    return function () {
                        a.teardown()
                    }
                }
            }(Be), function (t) {
                var e = /^hook:/;
                t.prototype.$on = function (t, n) {
                    var i = this;
                    if (Array.isArray(t)) for (var a = 0, r = t.length; a < r; a++) i.$on(t[a], n); else (i._events[t] || (i._events[t] = [])).push(n), e.test(t) && (i._hasHookEvent = !0);
                    return i
                }, t.prototype.$once = function (t, e) {
                    function n() {
                        i.$off(t, n), e.apply(i, arguments)
                    }

                    var i = this;
                    return n.fn = e, i.$on(t, n), i
                }, t.prototype.$off = function (t, e) {
                    var n = this;
                    if (!arguments.length) return n._events = Object.create(null), n;
                    if (Array.isArray(t)) {
                        for (var i = 0, a = t.length; i < a; i++) n.$off(t[i], e);
                        return n
                    }
                    var r, o = n._events[t];
                    if (!o) return n;
                    if (!e) return n._events[t] = null, n;
                    for (var c = o.length; c--;) if ((r = o[c]) === e || r.fn === e) {
                        o.splice(c, 1);
                        break
                    }
                    return n
                }, t.prototype.$emit = function (t) {
                    var e = this, n = e._events[t];
                    if (n) {
                        n = n.length > 1 ? _(n) : n;
                        for (var i = _(arguments, 1), a = 'event handler for "' + t + '"', r = 0, o = n.length; r < o; r++) at(n[r], e, i, e, a)
                    }
                    return e
                }
            }(Be), function (t) {
                t.prototype._update = function (t, e) {
                    var n = this, i = n.$el, a = n._vnode, r = fe(n);
                    n._vnode = t, n.$el = a ? n.__patch__(a, t) : n.__patch__(n.$el, t, e, !1), r(), i && (i.__vue__ = null), n.$el && (n.$el.__vue__ = n), n.$vnode && n.$parent && n.$vnode === n.$parent._vnode && (n.$parent.$el = n.$el)
                }, t.prototype.$forceUpdate = function () {
                    var t = this;
                    t._watcher && t._watcher.update()
                }, t.prototype.$destroy = function () {
                    var t = this;
                    if (!t._isBeingDestroyed) {
                        me(t, "beforeDestroy"), t._isBeingDestroyed = !0;
                        var e = t.$parent;
                        !e || e._isBeingDestroyed || t.$options.abstract || v(e.$children, t), t._watcher && t._watcher.teardown();
                        for (var n = t._watchers.length; n--;) t._watchers[n].teardown();
                        t._data.__ob__ && t._data.__ob__.vmCount--, t._isDestroyed = !0, t.__patch__(t._vnode, null), me(t, "destroyed"), t.$off(), t.$el && (t.$el.__vue__ = null), t.$vnode && (t.$vnode.parent = null)
                    }
                }
            }(Be), function (t) {
                Nt(t.prototype), t.prototype.$nextTick = function (t) {
                    return lt(t, this)
                }, t.prototype._render = function () {
                    var t, e = this, n = e.$options, i = n.render, a = n._parentVnode;
                    a && (e.$scopedSlots = St(a.data.scopedSlots, e.$slots, e.$scopedSlots)), e.$vnode = a;
                    try {
                        bi = e, t = i.call(e._renderProxy, e.$createElement)
                    } catch (n) {
                        it(n, e, "render"), t = e._vnode
                    } finally {
                        bi = null
                    }
                    return Array.isArray(t) && 1 === t.length && (t = t[0]), t instanceof Kn || (t = Zn()), t.parent = a, t
                }
            }(Be);
            var Ri = [String, RegExp, Array], Ei = {
                KeepAlive: {
                    name: "keep-alive",
                    abstract: !0,
                    props: {include: Ri, exclude: Ri, max: [String, Number]},
                    created: function () {
                        this.cache = Object.create(null), this.keys = []
                    },
                    destroyed: function () {
                        for (var t in this.cache) Qe(this.cache, t, this.keys)
                    },
                    mounted: function () {
                        var t = this;
                        this.$watch("include", function (e) {
                            Je(t, function (t) {
                                return Xe(e, t)
                            })
                        }), this.$watch("exclude", function (e) {
                            Je(t, function (t) {
                                return !Xe(e, t)
                            })
                        })
                    },
                    render: function () {
                        var t = this.$slots.default, e = oe(t), n = e && e.componentOptions;
                        if (n) {
                            var i = Ge(n), a = this, r = a.include, o = a.exclude;
                            if (r && (!i || !Xe(r, i)) || o && i && Xe(o, i)) return e;
                            var c = this, l = c.cache, s = c.keys,
                                u = null == e.key ? n.Ctor.cid + (n.tag ? "::" + n.tag : "") : e.key;
                            l[u] ? (e.componentInstance = l[u].componentInstance, v(s, u), s.push(u)) : (l[u] = e, s.push(u), this.max && s.length > parseInt(this.max) && Qe(l, s[0], s, this._vnode)), e.data.keepAlive = !0
                        }
                        return e || t && t[0]
                    }
                }
            };
            (function (t) {
                var e = {
                    get: function () {
                        return Ln
                    }
                };
                Object.defineProperty(t, "config", e), t.util = {
                    warn: Gn,
                    extend: x,
                    mergeOptions: Q,
                    defineReactive: I
                }, t.set = z, t.delete = B, t.nextTick = lt, t.observable = function (t) {
                    return E(t), t
                }, t.options = Object.create(null), On.forEach(function (e) {
                    t.options[e + "s"] = Object.create(null)
                }), t.options._base = t, x(t.options.components, Ei), Ne(t), We(t), He(t), qe(t)
            })(Be), Object.defineProperty(Be.prototype, "$isServer", {get: Un}), Object.defineProperty(Be.prototype, "$ssrContext", {
                get: function () {
                    return this.$vnode && this.$vnode.ssrContext
                }
            }), Object.defineProperty(Be, "FunctionalRenderContext", {value: Wt}), Be.version = "2.6.10";
            var Ii = "[object Array]", zi = "[object Object]", Bi = m(function (t) {
                    var e = {}, n = /;(?![^(]*\))/g, i = /:(.+)/;
                    return t.split(n).forEach(function (t) {
                        if (t) {
                            var n = t.split(i);
                            n.length > 1 && (e[n[0].trim()] = n[1].trim())
                        }
                    }), e
                }), Ni = ["createSelectorQuery", "createIntersectionObserver", "selectAllComponents", "selectComponent"],
                Wi = ["onLaunch", "onShow", "onHide", "onUniNViewMessage", "onError", "onLoad", "onReady", "onUnload", "onPullDownRefresh", "onReachBottom", "onTabItemTap", "onShareAppMessage", "onResize", "onPageScroll", "onNavigationBarButtonTap", "onBackPress", "onNavigationBarSearchInputChanged", "onNavigationBarSearchInputConfirmed", "onNavigationBarSearchInputClicked", "onPageShow", "onPageHide", "onPageResize"];
            Be.prototype.__patch__ = function (t, e) {
                var n = this;
                if (null !== e && ("page" === this.mpType || "component" === this.mpType)) {
                    var i = this.$scope, a = Object.create(null);
                    try {
                        a = on(this)
                    } catch (t) {
                        console.error(t)
                    }
                    a.__webviewId__ = i.data.__webviewId__;
                    var r = Object.create(null);
                    Object.keys(a).forEach(function (t) {
                        r[t] = i.data[t]
                    });
                    var o = Ke(a, r);
                    Object.keys(o).length ? (Object({
                        NODE_ENV: "production",
                        VUE_APP_PLATFORM: "mp-weixin",
                        BASE_URL: "/"
                    }).VUE_APP_DEBUG && console.log("[" + +new Date + "][" + (i.is || i.route) + "][" + this._uid + "]差量更新", JSON.stringify(o)), this.__next_tick_pending = !0, i.setData(o, function () {
                        n.__next_tick_pending = !1, nn(n)
                    })) : nn(this)
                }
            }, Be.prototype.$mount = function (t, e) {
                return ln(this, 0, e)
            }, function (t) {
                var e = t.extend;
                t.extend = function (t) {
                    var n = (t = t || {}).methods;
                    return n && Object.keys(n).forEach(function (e) {
                        -1 !== Wi.indexOf(e) && (t[e] = n[e], delete n[e])
                    }), e.call(this, t)
                };
                var n = t.config.optionMergeStrategies, i = n.created;
                Wi.forEach(function (t) {
                    n[t] = i
                }), t.prototype.__lifecycle_hooks__ = Wi
            }(Be), function (t) {
                t.config.errorHandler = function (t) {
                    console.error(t)
                };
                var e = t.prototype.$emit;
                t.prototype.$emit = function (t) {
                    return this.$scope && t && this.$scope.triggerEvent(t, {__args__: _(arguments, 1)}), e.apply(this, arguments)
                }, t.prototype.$nextTick = function (t) {
                    return rn(this, t)
                }, Ni.forEach(function (e) {
                    t.prototype[e] = function (t) {
                        if (this.$scope) return this.$scope[e](t)
                    }
                }), t.prototype.__init_provide = _t, t.prototype.__init_injections = xt, t.prototype.__call_hook = function (t, e) {
                    var n = this;
                    C();
                    var i, a = n.$options[t], r = t + " hook";
                    if (a) for (var o = 0, c = a.length; o < c; o++) i = at(a[o], n, e ? [e] : null, n, r);
                    return n._hasHookEvent && n.$emit("hook:" + t), L(), i
                }, t.prototype.__set_model = function (t, e, n, i) {
                    Array.isArray(i) && (-1 !== i.indexOf("trim") && (n = n.trim()), -1 !== i.indexOf("number") && (n = this._n(n))), t || (t = this), t[e] = n
                }, t.prototype.__set_sync = function (t, e, n) {
                    t || (t = this), t[e] = n
                }, t.prototype.__get_orig = function (t) {
                    return s(t) && t.$orig || t
                }, t.prototype.__get_value = function (t, e) {
                    return gn(e || this, t)
                }, t.prototype.__get_class = function (t, e) {
                    return sn(e, t)
                }, t.prototype.__get_style = function (t, e) {
                    if (!t && !e) return "";
                    var n = hn(t), i = e ? x(e, n) : n;
                    return Object.keys(i).map(function (t) {
                        return An(t) + ":" + i[t]
                    }).join(";")
                }, t.prototype.__map = function (t, e) {
                    var n, i, a, r, o;
                    if (Array.isArray(t)) {
                        for (n = new Array(t.length), i = 0, a = t.length; i < a; i++) n[i] = e(t[i], i);
                        return n
                    }
                    if (l(t)) {
                        for (r = Object.keys(t), n = Object.create(null), i = 0, a = r.length; i < a; i++) n[o = r[i]] = e(t[o], o, i);
                        return n
                    }
                    return []
                }
            }(Be), n.default = Be
        }.call(this, i("c8ba"))
    }, "67b9": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c4db")).default)
        }).call(this, n("543d").createPage)
    }, "6acb": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("cd08")).default)
        }).call(this, n("543d").createPage)
    }, "6af7": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("70db")).default)
        }).call(this, n("543d").createPage)
    }, "6c7f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("6187")).default)
        }).call(this, n("543d").createPage)
    }, "6cdc": function (t, e, n) {
    }, "6dd1": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("bb96")).default)
        }).call(this, n("543d").createPage)
    }, "708f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c4a5")).default)
        }).call(this, n("543d").createPage)
    }, 7275: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("0b95")).default)
        }).call(this, n("543d").createPage)
    }, 7319: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        e.default = function (t) {
            var e = this;
            return new Promise(function (n, i) {
                try {
                    var a = null;
                    (a = e.createSelectorQuery()).select(".".concat(t)).boundingClientRect(), a.exec(function (t) {
                        n(t)
                    })
                } catch (t) {
                    i(t)
                }
            })
        }
    }, "746d": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b2f2")).default)
        }).call(this, n("543d").createPage)
    }, 7544: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b912")).default)
        }).call(this, n("543d").createPage)
    }, "7ab7": function (t, e, n) {
        (function (t) {
            function i(t) {
                return t && t.__esModule ? t : {default: t}
            }

            function a() {
                if ("function" != typeof WeakMap) return null;
                var t = new WeakMap;
                return a = function () {
                    return t
                }, t
            }

            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var r = function (t) {
                if (t && t.__esModule) return t;
                var e = a();
                if (e && e.has(t)) return e.get(t);
                var n = {};
                if (null != t) {
                    var i = Object.defineProperty && Object.getOwnPropertyDescriptor;
                    for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) {
                        var o = i ? Object.getOwnPropertyDescriptor(t, r) : null;
                        o && (o.get || o.set) ? Object.defineProperty(n, r, o) : n[r] = t[r]
                    }
                }
                return n.default = t, e && e.set(t, n), n
            }(n("b1c7")), o = i(n("4360")), c = i(n("816e")), l = n("ac6b");
            e.default = function (e) {
                if (console.log(e), e.open_type || e.params || e.page_url) {
                    var n = e.open_type, i = e.params, a = e.page_url;
                    switch (!Array.isArray(i) && "[object String]" === Object.prototype.toString.call(i) && i && (i = JSON.parse(i)), n) {
                        case"reLaunch":
                            t.reLaunch({url: i[0].value});
                            break;
                        case"redirect":
                            t.redirectTo({url: i[0].value});
                            break;
                        case"navigate":
                            for (var s = a.split("?")[0], u = "?", d = 0; d < i.length; d++) u += "".concat(i[d].key, "=").concat(i[d].value, "&");
                            s += u.slice(0, u.length - 1), "?" === u && (s = a), t.navigateTo({url: s});
                            break;
                        case"app_admin":
                            1 == o.default.state.user.info.identity.is_admin && t.navigateTo({url: e.url});
                            break;
                        case"back":
                            t.navigateBack({});
                            break;
                        case"tel":
                            t.makePhoneCall({phoneNumber: i[0].value});
                            break;
                        case"web":
                            t.navigateTo({url: "".concat(a.split("?")[0], "?url=").concat(encodeURIComponent(i[0].value))});
                            break;
                        case"app":
                            for (var f = "", p = 0; p < i.length; p++) f += "".concat(i[p].key, "=").concat(i[p].value, "&");
                            if ("string" != typeof f) return;
                            var h = r.urlParamsToObject(f), g = "", v = "";
                            g = h.app_id || "", v = h.path || "", t.navigateToMiniProgram({
                                appId: g,
                                path: v,
                                success: function (t) {
                                    console.log("打开小程序成功", t)
                                },
                                fail: function (t) {
                                    console.log("打开小程序失败", t)
                                }
                            });
                            break;
                        case"clear_cache":
                            t.showModal({
                                title: "提示",
                                content: "确认清理缓存？",
                                cancelText: "取消",
                                confirmText: "确认",
                                success: function (e) {
                                    e.confirm && (t.showLoading({title: "清理缓存..."}), (0, l.clearStorage)(), c.default && o.default && o.default.state.user.accessToken && c.default.loginByToken(o.default.state.user.accessToken), o.default.dispatch("mallConfig/actionResetConfig"), t.hideLoading(), t.showToast({
                                        title: "清理完成",
                                        duration: 1e3
                                    }))
                                }
                            })
                    }
                }
            }
        }).call(this, n("543d").default)
    }, "7c33": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("02b0")).default)
        }).call(this, n("543d").createPage)
    }, "810d": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("be28")).default)
        }).call(this, n("543d").createPage)
    }, "816e": function (t, e, n) {
        (function (t) {
            function i(t) {
                return t && t.__esModule ? t : {default: t}
            }

            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var a = i(n("9dc18")), r = i(n("27f5")), o = n("ac6b"), c = i(n("9d0f")), l = i(n("2749")),
                s = i(n("4360")), u = "_USER_ACCESS_TOKEN", d = null, f = {
                    getUserInfoResolve: null, getUserInfoReject: null, getAccessToken: function (e) {
                        var n = this;
                        return void 0 === (e = e || {}).cacheOnly && (e.cacheOnly = !1), new Promise(function (i, s) {
                            var d = (0, o.getStorageSync)(u);
                            if (d) return i(d);
                            if (e.cacheOnly) return i(d);
                            var f = getCurrentPages();
                            f[f.length - 1].$vm.$store.commit("user/showLoginModal", !0), n.getUserInfoResolve = function (e) {
                                console.log("getUserInfoResolve->", e), t.showLoading({
                                    mask: !0,
                                    title: "正在登录"
                                }), t.login({
                                    scopes: "auth_base", success: function (n) {
                                        var d = {
                                            encryptedData: e.detail.encryptedData,
                                            iv: e.detail.iv,
                                            rawData: e.detail.rawData,
                                            signature: e.detail.signature,
                                            code: n.code
                                        };
                                        (0, a.default)({
                                            url: r.default.passport.login,
                                            method: "post",
                                            data: d
                                        }).then(function (e) {
                                            return t.hideLoading(), 0 === e.code ? (c.default.trigger(l.default.EVENT_USER_LOGIN), (0, o.setStorageSync)(u, e.data.access_token), i(e.data.access_token)) : s(e.msg)
                                        }).catch(function (e) {
                                            t.hideLoading(), s(e)
                                        })
                                    }
                                })
                            }, n.getUserInfoReject = function (t) {
                                s(t)
                            }
                        })
                    }, getInfo: function (e) {
                        var n = this;
                        return void 0 === (e = e || {}).refresh && (e.refresh = !1), new Promise(function (i, o) {
                            if (e.refresh && (d = null), d) return i(d);
                            n.getAccessToken().then(function (e) {
                                t.showNavigationBarLoading(), (0, a.default)({url: r.default.user.user_info}).then(function (e) {
                                    if (t.hideNavigationBarLoading(), 0 === e.code) {
                                        if (d = e.data, c.default.trigger(l.default.EVENT_USER_REGISTER, d), void 0 !== d.register) {
                                            var n = d.register;
                                            if (n.coupon_list) {
                                                var a = {list: n.coupon_list, type: "register"};
                                                s.default.dispatch("page/actionSetCoupon", a)
                                            }
                                        }
                                        return i(d)
                                    }
                                    return o(e.msg)
                                }).catch(function (e) {
                                    return t.hideNavigationBarLoading(), o(e)
                                })
                            }).catch(function (t) {
                                return o(t)
                            })
                        })
                    }, isLogin: function () {
                        return !!(s.default && s.default.state.user && s.default.state.user.accessToken) || !!(0, o.getStorageSync)(u)
                    }, loginByToken: function (t) {
                        (0, o.setStorageSync)(u, t)
                    }
                };
            e.default = f
        }).call(this, n("543d").default)
    }, 82951: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7ba4")).default)
        }).call(this, n("543d").createPage)
    }, "833f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9749")).default)
        }).call(this, n("543d").createPage)
    }, "83ab": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("8b11")).default)
        }).call(this, n("543d").createPage)
    }, "89f3": function (t, e, n) {
    }, "8a13": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7d94")).default)
        }).call(this, n("543d").createPage)
    }, "8a21": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("86f4")).default)
        }).call(this, n("543d").createPage)
    }, "8afb": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4732")).default)
        }).call(this, n("543d").createPage)
    }, "8d1c": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5a67")).default)
        }).call(this, n("543d").createPage)
    }, "8d66": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("8478")).default)
        }).call(this, n("543d").createPage)
    }, "8de3": function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.push = e.popAll = void 0;
        var i = n("b1c7"), a = n("ac6b");
        e.popAll = function () {
            var t = (0, a.getStorageSync)("_FORM_ID_LIST");
            return (0, a.setStorageSync)("_FORM_ID_LIST", []), t || []
        };
        e.push = function (t) {
            if (!t || "the formId is a mock one" === t) return !1;
            var e = (0, a.getStorageSync)("_FORM_ID_LIST");
            e && e.length || (e = []);
            var n = {value: t, type: 0, remains: 1, expires_at: (0, i.datetime)(null, (0, i.time)() + 604800 - 60)};
            e.push(n), (0, a.setStorageSync)("_FORM_ID_LIST", e)
        }
    }, "8e27": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("6683")).default)
        }).call(this, n("543d").createPage)
    }, "8e35": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1d0a")).default)
        }).call(this, n("543d").createPage)
    }, "8f6d": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("73d2")).default)
        }).call(this, n("543d").createPage)
    }, 9237: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("881e")).default)
        }).call(this, n("543d").createPage)
    }, "933a": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("23fb")).default)
        }).call(this, n("543d").createPage)
    }, 9403: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7dea")).default)
        }).call(this, n("543d").createPage)
    }, "940e": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e7e8")).default)
        }).call(this, n("543d").createPage)
    }, 9465: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0,
            state: {id: null, showPayment: !1, payData: null, payType: null, resolve: null, reject: null},
            getters: {
                id: function (t) {
                    return t.id
                }, showPayment: function (t) {
                    return t.showPayment
                }, payData: function (t) {
                    return t.payData
                }, payType: function (t) {
                    return t.payType
                }, resolve: function (t) {
                    return t.resolve
                }, reject: function (t) {
                    return t.reject
                }
            },
            mutations: {
                id: function (t, e) {
                    t.id = e
                }, showPayment: function (t, e) {
                    t.showPayment = e
                }, payData: function (t, e) {
                    t.payData = e
                }, payType: function (t, e) {
                    t.payType = e
                }, resolve: function (t, e) {
                    t.resolve = e
                }, reject: function (t, e) {
                    t.reject = e
                }, setAll: function (t, e) {
                    for (var n in e) t[n] = e[n];
                    console.log("in payment.js setAll ok:", t)
                }
            },
            actions: {
                reset: function (t) {
                    t.commit("id", null), t.commit("showPayment", !1), t.commit("payData", null), t.commit("payType", null), t.commit("resolve", null), t.commit("reject", null)
                }
            }
        };
        e.default = i
    }, "94cc": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("391f")).default)
        }).call(this, n("543d").createPage)
    }, 9570: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("8287")).default)
        }).call(this, n("543d").createPage)
    }, "96cf": function (e, n) {
        !function (n) {
            function i(t, e, n, i) {
                var a = e && e.prototype instanceof r ? e : r, o = Object.create(a.prototype), c = new h(i || []);
                return o._invoke = u(t, n, c), o
            }

            function a(t, e, n) {
                try {
                    return {type: "normal", arg: t.call(e, n)}
                } catch (t) {
                    return {type: "throw", arg: t}
                }
            }

            function r() {
            }

            function o() {
            }

            function c() {
            }

            function l(t) {
                ["next", "throw", "return"].forEach(function (e) {
                    t[e] = function (t) {
                        return this._invoke(e, t)
                    }
                })
            }

            function s(e) {
                function n(i, r, o, c) {
                    var l = a(e[i], e, r);
                    if ("throw" !== l.type) {
                        var s = l.arg, u = s.value;
                        return u && "object" === (void 0 === u ? "undefined" : t(u)) && _.call(u, "__await") ? Promise.resolve(u.__await).then(function (t) {
                            n("next", t, o, c)
                        }, function (t) {
                            n("throw", t, o, c)
                        }) : Promise.resolve(u).then(function (t) {
                            s.value = t, o(s)
                        }, function (t) {
                            return n("throw", t, o, c)
                        })
                    }
                    c(l.arg)
                }

                var i;
                this._invoke = function (t, e) {
                    function a() {
                        return new Promise(function (i, a) {
                            n(t, e, i, a)
                        })
                    }

                    return i = i ? i.then(a, a) : a()
                }
            }

            function u(t, e, n) {
                var i = T;
                return function (r, o) {
                    if (i === M) throw new Error("Generator is already running");
                    if (i === O) {
                        if ("throw" === r) throw o;
                        return v()
                    }
                    for (n.method = r, n.arg = o; ;) {
                        var c = n.delegate;
                        if (c) {
                            var l = d(c, n);
                            if (l) {
                                if (l === C) continue;
                                return l
                            }
                        }
                        if ("next" === n.method) n.sent = n._sent = n.arg; else if ("throw" === n.method) {
                            if (i === T) throw i = O, n.arg;
                            n.dispatchException(n.arg)
                        } else "return" === n.method && n.abrupt("return", n.arg);
                        i = M;
                        var s = a(t, e, n);
                        if ("normal" === s.type) {
                            if (i = n.done ? O : k, s.arg === C) continue;
                            return {value: s.arg, done: n.done}
                        }
                        "throw" === s.type && (i = O, n.method = "throw", n.arg = s.arg)
                    }
                }
            }

            function d(t, e) {
                var n = t.iterator[e.method];
                if (n === y) {
                    if (e.delegate = null, "throw" === e.method) {
                        if (t.iterator.return && (e.method = "return", e.arg = y, d(t, e), "throw" === e.method)) return C;
                        e.method = "throw", e.arg = new TypeError("The iterator does not provide a 'throw' method")
                    }
                    return C
                }
                var i = a(n, t.iterator, e.arg);
                if ("throw" === i.type) return e.method = "throw", e.arg = i.arg, e.delegate = null, C;
                var r = i.arg;
                return r ? r.done ? (e[t.resultName] = r.value, e.next = t.nextLoc, "return" !== e.method && (e.method = "next", e.arg = y), e.delegate = null, C) : r : (e.method = "throw", e.arg = new TypeError("iterator result is not an object"), e.delegate = null, C)
            }

            function f(t) {
                var e = {tryLoc: t[0]};
                1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e)
            }

            function p(t) {
                var e = t.completion || {};
                e.type = "normal", delete e.arg, t.completion = e
            }

            function h(t) {
                this.tryEntries = [{tryLoc: "root"}], t.forEach(f, this), this.reset(!0)
            }

            function g(t) {
                if (t) {
                    var e = t[b];
                    if (e) return e.call(t);
                    if ("function" == typeof t.next) return t;
                    if (!isNaN(t.length)) {
                        var n = -1, i = function e() {
                            for (; ++n < t.length;) if (_.call(t, n)) return e.value = t[n], e.done = !1, e;
                            return e.value = y, e.done = !0, e
                        };
                        return i.next = i
                    }
                }
                return {next: v}
            }

            function v() {
                return {value: y, done: !0}
            }

            var y, m = Object.prototype, _ = m.hasOwnProperty, x = "function" == typeof Symbol ? Symbol : {},
                b = x.iterator || "@@iterator", P = x.asyncIterator || "@@asyncIterator",
                w = x.toStringTag || "@@toStringTag", S = "object" === (void 0 === e ? "undefined" : t(e)),
                A = n.regeneratorRuntime;
            if (A) S && (e.exports = A); else {
                (A = n.regeneratorRuntime = S ? e.exports : {}).wrap = i;
                var T = "suspendedStart", k = "suspendedYield", M = "executing", O = "completed", C = {}, L = {};
                L[b] = function () {
                    return this
                };
                var D = Object.getPrototypeOf, $ = D && D(D(g([])));
                $ && $ !== m && _.call($, b) && (L = $);
                var j = c.prototype = r.prototype = Object.create(L);
                o.prototype = j.constructor = c, c.constructor = o, c[w] = o.displayName = "GeneratorFunction", A.isGeneratorFunction = function (t) {
                    var e = "function" == typeof t && t.constructor;
                    return !!e && (e === o || "GeneratorFunction" === (e.displayName || e.name))
                }, A.mark = function (t) {
                    return Object.setPrototypeOf ? Object.setPrototypeOf(t, c) : (t.__proto__ = c, w in t || (t[w] = "GeneratorFunction")), t.prototype = Object.create(j), t
                }, A.awrap = function (t) {
                    return {__await: t}
                }, l(s.prototype), s.prototype[P] = function () {
                    return this
                }, A.AsyncIterator = s, A.async = function (t, e, n, a) {
                    var r = new s(i(t, e, n, a));
                    return A.isGeneratorFunction(e) ? r : r.next().then(function (t) {
                        return t.done ? t.value : r.next()
                    })
                }, l(j), j[w] = "Generator", j[b] = function () {
                    return this
                }, j.toString = function () {
                    return "[object Generator]"
                }, A.keys = function (t) {
                    var e = [];
                    for (var n in t) e.push(n);
                    return e.reverse(), function n() {
                        for (; e.length;) {
                            var i = e.pop();
                            if (i in t) return n.value = i, n.done = !1, n
                        }
                        return n.done = !0, n
                    }
                }, A.values = g, h.prototype = {
                    constructor: h, reset: function (t) {
                        if (this.prev = 0, this.next = 0, this.sent = this._sent = y, this.done = !1, this.delegate = null, this.method = "next", this.arg = y, this.tryEntries.forEach(p), !t) for (var e in this) "t" === e.charAt(0) && _.call(this, e) && !isNaN(+e.slice(1)) && (this[e] = y)
                    }, stop: function () {
                        this.done = !0;
                        var t = this.tryEntries[0].completion;
                        if ("throw" === t.type) throw t.arg;
                        return this.rval
                    }, dispatchException: function (t) {
                        function e(e, i) {
                            return r.type = "throw", r.arg = t, n.next = e, i && (n.method = "next", n.arg = y), !!i
                        }

                        if (this.done) throw t;
                        for (var n = this, i = this.tryEntries.length - 1; i >= 0; --i) {
                            var a = this.tryEntries[i], r = a.completion;
                            if ("root" === a.tryLoc) return e("end");
                            if (a.tryLoc <= this.prev) {
                                var o = _.call(a, "catchLoc"), c = _.call(a, "finallyLoc");
                                if (o && c) {
                                    if (this.prev < a.catchLoc) return e(a.catchLoc, !0);
                                    if (this.prev < a.finallyLoc) return e(a.finallyLoc)
                                } else if (o) {
                                    if (this.prev < a.catchLoc) return e(a.catchLoc, !0)
                                } else {
                                    if (!c) throw new Error("try statement without catch or finally");
                                    if (this.prev < a.finallyLoc) return e(a.finallyLoc)
                                }
                            }
                        }
                    }, abrupt: function (t, e) {
                        for (var n = this.tryEntries.length - 1; n >= 0; --n) {
                            var i = this.tryEntries[n];
                            if (i.tryLoc <= this.prev && _.call(i, "finallyLoc") && this.prev < i.finallyLoc) {
                                var a = i;
                                break
                            }
                        }
                        a && ("break" === t || "continue" === t) && a.tryLoc <= e && e <= a.finallyLoc && (a = null);
                        var r = a ? a.completion : {};
                        return r.type = t, r.arg = e, a ? (this.method = "next", this.next = a.finallyLoc, C) : this.complete(r)
                    }, complete: function (t, e) {
                        if ("throw" === t.type) throw t.arg;
                        return "break" === t.type || "continue" === t.type ? this.next = t.arg : "return" === t.type ? (this.rval = this.arg = t.arg, this.method = "return", this.next = "end") : "normal" === t.type && e && (this.next = e), C
                    }, finish: function (t) {
                        for (var e = this.tryEntries.length - 1; e >= 0; --e) {
                            var n = this.tryEntries[e];
                            if (n.finallyLoc === t) return this.complete(n.completion, n.afterLoc), p(n), C
                        }
                    }, catch: function (t) {
                        for (var e = this.tryEntries.length - 1; e >= 0; --e) {
                            var n = this.tryEntries[e];
                            if (n.tryLoc === t) {
                                var i = n.completion;
                                if ("throw" === i.type) {
                                    var a = i.arg;
                                    p(n)
                                }
                                return a
                            }
                        }
                        throw new Error("illegal catch attempt")
                    }, delegateYield: function (t, e, n) {
                        return this.delegate = {
                            iterator: g(t),
                            resultName: e,
                            nextLoc: n
                        }, "next" === this.method && (this.arg = y), C
                    }
                }
            }
        }(function () {
            return this || "object" === ("undefined" == typeof self ? "undefined" : t(self)) && self
        }() || Function("return this")())
    }, "971f": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("ae51")).default)
        }).call(this, n("543d").createPage)
    }, 9727: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d028")).default)
        }).call(this, n("543d").createPage)
    }, "972f": function (t, e, n) {
        (function (t) {
            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var i = n("8de3"), a = n("ac6b");
            e.default = function (e) {
                switch (e.form && (0, i.push)(e.e.detail.formId), e.open_type) {
                    case"redirect":
                        t.redirectTo({url: e.url});
                        break;
                    case"navigate":
                        console.log(e.url), t.navigateTo({url: e.url});
                        break;
                    case"app_admin":
                        1 == e.$store.state.user.info.identity.is_admin && t.navigateTo({url: e.url});
                        break;
                    case"tel":
                        e.params ? t.makePhoneCall({phoneNumber: e.params[0].value}) : e.number && t.makePhoneCall({phoneNumber: e.number});
                        break;
                    case"web":
                        t.navigateTo({url: e.url});
                        break;
                    case"app":
                        if (e.url) {
                            var n = e.url.split("?")[1];
                            t.navigateToMiniProgram({
                                appId: n.slice(0, 25).split("=")[1],
                                path: n.slice(n.indexOf("&") + 1, n.length).split("=")[1],
                                success: function (t) {
                                    console.log(t)
                                },
                                fail: function (t) {
                                    console.log(t)
                                }
                            })
                        } else e.appId && t.navigateToMiniProgram({
                            appId: e.appId, success: function (t) {
                                console.log(t)
                            }, fail: function (t) {
                                console.log(t)
                            }
                        });
                        break;
                    case"clear_cache":
                        (0, a.clearStorage)();
                        break;
                    case"map":
                        t.openLocation({
                            latitude: Number(e.latitude),
                            longitude: Number(e.longitude),
                            name: e.address,
                            success: function () {
                                console.log("success")
                            }
                        });
                        break;
                    case"reload":
                        t.redirectTo({url: this.$platDiff.routeWithOption()})
                }
            }
        }).call(this, n("543d").default)
    }, "99e4": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("54fd")).default)
        }).call(this, n("543d").createPage)
    }, "9a5b": function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a99c")).default)
        }).call(this, n("543d").createPage)
    }, "9d0f": function (t, e, n) {
        function i(t) {
            this.resolveFunc = function () {
            }, this.rejectFunc = function () {
            }, t(this.resolve.bind(this), this.reject.bind(this))
        }

        i.prototype.resolve = function (t) {
            var e = this;
            setTimeout(function () {
                e.resolveFunc(t)
            }, 0)
        }, i.prototype.reject = function (t) {
            var e = this;
            setTimeout(function () {
                e.rejectFunc(t)
            }, 0)
        }, i.prototype.then = function (t, e) {
            return this.resolveFunc = t, this.rejectFunc = e, this
        }, t.exports = {
            _resolveStorage: {}, _addResolve: function (t, e, n) {
                this._resolveStorage[t] || (this._resolveStorage[t] = []), this._resolveStorage[t].push({
                    resolve: e,
                    removeEventAfterTrigger: n
                })
            }, on: function (t, e) {
                var n = this;
                return new i(function (i) {
                    void 0 === e && (e = !0), n._addResolve(t, i, e)
                })
            }, trigger: function (t, e) {
                if (this._resolveStorage[t] && this._resolveStorage[t].length) {
                    var n = [];
                    for (var i in this._resolveStorage[t]) this._resolveStorage[t][i].resolve(e), this._resolveStorage[t][i].removeEventAfterTrigger || n.push(this._resolveStorage[t][i]);
                    this._resolveStorage[t] = n
                }
            }
        }
    }, "9dc18": function (t, e, n) {
        (function (t) {
            function i(t) {
                return t && t.__esModule ? t : {default: t}
            }

            function a(t, e) {
                return c(t) || o(t, e) || r()
            }

            function r() {
                throw new TypeError("Invalid attempt to destructure non-iterable instance")
            }

            function o(t, e) {
                if (Symbol.iterator in Object(t) || "[object Arguments]" === Object.prototype.toString.call(t)) {
                    var n = [], i = !0, a = !1, r = void 0;
                    try {
                        for (var o, c = t[Symbol.iterator](); !(i = (o = c.next()).done) && (n.push(o.value), !e || n.length !== e); i = !0) ;
                    } catch (t) {
                        a = !0, r = t
                    } finally {
                        try {
                            i || null == c.return || c.return()
                        } finally {
                            if (a) throw r
                        }
                    }
                    return n
                }
            }

            function c(t) {
                if (Array.isArray(t)) return t
            }

            function l(t, e, n, i, a, r, o) {
                try {
                    var c = t[r](o), l = c.value
                } catch (t) {
                    return void n(t)
                }
                c.done ? e(l) : Promise.resolve(l).then(i, a)
            }

            function s(t) {
                return function () {
                    var e = this, n = arguments;
                    return new Promise(function (i, a) {
                        function r(t) {
                            l(c, i, a, r, o, "next", t)
                        }

                        function o(t) {
                            l(c, i, a, r, o, "throw", t)
                        }

                        var c = t.apply(e, n);
                        r(void 0)
                    })
                }
            }

            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var u = i(n("a34a")), d = i(n("66fd")), f = n("8de3"), p = n("63ad"), h = i(n("36e8")), g = n("b1c7"),
                v = i(n("4360")), y = function () {
                    var e = s(u.default.mark(function e(n) {
                        var i, r, o, c, l, s, y, m;
                        return u.default.wrap(function (e) {
                            for (; ;) switch (e.prev = e.next) {
                                case 0:
                                    return i = {
                                        "X-App-Platform": n.header && n.header["X-App-Platform"] ? n.header["X-App-Platform"] : p.platform,
                                        "X-Form-Id-List": JSON.stringify((0, f.popAll)()),
                                        "X-Requested-With": n.header && n.header["X-Requested-With"] ? n.header["X-Requested-With"] : "XMLHttpRequest",
                                        "X-App-Version": d.default.prototype.$appVersion,
                                        "content-type": "application/x-www-form-urlencoded"
                                    }, e.next = 3, v.default.dispatch("user/loadAccessTokenFormCache");
                                case 3:
                                    return v.default.state.user && v.default.state.user.accessToken && (i["X-Access-Token"] = v.default.state.user.accessToken), v.default.state.user && 0 !== v.default.state.user.tempParentId && (i["X-User-Id"] = v.default.state.user.tempParentId + ""), r = {}, n.url.replace(/([^=&]+)=([^&]*)/g, function (t, e, n) {
                                        r[decodeURIComponent(e)] = decodeURIComponent(n)
                                    }), -1 !== (0, g.objectValues)(h.default.mch).indexOf(r.r) && (o = t.getStorageSync("MCH2019"), i["Mch-Access-Token"] = o.token), e.next = 10, t.request({
                                        url: n.url,
                                        method: n.method || "get",
                                        data: n.data,
                                        header: i
                                    });
                                case 10:
                                    if (c = e.sent, l = a(c, 2), s = l[0], y = l[1], !s) {
                                        e.next = 20;
                                        break
                                    }
                                    return m = {
                                        code: 400,
                                        msg: s.errMsg,
                                        data: s
                                    }, _(m), e.abrupt("return", Promise.reject(m));
                                case 20:
                                    return e.abrupt("return", b(y));
                                case 21:
                                case"end":
                                    return e.stop()
                            }
                        }, e)
                    }));
                    return function (t) {
                        return e.apply(this, arguments)
                    }
                }(), m = function () {
                    var e = getCurrentPages(), n = e[e.length - 1], i = n.options || {}, a = n.route || "";
                    0 !== a.indexOf("/") && (a = "/" + a);
                    var r = "";
                    for (var o in i) r += "".concat(o, "=").concat(i[o], "&");
                    t.redirectTo({url: a + (r ? "?".concat(r) : "")})
                }, _ = function (e) {
                    t.showModal({
                        title: "网络错误",
                        content: "网络开了小差，请刷新重试下哦~",
                        cancelText: "复制错误",
                        confirmText: "刷新页面",
                        success: function (n) {
                            if (n.cancel) {
                                var i = "code: ".concat(e.code, ", \r\nmsg: ").concat(e.msg, ", \r\ndetail: ") + (e.data ? "string" == typeof e.data ? e.data : JSON.stringify(e.data) : null);
                                t.setClipboardData({
                                    data: i, fail: function (t) {
                                        console.log("错误复制失败", t)
                                    }
                                })
                            }
                            n.confirm && m()
                        }
                    })
                }, x = function (e) {
                    if (!e.data) return Promise.reject({code: 200, msg: "数据不存在", data: e});
                    var n = e.data, i = n.msg, a = n.code;
                    return a >= 400 ? (_({
                        code: a,
                        msg: i,
                        data: e.data.error || e.data.data || i
                    }), Promise.reject(i)) : -1 === a ? (v.default.dispatch("user/logout"), v.default.dispatch("user/accessToken"), Promise.reject(i)) : -2 !== a ? -3 === a ? (t.redirectTo({url: "/plugins/mch/mch/login/login"}), Promise.reject(i)) : Promise.resolve(e.data) : void t.redirectTo({url: "/pages/disabled/disabled?text=" + e.data.data.text})
                }, b = function (t) {
                    var e = {code: 500, msg: "服务器内部错误", data: t};
                    switch (t.statusCode) {
                        case 200:
                            return x(t);
                        case 404:
                            e = {code: 404, msg: "资源获取不到", data: t};
                            break;
                        case 500:
                            e = {code: 500, msg: "服务器内部错误", data: t};
                            break;
                        case 503:
                            e = {code: 503, msg: "服务不可用", data: t};
                            break;
                        case 504:
                            e = {code: 504, msg: "网关超时", data: t};
                            break;
                        case 400:
                            e = {code: 400, msg: "服务器不理解请求的语法", data: t};
                            break;
                        case 403:
                            e = {code: 403, msg: "服务器拒绝请求", data: t};
                            break;
                        case 405:
                            e = {code: 405, msg: "方法禁用", data: t};
                            break;
                        case 406:
                            e = {code: 406, msg: "无法使用请求的内容特性响应请求的网页", data: t};
                            break;
                        case 407:
                            e = {code: 407, msg: "需要代理授权", data: t};
                            break;
                        case 408:
                            e = {code: 408, msg: "请求超时", data: t};
                            break;
                        case 409:
                            e = {code: 409, msg: "冲突", data: t};
                            break;
                        case 410:
                            e = {code: 410, msg: "已删除", data: t};
                            break;
                        case 411:
                            e = {code: 411, msg: "需要有效长度", data: t};
                            break;
                        case 412:
                            e = {code: 412, msg: "服务器未满足请求者在请求中设置的其中一个前提条件", data: t};
                            break;
                        case 413:
                            e = {code: 413, msg: "请求实体过大", data: t};
                            break;
                        case 414:
                            e = {code: 414, msg: "求情URI过长", data: t};
                            break;
                        case 415:
                            e = {code: 415, msg: "不支持的媒体类型", data: t};
                            break;
                        case 416:
                            e = {code: 416, msg: "请求范围不符合要求", data: t};
                            break;
                        case 417:
                            e = {code: 417, msg: "未满足期望值", data: t}
                    }
                    return _(e), Promise.reject(e)
                }, P = y;
            e.default = P
        }).call(this, n("543d").default)
    }, a0c2: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("ca3d")).default)
        }).call(this, n("543d").createPage)
    }, a13e: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("005c")).default)
        }).call(this, n("543d").createPage)
    }, a1dd: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e21a")).default)
        }).call(this, n("543d").createPage)
    }, a233: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("fffe")).default)
        }).call(this, n("543d").createPage)
    }, a242: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("0b8f")).default)
        }).call(this, n("543d").createPage)
    }, a34a: function (t, e, n) {
        t.exports = n("bbdd")
    }, a45e: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("88ce")).default)
        }).call(this, n("543d").createPage)
    }, a88e: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3329")).default)
        }).call(this, n("543d").createPage)
    }, a9cc: function (e, n, i) {
        (function (e, n) {
            function i(e) {
                return (i = "function" == typeof Symbol && "symbol" === t(Symbol.iterator) ? function (e) {
                    return void 0 === e ? "undefined" : t(e)
                } : function (e) {
                    return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : void 0 === e ? "undefined" : t(e)
                })(e)
            }

            function a(t, e) {
                var n = /^#?([a-f\d])([a-f\d])([a-f\d])$/i, i = t.replace(n, function (t, e, n, i) {
                    return e + e + n + n + i + i
                }), a = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(i);
                return "rgba(" + parseInt(a[1], 16) + "," + parseInt(a[2], 16) + "," + parseInt(a[3], 16) + "," + e + ")"
            }

            function r(t, e, n) {
                if (isNaN(t)) throw new Error("[uCharts] unvalid series data!");
                n = n || 10, e = e || "upper";
                for (var i = 1; 1 > n;) n *= 10, i *= 10;
                for (t = "upper" === e ? Math.ceil(t * i) : Math.floor(t * i); 0 != t % n;) "upper" === e ? t++ : t--;
                return t / i
            }

            function o(t, e, n, i) {
                for (var a, r = [], o = 0; o < t.length; o++) {
                    a = {data: [], name: e[o], color: n[o]};
                    for (var c = 0, l = i.length; c < l; c++) if (c < t[o]) a.data.push(null); else {
                        for (var s = 0, u = 0; u < t[o]; u++) s += i[c - u][1];
                        a.data.push(+(s / t[o]).toFixed(3))
                    }
                    r.push(a)
                }
                return r
            }

            function c(t, e, n, i, a) {
                var r = a.width - a.area[1] - a.area[3],
                    o = n.eachSpacing * (a.chartData.xAxisData.xAxisPoints.length - 1), c = e;
                return 0 <= e ? (c = 0, t.event.trigger("scrollLeft")) : Math.abs(e) >= o - r && (c = r - o, t.event.trigger("scrollRight")), c
            }

            function l(t, e, n) {
                function i(t) {
                    for (; 0 > t;) t += 2 * a;
                    for (; t > 2 * a;) t -= 2 * a;
                    return t
                }

                var a = Math.PI;
                return t = i(t), e = i(e), n = i(n), e > n && (n += 2 * a, t < e && (t += 2 * a)), t >= e && t <= n
            }

            function s(t, e, n) {
                var i = t, a = n - e, r = i + (n - a - i) / 1.4142135623730951;
                return r *= -1, {transX: r, transY: .41421356237309515 * (n - a) - (n - a - i) / 1.4142135623730951}
            }

            function u(t, e) {
                function n(t, e) {
                    return !(!t[e - 1] || !t[e + 1]) && (t[e].y >= Math.max(t[e - 1].y, t[e + 1].y) || t[e].y <= Math.min(t[e - 1].y, t[e + 1].y))
                }

                var i = null, a = null, r = null, o = null;
                if (1 > e ? (i = t[0].x + .2 * (t[1].x - t[0].x), a = t[0].y + .2 * (t[1].y - t[0].y)) : (i = t[e].x + .2 * (t[e + 1].x - t[e - 1].x), a = t[e].y + .2 * (t[e + 1].y - t[e - 1].y)), e > t.length - 3) {
                    var c = t.length - 1;
                    r = t[c].x - .2 * (t[c].x - t[c - 1].x), o = t[c].y - .2 * (t[c].y - t[c - 1].y)
                } else r = t[e + 1].x - .2 * (t[e + 2].x - t[e].x), o = t[e + 1].y - .2 * (t[e + 2].y - t[e].y);
                return n(t, e + 1) && (o = t[e + 1].y), n(t, e) && (a = t[e].y), {
                    ctrA: {x: i, y: a},
                    ctrB: {x: r, y: o}
                }
            }

            function d(t, e, n) {
                return {x: n.x + t, y: n.y - e}
            }

            function f(t, e) {
                if (e) for (; Jt.isCollision(t, e);) 0 < t.start.x ? t.start.y-- : 0 > t.start.x ? t.start.y++ : 0 < t.start.y ? t.start.y++ : t.start.y--;
                return t
            }

            function p(t, e, n) {
                var i = 0;
                return t.map(function (t) {
                    if (t.color || (t.color = n.colors[i], i = (i + 1) % n.colors.length), t.index || (t.index = 0), t.type || (t.type = e.type), void 0 === t.show && (t.show = !0), t.type || (t.type = e.type), t.pointShape || (t.pointShape = "circle"), !t.legendShape) switch (t.type) {
                        case"line":
                            t.legendShape = "line";
                            break;
                        case"column":
                            t.legendShape = "rect";
                            break;
                        case"area":
                            t.legendShape = "triangle";
                            break;
                        default:
                            t.legendShape = "circle"
                    }
                    return t
                })
            }

            function h(t, e) {
                var n = 0, i = e - t;
                return n = 1e4 <= i ? 1e3 : 1e3 <= i ? 100 : 100 <= i ? 10 : 10 <= i ? 5 : 1 <= i ? 1 : .1 <= i ? .1 : .01 <= i ? .01 : .001 <= i ? .001 : 1e-4 <= i ? 1e-4 : 1e-5 <= i ? 1e-5 : 1e-6, {
                    minRange: r(t, "lower", n),
                    maxRange: r(e, "upper", n)
                }
            }

            function g(t) {
                var e = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : Gt.fontSize;
                t = (t += "").split("");
                for (var n, i = 0, a = 0; a < t.length; a++) n = t[a], i += /[a-zA-Z]/.test(n) ? 7 : /[0-9]/.test(n) ? 5.5 : /\./.test(n) ? 2.7 : /-/.test(n) ? 3.25 : /[\u4e00-\u9fa5]/.test(n) ? 10 : /\(|\)/.test(n) ? 3.73 : /\s/.test(n) ? 2.5 : /%/.test(n) ? 8 : 10;
                return i * e / 10
            }

            function v(t) {
                return t.reduce(function (t, e) {
                    return (t.data ? t.data : t).concat(e.data)
                }, [])
            }

            function y(t, e) {
                for (var n = Array(e), i = 0; i < n.length; i++) n[i] = 0;
                for (var a = 0; a < t.length; a++) for (i = 0; i < n.length; i++) n[i] += t[a].data[i];
                return t.reduce(function (t, e) {
                    return (t.data ? t.data : t).concat(e.data).concat(n)
                }, [])
            }

            function m(t, e, n) {
                var i, a;
                return t.clientX ? e.rotate ? (a = e.height - t.clientX * e.pixelRatio, i = (t.pageY - n.currentTarget.offsetTop - e.height / e.pixelRatio / 2 * (e.pixelRatio - 1)) * e.pixelRatio) : (i = t.clientX * e.pixelRatio, a = (t.pageY - n.currentTarget.offsetTop - e.height / e.pixelRatio / 2 * (e.pixelRatio - 1)) * e.pixelRatio) : e.rotate ? (a = e.height - t.x * e.pixelRatio, i = t.y * e.pixelRatio) : (i = t.x * e.pixelRatio, a = t.y * e.pixelRatio), {
                    x: i,
                    y: a
                }
            }

            function _(t, e) {
                for (var n, i = [], a = 0; a < t.length; a++) if (null !== (n = t[a]).data[e] && void 0 !== n.data[e] && n.show) {
                    var r = {};
                    r.color = n.color, r.type = n.type, r.style = n.style, r.pointShape = n.pointShape, r.disableLegend = n.disableLegend, r.name = n.name, r.show = n.show, r.data = n.format ? n.format(n.data[e]) : n.data[e], i.push(r)
                }
                return i
            }

            function x(t) {
                var e = t.map(function (t) {
                    return g(t)
                });
                return Math.max.apply(null, e)
            }

            function b(t) {
                for (var e = Math.PI, n = [], i = 0; i < t; i++) n.push(2 * e / t * i);
                return n.map(function (t) {
                    return -1 * t + e / 2
                })
            }

            function P(t, e, n, i) {
                for (var a, r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : {}, o = t.map(function (t) {
                    return {text: r.format ? r.format(t, i[n]) : t.name + ": " + t.data, color: t.color}
                }), c = [], l = {
                    x: 0,
                    y: 0
                }, s = 0; s < e.length; s++) void 0 !== (a = e[s])[n] && null !== a[n] && c.push(a[n]);
                for (var u, d = 0; d < c.length; d++) u = c[d], l.x = Math.round(u.x), l.y += u.y;
                return l.y /= c.length, {textList: o, offset: l}
            }

            function w(t, e, n, i) {
                var a = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : {}, r = t.map(function (t) {
                    return {
                        text: a.format ? a.format(t, i[n]) : t.name + ": " + t.data,
                        color: t.color,
                        disableLegend: !!t.disableLegend
                    }
                });
                r = r.filter(function (t) {
                    if (!0 !== t.disableLegend) return t
                });
                for (var o, c = [], l = {
                    x: 0,
                    y: 0
                }, s = 0; s < e.length; s++) void 0 !== (o = e[s])[n] && null !== o[n] && c.push(o[n]);
                for (var u, d = 0; d < c.length; d++) u = c[d], l.x = Math.round(u.x), l.y += u.y;
                return l.y /= c.length, {textList: r, offset: l}
            }

            function S(t, e, n, i, a, r) {
                !(6 < arguments.length && void 0 !== arguments[6]) || arguments[6];
                var o = r.color.upFill, c = r.color.downFill, l = [o, o, c, o], s = [], u = {text: a[i], color: null};
                s.push(u), e.map(function (e) {
                    0 == i && 0 > e.data[1] - e.data[0] ? l[1] = c : (e.data[0] < t[i - 1][1] && (l[0] = c), e.data[1] < e.data[0] && (l[1] = c), e.data[2] > t[i - 1][1] && (l[2] = o), e.data[3] < t[i - 1][1] && (l[3] = c));
                    var n = {text: "开盘：" + e.data[0], color: l[0]}, a = {text: "收盘：" + e.data[1], color: l[1]},
                        r = {text: "最低：" + e.data[2], color: l[2]}, u = {text: "最高：" + e.data[3], color: l[3]};
                    s.push(n, a, r, u)
                });
                for (var d, f = [], p = {
                    x: 0,
                    y: 0
                }, h = 0; h < n.length; h++) void 0 !== (d = n[h])[i] && null !== d[i] && f.push(d[i]);
                return p.x = Math.round(f[0][0].x), {textList: s, offset: p}
            }

            function A(t) {
                for (var e = [], n = 0; n < t.length; n++) 1 == t[n].show && e.push(t[n]);
                return e
            }

            function T(t, e, n, i) {
                var a = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 0, r = -1, o = 0;
                return ("line" == n.type || "area" == n.type) && "justify" == n.xAxis.boundaryGap && (o = n.chartData.eachSpacing / 2), O(t, n) && e.forEach(function (e, n) {
                    t.x + a + o > e && (r = n)
                }), r
            }

            function k(t, e) {
                var n = -1;
                if (M(t, e.area)) {
                    for (var i, a = e.points, r = -1, o = 0, c = a.length; o < c; o++) {
                        i = a[o];
                        for (var l = 0; l < i.length; l++) {
                            r += 1;
                            var s = i[l].area;
                            if (t.x > s[0] && t.x < s[2] && t.y > s[1] && t.y < s[3]) {
                                n = r;
                                break
                            }
                        }
                    }
                    return n
                }
                return n
            }

            function M(t, e) {
                return t.x > e.start.x && t.x < e.end.x && t.y > e.start.y && t.y < e.end.y
            }

            function O(t, e) {
                return t.x <= e.width - e.area[1] + 10 && t.x >= e.area[3] - 10 && t.y >= e.area[0] && t.y <= e.height - e.area[2]
            }

            function C(t, e, n) {
                var i = Math.PI, a = 2 * i / n, r = -1;
                if (F(t, e.center, e.radius)) {
                    var o = function (t) {
                        return 0 > t && (t += 2 * i), t > 2 * i && (t -= 2 * i), t
                    }, c = Math.atan2(e.center.y - t.y, t.x - e.center.x);
                    0 > (c *= -1) && (c += 2 * i), e.angleList.map(function (t) {
                        return t = o(-1 * t)
                    }).forEach(function (t, e) {
                        var n = o(t - a / 2), l = o(t + a / 2);
                        l < n && (l += 2 * i), (c >= n && c <= l || c + 2 * i >= n && c + 2 * i <= l) && (r = e)
                    })
                }
                return r
            }

            function L(t, e) {
                for (var n, i = -1, a = 0, r = e.series.length; a < r; a++) if (n = e.series[a], t.x > n.funnelArea[0] && t.x < n.funnelArea[2] && t.y > n.funnelArea[1] && t.y < n.funnelArea[3]) {
                    i = a;
                    break
                }
                return i
            }

            function D(t, e) {
                for (var n, i = -1, a = 0, r = e.length; a < r; a++) if (n = e[a], t.x > n.area[0] && t.x < n.area[2] && t.y > n.area[1] && t.y < n.area[3]) {
                    i = a;
                    break
                }
                return i
            }

            function $(t, e) {
                for (var n, i = -1, a = e.chartData.mapData, r = e.series, o = Ft(t.y, t.x, a.bounds, a.scale, a.xoffset, a.yoffset), c = [o.x, o.y], l = 0, s = r.length; l < s; l++) if (n = r[l].geometry.coordinates, Et(c, n)) {
                    i = l;
                    break
                }
                return i
            }

            function j(t, e) {
                var n = -1;
                if (F(t, e.center, e.radius)) {
                    var i = Math.atan2(e.center.y - t.y, t.x - e.center.x);
                    i = -i;
                    for (var a, r = 0, o = e.series.length; r < o; r++) if (a = e.series[r], l(i, a._start_, a._start_ + 2 * a._proportion_ * Math.PI)) {
                        n = r;
                        break
                    }
                }
                return n
            }

            function F(t, e, n) {
                var i = Math.pow;
                return i(t.x - e.x, 2) + i(t.y - e.y, 2) <= i(n, 2)
            }

            function R(t) {
                var e = [], n = [];
                return t.forEach(function (t) {
                    null === t ? (n.length && e.push(n), n = []) : n.push(t)
                }), n.length && e.push(n), e
            }

            function E(t, e, n, i) {
                var a = Math.max, r = Math.floor, o = {
                    area: {
                        start: {x: 0, y: 0},
                        end: {x: 0, y: 0},
                        width: 0,
                        height: 0,
                        wholeWidth: 0,
                        wholeHeight: 0
                    }, points: [], widthArr: [], heightArr: []
                };
                if (!1 === e.legend.show) return i.legendData = o, o;
                var c = e.legend.padding, l = e.legend.margin, s = e.legend.fontSize, u = 15 * e.pixelRatio,
                    d = 5 * e.pixelRatio, f = a(e.legend.lineHeight * e.pixelRatio, s);
                if ("top" == e.legend.position || "bottom" == e.legend.position) {
                    for (var p = [], h = 0, v = [], y = [], m = 0; m < t.length; m++) {
                        var _ = t[m], x = u + d + g(_.name || "undefined", s) + e.legend.itemGap;
                        h + x > e.width - e.padding[1] - e.padding[3] ? (p.push(y), v.push(h - e.legend.itemGap), h = x, y = [_]) : (h += x, y.push(_))
                    }
                    if (y.length) {
                        p.push(y), v.push(h - e.legend.itemGap), o.widthArr = v;
                        var b = a.apply(null, v);
                        switch (e.legend.float) {
                            case"left":
                                o.area.start.x = e.padding[3], o.area.end.x = e.padding[3] + 2 * c;
                                break;
                            case"right":
                                o.area.start.x = e.width - e.padding[1] - b - 2 * c, o.area.end.x = e.width - e.padding[1];
                                break;
                            default:
                                o.area.start.x = (e.width - b) / 2 - c, o.area.end.x = (e.width + b) / 2 + c
                        }
                        o.area.width = b + 2 * c, o.area.wholeWidth = b + 2 * c, o.area.height = p.length * f + 2 * c, o.area.wholeHeight = p.length * f + 2 * c + 2 * l, o.points = p
                    }
                } else {
                    var P = t.length, w = e.height - e.padding[0] - e.padding[2] - 2 * l - 2 * c,
                        S = Math.min(r(w / f), P);
                    switch (o.area.height = S * f + 2 * c, o.area.wholeHeight = S * f + 2 * c, e.legend.float) {
                        case"top":
                            o.area.start.y = e.padding[0] + l, o.area.end.y = e.padding[0] + l + o.area.height;
                            break;
                        case"bottom":
                            o.area.start.y = e.height - e.padding[2] - l - o.area.height, o.area.end.y = e.height - e.padding[2] - l;
                            break;
                        default:
                            o.area.start.y = (e.height - o.area.height) / 2, o.area.end.y = (e.height + o.area.height) / 2
                    }
                    for (var A, T = 0 == P % S ? P / S : r(P / S + 1), k = [], M = 0; M < T; M++) A = t.slice(M * S, M * S + S), k.push(A);
                    if (o.points = k, k.length) {
                        for (var O = 0; O < k.length; O++) {
                            for (var C, L = k[O], D = 0, $ = 0; $ < L.length; $++) (C = u + d + g(L[$].name || "undefined", s) + e.legend.itemGap) > D && (D = C);
                            o.widthArr.push(D), o.heightArr.push(L.length * f + 2 * c)
                        }
                        for (var j = 0, F = 0; F < o.widthArr.length; F++) j += o.widthArr[F];
                        o.area.width = j - e.legend.itemGap + 2 * c, o.area.wholeWidth = o.area.width + c
                    }
                }
                switch (e.legend.position) {
                    case"top":
                        o.area.start.y = e.padding[0] + l, o.area.end.y = e.padding[0] + l + o.area.height;
                        break;
                    case"bottom":
                        o.area.start.y = e.height - e.padding[2] - o.area.height - l, o.area.end.y = e.height - e.padding[2] - l;
                        break;
                    case"left":
                        o.area.start.x = e.padding[3], o.area.end.x = e.padding[3] + o.area.width;
                        break;
                    case"right":
                        o.area.start.x = e.width - e.padding[1] - o.area.width, o.area.end.x = e.width - e.padding[1]
                }
                return i.legendData = o, o
            }

            function I(t, e, n, i) {
                var a = {angle: 0, xAxisHeight: n.xAxisHeight}, r = t.map(function (t) {
                    return g(t)
                }), o = Math.max.apply(this, r);
                return 1 == e.xAxis.rotateLabel && o + 2 * n.xAxisTextPadding > i && (a.angle = 45 * Math.PI / 180, a.xAxisHeight = 2 * n.xAxisTextPadding + o * Math.sin(a.angle)), a
            }

            function z(t, e, n, i, a) {
                var r = Math.max, o = 5 < arguments.length && void 0 !== arguments[5] ? arguments[5] : 1,
                    c = a.extra.radar || {};
                c.max = c.max || 0;
                for (var l = r(c.max, r.apply(null, v(i))), s = [], u = 0; u < i.length; u++) !function (a) {
                    var r = i[a], c = {};
                    c.color = r.color, c.legendShape = r.legendShape, c.pointShape = r.pointShape, c.data = [], r.data.forEach(function (i, a) {
                        var r = {};
                        r.angle = t[a], r.proportion = i / l, r.position = d(n * r.proportion * o * Math.cos(r.angle), n * r.proportion * o * Math.sin(r.angle), e), c.data.push(r)
                    }), s.push(c)
                }(u);
                return s
            }

            function B(t, e) {
                for (var n, i = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : 1, a = 0, r = 0, o = 0; o < t.length; o++) (n = t[o]).data = null === n.data ? 0 : n.data, a += n.data;
                for (var c, l = 0; l < t.length; l++) (c = t[l]).data = null === c.data ? 0 : c.data, c._proportion_ = 0 === a ? 1 / t.length * i : c.data / a * i, c._radius_ = e;
                for (var s, u = 0; u < t.length; u++) (s = t[u])._start_ = r, r += 2 * s._proportion_ * Math.PI;
                return t
            }

            function N(t, e) {
                var n = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : 1;
                t = t.sort(function (t, e) {
                    return parseInt(e.data) - parseInt(t.data)
                });
                for (var i = 0; i < t.length; i++) t[i].radius = t[i].data / t[0].data * e * n, t[i]._proportion_ = t[i].data / t[0].data;
                return t.reverse()
            }

            function W(t, e, n, i) {
                for (var a, r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, o = 0, c = 0, l = [], s = 0; s < t.length; s++) (a = t[s]).data = null === a.data ? 0 : a.data, o += a.data, l.push(a.data);
                for (var u, d = Math.min.apply(null, l), f = Math.max.apply(null, l), p = 0; p < t.length; p++) (u = t[p]).data = null === u.data ? 0 : u.data, 0 === o || "area" == e ? (u._proportion_ = u.data / o * r, u._rose_proportion_ = 1 / t.length * r) : (u._proportion_ = u.data / o * r, u._rose_proportion_ = u.data / o * r), u._radius_ = n + (i - n) * ((u.data - d) / (f - d));
                for (var h, g = 0; g < t.length; g++) (h = t[g])._start_ = c, c += 2 * h._rose_proportion_ * Math.PI;
                return t
            }

            function H(t, e) {
                var n = 2 < arguments.length && void 0 !== arguments[2] ? arguments[2] : 1;
                1 == n && (n = .999999);
                for (var i, a = 0; a < t.length; a++) {
                    (i = t[a]).data = null === i.data ? 0 : i.data;
                    var r = void 0;
                    r = "circle" == e.type ? 2 : e.endAngle < e.startAngle ? 2 + e.endAngle - e.startAngle : e.startAngle - e.endAngle, i._proportion_ = r * i.data * n + e.startAngle, 2 <= i._proportion_ && (i._proportion_ %= 2)
                }
                return t
            }

            function U(t, e, n) {
                for (var i = e, a = 0; a < t.length; a++) t[a].value = null === t[a].value ? 0 : t[a].value, t[a]._startAngle_ = i, t[a]._endAngle_ = (e - n + 1) * t[a].value + e, 2 <= t[a]._endAngle_ && (t[a]._endAngle_ %= 2), i = t[a]._endAngle_;
                return t
            }

            function V(t, e, n) {
                for (var i, a = 3 < arguments.length && void 0 !== arguments[3] ? arguments[3] : 1, r = 0; r < t.length; r++) {
                    if (i = t[r], i.data = null === i.data ? 0 : i.data, "auto" == n.pointer.color) {
                        for (var o = 0; o < e.length; o++) if (i.data <= e[o].value) {
                            i.color = e[o].color;
                            break
                        }
                    } else i.color = n.pointer.color;
                    var c = n.startAngle - n.endAngle + 1;
                    i._endAngle_ = c * i.data + n.startAngle, i._oldAngle_ = n.oldAngle, n.oldAngle < n.endAngle && (i._oldAngle_ += 2), i._proportion_ = i.data >= n.oldData ? (i._endAngle_ - i._oldAngle_) * a + n.oldAngle : i._oldAngle_ - (i._oldAngle_ - i._endAngle_) * a, 2 <= i._proportion_ && (i._proportion_ %= 2)
                }
                return t
            }

            function q(t) {
                t = B(t);
                for (var e = 0, n = 0; n < t.length; n++) {
                    var i = t[n],
                        a = i.format ? i.format(+i._proportion_.toFixed(2)) : Jt.toFixed(100 * i._proportion_) + "%";
                    e = Math.max(e, g(a))
                }
                return e
            }

            function G(t, e, n, i, a, r) {
                return t.map(function (t) {
                    return null === t ? null : (t.width = Math.ceil((e - 2 * a.columePadding) / n), r.extra.column && r.extra.column.width && 0 < +r.extra.column.width && (t.width = Math.min(t.width, +r.extra.column.width)), 0 >= t.width && (t.width = 1), t.x += (i + .5 - n / 2) * t.width, t)
                })
            }

            function X(t, e, n, i, a, r, o) {
                return t.map(function (t) {
                    return null === t ? null : (t.width = Math.ceil((e - 2 * a.columePadding) / 2), r.extra.column && r.extra.column.width && 0 < +r.extra.column.width && (t.width = Math.min(t.width, +r.extra.column.width)), 0 < i && (t.width -= 2 * o), t)
                })
            }

            function J(t, e, n, i, a, r) {
                return t.map(function (t) {
                    return null === t ? null : (t.width = Math.ceil((e - 2 * a.columePadding) / 2), r.extra.column && r.extra.column.width && 0 < +r.extra.column.width && (t.width = Math.min(t.width, +r.extra.column.width)), t)
                })
            }

            function Q(t, e) {
                var n = e.width - e.area[1] - e.area[3],
                    i = e.enableScroll ? Math.min(e.xAxis.itemCount, t.length) : t.length;
                ("line" == e.type || "area" == e.type) && 1 < i && "justify" == e.xAxis.boundaryGap && (i -= 1);
                var a = n / i, r = [], o = e.area[3], c = e.width - e.area[1];
                return t.forEach(function (t, e) {
                    r.push(o + e * a)
                }), "justify" !== e.xAxis.boundaryGap && (!0 === e.enableScroll ? r.push(o + t.length * a) : r.push(c)), {
                    xAxisPoints: r,
                    startX: o,
                    endX: c,
                    eachSpacing: a
                }
            }

            function K(t, e, n, i, a, r) {
                var o = Math.round, c = 7 < arguments.length && void 0 !== arguments[7] ? arguments[7] : 1, l = [],
                    s = r.height - r.area[0] - r.area[2];
                return t.forEach(function (t, u) {
                    if (null === t) l.push(null); else {
                        var d = [];
                        t.forEach(function (t) {
                            var l = {x: i[u] + o(a / 2)}, f = t.value || t, p = s * (f - e) / (n - e);
                            p *= c, l.y = r.height - o(p) - r.area[2], d.push(l)
                        }), l.push(d)
                    }
                }), l
            }

            function Y(t, e, n, a, r, o) {
                var c = Math.round, l = 7 < arguments.length && void 0 !== arguments[7] ? arguments[7] : 1,
                    s = "center";
                ("line" == o.type || "area" == o.type) && (s = o.xAxis.boundaryGap);
                var u = [], d = o.height - o.area[0] - o.area[2];
                return t.forEach(function (t, f) {
                    if (null === t) u.push(null); else {
                        var p = {};
                        p.color = t.color, p.x = a[f], "center" == s && (p.x += c(r / 2));
                        var h = t;
                        "object" == i(t) && null != t && (h = t.value);
                        var g = d * (h - e) / (n - e);
                        g *= l, p.y = o.height - c(g) - o.area[2], u.push(p)
                    }
                }), u
            }

            function Z(t, e, n, i, a, r, o, c, l) {
                var s = Math.round, u = 9 < arguments.length && void 0 !== arguments[9] ? arguments[9] : 1, d = [],
                    f = r.height - r.area[0] - r.area[2];
                return t.forEach(function (t, o) {
                    if (null === t) d.push(null); else {
                        var p = {color: t.color, x: i[o] + s(a / 2)};
                        if (0 < c) {
                            for (var h = 0, g = 0; g <= c; g++) h += l[g].data[o];
                            var v = f * (h - e) / (n - e), y = f * (h - t - e) / (n - e)
                        } else h = t, v = f * (h - e) / (n - e), y = 0;
                        var m = y;
                        v *= u, m *= u, p.y = r.height - s(v) - r.area[2], p.y0 = r.height - s(m) - r.area[2], d.push(p)
                    }
                }), d
            }

            function tt(t, e, n, a) {
                var r = Math.min, o = Math.max, c = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : -1,
                    l = [];
                ("stack" == a ? y(t, e.categories.length) : v(t)).filter(function (t) {
                    return "object" == i(t) && null !== t ? t.constructor == Array ? null !== t : null !== t.value : null !== t
                }).map(function (t) {
                    "object" == i(t) ? t.constructor == Array ? t.map(function (t) {
                        l.push(t)
                    }) : l.push(t.value) : l.push(t)
                });
                var s = 0, u = 0;
                0 < l.length && (s = r.apply(this, l), u = o.apply(this, l)), -1 < c ? ("number" == typeof e.yAxis.data[c].min && (s = r(e.yAxis.data[c].min, s)), "number" == typeof e.yAxis.data[c].max && (u = o(e.yAxis.data[c].max, u))) : ("number" == typeof e.yAxis.min && (s = r(e.yAxis.min, s)), "number" == typeof e.yAxis.max && (u = o(e.yAxis.max, u))), s === u && (u += u || 10);
                for (var d = h(s, u), f = d.minRange, p = [], g = (d.maxRange - f) / n.yAxisSplit, m = 0; m <= n.yAxisSplit; m++) p.push(f + g * m);
                return p.reverse()
            }

            function et(t, e, n) {
                var i = Math.max, a = Xt({}, {type: ""}, e.extra.column), r = e.yAxis.data.length, o = Array(r);
                if (0 < r) {
                    for (var c = 0; c < r; c++) {
                        o[c] = [];
                        for (var l = 0; l < t.length; l++) t[l].index == c && o[c].push(t[l])
                    }
                    for (var s, u = Array(r), d = Array(r), f = Array(r), p = 0; p < r; p++) !function (t, r) {
                        r = e.yAxis.data[t], 1 == e.yAxis.disabled && (r.disabled = !0), u[t] = tt(o[t], e, n, a.type, t);
                        var c = r.fontSize || n.fontSize;
                        f[t] = {position: r.position ? r.position : "left", width: 0}, d[t] = u[t].map(function (e) {
                            return s = r, e = Jt.toFixed(e, 6), e = r.format ? r.format(+e) : e, f[t].width = i(f[t].width, g(e, c) + 5), e
                        });
                        var l = r.calibration ? 4 * e.pixelRatio : 0;
                        f[t].width += l + 3 * e.pixelRatio, !0 === r.disabled && (f[t].width = 0), s = r
                    }(p, s)
                } else {
                    d = [,], f = [,], (u = [,])[0] = tt(t, e, n, a.type), f[0] = {position: "left", width: 0};
                    var h = e.yAxis.fontSize || n.fontSize;
                    d[0] = u[0].map(function (t) {
                        return t = Jt.toFixed(t, 6), t = e.yAxis.format ? e.yAxis.format(+t) : t, f[0].width = i(f[0].width, g(t, h) + 5), t
                    }), f[0].width += 3 * e.pixelRatio, !0 === e.yAxis.disabled ? (f[0] = {
                        position: "left",
                        width: 0
                    }, e.yAxis.data[0] = {disabled: !0}) : e.yAxis.data[0] = {
                        disabled: !1,
                        position: "left",
                        max: e.yAxis.max,
                        min: e.yAxis.min,
                        format: e.yAxis.format
                    }
                }
                return {rangesFormat: d, ranges: u, yAxisWidth: f}
            }

            function nt(t, e, n) {
                for (var i = [].concat(n.chartData.yAxisData.ranges), a = n.height - n.area[0] - n.area[2], r = n.area[0], o = [], c = 0; c < i.length; c++) {
                    var l = i[c].shift(), s = l - (l - i[c].pop()) * (t - r) / a;
                    s = n.yAxis.data[c].format ? n.yAxis.data[c].format(+s) : s.toFixed(0), o.push(s + "")
                }
                return o
            }

            function it(t, e) {
                for (var n, i, a = e.height - e.area[0] - e.area[2], r = 0; r < t.length; r++) {
                    t[r].yAxisIndex = t[r].yAxisIndex ? t[r].yAxisIndex : 0;
                    var o = [].concat(e.chartData.yAxisData.ranges[t[r].yAxisIndex]);
                    n = o.pop(), i = o.shift();
                    var c = a * (t[r].value - n) / (i - n);
                    t[r].y = e.height - Math.round(c) - e.area[2]
                }
                return t
            }

            function at(t, e) {
                var n = Math.PI;
                !0 === e.rotateLock ? !0 !== e._rotate_ && (t.translate(e.height, 0), t.rotate(90 * n / 180), e._rotate_ = !0) : (t.translate(e.height, 0), t.rotate(90 * n / 180))
            }

            function rt(t, e, n, i, a) {
                i.beginPath(), i.setStrokeStyle("#ffffff"), i.setLineWidth(1 * a.pixelRatio), i.setFillStyle(e), "diamond" === n ? t.forEach(function (t) {
                    null !== t && (i.moveTo(t.x, t.y - 4.5), i.lineTo(t.x - 4.5, t.y), i.lineTo(t.x, t.y + 4.5), i.lineTo(t.x + 4.5, t.y), i.lineTo(t.x, t.y - 4.5))
                }) : "circle" === n ? t.forEach(function (t) {
                    null !== t && (i.moveTo(t.x + 3.5 * a.pixelRatio, t.y), i.arc(t.x, t.y, 4 * a.pixelRatio, 0, 2 * Math.PI, !1))
                }) : "rect" === n ? t.forEach(function (t) {
                    null !== t && (i.moveTo(t.x - 3.5, t.y - 3.5), i.rect(t.x - 3.5, t.y - 3.5, 7, 7))
                }) : "triangle" == n && t.forEach(function (t) {
                    null !== t && (i.moveTo(t.x, t.y - 4.5), i.lineTo(t.x - 4.5, t.y + 4.5), i.lineTo(t.x + 4.5, t.y + 4.5), i.lineTo(t.x, t.y - 4.5))
                }), i.closePath(), i.fill(), i.stroke()
            }

            function ot(t, e, n, i) {
                var a = t.title.fontSize || e.titleFontSize, r = t.subtitle.fontSize || e.subtitleFontSize,
                    o = t.title.name || "", c = t.subtitle.name || "", l = t.title.color || e.titleColor,
                    s = t.subtitle.color || e.subtitleColor, u = o ? a : 0, d = c ? r : 0;
                if (c) {
                    var f = g(c, r), p = i.x - f / 2 + (t.subtitle.offsetX || 0),
                        h = i.y + r / 2 + (t.subtitle.offsetY || 0);
                    o && (h += (u + 5) / 2), n.beginPath(), n.setFontSize(r), n.setFillStyle(s), n.fillText(c, p, h), n.closePath(), n.stroke()
                }
                if (o) {
                    var v = g(o, a), y = i.x - v / 2 + (t.title.offsetX || 0), m = i.y + a / 2 + (t.title.offsetY || 0);
                    c && (m -= (d + 5) / 2), n.beginPath(), n.setFontSize(a), n.setFillStyle(l), n.fillText(o, y, m), n.closePath(), n.stroke()
                }
            }

            function ct(t, e, n, a) {
                var r = e.data;
                t.forEach(function (t, o) {
                    if (null !== t) {
                        a.beginPath(), a.setFontSize(e.textSize || n.fontSize), a.setFillStyle(e.textColor || "#666666");
                        var c = r[o];
                        "object" == i(r[o]) && null !== r[o] && (c = r[o].value);
                        var l = e.format ? e.format(c) : c;
                        a.fillText(l + "", t.x - g(l, e.textSize || n.fontSize) / 2, t.y - 2), a.closePath(), a.stroke()
                    }
                })
            }

            function lt(t, e, n, i, a, r) {
                var o = Math.PI;
                e -= t.width / 2 + a.gaugeLabelTextMargin;
                for (var c = (t.startAngle - t.endAngle + 1) / t.splitLine.splitNumber, l = (t.endNumber - t.startNumber) / t.splitLine.splitNumber, s = t.startAngle, u = t.startNumber, d = 0; d < t.splitLine.splitNumber + 1; d++) {
                    var f = {x: e * Math.cos(s * o), y: e * Math.sin(s * o)}, p = t.labelFormat ? t.labelFormat(u) : u;
                    f.x += n.x - g(p) / 2, f.y += n.y;
                    var h = f.x, v = f.y;
                    r.beginPath(), r.setFontSize(a.fontSize), r.setFillStyle(t.labelColor || "#666666"), r.fillText(p, h, v + a.fontSize / 2), r.closePath(), r.stroke(), 2 <= (s += c) && (s %= 2), u += l
                }
            }

            function st(t, e, n, i, a, r) {
                var o = i.extra.radar || {};
                e += a.radarLabelTextMargin, t.forEach(function (t, c) {
                    var l = {x: e * Math.cos(t), y: e * Math.sin(t)}, s = d(l.x, l.y, n), u = s.x, f = s.y;
                    Jt.approximatelyEqual(l.x, 0) ? u -= g(i.categories[c] || "") / 2 : 0 > l.x && (u -= g(i.categories[c] || "")), r.beginPath(), r.setFontSize(a.fontSize), r.setFillStyle(o.labelColor || "#666666"), r.fillText(i.categories[c] || "", u, f + a.fontSize / 2), r.closePath(), r.stroke()
                })
            }

            function ut(t, e, n, i, a, r) {
                for (var o = Math.cos, c = Math.sin, l = Math.min, s = Math.max, u = Math.PI, p = n.pieChartLinePadding, h = [], v = null, y = t.map(function (t) {
                    var e = t.format ? t.format(+t._proportion_.toFixed(2)) : Jt.toFixed(100 * t._proportion_.toFixed(4)) + "%";
                    return t._rose_proportion_ && (t._proportion_ = t._rose_proportion_), {
                        arc: 2 * u - (t._start_ + 2 * u * t._proportion_ / 2),
                        text: e,
                        color: t.color,
                        radius: t._radius_,
                        textColor: t.textColor,
                        textSize: t.textSize
                    }
                }), m = 0; m < y.length; m++) {
                    var _ = y[m], x = o(_.arc) * (_.radius + p), b = c(_.arc) * (_.radius + p), P = o(_.arc) * _.radius,
                        w = c(_.arc) * _.radius, S = 0 <= x ? x + n.pieChartTextPadding : x - n.pieChartTextPadding,
                        A = b, T = g(_.text), k = A;
                    v && Jt.isSameXCoordinateArea(v.start, {x: S}) && (k = 0 < S ? l(A, v.start.y) : 0 > x ? s(A, v.start.y) : 0 < A ? s(A, v.start.y) : l(A, v.start.y)), 0 > S && (S -= T), v = f({
                        lineStart: {
                            x: P,
                            y: w
                        },
                        lineEnd: {x: x, y: b},
                        start: {x: S, y: k},
                        width: T,
                        height: n.fontSize,
                        text: _.text,
                        color: _.color,
                        textColor: _.textColor,
                        textSize: _.textSize
                    }, v), h.push(v)
                }
                for (var M = 0; M < h.length; M++) {
                    var O = h[M], C = d(O.lineStart.x, O.lineStart.y, r), L = d(O.lineEnd.x, O.lineEnd.y, r),
                        D = d(O.start.x, O.start.y, r);
                    i.setLineWidth(1 * e.pixelRatio), i.setFontSize(n.fontSize), i.beginPath(), i.setStrokeStyle(O.color), i.setFillStyle(O.color), i.moveTo(C.x, C.y);
                    var $ = 0 > O.start.x ? D.x + O.width : D.x, j = 0 > O.start.x ? D.x - 5 : D.x + 5;
                    i.quadraticCurveTo(L.x, L.y, $, D.y), i.moveTo(C.x, C.y), i.stroke(), i.closePath(), i.beginPath(), i.moveTo(D.x + O.width, D.y), i.arc($, D.y, 2, 0, 2 * u), i.closePath(), i.fill(), i.beginPath(), i.setFontSize(O.textSize || n.fontSize), i.setFillStyle(O.textColor || "#666666"), i.fillText(O.text, j, D.y + 3), i.closePath(), i.stroke(), i.closePath()
                }
            }

            function dt(t, e, n, i) {
                var r = e.extra.tooltip || {};
                r.gridType = null == r.gridType ? "solid" : r.gridType, r.dashLength = null == r.dashLength ? 4 : r.dashLength;
                var o = e.area[0], c = e.height - e.area[2];
                if ("dash" == r.gridType && i.setLineDash([r.dashLength, r.dashLength]), i.setStrokeStyle(r.gridColor || "#cccccc"), i.setLineWidth(1 * e.pixelRatio), i.beginPath(), i.moveTo(t, o), i.lineTo(t, c), i.stroke(), i.setLineDash([]), r.xAxisLabel) {
                    var l = e.categories[e.tooltip.index];
                    i.setFontSize(n.fontSize);
                    var s = g(l, n.fontSize), u = t - .5 * s, d = c;
                    i.beginPath(), i.setFillStyle(a(r.labelBgColor || n.toolTipBackground, r.labelBgOpacity || n.toolTipOpacity)), i.setStrokeStyle(r.labelBgColor || n.toolTipBackground), i.setLineWidth(1 * e.pixelRatio), i.rect(u - n.toolTipPadding, d, s + 2 * n.toolTipPadding, n.fontSize + 2 * n.toolTipPadding), i.closePath(), i.stroke(), i.fill(), i.beginPath(), i.setFontSize(n.fontSize), i.setFillStyle(r.labelFontColor || n.fontColor), i.fillText(l + "", u, d + n.toolTipPadding + n.fontSize), i.closePath(), i.stroke()
                }
            }

            function ft(t, e, n) {
                for (var i, r = Xt({}, {
                    type: "solid",
                    dashLength: 4,
                    data: []
                }, t.extra.markLine), o = t.area[3], c = t.width - t.area[1], l = it(r.data, t), s = 0; s < l.length; s++) if (i = Xt({}, {
                    lineColor: "#DE4A42",
                    showLabel: !1,
                    labelFontColor: "#666666",
                    labelBgColor: "#DFE8FF",
                    labelBgOpacity: .8,
                    yAxisIndex: 0
                }, l[s]), "dash" == r.type && n.setLineDash([r.dashLength, r.dashLength]), n.setStrokeStyle(i.lineColor), n.setLineWidth(1 * t.pixelRatio), n.beginPath(), n.moveTo(o, i.y), n.lineTo(c, i.y), n.stroke(), n.setLineDash([]), i.showLabel) {
                    var u = t.yAxis.format ? t.yAxis.format(+i.value) : i.value;
                    n.setFontSize(e.fontSize);
                    var d = g(u, e.fontSize), f = t.padding[3] + e.yAxisTitleWidth - e.toolTipPadding,
                        p = Math.max(t.area[3], d + 2 * e.toolTipPadding) - f, h = i.y;
                    n.setFillStyle(a(i.labelBgColor, i.labelBgOpacity)), n.setStrokeStyle(i.labelBgColor), n.setLineWidth(1 * t.pixelRatio), n.beginPath(), n.rect(f, h - .5 * e.fontSize - e.toolTipPadding, p, e.fontSize + 2 * e.toolTipPadding), n.closePath(), n.stroke(), n.fill(), n.beginPath(), n.setFontSize(e.fontSize), n.setFillStyle(i.labelFontColor), n.fillText(u + "", f + (p - d) / 2, h + .5 * e.fontSize), n.stroke()
                }
            }

            function pt(t, e, n, i) {
                var r = Math.max, o = Xt({}, {gridType: "solid", dashLength: 4}, t.extra.tooltip), c = t.area[3],
                    l = t.width - t.area[1];
                if ("dash" == o.gridType && n.setLineDash([o.dashLength, o.dashLength]), n.setStrokeStyle(o.gridColor || "#cccccc"), n.setLineWidth(1 * t.pixelRatio), n.beginPath(), n.moveTo(c, t.tooltip.offset.y), n.lineTo(l, t.tooltip.offset.y), n.stroke(), n.setLineDash([]), o.yAxisLabel) for (var s = nt(t.tooltip.offset.y, t.series, t, e, i), u = t.chartData.yAxisData.yAxisWidth, d = t.area[3], f = t.width - t.area[1], p = 0; p < s.length; p++) {
                    n.setFontSize(e.fontSize);
                    var h = void 0, v = void 0, y = void 0, m = g(s[p], e.fontSize);
                    "left" == u[p].position ? (h = d - u[p].width, v = r(h, h + m + 2 * e.toolTipPadding)) : (h = f, v = r(h + u[p].width, h + m + 2 * e.toolTipPadding));
                    var _ = h + ((y = v - h) - m) / 2, x = t.tooltip.offset.y;
                    n.beginPath(), n.setFillStyle(a(o.labelBgColor || e.toolTipBackground, o.labelBgOpacity || e.toolTipOpacity)), n.setStrokeStyle(o.labelBgColor || e.toolTipBackground), n.setLineWidth(1 * t.pixelRatio), n.rect(h, x - .5 * e.fontSize - e.toolTipPadding, y, e.fontSize + 2 * e.toolTipPadding), n.closePath(), n.stroke(), n.fill(), n.beginPath(), n.setFontSize(e.fontSize), n.setFillStyle(o.labelFontColor || e.fontColor), n.fillText(s[p], _, x + .5 * e.fontSize), n.closePath(), n.stroke(), "left" == u[p].position ? d -= u[p].width + t.yAxis.padding : f += u[p].width + t.yAxis.padding
                }
            }

            function ht(t, e, n, i, r) {
                var o = Xt({}, {activeBgColor: "#000000", activeBgOpacity: .08}, e.extra.tooltip), c = e.area[0],
                    l = e.height - e.area[2];
                i.beginPath(), i.setFillStyle(a(o.activeBgColor, o.activeBgOpacity)), i.rect(t - r / 2, c, r, l - c), i.closePath(), i.fill()
            }

            function gt(t, e, n, i, r) {
                var o = Math.round,
                    c = Xt({}, {showBox: !0, bgColor: "#000000", bgOpacity: .7, fontColor: "#FFFFFF"}, n.extra.tooltip),
                    l = 4 * n.pixelRatio, s = 5 * n.pixelRatio, u = 8 * n.pixelRatio, d = !1;
                ("line" == n.type || "area" == n.type || "candle" == n.type || "mix" == n.type) && dt(n.tooltip.offset.x, n, i, r), (e = Xt({
                    x: 0,
                    y: 0
                }, e)).y -= 8 * n.pixelRatio;
                var f = t.map(function (t) {
                        return g(t.text, i.fontSize)
                    }), p = l + s + 4 * i.toolTipPadding + Math.max.apply(null, f),
                    h = 2 * i.toolTipPadding + t.length * i.toolTipLineHeight;
                0 == c.showBox || (e.x - Math.abs(n._scrollDistance_) + u + p > n.width && (d = !0), h + e.y > n.height && (e.y = n.height - h), r.beginPath(), r.setFillStyle(a(c.bgColor || i.toolTipBackground, c.bgOpacity || i.toolTipOpacity)), d ? (r.moveTo(e.x, e.y + 10 * n.pixelRatio), r.lineTo(e.x - u, e.y + 10 * n.pixelRatio - 5 * n.pixelRatio), r.lineTo(e.x - u, e.y), r.lineTo(e.x - u - o(p), e.y), r.lineTo(e.x - u - o(p), e.y + h), r.lineTo(e.x - u, e.y + h), r.lineTo(e.x - u, e.y + 10 * n.pixelRatio + 5 * n.pixelRatio), r.lineTo(e.x, e.y + 10 * n.pixelRatio)) : (r.moveTo(e.x, e.y + 10 * n.pixelRatio), r.lineTo(e.x + u, e.y + 10 * n.pixelRatio - 5 * n.pixelRatio), r.lineTo(e.x + u, e.y), r.lineTo(e.x + u + o(p), e.y), r.lineTo(e.x + u + o(p), e.y + h), r.lineTo(e.x + u, e.y + h), r.lineTo(e.x + u, e.y + 10 * n.pixelRatio + 5 * n.pixelRatio), r.lineTo(e.x, e.y + 10 * n.pixelRatio)), r.closePath(), r.fill(), t.forEach(function (t, n) {
                    if (null !== t.color) {
                        r.beginPath(), r.setFillStyle(t.color);
                        var a = e.x + u + 2 * i.toolTipPadding,
                            o = e.y + (i.toolTipLineHeight - i.fontSize) / 2 + i.toolTipLineHeight * n + i.toolTipPadding + 1;
                        d && (a = e.x - p - u + 2 * i.toolTipPadding), r.fillRect(a, o, l, i.fontSize), r.closePath()
                    }
                }), t.forEach(function (t, n) {
                    var a = e.x + u + 2 * i.toolTipPadding + l + s;
                    d && (a = e.x - p - u + 2 * i.toolTipPadding + +l + s);
                    var o = e.y + (i.toolTipLineHeight - i.fontSize) / 2 + i.toolTipLineHeight * n + i.toolTipPadding;
                    r.beginPath(), r.setFontSize(i.fontSize), r.setFillStyle(c.fontColor), r.fillText(t.text, a, o + i.fontSize), r.closePath(), r.stroke()
                }))
            }

            function vt(t, e, n, i) {
                var a = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, r = e.chartData.xAxisData,
                    o = r.xAxisPoints, c = r.eachSpacing,
                    l = Xt({}, {type: "group", width: c / 2, meter: {border: 4, fillColor: "#FFFFFF"}}, e.extra.column),
                    s = [];
                i.save();
                var u = -2, d = o.length + 2;
                return e._scrollDistance_ && 0 !== e._scrollDistance_ && !0 === e.enableScroll && (i.translate(e._scrollDistance_, 0), u = Math.floor(-e._scrollDistance_ / c) - 2, d = u + e.xAxis.itemCount + 4), e.tooltip && e.tooltip.textList && e.tooltip.textList.length && 1 === a && ht(e.tooltip.offset.x, e, n, i, c), t.forEach(function (r, f) {
                    var p, h, g;
                    h = (p = [].concat(e.chartData.yAxisData.ranges[r.index])).pop(), g = p.shift();
                    var v = r.data;
                    switch (l.type) {
                        case"group":
                            var y = Y(v, h, g, o, c, e, n, a), m = Z(v, h, g, o, c, e, n, f, t, a);
                            s.push(m), y = G(y, c, t.length, f, n, e);
                            for (var _, x = 0; x < y.length; x++) if (null !== (_ = y[x]) && x > u && x < d) {
                                i.beginPath(), i.setStrokeStyle(_.color || r.color), i.setLineWidth(1), i.setFillStyle(_.color || r.color);
                                var b = _.x - _.width / 2, P = e.height - _.y - e.area[2];
                                i.moveTo(b - 1, _.y), i.lineTo(b + _.width - 2, _.y), i.lineTo(b + _.width - 2, e.height - e.area[2]), i.lineTo(b, e.height - e.area[2]), i.lineTo(b, _.y), i.closePath(), i.stroke(), i.fill()
                            }
                            break;
                        case"stack":
                            y = Z(v, h, g, o, c, e, n, f, t, a), s.push(y), y = J(y, c, t.length, f, n, e, t);
                            for (var w, S = 0; S < y.length; S++) if (null !== (w = y[S]) && S > u && S < d) {
                                i.beginPath(), i.setFillStyle(w.color || r.color), b = w.x - w.width / 2 + 1, P = e.height - w.y - e.area[2];
                                var A = e.height - w.y0 - e.area[2];
                                0 < f && (P -= A), i.moveTo(b, w.y), i.fillRect(b, w.y, w.width - 2, P), i.closePath(), i.fill()
                            }
                            break;
                        case"meter":
                            if (y = Y(v, h, g, o, c, e, n, a), s.push(y), y = X(y, c, t.length, f, n, e, l.meter.border), 0 == f) for (var T, k = 0; k < y.length; k++) null !== (T = y[k]) && k > u && k < d && (i.beginPath(), i.setFillStyle(l.meter.fillColor), b = T.x - T.width / 2, P = e.height - T.y - e.area[2], i.moveTo(b, T.y), i.fillRect(b, T.y, T.width, P), i.closePath(), i.fill(), 0 < l.meter.border && (i.beginPath(), i.setStrokeStyle(r.color), i.setLineWidth(l.meter.border * e.pixelRatio), i.moveTo(b + .5 * l.meter.border, T.y + P), i.lineTo(b + .5 * l.meter.border, T.y + .5 * l.meter.border), i.lineTo(b + T.width - .5 * l.meter.border, T.y + .5 * l.meter.border), i.lineTo(b + T.width - .5 * l.meter.border, T.y + P), i.stroke())); else for (var M, O = 0; O < y.length; O++) null !== (M = y[O]) && O > u && O < d && (i.beginPath(), i.setFillStyle(M.color || r.color), b = M.x - M.width / 2, P = e.height - M.y - e.area[2], i.moveTo(b, M.y), i.fillRect(b, M.y, M.width, P), i.closePath(), i.fill())
                    }
                }), !1 !== e.dataLabel && 1 === a && t.forEach(function (r, s) {
                    var u, d, f;
                    d = (u = [].concat(e.chartData.yAxisData.ranges[r.index])).pop(), f = u.shift();
                    var p = r.data;
                    switch (l.type) {
                        case"group":
                            var h = Y(p, d, f, o, c, e, n, a);
                            ct(h = G(h, c, t.length, s, n, e), r, n, i);
                            break;
                        case"stack":
                            ct(h = Z(p, d, f, o, c, e, n, s, t, a), r, n, i);
                            break;
                        case"meter":
                            ct(h = Y(p, d, f, o, c, e, n, a), r, n, i)
                    }
                }), i.restore(), {xAxisPoints: o, calPoints: s, eachSpacing: c}
            }

            function yt(t, e, n, i, a) {
                var r = 5 < arguments.length && void 0 !== arguments[5] ? arguments[5] : 1,
                    o = Xt({}, {color: {}, average: {}}, n.extra.candle);
                o.color = Xt({}, {
                    upLine: "#f04864",
                    upFill: "#f04864",
                    downLine: "#2fc25b",
                    downFill: "#2fc25b"
                }, o.color), o.average = Xt({}, {
                    show: !1,
                    name: [],
                    day: [],
                    color: i.colors
                }, o.average), n.extra.candle = o;
                var c = n.chartData.xAxisData, l = c.xAxisPoints, s = c.eachSpacing, d = [];
                a.save();
                var f = -2, p = l.length + 2, h = 0, g = n.width + s;
                return n._scrollDistance_ && 0 !== n._scrollDistance_ && !0 === n.enableScroll && (a.translate(n._scrollDistance_, 0), f = Math.floor(-n._scrollDistance_ / s) - 2, p = f + n.xAxis.itemCount + 4, h = -n._scrollDistance_ - s + n.area[3], g = h + (n.xAxis.itemCount + 4) * s), o.average.show && e.forEach(function (t) {
                    var e, o, c;
                    o = (e = [].concat(n.chartData.yAxisData.ranges[t.index])).pop(), c = e.shift();
                    for (var d, f = R(Y(t.data, o, c, l, s, n, i, r)), p = 0; p < f.length; p++) {
                        if (d = f[p], a.beginPath(), a.setStrokeStyle(t.color), a.setLineWidth(1), 1 === d.length) a.moveTo(d[0].x, d[0].y), a.arc(d[0].x, d[0].y, 1, 0, 2 * Math.PI); else {
                            a.moveTo(d[0].x, d[0].y);
                            for (var v, y = 0, m = 0; m < d.length; m++) if (v = d[m], 0 == y && v.x > h && (a.moveTo(v.x, v.y), y = 1), 0 < m && v.x > h && v.x < g) {
                                var _ = u(d, m - 1);
                                a.bezierCurveTo(_.ctrA.x, _.ctrA.y, _.ctrB.x, _.ctrB.y, v.x, v.y)
                            }
                            a.moveTo(d[0].x, d[0].y)
                        }
                        a.closePath(), a.stroke()
                    }
                }), t.forEach(function (t) {
                    var e, c, u;
                    c = (e = [].concat(n.chartData.yAxisData.ranges[t.index])).pop(), u = e.shift();
                    var h = t.data, g = K(h, c, u, l, s, n, i, r);
                    d.push(g);
                    for (var v = R(g), y = 0; y < v[0].length; y++) if (y > f && y < p) {
                        var m = v[0][y];
                        a.beginPath(), 0 < h[y][1] - h[y][0] ? (a.setStrokeStyle(o.color.upLine), a.setFillStyle(o.color.upFill), a.setLineWidth(1 * n.pixelRatio), a.moveTo(m[3].x, m[3].y), a.lineTo(m[1].x, m[1].y), a.lineTo(m[1].x - s / 4, m[1].y), a.lineTo(m[0].x - s / 4, m[0].y), a.lineTo(m[0].x, m[0].y), a.lineTo(m[2].x, m[2].y), a.lineTo(m[0].x, m[0].y), a.lineTo(m[0].x + s / 4, m[0].y), a.lineTo(m[1].x + s / 4, m[1].y), a.lineTo(m[1].x, m[1].y), a.moveTo(m[3].x, m[3].y)) : (a.setStrokeStyle(o.color.downLine), a.setFillStyle(o.color.downFill), a.setLineWidth(1 * n.pixelRatio), a.moveTo(m[3].x, m[3].y), a.lineTo(m[0].x, m[0].y), a.lineTo(m[0].x - s / 4, m[0].y), a.lineTo(m[1].x - s / 4, m[1].y), a.lineTo(m[1].x, m[1].y), a.lineTo(m[2].x, m[2].y), a.lineTo(m[1].x, m[1].y), a.lineTo(m[1].x + s / 4, m[1].y), a.lineTo(m[0].x + s / 4, m[0].y), a.lineTo(m[0].x, m[0].y), a.moveTo(m[3].x, m[3].y)), a.closePath(), a.fill(), a.stroke()
                    }
                }), a.restore(), {xAxisPoints: l, calPoints: d, eachSpacing: s}
            }

            function mt(t, e, n, i) {
                var r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1,
                    o = Xt({}, {type: "straight", opacity: .2, addLine: !1, width: 2}, e.extra.area),
                    c = e.chartData.xAxisData, l = c.xAxisPoints, s = c.eachSpacing, d = e.height - e.area[2], f = [];
                i.save();
                var p = 0, h = e.width + s;
                return e._scrollDistance_ && 0 !== e._scrollDistance_ && !0 === e.enableScroll && (i.translate(e._scrollDistance_, 0), p = -e._scrollDistance_ - s + e.area[3], h = p + (e.xAxis.itemCount + 4) * s), t.forEach(function (t) {
                    var c, g, v;
                    g = (c = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), v = c.shift();
                    var y = Y(t.data, g, v, l, s, e, n, r);
                    f.push(y);
                    for (var m, _ = R(y), x = 0; x < _.length; x++) {
                        if (m = _[x], i.beginPath(), i.setStrokeStyle(a(t.color, o.opacity)), i.setFillStyle(a(t.color, o.opacity)), i.setLineWidth(o.width * e.pixelRatio), 1 < m.length) {
                            var b = m[0], P = m[m.length - 1];
                            i.moveTo(b.x, b.y);
                            var w = 0;
                            if ("curve" === o.type) {
                                for (var S, A = 0; A < m.length; A++) if (S = m[A], 0 == w && S.x > p && (i.moveTo(S.x, S.y), w = 1), 0 < A && S.x > p && S.x < h) {
                                    var T = u(m, A - 1);
                                    i.bezierCurveTo(T.ctrA.x, T.ctrA.y, T.ctrB.x, T.ctrB.y, S.x, S.y)
                                }
                            } else for (var k, M = 0; M < m.length; M++) k = m[M], 0 == w && k.x > p && (i.moveTo(k.x, k.y), w = 1), 0 < M && k.x > p && k.x < h && i.lineTo(k.x, k.y);
                            i.lineTo(P.x, d), i.lineTo(b.x, d), i.lineTo(b.x, b.y)
                        } else {
                            var O = m[0];
                            i.moveTo(O.x - s / 2, O.y), i.lineTo(O.x + s / 2, O.y), i.lineTo(O.x + s / 2, d), i.lineTo(O.x - s / 2, d), i.moveTo(O.x - s / 2, O.y)
                        }
                        if (i.closePath(), i.fill(), o.addLine) {
                            if ("dash" == t.lineType) {
                                var C = t.dashLength ? t.dashLength : 8;
                                C *= e.pixelRatio, i.setLineDash([C, C])
                            }
                            if (i.beginPath(), i.setStrokeStyle(t.color), i.setLineWidth(o.width * e.pixelRatio), 1 === m.length) i.moveTo(m[0].x, m[0].y), i.arc(m[0].x, m[0].y, 1, 0, 2 * Math.PI); else {
                                i.moveTo(m[0].x, m[0].y);
                                var L = 0;
                                if ("curve" === o.type) {
                                    for (var D, $ = 0; $ < m.length; $++) if (D = m[$], 0 == L && D.x > p && (i.moveTo(D.x, D.y), L = 1), 0 < $ && D.x > p && D.x < h) {
                                        var j = u(m, $ - 1);
                                        i.bezierCurveTo(j.ctrA.x, j.ctrA.y, j.ctrB.x, j.ctrB.y, D.x, D.y)
                                    }
                                } else for (var F, E = 0; E < m.length; E++) F = m[E], 0 == L && F.x > p && (i.moveTo(F.x, F.y), L = 1), 0 < E && F.x > p && F.x < h && i.lineTo(F.x, F.y);
                                i.moveTo(m[0].x, m[0].y)
                            }
                            i.stroke(), i.setLineDash([])
                        }
                    }
                    !1 !== e.dataPointShape && rt(y, t.color, t.pointShape, i, e)
                }), !1 !== e.dataLabel && 1 === r && t.forEach(function (t) {
                    var a, o, c;
                    o = (a = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), c = a.shift(), ct(Y(t.data, o, c, l, s, e, n, r), t, n, i)
                }), i.restore(), {xAxisPoints: l, calPoints: f, eachSpacing: s}
            }

            function _t(t, e, n, i) {
                var a = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1,
                    r = Xt({}, {type: "straight", width: 2}, e.extra.line);
                r.width *= e.pixelRatio;
                var o = e.chartData.xAxisData, c = o.xAxisPoints, l = o.eachSpacing, s = [];
                i.save();
                var d = 0, f = e.width + l;
                return e._scrollDistance_ && 0 !== e._scrollDistance_ && !0 === e.enableScroll && (i.translate(e._scrollDistance_, 0), d = -e._scrollDistance_ - l + e.area[3], f = d + (e.xAxis.itemCount + 4) * l), t.forEach(function (t) {
                    var o, p, h;
                    p = (o = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), h = o.shift();
                    var g = Y(t.data, p, h, c, l, e, n, a);
                    s.push(g);
                    var v = R(g);
                    if ("dash" == t.lineType) {
                        var y = t.dashLength ? t.dashLength : 8;
                        y *= e.pixelRatio, i.setLineDash([y, y])
                    }
                    i.beginPath(), i.setStrokeStyle(t.color), i.setLineWidth(r.width), v.forEach(function (t) {
                        if (1 === t.length) i.moveTo(t[0].x, t[0].y), i.arc(t[0].x, t[0].y, 1, 0, 2 * Math.PI); else {
                            i.moveTo(t[0].x, t[0].y);
                            var e = 0;
                            if ("curve" === r.type) {
                                for (var n, a = 0; a < t.length; a++) if (n = t[a], 0 == e && n.x > d && (i.moveTo(n.x, n.y), e = 1), 0 < a && n.x > d && n.x < f) {
                                    var o = u(t, a - 1);
                                    i.bezierCurveTo(o.ctrA.x, o.ctrA.y, o.ctrB.x, o.ctrB.y, n.x, n.y)
                                }
                            } else for (var c, l = 0; l < t.length; l++) c = t[l], 0 == e && c.x > d && (i.moveTo(c.x, c.y), e = 1), 0 < l && c.x > d && c.x < f && i.lineTo(c.x, c.y);
                            i.moveTo(t[0].x, t[0].y)
                        }
                    }), i.stroke(), i.setLineDash([]), !1 !== e.dataPointShape && rt(g, t.color, t.pointShape, i, e)
                }), !1 !== e.dataLabel && 1 === a && t.forEach(function (t) {
                    var r, o, s;
                    o = (r = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), s = r.shift(), ct(Y(t.data, o, s, c, l, e, n, a), t, n, i)
                }), i.restore(), {xAxisPoints: c, calPoints: s, eachSpacing: l}
            }

            function xt(t, e, n, i) {
                var r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, o = e.chartData.xAxisData,
                    c = o.xAxisPoints, l = o.eachSpacing, s = e.height - e.area[2], d = [], f = 0, p = 0;
                t.forEach(function (t) {
                    "column" == t.type && (p += 1)
                }), i.save();
                var h = -2, g = c.length + 2, v = 0, y = e.width + l;
                return e._scrollDistance_ && 0 !== e._scrollDistance_ && !0 === e.enableScroll && (i.translate(e._scrollDistance_, 0), h = Math.floor(-e._scrollDistance_ / l) - 2, g = h + e.xAxis.itemCount + 4, v = -e._scrollDistance_ - l + e.area[3], y = v + (e.xAxis.itemCount + 4) * l), t.forEach(function (t) {
                    var o, m, _;
                    m = (o = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), _ = o.shift();
                    var x = Y(t.data, m, _, c, l, e, n, r);
                    if (d.push(x), "column" == t.type) {
                        x = G(x, l, p, f, n, e);
                        for (var b, P = 0; P < x.length; P++) if (null !== (b = x[P]) && P > h && P < g) {
                            i.beginPath(), i.setStrokeStyle(b.color || t.color), i.setLineWidth(1), i.setFillStyle(b.color || t.color);
                            var w = b.x - b.width / 2;
                            e.height, b.y, e.area[2], i.moveTo(w, b.y), i.moveTo(w - 1, b.y), i.lineTo(w + b.width - 2, b.y), i.lineTo(w + b.width - 2, e.height - e.area[2]), i.lineTo(w, e.height - e.area[2]), i.lineTo(w, b.y), i.closePath(), i.stroke(), i.fill(), i.closePath(), i.fill()
                        }
                        f += 1
                    }
                    if ("area" == t.type) for (var S, A = R(x), T = 0; T < A.length; T++) {
                        if (S = A[T], i.beginPath(), i.setStrokeStyle(t.color), i.setFillStyle(a(t.color, .2)), i.setLineWidth(2 * e.pixelRatio), 1 < S.length) {
                            var k = S[0], M = S[S.length - 1];
                            i.moveTo(k.x, k.y);
                            var O = 0;
                            if ("curve" === t.style) {
                                for (var C, L = 0; L < S.length; L++) if (C = S[L], 0 == O && C.x > v && (i.moveTo(C.x, C.y), O = 1), 0 < L && C.x > v && C.x < y) {
                                    var D = u(S, L - 1);
                                    i.bezierCurveTo(D.ctrA.x, D.ctrA.y, D.ctrB.x, D.ctrB.y, C.x, C.y)
                                }
                            } else for (var $, j = 0; j < S.length; j++) $ = S[j], 0 == O && $.x > v && (i.moveTo($.x, $.y), O = 1), 0 < j && $.x > v && $.x < y && i.lineTo($.x, $.y);
                            i.lineTo(M.x, s), i.lineTo(k.x, s), i.lineTo(k.x, k.y)
                        } else {
                            var F = S[0];
                            i.moveTo(F.x - l / 2, F.y), i.lineTo(F.x + l / 2, F.y), i.lineTo(F.x + l / 2, s), i.lineTo(F.x - l / 2, s), i.moveTo(F.x - l / 2, F.y)
                        }
                        i.closePath(), i.fill()
                    }
                    "line" == t.type && R(x).forEach(function (n) {
                        if ("dash" == t.lineType) {
                            var a = t.dashLength ? t.dashLength : 8;
                            a *= e.pixelRatio, i.setLineDash([a, a])
                        }
                        if (i.beginPath(), i.setStrokeStyle(t.color), i.setLineWidth(2 * e.pixelRatio), 1 === n.length) i.moveTo(n[0].x, n[0].y), i.arc(n[0].x, n[0].y, 1, 0, 2 * Math.PI); else {
                            i.moveTo(n[0].x, n[0].y);
                            var r = 0;
                            if ("curve" == t.style) {
                                for (var o, c = 0; c < n.length; c++) if (o = n[c], 0 == r && o.x > v && (i.moveTo(o.x, o.y), r = 1), 0 < c && o.x > v && o.x < y) {
                                    var l = u(n, c - 1);
                                    i.bezierCurveTo(l.ctrA.x, l.ctrA.y, l.ctrB.x, l.ctrB.y, o.x, o.y)
                                }
                            } else for (var s, d = 0; d < n.length; d++) s = n[d], 0 == r && s.x > v && (i.moveTo(s.x, s.y), r = 1), 0 < d && s.x > v && s.x < y && i.lineTo(s.x, s.y);
                            i.moveTo(n[0].x, n[0].y)
                        }
                        i.stroke(), i.setLineDash([])
                    }), "point" == t.type && (t.addPoint = !0), 1 == t.addPoint && "column" !== t.type && rt(x, t.color, t.pointShape, i, e)
                }), !1 !== e.dataLabel && 1 === r && (f = 0, t.forEach(function (t) {
                    var a, o, s;
                    o = (a = [].concat(e.chartData.yAxisData.ranges[t.index])).pop(), s = a.shift();
                    var u = Y(t.data, o, s, c, l, e, n, r);
                    "column" === t.type ? (u = G(u, l, p, f, n, e), ct(u, t, n, i), f += 1) : ct(u, t, n, i)
                })), i.restore(), {xAxisPoints: c, calPoints: d, eachSpacing: l}
            }

            function bt(t, e, n, i, a, r) {
                (t.extra.tooltip || {}).horizentalLine && t.tooltip && 1 === i && ("line" == t.type || "area" == t.type || "column" == t.type || "candle" == t.type || "mix" == t.type) && pt(t, e, n, a, r), n.save(), t._scrollDistance_ && 0 !== t._scrollDistance_ && !0 === t.enableScroll && n.translate(t._scrollDistance_, 0), t.tooltip && t.tooltip.textList && t.tooltip.textList.length && 1 === i && gt(t.tooltip.textList, t.tooltip.offset, t, e, n, a, r), n.restore()
            }

            function Pt(t, e, n, i) {
                var a = Math.ceil, r = e.chartData.xAxisData, o = r.xAxisPoints, c = r.startX, l = r.endX,
                    u = r.eachSpacing, d = "center";
                ("line" == e.type || "area" == e.type) && (d = e.xAxis.boundaryGap);
                var f = e.height - e.area[2], p = e.area[0];
                if (e.enableScroll && e.xAxis.scrollShow) {
                    var h = e.height - e.area[2] + n.xAxisHeight, v = l - c, y = u * (o.length - 1), m = 0;
                    e._scrollDistance_ && (m = -e._scrollDistance_ * v / y), i.beginPath(), i.setLineCap("round"), i.setLineWidth(6 * e.pixelRatio), i.setStrokeStyle(e.xAxis.scrollBackgroundColor || "#EFEBEF"), i.moveTo(c, h), i.lineTo(l, h), i.stroke(), i.closePath(), i.beginPath(), i.setLineCap("round"), i.setLineWidth(6 * e.pixelRatio), i.setStrokeStyle(e.xAxis.scrollColor || "#A6A6A6"), i.moveTo(c + m, h), i.lineTo(c + m + v * v / y, h), i.stroke(), i.closePath(), i.setLineCap("butt")
                }
                if (i.save(), e._scrollDistance_ && 0 !== e._scrollDistance_ && i.translate(e._scrollDistance_, 0), !0 === e.xAxis.calibration && (i.setStrokeStyle(e.xAxis.gridColor || "#cccccc"), i.setLineCap("butt"), i.setLineWidth(1 * e.pixelRatio), o.forEach(function (t, n) {
                    0 < n && (i.beginPath(), i.moveTo(t - u / 2, f), i.lineTo(t - u / 2, f + 3 * e.pixelRatio), i.closePath(), i.stroke())
                })), !0 !== e.xAxis.disableGrid && (i.setStrokeStyle(e.xAxis.gridColor || "#cccccc"), i.setLineCap("butt"), i.setLineWidth(1 * e.pixelRatio), "dash" == e.xAxis.gridType && i.setLineDash([e.xAxis.dashLength, e.xAxis.dashLength]), e.xAxis.gridEval = e.xAxis.gridEval || 1, o.forEach(function (t, n) {
                    0 == n % e.xAxis.gridEval && (i.beginPath(), i.moveTo(t, f), i.lineTo(t, p), i.stroke())
                }), i.setLineDash([])), !0 !== e.xAxis.disabled) {
                    var _ = t.length;
                    e.xAxis.labelCount && (_ = e.xAxis.itemCount ? a(t.length / e.xAxis.itemCount * e.xAxis.labelCount) : e.xAxis.labelCount, _ -= 1);
                    for (var x = a(t.length / _), b = [], P = t.length, w = 0; w < P; w++) 0 == w % x ? b.push(t[w]) : b.push("");
                    b[P - 1] = t[P - 1];
                    var S = e.xAxis.fontSize || n.fontSize;
                    0 === n._xAxisTextAngle_ ? b.forEach(function (t, a) {
                        var r = -g(t, S) / 2;
                        "center" == d && (r += u / 2);
                        var c = 0;
                        e.xAxis.scrollShow && (c = 6 * e.pixelRatio), i.beginPath(), i.setFontSize(S), i.setFillStyle(e.xAxis.fontColor || "#666666"), i.fillText(t, o[a] + r, f + S + (n.xAxisHeight - c - S) / 2), i.closePath(), i.stroke()
                    }) : b.forEach(function (t, a) {
                        i.save(), i.beginPath(), i.setFontSize(S), i.setFillStyle(e.xAxis.fontColor || "#666666");
                        var r = -g(t);
                        "center" == d && (r += u / 2);
                        var c = s(o[a] + u / 2, f + S / 2 + 5, e.height), l = c.transX + 15, p = c.transY;
                        i.rotate(-1 * n._xAxisTextAngle_), i.translate(l, p), i.fillText(t, o[a] + r, f + S + 5), i.closePath(), i.stroke(), i.restore()
                    })
                }
                i.restore(), e.xAxis.axisLine && (i.beginPath(), i.setStrokeStyle(e.xAxis.axisLineColor), i.setLineWidth(1 * e.pixelRatio), i.moveTo(c, e.height - e.area[2]), i.lineTo(l, e.height - e.area[2]), i.stroke())
            }

            function wt(t, e, n, i) {
                if (!0 !== e.yAxis.disableGrid) {
                    for (var a = (e.height - e.area[0] - e.area[2]) / n.yAxisSplit, r = e.area[3], o = e.chartData.xAxisData.xAxisPoints, c = e.chartData.xAxisData.eachSpacing * (o.length - 1), l = [], s = 0; s < n.yAxisSplit + 1; s++) l.push(e.height - e.area[2] - a * s);
                    i.save(), e._scrollDistance_ && 0 !== e._scrollDistance_ && i.translate(e._scrollDistance_, 0), "dash" == e.yAxis.gridType && i.setLineDash([e.yAxis.dashLength, e.yAxis.dashLength]), i.setStrokeStyle(e.yAxis.gridColor), i.setLineWidth(1 * e.pixelRatio), l.forEach(function (t) {
                        i.beginPath(), i.moveTo(r, t), i.lineTo(r + c, t), i.stroke()
                    }), i.setLineDash([]), i.restore()
                }
            }

            function St(t, e, n, i) {
                if (!0 !== e.yAxis.disabled) {
                    var a = (e.height - e.area[0] - e.area[2]) / n.yAxisSplit, r = e.area[3], o = e.width - e.area[1],
                        c = e.height - e.area[2], l = c + n.xAxisHeight;
                    e.xAxis.scrollShow && (l -= 3 * e.pixelRatio), e.xAxis.rotateLabel && (l = e.height - e.area[2] + 3), i.beginPath(), i.setFillStyle(e.background || "#ffffff"), 0 > e._scrollDistance_ && i.fillRect(0, 0, r, l), 1 == e.enableScroll && i.fillRect(o, 0, e.width, l), i.closePath(), i.stroke();
                    for (var s = [], u = 0; u <= n.yAxisSplit; u++) s.push(e.area[0] + a * u);
                    for (var d, f = e.area[3], p = e.width - e.area[1], h = 0; h < e.yAxis.data.length; h++) !function (t, a) {
                        if (!0 !== (t = e.yAxis.data[a]).disabled) {
                            var r = e.chartData.yAxisData.rangesFormat[a], o = t.fontSize || n.fontSize,
                                l = e.chartData.yAxisData.yAxisWidth[a];
                            if (r.forEach(function (n, a) {
                                var r = s[a] ? s[a] : c;
                                i.beginPath(), i.setFontSize(o), i.setLineWidth(1 * e.pixelRatio), i.setStrokeStyle(t.axisLineColor || "#cccccc"), i.setFillStyle(t.fontColor || "#666666"), "left" == l.position ? (i.fillText(n + "", f - l.width, r + o / 2), 1 == t.calibration && (i.moveTo(f, r), i.lineTo(f - 3 * e.pixelRatio, r))) : (i.fillText(n + "", p + 4 * e.pixelRatio, r + o / 2), 1 == t.calibration && (i.moveTo(p, r), i.lineTo(p + 3 * e.pixelRatio, r))), i.closePath(), i.stroke()
                            }), !1 !== t.axisLine && (i.beginPath(), i.setStrokeStyle(t.axisLineColor || "#cccccc"), i.setLineWidth(1 * e.pixelRatio), "left" == l.position ? (i.moveTo(f, e.height - e.area[2]), i.lineTo(f, e.area[0])) : (i.moveTo(p, e.height - e.area[2]), i.lineTo(p, e.area[0])), i.stroke()), e.yAxis.showTitle) {
                                var u = t.titleFontSize || n.fontSize, h = t.title;
                                i.beginPath(), i.setFontSize(u), i.setFillStyle(t.titleFontColor || "#666666"), "left" == l.position ? i.fillText(h, f - g(h, u) / 2, e.area[0] - 10 * e.pixelRatio) : i.fillText(h, p - g(h, u) / 2, e.area[0] - 10 * e.pixelRatio), i.closePath(), i.stroke()
                            }
                            "left" == l.position ? f -= l.width + e.yAxis.padding : p += l.width + e.yAxis.padding
                        }
                        d = t
                    }(d, h)
                }
            }

            function At(t, e, n, i, a) {
                if (!1 !== e.legend.show) {
                    var r = a.legendData, o = r.points, c = r.area, l = e.legend.padding, s = e.legend.fontSize,
                        u = 15 * e.pixelRatio, d = 5 * e.pixelRatio, f = e.legend.itemGap,
                        p = Math.max(e.legend.lineHeight * e.pixelRatio, s);
                    i.beginPath(), i.setLineWidth(e.legend.borderWidth), i.setStrokeStyle(e.legend.borderColor), i.setFillStyle(e.legend.backgroundColor), i.moveTo(c.start.x, c.start.y), i.rect(c.start.x, c.start.y, c.width, c.height), i.closePath(), i.fill(), i.stroke(), o.forEach(function (t, a) {
                        var o = 0, h = 0;
                        o = r.widthArr[a], h = r.heightArr[a];
                        var v = 0, y = 0;
                        "top" == e.legend.position || "bottom" == e.legend.position ? (v = c.start.x + (c.width - o) / 2, y = c.start.y + l + a * p) : (o = 0 == a ? 0 : r.widthArr[a - 1], v = c.start.x + l + o, y = c.start.y + l + (c.height - h) / 2), i.setFontSize(n.fontSize);
                        for (var m, _ = 0; _ < t.length; _++) {
                            switch (m = t[_], m.area = [0, 0, 0, 0], m.area[0] = v, m.area[1] = y, m.area[3] = y + p, i.beginPath(), i.setLineWidth(1 * e.pixelRatio), i.setStrokeStyle(m.show ? m.color : e.legend.hiddenColor), i.setFillStyle(m.show ? m.color : e.legend.hiddenColor), m.legendShape) {
                                case"line":
                                    i.moveTo(v, y + .5 * p - 2 * e.pixelRatio), i.fillRect(v, y + .5 * p - 2 * e.pixelRatio, 15 * e.pixelRatio, 4 * e.pixelRatio);
                                    break;
                                case"triangle":
                                    i.moveTo(v + 7.5 * e.pixelRatio, y + .5 * p - 5 * e.pixelRatio), i.lineTo(v + 2.5 * e.pixelRatio, y + .5 * p + 5 * e.pixelRatio), i.lineTo(v + 12.5 * e.pixelRatio, y + .5 * p + 5 * e.pixelRatio), i.lineTo(v + 7.5 * e.pixelRatio, y + .5 * p - 5 * e.pixelRatio);
                                    break;
                                case"diamond":
                                    i.moveTo(v + 7.5 * e.pixelRatio, y + .5 * p - 5 * e.pixelRatio), i.lineTo(v + 2.5 * e.pixelRatio, y + .5 * p), i.lineTo(v + 7.5 * e.pixelRatio, y + .5 * p + 5 * e.pixelRatio), i.lineTo(v + 12.5 * e.pixelRatio, y + .5 * p), i.lineTo(v + 7.5 * e.pixelRatio, y + .5 * p - 5 * e.pixelRatio);
                                    break;
                                case"circle":
                                    i.moveTo(v + 7.5 * e.pixelRatio, y + .5 * p), i.arc(v + 7.5 * e.pixelRatio, y + .5 * p, 5 * e.pixelRatio, 0, 2 * Math.PI);
                                    break;
                                case"rect":
                                    i.moveTo(v, y + .5 * p - 5 * e.pixelRatio), i.fillRect(v, y + .5 * p - 5 * e.pixelRatio, 15 * e.pixelRatio, 10 * e.pixelRatio);
                                    break;
                                default:
                                    i.moveTo(v, y + .5 * p - 5 * e.pixelRatio), i.fillRect(v, y + .5 * p - 5 * e.pixelRatio, 15 * e.pixelRatio, 10 * e.pixelRatio)
                            }
                            i.closePath(), i.fill(), i.stroke(), v += u + d, i.beginPath(), i.setFontSize(s), i.setFillStyle(m.show ? e.legend.fontColor : e.legend.hiddenColor), i.fillText(m.name, v, y + (.5 * p + .5 * s - 2)), i.closePath(), i.stroke(), "top" == e.legend.position || "bottom" == e.legend.position ? (v += g(m.name, s) + f, m.area[2] = v) : (m.area[2] = v + g(m.name, s) + f, v -= u + d, y += p)
                        }
                    })
                }
            }

            function Tt(t, e, n, i) {
                var r = Math.PI, o = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, c = Xt({}, {
                    activeOpacity: .5,
                    activeRadius: 10 * e.pixelRatio,
                    offsetAngle: 0,
                    labelWidth: 15 * e.pixelRatio,
                    ringWidth: 0,
                    border: !1,
                    borderWidth: 2,
                    borderColor: "#FFFFFF"
                }, e.extra.pie), l = {
                    x: e.area[3] + (e.width - e.area[1] - e.area[3]) / 2,
                    y: e.area[0] + (e.height - e.area[0] - e.area[2]) / 2
                };
                0 == n.pieChartLinePadding && (n.pieChartLinePadding = c.activeRadius);
                var s = Math.min((e.width - e.area[1] - e.area[3]) / 2 - n.pieChartLinePadding - n.pieChartTextPadding - n._pieTextMaxLength_, (e.height - e.area[0] - e.area[2]) / 2 - n.pieChartLinePadding - n.pieChartTextPadding);
                t = B(t, s, o);
                var u = c.activeRadius;
                if ((t = t.map(function (t) {
                    return t._start_ += c.offsetAngle * r / 180, t
                })).forEach(function (t, n) {
                    e.tooltip && e.tooltip.index == n && (i.beginPath(), i.setFillStyle(a(t.color, e.extra.pie.activeOpacity || .5)), i.moveTo(l.x, l.y), i.arc(l.x, l.y, t._radius_ + u, t._start_, t._start_ + 2 * t._proportion_ * r), i.closePath(), i.fill()), i.beginPath(), i.setLineWidth(c.borderWidth * e.pixelRatio), i.lineJoin = "round", i.setStrokeStyle(c.borderColor), i.setFillStyle(t.color), i.moveTo(l.x, l.y), i.arc(l.x, l.y, t._radius_, t._start_, t._start_ + 2 * t._proportion_ * r), i.closePath(), i.fill(), 1 == c.border && i.stroke()
                }), "ring" === e.type) {
                    var d = .6 * s;
                    "number" == typeof e.extra.pie.ringWidth && 0 < e.extra.pie.ringWidth && (d = Math.max(0, s - e.extra.pie.ringWidth)), i.beginPath(), i.setFillStyle(e.background || "#ffffff"), i.moveTo(l.x, l.y), i.arc(l.x, l.y, d, 0, 2 * r), i.closePath(), i.fill()
                }
                if (!1 !== e.dataLabel && 1 === o) {
                    for (var f = !1, p = 0, h = t.length; p < h; p++) if (0 < t[p].data) {
                        f = !0;
                        break
                    }
                    f && ut(t, e, n, i, s, l)
                }
                return 1 === o && "ring" === e.type && ot(e, n, i, l), {center: l, radius: s, series: t}
            }

            function kt(t, e, n, i) {
                var r = Math.PI, o = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, c = Xt({}, {
                    type: "area",
                    activeOpacity: .5,
                    activeRadius: 10 * e.pixelRatio,
                    offsetAngle: 0,
                    labelWidth: 15 * e.pixelRatio,
                    border: !1,
                    borderWidth: 2,
                    borderColor: "#FFFFFF"
                }, e.extra.rose);
                0 == n.pieChartLinePadding && (n.pieChartLinePadding = c.activeRadius);
                var l = {
                        x: e.area[3] + (e.width - e.area[1] - e.area[3]) / 2,
                        y: e.area[0] + (e.height - e.area[0] - e.area[2]) / 2
                    },
                    s = Math.min((e.width - e.area[1] - e.area[3]) / 2 - n.pieChartLinePadding - n.pieChartTextPadding - n._pieTextMaxLength_, (e.height - e.area[0] - e.area[2]) / 2 - n.pieChartLinePadding - n.pieChartTextPadding),
                    u = c.minRadius || .5 * s;
                t = W(t, c.type, u, s, o);
                var d = c.activeRadius;
                if ((t = t.map(function (t) {
                    return t._start_ += (c.offsetAngle || 0) * r / 180, t
                })).forEach(function (t, n) {
                    e.tooltip && e.tooltip.index == n && (i.beginPath(), i.setFillStyle(a(t.color, c.activeOpacity || .5)), i.moveTo(l.x, l.y), i.arc(l.x, l.y, d + t._radius_, t._start_, t._start_ + 2 * t._rose_proportion_ * r), i.closePath(), i.fill()), i.beginPath(), i.setLineWidth(c.borderWidth * e.pixelRatio), i.lineJoin = "round", i.setStrokeStyle(c.borderColor), i.setFillStyle(t.color), i.moveTo(l.x, l.y), i.arc(l.x, l.y, t._radius_, t._start_, t._start_ + 2 * t._rose_proportion_ * r), i.closePath(), i.fill(), 1 == c.border && i.stroke()
                }), !1 !== e.dataLabel && 1 === o) {
                    for (var f = !1, p = 0, h = t.length; p < h; p++) if (0 < t[p].data) {
                        f = !0;
                        break
                    }
                    f && ut(t, e, n, i, s, l)
                }
                return {center: l, radius: s, series: t}
            }

            function Mt(t, e, n, i) {
                var a = Math.PI, r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, o = Xt({}, {
                    startAngle: .75,
                    endAngle: .25,
                    type: "price-sort-default.png",
                    width: 12 * e.pixelRatio
                }, e.extra.arcbar);
                t = H(t, o, r);
                var c = {x: e.width / 2, y: e.height / 2}, l = Math.min(c.x, c.y);
                l -= 5 * e.pixelRatio, l -= o.width / 2, i.setLineWidth(o.width), i.setStrokeStyle(o.backgroundColor || "#E9E9E9"), i.setLineCap("round"), i.beginPath(), "price-sort-default.png" == o.type ? i.arc(c.x, c.y, l, o.startAngle * a, o.endAngle * a, !1) : i.arc(c.x, c.y, l, 0, 2 * a, !1), i.stroke();
                for (var s, u = 0; u < t.length; u++) s = t[u], i.setLineWidth(o.width), i.setStrokeStyle(s.color), i.setLineCap("round"), i.beginPath(), i.arc(c.x, c.y, l, o.startAngle * a, s._proportion_ * a, !1), i.stroke();
                return ot(e, n, i, c), {center: c, radius: l, series: t}
            }

            function Ot(t, e, n, i, r) {
                var o = Math.PI, c = 5 < arguments.length && void 0 !== arguments[5] ? arguments[5] : 1, l = Xt({}, {
                    type: "price-sort-default.png",
                    startAngle: .75,
                    endAngle: .25,
                    width: 15,
                    splitLine: {
                        fixRadius: 0,
                        splitNumber: 10,
                        width: 15,
                        color: "#FFFFFF",
                        childNumber: 5,
                        childWidth: 5
                    },
                    pointer: {width: 15, color: "auto"}
                }, n.extra.gauge);
                null == l.oldAngle && (l.oldAngle = l.startAngle), null == l.oldData && (l.oldData = 0), t = U(t, l.startAngle, l.endAngle);
                var s = {x: n.width / 2, y: n.height / 2}, u = Math.min(s.x, s.y);
                u -= 5 * n.pixelRatio;
                var d = (u -= l.width / 2) - l.width, f = 0;
                if ("progress" == l.type) {
                    var p = u - 3 * l.width;
                    r.beginPath();
                    var h = r.createLinearGradient(s.x, s.y - p, s.x, s.y + p);
                    h.addColorStop("0", a(e[0].color, .3)), h.addColorStop("1.0", a("#FFFFFF", .1)), r.setFillStyle(h), r.arc(s.x, s.y, p, 0, 2 * o, !1), r.fill(), r.setLineWidth(l.width), r.setStrokeStyle(a(e[0].color, .3)), r.setLineCap("round"), r.beginPath(), r.arc(s.x, s.y, d, l.startAngle * o, l.endAngle * o, !1), r.stroke(), f = l.startAngle - l.endAngle + 1, l.splitLine.splitNumber;
                    var g = f / l.splitLine.splitNumber / l.splitLine.childNumber,
                        v = -u - .5 * l.width - l.splitLine.fixRadius,
                        y = -u - l.width - l.splitLine.fixRadius + l.splitLine.width;
                    r.save(), r.translate(s.x, s.y), r.rotate((l.startAngle - 1) * o);
                    for (var m = l.splitLine.splitNumber * l.splitLine.childNumber + 1, _ = e[0].data * c, x = 0; x < m; x++) r.beginPath(), _ > x / m ? r.setStrokeStyle(a(e[0].color, 1)) : r.setStrokeStyle(a(e[0].color, .3)), r.setLineWidth(3 * n.pixelRatio), r.moveTo(v, 0), r.lineTo(y, 0), r.stroke(), r.rotate(g * o);
                    r.restore(), e = H(e, l, c), r.setLineWidth(l.width);
                    var b = r.createLinearGradient(s.x - d, s.y, s.x + d, s.y);
                    b.addColorStop("0", a(e[0].color, .2)), b.addColorStop("1.0", a(e[0].color, 1)), r.setStrokeStyle(b), r.setLineCap("round"), r.beginPath(), r.arc(s.x, s.y, d, l.startAngle * o, e[0]._proportion_ * o, !1), r.stroke();
                    var P = u - 2.5 * l.width;
                    r.save(), r.translate(s.x, s.y), r.rotate((e[0]._proportion_ - 1) * o), r.beginPath(), r.setLineWidth(l.width / 3);
                    var w = r.createLinearGradient(0, .6 * -P, 0, .6 * P);
                    w.addColorStop("0", a("#FFFFFF", 0)), w.addColorStop("0.5", a(e[0].color, 1)), w.addColorStop("1.0", a("#FFFFFF", 0)), r.setStrokeStyle(w), r.arc(0, 0, P, .85 * o, 1.15 * o, !1), r.stroke(), r.beginPath(), r.setLineWidth(1), r.setStrokeStyle(e[0].color), r.setFillStyle(e[0].color), r.moveTo(-P - l.width / 3 / 2, -4), r.lineTo(-P - l.width / 3 / 2 - 4, 0), r.lineTo(-P - l.width / 3 / 2, 4), r.lineTo(-P - l.width / 3 / 2, -4), r.stroke(), r.fill(), r.restore()
                } else {
                    r.setLineWidth(l.width), r.setLineCap("butt");
                    for (var S, A = 0; A < t.length; A++) S = t[A], r.beginPath(), r.setStrokeStyle(S.color), r.arc(s.x, s.y, u, S._startAngle_ * o, S._endAngle_ * o, !1), r.stroke();
                    r.save();
                    var T = (f = l.startAngle - l.endAngle + 1) / l.splitLine.splitNumber,
                        k = f / l.splitLine.splitNumber / l.splitLine.childNumber,
                        M = -u - .5 * l.width - l.splitLine.fixRadius,
                        O = -u - .5 * l.width - l.splitLine.fixRadius + l.splitLine.width,
                        C = -u - .5 * l.width - l.splitLine.fixRadius + l.splitLine.childWidth;
                    r.translate(s.x, s.y), r.rotate((l.startAngle - 1) * o);
                    for (var L = 0; L < l.splitLine.splitNumber + 1; L++) r.beginPath(), r.setStrokeStyle(l.splitLine.color), r.setLineWidth(2 * n.pixelRatio), r.moveTo(M, 0), r.lineTo(O, 0), r.stroke(), r.rotate(T * o);
                    r.restore(), r.save(), r.translate(s.x, s.y), r.rotate((l.startAngle - 1) * o);
                    for (var D = 0; D < l.splitLine.splitNumber * l.splitLine.childNumber + 1; D++) r.beginPath(), r.setStrokeStyle(l.splitLine.color), r.setLineWidth(1 * n.pixelRatio), r.moveTo(M, 0), r.lineTo(C, 0), r.stroke(), r.rotate(k * o);
                    r.restore(), e = V(e, t, l, c);
                    for (var $, j = 0; j < e.length; j++) $ = e[j], r.save(), r.translate(s.x, s.y), r.rotate(($._proportion_ - 1) * o), r.beginPath(), r.setFillStyle($.color), r.moveTo(l.pointer.width, 0), r.lineTo(0, -l.pointer.width / 2), r.lineTo(-d, 0), r.lineTo(0, l.pointer.width / 2), r.lineTo(l.pointer.width, 0), r.closePath(), r.fill(), r.beginPath(), r.setFillStyle("#FFFFFF"), r.arc(0, 0, l.pointer.width / 6, 0, 2 * o, !1), r.fill(), r.restore();
                    !1 !== n.dataLabel && lt(l, u, s, n, i, r)
                }
                return ot(n, i, r, s), 1 === c && "gauge" === n.type && (n.extra.gauge.oldAngle = e[0]._proportion_, n.extra.gauge.oldData = e[0].data), {
                    center: s,
                    radius: u,
                    innerRadius: d,
                    categories: t,
                    totalAngle: f
                }
            }

            function Ct(t, e, n, i) {
                var r = Math.cos, o = Math.sin, c = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1,
                    l = Xt({}, {gridColor: "#cccccc", labelColor: "#666666", opacity: .2}, e.extra.radar),
                    s = b(e.categories.length), u = {
                        x: e.area[3] + (e.width - e.area[1] - e.area[3]) / 2,
                        y: e.area[0] + (e.height - e.area[0] - e.area[2]) / 2
                    }, f = Math.min(u.x - (x(e.categories) + n.radarLabelTextMargin), u.y - n.radarLabelTextMargin);
                f -= e.padding[1], i.beginPath(), i.setLineWidth(1 * e.pixelRatio), i.setStrokeStyle(l.gridColor), s.forEach(function (t) {
                    var e = d(f * r(t), f * o(t), u);
                    i.moveTo(u.x, u.y), i.lineTo(e.x, e.y)
                }), i.stroke(), i.closePath();
                for (var p = 1; p <= n.radarGridCount; p++) !function (t) {
                    var a = {};
                    i.beginPath(), i.setLineWidth(1 * e.pixelRatio), i.setStrokeStyle(l.gridColor), s.forEach(function (e, c) {
                        var l = d(f / n.radarGridCount * t * r(e), f / n.radarGridCount * t * o(e), u);
                        0 === c ? (a = l, i.moveTo(l.x, l.y)) : i.lineTo(l.x, l.y)
                    }), i.lineTo(a.x, a.y), i.stroke(), i.closePath()
                }(p);
                return z(s, u, f, t, e, c).forEach(function (t) {
                    i.beginPath(), i.setFillStyle(a(t.color, l.opacity)), t.data.forEach(function (t, e) {
                        0 === e ? i.moveTo(t.position.x, t.position.y) : i.lineTo(t.position.x, t.position.y)
                    }), i.closePath(), i.fill(), !1 !== e.dataPointShape && rt(t.data.map(function (t) {
                        return t.position
                    }), t.color, t.pointShape, i, e)
                }), st(s, f, u, e, n, i), {center: u, radius: f, angleList: s}
            }

            function Lt(t, e, n) {
                n = 0 == n ? 1 : n;
                for (var i = [], a = 0; a < n; a++) i[a] = Math.random();
                return Math.floor(i.reduce(function (t, e) {
                    return t + e
                }) / n * (e - t)) + t
            }

            function Dt(t, e, n, i) {
                for (var a = !1, r = 0; r < e.length; r++) if (e[r].area) {
                    if (!(t[3] < e[r].area[1] || t[0] > e[r].area[2] || t[1] > e[r].area[3] || t[2] < e[r].area[0])) {
                        a = !0;
                        break
                    }
                    if (0 > t[0] || 0 > t[1] || t[2] > n || t[3] > i) {
                        a = !0;
                        break
                    }
                    a = !1
                }
                return a
            }

            function $t(t) {
                for (var e, n, i = {xMin: 180, xMax: 0, yMin: 90, yMax: 0}, a = 0; a < t.length; a++) {
                    n = t[a].geometry.coordinates;
                    for (var r = 0; r < n.length; r++) {
                        1 == (e = n[r]).length && (e = e[0]);
                        for (var o = 0; o < e.length; o++) {
                            var c = {x: e[o][0], y: e[o][1]};
                            i.xMin = i.xMin < c.x ? i.xMin : c.x, i.xMax = i.xMax > c.x ? i.xMax : c.x, i.yMin = i.yMin < c.y ? i.yMin : c.y, i.yMax = i.yMax > c.y ? i.yMax : c.y
                        }
                    }
                }
                return i
            }

            function jt(t, e, n, i, a, r) {
                return {x: (e - n.xMin) * i + a, y: (n.yMax - t) * i + r}
            }

            function Ft(t, e, n, i, a, r) {
                return {x: (e - a) / i + n.xMin, y: n.yMax - (t - r) / i}
            }

            function Rt(t, e, n) {
                return e[1] != n[1] && (!(e[1] > t[1] && n[1] > t[1]) && (!(e[1] < t[1] && n[1] < t[1]) && (!(e[1] == t[1] && n[1] > t[1]) && (!(n[1] == t[1] && e[1] > t[1]) && (!(e[0] < t[0] && n[1] < t[1]) && !(n[0] - (n[0] - e[0]) * (n[1] - t[1]) / (n[1] - e[1]) < t[0]))))))
            }

            function Et(t, e) {
                for (var n, i = 0, a = 0; a < e.length; a++) {
                    n = e[a][0], 1 == e.length && (n = e[a][0]);
                    for (var r = 0; r < n.length - 1; r++) Rt(t, n[r], n[r + 1]) && (i += 1)
                }
                return !(1 != i % 2)
            }

            function It(t, e, n, i) {
                var r, o, c = Math.abs, l = Xt({}, {
                        border: !0,
                        borderWidth: 1,
                        borderColor: "#666666",
                        fillOpacity: .6,
                        activeBorderColor: "#f04864",
                        activeFillColor: "#facc14",
                        activeFillOpacity: 1
                    }, e.extra.map), s = t, u = $t(s), d = e.width / c(u.xMax - u.xMin), f = e.height / c(u.yMax - u.yMin),
                    p = d < f ? d : f, h = e.width / 2 - c(u.xMax - u.xMin) / 2 * p,
                    v = e.height / 2 - c(u.yMax - u.yMin) / 2 * p;
                i.beginPath(), i.clearRect(0, 0, e.width, e.height), i.setFillStyle(e.background || "#FFFFFF"), i.rect(0, 0, e.width, e.height), i.fill();
                for (var y = 0; y < s.length; y++) {
                    i.beginPath(), i.setLineWidth(l.borderWidth * e.pixelRatio), i.setStrokeStyle(l.borderColor), i.setFillStyle(a(t[y].color, l.fillOpacity)), e.tooltip && e.tooltip.index == y && (i.setStrokeStyle(l.activeBorderColor), i.setFillStyle(a(l.activeFillColor, l.activeFillOpacity)));
                    for (var m = s[y].geometry.coordinates, _ = 0; _ < m.length; _++) {
                        1 == (r = m[_]).length && (r = r[0]);
                        for (var x = 0; x < r.length; x++) o = jt(r[x][1], r[x][0], u, p, h, v), 0 == x ? (i.beginPath(), i.moveTo(o.x, o.y)) : i.lineTo(o.x, o.y);
                        i.fill(), 1 == l.border && i.stroke()
                    }
                    if (1 == e.dataLabel) {
                        var b = s[y].properties.centroid;
                        if (b) {
                            o = jt(b[1], b[0], u, p, h, v);
                            var P = s[y].textSize || n.fontSize, w = s[y].properties.name;
                            i.beginPath(), i.setFontSize(P), i.setFillStyle(s[y].textColor || "#666666"), i.fillText(w, o.x - g(w, P) / 2, o.y + P / 2), i.closePath(), i.stroke()
                        }
                    }
                }
                e.chartData.mapData = {bounds: u, scale: p, xoffset: h, yoffset: v}, bt(e, n, i, 1), i.draw()
            }

            function zt(t, e) {
                var n = t.series.sort(function (t, e) {
                    return parseInt(e.textSize) - parseInt(t.textSize)
                });
                switch (e) {
                    case"normal":
                        for (var i = 0; i < n.length; i++) {
                            for (var a = void 0, r = void 0, o = void 0, c = n[i].name, l = n[i].textSize, s = g(c, l), u = 0; u++, a = Lt(-t.width / 2, t.width / 2, 5) - s / 2, r = Lt(-t.height / 2, t.height / 2, 5) + l / 2, Dt(o = [a - 5 + t.width / 2, r - 5 - l + t.height / 2, a + s + 5 + t.width / 2, r + 5 + t.height / 2], n, t.width, t.height);) if (1e3 == u) {
                                o = [-100, -100, -100, -100];
                                break
                            }
                            n[i].area = o
                        }
                        break;
                    case"vertical":
                        for (var d = 0; d < n.length; d++) {
                            for (var f = void 0, p = void 0, h = void 0, v = void 0, y = n[d].name, m = n[d].textSize, _ = g(y, m), x = !!(.7 < Math.random()), b = 0; ;) {
                                b++;
                                var P = void 0;
                                if (x ? (f = Lt(-t.width / 2, t.width / 2, 5) - _ / 2, p = Lt(-t.height / 2, t.height / 2, 5) + m / 2, h = [p - 5 - _ + t.width / 2, -f - 5 + t.height / 2, p + 5 + t.width / 2, -f + m + 5 + t.height / 2], v = [t.width - (t.width / 2 - t.height / 2) - (-f + m + 5 + t.height / 2) - 5, t.height / 2 - t.width / 2 + (p - 5 - _ + t.width / 2) - 5, t.width - (t.width / 2 - t.height / 2) - (-f + m + 5 + t.height / 2) + m, t.height / 2 - t.width / 2 + (p - 5 - _ + t.width / 2) + _ + 5], P = Dt(v, n, t.height, t.width)) : (f = Lt(-t.width / 2, t.width / 2, 5) - _ / 2, p = Lt(-t.height / 2, t.height / 2, 5) + m / 2, h = [f - 5 + t.width / 2, p - 5 - m + t.height / 2, f + _ + 5 + t.width / 2, p + 5 + t.height / 2], P = Dt(h, n, t.width, t.height)), !P) break;
                                if (1e3 == b) {
                                    h = [-1e3, -1e3, -1e3, -1e3];
                                    break
                                }
                            }
                            x ? (n[d].area = v, n[d].areav = h) : n[d].area = h, n[d].rotate = x
                        }
                }
                return n
            }

            function Bt(t, e, n, i) {
                var a = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1;
                Xt({}, {
                    type: "normal",
                    autoColors: !0
                }, e.extra.word), i.beginPath(), i.setFillStyle(e.background || "#FFFFFF"), i.rect(0, 0, e.width, e.height), i.fill(), i.save();
                var r = e.chartData.wordCloudData;
                i.translate(e.width / 2, e.height / 2);
                for (var o = 0; o < r.length; o++) {
                    i.save(), r[o].rotate && i.rotate(90 * Math.PI / 180);
                    var c = r[o].name, l = r[o].textSize, s = g(c, l);
                    i.beginPath(), i.setStrokeStyle(r[o].color), i.setFillStyle(r[o].color), i.setFontSize(l), r[o].rotate ? 0 < r[o].areav[0] && (e.tooltip && e.tooltip.index == o ? i.strokeText(c, (r[o].areav[0] + 5 - e.width / 2) * a - s * (1 - a) / 2, (r[o].areav[1] + 5 + l - e.height / 2) * a) : i.fillText(c, (r[o].areav[0] + 5 - e.width / 2) * a - s * (1 - a) / 2, (r[o].areav[1] + 5 + l - e.height / 2) * a)) : 0 < r[o].area[0] && (e.tooltip && e.tooltip.index == o ? i.strokeText(c, (r[o].area[0] + 5 - e.width / 2) * a - s * (1 - a) / 2, (r[o].area[1] + 5 + l - e.height / 2) * a) : i.fillText(c, (r[o].area[0] + 5 - e.width / 2) * a - s * (1 - a) / 2, (r[o].area[1] + 5 + l - e.height / 2) * a)), i.stroke(), i.restore()
                }
                i.restore()
            }

            function Nt(t, e, n, i) {
                var r = 4 < arguments.length && void 0 !== arguments[4] ? arguments[4] : 1, o = Xt({}, {
                        activeWidth: 10,
                        activeOpacity: .3,
                        border: !1,
                        borderWidth: 2,
                        borderColor: "#FFFFFF",
                        fillOpacity: 1,
                        labelAlign: "right"
                    }, e.extra.funnel), c = (e.height - e.area[0] - e.area[2]) / t.length,
                    l = {x: e.area[3] + (e.width - e.area[1] - e.area[3]) / 2, y: e.height - e.area[2]},
                    s = o.activeWidth,
                    u = Math.min((e.width - e.area[1] - e.area[3]) / 2 - s, (e.height - e.area[0] - e.area[2]) / 2 - s);
                t = N(t, u, r), i.save(), i.translate(l.x, l.y);
                for (var d = 0; d < t.length; d++) 0 == d ? (e.tooltip && e.tooltip.index == d && (i.beginPath(), i.setFillStyle(a(t[d].color, o.activeOpacity)), i.moveTo(-s, 0), i.lineTo(-t[d].radius - s, -c), i.lineTo(t[d].radius + s, -c), i.lineTo(s, 0), i.lineTo(-s, 0), i.closePath(), i.fill()), t[d].funnelArea = [l.x - t[d].radius, l.y - c, l.x + t[d].radius, l.y], i.beginPath(), i.setLineWidth(o.borderWidth * e.pixelRatio), i.setStrokeStyle(o.borderColor), i.setFillStyle(a(t[d].color, o.fillOpacity)), i.moveTo(0, 0), i.lineTo(-t[d].radius, -c), i.lineTo(t[d].radius, -c), i.lineTo(0, 0), i.closePath(), i.fill(), 1 == o.border && i.stroke()) : (e.tooltip && e.tooltip.index == d && (i.beginPath(), i.setFillStyle(a(t[d].color, o.activeOpacity)), i.moveTo(0, 0), i.lineTo(-t[d - 1].radius - s, 0), i.lineTo(-t[d].radius - s, -c), i.lineTo(t[d].radius + s, -c), i.lineTo(t[d - 1].radius + s, 0), i.lineTo(0, 0), i.closePath(), i.fill()), t[d].funnelArea = [l.x - t[d].radius, l.y - c * (d + 1), l.x + t[d].radius, l.y - c * d], i.beginPath(), i.setLineWidth(o.borderWidth * e.pixelRatio), i.setStrokeStyle(o.borderColor), i.setFillStyle(a(t[d].color, o.fillOpacity)), i.moveTo(0, 0), i.lineTo(-t[d - 1].radius, 0), i.lineTo(-t[d].radius, -c), i.lineTo(t[d].radius, -c), i.lineTo(t[d - 1].radius, 0), i.lineTo(0, 0), i.closePath(), i.fill(), 1 == o.border && i.stroke()), i.translate(0, -c);
                return i.restore(), !1 !== e.dataLabel && 1 === r && Wt(t, e, i, c, o.labelAlign, s, l), {
                    center: l,
                    radius: u,
                    series: t
                }
            }

            function Wt(t, e, n, i, a, r, o) {
                for (var c = Math.PI, l = 0; l < t.length; l++) {
                    var s = void 0, u = void 0, d = void 0, f = void 0, p = t[l],
                        h = p.format ? p.format(+p._proportion_.toFixed(2)) : Jt.toFixed(100 * p._proportion_) + "%";
                    "right" == a ? (s = 0 == l ? (p.funnelArea[2] + o.x) / 2 : (p.funnelArea[2] + t[l - 1].funnelArea[2]) / 2, u = s + 2 * r, d = p.funnelArea[1] + i / 2, f = p.textSize || e.fontSize, n.setLineWidth(1 * e.pixelRatio), n.setStrokeStyle(p.color), n.setFillStyle(p.color), n.beginPath(), n.moveTo(s, d), n.lineTo(u, d), n.stroke(), n.closePath(), n.beginPath(), n.moveTo(u, d), n.arc(u, d, 2, 0, 2 * c), n.closePath(), n.fill(), n.beginPath(), n.setFontSize(f), n.setFillStyle(p.textColor || "#666666"), n.fillText(h, u + 5, d + f / 2 - 2), n.closePath(), n.stroke(), n.closePath()) : (s = 0 == l ? (p.funnelArea[0] + o.x) / 2 : (p.funnelArea[0] + t[l - 1].funnelArea[0]) / 2, u = s - 2 * r, d = p.funnelArea[1] + i / 2, f = p.textSize || e.fontSize, n.setLineWidth(1 * e.pixelRatio), n.setStrokeStyle(p.color), n.setFillStyle(p.color), n.beginPath(), n.moveTo(s, d), n.lineTo(u, d), n.stroke(), n.closePath(), n.beginPath(), n.moveTo(u, d), n.arc(u, d, 2, 0, 2 * c), n.closePath(), n.fill(), n.beginPath(), n.setFontSize(f), n.setFillStyle(p.textColor || "#666666"), n.fillText(h, u - 5 - g(h), d + f / 2 - 2), n.closePath(), n.stroke(), n.closePath())
                }
            }

            function Ht(t, e) {
                e.draw()
            }

            function Ut(t) {
                this.isStop = !1, t.duration = void 0 === t.duration ? 1e3 : t.duration, t.timing = t.timing || "linear";
                var e = "undefined" == typeof setTimeout ? "undefined" == typeof requestAnimationFrame ? function (t) {
                    t(null)
                } : requestAnimationFrame : function (t, e) {
                    setTimeout(function () {
                        var e = +new Date;
                        t(e)
                    }, e)
                }, n = null, i = function (a) {
                    if (null === a || !0 === this.isStop) return t.onProcess && t.onProcess(1), void (t.onAnimationFinish && t.onAnimationFinish());
                    if (null === n && (n = a), a - n < t.duration) {
                        var r = (a - n) / t.duration;
                        r = (0, Qt[t.timing])(r), t.onProcess && t.onProcess(r), e(i, 17)
                    } else t.onProcess && t.onProcess(1), t.onAnimationFinish && t.onAnimationFinish()
                };
                i = i.bind(this), e(i, 17)
            }

            function Vt(t, e, n, i) {
                var a = this, r = e.series, c = e.categories;
                r = p(r, e, n);
                var l = e.animation ? e.duration : 0;
                this.animationInstance && this.animationInstance.stop();
                var s = null;
                if ("candle" == t) {
                    var u = Xt({}, e.extra.candle.average);
                    u.show ? (s = o(u.day, u.name, u.color, r[0].data), s = p(s, e, n), e.seriesMA = s) : s = e.seriesMA ? e.seriesMA = p(e.seriesMA, e, n) : r
                } else s = r;
                e._series_ = r = A(r), e.area = [, , , ,];
                for (var d = 0; 4 > d; d++) e.area[d] = e.padding[d];
                var f = E(s, e, n, e.chartData), h = f.area.wholeHeight, g = f.area.wholeWidth;
                switch (e.legend.position) {
                    case"top":
                        e.area[0] += h;
                        break;
                    case"bottom":
                        e.area[2] += h;
                        break;
                    case"left":
                        e.area[3] += g;
                        break;
                    case"right":
                        e.area[1] += g
                }
                var v = {}, y = 0;
                if ("line" === e.type || "column" === e.type || "area" === e.type || "mix" === e.type || "candle" === e.type) {
                    if (v = et(r, e, n), y = v.yAxisWidth, e.yAxis.showTitle) {
                        for (var m = 0, _ = 0; _ < e.yAxis.data.length; _++) m = Math.max(m, e.yAxis.data[_].titleFontSize ? e.yAxis.data[_].titleFontSize : n.fontSize);
                        e.area[0] += (m + 6) * e.pixelRatio
                    }
                    for (var x = 0, b = 0, P = 0; P < y.length; P++) "left" == y[P].position ? (e.area[3] += 0 < b ? y[P].width + e.yAxis.padding : y[P].width, b += 1) : (e.area[1] += 0 < x ? y[P].width + e.yAxis.padding : y[P].width, x += 1)
                } else n.yAxisWidth = y;
                if (e.chartData.yAxisData = v, e.categories && e.categories.length) {
                    e.chartData.xAxisData = Q(e.categories, e, n);
                    var w = I(e.categories, e, n, e.chartData.xAxisData.eachSpacing), S = w.xAxisHeight, T = w.angle;
                    n.xAxisHeight = S, n._xAxisTextAngle_ = T, e.area[2] += S, e.chartData.categoriesData = w
                } else e.chartData.xAxisData = {xAxisPoints: []};
                if (e.enableScroll && "right" == e.xAxis.scrollAlign && void 0 === e._scrollDistance_) {
                    var k = 0, M = e.chartData.xAxisData.xAxisPoints, O = e.chartData.xAxisData.startX;
                    k = e.chartData.xAxisData.endX - O - e.chartData.xAxisData.eachSpacing * (M.length - 1), a.scrollOption = {
                        currentOffset: k,
                        startTouchX: k,
                        distance: 0,
                        lastMoveTime: 0
                    }, e._scrollDistance_ = k
                }
                switch (("pie" === t || "ring" === t || "rose" === t) && (n._pieTextMaxLength_ = !1 === e.dataLabel ? 0 : q(s)), t) {
                    case"word":
                        var C = Xt({}, {type: "normal", autoColors: !0}, e.extra.word);
                        (1 == e.updateData || null == e.updateData) && (e.chartData.wordCloudData = zt(e, C.type)), this.animationInstance = new Ut({
                            timing: "easeInOut",
                            duration: l,
                            onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), Bt(r, e, n, i, t), Ht(e, i)
                            },
                            onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"map":
                        i.clearRect(0, 0, e.width, e.height), It(r, e, n, i);
                        break;
                    case"funnel":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.funnelData = Nt(r, e, n, i, t), At(e.series, e, n, i, e.chartData), bt(e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"line":
                        this.animationInstance = new Ut({
                            timing: "easeIn", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), wt(c, e, n, i), Pt(c, e, n, i);
                                var a = _t(r, e, n, i, t), o = a.xAxisPoints, l = a.calPoints, s = a.eachSpacing;
                                e.chartData.xAxisPoints = o, e.chartData.calPoints = l, e.chartData.eachSpacing = s, St(r, e, n, i), !1 !== e.enableMarkLine && 1 === t && ft(e, n, i), At(e.series, e, n, i, e.chartData), bt(e, n, i, t, s, o), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"mix":
                        this.animationInstance = new Ut({
                            timing: "easeIn", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), wt(c, e, n, i), Pt(c, e, n, i);
                                var a = xt(r, e, n, i, t), o = a.xAxisPoints, l = a.calPoints, s = a.eachSpacing;
                                e.chartData.xAxisPoints = o, e.chartData.calPoints = l, e.chartData.eachSpacing = s, St(r, e, n, i), !1 !== e.enableMarkLine && 1 === t && ft(e, n, i), At(e.series, e, n, i, e.chartData), bt(e, n, i, t, s, o), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"column":
                        this.animationInstance = new Ut({
                            timing: "easeIn", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), wt(c, e, n, i), Pt(c, e, n, i);
                                var a = vt(r, e, n, i, t), o = a.xAxisPoints, l = a.calPoints, s = a.eachSpacing;
                                e.chartData.xAxisPoints = o, e.chartData.calPoints = l, e.chartData.eachSpacing = s, St(r, e, n, i), !1 !== e.enableMarkLine && 1 === t && ft(e, n, i), At(e.series, e, n, i, e.chartData), bt(e, n, i, t, s, o), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"area":
                        this.animationInstance = new Ut({
                            timing: "easeIn", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), wt(c, e, n, i), Pt(c, e, n, i);
                                var a = mt(r, e, n, i, t), o = a.xAxisPoints, l = a.calPoints, s = a.eachSpacing;
                                e.chartData.xAxisPoints = o, e.chartData.calPoints = l, e.chartData.eachSpacing = s, St(r, e, n, i), !1 !== e.enableMarkLine && 1 === t && ft(e, n, i), At(e.series, e, n, i, e.chartData), bt(e, n, i, t, s, o), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"ring":
                    case"pie":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.pieData = Tt(r, e, n, i, t), At(e.series, e, n, i, e.chartData), bt(e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"rose":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.pieData = kt(r, e, n, i, t), At(e.series, e, n, i, e.chartData), bt(e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"radar":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.radarData = Ct(r, e, n, i, t), At(e.series, e, n, i, e.chartData), bt(e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"arcbar":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.arcbarData = Mt(r, e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"gauge":
                        this.animationInstance = new Ut({
                            timing: "easeInOut", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), e.chartData.gaugeData = Ot(c, r, e, n, i, t), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        });
                        break;
                    case"candle":
                        this.animationInstance = new Ut({
                            timing: "easeIn", duration: l, onProcess: function (t) {
                                i.clearRect(0, 0, e.width, e.height), e.rotate && at(i, e), wt(c, e, n, i), Pt(c, e, n, i);
                                var a = yt(r, s, e, n, i, t), o = a.xAxisPoints, l = a.calPoints, u = a.eachSpacing;
                                e.chartData.xAxisPoints = o, e.chartData.calPoints = l, e.chartData.eachSpacing = u, St(r, e, n, i), !1 !== e.enableMarkLine && 1 === t && ft(e, n, i), At(s || e.series, e, n, i, e.chartData), bt(e, n, i, t, u, o), Ht(e, i)
                            }, onAnimationFinish: function () {
                                a.event.trigger("renderComplete")
                            }
                        })
                }
            }

            function qt() {
                this.events = {}
            }

            var Gt = {
                yAxisWidth: 15,
                yAxisSplit: 5,
                xAxisHeight: 15,
                xAxisLineHeight: 15,
                legendHeight: 15,
                yAxisTitleWidth: 15,
                padding: [10, 10, 10, 10],
                pixelRatio: 1,
                rotate: !1,
                columePadding: 3,
                fontSize: 13,
                dataPointShape: ["circle", "circle", "circle", "circle"],
                colors: ["#1890ff", "#2fc25b", "#facc14", "#f04864", "#8543e0", "#90ed7d"],
                pieChartLinePadding: 15,
                pieChartTextPadding: 5,
                xAxisTextPadding: 3,
                titleColor: "#333333",
                titleFontSize: 20,
                subtitleColor: "#999999",
                subtitleFontSize: 15,
                toolTipPadding: 3,
                toolTipBackground: "#000000",
                toolTipOpacity: .7,
                toolTipLineHeight: 20,
                radarGridCount: 3,
                radarLabelTextMargin: 15,
                gaugeLabelTextMargin: 15
            }, Xt = Object.assign ? Object.assign : function (t) {
                if (null == t) throw new TypeError("Cannot convert undefined or null to object");
                for (var e, n = Object(t), i = 1; i < arguments.length; i++) if (null != (e = arguments[i])) for (var a in e) Object.prototype.hasOwnProperty.call(e, a) && (n[a] = e[a]);
                return n
            }, Jt = {
                toFixed: function (t, e) {
                    return e = e || 2, this.isFloat(t) && (t = t.toFixed(e)), t
                }, isFloat: function (t) {
                    return 0 != t % 1
                }, approximatelyEqual: function (t, e) {
                    return 1e-10 > Math.abs(t - e)
                }, isSameSign: function (t, e) {
                    var n = Math.abs;
                    return n(t) === t && n(e) === e || n(t) !== t && n(e) !== e
                }, isSameXCoordinateArea: function (t, e) {
                    return this.isSameSign(t.x, e.x)
                }, isCollision: function (t, e) {
                    return t.end = {}, t.end.x = t.start.x + t.width, t.end.y = t.start.y - t.height, e.end = {}, e.end.x = e.start.x + e.width, e.end.y = e.start.y - e.height, !(e.start.x > t.end.x || e.end.x < t.start.x || e.end.y > t.start.y || e.start.y < t.end.y)
                }
            }, Qt = {
                easeIn: function (t) {
                    return Math.pow(t, 3)
                }, easeOut: function (t) {
                    return Math.pow(t - 1, 3) + 1
                }, easeInOut: function (t) {
                    var e = Math.pow;
                    return 1 > (t /= .5) ? .5 * e(t, 3) : .5 * (e(t - 2, 3) + 2)
                }, linear: function (t) {
                    return t
                }
            };
            Ut.prototype.stop = function () {
                this.isStop = !0
            }, qt.prototype.addEventListener = function (t, e) {
                this.events[t] = this.events[t] || [], this.events[t].push(e)
            }, qt.prototype.trigger = function () {
                for (var t = arguments.length, e = Array(t), n = 0; n < t; n++) e[n] = arguments[n];
                var i = e[0], a = e.slice(1);
                !this.events[i] || this.events[i].forEach(function (t) {
                    try {
                        t.apply(null, a)
                    } catch (t) {
                        console.error(t)
                    }
                })
            };
            var Kt = function (t) {
                t.pixelRatio = t.pixelRatio ? t.pixelRatio : 1, t.fontSize = t.fontSize ? t.fontSize * t.pixelRatio : 13 * t.pixelRatio, t.title = Xt({}, t.title), t.subtitle = Xt({}, t.subtitle), t.duration = t.duration ? t.duration : 1e3, t.yAxis = Xt({}, {
                    data: [],
                    showTitle: !1,
                    disabled: !1,
                    disableGrid: !1,
                    splitNumber: 5,
                    gridType: "solid",
                    dashLength: 4 * t.pixelRatio,
                    gridColor: "#cccccc",
                    padding: 10,
                    fontColor: "#666666"
                }, t.yAxis), t.yAxis.dashLength *= t.pixelRatio, t.yAxis.padding *= t.pixelRatio, t.xAxis = Xt({}, {
                    rotateLabel: !1,
                    type: "calibration",
                    gridType: "solid",
                    dashLength: 4,
                    scrollAlign: "left",
                    boundaryGap: "center",
                    axisLine: !0,
                    axisLineColor: "#cccccc"
                }, t.xAxis), t.xAxis.dashLength *= t.pixelRatio, t.legend = Xt({}, {
                    show: !0,
                    position: "bottom",
                    float: "center",
                    backgroundColor: "rgba(0,0,0,0)",
                    borderColor: "rgba(0,0,0,0)",
                    borderWidth: 0,
                    padding: 5,
                    margin: 5,
                    itemGap: 10,
                    fontSize: t.fontSize,
                    lineHeight: t.fontSize,
                    fontColor: "#333333",
                    format: {},
                    hiddenColor: "#CECECE"
                }, t.legend), t.legend.borderWidth *= t.pixelRatio, t.legend.itemGap *= t.pixelRatio, t.legend.padding *= t.pixelRatio, t.legend.margin *= t.pixelRatio, t.extra = Xt({}, t.extra), t.rotate = !!t.rotate, t.animation = !!t.animation;
                var n = JSON.parse(JSON.stringify(Gt));
                if (n.colors = t.colors ? t.colors : n.colors, n.yAxisTitleWidth = !0 !== t.yAxis.disabled && t.yAxis.title ? n.yAxisTitleWidth : 0, ("pie" == t.type || "ring" == t.type) && (n.pieChartLinePadding = !1 === t.dataLabel ? 0 : t.extra.pie.labelWidth * t.pixelRatio || n.pieChartLinePadding * t.pixelRatio), "rose" == t.type && (n.pieChartLinePadding = !1 === t.dataLabel ? 0 : t.extra.rose.labelWidth * t.pixelRatio || n.pieChartLinePadding * t.pixelRatio), n.pieChartTextPadding = !1 === t.dataLabel ? 0 : n.pieChartTextPadding * t.pixelRatio, n.yAxisSplit = t.yAxis.splitNumber ? t.yAxis.splitNumber : Gt.yAxisSplit, n.rotate = t.rotate, t.rotate) {
                    var i = t.width, a = t.height;
                    t.width = a, t.height = i
                }
                t.padding = t.padding ? t.padding : n.padding;
                for (var r = 0; 4 > r; r++) t.padding[r] *= t.pixelRatio;
                n.yAxisWidth = Gt.yAxisWidth * t.pixelRatio, n.xAxisHeight = Gt.xAxisHeight * t.pixelRatio, t.enableScroll && t.xAxis.scrollShow && (n.xAxisHeight += 6 * t.pixelRatio), n.xAxisLineHeight = Gt.xAxisLineHeight * t.pixelRatio, n.fontSize = t.fontSize, n.titleFontSize = Gt.titleFontSize * t.pixelRatio, n.subtitleFontSize = Gt.subtitleFontSize * t.pixelRatio, n.toolTipPadding = Gt.toolTipPadding * t.pixelRatio, n.toolTipLineHeight = Gt.toolTipLineHeight * t.pixelRatio, n.columePadding = Gt.columePadding * t.pixelRatio, t.$this = t.$this ? t.$this : this, this.context = e.createCanvasContext(t.canvasId, t.$this), t.chartData = {}, this.event = new qt, this.scrollOption = {
                    currentOffset: 0,
                    startTouchX: 0,
                    distance: 0,
                    lastMoveTime: 0
                }, this.opts = t, this.config = n, Vt.call(this, t.type, t, n, this.context)
            };
            Kt.prototype.updateData = function () {
                var t = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {};
                switch (this.opts = Xt({}, this.opts, t), this.opts.updateData = !0, t.scrollPosition || "current") {
                    case"current":
                        this.opts._scrollDistance_ = this.scrollOption.currentOffset;
                        break;
                    case"left":
                        this.opts._scrollDistance_ = 0, this.scrollOption = {
                            currentOffset: 0,
                            startTouchX: 0,
                            distance: 0,
                            lastMoveTime: 0
                        };
                        break;
                    case"right":
                        var e = et(this.opts.series, this.opts, this.config).yAxisWidth;
                        this.config.yAxisWidth = e;
                        var n = 0, i = Q(this.opts.categories, this.opts, this.config), a = i.xAxisPoints, r = i.startX;
                        n = i.endX - r - i.eachSpacing * (a.length - 1), this.scrollOption = {
                            currentOffset: n,
                            startTouchX: n,
                            distance: 0,
                            lastMoveTime: 0
                        }, this.opts._scrollDistance_ = n
                }
                Vt.call(this, this.opts.type, this.opts, this.config, this.context)
            }, Kt.prototype.zoom = function () {
                var t = Math.round,
                    e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : this.opts.xAxis.itemCount;
                if (!0 === this.opts.enableScroll) {
                    var n = t(Math.abs(this.scrollOption.currentOffset) / this.opts.chartData.eachSpacing) + t(this.opts.xAxis.itemCount / 2);
                    this.opts.animation = !1, this.opts.xAxis.itemCount = e.itemCount;
                    var i = et(this.opts.series, this.opts, this.config).yAxisWidth;
                    this.config.yAxisWidth = i;
                    var a = 0, r = Q(this.opts.categories, this.opts, this.config), o = r.xAxisPoints, c = r.startX,
                        l = r.endX, s = r.eachSpacing, u = l - c, d = u - s * (o.length - 1);
                    0 < (a = u / 2 - s * n) && (a = 0), a < d && (a = d), this.scrollOption = {
                        currentOffset: a,
                        startTouchX: a,
                        distance: 0,
                        lastMoveTime: 0
                    }, this.opts._scrollDistance_ = a, Vt.call(this, this.opts.type, this.opts, this.config, this.context)
                } else console.log("请启用滚动条后使用！")
            }, Kt.prototype.stopAnimation = function () {
                this.animationInstance && this.animationInstance.stop()
            }, Kt.prototype.addEventListener = function (t, e) {
                this.event.addEventListener(t, e)
            }, Kt.prototype.getCurrentDataIndex = function (t) {
                var e = null;
                if (e = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0]) {
                    var n = m(e, this.opts, t);
                    return "pie" === this.opts.type || "ring" === this.opts.type || "rose" === this.opts.type ? j({
                        x: n.x,
                        y: n.y
                    }, this.opts.chartData.pieData) : "radar" === this.opts.type ? C({
                        x: n.x,
                        y: n.y
                    }, this.opts.chartData.radarData, this.opts.categories.length) : "funnel" === this.opts.type ? L({
                        x: n.x,
                        y: n.y
                    }, this.opts.chartData.funnelData) : "map" === this.opts.type ? $({
                        x: n.x,
                        y: n.y
                    }, this.opts) : "word" === this.opts.type ? D({
                        x: n.x,
                        y: n.y
                    }, this.opts.chartData.wordCloudData) : T({
                        x: n.x,
                        y: n.y
                    }, this.opts.chartData.xAxisPoints, this.opts, this.config, Math.abs(this.scrollOption.currentOffset))
                }
                return -1
            }, Kt.prototype.getLegendDataIndex = function (t) {
                var e = null;
                if (e = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0]) {
                    var n = m(e, this.opts, t);
                    return k({x: n.x, y: n.y}, this.opts.chartData.legendData)
                }
                return -1
            }, Kt.prototype.touchLegend = function (t) {
                var e = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, n = null;
                if (n = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0]) {
                    m(n, this.opts, t);
                    var i = this.getLegendDataIndex(t);
                    0 <= i && (this.opts.series[i].show = !this.opts.series[i].show, this.opts.animation = !!e.animation, this.opts._scrollDistance_ = this.scrollOption.currentOffset, Vt.call(this, this.opts.type, this.opts, this.config, this.context))
                }
            }, Kt.prototype.showToolTip = function (t) {
                var e = 1 < arguments.length && void 0 !== arguments[1] ? arguments[1] : {}, n = null;
                (n = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0]) || console.log("touchError");
                var i = m(n, this.opts, t), a = this.scrollOption.currentOffset,
                    r = Xt({}, this.opts, {_scrollDistance_: a, animation: !1});
                if ("line" === this.opts.type || "area" === this.opts.type || "column" === this.opts.type) {
                    var o = this.getCurrentDataIndex(t);
                    if (-1 < o) {
                        var c = _(this.opts.series, o);
                        if (0 !== c.length) {
                            var l = P(c, this.opts.chartData.calPoints, o, this.opts.categories, e), s = l.textList,
                                u = l.offset;
                            u.y = i.y, r.tooltip = {textList: s, offset: u, option: e, index: o}
                        }
                    }
                    Vt.call(this, r.type, r, this.config, this.context)
                }
                if ("mix" === this.opts.type) {
                    if (-1 < (o = this.getCurrentDataIndex(t)) && (a = this.scrollOption.currentOffset, r = Xt({}, this.opts, {
                        _scrollDistance_: a,
                        animation: !1
                    }), 0 !== (c = _(this.opts.series, o)).length)) {
                        var d = w(c, this.opts.chartData.calPoints, o, this.opts.categories, e);
                        s = d.textList, (u = d.offset).y = i.y, r.tooltip = {
                            textList: s,
                            offset: u,
                            option: e,
                            index: o
                        }
                    }
                    Vt.call(this, r.type, r, this.config, this.context)
                }
                "candle" === this.opts.type && (-1 < (o = this.getCurrentDataIndex(t)) && (a = this.scrollOption.currentOffset, r = Xt({}, this.opts, {
                    _scrollDistance_: a,
                    animation: !1
                }), 0 !== (c = _(this.opts.series, o)).length && (s = (l = S(this.opts.series[0].data, c, this.opts.chartData.calPoints, o, this.opts.categories, this.opts.extra.candle, e)).textList, (u = l.offset).y = i.y, r.tooltip = {
                    textList: s,
                    offset: u,
                    option: e,
                    index: o
                })), Vt.call(this, r.type, r, this.config, this.context)), "pie" !== this.opts.type && "ring" !== this.opts.type && "rose" !== this.opts.type && "funnel" !== this.opts.type || (-1 < (o = this.getCurrentDataIndex(t)) && (a = this.scrollOption.currentOffset, r = Xt({}, this.opts, {
                    _scrollDistance_: a,
                    animation: !1
                }), c = this.opts._series_[o], s = [{
                    text: e.format ? e.format(c) : c.name + ": " + c.data,
                    color: c.color
                }], u = {x: i.x, y: i.y}, r.tooltip = {
                    textList: s,
                    offset: u,
                    option: e,
                    index: o
                }), Vt.call(this, r.type, r, this.config, this.context)), "map" !== this.opts.type && "word" !== this.opts.type || (-1 < (o = this.getCurrentDataIndex(t)) && (a = this.scrollOption.currentOffset, r = Xt({}, this.opts, {
                    _scrollDistance_: a,
                    animation: !1
                }), c = this.opts._series_[o], s = [{
                    text: e.format ? e.format(c) : c.properties.name,
                    color: c.color
                }], u = {x: i.x, y: i.y}, r.tooltip = {
                    textList: s,
                    offset: u,
                    option: e,
                    index: o
                }), r.updateData = !1, Vt.call(this, r.type, r, this.config, this.context)), "radar" === this.opts.type && (-1 < (o = this.getCurrentDataIndex(t)) && (a = this.scrollOption.currentOffset, r = Xt({}, this.opts, {
                    _scrollDistance_: a,
                    animation: !1
                }), 0 !== (c = _(this.opts.series, o)).length && (s = c.map(function (t) {
                    return {text: e.format ? e.format(t) : t.name + ": " + t.data, color: t.color}
                }), u = {x: i.x, y: i.y}, r.tooltip = {
                    textList: s,
                    offset: u,
                    option: e,
                    index: o
                })), Vt.call(this, r.type, r, this.config, this.context))
            }, Kt.prototype.translate = function (t) {
                this.scrollOption = {currentOffset: t, startTouchX: t, distance: 0, lastMoveTime: 0};
                var e = Xt({}, this.opts, {_scrollDistance_: t, animation: !1});
                Vt.call(this, this.opts.type, e, this.config, this.context)
            }, Kt.prototype.scrollStart = function (t) {
                var e = null, n = m(e = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0], this.opts, t);
                e && !0 === this.opts.enableScroll && (this.scrollOption.startTouchX = n.x)
            }, Kt.prototype.scroll = function (t) {
                0 === this.scrollOption.lastMoveTime && (this.scrollOption.lastMoveTime = Date.now());
                var e = this.opts.extra.touchMoveLimit || 20, n = Date.now();
                if (!(n - this.scrollOption.lastMoveTime < Math.floor(1e3 / e))) {
                    this.scrollOption.lastMoveTime = n;
                    var i = null;
                    if ((i = t.changedTouches ? t.changedTouches[0] : t.mp.changedTouches[0]) && !0 === this.opts.enableScroll) {
                        var a;
                        a = m(i, this.opts, t).x - this.scrollOption.startTouchX;
                        var r = this.scrollOption.currentOffset,
                            o = c(this, r + a, this.opts.chartData, this.config, this.opts);
                        this.scrollOption.distance = a = o - r;
                        var l = Xt({}, this.opts, {_scrollDistance_: r + a, animation: !1});
                        return Vt.call(this, l.type, l, this.config, this.context), r + a
                    }
                }
            }, Kt.prototype.scrollEnd = function () {
                if (!0 === this.opts.enableScroll) {
                    var t = this.scrollOption, e = t.currentOffset, n = t.distance;
                    this.scrollOption.currentOffset = e + n, this.scrollOption.distance = 0
                }
            }, "object" == i(n) && "object" == i(n.exports) && (n.exports = Kt)
        }).call(this, i("543d").default, i("62e4")(e))
    }, a9f6: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f179")).default)
        }).call(this, n("543d").createPage)
    }, ab4f: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            list: {}, show: null, first: {}, is_storage: !1, setList: function (t) {
                void 0 === this.list[t] && (this.list[t] = !0)
            }
        };
        e.default = i
    }, ab96: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("cc49")).default)
        }).call(this, n("543d").createPage)
    }, ac31: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b42e")).default)
        }).call(this, n("543d").createPage)
    }, ac6b: function (t, e, n) {
        (function (t) {
            Object.defineProperty(e, "__esModule", {value: !0}), e.removeStorageSync = e.getStorageSync = e.setStorageSync = e.clearStorage = void 0;
            e.clearStorage = function () {
                t.clearStorage({
                    success: function (t) {
                        console.log(t)
                    }, fail: function (t) {
                        console.log(t)
                    }
                })
            };
            e.setStorageSync = function (e, n) {
                try {
                    t.setStorageSync(e, n)
                } catch (t) {
                    console.log(t)
                }
            };
            e.getStorageSync = function (e) {
                try {
                    return t.getStorageSync(e)
                } catch (t) {
                    console.log(t)
                }
            };
            e.removeStorageSync = function (e) {
                try {
                    t.removeStorageSync(e)
                } catch (t) {
                    console.log(t)
                }
            }
        }).call(this, n("543d").default)
    }, adef: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4c2a")).default)
        }).call(this, n("543d").createPage)
    }, afc3: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("12fe")).default)
        }).call(this, n("543d").createPage)
    }, b131c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e279")).default)
        }).call(this, n("543d").createPage)
    }, b1c7: function (t, e, n) {
        (function (t) {
            Object.defineProperty(e, "__esModule", {value: !0}), e.batchSave = e.hideLoading = e.showLoading = e.urlParamsToObject = e.randomString = e.objectValues = e.earthDistance = e.timeDifference = e.objectToUrlParams = e.strtotime = e.datetime = e.time = void 0;
            e.time = function () {
                return parseInt(Math.round(new Date / 1e3))
            };
            e.datetime = function (t, e) {
                void 0 !== t && null !== t || (t = "Y-m-d h:i:s"), void 0 !== e && null !== e || (e = this.time());
                var n = new Date;
                n.setTime(1e3 * e);
                var i = {
                    Y: n.getFullYear(),
                    "m+": n.getMonth() + 1 < 10 ? "0".concat(n.getMonth() + 1) : n.getMonth() + 1,
                    "d+": n.getDate() < 10 ? "0".concat(n.getDate()) : n.getDate(),
                    "h+": n.getHours() < 10 ? "0".concat(n.getHours()) : n.getHours(),
                    "i+": n.getMinutes() < 10 ? "0".concat(n.getMinutes()) : n.getMinutes(),
                    "s+": n.getSeconds() < 10 ? "0".concat(n.getSeconds()) : n.getSeconds(),
                    "q+": Math.floor((n.getMonth() + 3) / 3),
                    "S+": n.getMilliseconds()
                };
                for (var a in i) new RegExp("(" + a + ")").test(t) && (t = t.replace(RegExp.$1, 1 === RegExp.$1.length ? i[a] : ("00" + i[a]).substr(("" + i[a]).length)));
                return t
            };
            e.strtotime = function (t) {
            };
            e.objectToUrlParams = function (t, e) {
                var n = "";
                for (var i in t) n += "&" + i + "=" + (e ? encodeURIComponent(t[i]) : t[i]);
                return n.substr(1)
            };
            e.timeDifference = function (t, e) {
                var n = parseInt((e - t) / 1e3), i = 0, a = 0, r = 0, o = 0;
                return n > 0 ? (i = Math.floor(n / 86400), a = Math.floor(n / 3600) - 24 * i, r = Math.floor(n / 60) - 24 * i * 60 - 60 * a, o = Math.floor(n) - 24 * i * 60 * 60 - 60 * a * 60 - 60 * r, {
                    d: i,
                    h: a < 10 ? "0" + a : a,
                    m: r < 10 ? "0" + r : r,
                    s: o < 10 ? "0" + o : o
                }) : null
            };
            e.earthDistance = function (t, e) {
                function n(t) {
                    return t * h / 180
                }

                var i, a, r, o, c, l, s, u = parseFloat(t.lat), d = parseFloat(t.lng), f = parseFloat(e.lat),
                    p = parseFloat(e.lng), h = Math.PI, g = n((u + f) / 2), v = n((u - f) / 2), y = n((d - p) / 2),
                    m = Math.sin(v), _ = Math.sin(y), x = Math.sin(g);
                return m *= m, _ *= _, x *= x, i = m * (1 - _) + (1 - x) * _, a = (1 - m) * (1 - _) + x * _, r = Math.atan(Math.sqrt(i / a)), o = Math.sqrt(i * a) / r, c = 2 * r * 6378137, l = (3 * o - 1) / 2 / a, s = (3 * o + 1) / 2 / i, c * (1 + 1 / 298.257 * (l * x * (1 - m) - s * (1 - x) * m))
            };
            e.objectValues = function (t) {
                var e = [];
                for (var n in t) e.push(t[n]);
                return e
            };
            e.randomString = function (t) {
                for (var e = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", n = "", i = t; i > 0; --i) n += e[Math.floor(Math.random() * e.length)];
                return n
            };
            e.urlParamsToObject = function (t) {
                var e = t.split("&"), n = {};
                for (var i in e) if ("string" == typeof e[i] && e[i].length) {
                    var a = e[i].split("=");
                    2 === a.length && (n[a[0]] = a[1])
                }
                return n
            };
            e.showLoading = function () {
                t.showLoading({title: "加载中", mask: !0})
            };
            e.hideLoading = function () {
                t.hideLoading()
            };
            var n = !1;
            e.batchSave = function (e) {
                var i = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "image";
                return new Promise(function (a, r) {
                    e instanceof Array || (e = [e]);
                    var o = "image" === i ? "图片" : "视频";
                    new Promise(function (e, n) {
                        var i = "scope.writePhotosAlbum";
                        t.authorize({
                            scope: i, success: function (t) {
                                e("success")
                            }, fail: function (a) {
                                t.showModal({
                                    title: "提示",
                                    content: "您好,请先授权保存到相册权限",
                                    showCancel: !1,
                                    success: function (a) {
                                        a.confirm && t.openSetting({
                                            success: function (t) {
                                                t.authSetting[i] ? e("success") : n("fail")
                                            }
                                        })
                                    }
                                })
                            }
                        })
                    }).then(function (e) {
                        n ? t.showLoading({title: o + "保存中", mask: !0}) : (n = !0, t.showLoading({
                            title: o + "保存中",
                            mask: !0
                        }))
                    }).then(function (o) {
                        Promise.all(e.map(function (e) {
                            return new Promise(function (n, a) {
                                try {
                                    var r = t.downloadFile({
                                        url: e, success: function (e) {
                                            "image" === i && t.saveImageToPhotosAlbum({
                                                filePath: e.tempFilePath,
                                                success: function () {
                                                    n("success")
                                                },
                                                fail: function (t) {
                                                    console.log(t), a("fail")
                                                }
                                            }), "video" === i && t.saveVideoToPhotosAlbum({
                                                filePath: e.tempFilePath,
                                                success: function () {
                                                    n("success")
                                                },
                                                fail: function (t) {
                                                    console.log(t), a("fail")
                                                }
                                            })
                                        }, fail: function (t) {
                                            console.log(t), a("fail")
                                        }
                                    });
                                    if ("video" === i) {
                                        var o = (new Date).getTime();
                                        r.onProgressUpdate(function (t) {
                                            (new Date).getTime() - o > 6e4 && (r.abort(), a("fail"))
                                        })
                                    }
                                } catch (t) {
                                    console.error(t.message), a("fail")
                                }
                            })
                        })).then(function (e) {
                            n = !1, t.hideLoading(), a("success")
                        }).catch(function (e) {
                            n = !1, t.hideLoading(), t.showToast({title: "下载失败"}), r("fail")
                        })
                    }).catch(function (e) {
                        t.showModal({title: "提示", content: "授权失败，请稍后重新获取", showCancel: !1}), r("fail")
                    })
                })
            }
        }).call(this, n("543d").default)
    }, b2cb: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("03b8")).default)
        }).call(this, n("543d").createPage)
    }, b3b4: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f188")).default)
        }).call(this, n("543d").createPage)
    }, b461: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2444")).default)
        }).call(this, n("543d").createPage)
    }, b4fe: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("bbb0")).default)
        }).call(this, n("543d").createPage)
    }, b62d: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("678a")).default)
        }).call(this, n("543d").createPage)
    }, b695: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("92c0")).default)
        }).call(this, n("543d").createPage)
    }, b6f8: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7843")).default)
        }).call(this, n("543d").createPage)
    }, b7dd: function (t, e, n) {
        function i(t, e, n, i, a, r, o) {
            try {
                var c = t[r](o), l = c.value
            } catch (t) {
                return void n(t)
            }
            c.done ? e(l) : Promise.resolve(l).then(i, a)
        }

        function a(t) {
            return function () {
                var e = this, n = arguments;
                return new Promise(function (a, r) {
                    function o(t) {
                        i(l, a, r, o, c, "next", t)
                    }

                    function c(t) {
                        i(l, a, r, o, c, "throw", t)
                    }

                    var l = t.apply(e, n);
                    o(void 0)
                })
            }
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var r = function (t) {
            return t && t.__esModule ? t : {default: t}
        }(n("a34a")), o = function () {
            var t = a(r.default.mark(function t(e) {
                return r.default.wrap(function (t) {
                    for (; ;) switch (t.prev = t.next) {
                        case 0:
                            (e = e || {}).isShow = !1, this.$store.dispatch("loading/actionGetLoading", e);
                        case 3:
                        case"end":
                            return t.stop()
                    }
                }, t, this)
            }));
            return function (e) {
                return t.apply(this, arguments)
            }
        }();
        e.default = o
    }, b954: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("1d92")).default)
        }).call(this, n("543d").createPage)
    }, ba56: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3ab6")).default)
        }).call(this, n("543d").createPage)
    }, baad: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d68f")).default)
        }).call(this, n("543d").createPage)
    }, bbdd: function (e, n, i) {
        var a = function () {
                return this || "object" === ("undefined" == typeof self ? "undefined" : t(self)) && self
            }() || Function("return this")(),
            r = a.regeneratorRuntime && Object.getOwnPropertyNames(a).indexOf("regeneratorRuntime") >= 0,
            o = r && a.regeneratorRuntime;
        if (a.regeneratorRuntime = void 0, e.exports = i("96cf"), r) a.regeneratorRuntime = o; else try {
            delete a.regeneratorRuntime
        } catch (t) {
            a.regeneratorRuntime = void 0
        }
    }, bbf9: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b379")).default)
        }).call(this, n("543d").createPage)
    }, bd5a: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a491")).default)
        }).call(this, n("543d").createPage)
    }, bec7: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c82d")).default)
        }).call(this, n("543d").createPage)
    }, bf78: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("79bc")).default)
        }).call(this, n("543d").createPage)
    }, bf8c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b89b")).default)
        }).call(this, n("543d").createPage)
    }, bfa7: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("57fa")).default)
        }).call(this, n("543d").createPage)
    }, bfd3: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a371")).default)
        }).call(this, n("543d").createPage)
    }, c00f: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e329")).default)
        }).call(this, n("543d").createPage)
    }, c3b2: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0,
            state: {scrollTop: 0, isScanQrCode: !1, coupon: {list: [], type: ""}, query: null},
            getters: {
                getScrollTop: function (t) {
                    return t.scrollTop
                }, getIsScanQrCode: function (t) {
                    return t.isScanQrCode
                }, getCoupon: function (t) {
                    return t.coupon
                }, getQuery: function (t) {
                    return t.query
                }
            },
            mutations: {
                mutSetScrollTop: function (t, e) {
                    t.scrollTop = e
                }, mutSetIsScanQrCode: function (t, e) {
                    t.isScanQrCode = e
                }, mutSetCoupon: function (t, e) {
                    t.coupon = e
                }, mutSetQuery: function (t, e) {
                    t.query = e
                }
            },
            actions: {
                actionSetScrollTop: function (t, e) {
                    t.commit("mutSetScrollTop", e)
                }, actionSetIsScanQrCode: function (t, e) {
                    t.commit("mutSetIsScanQrCode", e)
                }, actionSetCoupon: function (t, e) {
                    t.commit("mutSetCoupon", e)
                }, actionSetQeury: function (t, e) {
                    t.commit("mutSetQuery", e)
                }
            }
        };
        e.default = i
    }, c86a: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9700")).default)
        }).call(this, n("543d").createPage)
    }, c8ba: function (e, n) {
        var i;
        i = function () {
            return this
        }();
        try {
            i = i || new Function("return this")()
        } catch (e) {
            "object" === ("undefined" == typeof window ? "undefined" : t(window)) && (i = window)
        }
        e.exports = i
    }, c8bb: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("0bb8")).default)
        }).call(this, n("543d").createPage)
    }, ca78: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b501")).default)
        }).call(this, n("543d").createPage)
    }, cad9: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9f10")).default)
        }).call(this, n("543d").createPage)
    }, cc1f6: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("58c3")).default)
        }).call(this, n("543d").createPage)
    }, ce85: function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = i(n("27f5")), r = i(n("9dc18")), o = {
            namespaced: !0, state: {data: null}, getters: {
                data: function (t) {
                    return t.data
                }
            }, mutations: {
                data: function (t, e) {
                    t.data = e
                }
            }, actions: {
                data: function (t) {
                    console.log("action data"), (0, r.default)({url: a.default.user.config}).then(function (e) {
                        0 === e.code && e.data && e.data.config && e.data.config.user_center && t.commit("data", e.data.config.user_center)
                    }).catch(function (t) {
                    })
                }
            }
        };
        e.default = o
    }, d1b8: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3d02")).default)
        }).call(this, n("543d").createPage)
    }, d1e4: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {namespaced: !0, state: {year: 1}};
        e.default = i
    }, d254: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f879")).default)
        }).call(this, n("543d").createPage)
    }, d26c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f99b")).default)
        }).call(this, n("543d").createPage)
    }, d30e: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = function (t) {
            return t && t.__esModule ? t : {default: t}
        }(n("d3b5")), a = {
            namespaced: !0,
            state: {
                auth_page: {},
                bar_title: {},
                cat_style: {},
                copyright: {},
                mall: {setting: {}},
                navbar: {navs: []},
                plugin: {},
                share_setting: [],
                share_setting_custom: {},
                user_center: {},
                __wxapp_img: {},
                theme: "classic-red",
                windowHeight: {height: 0, width: 0, boolean: !1}
            },
            getters: {
                getNavBar: function (t) {
                    return t.navbar
                }, getNavBarNavs: function (t) {
                    for (var e = 0, n = t.navbar.navs.length; e < n; e++) ;
                }, getUserCenter: function (t) {
                    return t.user_center
                }, getWxappImg: function (t) {
                    return t.__wxapp_img
                }, getCatStyle: function (t) {
                    return t.cat_style
                }, getVip: function (t) {
                    return t.plugin.vip_card
                }
            },
            mutations: {
                mutSetConfig: function (t, e) {
                    for (var n in e) {
                        if ("navbar" === n) for (var i = 0; i < e[n].navs.length; i++) e[n].navs[i].id = i;
                        t[n] = e[n]
                    }
                }, mutSetHeight: function (t, e) {
                    t.windowHeight = e
                }
            },
            actions: {
                actionGetConfig: function (t) {
                    i.default.getConfig().then(function (e) {
                        t.commit("mutSetConfig", e)
                    }).catch(function (t) {
                        console.log(t)
                    })
                }, actionHeight: function (t, e) {
                    t.commit("mutSetHeight", e)
                }, actionResetConfig: function (t) {
                    i.default.resetConfig(), i.default.getConfig().then(function (e) {
                        t.commit("mutSetConfig", e)
                    }).catch(function (t) {
                        console.log(t)
                    })
                }
            }
        };
        e.default = a
    }, d3b5: function (t, e, n) {
        (function (t) {
            function i(t) {
                return t && t.__esModule ? t : {default: t}
            }

            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var a = i(n("9dc18")), r = i(n("27f5")), o = !0, c = !1, l = null, s = "_APP_CONFIG", u = [], d = [],
                f = function (e, n) {
                    e && u.push(e), n && d.push(n), c || (c = !0, (0, a.default)({url: r.default.index.config}).then(function (e) {
                        if (c = !1, 0 === e.code) {
                            for (var n in l = e.data, t.setStorageSync(s, l), u) u[n](l);
                            u = []
                        } else {
                            for (var i in d) d[i](e.msg);
                            d = []
                        }
                    }).catch(function (t) {
                        for (var e in c = !1, d) d[e](t.msg);
                        d = []
                    }))
                }, p = {
                    getConfig: function (e) {
                        return new Promise(function (e, n) {
                            return l ? e(l) : (l = t.getStorageSync(s)) ? (o && (o = !1, f()), e(l)) : void f(e, n)
                        })
                    }, resetConfig: function () {
                        l = null, c = !1
                    }
                };
            e.default = p
        }).call(this, n("543d").default)
    }, d43c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d2e6")).default)
        }).call(this, n("543d").createPage)
    }, d537: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f75a")).default)
        }).call(this, n("543d").createPage)
    }, d54b: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("7377")).default)
        }).call(this, n("543d").createPage)
    }, d706: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2d0f")).default)
        }).call(this, n("543d").createPage)
    }, d80c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("425c")).default)
        }).call(this, n("543d").createPage)
    }, db14: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e78d")).default)
        }).call(this, n("543d").createPage)
    }, dcdb: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0, state: {formData: null}, mutations: {
                mutSetFormData: function (t, e) {
                    t.formData = e
                }
            }, actions: {}
        };
        e.default = i
    }, dd00: function (t, e, n) {
        function i() {
            if ("function" != typeof WeakMap) return null;
            var t = new WeakMap;
            return i = function () {
                return t
            }, t
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = function (t) {
            if (t && t.__esModule) return t;
            var e = i();
            if (e && e.has(t)) return e.get(t);
            var n = {};
            if (null != t) {
                var a = Object.defineProperty && Object.getOwnPropertyDescriptor;
                for (var r in t) if (Object.prototype.hasOwnProperty.call(t, r)) {
                    var o = a ? Object.getOwnPropertyDescriptor(t, r) : null;
                    o && (o.get || o.set) ? Object.defineProperty(n, r, o) : n[r] = t[r]
                }
            }
            return n.default = t, e && e.set(t, n), n
        }(n("b1c7")), r = {
            route: function () {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null;
                if (!t) {
                    var e = getCurrentPages();
                    e.length && (t = e[e.length - 1])
                }
                return "/".concat(t.route.split("?")[0])
            }, routeWithOption: function () {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null;
                if (!t) {
                    var e = getCurrentPages();
                    e.length && (t = e[e.length - 1])
                }
                var n = "";
                return n = "/".concat(t.route.split("?")[0]), t.options && (n += "?" + a.objectToUrlParams(t.options)), n
            }, tabBarUrl: function () {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null;
                if (!t) {
                    var e = getCurrentPages();
                    e.length && (t = e[e.length - 1])
                }
                var n = "";
                if (n = "/".concat(t.route.split("?")[0]), t.options) {
                    var i = {};
                    for (var r in t.options) "user_id" != r && "scene" != r && (i[r] = t.options[r]);
                    (i = a.objectToUrlParams(i)) && (n += "?" + i)
                }
                return n
            }
        };
        e.default = r
    }, df87: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("b29c")).default)
        }).call(this, n("543d").createPage)
    }, df8a: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9f22")).default)
        }).call(this, n("543d").createPage)
    }, e020: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("73ff")).default)
        }).call(this, n("543d").createPage)
    }, e191: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("487c")).default)
        }).call(this, n("543d").createPage)
    }, e19b: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5293")).default)
        }).call(this, n("543d").createPage)
    }, e1fb: function (t, e, n) {
        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var i = {
            namespaced: !0,
            state: {emptyHeight: 0, botNavHei: 110, XBoolean: !1},
            getters: {
                getBoolEmpty: function (t) {
                    return {emptyHeight: t.emptyHeight, XBoolean: t.XBoolean}
                }, getNavHei: function (t) {
                    return t.botNavHei
                }, getBotHeight: function (t) {
                    return t.emptyHeight + t.botNavHei
                }, getAll: function (t) {
                    return t
                }, getEmpty: function (t) {
                    return t.emptyHeight
                }
            },
            mutations: {
                setXBoolean: function (t, e) {
                    t.XBoolean = e
                }, setEmptyHeight: function (t, e) {
                    t.emptyHeight = e
                }
            },
            actions: {
                setIphone: function (t, e) {
                    (e.model.indexOf("iPhone X") > -1 || e.model.indexOf("iPhone12") > -1 || e.model.indexOf("iPhone 11") > -1) && (t.commit("setXBoolean", !0), t.commit("setEmptyHeight", 50))
                }
            }
        };
        e.default = i
    }, e32e: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("525b")).default)
        }).call(this, n("543d").createPage)
    }, e3c0: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d674")).default)
        }).call(this, n("543d").createPage)
    }, e4b7: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("c938")).default)
        }).call(this, n("543d").createPage)
    }, e5e4: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("41b7")).default)
        }).call(this, n("543d").createPage)
    }, ea53: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("a48e")).default)
        }).call(this, n("543d").createPage)
    }, eb4f: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("4046")).default)
        }).call(this, n("543d").createPage)
    }, ee50: function (t, e, n) {
        (function (t) {
            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var n = {
                copyText: function (e) {
                    t.setClipboardData({
                        data: e, success: function () {
                            t.hideLoading(), t.showToast({title: "复制成功", icon: "none"})
                        }
                    })
                }
            };
            e.default = n
        }).call(this, n("543d").default)
    }, eef2: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("650b")).default)
        }).call(this, n("543d").createPage)
    }, f145: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("ff04")).default)
        }).call(this, n("543d").createPage)
    }, f154: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("77cb")).default)
        }).call(this, n("543d").createPage)
    }, f163: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("f100")).default)
        }).call(this, n("543d").createPage)
    }, f169: function (t, e, n) {
        function i(t) {
            return t && t.__esModule ? t : {default: t}
        }

        Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
        var a = n("b1c7"), r = i(n("9dc18")), o = i(n("27f5")), c = i(n("4360"));
        e.default = function (t) {
            console.log(t), void 0 === (t = t || {
                title: "这是一个分享页面",
                path: "/pages/index/index",
                params: {}
            }).params && (t.params = {});
            var e = 0;
            return this.$user.isLogin() && this.$store.state.user.info && (e = this.$store.state.user.info.options.user_id), void 0 === t.path || "/pages/index/index" === t.path ? t.path = "/pages/index/index?user_id=".concat(e, "&") + (0, a.objectToUrlParams)(t.params) : (t.params.path = t.path, t.params.user_id = e, t.path = "/pages/index/index?scene=share&user_id=".concat(e, "&params=").concat(JSON.stringify(t.params))), setTimeout(function () {
                (0, r.default)({url: o.default.coupon.share_coupon}).then(function (t) {
                    if (0 === t.code) {
                        var e = {list: t.data.list, type: "share"};
                        c.default.dispatch("page/actionSetCoupon", e)
                    }
                }).catch(function (t) {
                })
            }, 1e3), t
        }
    }, f29c: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("22e6")).default)
        }).call(this, n("543d").createPage)
    }, f539: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("ab0c")).default)
        }).call(this, n("543d").createPage)
    }, f612: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("3614")).default)
        }).call(this, n("543d").createPage)
    }, f918: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("de25")).default)
        }).call(this, n("543d").createPage)
    }, f9a3: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("9549")).default)
        }).call(this, n("543d").createPage)
    }, f9eb: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("e4df")).default)
        }).call(this, n("543d").createPage)
    }, fb15: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("d0fe")).default)
        }).call(this, n("543d").createPage)
    }, fbe2: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("781e")).default)
        }).call(this, n("543d").createPage)
    }, fbf7: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5f47")).default)
        }).call(this, n("543d").createPage)
    }, fd58: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("2d0d")).default)
        }).call(this, n("543d").createPage)
    }, fdf1: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("64ef")).default)
        }).call(this, n("543d").createPage)
    }, fe7e: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("206b")).default)
        }).call(this, n("543d").createPage)
    }, ff26: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("6e96")).default)
        }).call(this, n("543d").createPage)
    }, ff35: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("fd12")).default)
        }).call(this, n("543d").createPage)
    }, ff69: function (t, e, n) {
        (function (t) {
            function i(t, e, n, i, a, r, o) {
                try {
                    var c = t[r](o), l = c.value
                } catch (t) {
                    return void n(t)
                }
                c.done ? e(l) : Promise.resolve(l).then(i, a)
            }

            function a(t) {
                return function () {
                    var e = this, n = arguments;
                    return new Promise(function (a, r) {
                        function o(t) {
                            i(l, a, r, o, c, "next", t)
                        }

                        function c(t) {
                            i(l, a, r, o, c, "throw", t)
                        }

                        var l = t.apply(e, n);
                        o(void 0)
                    })
                }
            }

            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var r = function (t) {
                return t && t.__esModule ? t : {default: t}
            }(n("a34a")), o = {
                setNavigationBarTitle: function () {
                    var e = a(r.default.mark(function e(n, i) {
                        var a, o;
                        return r.default.wrap(function (e) {
                            for (; ;) switch (e.prev = e.next) {
                                case 0:
                                    if (!(Object.keys(n).length > 0)) {
                                        e.next = 9;
                                        break
                                    }
                                    a = 0, o = n.length;
                                case 2:
                                    if (!(a < o)) {
                                        e.next = 9;
                                        break
                                    }
                                    if (!i.includes(n[a].value)) {
                                        e.next = 6;
                                        break
                                    }
                                    return t.setNavigationBarTitle({title: n[a].new_name}), e.abrupt("return", n[a].new_name);
                                case 6:
                                    a++, e.next = 2;
                                    break;
                                case 9:
                                case"end":
                                    return e.stop()
                            }
                        }, e)
                    }));
                    return function (t, n) {
                        return e.apply(this, arguments)
                    }
                }()
            };
            e.default = o
        }).call(this, n("543d").default)
    }, ffa5: function (t, e, n) {
        (function (t) {
            Object.defineProperty(e, "__esModule", {value: !0}), e.default = void 0;
            var i = function (t) {
                return t && t.__esModule ? t : {default: t}
            }(n("9dc18")), a = {
                namespaced: !0,
                state: {theme: "", address_id: "", store_id: "", form_data: {}, big_gift_pic: ""},
                getters: {},
                mutations: {
                    addressId: function (t, e) {
                        t.address_id = e
                    }, storeId: function (t, e) {
                        t.store_id = e
                    }, setGiftPic: function (t, e) {
                        t.big_gift_pic = e
                    }, setTheme: function (t, e) {
                        switch (e) {
                            case 1:
                                t.theme = "streamer-gold-gift";
                                break;
                            case 2:
                                t.theme = "romantic-powder-gift";
                                break;
                            case 3:
                                t.theme = "taste-red-gift";
                                break;
                            case 4:
                                t.theme = "elegant-purple-gift";
                                break;
                            case 5:
                                t.theme = "fresh-green-gift";
                                break;
                            case 6:
                                t.theme = "business-blue-gift";
                                break;
                            default:
                                t.theme = "streamer-gold-gift"
                        }
                    }, setFormData: function (t, e) {
                        t.form_data = e
                    }
                },
                actions: {
                    getConfig: function (e, n) {
                        (0, i.default)({url: n, method: "get"}).then(function (e) {
                            t.hideLoading(), 0 === e.code ? context.commit("setTheme", Number(e.data.theme.id)) : 1 === e.code && console.log(e)
                        }).catch(function () {
                            t.hideLoading()
                        })
                    }
                }
            };
            e.default = a
        }).call(this, n("543d").default)
    }, fff4: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("5f51")).default)
        }).call(this, n("543d").createPage)
    }, fff7: function (t, e, n) {
        (function (t) {
            function e(t) {
                return t && t.__esModule ? t : {default: t}
            }

            n("6cdc"), e(n("66fd")), t(e(n("8208")).default)
        }).call(this, n("543d").createPage)
    }
}]);