// public/static/js/chatMember.js
(function () {
  const state = Vue.reactive({
    input: "",
    messages: [],
    kefuName: "",
    kefuAvatar: "",
    kefuGuid: "",
    sessionGuid: "",
    style: { box_color: "#009688" }, // 默认值，防止未赋值时报错
    voiceOpen: true, // 声音开关状态
    type: 2, // 设置type为2，显示声音开关
    wsStatus: {
      // WebSocket状态
      className: "disconnected",
      text: "连接断开",
    },
    previewImageUrl: "", // 图片预览URL
    // ...其它会员端状态
  });

  // 在开头添加工具函数
  function sanitizeMessage(content) {
    return content.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
  }

  function sendMessage() {
    if (!state.input.trim()) return;
    const local_id = ChatCore.createGuid();
    const msg = {
      content: state.input,
      from_guid: window.kefuConfig.customerId,
      from_name: window.kefuConfig.customerName,
      from_type: 1,
      from_avatar: window.kefuConfig.avatar,
      to_guid: state.kefuGuid,
      to_name: state.kefuName,
      bid: window.kefuConfig.seller,
      session_guid: state.sessionGuid,
      local_id,
    };

    // 本地回显
    state.messages.push({
      ...msg,
      status: "sending",
      guid: "",
      send_time: new Date().toLocaleString(),
      content: sanitizeMessage(state.input), // XSS防护
    });

    // 立即滚动到底部显示刚发送的消息
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);

    // 发送消息并处理重试
    ChatCore.resendMessage(msg).catch((error) => {
      console.error("消息发送失败", error);
      const idx = state.messages.findIndex((m) => m.local_id === local_id);
      if (idx !== -1) {
        state.messages[idx].status = "fail";
      }
    });

    state.input = "";
  }

  // 输入预览发送函数
  function sendTypingStatus(status, content) {
    if (!state.sessionGuid || !state.kefuGuid) {
      console.log("[输入预览] 会话信息不完整，跳过发送");
      return;
    }

    const typingData = {
      session_guid: state.sessionGuid,
      member_guid: window.kefuConfig.customerId,
      user_guid: state.kefuGuid,
      content: content,
      status: status,
      bid: window.kefuConfig.seller,
    };

    console.log("[输入预览] 发送状态:", status, "内容长度:", content.length);

    ChatCore.sendMsg("typing", typingData).catch((error) => {
      console.error("[输入预览] 发送失败:", error);
    });
  }

  // 初始化输入预览
  function initTypingPreview() {
    // 使用TypingManager初始化输入监听
    // 直接选择textarea元素
    ChatCore.TypingManager.init("textarea", sendTypingStatus);
    console.log("[输入预览] 初始化完成");
  }

  function registerEvents() {
    // userInit
    ChatCore.on("userInit", (msg) => {
      console.log("[会员端] 收到userInit响应:", msg);

      console.log("[会员端] userInit完整响应:", msg);
      console.log("[会员端] msg.data:", msg.data);
      console.log("[会员端] msg.data.code类型:", typeof msg.data?.code, "值:", msg.data?.code);

      if (msg.data && msg.data.code === 0 && msg.data.session) {
        // 成功分配客服
        const session = msg.data.session;
        state.kefuGuid = session.user_guid;
        state.kefuName = session.kefu_name || "";
        state.kefuAvatar = session.kefu_avatar || "";
        state.sessionGuid = session.guid;
        // 拉取历史消息
        fetchHistoryMessages(session.guid);

        // 初始化输入预览（在获得会话信息后）
        setTimeout(() => {
          initTypingPreview();
        }, 500);
      } else if (msg.data && (msg.data.code === 201 || msg.data.code === "201")) {
        // 没有客服在线 - 显示为系统消息
        console.log("[会员端] 收到code:201，准备显示系统消息:", msg.data.msg);
        addSystemMessage(msg.data.msg || "暂无客服在线，请稍后再来");
        console.log("[会员端] 系统消息已添加，当前消息数量:", state.messages.length);

        // 检查是否启用自动回复
        if (msg.data.auto_reply_enabled && window.AutoReplyModule) {
          console.log("[会员端] 自动回复已启用，等待触发消息");
        }
      } else if (msg.data && (msg.data.code === 202 || msg.data.code === "202")) {
        // 客服全忙，排队中 - 显示为系统消息
        console.log("[会员端] 收到code:202，准备显示系统消息:", msg.data.msg);
        addSystemMessage(msg.data.msg || "客服全忙，排队中请等待");
        console.log("[会员端] 系统消息已添加，当前消息数量:", state.messages.length);

        // 检查是否启用自动回复
        if (msg.data.auto_reply_enabled && window.AutoReplyModule) {
          console.log("[会员端] 自动回复已启用，等待触发消息");
        }
      } else {
        // 其他错误情况 - 显示为系统消息
        console.log("[会员端] 收到其他错误，code:", msg.data?.code, "准备显示系统消息:", msg.data?.msg);
        addSystemMessage(msg.data?.msg || "连接客服失败，请稍后重试");
        console.error("[会员端] userInit失败:", msg);
      }
    });
    // afterSend
    ChatCore.on("afterSend", (msg) => {
      if (msg.data && msg.data.local_id) {
        const idx = state.messages.findIndex((m) => m.local_id === msg.data.local_id);
        if (idx !== -1) {
          state.messages[idx] = { ...state.messages[idx], ...msg.data.data };
        }
      }
    });
    // chatMessage
    ChatCore.on("chatMessage", (msg) => {
      if (msg.data) {
        // 更严格的消息去重
        const isDuplicate = state.messages.some((m) => (m.guid === msg.data.guid && msg.data.guid) || (m.local_id === msg.data.local_id && msg.data.local_id));

        if (!isDuplicate) {
          // XSS防护
          const safeMsg = {
            ...msg.data,
            content: sanitizeMessage(msg.data.content),
          };

          state.messages.push(safeMsg);
          // 接收到新消息时智能滚动（丝滑效果）
          ChatCore.ScrollManager.smartScrollToBottom(".chat-box", 100, true);

          // 新消息提醒
          notifyNewMessage(safeMsg);
        }

        // 收到消息后自动发送已读回执
        if (!document.hidden) {
          sendReadMessage();
        }
      }
    });
    // rollBackMessage
    ChatCore.on("rollBackMessage", (msg) => {
      if (msg.data && msg.data.guid) {
        const m = state.messages.find((m) => m.guid === msg.data.guid);
        if (m) m.is_recall = 1;
      }
    });
    // 新增：WebSocket 事件 sessionUpdate 处理
    ChatCore.on("sessionUpdate", (msg) => {
      if (msg.data && msg.data.session_guid === state.sessionGuid) {
        // 处理客服变更
        if (msg.data.user_guid && msg.data.user_guid !== state.kefuGuid) {
          state.kefuGuid = msg.data.user_guid;
          // 更新客服名称和头像
          if (msg.data.kefu_name) {
            state.kefuName = msg.data.kefu_name;
          }
          if (msg.data.kefu_avatar) {
            state.kefuAvatar = msg.data.kefu_avatar;
          }
          // 如果有转接通知，显示提示
          if (msg.data.transfer_notice) {
            layerMsg("会话已转接给新客服，您可以继续对话");
          }
        }
        // 处理会话状态
        if (msg.data.status === 1) {
          layerMsg("客服已接入，您可以开始对话");
        } else if (msg.data.status === 2) {
          layerMsg("本次会话已结束，感谢您的咨询！");
        }
      }
    });

    // 已读回执
    ChatCore.on("readMessage", (msg) => {
      ChatCore.ReadManager.handleReadReceipt(msg, state.messages, state.sessionGuid, "会员端");
    });

    // 机器人消息事件 - 接收服务端下发的机器人消息
    ChatCore.on("robotMessage", (msg) => {
      console.log("[会员端] 收到机器人消息:", msg);
      if (msg.data) {
        handleRobotMessage(msg.data);
      }
    });

    // 机器人回复事件 - 接收用户点击快捷回复后的响应
    ChatCore.on("robotReply", (msg) => {
      console.log("[会员端] 收到机器人回复:", msg);
      if (msg.data) {
        handleRobotReply(msg.data);
      }
    });

    // ...其它事件
  }

  // 新增：拉取历史消息方法
  function fetchHistoryMessages(session_guid) {
    if (!session_guid) return;
    ajax_member_api_v1(
      "/kefu/message_list",
      { session_guid, page: 1, limit: 20 },
      function (res) {
        if (res.data && res.data.data) {
          // 消息去重，避免重复
          const newMsgs = res.data.data.filter((msg) => !state.messages.some((m) => m.guid === msg.guid));
          state.messages = [...newMsgs, ...state.messages];
          // 历史消息加载完成后多重滚动，确保滚动到真正的底部
          ChatCore.ScrollManager.scrollToBottomAfterLoad(".chat-box", true);
        }
      },
      function (res) {
        console.error("获取历史消息失败:", res.msg || "未知错误");
      }
    );
  }

  function sendReadMessage() {
    if (window.kefuConfig && state.kefuGuid && state.sessionGuid) {
      ChatCore.ReadManager.sendReadReceipt(
        state.sessionGuid,
        window.kefuConfig.customerId, // 会员的GUID（读消息的人）
        state.kefuGuid // 客服的GUID（消息发送方）
      );
    } else {
      console.log("会员端无法发送已读回执，缺少必要参数", {
        hasKefuConfig: !!window.kefuConfig,
        kefuGuid: state.kefuGuid,
        sessionGuid: state.sessionGuid,
      });
    }
  }

  function layerMsg(msg) {
    if (window.layui) {
      layui.use("layer", function () {
        layui.layer.msg(msg);
      });
    } else {
      alert(msg);
    }
  }

  // 添加系统消息到聊天区域
  function addSystemMessage(content) {
    console.log("[addSystemMessage] 开始添加系统消息:", content);

    const systemMsg = {
      guid: "system_" + Date.now(),
      content: content,
      msg_type: 99, // 系统消息类型
      from_type: 2, // 系统
      from_name: "系统",
      from_avatar: "",
      send_time: new Date().toLocaleString(),
      is_system: true, // 标记为系统消息
    };

    console.log("[addSystemMessage] 创建的系统消息对象:", systemMsg);

    state.messages.push(systemMsg);

    console.log("[addSystemMessage] 消息已添加到state.messages，当前总数:", state.messages.length);
    console.log("[addSystemMessage] 当前所有消息:", state.messages);

    // 系统消息添加后强制滚动到底部（丝滑效果）
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);
  }

  // 撤回消息方法，供模板 @click 调用
  function recallMessage(msg) {
    if (!msg || !msg.guid) {
      console.error("撤回消息失败：消息对象或消息ID不存在", msg);
      return;
    }

    // 使用 layui 确认对话框
    layui.use("layer", function () {
      const layer = layui.layer;
      layer.confirm(
        "确定要撤回这条消息吗？",
        {
          btn: ["确定", "取消"],
          title: "操作确认",
        },
        function (index) {
          console.log("准备撤回消息", msg);

          ChatCore.recallMessage({
            guid: msg.guid,
            session_guid: msg.session_guid,
            from_guid: window.kefuConfig?.customerId,
            bid: window.kefuConfig?.seller,
          })
            .then(() => {
              console.log("消息撤回成功");
              const messageIndex = state.messages.findIndex((m) => m.guid === msg.guid);
              if (messageIndex !== -1) {
                state.messages[messageIndex].is_recall = 1;
              }
            })
            .catch((error) => {
              console.error("消息撤回失败", error);
              layerMsg("撤回失败：" + error.message);
            });

          layer.close(index);
        }
      );
    });
  }

  // 添加新消息提醒
  function notifyNewMessage(msg) {
    // 只对客服发送的消息进行提醒
    if (msg.from_type === 2) {
      // 客服发送的消息
      ChatCore.NotificationManager.showNewMessageNotification(msg);
    }
  }

  // 添加声音提醒（保留兼容性）
  function playMessageSound() {
    ChatCore.NotificationManager.playSound();
  }

  // 图片上传相关方法
  function openImageUpload() {
    if (!state.sessionGuid) {
      layerMsg("请先连接客服");
      return;
    }
    document.getElementById("hiddenImageInput").click();
  }

  // 表情选择器相关
  let emojiPickerIndex = null;

  function openEmojiPicker() {
    if (!state.sessionGuid) {
      layerMsg("请先开始会话");
      return;
    }

    // 如果表情选择器已经打开，则关闭
    if (emojiPickerIndex) {
      layui.use(["layer"], function () {
        const layer = layui.layer;
        layer.close(emojiPickerIndex);
        emojiPickerIndex = null;
      });
      return;
    }

    // 创建表情选择器内容
    const emojiPickerHTML = ChatCore.EmojiManager.createEmojiPicker();

    // 添加CSS样式（如果还没有添加）
    if (!document.getElementById("emoji-picker-styles")) {
      const style = document.createElement("style");
      style.id = "emoji-picker-styles";
      style.textContent = `
        .emoji-picker {
          width: 320px;
          height: 280px;
          background: white;
          border: 1px solid #e6e6e6;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          display: flex;
          flex-direction: column;
          font-family: Arial, sans-serif;
        }

        .emoji-categories {
          display: flex;
          border-bottom: 1px solid #e6e6e6;
          background: #f8f9fa;
          border-radius: 8px 8px 0 0;
          padding: 8px;
          gap: 4px;
        }

        .emoji-category-tab {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 6px;
          font-size: 18px;
          transition: background-color 0.2s;
        }

        .emoji-category-tab:hover {
          background: #e9ecef;
        }

        .emoji-category-tab.active {
          background: #007bff;
          color: white;
        }

        .emoji-content {
          flex: 1;
          overflow-y: auto;
          padding: 8px;
        }

        .emoji-category-content {
          height: 100%;
        }

        .emoji-grid {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          gap: 4px;
          height: 100%;
        }

        .emoji-item {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 4px;
          font-size: 20px;
          transition: background-color 0.2s;
          user-select: none;
        }

        .emoji-item:hover {
          background: #f0f0f0;
          transform: scale(1.1);
        }

        .emoji-content::-webkit-scrollbar {
          width: 6px;
        }

        .emoji-content::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .emoji-content::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        .emoji-content::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
      `;
      document.head.appendChild(style);
    }

    // 使用layui的layer模块
    layui.use(["layer"], function () {
      const layer = layui.layer;

      // 打开表情选择器弹窗
      emojiPickerIndex = layer.open({
        type: 1,
        title: false,
        closeBtn: 0,
        shade: 0.3,
        shadeClose: true,
        area: ["340px", "300px"],
        offset: "auto",
        content: emojiPickerHTML,
        success: function (layero, index) {
          // 初始化表情选择器事件
          ChatCore.EmojiManager.initEmojiPicker(layero[0], function (emoji) {
            // 插入表情到输入框
            insertEmojiToInput(emoji);
            // 关闭表情选择器
            layer.close(index);
            emojiPickerIndex = null;
          });
        },
        end: function () {
          emojiPickerIndex = null;
        },
      });
    });
  }

  function insertEmojiToInput(emoji) {
    // 获取当前输入框的值
    const currentInput = state.input || "";
    // 插入表情到响应式状态
    state.input = currentInput + emoji;
  }

  function handleImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 使用ChatCore的媒体上传管理器
    ChatCore.MediaUploadManager.handleMediaSelect(
      file,
      "member", // 会员端API类型
      sendMessageWithContent, // 发送消息回调
      layerMsg // 显示消息回调
    );

    // 清空文件选择
    document.getElementById("hiddenImageInput").value = "";
  }

  function sendImageMessage(imageUrl) {
    if (!state.sessionGuid) return;

    // 构造图片消息内容
    const imageContent = `img[${imageUrl}]`;

    // 使用现有的发送消息逻辑
    sendMessageWithContent(imageContent);
  }

  function sendMessageWithContent(content) {
    if (!content.trim()) return;

    const local_id = ChatCore.createGuid();
    const msg = {
      content: content,
      from_guid: window.kefuConfig.customerId,
      from_name: window.kefuConfig.customerName,
      from_type: 1,
      from_avatar: window.kefuConfig.avatar,
      to_guid: state.kefuGuid,
      to_name: state.kefuName,
      bid: window.kefuConfig.seller,
      session_guid: state.sessionGuid,
      local_id,
    };

    // 本地回显
    state.messages.push({
      ...msg,
      status: "sending",
      guid: "",
      send_time: new Date().toLocaleString(),
      content: content, // 图片消息不需要XSS防护
    });

    // 发送内容消息后强制滚动到底部（丝滑效果）
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);

    // 发送消息并处理重试
    ChatCore.resendMessage(msg).catch((error) => {
      console.error("消息发送失败", error);
      const idx = state.messages.findIndex((m) => m.local_id === local_id);
      if (idx !== -1) {
        state.messages[idx].status = "fail";
      }
    });
  }

  function previewImage(imageUrl) {
    state.previewImageUrl = imageUrl;
  }

  function closeImagePreview() {
    state.previewImageUrl = "";
  }

  // 切换声音开关
  function toggleSound() {
    const newState = ChatCore.NotificationManager.toggleSound();
    state.voiceOpen = newState;
    ChatCore.NotificationManager.showSoundStatusMessage(newState);
  }

  // 更新WebSocket状态
  function updateWebSocketStatus(status) {
    const config = ChatCore.WebSocketStatusManager.getStatusConfig(status);
    state.wsStatus.className = config.className;
    state.wsStatus.text = config.text;
  }

  // 显示连接信息
  function showConnectionInfo() {
    const info = ChatCore.WebSocketStatusManager.getConnectionInfo();
    let message = `连接状态: ${info.statusText}`;

    if (info.connectTime) {
      message += `\n<br/>连接时间: ${info.connectTime}`;
    }
    if (info.connectDuration) {
      message += `\n<br/>连接时长: ${info.connectDuration}`;
    }

    if (window.layui) {
      layui.use("layer", function () {
        layui.layer.alert(message, {
          title: "WebSocket连接信息",
          icon: info.status === "connected" ? 1 : 2,
        });
      });
    }
  }

  // 注意：滚动处理已统一使用 ChatCore.ScrollManager

  function init() {
    console.log("init called");
    ajax_member_api_v1(
      "/kefu/config",
      {},
      function (res) {
        console.log("客服配置拉取成功", res);

        // 检查客服功能是否启用
        if (!res.data || !res.data.enabled) {
          console.log("客服功能未启用，停止初始化");
          return;
        }

        console.log("客服功能已启用，继续初始化");
        // 兼容 socketUrl
        window.kefuConfig = res.data;
        // 赋值 style，防止 undefined
        state.style = res.data.style || { box_color: "#009688" };

        // 初始化声音开关状态
        state.voiceOpen = ChatCore.NotificationManager.initSoundSettings();

        // 机器人会话已集成到chatMember.js中，无需额外初始化
        console.log("[会员端] 机器人会话功能已就绪");

        // 其它初始化逻辑...
        // 初始化WebSocket状态管理
        ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTING);
        updateWebSocketStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTING);

        // 注册状态变化监听
        ChatCore.WebSocketStatusManager.onStatusChange((newStatus, oldStatus) => {
          updateWebSocketStatus(newStatus);
          console.log(`[访客端] WebSocket状态变化: ${oldStatus} -> ${newStatus}`);
        });

        ChatCore.connectWS(
          function onOpen() {
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTED);
            // 自动发送 userInit
            ChatCore.sendMsg("userInit", {
              member_guid: window.kefuConfig.customerId,
              bid: window.kefuConfig.seller,
            });
          },
          function onClose() {
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.DISCONNECTED);
          },
          function onError() {
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.DISCONNECTED);
          }
        );
        registerEvents();
        // 页面激活时自动发送已读回执
        if (typeof document !== "undefined") {
          const hiddenProperty = "hidden" in document ? "hidden" : "webkitHidden" in document ? "webkitHidden" : "mozHidden" in document ? "mozHidden" : null;
          if (hiddenProperty) {
            const visibilityChangeEvent = hiddenProperty.replace(/hidden/i, "visibilitychange");
            document.addEventListener(visibilityChangeEvent, function () {
              if (!document[hiddenProperty]) {
                sendReadMessage();
              }
            });
          }
        }
        console.log("准备挂载 Vue 应用");
        // 挂载 Vue 应用
        window.vueApp = Vue.createApp({
          setup() {
            console.log("setup 执行");

            function renderMessage(msg) {
              const isSelf = msg.from_guid === window.kefuConfig.customerId || msg.from_type == 1;
              const avatar = isSelf
                ? msg.from_avatar || window.kefuConfig.avatar // 会员头像从config获取
                : msg.from_avatar || state.kefuAvatar; // 客服头像从state获取
              const name = isSelf
                ? msg.from_name || window.kefuConfig.customerName // 会员名称从config获取
                : msg.from_name || state.kefuName; // 客服名称从state获取

              return {
                ...msg,
                avatar,
                name,
                isSelf,
              };
            }

            function onInputBlur() {}

            function onInputChange() {}

            function recallMsgText(msg) {
              const selfId = window.kefuConfig && window.kefuConfig.customerId;
              return msg.from_guid === selfId || msg.from_type == 1 ? "你撤回了一条消息" : "对方撤回了一条消息";
            }

            // 处理快捷回复点击 - 发送WebSocket消息到服务端
            function handleQuickReply(replyId, questionText) {
              console.log("[机器人] 处理快捷回复:", replyId, questionText);

              // 添加用户选择的问题到聊天记录
              addUserMessage(questionText);

              // 通过WebSocket发送快捷回复选择到服务端
              const message = {
                cmd: "robotQuickReply",
                data: {
                  reply_id: replyId,
                  question_text: questionText,
                  session_guid: state.sessionGuid,
                  member_guid: window.kefuConfig.customerId,
                  bid: window.kefuConfig.seller,
                  timestamp: Date.now(),
                },
              };

              // 调试WebSocket状态
              console.log("[机器人] WebSocket状态检查:", {
                ws: !!ChatCore.ws,
                readyState: ChatCore.ws ? ChatCore.ws.readyState : "undefined",
                OPEN: WebSocket.OPEN,
              });

              if (ChatCore.ws && ChatCore.ws.readyState === WebSocket.OPEN) {
                ChatCore.ws.send(JSON.stringify(message));
                console.log("[机器人] 发送快捷回复选择到服务端:", message);
              } else {
                console.error("[机器人] WebSocket连接不可用，无法发送快捷回复");
                // 显示错误提示
                setTimeout(() => {
                  const errorMsg = {
                    guid: "error_" + Date.now(),
                    content: "网络连接异常，请稍后重试",
                    msg_type: 99, // 系统消息
                    from_type: 2,
                    from_name: "系统",
                    send_time: new Date().toLocaleString(),
                    is_system: true,
                  };
                  state.messages.push(errorMsg);
                }, 500);
              }
            }

            // 将handleQuickReply暴露到全局作用域，供onclick使用
            window.handleQuickReply = handleQuickReply;

            return {
              ...Vue.toRefs(state),
              sendMessage,
              renderMessage,
              onInputBlur,
              onInputChange,
              recallMessage, // 新增，供模板调用
              recallMsgText,
              toggleSound, // 声音开关
              showConnectionInfo, // WebSocket连接信息
              // 图片上传相关方法
              openImageUpload,
              handleImageSelect,
              previewImage,
              closeImagePreview,
              // 表情相关方法
              openEmojiPicker,
              // 媒体消息处理方法（使用ChatCore）
              isImageMessage: ChatCore.MediaUploadManager.isImageMessage,
              getImageUrl: ChatCore.MediaUploadManager.getImageUrl,
              // 机器人快捷回复方法
              handleQuickReply,
              isVideoMessage: ChatCore.MediaUploadManager.isVideoMessage,
              getVideoUrl: ChatCore.MediaUploadManager.getVideoUrl,
              getVideoThumbnail: ChatCore.MediaUploadManager.getVideoThumbnail,
              getVideoDuration: ChatCore.MediaUploadManager.getVideoDuration,
              formatVideoDuration: ChatCore.MediaUploadManager.formatVideoDuration,
              playVideo: ChatCore.MediaUploadManager.playVideo,
            };
          },
        }).mount("#app_chat");
        console.log("Vue 应用已挂载");

        // 初始化成功后显示悬浮按钮
        showFloatingButton();
      },
      function (res) {
        console.error("获取客服配置失败:", res.msg || "未知错误");
        layerMsg("系统初始化失败，请刷新页面重试");
      }
    );
  }

  // 机器人基础配置（仅保留UI相关配置）
  const robotConfig = {
    name: "智能助手",
    avatar: "/static/customer/common/images/robot.svg",
  };

  // 处理服务端下发的机器人消息
  function handleRobotMessage(data) {
    console.log("[机器人] 处理服务端机器人消息:", data);

    const robotMsg = {
      guid: data.guid || "robot_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9),
      content: data.content,
      msg_type: data.msg_type || 1,
      from_type: 2, // 客服端（显示在左边）
      from_guid: "robot_assistant",
      from_name: data.from_name || robotConfig.name,
      from_avatar: data.from_avatar || robotConfig.avatar,
      send_time: data.send_time || new Date().toLocaleString(),
      is_robot: true,
      is_read: 1,
      // 如果是快捷回复消息，保存选项数据
      quick_replies: data.quick_replies || null,
    };

    state.messages.push(robotMsg);
    console.log("[机器人] 添加服务端机器人消息:", robotMsg);

    // 滚动到底部
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);
  }

  // 处理机器人回复
  function handleRobotReply(data) {
    console.log("[机器人] 处理机器人回复:", data);

    // 添加机器人回复消息
    const replyMsg = {
      guid: data.guid || "robot_reply_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9),
      content: data.content,
      msg_type: data.msg_type || 1,
      from_type: 2, // 客服端（显示在左边）
      from_guid: "robot_assistant",
      from_name: data.from_name || robotConfig.name,
      from_avatar: data.from_avatar || robotConfig.avatar,
      send_time: data.send_time || new Date().toLocaleString(),
      is_robot: true,
      is_read: 1,
    };

    state.messages.push(replyMsg);
    console.log("[机器人] 添加机器人回复消息:", replyMsg);

    // 滚动到底部
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);
  }

  // 生成快捷回复HTML（基于服务端数据）
  function generateQuickReplyHTML(quickReplies, title = "💡 猜您想问：") {
    if (!quickReplies || !Array.isArray(quickReplies)) {
      return "";
    }

    const buttons = quickReplies.map((item) => `<div class="quick-reply-btn" data-reply-id="${item.id}" onclick="handleQuickReply('${item.id}', '${item.text}')">${item.text}</div>`).join("");

    return `
      <div class="robot-quick-replies">
        <div class="quick-reply-title">${title}</div>
        <div class="quick-reply-buttons">
          ${buttons}
        </div>
      </div>
    `;
  }

  // 显示悬浮按钮（初始化成功后调用）
  function showFloatingButton() {
    const floatBtn = document.getElementById("kefu-float-btn");
    if (floatBtn) {
      // 添加淡入动画效果
      floatBtn.style.opacity = "0";
      floatBtn.style.display = "flex";
      floatBtn.style.transition = "opacity 0.5s ease-in-out";

      // 延迟显示，创建淡入效果
      setTimeout(() => {
        floatBtn.style.opacity = "1";
      }, 100);

      console.log("悬浮客服按钮已显示");
    } else {
      console.warn("未找到悬浮客服按钮元素");
    }
  }

  // 添加用户消息（供机器人使用）
  function addUserMessage(content) {
    const userMsg = {
      guid: "user_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9),
      content: content,
      msg_type: 1, // 文本消息
      from_type: 1, // 用户
      from_name: "我",
      from_avatar: "",
      send_time: new Date().toLocaleString(),
      is_read: 1,
    };

    state.messages.push(userMsg);
    console.log("[机器人] 添加用户消息:", userMsg);

    // 滚动到底部
    ChatCore.ScrollManager.scrollToBottomNextTick(".chat-box", true);
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
