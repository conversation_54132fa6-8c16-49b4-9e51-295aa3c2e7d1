(function (global) {
    "use strict";

    var config = {
        // serverBeacon: 'http://www.yikayi.net/monitor.webp',
        serverBeacon: '//www.yikayi.net/index/plugins/web_monitor',
        isTrackAjax: true
    };

    var Metric = function (performance, config) {
        var timing,
            timeStamp,
            jsdata = {},
            cookie = {},
            requestCount = 0,
            metrics = {},
            ajaxMetrics = [];
        this.config = config;
        this.ajaxMetrics = ajaxMetrics;

        if (performance && performance.timing) {
            this.performance = performance;
            timing = performance.timing;
        } else {
            jsdata['type'] = 3;
            jsdata['errorMessage'] = "performance API is not supported";
            this._sendData(jsdata);
            return global.console.error('performance API is not supported');
        }

        timeStamp = this._setupTimes();

        // cookie = this._setupCookies();
        cookie = {};

        metrics = this._setupMetrics(metrics, timing, cookie, timeStamp);

        jsdata = this._setupJserr(timeStamp);

        if (this.config.isTrackAjax) {
            this._attachAjaxMetrics(requestCount, cookie, timeStamp);
        }

        return {
            ajaxMetrics: ajaxMetrics,
            metrics: metrics,
            jsdata: jsdata
        };
    };


    Metric.prototype = {
        serializeData: function (obj, prefix) {
            var str = [];
            for (var p in obj) {
                if (obj.hasOwnProperty(p)) {
                    var k = prefix ? prefix + "[" + p + "]" : p, v = obj[p];
                    str.push(typeof v === "object" ?
                        this.serializeData(v, k) :
                        encodeURIComponent(k) + "=" + encodeURIComponent(v));
                }
            }
            return str.join("&");
        },

        _setupTimes: function () {
            var timeStamp, timeStr, reg
            timeStamp = new Date(parseInt(Date.parse(new Date())));
            var year = timeStamp.getFullYear();
            var month = timeStamp.getMonth() + 1;
            var date = timeStamp.getDate();
            if (month <= 9) {
                month = "0" + month;
            }
            if (date <= 9) {
                date = "0" + date;
            }
            timeStamp = year + '-' + month + '-' + date + ' ' + timeStamp.toTimeString().split(' ')[0].substring(0, 8);
            return timeStamp
        },

        _setupCookies: function () {
            var ck, car, c, d, e, f, g, h = {}, k, v;
            ck = document.cookie;
            car = ck.split(";");
            for (c in car) {
                d = car[c];
                if (typeof(d) == 'string') {
                    d = d.replace(/(^\s*)|(\s*$)/g, "");
                    if (d.indexOf("UserInfo")) {
                        d = d.replace("UserInfo=", "");
                        e = d.split("&");
                        for (f in e) {
                            g = e[f];
                            if (typeof(g) == 'string') {
                                if (g.indexOf("UserAccount") || g.indexOf("BusinessAccount") || g.indexOf("BID")) {
                                    g = g.split("=");
                                    k = g[0];
                                    v = g[1];
                                    h[k] = v;
                                }
                            }
                        }
                    }
                }
            }
            h['cookie'] = ck
            return h;
        },

        _setupMetrics: function (metrics, timing, cookie, timeStamp) {
            var firstPaint = 0,
                start,
                _this = this;

            start = timing.connectStart;

            // DNS解析时间
            metrics.timeToDns = timing.domainLookupEnd - timing.domainLookupStart;
            // TCP连接时间
            metrics.timeToConnect = timing.connectEnd - start;
            // 首字节时间
            metrics.timeToFirstByte = timing.responseStart - start;
            // request时间
            metrics.timeToRequest = timing.responseStart - timing.requestStart;
            // response时间
            metrics.timeToResponse = timing.responseEnd - timing.responseStart;
            // url
            metrics.url = location.pathname;
            metrics.args = location.search;
            metrics.protocol = location.protocol.substring(0, 4);
            // domain
            metrics.domain = location.host;
            metrics.state = (!!history.pushState).toString();

            var domMetrics = function () {
                // dom时间
                metrics.timeToDom = timing.domComplete - timing.responseEnd;
                // 首屏时间
                metrics.timeToFirstScreen = timing.loadEventEnd - timing.fetchStart;
                if (metrics.timeToFirstScreen < 0) {
                    metrics.timeToFirstScreen = 0;
                }
                // 白屏时间
                if (global.chrome && global.chrome.loadTimes) {
                    firstPaint = global.chrome.loadTimes().firstPaintTime * 1000;
                    metrics.timeToFirstPaint = Math.round(firstPaint - start);
                }
                else if (typeof timing.msFirstPaint === 'number') {
                    firstPaint = timing.msFirstPaint;
                    metrics.timeToFirstPaint = firstPaint - start;
                }
                else if (timing.navigationStart && global.mozAnimationStartTime) {
                    firstPaint = global.mozAnimationStartTime;
                    metrics.timeToFirstPaint = firstPaint - start;
                }

                // metrics = $.extend({}, metrics, cookie);
                if (metrics.timeToResponse < 0) {
                    return;
                }

                metrics.timeStamp = timeStamp;
                metrics.type = 1;
                _this._sendData(metrics);
            };

            if (global.addEventListener) {
                metrics.events = "addEventListener";
                global.addEventListener('load', domMetrics, false);
            } else {
                metrics.events = "attachEvent";
                global.attachEvent("onload", domMetrics);
            }

            return metrics;
        },

        _setupJserr: function (timeStamp) {
            var jsdata = {},
                _this = this;
            try {
                onerror = function (errorMessage, scriptURI, lineNumber, columnNumber, errorObj) {
                    if (errorObj !== undefined) {
                        console.log('Error: ' + errorObj.stack);
                    }
                    jsdata['type'] = 3;
                    jsdata['errorMessage'] = errorMessage;
                    jsdata['lineNumber'] = lineNumber;
                    jsdata['columnNumber'] = columnNumber;
                    jsdata['scriptURI'] = scriptURI;
                    jsdata['timeStamp'] = timeStamp;
                    jsdata['domain'] = location.host;
                    jsdata['url'] = location.pathname;
                    _this._sendData(jsdata);
                }
            } catch (e) {
                null
            }
            return jsdata;
        },

        _attachAjaxMetrics: function (requestCount, cookie, timeStamp) {
            var openOriginal = XMLHttpRequest.prototype.send,
                timeStart,
                _this = this,
                perf = this.performance;

            XMLHttpRequest.prototype.send = function () {
                var data = {}, sendTiming,
                    timing = {};
                timeStart = perf.now();

                this.addEventListener('readystatechange', function () {
                    switch (this.readyState) {
                        case 2:
                            timing.timeToReceive = perf.now() - timeStart;
                            break;

                        case 3:
                            timing.timeToProcess = perf.now() - timeStart;
                            break;

                        case 4:
                            timing.timeToResponseReady = perf.now() - timeStart;

                            sendTiming = timing.timeToReceive + timing.timeToProcess + timing.timeToResponseReady;
                            if (isNaN(sendTiming)) {
                                sendTiming = 0;
                            }
                            data = {
                                type: 2,
                                requestCount: requestCount++,
                                timeStamp: timeStamp,
                                url: this.responseURL,
                                timeToReceive: parseInt(timing.timeToReceive),
                                timeToProcess: parseInt(timing.timeToProcess),
                                timeToResponseReady: parseInt(timing.timeToResponseReady),
                                httpCode: this.status
                            };

                            // data = $.extend({}, data, cookie)
                            _this._sendData(data);
                            _this._collect(data);
                    }
                });
                openOriginal.apply(this, arguments);
            };
        },


        _sendData: function (data) {
            //var i = new Image();
            //i.src = this.config.serverBeacon + '?' + this.serializeData(data);
            var formData = new FormData();
            for (var key in data) {
                formData.append(key, data[key]);
            }
            // navigator.sendBeacon(this.config.serverBeacon, formData);
            navigator.sendBeacon(this.config.serverBeacon, JSON.stringify(data));

        },

        _collect: function (data) {
            this.ajaxMetrics.push(data);
        }
    };

    global.metric = global.metric || new Metric(global.performance, config);
}(window));

