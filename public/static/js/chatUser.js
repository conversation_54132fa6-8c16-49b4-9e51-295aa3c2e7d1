// public/static/js/chatUser.js
(function () {
  const state = Vue.reactive({
    user: {},
    sessionList: [],
    queueList: [],
    activeSessionGuid: "",
    activeSession: null,
    messageList: [],
    input: "",
    draftMessages: {}, // 保存每个会话的草稿
    loadingState: {
      messageLoading: false,
      sessionLoading: false,
      sending: false,
    },
    soundEnabled: true, // 声音开关状态
    wsStatus: {
      // WebSocket状态
      className: "disconnected",
      text: "连接断开",
    },
    transferDialog: {
      visible: false,
      kefuList: [],
      selectedKefu: null,
      transferReason: "",
      loading: false,
    },
    visitorInfoCache: {}, // 访客信息缓存
    // 图片上传相关状态
    previewImageUrl: "", // 图片预览URL
    commonTab: "sys", // 常用语标签页
    commonWords: [
      { id: 1, text: "您好，有什么可以帮助您的吗？" },
      { id: 2, text: "请稍等，我为您查询一下" },
      { id: 3, text: "感谢您的咨询，祝您生活愉快！" },
    ],
    myCommonWords: [
      { id: 1, text: "我的常用语1" },
      { id: 2, text: "我的常用语2" },
    ],
    // 输入预览状态
    typingInfo: {
      isTyping: false,
      memberName: "",
      memberAvatar: "",
      content: "",
      sessionGuid: "",
      memberGuid: "",
      timestamp: 0,
    },
    // 手机端状态
    mobileSessionOpen: false, // 手机端会话列表是否打开
    mobileVisitorInfoOpen: false, // 手机端访客信息是否打开
    mobileCurrentTab: "chat", // 手机端当前标签页
    // ...其它客服端状态
  });

  // 选择会话
  function selectSession(session) {
    if (!session || !session.session_guid) return;

    // 保存当前会话草稿
    if (state.activeSession) {
      state.draftMessages[state.activeSession.session_guid] = state.input;
    }

    // 切换会话前清空消息列表
    state.messageList = [];
    state.loadingState.messageLoading = true;

    // 清除输入预览状态
    state.typingInfo.isTyping = false;

    // 切换会话
    state.activeSession = session;
    state.activeSessionGuid = session.session_guid;

    // 恢复草稿
    state.input = state.draftMessages[session.session_guid] || "";

    // 拉取消息
    fetchMessageList(session.session_guid);

    // 异步加载访客信息
    loadVisitorInfo(session.member_guid);

    // 选中会话后自动上报已读
    ChatCore.ReadManager.sendReadReceipt(
      session.session_guid,
      state.user.guid, // 客服的GUID（读消息的人）
      session.member_guid // 会员的GUID（消息发送方）
    );

    // 延迟多重滚动，确保切换会话后滚动到真正的底部
    setTimeout(() => {
      ChatCore.ScrollManager.scrollToBottomAfterLoad(".kefu-chat-messages", true);
    }, 200);
  }

  // 接受待接入会话
  function acceptSession(session) {
    if (!session || !session.session_guid) return;
    ChatCore.sendMsg("acceptSession", {
      session_guid: session.session_guid,
      kefu_guid: state.user.guid,
      bid: state.user.bid,
    });
  }

  // 拒绝待接入会话
  function rejectSession(session) {
    if (!session || !session.session_guid) return;

    // 确认对话框
    layui.use("layer", function () {
      const layer = layui.layer;
      layer.confirm(
        `确定要拒绝来自 ${session.name || session.id || "访客"} 的会话请求吗？`,
        {
          btn: ["确定", "取消"],
          title: "拒绝会话",
          icon: 3,
        },
        function (index) {
          ChatCore.sendMsg("rejectSession", {
            session_guid: session.session_guid,
            kefu_guid: state.user.guid,
            bid: state.user.bid,
          });
          layer.close(index);
        }
      );
    });
  }

  // 消息内容安全处理
  function sanitizeMessage(message) {
    if (!message) return message;

    // 深拷贝消息对象，避免修改原对象
    const msg = JSON.parse(JSON.stringify(message));

    // 处理文本内容
    if (msg.content_type === "text") {
      // 转义HTML特殊字符
      msg.content = msg.content.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");

      // 将URL转换为可点击的链接
      msg.content = msg.content.replace(/(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    }

    // 处理图片消息
    if (msg.content_type === "image") {
      // 确保图片URL是安全的
      if (!/^(https?:\/\/|data:image\/)/.test(msg.content)) {
        msg.content = "";
      }
    }

    return msg;
  }

  // 拉取消息列表
  function fetchMessageList(session_guid) {
    if (!session_guid) return;

    state.loadingState.messageLoading = true;
    ajax_admin_api_v1(
      "/kefu/message_list",
      { session_guid, page: 1, limit: 20 },
      function (res) {
        state.loadingState.messageLoading = false;

        if (res.data && res.data.data) {
          let list = res.data.data;
          list = list.map((msg) => ({
            ...msg,
            from: msg.from_type === 2 ? "right" : "left",
            content: sanitizeMessage(msg.content), // XSS防护
          }));
          state.messageList = list;
          // 消息加载完成后多重滚动，确保滚动到真正的底部
          ChatCore.ScrollManager.scrollToBottomAfterLoad(".kefu-chat-messages", true);
        }
      },
      function (res) {
        state.loadingState.messageLoading = false;
        console.error("获取消息列表失败:", res.msg || "未知错误");
      }
    );
  }

  // 发送消息
  function sendMessage() {
    if (!state.input.trim() || !state.activeSession) return;

    // 防止重复发送
    if (state.loadingState.sending) {
      console.log("正在发送中，请勿重复点击");
      return;
    }

    state.loadingState.sending = true;
    const local_id = ChatCore.createGuid();
    const params = {
      session_guid: state.activeSession.session_guid,
      content: state.input,
      from_guid: state.user.guid || "",
      from_name: state.user.name || "",
      from_avatar: state.user.avatar || "", // 使用config中的头像
      to_guid: state.activeSession.to_guid || state.activeSession.member_guid || "",
      to_name: state.activeSession.to_name || state.activeSession.visitor_name || "",
      to_avatar: state.activeSession.to_avatar || state.activeSession.visitor_avatar || "",
      bid: state.activeSession.bid || "",
      from_type: 2,
      local_id,
    };

    // 本地回显
    const localMsg = {
      ...params,
      from: "right",
      send_time: new Date().toLocaleString(),
      is_read: 0,
      status: "sending",
      guid: "",
      content: sanitizeMessage(state.input), // XSS防护
    };
    state.messageList.push(localMsg);
    // 发送消息后强制滚动到底部（丝滑效果）
    ChatCore.ScrollManager.scrollToBottomNextTick(".kefu-chat-messages", true);

    // 发送消息并处理重试
    ChatCore.resendMessage(params)
      .then(() => {
        state.loadingState.sending = false;
      })
      .catch((error) => {
        console.error("消息发送失败", error);
        const idx = state.messageList.findIndex((m) => m.local_id === local_id);
        if (idx !== -1) {
          state.messageList[idx].status = "fail";
        }
        state.loadingState.sending = false;
      });

    state.input = "";
    // 清除草稿
    if (state.activeSession) {
      delete state.draftMessages[state.activeSession.session_guid];
    }
  }

  // 注意：滚动处理已统一使用 ChatCore.ScrollManager

  // 注册事件
  function registerEvents() {
    // 会话列表更新
    ChatCore.on("sessionListUpdate", (msg) => {
      if (!msg.data) return;

      // 保存当前活动会话
      const currentSession = state.activeSession;

      // 更新会话列表
      if (Array.isArray(msg.data.session_list)) {
        state.sessionList = msg.data.session_list.map((item) => ({
          ...item,
          session_guid: item.guid,
          to_guid: item.member_guid,
          from_guid: item.user_guid,
        }));
      }

      // 更新队列列表
      if (Array.isArray(msg.data.queue_list)) {
        state.queueList = msg.data.queue_list.map((item) => ({
          ...item,
          session_guid: item.guid,
          to_guid: item.member_guid,
          from_guid: item.user_guid,
        }));
      }

      // 如果当前会话还在列表中，更新它
      if (currentSession) {
        const updatedSession = state.sessionList.find((s) => s.session_guid === currentSession.session_guid);
        if (updatedSession) {
          state.activeSession = updatedSession;
        }
      }
    });

    // 新会话通知
    ChatCore.on("newSession", (msg) => {
      if (!msg.data) return;

      // 显示新会话提醒
      ChatCore.NotificationManager.showNewSessionNotification(msg.data.session || {});

      // 刷新会话列表
      fetchChatList().catch(console.error);

      layerMsg("有新的会话请求，请及时处理");
    });

    // 会话状态变更
    ChatCore.on("sessionStateChange", (msg) => {
      if (!msg.data) return;
      fetchChatList().catch(console.error); // 会话状态变更时刷新一次列表
    });

    // 消息接收
    ChatCore.on("chatMessage", (msg) => {
      if (!msg.data) return;

      // 显示新消息通知（仅在页面不可见时且是会员发送的消息）
      if (msg.data.from_type === 1) {
        // 会员发送的消息
        ChatCore.NotificationManager.showNewMessageNotification(msg.data);
      }

      if (msg.data && msg.data.session_guid === state.activeSessionGuid) {
        // 消息去重
        if (!state.messageList.some((m) => m.guid === msg.data.guid && msg.data.guid)) {
          state.messageList.push({
            ...msg.data,
            from: msg.data.from_type === 2 ? "right" : "left",
          });
          // 接收到新消息时强制滚动到底部（确保客服能看到新消息）
          ChatCore.ScrollManager.scrollToBottomNextTick(".kefu-chat-messages", true);

          // 如果是会员发送的消息，自动发送已读回执
          if (msg.data.from_type === 1 && !document.hidden) {
            sendReadMessage(msg.data.session_guid, msg.data.from_guid);
          }
        }
      }
    });
    // afterSend 补全 guid
    ChatCore.on("afterSend", (msg) => {
      if (msg.data && msg.data.local_id) {
        const idx = state.messageList.findIndex((m) => m.local_id === msg.data.local_id);
        if (idx !== -1) {
          state.messageList[idx] = {
            ...state.messageList[idx],
            ...(msg.data.data || {}),
            status: "success",
          };
        }
      }
    });
    // 撤回消息
    ChatCore.on("rollBackMessage", (msg) => {
      if (msg.data && msg.data.guid) {
        const m = state.messageList.find((m) => m.guid === msg.data.guid);
        if (m) m.is_recall = 1;
      }
    });
    // 输入预览状态
    ChatCore.on("typingStatus", (msg) => {
      if (!msg.data) return;

      const data = msg.data;
      console.log("[输入预览] 收到输入状态:", data);

      // 只处理当前活动会话的输入预览
      if (data.session_guid !== state.activeSessionGuid) {
        console.log("[输入预览] 非当前会话，忽略");
        return;
      }

      if (data.status === "start" || data.status === "typing") {
        // 开始或继续输入
        state.typingInfo = {
          isTyping: true,
          memberName: data.member_name || "访客",
          memberAvatar: state.activeSession?.member_avatar || "/static/customer/common/images/customer.png",
          content: data.content || "",
          sessionGuid: data.session_guid,
          memberGuid: data.member_guid,
          timestamp: data.timestamp || Date.now(),
        };

        console.log("[输入预览] 显示输入状态:", state.typingInfo);
      } else if (data.status === "stop") {
        // 停止输入
        state.typingInfo.isTyping = false;
        console.log("[输入预览] 隐藏输入状态");
      }
    });

    // 已读回执
    ChatCore.on("readMessage", (msg) => {
      ChatCore.ReadManager.handleReadReceipt(msg, state.messageList, state.activeSessionGuid, "客服端");
    });
    // 会话状态更新
    ChatCore.on("sessionUpdate", (msg) => {
      if (msg.data) {
        if (msg.data.status === 1 && msg.data.user_guid === state.user.guid) {
          // 会话被当前客服接入，刷新列表
          fetchChatList().catch(console.error);
        } else if (msg.data.session_guid === state.activeSessionGuid && msg.data.status === 2) {
          // 当前活跃会话已结束
          layerMsg("会话已结束");
          fetchChatList().catch(console.error);
          state.activeSession = null;
          state.activeSessionGuid = "";
          state.messageList = [];
        } else {
          // 其他会话状态变更，刷新列表
          fetchChatList().catch(console.error);
        }
      }
    });

    // 关闭会话回执
    ChatCore.on("closeSession", (msg) => {
      if (msg.data && msg.data.code === 0) {
        layerMsg("会话已成功结束");
      } else {
        layerMsg("结束会话失败：" + (msg.data?.msg || "未知错误"));
      }
    });

    // 接入会话回执
    ChatCore.on("acceptSession", (msg) => {
      if (msg.data && msg.data.code === 0) {
        layerMsg("接入成功");

        // 停止新会话提醒
        ChatCore.NotificationManager.stopTitleBlink();

        // 刷新会话列表
        fetchChatList().catch(console.error);

        // 自动选中刚接入的会话并打开聊天窗口
        if (msg.data.session) {
          setTimeout(() => {
            const session = state.sessionList.find((s) => s.session_guid === msg.data.session.guid);
            if (session) {
              selectSession(session);
              // 确保聊天区域可见（丝滑滚动）
              ChatCore.ScrollManager.scrollToBottomNextTick(".kefu-chat-messages", true);
            }
          }, 500); // 等待列表刷新完成
        }
      } else {
        layerMsg("接入失败：" + (msg.data?.msg || "未知错误"));
      }
    });

    // 拒绝会话回执
    ChatCore.on("rejectSession", (msg) => {
      if (msg.data && msg.data.code === 0) {
        layerMsg("已拒绝会话");

        // 停止新会话提醒
        ChatCore.NotificationManager.stopTitleBlink();

        // 刷新会话列表
        fetchChatList().catch(console.error);
      } else {
        layerMsg("拒绝失败：" + (msg.data?.msg || "未知错误"));
      }
    });

    // 转接响应回执
    ChatCore.on("respondTransferReply", (msg) => {
      if (msg.data && msg.data.code === 0) {
        // 接受转接成功，刷新会话列表
        fetchChatList().catch(console.error);
      }
    });

    // 转接接受成功事件
    ChatCore.on("transferAccepted", (data) => {
      if (data && data.session_guid) {
        // 刷新会话列表，然后自动选择该会话
        fetchChatList().then(() => {
          // 查找刚接受的会话
          const acceptedSession = state.sessionList.find((session) => session.session_guid === data.session_guid);

          if (acceptedSession) {
            // 自动选择该会话并打开聊天窗口
            selectSession(acceptedSession);

            // 提示用户
            layerMsg("已自动切换到转接的会话");
          }
        });
      }
    });

    // 会话转接完成事件（接收方）
    ChatCore.on("sessionTransferred", (msg) => {
      if (msg.data) {
        const sessionData = msg.data;

        // 刷新会话列表以获取最新状态
        fetchChatList().then(() => {
          // 查找转接的会话
          const transferredSession = state.sessionList.find((session) => session.session_guid === sessionData.session_guid);

          if (transferredSession) {
            // 自动选择该会话并打开聊天窗口
            selectSession(transferredSession);

            // 提示用户
            layerMsg(`已接收来自 ${sessionData.from_user_name} 的转接会话`);
          }
        });
      }
    });

    // 页面可见性变化时发送已读回执
    ChatCore.ReadManager.setupVisibilityListener(() => {
      if (state.activeSession && state.activeSession.member_guid) {
        sendReadMessage(state.activeSessionGuid, state.activeSession.member_guid);
      }
    });

    // 转接完成通知
    ChatCore.on("transferComplete", (msg) => {
      if (msg.data) {
        const action = msg.data.action;
        if (action === "accepted") {
          // 转接被接受，从当前会话列表中移除该会话
          const sessionGuid = msg.data.session_guid;
          state.sessionList = state.sessionList.filter((s) => s.session_guid !== sessionGuid);

          // 如果是当前活动会话，清空
          if (state.activeSession && state.activeSession.session_guid === sessionGuid) {
            state.activeSession = null;
            state.activeSessionGuid = "";
            state.messageList = [];
          }

          layerMsg("转接已被接受");
        } else if (action === "rejected") {
          layerMsg("转接被拒绝：" + (msg.data.reject_reason || "无原因"));
        }

        // 刷新会话列表
        fetchChatList().catch(console.error);
      }
    });

    // 会话更新通知（包括转接）
    ChatCore.on("sessionUpdate", (msg) => {
      if (msg.data) {
        const sessionGuid = msg.data.session_guid;

        // 如果是转接通知
        if (msg.data.transfer_notice) {
          // 刷新会话列表以获取最新状态
          fetchChatList().catch(console.error);

          // 如果是当前活动会话，显示转接提示
          if (state.activeSession && state.activeSession.session_guid === sessionGuid) {
            layerMsg("会话已被转接，客服已变更");
          }
        }
      }
    });

    // ...其它事件
  }

  // 发送已读回执
  function sendReadMessage(sessionGuid, memberGuid) {
    if (state.user && state.user.guid && sessionGuid && memberGuid) {
      ChatCore.ReadManager.sendReadReceipt(
        sessionGuid,
        state.user.guid, // 客服的GUID（读消息的人）
        memberGuid // 会员的GUID（消息发送方）
      );
    } else {
      console.log("客服端无法发送已读回执，缺少必要参数", {
        hasUser: !!state.user,
        userGuid: state.user?.guid,
        sessionGuid: sessionGuid,
        memberGuid: memberGuid,
      });
    }
  }

  // 拉取会话列表
  function fetchChatList() {
    return new Promise((resolve, reject) => {
      state.loadingState.sessionLoading = true;
      ajax_admin_api_v1(
        "/kefu/chat_list",
        {},
        function (res) {
          state.loadingState.sessionLoading = false;

          try {
            // 保存当前活动会话
            const currentSession = state.activeSession;

            state.sessionList = (res.data.session_list || []).map((item) => ({
              ...item,
              session_guid: item.guid,
              to_guid: item.member_guid,
              from_guid: item.user_guid,
            }));

            state.queueList = (res.data.queue_list || []).map((item) => ({
              ...item,
              session_guid: item.guid,
              to_guid: item.member_guid,
              from_guid: item.user_guid,
            }));

            // 如果当前会话还在列表中，更新它
            if (currentSession) {
              const updatedSession = state.sessionList.find((s) => s.session_guid === currentSession.session_guid);
              if (updatedSession) {
                state.activeSession = updatedSession;
              }
            }

            resolve(res);
          } catch (error) {
            reject(error);
          }
        },
        function (error) {
          state.loadingState.sessionLoading = false;
          reject(error);
        }
      );
    });
  }

  // 撤回消息
  function recallMessage(msg) {
    if (!msg || !msg.guid) return;
    ChatCore.recallMessage({
      guid: msg.guid,
      session_guid: msg.session_guid || (state.activeSession && state.activeSession.session_guid),
      from_guid: state.user.guid,
      bid: state.user.bid,
    });
  }

  // 重发消息
  function resendMessage(msg) {
    if (!msg || !msg.local_id) return;
    msg.status = "sending";
    ChatCore.sendMsg("chatMessage", { ...msg });
    setTimeout(() => {
      const idx = state.messageList.findIndex((m) => m.local_id === msg.local_id && m.status === "sending");
      if (idx !== -1) state.messageList[idx].status = "fail";
    }, 8000);
  }

  // 快捷短语插入
  function quickSend(word) {
    if (!word || !word.text) return;
    // 插入到输入框光标处（简单实现：直接替换输入框内容）
    state.input = word.text;
    // 可选：自动聚焦输入框
    Vue.nextTick(() => {
      const textarea = document.querySelector(".kefu-chat-input textarea");
      if (textarea) textarea.focus();
    });
  }

  // 图片上传相关方法
  function openImageUpload() {
    if (!state.activeSession) {
      layerMsg("请先选择会话");
      return;
    }
    document.getElementById("hiddenImageInput").click();
  }

  // 表情选择器相关
  let emojiPickerIndex = null;

  function openEmojiPicker() {
    if (!state.activeSession) {
      layerMsg("请先选择会话");
      return;
    }

    // 如果表情选择器已经打开，则关闭
    if (emojiPickerIndex) {
      layer.close(emojiPickerIndex);
      emojiPickerIndex = null;
      return;
    }

    // 创建表情选择器内容
    const emojiPickerHTML = ChatCore.EmojiManager.createEmojiPicker();

    // 添加CSS样式
    if (!document.getElementById("emoji-picker-styles")) {
      const style = document.createElement("style");
      style.id = "emoji-picker-styles";
      style.textContent = `
        .emoji-picker {
          width: 320px;
          height: 280px;
          background: white;
          border: 1px solid #e6e6e6;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          display: flex;
          flex-direction: column;
          font-family: Arial, sans-serif;
        }

        .emoji-categories {
          display: flex;
          border-bottom: 1px solid #e6e6e6;
          background: #f8f9fa;
          border-radius: 8px 8px 0 0;
          padding: 8px;
          gap: 4px;
        }

        .emoji-category-tab {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 6px;
          font-size: 18px;
          transition: background-color 0.2s;
        }

        .emoji-category-tab:hover {
          background: #e9ecef;
        }

        .emoji-category-tab.active {
          background: #007bff;
          color: white;
        }

        .emoji-content {
          flex: 1;
          overflow-y: auto;
          padding: 8px;
        }

        .emoji-category-content {
          height: 100%;
        }

        .emoji-grid {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          gap: 4px;
          height: 100%;
        }

        .emoji-item {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 4px;
          font-size: 20px;
          transition: background-color 0.2s;
          user-select: none;
        }

        .emoji-item:hover {
          background: #f0f0f0;
          transform: scale(1.1);
        }

        .emoji-content::-webkit-scrollbar {
          width: 6px;
        }

        .emoji-content::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .emoji-content::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        .emoji-content::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
      `;
      document.head.appendChild(style);
    }

    // 打开表情选择器弹窗
    emojiPickerIndex = layer.open({
      type: 1,
      title: false,
      closeBtn: 0,
      shade: 0.3,
      shadeClose: true,
      area: ["340px", "300px"],
      offset: "auto",
      content: emojiPickerHTML,
      success: function (layero, index) {
        // 初始化表情选择器事件
        ChatCore.EmojiManager.initEmojiPicker(layero[0], function (emoji) {
          // 插入表情到输入框
          insertEmojiToInput(emoji);
          // 关闭表情选择器
          layer.close(index);
          emojiPickerIndex = null;
        });
      },
      end: function () {
        emojiPickerIndex = null;
      },
    });
  }

  function insertEmojiToInput(emoji) {
    // 获取当前输入框的值
    const currentInput = state.input || "";
    // 插入表情
    state.input = currentInput + emoji;

    // 触发Vue的响应式更新
    if (window.vueApp && window.vueApp.input !== undefined) {
      window.vueApp.input = state.input;
    }
  }

  function handleImageSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 使用ChatCore的媒体上传管理器
    ChatCore.MediaUploadManager.handleMediaSelect(
      file,
      "admin", // 客服端API类型
      sendMessageWithContent, // 发送消息回调
      layerMsg // 显示消息回调
    );

    // 清空文件选择
    document.getElementById("hiddenImageInput").value = "";
  }

  function sendImageMessage(imageUrl) {
    if (!state.activeSession) return;

    // 构造图片消息内容
    const imageContent = `img[${imageUrl}]`;

    // 使用现有的发送消息逻辑
    sendMessageWithContent(imageContent);
  }

  function sendMessageWithContent(content) {
    if (!state.activeSession) return;

    const local_id = ChatCore.createGuid();
    const params = {
      session_guid: state.activeSession.session_guid,
      content: content,
      from_guid: state.user.guid || "",
      from_name: state.user.name || "",
      from_avatar: state.user.avatar || "",
      to_guid: state.activeSession.to_guid || state.activeSession.member_guid || "",
      to_name: state.activeSession.to_name || state.activeSession.visitor_name || "",
      to_avatar: state.activeSession.to_avatar || state.activeSession.visitor_avatar || "",
      bid: state.activeSession.bid || "",
      from_type: 2,
      local_id,
    };

    // 本地回显
    const localMsg = {
      ...params,
      from: "right",
      send_time: new Date().toLocaleString(),
      is_read: 0,
      status: "sending",
      guid: "",
      content: content,
    };
    state.messageList.push(localMsg);
    // 发送内容消息后强制滚动到底部（丝滑效果）
    ChatCore.ScrollManager.scrollToBottomNextTick(".kefu-chat-messages", true);

    // 发送消息
    ChatCore.resendMessage(params)
      .then(() => {
        console.log("[客服端] 消息发送成功");
      })
      .catch((error) => {
        console.error("消息发送失败", error);
        const idx = state.messageList.findIndex((m) => m.local_id === local_id);
        if (idx !== -1) {
          state.messageList[idx].status = "fail";
        }
      });
  }

  // 图片消息处理方法

  function previewImage(imageUrl) {
    state.previewImageUrl = imageUrl;
  }

  function closeImagePreview() {
    state.previewImageUrl = "";
  }

  function logout() {
    if (confirm("确定要退出吗？")) {
      window.location.href = "/admin/common/login";
    }
  }

  // 转接会话
  function transferSession() {
    if (!state.activeSession || !state.activeSession.session_guid) {
      layerMsg("没有活跃的会话");
      return;
    }

    // 获取可转接的客服列表
    state.transferDialog.loading = true;
    ajax_admin_api_v1(
      "/kefu/transfer_kefu_list",
      {},
      function (res) {
        state.transferDialog.loading = false;
        if (res.data) {
          state.transferDialog.kefuList = res.data;
          showTransferDialog();
        } else {
          layerMsg("获取客服列表数据为空");
        }
      },
      function (res) {
        state.transferDialog.loading = false;
        layerMsg("获取客服列表失败：" + (res.msg || "未知错误"));
      }
    );
  }

  // 显示转接对话框
  function showTransferDialog() {
    if (state.transferDialog.kefuList.length === 0) {
      layerMsg("没有可转接的客服");
      return;
    }

    // 使用 layui 弹窗
    layui.use(["layer", "form"], function () {
      const layer = layui.layer;
      const form = layui.form;

      // 构建客服选择HTML
      let kefuOptionsHtml = "";
      state.transferDialog.kefuList.forEach((kefu) => {
        const disabled = !kefu.can_transfer ? "disabled" : "";
        const statusText = kefu.can_transfer ? `(${kefu.session_count}个会话)` : `(已满${kefu.session_count}个会话)`;

        kefuOptionsHtml += `<option value="${kefu.guid}" ${disabled}>${kefu.nickname} ${statusText}</option>`;
      });

      // 弹窗内容
      const content = `
        <div class="layui-form" style="padding: 20px;">
          <div class="layui-form-item">
            <label class="layui-form-label">选择客服</label>
            <div class="layui-input-block">
              <select name="to_kefu" lay-verify="required" lay-filter="to_kefu">
                <option value="">请选择客服</option>
                ${kefuOptionsHtml}
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">转接原因</label>
            <div class="layui-input-block">
              <textarea name="transfer_reason" placeholder="请输入转接原因" class="layui-textarea"></textarea>
            </div>
          </div>
        </div>
      `;

      // 打开弹窗
      layer.open({
        type: 1,
        title: "转接会话",
        content: content,
        area: ["500px", "300px"],
        btn: ["确定转接", "取消"],
        success: function (layero, index) {
          // 重新渲染表单
          form.render();

          // 监听下拉框选择
          form.on("select(to_kefu)", function (data) {
            state.transferDialog.selectedKefu = data.value;
          });
        },
        yes: function (index, layero) {
          // 获取表单数据
          const toKefuGuid = state.transferDialog.selectedKefu;
          const transferReason = layero.find('textarea[name="transfer_reason"]').val();

          if (!toKefuGuid) {
            layerMsg("请选择要转接的客服");
            return;
          }

          // 发起转接请求
          ChatCore.TransferManager.transferSession(state.activeSession.session_guid, toKefuGuid, transferReason)
            .then(() => {
              layer.close(index);
            })
            .catch((err) => {
              layerMsg("转接请求发送失败：" + err.message);
            });
        },
      });
    });
  }

  // 结束会话（WebSocket方式）
  function closeSession() {
    if (!state.activeSession || !state.activeSession.session_guid) {
      layerMsg("没有活跃的会话");
      return;
    }

    // 使用 layui 确认对话框
    layui.use("layer", function () {
      const layer = layui.layer;
      layer.confirm(
        "确定要结束当前会话吗？",
        {
          btn: ["确定", "取消"],
          title: "操作确认",
        },
        function (index) {
          const closeData = {
            session_guid: state.activeSession.session_guid,
            kefu_guid: state.user.guid,
            bid: state.user.bid,
          };

          console.log("发送关闭会话请求", closeData);
          ChatCore.sendMsg("closeSession", closeData);
          layer.close(index);
        }
      );
    });
  }

  // 手机端交互方法
  function openMobileSession() {
    state.mobileSessionOpen = true;
    // 防止背景滚动
    document.body.style.overflow = "hidden";
  }

  function closeMobileSession() {
    state.mobileSessionOpen = false;
    // 恢复背景滚动
    document.body.style.overflow = "";
  }

  function openMobileVisitorInfo() {
    if (!state.activeSession) {
      layerMsg("请先选择会话");
      return;
    }
    state.mobileVisitorInfoOpen = true;
    state.mobileCurrentTab = "visitor";

    // 防止背景滚动
    document.body.style.overflow = "hidden";
  }

  function closeMobileVisitorInfo() {
    state.mobileVisitorInfoOpen = false;
    state.mobileCurrentTab = "chat";

    // 恢复背景滚动
    document.body.style.overflow = "";
  }

  function switchMobileTab(tab) {
    state.mobileCurrentTab = tab;

    switch (tab) {
      case "sessions":
        openMobileSession();
        break;
      case "chat":
        closeMobileSession();
        closeMobileVisitorInfo();
        break;
      case "visitor":
        if (state.activeSession) {
          openMobileVisitorInfo();
        } else {
          layerMsg("请先选择会话");
          state.mobileCurrentTab = "chat";
        }
        break;
      case "tools":
        // 可以扩展工具功能
        layerMsg("工具功能开发中");
        break;
    }
  }

  // 手机端会话选择（自动关闭会话列表）
  function selectSessionMobile(session) {
    selectSession(session);
    closeMobileSession();
    state.mobileCurrentTab = "chat";
  }

  // 手机端输入框优化
  function initMobileInputOptimization() {
    if (window.innerWidth > 600) return; // 只在手机端启用

    // 监听输入框焦点，调整视口
    const textarea = document.querySelector(".kefu-chat-input textarea");
    if (textarea) {
      textarea.addEventListener("focus", function () {
        // 延迟滚动到输入框，避免键盘弹出时的布局问题
        setTimeout(() => {
          this.scrollIntoView({ behavior: "smooth", block: "center" });
        }, 300);
      });

      // 监听输入，自动调整高度
      textarea.addEventListener("input", function () {
        this.style.height = "auto";
        this.style.height = Math.min(this.scrollHeight, 120) + "px";
      });
    }

    // 优化发送按钮的触摸反馈
    const sendBtn = document.querySelector(".kefu-chat-input .send-btn");
    if (sendBtn) {
      sendBtn.addEventListener("touchstart", function () {
        this.style.transform = "scale(0.95)";
      });

      sendBtn.addEventListener("touchend", function () {
        this.style.transform = "scale(1)";
      });
    }
  }

  // 响应式布局处理
  function handleResponsiveLayout() {
    const width = window.innerWidth;

    // 如果从手机端切换到桌面端，关闭手机端特有的界面
    if (width > 600) {
      if (state.mobileSessionOpen) {
        closeMobileSession();
      }
      if (state.mobileVisitorInfoOpen) {
        closeMobileVisitorInfo();
      }
      state.mobileCurrentTab = "chat";

      // 恢复背景滚动
      document.body.style.overflow = "";
    }
  }

  // 监听窗口大小变化
  function initResponsiveListener() {
    let resizeTimer = null;

    window.addEventListener("resize", function () {
      // 防抖处理
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }

      resizeTimer = setTimeout(() => {
        handleResponsiveLayout();

        // 重新初始化手机端功能
        if (window.innerWidth <= 600) {
          initMobileInputOptimization();
          initMobileGestures();
        }
      }, 150);
    });
  }

  // 初始化手机端手势支持
  function initMobileGestures() {
    if (window.innerWidth > 600) return; // 只在手机端启用

    let startX = 0;
    let startY = 0;
    let isSwipeGesture = false;

    // 监听触摸开始
    document.addEventListener(
      "touchstart",
      function (e) {
        if (e.touches.length === 1) {
          startX = e.touches[0].clientX;
          startY = e.touches[0].clientY;
          isSwipeGesture = false;
        }
      },
      { passive: true }
    );

    // 监听触摸移动
    document.addEventListener(
      "touchmove",
      function (e) {
        if (e.touches.length === 1 && !isSwipeGesture) {
          const currentX = e.touches[0].clientX;
          const currentY = e.touches[0].clientY;
          const deltaX = currentX - startX;
          const deltaY = currentY - startY;

          // 判断是否为水平滑动手势
          if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 30) {
            isSwipeGesture = true;

            // 从左边缘向右滑动打开会话列表
            if (startX < 50 && deltaX > 80 && !state.mobileSessionOpen && !state.mobileVisitorInfoOpen) {
              openMobileSession();
            }
            // 在会话列表上向左滑动关闭
            else if (state.mobileSessionOpen && deltaX < -80) {
              closeMobileSession();
            }
            // 在访客信息页面向右滑动关闭
            else if (state.mobileVisitorInfoOpen && deltaX > 80) {
              closeMobileVisitorInfo();
            }
          }
        }
      },
      { passive: true }
    );

    // 监听触摸结束
    document.addEventListener(
      "touchend",
      function (e) {
        isSwipeGesture = false;
      },
      { passive: true }
    );
  }

  // 自动刷新定时器
  let refreshTimer = null;

  // 开始自动刷新
  function startAutoRefresh() {
    if (refreshTimer) return;
    refreshTimer = setInterval(() => {
      if (state.loadingState.sessionLoading) return;
      fetchChatList().catch(console.error);
    }, 10000); // 每10秒刷新一次
  }

  // 停止自动刷新
  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  }

  // 切换声音开关
  function toggleSound() {
    const newState = ChatCore.NotificationManager.toggleSound();
    state.soundEnabled = newState;
    ChatCore.NotificationManager.showSoundStatusMessage(newState);
  }

  // 更新WebSocket状态
  function updateWebSocketStatus(status) {
    const config = ChatCore.WebSocketStatusManager.getStatusConfig(status);
    state.wsStatus.className = config.className;
    state.wsStatus.text = config.text;
  }

  // 显示连接信息
  function showConnectionInfo() {
    const info = ChatCore.WebSocketStatusManager.getConnectionInfo();
    let message = `连接状态: ${info.statusText}`;

    if (info.connectTime) {
      message += `\n<br/>连接时间: ${info.connectTime}`;
    }
    if (info.connectDuration) {
      message += `\n<br/>连接时长: ${info.connectDuration}`;
    }

    if (window.layui) {
      layui.use("layer", function () {
        layui.layer.alert(message, {
          title: "WebSocket连接信息",
          icon: info.status === "connected" ? 1 : 2,
        });
      });
    }
  }

  // layer消息
  function layerMsg(msg) {
    if (window.layui) {
      layui.use("layer", function () {
        layui.layer.msg(msg);
      });
    } else {
      alert(msg);
    }
  }

  // 消息渲染（后端已返回完整头像昵称，无需前端处理默认值）
  function renderMessage(msg) {
    const isSelf = msg.from_guid === state.user.guid || msg.from_type === 2;
    return {
      ...msg,
      avatar: msg.from_avatar, // 直接使用后端返回的头像
      name: msg.from_name, // 直接使用后端返回的昵称
      isSelf,
    };
  }

  // 加载访客信息
  function loadVisitorInfo(memberGuid) {
    if (!memberGuid) return;

    // 检查缓存
    if (state.visitorInfoCache[memberGuid]) {
      // 使用缓存的信息更新当前会话
      if (state.activeSession && state.activeSession.member_guid === memberGuid) {
        Object.assign(state.activeSession, state.visitorInfoCache[memberGuid]);
      }
      return Promise.resolve(state.visitorInfoCache[memberGuid]);
    }

    // 发起API请求
    return new Promise((resolve, reject) => {
      ajax_admin_api_v1(
        "/kefu/visitor_info",
        {
          member_guid: memberGuid,
          session_guid: state.activeSession ? state.activeSession.session_guid : "",
        },
        function (res) {
          // 成功回调 - res.code === 0 的情况
          if (res.data) {
            // 缓存访客信息
            const visitorInfo = {
              name: res.data.name || "访客",
              ip: res.data.ip || "",
              address: res.data.address || "",
              device: res.data.device || "",
              remark: res.data.remark || "",
              mobile: res.data.mobile || "",
              register_time: res.data.register_time || "",
            };

            state.visitorInfoCache[memberGuid] = visitorInfo;

            // 更新当前活跃会话的访客信息
            if (state.activeSession && state.activeSession.member_guid === memberGuid) {
              Object.assign(state.activeSession, visitorInfo);
            }

            resolve(visitorInfo);
          } else {
            reject(new Error("访客信息数据为空"));
          }
        },
        function (res) {
          // 失败回调 - res.code !== 0 的情况
          console.error("获取访客信息失败:", res.msg || "未知错误");
          reject(new Error(res.msg || "获取访客信息失败"));
        }
      );
    });
  }

  // 更新访客备注
  function updateRemark() {
    if (!state.activeSession || !state.activeSession.member_guid) {
      return;
    }

    const memberGuid = state.activeSession.member_guid;
    const remark = state.activeSession.remark || "";

    // 添加保存中的视觉反馈
    const textarea = document.querySelector(".remark-textarea");
    const statusEl = document.getElementById("remarkStatus");

    if (textarea) {
      textarea.classList.add("saving");
    }
    if (statusEl) {
      statusEl.className = "remark-status saving";
      statusEl.textContent = "保存中...";
    }

    ajax_admin_api_v1(
      "/kefu/update_visitor_remark",
      {
        member_guid: memberGuid,
        remark: remark,
        session_guid: state.activeSession.session_guid || "",
      },
      function (res) {
        // 成功回调 - res.code === 0 的情况
        // 移除保存中状态
        if (textarea) {
          textarea.classList.remove("saving");
        }

        // 更新缓存
        if (state.visitorInfoCache[memberGuid]) {
          state.visitorInfoCache[memberGuid].remark = remark;
        }

        // 添加保存成功的视觉反馈
        if (textarea) {
          textarea.classList.add("saved");
          setTimeout(() => {
            textarea.classList.remove("saved");
          }, 600);
        }

        // 显示成功状态
        if (statusEl) {
          statusEl.className = "remark-status saved";
          statusEl.textContent = "已保存";
          setTimeout(() => {
            statusEl.className = "remark-status";
            statusEl.textContent = "";
          }, 2000);
        }
      },
      function (res) {
        // 失败回调 - res.code !== 0 的情况
        // 移除保存中状态
        if (textarea) {
          textarea.classList.remove("saving");
        }

        console.error("更新备注失败:", res.msg || "未知错误");
        // 显示错误状态
        if (statusEl) {
          statusEl.className = "remark-status error";
          statusEl.textContent = res.msg || "保存失败";
          setTimeout(() => {
            statusEl.className = "remark-status";
            statusEl.textContent = "";
          }, 3000);
        }
      }
    );
  }

  // 客服状态心跳
  let heartbeatTimer = null;

  function startKefuHeartbeat() {
    // 每5分钟发送一次心跳，更新客服活跃状态
    heartbeatTimer = setInterval(() => {
      if (state.user && state.user.guid) {
        ajax_admin_api_v1(
          "/kefu/heartbeat",
          {
            kefu_guid: state.user.guid,
            bid: state.user.bid,
          },
          function (res) {
            // 心跳成功
            console.log("[HEARTBEAT] 客服状态心跳成功");
          },
          function (res) {
            console.error("[HEARTBEAT] 客服状态心跳失败:", res.msg);
          }
        );
      }
    }, 5 * 60 * 1000); // 5分钟
  }

  function stopKefuHeartbeat() {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }
  }

  // 页面初始化
  function init() {
    // 初始化声音开关状态
    state.soundEnabled = ChatCore.NotificationManager.initSoundSettings();

    ajax_admin_api_v1(
      "/kefu/config",
      {},
      function (res) {
        state.user = res.data.user;
        // 兼容 socketUrl
        window.kefuConfig = res.data;

        // 初始化WebSocket状态管理
        ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTING);
        updateWebSocketStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTING);

        // 注册状态变化监听
        ChatCore.WebSocketStatusManager.onStatusChange((newStatus, oldStatus) => {
          updateWebSocketStatus(newStatus);
          console.log(`[客服端] WebSocket状态变化: ${oldStatus} -> ${newStatus}`);
        });

        ChatCore.connectWS(
          function onOpen() {
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.CONNECTED);
            ChatCore.sendMsg("kefuInit", {
              kefu_guid: state.user.guid,
              name: state.user.name,
              avatar: state.user.head || state.user.avatar || "",
              bid: state.user.bid,
            });
            // 连接成功后获取一次最新数据
            fetchChatList().catch(console.error);
          },
          function onClose() {
            // 连接关闭时的处理
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.DISCONNECTED);
          },
          function onError() {
            // 连接错误时的处理
            ChatCore.WebSocketStatusManager.updateStatus(ChatCore.WebSocketStatusManager.STATUS.DISCONNECTED);
          }
        );

        registerEvents();
        fetchChatList().catch(console.error);

        // 初始化响应式布局
        initResponsiveListener();

        // 初始化手机端优化
        initMobileInputOptimization();
        initMobileGestures();

        // 启动客服状态心跳
        startKefuHeartbeat();

        // 页面关闭时清理
        window.addEventListener("beforeunload", () => {
          console.log("[KEFU] 页面即将关闭，停止心跳并清理WebSocket连接");
          stopKefuHeartbeat();
          ChatCore.cleanup();
        });

        Vue.createApp({
          setup() {
            return {
              ...Vue.toRefs(state),
              selectSession,
              acceptSession,
              rejectSession,
              sendMessage,
              recallMessage,
              resendMessage,
              quickSend,
              closeSession,
              transferSession,
              toggleSound,
              showConnectionInfo,
              updateRemark,
              logout,
              // 图片上传相关方法
              openImageUpload,
              handleImageSelect,
              previewImage,
              closeImagePreview,
              // 表情相关方法
              openEmojiPicker,
              // 手机端交互方法
              openMobileSession,
              closeMobileSession,
              openMobileVisitorInfo,
              closeMobileVisitorInfo,
              switchMobileTab,
              selectSessionMobile,
              // 媒体消息处理方法（使用ChatCore）
              isImageMessage: ChatCore.MediaUploadManager.isImageMessage,
              getImageUrl: ChatCore.MediaUploadManager.getImageUrl,
              isVideoMessage: ChatCore.MediaUploadManager.isVideoMessage,
              getVideoUrl: ChatCore.MediaUploadManager.getVideoUrl,
              getVideoThumbnail: ChatCore.MediaUploadManager.getVideoThumbnail,
              getVideoDuration: ChatCore.MediaUploadManager.getVideoDuration,
              formatVideoDuration: ChatCore.MediaUploadManager.formatVideoDuration,
              playVideo: ChatCore.MediaUploadManager.playVideo,
            };
          },
        }).mount("#app_chat");
      },
      function (res) {
        layerMsg("获取客服配置失败:" + res.msg);
      }
    );
  }

  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", init);
  } else {
    init();
  }
})();
