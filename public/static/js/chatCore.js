// public/static/js/chatCore.js
(function (global) {
  const eventHandlers = {}; // { cmd: [fn, ...] }
  let ws = null;
  let heartbeatTimer = null;
  let reconnectTimer = null;
  let lastUrl = "";
  let lastOnOpen = null;
  const MAX_RECONNECT_ATTEMPTS = 10; // 增加最大重连次数
  let reconnectAttempts = 0;
  let reconnectBackoffMultiplier = 1.5; // 退避倍数
  let maxReconnectDelay = 60000; // 最大重连延迟60秒
  let isManualClose = false; // 是否手动关闭连接

  // 注册事件监听
  function on(cmd, handler) {
    if (!eventHandlers[cmd]) eventHandlers[cmd] = [];
    eventHandlers[cmd].push(handler);
  }

  // 移除事件监听
  function off(cmd, handler) {
    if (!eventHandlers[cmd]) return;
    const index = eventHandlers[cmd].indexOf(handler);
    if (index > -1) {
      eventHandlers[cmd].splice(index, 1);
    }
  }

  // 统一分发
  function dispatch(cmd, data) {
    // 执行注册的事件监听器
    (eventHandlers[cmd] || []).forEach((fn) => fn(data));

    // 自动执行业务页面定义的回调函数（如果存在）
    const callbackName = cmd + "Callback";
    if (typeof global[callbackName] === "function") {
      try {
        console.log(`[ChatCore] 执行自动回调: ${callbackName}`);
        global[callbackName](data);
      } catch (error) {
        console.error(`[ChatCore] 回调函数 ${callbackName} 执行错误:`, error);
      }
    }
  }

  // 心跳相关变量
  let heartbeatInterval = 30000; // 心跳间隔，默认30秒
  let heartbeatTimeout = 10000; // 心跳超时时间，默认10秒
  let lastHeartbeatTime = 0; // 最后一次心跳时间
  let heartbeatTimeoutTimer = null; // 心跳超时定时器
  let missedHeartbeats = 0; // 连续丢失的心跳次数
  const MAX_MISSED_HEARTBEATS = 3; // 最大允许丢失心跳次数

  // 心跳检测
  function startHeartbeat() {
    if (heartbeatTimer) clearInterval(heartbeatTimer);

    heartbeatTimer = setInterval(() => {
      if (ws && ws.readyState === 1) {
        const now = Date.now();
        lastHeartbeatTime = now;

        // 发送心跳
        sendMsg("heartbeat", {
          timestamp: now,
          client_type: getClientType(),
          missed_count: missedHeartbeats,
        });

        // 设置心跳超时检测
        if (heartbeatTimeoutTimer) clearTimeout(heartbeatTimeoutTimer);
        heartbeatTimeoutTimer = setTimeout(() => {
          handleHeartbeatTimeout();
        }, heartbeatTimeout);

        console.log(`[HEARTBEAT] 发送心跳 - 时间: ${new Date(now).toLocaleTimeString()}, 丢失次数: ${missedHeartbeats}`);
      }
    }, heartbeatInterval);
  }

  // 处理心跳超时
  function handleHeartbeatTimeout() {
    missedHeartbeats++;
    console.warn(`[HEARTBEAT] 心跳超时 - 连续丢失: ${missedHeartbeats}/${MAX_MISSED_HEARTBEATS}`);

    if (missedHeartbeats >= MAX_MISSED_HEARTBEATS) {
      console.error("[HEARTBEAT] 心跳连续超时，触发重连");
      dispatch("wsHeartbeatTimeout", { missedCount: missedHeartbeats });

      // 强制重连
      if (ws) {
        ws.close(1000, "心跳超时");
      }
    } else {
      // 调整心跳间隔，加快检测频率
      adjustHeartbeatInterval();
    }
  }

  // 处理心跳响应
  function handleHeartbeatResponse(data) {
    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer);
      heartbeatTimeoutTimer = null;
    }

    // 重置丢失计数
    if (missedHeartbeats > 0) {
      console.log(`[HEARTBEAT] 心跳恢复 - 重置丢失计数: ${missedHeartbeats} -> 0`);
      missedHeartbeats = 0;

      // 恢复正常心跳间隔
      resetHeartbeatInterval();
    }

    // 更新服务器时间差（可选）
    if (data && data.server_timestamp) {
      const clientTime = Date.now();
      const timeDiff = clientTime - data.server_timestamp;
      console.log(`[HEARTBEAT] 时间差: ${timeDiff}ms`);
    }
  }

  // 调整心跳间隔
  function adjustHeartbeatInterval() {
    const newInterval = Math.max(heartbeatInterval * 0.7, 10000); // 最小10秒
    if (newInterval !== heartbeatInterval) {
      heartbeatInterval = newInterval;
      console.log(`[HEARTBEAT] 调整心跳间隔: ${heartbeatInterval}ms`);

      // 重启心跳
      stopHeartbeat();
      startHeartbeat();
    }
  }

  // 重置心跳间隔
  function resetHeartbeatInterval() {
    const defaultInterval = 30000;
    if (heartbeatInterval !== defaultInterval) {
      heartbeatInterval = defaultInterval;
      console.log(`[HEARTBEAT] 恢复默认心跳间隔: ${heartbeatInterval}ms`);

      // 重启心跳
      stopHeartbeat();
      startHeartbeat();
    }
  }

  // 获取客户端类型
  function getClientType() {
    // 根据当前页面判断客户端类型
    if (window.location.pathname.includes("/admin/")) {
      return "admin";
    } else if (window.location.pathname.includes("/member/")) {
      return "member";
    }
    return "unknown";
  }

  // 停止心跳
  function stopHeartbeat() {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }
    if (heartbeatTimeoutTimer) {
      clearTimeout(heartbeatTimeoutTimer);
      heartbeatTimeoutTimer = null;
    }
    missedHeartbeats = 0;
  }

  // 重置重连次数
  function resetReconnectAttempts() {
    reconnectAttempts = 0;
  }

  // 连接状态管理
  const ConnectionState = {
    CONNECTING: "connecting",
    CONNECTED: "connected",
    DISCONNECTED: "disconnected",
    RECONNECTING: "reconnecting",
    ERROR: "error",
  };

  let currentConnectionState = ConnectionState.DISCONNECTED;
  let connectionStatusElement = null;

  // 更新连接状态
  function updateConnectionState(newState, message = "") {
    const oldState = currentConnectionState;
    currentConnectionState = newState;

    console.log(`[CONNECTION] 状态变更: ${oldState} -> ${newState}${message ? ` (${message})` : ""}`);

    // 触发状态变更事件
    dispatch("wsConnectionStateChange", {
      oldState,
      newState,
      message,
      timestamp: Date.now(),
    });

    // 更新UI状态指示器
    updateConnectionStatusUI(newState, message);
  }

  // 更新连接状态UI
  function updateConnectionStatusUI(state, message) {
    if (!connectionStatusElement) {
      createConnectionStatusElement();
    }

    const statusConfig = {
      [ConnectionState.CONNECTING]: {
        text: "连接中...",
        class: "ws-status-connecting",
        color: "#1890ff",
      },
      [ConnectionState.CONNECTED]: {
        text: "已连接",
        class: "ws-status-connected",
        color: "#52c41a",
      },
      [ConnectionState.DISCONNECTED]: {
        text: "连接断开",
        class: "ws-status-disconnected",
        color: "#ff4d4f",
      },
      [ConnectionState.RECONNECTING]: {
        text: "重连中...",
        class: "ws-status-reconnecting",
        color: "#faad14",
      },
      [ConnectionState.ERROR]: {
        text: "连接错误",
        class: "ws-status-error",
        color: "#ff4d4f",
      },
    };

    const config = statusConfig[state] || statusConfig[ConnectionState.ERROR];

    if (connectionStatusElement) {
      connectionStatusElement.textContent = message || config.text;
      connectionStatusElement.className = `ws-connection-status ${config.class}`;
      connectionStatusElement.style.color = config.color;

      // 自动隐藏已连接状态
      if (state === ConnectionState.CONNECTED) {
        setTimeout(() => {
          if (connectionStatusElement && currentConnectionState === ConnectionState.CONNECTED) {
            connectionStatusElement.style.display = "none";
          }
        }, 3000);
      } else {
        connectionStatusElement.style.display = "block";
      }
    }
  }

  // 创建连接状态元素
  function createConnectionStatusElement() {
    if (connectionStatusElement) return;

    connectionStatusElement = document.createElement("div");
    connectionStatusElement.className = "ws-connection-status";
    connectionStatusElement.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 8px 12px;
      background: rgba(255, 255, 255, 0.95);
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      display: none;
    `;

    document.body.appendChild(connectionStatusElement);
  }

  // 错误类型定义
  const ErrorType = {
    NETWORK_ERROR: "network_error",
    TIMEOUT_ERROR: "timeout_error",
    AUTH_ERROR: "auth_error",
    SERVER_ERROR: "server_error",
    UNKNOWN_ERROR: "unknown_error",
  };

  // 错误处理
  function handleError(error) {
    console.error("[WS ERROR]", error);

    const errorInfo = classifyError(error);
    updateConnectionState(ConnectionState.ERROR, errorInfo.message);
    WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.DISCONNECTED);

    // 根据错误类型采取不同的恢复策略
    handleErrorRecovery(errorInfo);

    dispatch("wsError", {
      error,
      type: errorInfo.type,
      message: errorInfo.message,
      recoveryAction: errorInfo.recoveryAction,
    });
  }

  // 错误分类
  function classifyError(error) {
    let type = ErrorType.UNKNOWN_ERROR;
    let message = "连接发生未知错误";
    let recoveryAction = "retry";

    if (!error) {
      return { type, message, recoveryAction };
    }

    // 网络错误
    if (error.type === "error" || error.code === "NETWORK_ERROR") {
      type = ErrorType.NETWORK_ERROR;
      message = "网络连接错误";
      recoveryAction = "retry";
    }
    // 超时错误
    else if (error.code === "TIMEOUT" || error.message?.includes("timeout")) {
      type = ErrorType.TIMEOUT_ERROR;
      message = "连接超时";
      recoveryAction = "retry";
    }
    // 认证错误
    else if (error.code === 1002 || error.code === 1003) {
      type = ErrorType.AUTH_ERROR;
      message = "认证失败";
      recoveryAction = "refresh";
    }
    // 服务器错误
    else if (error.code >= 1011 && error.code <= 1015) {
      type = ErrorType.SERVER_ERROR;
      message = "服务器错误";
      recoveryAction = "wait";
    }

    return { type, message, recoveryAction };
  }

  // 错误恢复处理
  function handleErrorRecovery(errorInfo) {
    switch (errorInfo.recoveryAction) {
      case "retry":
        // 立即重试，但不增加重连次数
        console.log("[ERROR RECOVERY] 立即重试连接");
        setTimeout(() => {
          if (lastUrl) {
            connectWS(lastOnOpen, handleClose, handleError);
          }
        }, 2000);
        break;

      case "refresh":
        // 建议用户刷新页面
        console.log("[ERROR RECOVERY] 建议刷新页面");
        updateConnectionState(ConnectionState.ERROR, "认证失败，请刷新页面");
        break;

      case "wait":
        // 等待更长时间后重试
        console.log("[ERROR RECOVERY] 等待后重试");
        setTimeout(() => {
          if (lastUrl && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            connectWS(lastOnOpen, handleClose, handleError);
          }
        }, 10000);
        break;

      default:
        console.log("[ERROR RECOVERY] 默认重连策略");
        break;
    }
  }

  // 连接关闭处理
  function handleClose(event) {
    console.log("[WS CLOSE]", event);
    stopHeartbeat();

    // 如果是手动关闭，不进行重连
    if (isManualClose) {
      console.log("[WS] 手动关闭连接，不进行重连");
      updateConnectionState(ConnectionState.DISCONNECTED, "连接已关闭");
      WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.DISCONNECTED);
      isManualClose = false;
      return;
    }

    // 分析关闭原因
    const closeReason = getCloseReason(event);
    console.log("[WS] 连接关闭原因:", closeReason);

    updateConnectionState(ConnectionState.DISCONNECTED, closeReason.message);
    WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.DISCONNECTED);

    // 清理旧的重连定时器
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }

    // 根据关闭原因决定是否重连
    if (!closeReason.shouldReconnect) {
      console.log("[WS] 根据关闭原因，不进行重连");
      updateConnectionState(ConnectionState.ERROR, closeReason.message);
      return;
    }

    // 达到最大重连次数则停止
    if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
      console.error("[WS] 达到最大重连次数，停止重连");
      updateConnectionState(ConnectionState.ERROR, "重连失败，请刷新页面");
      dispatch("wsMaxReconnectAttempts", { attempts: reconnectAttempts });
      return;
    }

    // 智能重连延迟计算
    const baseDelay = 1000; // 基础延迟1秒
    const delay = Math.min(baseDelay * Math.pow(reconnectBackoffMultiplier, reconnectAttempts), maxReconnectDelay);

    const delaySeconds = Math.ceil(delay / 1000);
    updateConnectionState(ConnectionState.RECONNECTING, `${delaySeconds}秒后重连(${reconnectAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})...`);
    WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.RECONNECTING);

    reconnectTimer = setTimeout(() => {
      console.log(`[WS] 第${reconnectAttempts + 1}次重连尝试，延迟: ${delay}ms`);
      reconnectAttempts++;
      connectWS(lastOnOpen, handleClose, handleError);
    }, delay);
  }

  // 分析关闭原因
  function getCloseReason(event) {
    const code = event.code;
    const reason = event.reason || "";

    // WebSocket关闭状态码分析
    switch (code) {
      case 1000: // 正常关闭
        return {
          message: "连接正常关闭",
          shouldReconnect: reason !== "手动关闭" && reason !== "新连接替换",
        };
      case 1001: // 端点离开
        return {
          message: "服务器关闭",
          shouldReconnect: true,
        };
      case 1002: // 协议错误
        return {
          message: "协议错误",
          shouldReconnect: false,
        };
      case 1003: // 不支持的数据类型
        return {
          message: "数据类型错误",
          shouldReconnect: false,
        };
      case 1006: // 异常关闭
        return {
          message: "连接异常断开",
          shouldReconnect: true,
        };
      case 1011: // 服务器错误
        return {
          message: "服务器内部错误",
          shouldReconnect: true,
        };
      case 1012: // 服务重启
        return {
          message: "服务器重启中",
          shouldReconnect: true,
        };
      default:
        return {
          message: `连接关闭 (${code}: ${reason})`,
          shouldReconnect: true,
        };
    }
  }

  // 连接 WebSocket
  function connectWS(onOpen, onClose, onError) {
    // 保存参数供重连使用
    //lastUrl = (location.protocol === "https:" ? "wss://" : "ws://") + location.hostname + ":2021";
    let wsProtocol = location.hostname === "127.0.0.1" || location.hostname === "localhost" ? "ws://" : "wss://";
    lastUrl = wsProtocol + location.hostname + ":2021";

    lastOnOpen = onOpen;

    // 清理旧连接
    if (ws) {
      try {
        // 防止触发重连
        ws.onclose = null;
        ws.onerror = null;
        ws.onopen = null;
        ws.onmessage = null;

        if (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN) {
          ws.close(1000, "新连接替换");
        }
      } catch (e) {
        console.warn("[WS] 清理旧连接失败:", e);
      }
      ws = null;
    }

    updateConnectionState(ConnectionState.CONNECTING, "正在连接...");
    WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.CONNECTING);

    try {
      // 使用原生WebSocket
      ws = new WebSocket(lastUrl);

      ws.onopen = function (event) {
        console.log("[WS OPEN]", event);
        resetReconnectAttempts();
        updateConnectionState(ConnectionState.CONNECTED, "连接成功");
        WebSocketStatusManager.updateStatus(WebSocketStatusManager.STATUS.CONNECTED);
        startHeartbeat();

        // 处理离线消息队列
        processOfflineQueue();

        if (onOpen) onOpen(event);
      };

      ws.onclose = function (event) {
        console.log("[WS CLOSE]", event);
        handleClose(event);
      };

      ws.onerror = function (event) {
        console.error("[WS ERROR]", event);
        handleError(event);
      };

      ws.onmessage = function (e) {
        let msg;
        try {
          msg = JSON.parse(e.data);
        } catch (err) {
          console.error("[WS] 消息解析失败", err);
          return;
        }

        // 打印接收日志
        console.log("[WS RECV]", msg);

        // 处理心跳响应
        if (msg.cmd === "heartbeat") {
          handleHeartbeatResponse(msg.data);
          return;
        }

        if (msg && msg.cmd) {
          // 特殊处理转接相关事件
          if (msg.cmd === "transferRequest") {
            TransferManager.addPendingTransfer(msg.data);
          } else if (msg.cmd === "transferComplete") {
            dispatch("transferComplete", msg.data);
            // 显示转接完成通知
            const action = msg.data.action;
            if (action === "accepted") {
              layui.use("layer", function () {
                layer.msg("转接请求已被接受");
              });
            } else if (action === "rejected") {
              layui.use("layer", function () {
                layer.msg("转接请求被拒绝: " + (msg.data.reject_reason || "无原因"));
              });
            }
          } else if (msg.cmd === "transferSessionReply") {
            dispatch("transferSessionReply", msg.data);
            if (msg.data.code === 0) {
              layui.use("layer", function () {
                layer.msg("转接请求已发送，等待对方响应");
              });
            } else {
              layui.use("layer", function () {
                layer.msg("转接失败: " + msg.data.msg);
              });
            }
          } else if (msg.cmd === "respondTransferReply") {
            dispatch("respondTransferReply", msg.data);
          }

          dispatch(msg.cmd, msg);
        }
      };

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (ws && ws.readyState === WebSocket.CONNECTING) {
          console.warn("[WS] 连接超时");
          ws.close(1000, "连接超时");
          handleError(new Error("连接超时"));
        }
      }, 10000); // 10秒超时

      // 连接成功后清除超时
      const originalOnOpen = ws.onopen;
      ws.onopen = function (event) {
        clearTimeout(connectionTimeout);
        if (originalOnOpen) {
          originalOnOpen.call(this, event);
        }
      };

      // 连接关闭或错误时也清除超时
      const originalOnClose = ws.onclose;
      ws.onclose = function (event) {
        clearTimeout(connectionTimeout);
        if (originalOnClose) {
          originalOnClose.call(this, event);
        }
      };

      const originalOnError = ws.onerror;
      ws.onerror = function (event) {
        clearTimeout(connectionTimeout);
        if (originalOnError) {
          originalOnError.call(this, event);
        }
      };
    } catch (error) {
      console.error("[WS] 连接失败", error);
      handleError(error);
    }

    return ws;
  }

  // 消息发送队列
  const messageQueue = [];
  const offlineMessageQueue = []; // 离线消息队列
  let isProcessingQueue = false;
  const MAX_RETRY_COUNT = 3;
  const RETRY_DELAY = 2000;

  // 消息状态
  const MessageStatus = {
    PENDING: "pending",
    SENDING: "sending",
    SENT: "sent",
    FAILED: "failed",
    RETRY: "retry",
  };

  // 处理消息队列
  async function processMessageQueue() {
    if (isProcessingQueue || messageQueue.length === 0) return;

    isProcessingQueue = true;

    while (messageQueue.length > 0) {
      const messageItem = messageQueue[0];
      const { cmd, data, resolve, reject, retryCount = 0, messageId } = messageItem;

      try {
        if (!ws || ws.readyState !== 1) {
          // 连接断开，将消息移到离线队列
          moveToOfflineQueue(messageItem);
          messageQueue.shift();
          continue;
        }

        // 更新消息状态
        updateMessageStatus(messageId, MessageStatus.SENDING);

        const payload = { cmd, data };
        console.log("[WS SEND]", payload);
        ws.send(JSON.stringify(payload));

        updateMessageStatus(messageId, MessageStatus.SENT);
        resolve();
      } catch (error) {
        console.error("[WS] 发送失败", error);

        // 重试逻辑
        if (retryCount < MAX_RETRY_COUNT) {
          messageItem.retryCount = retryCount + 1;
          updateMessageStatus(messageId, MessageStatus.RETRY);

          console.log(`[WS] 消息重试 ${retryCount + 1}/${MAX_RETRY_COUNT}: ${cmd}`);

          // 延迟重试
          setTimeout(() => {
            processMessageQueue();
          }, RETRY_DELAY * (retryCount + 1));

          messageQueue.shift();
          messageQueue.unshift(messageItem); // 重新放到队列前面
          break;
        } else {
          updateMessageStatus(messageId, MessageStatus.FAILED);
          reject(new Error(`消息发送失败，已重试${MAX_RETRY_COUNT}次: ${error.message}`));
        }
      }

      messageQueue.shift();
    }

    isProcessingQueue = false;
  }

  // 移动消息到离线队列
  function moveToOfflineQueue(messageItem) {
    console.log("[WS] 连接断开，消息移至离线队列:", messageItem.cmd);
    offlineMessageQueue.push({
      ...messageItem,
      timestamp: Date.now(),
    });

    // 限制离线队列大小
    if (offlineMessageQueue.length > 100) {
      offlineMessageQueue.shift();
    }
  }

  // 处理离线消息队列
  function processOfflineQueue() {
    if (offlineMessageQueue.length === 0) return;

    console.log(`[WS] 处理离线消息队列，共${offlineMessageQueue.length}条消息`);

    // 将离线消息重新加入发送队列
    while (offlineMessageQueue.length > 0) {
      const offlineMessage = offlineMessageQueue.shift();
      messageQueue.push(offlineMessage);
    }

    processMessageQueue();
  }

  // 更新消息状态
  function updateMessageStatus(messageId, status) {
    if (!messageId) return;

    dispatch("messageStatusChange", {
      messageId,
      status,
      timestamp: Date.now(),
    });
  }

  // 发送消息
  function sendMsg(cmd, data, options = {}) {
    return new Promise((resolve, reject) => {
      const messageId = options.messageId || createGuid();
      const messageItem = {
        cmd,
        data,
        resolve,
        reject,
        messageId,
        priority: options.priority || 0, // 消息优先级
      };

      updateMessageStatus(messageId, MessageStatus.PENDING);

      // 根据优先级插入队列
      if (options.priority > 0) {
        // 高优先级消息插入队列前面
        const insertIndex = messageQueue.findIndex((item) => (item.priority || 0) < options.priority);
        if (insertIndex === -1) {
          messageQueue.push(messageItem);
        } else {
          messageQueue.splice(insertIndex, 0, messageItem);
        }
      } else {
        messageQueue.push(messageItem);
      }

      processMessageQueue();
    });
  }

  // 通用 guid 生成
  function createGuid() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      var r = (Math.random() * 16) | 0,
        v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  // 消息重发机制
  function resendMessage(msg, maxRetries = 3) {
    let retryCount = 0;
    let isResolved = false;
    let timeoutId = null;

    return new Promise((resolve, reject) => {
      // 监听 afterSend 回执
      const afterSendHandler = (response) => {
        if (response.data && response.data.local_id === msg.local_id && !isResolved) {
          isResolved = true;
          if (timeoutId) clearTimeout(timeoutId);
          off("afterSend", afterSendHandler); // 移除监听器
          resolve(response.data);
        }
      };

      // 注册 afterSend 监听器
      on("afterSend", afterSendHandler);

      // 总超时保护（30秒后强制失败）
      const totalTimeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          off("afterSend", afterSendHandler);
          const error = new Error("消息发送超时");
          dispatch("sendFailed", { local_id: msg.local_id, error });
          reject(error);
        }
      }, 30000);

      const retry = async () => {
        if (retryCount >= maxRetries) {
          isResolved = true;
          clearTimeout(totalTimeout);
          off("afterSend", afterSendHandler); // 移除监听器
          const error = new Error("达到最大重试次数");
          dispatch("sendFailed", { local_id: msg.local_id, error });
          reject(error);
          return;
        }

        retryCount++;
        try {
          await sendMsg("chatMessage", msg);
          // 等待回执，如果3秒内没收到就重试
          timeoutId = setTimeout(() => {
            if (!isResolved) {
              retry();
            }
          }, 3000);
        } catch (error) {
          if (!isResolved) {
            retry();
          }
        }
      };

      retry();
    });
  }

  // 通用撤回消息
  function recallMessage({ guid, session_guid, from_guid, bid }) {
    if (!guid) return Promise.reject(new Error("缺少消息ID"));

    return new Promise((resolve, reject) => {
      // 监听撤回回执
      const recallHandler = (response) => {
        // 检查是否是对应消息的回执
        // 注意：后端发送的数据结构是 { cmd: "rollBackMessageReply", data: { code: 0, msg: "...", data: { guid: "..." } } }
        if (response.data && response.data.data && response.data.data.guid === guid) {
          off("rollBackMessageReply", recallHandler);
          if (response.data.code === 0) {
            resolve(response.data.data);
          } else {
            reject(new Error(response.data.msg || "撤回失败"));
          }
        }
      };

      // 注册监听器
      on("rollBackMessageReply", recallHandler);

      // 发送撤回请求
      sendMsg("rollBackMessage", {
        guid,
        session_guid,
        from_guid,
        bid,
      }).catch((error) => {
        off("rollBackMessageReply", recallHandler);
        reject(error);
      });

      // 超时保护
      setTimeout(() => {
        off("rollBackMessageReply", recallHandler);
        reject(new Error("撤回请求超时"));
      }, 10000);
    });
  }

  // 手动关闭连接
  function closeConnection() {
    isManualClose = true;
    if (ws) {
      ws.close(1000, "手动关闭");
    }
    updateConnectionState(ConnectionState.DISCONNECTED, "连接已关闭");
  }

  // 强制重连
  function forceReconnect() {
    console.log("[WS] 强制重连");
    reconnectAttempts = 0; // 重置重连次数
    if (ws) {
      ws.close(1000, "强制重连");
    }
  }

  // 清理资源
  function cleanup() {
    console.log("[CHATCORE] 开始清理WebSocket资源");
    isManualClose = true; // 标记为手动关闭，避免重连
    console.log("[CHATCORE] 已标记为手动关闭");

    stopHeartbeat();
    console.log("[CHATCORE] 已停止心跳");

    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
      console.log("[CHATCORE] 已清理重连定时器");
    }

    if (ws) {
      console.log("[CHATCORE] WebSocket状态:", ws.readyState);
      ws.onclose = null;
      ws.close();
      ws = null;
      console.log("[CHATCORE] 已关闭WebSocket连接");
    }

    // 清理状态UI
    if (connectionStatusElement) {
      connectionStatusElement.remove();
      connectionStatusElement = null;
      console.log("[CHATCORE] 已清理状态UI");
    }

    updateConnectionState(ConnectionState.DISCONNECTED, "连接已清理");
    console.log("[CHATCORE] WebSocket资源清理完成");
  }

  // 已读功能公共逻辑
  const ReadManager = {
    /**
     * 发送已读回执
     * @param {string} sessionGuid 会话GUID
     * @param {string} fromGuid 读消息的人GUID
     * @param {string} toGuid 消息发送方GUID
     */
    sendReadReceipt(sessionGuid, fromGuid, toGuid) {
      if (!sessionGuid || !fromGuid || !toGuid) {
        console.log("无法发送已读回执，缺少必要参数", {
          sessionGuid,
          fromGuid,
          toGuid,
        });
        return;
      }

      console.log("发送已读回执", {
        session_guid: sessionGuid,
        from_guid: fromGuid,
        to_guid: toGuid,
      });

      sendMsg("readMessage", {
        session_guid: sessionGuid,
        from_guid: fromGuid,
        to_guid: toGuid,
      });
    },

    /**
     * 处理已读回执
     * @param {Object} msg 接收到的消息
     * @param {Array} messageList 消息列表
     * @param {string} currentSessionGuid 当前会话GUID
     * @param {string} logPrefix 日志前缀
     */
    handleReadReceipt(msg, messageList, currentSessionGuid, logPrefix = "") {
      console.log(`${logPrefix}收到已读回执`, msg);
      console.log(`${logPrefix}当前会话GUID`, currentSessionGuid);
      console.log(
        `${logPrefix}当前消息列表`,
        messageList.map((m) => ({
          guid: m.guid,
          is_read: m.is_read,
          content: m.content?.substring(0, 10),
        }))
      );

      if (msg.data && msg.data.session_guid === currentSessionGuid && msg.data.msg_ids) {
        console.log(`${logPrefix}开始处理已读回执，消息IDs:`, msg.data.msg_ids);

        // 根据具体的消息ID列表标记为已读
        msg.data.msg_ids.forEach((msgId) => {
          const message = messageList.find((m) => m.guid === msgId);
          if (message) {
            console.log(`${logPrefix}找到消息并标记为已读`, msgId, message.content?.substring(0, 10));
            message.is_read = 1;
          } else {
            console.log(`${logPrefix}未找到消息`, msgId);
          }
        });

        console.log(
          `${logPrefix}处理后的消息列表`,
          messageList.map((m) => ({
            guid: m.guid,
            is_read: m.is_read,
            content: m.content?.substring(0, 10),
          }))
        );
      } else {
        console.log(`${logPrefix}已读回执条件不满足`, {
          hasData: !!msg.data,
          sessionMatch: msg.data?.session_guid === currentSessionGuid,
          hasMsgIds: !!msg.data?.msg_ids,
        });
      }
    },

    /**
     * 设置页面可见性监听
     * @param {Function} callback 页面变为可见时的回调函数
     */
    setupVisibilityListener(callback) {
      if (typeof document === "undefined") return;

      const hiddenProperty = "hidden" in document ? "hidden" : "webkitHidden" in document ? "webkitHidden" : "mozHidden" in document ? "mozHidden" : null;

      if (hiddenProperty) {
        const visibilityChangeEvent = hiddenProperty.replace(/hidden/i, "visibilitychange");
        document.addEventListener(visibilityChangeEvent, function () {
          if (!document[hiddenProperty]) {
            callback();
          }
        });
      }
    },
  };

  // 通知管理器
  const NotificationManager = {
    titleBlinkTimer: null,
    soundEnabled: true, // 声音开关

    // 请求通知权限
    async requestPermission() {
      if (!("Notification" in window)) {
        console.log("此浏览器不支持桌面通知");
        return false;
      }

      if (Notification.permission === "granted") {
        return true;
      }

      if (Notification.permission !== "denied") {
        const permission = await Notification.requestPermission();
        return permission === "granted";
      }

      return false;
    },

    // 设置声音开关
    setSoundEnabled(enabled) {
      this.soundEnabled = enabled;
      // 保存到本地存储
      localStorage.setItem("kefu_sound_enabled", enabled ? "1" : "0");
    },

    // 获取声音开关状态
    isSoundEnabled() {
      const saved = localStorage.getItem("kefu_sound_enabled");
      return saved !== null ? saved === "1" : this.soundEnabled;
    },

    // 播放声音提醒
    playSound() {
      if (!this.isSoundEnabled()) return;

      try {
        const audio = document.getElementById("whisper-index-audio");
        if (audio) {
          audio.play().catch((e) => {
            console.log("声音播放失败:", e);
          });
        }
      } catch (e) {
        console.log("声音播放异常:", e);
      }
    },

    // 显示新会话通知
    showNewSessionNotification(session) {
      // 播放声音
      this.playSound();

      // 桌面通知
      this.showDesktopNotification("新会话提醒", `${session.name || session.id || "访客"} 请求咨询`, session.avatar);

      // 浏览器标题闪烁
      this.startTitleBlink("新会话");
    },

    // 显示新消息通知
    showNewMessageNotification(message) {
      // 总是播放声音提醒
      this.playSound();

      // 只在页面不可见时显示桌面通知和标题闪烁
      if (document.hidden) {
        // 桌面通知
        this.showDesktopNotification("新消息", message.content || "您有新消息", message.from_avatar);

        // 浏览器标题闪烁
        this.startTitleBlink("新消息");
      }
    },

    // 桌面通知
    async showDesktopNotification(title, body, icon) {
      const hasPermission = await this.requestPermission();
      if (!hasPermission) return;

      try {
        const notification = new Notification(title, {
          body: body,
          icon: icon || "/static/img/service.png",
          tag: "kefu-notification-" + Date.now(),
          requireInteraction: false,
        });

        notification.onclick = () => {
          window.focus();
          notification.close();
        };

        // 3秒后自动关闭
        setTimeout(() => {
          notification.close();
        }, 3000);
      } catch (e) {
        console.log("桌面通知失败:", e);
      }
    },

    // 浏览器标题闪烁
    startTitleBlink(message) {
      if (this.titleBlinkTimer) {
        clearInterval(this.titleBlinkTimer);
      }

      const originalTitle = document.title;
      let isBlinking = false;

      this.titleBlinkTimer = setInterval(() => {
        document.title = isBlinking ? originalTitle : `【${message}】${originalTitle}`;
        isBlinking = !isBlinking;
      }, 1000);

      // 页面获得焦点时停止闪烁
      const stopBlink = () => {
        if (this.titleBlinkTimer) {
          clearInterval(this.titleBlinkTimer);
          this.titleBlinkTimer = null;
        }
        document.title = originalTitle;
        window.removeEventListener("focus", stopBlink);
      };

      window.addEventListener("focus", stopBlink);

      // 10秒后自动停止闪烁
      setTimeout(stopBlink, 10000);
    },

    // 停止标题闪烁
    stopTitleBlink() {
      if (this.titleBlinkTimer) {
        clearInterval(this.titleBlinkTimer);
        this.titleBlinkTimer = null;
      }
    },

    // 切换声音开关
    toggleSound() {
      const newState = !this.isSoundEnabled();
      this.setSoundEnabled(newState);
      return newState;
    },

    // 获取声音开关状态文本
    getSoundStatusText() {
      return this.isSoundEnabled() ? "已开启" : "已关闭";
    },

    // 显示声音状态消息（可被重写）
    showSoundStatusMessage(enabled) {
      const message = `声音提醒${enabled ? "已开启" : "已关闭"}`;
      console.log(message);

      // 尝试使用layui消息提示
      if (window.layui) {
        layui.use("layer", function () {
          layui.layer.msg(message);
        });
      } else if (window.layer) {
        window.layer.msg(message);
      }
    },

    // 初始化声音设置（从本地存储读取）
    initSoundSettings() {
      // 从本地存储读取设置，如果没有则默认开启
      const saved = localStorage.getItem("kefu_sound_enabled");
      if (saved === null) {
        this.setSoundEnabled(true); // 默认开启
      }
      return this.isSoundEnabled();
    },
  };

  // 声音管理快捷方法（向后兼容）
  const SoundManager = {
    toggle: () => NotificationManager.toggleSound(),
    enable: () => NotificationManager.setSoundEnabled(true),
    disable: () => NotificationManager.setSoundEnabled(false),
    isEnabled: () => NotificationManager.isSoundEnabled(),
    getStatusText: () => NotificationManager.getSoundStatusText(),
    init: () => NotificationManager.initSoundSettings(),
  };

  // WebSocket状态管理器
  const WebSocketStatusManager = {
    // 状态常量
    STATUS: {
      CONNECTED: "connected",
      CONNECTING: "connecting",
      DISCONNECTED: "disconnected",
      RECONNECTING: "reconnecting",
    },

    // 当前状态
    currentStatus: "disconnected",

    // 连接开始时间
    connectTime: null,

    // 状态变化回调函数列表
    statusCallbacks: [],

    // 更新状态
    updateStatus(newStatus) {
      const oldStatus = this.currentStatus;
      this.currentStatus = newStatus;

      // 记录连接时间
      if (newStatus === this.STATUS.CONNECTED) {
        this.connectTime = new Date();
      } else if (newStatus === this.STATUS.DISCONNECTED) {
        this.connectTime = null;
      }

      // 触发状态变化回调
      this.statusCallbacks.forEach((callback) => {
        try {
          callback(newStatus, oldStatus);
        } catch (error) {
          console.error("WebSocket状态回调执行错误:", error);
        }
      });

      console.log(`[WebSocket] 状态变化: ${oldStatus} -> ${newStatus}`);
    },

    // 注册状态变化回调
    onStatusChange(callback) {
      if (typeof callback === "function") {
        this.statusCallbacks.push(callback);
      }
    },

    // 移除状态变化回调
    offStatusChange(callback) {
      const index = this.statusCallbacks.indexOf(callback);
      if (index > -1) {
        this.statusCallbacks.splice(index, 1);
      }
    },

    // 获取当前状态
    getStatus() {
      return this.currentStatus;
    },

    // 获取状态配置
    getStatusConfig(status = this.currentStatus) {
      const configs = {
        [this.STATUS.CONNECTED]: {
          color: "#52c41a",
          text: "连接正常",
          icon: "layui-icon-wifi",
          className: "connected",
        },
        [this.STATUS.CONNECTING]: {
          color: "#faad14",
          text: "正在连接...",
          icon: "layui-icon-wifi",
          className: "connecting",
        },
        [this.STATUS.DISCONNECTED]: {
          color: "#ff4d4f",
          text: "连接断开",
          icon: "layui-icon-wifi",
          className: "disconnected",
        },
        [this.STATUS.RECONNECTING]: {
          color: "#fa8c16",
          text: "重连中...",
          icon: "layui-icon-wifi",
          className: "reconnecting",
        },
      };
      return configs[status] || configs[this.STATUS.DISCONNECTED];
    },

    // 获取连接详情
    getConnectionInfo() {
      const config = this.getStatusConfig();
      let info = {
        status: this.currentStatus,
        statusText: config.text,
        color: config.color,
      };

      if (this.currentStatus === this.STATUS.CONNECTED && this.connectTime) {
        const duration = Math.floor((new Date() - this.connectTime) / 1000);
        const minutes = Math.floor(duration / 60);
        const seconds = duration % 60;
        info.connectDuration = minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`;
        info.connectTime = this.connectTime.toLocaleTimeString();
      }

      return info;
    },

    // 显示状态提示消息
    showStatusMessage(message, type = "info") {
      if (window.layui) {
        layui.use("layer", function () {
          const iconMap = {
            success: 1,
            error: 2,
            warning: 3,
            info: 0,
          };
          layui.layer.msg(message, {
            icon: iconMap[type] || 0,
            time: 2000,
          });
        });
      } else {
        console.log(`[WebSocket状态] ${message}`);
      }
    },

    // 初始化
    init() {
      console.log("[WebSocket状态管理器] 初始化完成");
      return this;
    },
  };

  // 转接管理器
  const TransferManager = {
    // 当前转接请求列表
    pendingTransfers: new Map(),

    // 发起转接请求
    transferSession(sessionGuid, toUserGuid, transferReason = "") {
      return new Promise((resolve, reject) => {
        sendMsg("transferSession", {
          session_guid: sessionGuid,
          to_user_guid: toUserGuid,
          transfer_reason: transferReason,
        })
          .then(resolve)
          .catch(reject);
      });
    },

    // 响应转接请求
    respondTransfer(transferGuid, action, rejectReason = "") {
      return new Promise((resolve, reject) => {
        sendMsg("respondTransfer", {
          transfer_guid: transferGuid,
          action: action, // 'accept' 或 'reject'
          reject_reason: rejectReason,
        })
          .then(() => {
            // 从待处理列表中移除
            this.pendingTransfers.delete(transferGuid);
            resolve();
          })
          .catch(reject);
      });
    },

    // 接受转接
    acceptTransfer(transferGuid) {
      return this.respondTransfer(transferGuid, "accept");
    },

    // 拒绝转接
    rejectTransfer(transferGuid, reason = "") {
      return this.respondTransfer(transferGuid, "reject", reason);
    },

    // 添加转接请求到待处理列表
    addPendingTransfer(transferData) {
      this.pendingTransfers.set(transferData.transfer_guid, {
        ...transferData,
        received_time: Date.now(),
      });

      // 触发转接请求事件
      dispatch("transferRequestReceived", transferData);

      // 显示转接通知
      this.showTransferNotification(transferData);
    },

    // 显示转接通知
    showTransferNotification(transferData) {
      const message = `${transferData.from_user_name} 请求将会话转接给您`;

      // 播放提示音
      NotificationManager.playSound();

      // 桌面通知
      if (document.hidden) {
        NotificationManager.showDesktopNotification("会话转接请求", message);
        NotificationManager.startTitleBlink("转接请求");
      }

      // 显示转接弹窗
      this.showTransferDialog(transferData);
    },

    // 显示转接对话框
    showTransferDialog(transferData) {
      // 创建转接对话框
      const dialog = document.createElement("div");
      dialog.className = "transfer-dialog-overlay";
      dialog.innerHTML = `
        <div class="transfer-dialog">
          <div class="transfer-dialog-header">
            <h3>会话转接请求</h3>
            <span class="transfer-dialog-close">&times;</span>
          </div>
          <div class="transfer-dialog-body">
            <div class="transfer-info">
              <p><strong>转出客服：</strong>${transferData.from_user_name}</p>
              <p><strong>转接原因：</strong>${transferData.transfer_reason || "无"}</p>
              <p><strong>会话信息：</strong>会员 ${transferData.session_info.member_guid}</p>
            </div>
            <div class="transfer-timeout">
              <span class="timeout-text">请在 <span class="countdown">300</span> 秒内响应</span>
            </div>
          </div>
          <div class="transfer-dialog-footer">
            <button class="btn-reject">拒绝</button>
            <button class="btn-accept">接受</button>
          </div>
        </div>
      `;

      // 添加样式
      if (!document.getElementById("transfer-dialog-style")) {
        const style = document.createElement("style");
        style.id = "transfer-dialog-style";
        style.textContent = `
          .transfer-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
          }
          .transfer-dialog {
            background: white;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          }
          .transfer-dialog-header {
            padding: 16px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .transfer-dialog-header h3 {
            margin: 0;
            color: #333;
          }
          .transfer-dialog-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
          }
          .transfer-dialog-body {
            padding: 20px;
          }
          .transfer-info p {
            margin: 8px 0;
            color: #666;
          }
          .transfer-timeout {
            margin-top: 16px;
            padding: 8px 12px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            text-align: center;
          }
          .countdown {
            font-weight: bold;
            color: #e17055;
          }
          .transfer-dialog-footer {
            padding: 16px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
          }
          .transfer-dialog-footer button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          }
          .btn-reject {
            background: #ddd;
            color: #666;
          }
          .btn-reject:hover {
            background: #ccc;
          }
          .btn-accept {
            background: #5cb85c;
            color: white;
          }
          .btn-accept:hover {
            background: #449d44;
          }
        `;
        document.head.appendChild(style);
      }

      document.body.appendChild(dialog);

      // 倒计时
      const countdownEl = dialog.querySelector(".countdown");
      const timeoutTime = new Date(transferData.timeout_time).getTime();
      const countdownTimer = setInterval(() => {
        const remaining = Math.max(0, Math.floor((timeoutTime - Date.now()) / 1000));
        countdownEl.textContent = remaining;

        if (remaining <= 0) {
          clearInterval(countdownTimer);
          dialog.remove();
          this.pendingTransfers.delete(transferData.transfer_guid);
        }
      }, 1000);

      // 事件处理
      dialog.querySelector(".transfer-dialog-close").onclick = () => {
        clearInterval(countdownTimer);
        dialog.remove();
      };

      dialog.querySelector(".btn-reject").onclick = () => {
        const reason = prompt("请输入拒绝原因（可选）：") || "";
        this.rejectTransfer(transferData.transfer_guid, reason)
          .then(() => {
            clearInterval(countdownTimer);
            dialog.remove();
            layui.use("layer", function () {
              layer.msg("已拒绝转接请求");
            });
          })
          .catch((err) => {
            console.error("拒绝转接失败:", err);
            layui.use("layer", function () {
              layer.msg("拒绝转接失败: " + err.message);
            });
          });
      };

      dialog.querySelector(".btn-accept").onclick = () => {
        this.acceptTransfer(transferData.transfer_guid)
          .then(() => {
            clearInterval(countdownTimer);
            dialog.remove();
            layui.use("layer", function () {
              layer.msg("已接受转接请求");
            });

            // 触发转接接受成功事件，通知前端更新UI
            dispatch("transferAccepted", {
              transfer_guid: transferData.transfer_guid,
              session_guid: transferData.session_guid,
              session_info: transferData.session_info,
            });
          })
          .catch((err) => {
            console.error("接受转接失败:", err);
            layui.use("layer", function () {
              layer.msg("接受转接失败: " + err.message);
            });
          });
      };
    },

    // 获取待处理转接列表
    getPendingTransfers() {
      return Array.from(this.pendingTransfers.values());
    },

    // 清理过期的转接请求
    cleanupExpiredTransfers() {
      const now = Date.now();
      for (const [guid, transfer] of this.pendingTransfers) {
        const timeoutTime = new Date(transfer.timeout_time).getTime();
        if (timeoutTime < now) {
          this.pendingTransfers.delete(guid);
        }
      }
    },
  };

  // 媒体文件上传管理器
  const MediaUploadManager = {
    // 验证媒体文件（图片和视频）
    validateMediaFile(file) {
      const validImageTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
      const validVideoTypes = ["video/mp4", "video/webm", "video/ogg", "video/avi", "video/mov", "video/wmv", "video/quicktime", "video/x-msvideo"];

      const maxImageSize = 5 * 1024 * 1024; // 5MB
      const maxVideoSize = 100 * 1024 * 1024; // 100MB

      const isImage = validImageTypes.includes(file.type);
      const isVideo = validVideoTypes.includes(file.type);

      if (!isImage && !isVideo) {
        return {
          valid: false,
          error: "请选择图片文件（JPG、PNG、GIF、WebP）或视频文件（MP4、WebM、AVI、MOV等）",
        };
      }

      if (isImage && file.size > maxImageSize) {
        return { valid: false, error: "图片大小不能超过5MB" };
      }

      if (isVideo && file.size > maxVideoSize) {
        return { valid: false, error: "视频大小不能超过100MB" };
      }

      return {
        valid: true,
        type: isImage ? "image" : "video",
      };
    },

    // 检查视频时长
    checkVideoDuration(file) {
      return new Promise((resolve, reject) => {
        const video = document.createElement("video");
        video.preload = "metadata";

        video.onloadedmetadata = function () {
          const duration = video.duration;
          const maxDuration = 60; // 5分钟

          URL.revokeObjectURL(video.src);

          if (duration > maxDuration) {
            reject(new Error(`视频时长不能超过5分钟，当前时长：${Math.round(duration)}秒`));
          } else {
            resolve({ duration: duration });
          }
        };

        video.onerror = function () {
          URL.revokeObjectURL(video.src);
          reject(new Error("无法读取视频信息，请确保文件格式正确"));
        };

        video.src = URL.createObjectURL(file);
      });
    },

    // 上传媒体文件（修复 Illegal invocation 错误）
    uploadMediaFile(file, fileType, apiType) {
      return new Promise((resolve, reject) => {
        console.log(`[MediaUpload] 开始上传${fileType === "video" ? "视频" : "图片"}:`, file.name);

        try {
          // 创建FormData
          const formData = new FormData();
          formData.append("file", file);

          // 根据 API 类型确定 URL
          const baseUrl = apiType === "admin" ? "/admin_api/v1" : "/member_api/v1";
          const uploadUrl = baseUrl + "/file/upload";

          // 使用 jQuery 的 $.ajax 直接上传，避免 Illegal invocation 错误
          $.ajax({
            url: uploadUrl,
            type: "POST",
            data: formData,
            processData: false, // 重要：不处理数据
            contentType: false, // 重要：不设置内容类型
            success: function (res) {
              console.log(`[MediaUpload] ${fileType === "video" ? "视频" : "图片"}上传响应:`, res);
              if (res.code === 0) {
                resolve({
                  success: true,
                  data: res.data,
                  fileType: fileType,
                });
              } else {
                reject(new Error(res.msg || `${fileType === "video" ? "视频" : "图片"}上传失败`));
              }
            },
            error: function (xhr, status, error) {
              console.error(`[MediaUpload] ${fileType === "video" ? "视频" : "图片"}上传错误:`, error);
              reject(new Error(`${fileType === "video" ? "视频" : "图片"}上传失败: ${error}`));
            },
          });
        } catch (error) {
          console.error(`[MediaUpload] 上传过程中发生错误:`, error);
          reject(new Error(`上传失败: ${error.message}`));
        }
      });
    },

    // 处理媒体文件选择
    handleMediaSelect(file, apiType, sendMessageCallback, showMessageCallback) {
      if (!file) return;

      // 验证媒体文件
      const validation = this.validateMediaFile(file);
      if (!validation.valid) {
        showMessageCallback(validation.error);
        return;
      }

      if (validation.type === "video") {
        this.checkVideoDuration(file)
          .then((videoInfo) => this.processMediaUpload(file, "video", apiType, sendMessageCallback, showMessageCallback, videoInfo.duration))
          .catch((error) => showMessageCallback(error.message));
      } else {
        this.processMediaUpload(file, "image", apiType, sendMessageCallback, showMessageCallback);
      }
    },

    // 处理媒体上传
    processMediaUpload(file, fileType, apiType, sendMessageCallback, showMessageCallback, videoDuration = 0) {
      showMessageCallback(`正在上传${fileType === "video" ? "视频" : "图片"}...`);

      this.uploadMediaFile(file, fileType, apiType)
        .then((result) => {
          const mediaUrl = result.data.filePath;

          if (fileType === "video") {
            // 视频消息：创建视频消息格式，使用前端提取的时长
            const thumbnailUrl = result.data.thumbnailPath || "";
            const duration = videoDuration || result.data.duration || 0; // 优先使用前端提取的时长
            const fileSize = file.size;
            const fileName = file.name;

            const videoContent = this.createVideoMessage(mediaUrl, thumbnailUrl, duration, fileSize, fileName);
            sendMessageCallback(videoContent);
          } else {
            // 图片消息：保持原有格式
            const imageContent = `img[${mediaUrl}]`;
            sendMessageCallback(imageContent);
          }

          showMessageCallback(`${fileType === "video" ? "视频" : "图片"}上传成功`);
        })
        .catch((error) => {
          showMessageCallback(error.message);
        });
    },

    // 创建视频消息格式
    createVideoMessage(videoUrl, thumbnailUrl, duration, fileSize, fileName) {
      return `video[${videoUrl}|${thumbnailUrl || ""}|${Math.round(duration)}|${fileSize}|${fileName || ""}]`;
    },

    // 解析视频消息
    parseVideoMessage(content) {
      const videoRegex = /^video\[([^|]*)\|([^|]*)\|([^|]*)\|([^|]*)\|([^|]*)\]$/;
      const match = content.match(videoRegex);

      if (match) {
        return {
          type: "video",
          videoUrl: match[1],
          thumbnailUrl: match[2] || null,
          duration: parseInt(match[3]) || 0,
          fileSize: parseInt(match[4]) || 0,
          fileName: match[5] || "视频文件",
        };
      }

      return null;
    },

    // 检查是否为视频消息
    isVideoMessage(content) {
      return content && content.startsWith("video[") && content.endsWith("]");
    },

    // 检查是否为图片消息
    isImageMessage(content) {
      return content && content.startsWith("img[") && content.endsWith("]");
    },

    // 获取图片URL
    getImageUrl(content) {
      if (MediaUploadManager.isImageMessage(content)) {
        return content.slice(4, -1); // 去掉 "img[" 和 "]"
      }
      return "";
    },

    // 获取视频URL
    getVideoUrl(content) {
      const videoData = MediaUploadManager.parseVideoMessage(content);
      return videoData ? videoData.videoUrl : "";
    },

    // 获取视频缩略图
    getVideoThumbnail(content) {
      const videoData = MediaUploadManager.parseVideoMessage(content);
      return videoData ? videoData.thumbnailUrl : "";
    },

    // 获取视频时长
    getVideoDuration(content) {
      const videoData = MediaUploadManager.parseVideoMessage(content);
      return videoData ? videoData.duration : 0;
    },

    // 格式化视频时长
    formatVideoDuration(seconds) {
      if (!seconds || seconds < 0) return "0:00";

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
      } else {
        return `${minutes}:${secs.toString().padStart(2, "0")}`;
      }
    },

    // 播放视频（全屏播放器）
    playVideo(content) {
      const videoData = MediaUploadManager.parseVideoMessage(content);
      if (!videoData) return;

      // 创建全屏视频播放器
      const overlay = document.createElement("div");
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: pointer;
      `;

      const video = document.createElement("video");
      video.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        border-radius: 8px;
      `;
      video.controls = true;
      video.autoplay = true;
      video.src = videoData.videoUrl;

      const closeBtn = document.createElement("div");
      closeBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #333;
        cursor: pointer;
      `;
      closeBtn.textContent = "×";

      overlay.appendChild(video);
      overlay.appendChild(closeBtn);
      document.body.appendChild(overlay);

      // 点击关闭
      const closeVideo = () => {
        video.pause();
        document.body.removeChild(overlay);
      };

      closeBtn.addEventListener("click", closeVideo);
      overlay.addEventListener("click", (e) => {
        if (e.target === overlay) {
          closeVideo();
        }
      });

      // ESC键关闭
      const escHandler = (e) => {
        if (e.key === "Escape") {
          closeVideo();
          document.removeEventListener("keydown", escHandler);
        }
      };
      document.addEventListener("keydown", escHandler);
    },
  };

  // 表情管理器
  const EmojiManager = {
    // 常用表情分类
    categories: {
      smileys: {
        name: "笑脸",
        icon: "😀",
        emojis: ["😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃", "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙", "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔"],
      },
      emotions: {
        name: "情感",
        icon: "😢",
        emojis: ["🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢", "🤮", "🤧", "🥵", "🥶", "🥴", "😵", "🤯", "🤠", "🥳", "😎", "🤓", "🧐"],
      },
      gestures: {
        name: "手势",
        icon: "👍",
        emojis: ["👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "🖕", "👇", "☝️", "👋", "🤚", "🖐️", "✋", "🖖", "👏", "🙌", "🤲", "🤝", "🙏", "✍️", "💪", "🦵", "🦶", "👂", "👃"],
      },
      hearts: {
        name: "爱心",
        icon: "❤️",
        emojis: ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟", "♥️"],
      },
      objects: {
        name: "物品",
        icon: "🎉",
        emojis: ["🎉", "🎊", "🎈", "🎁", "🏆", "🥇", "🥈", "🥉", "⭐", "🌟", "💫", "✨", "🔥", "💯", "💢", "💨", "💦", "💤", "🕳️", "💣"],
      },
    },

    // 创建表情选择器HTML
    createEmojiPicker() {
      const categories = this.categories;
      let html = `
        <div class="emoji-picker">
          <div class="emoji-categories">
            ${Object.keys(categories)
              .map(
                (key) =>
                  `<div class="emoji-category-tab" data-category="${key}" title="${categories[key].name}">
                ${categories[key].icon}
              </div>`
              )
              .join("")}
          </div>
          <div class="emoji-content">
            ${Object.keys(categories)
              .map(
                (key) =>
                  `<div class="emoji-category-content" data-category="${key}" style="display: ${key === "smileys" ? "block" : "none"}">
                <div class="emoji-grid">
                  ${categories[key].emojis.map((emoji) => `<span class="emoji-item" data-emoji="${emoji}" title="${emoji}">${emoji}</span>`).join("")}
                </div>
              </div>`
              )
              .join("")}
          </div>
        </div>
      `;
      return html;
    },

    // 初始化表情选择器事件
    initEmojiPicker(container, onEmojiSelect) {
      const picker = container.querySelector(".emoji-picker");
      if (!picker) return;

      // 分类切换事件
      const categoryTabs = picker.querySelectorAll(".emoji-category-tab");
      const categoryContents = picker.querySelectorAll(".emoji-category-content");

      categoryTabs.forEach((tab) => {
        tab.addEventListener("click", () => {
          const category = tab.dataset.category;

          // 切换激活状态
          categoryTabs.forEach((t) => t.classList.remove("active"));
          tab.classList.add("active");

          // 切换内容显示
          categoryContents.forEach((content) => {
            content.style.display = content.dataset.category === category ? "block" : "none";
          });
        });
      });

      // 设置默认激活状态
      if (categoryTabs.length > 0) {
        categoryTabs[0].classList.add("active");
      }

      // 表情点击事件
      const emojiItems = picker.querySelectorAll(".emoji-item");
      emojiItems.forEach((item) => {
        item.addEventListener("click", () => {
          const emoji = item.dataset.emoji;
          if (onEmojiSelect) {
            onEmojiSelect(emoji);
          }
        });
      });
    },

    // 在文本中渲染表情（如果需要自定义渲染）
    renderEmojis(text) {
      // 现代浏览器原生支持emoji，通常不需要特殊处理
      return text;
    },

    // 检查是否为表情字符
    isEmoji(char) {
      const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
      return emojiRegex.test(char);
    },
  };

  // 滚动工具管理器
  const ScrollManager = {
    // 丝滑滚动到聊天区域底部（参考会员端优化逻辑）
    scrollToBottom(selector = ".chat-box", smooth = true) {
      const chatBox = document.querySelector(selector);
      if (!chatBox) {
        console.warn(`[ScrollManager] 未找到滚动容器: ${selector}`);
        return;
      }

      // 判断是否需要滚动（用户是否在底部附近）
      const isNearBottom = chatBox.scrollHeight - chatBox.scrollTop - chatBox.clientHeight < 100;
      if (!isNearBottom && !smooth) return;

      // 使用丝滑的滚动效果
      chatBox.scrollTo({
        top: chatBox.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });

      console.log(`[ScrollManager] 丝滑滚动到底部: ${selector}`);
    },

    // 延迟滚动到底部（等待DOM更新）
    scrollToBottomDelayed(selector = ".chat-box", delay = 100, smooth = true) {
      setTimeout(() => {
        this.scrollToBottom(selector, smooth);
      }, delay);
    },

    // 使用Vue.nextTick滚动到底部（增强版，确保DOM完全渲染）
    scrollToBottomNextTick(selector = ".chat-box", smooth = true) {
      if (typeof Vue !== "undefined" && Vue.nextTick) {
        Vue.nextTick(() => {
          // 第一次滚动
          this.forceScrollToBottom(selector, smooth);
          // 延迟再次滚动，确保DOM完全渲染
          setTimeout(() => {
            this.forceScrollToBottom(selector, smooth);
          }, 100);
        });
      } else {
        // 如果Vue不可用，使用延迟滚动
        this.scrollToBottomDelayed(selector, 50, smooth);
        // 再次延迟滚动
        setTimeout(() => {
          this.forceScrollToBottom(selector, smooth);
        }, 150);
      }
    },

    // 检查是否需要滚动（用户是否在底部附近）
    shouldAutoScroll(selector = ".chat-box", threshold = 100) {
      const chatBox = document.querySelector(selector);
      if (!chatBox) return false;

      const { scrollTop, scrollHeight, clientHeight } = chatBox;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      return distanceFromBottom <= threshold;
    },

    // 智能滚动（只有在用户接近底部时才滚动）
    smartScrollToBottom(selector = ".chat-box", threshold = 100, smooth = true) {
      const chatBox = document.querySelector(selector);
      if (!chatBox) {
        console.warn(`[ScrollManager] 未找到滚动容器: ${selector}`);
        return false;
      }

      // 判断是否需要滚动
      const isNearBottom = chatBox.scrollHeight - chatBox.scrollTop - chatBox.clientHeight < threshold;
      if (isNearBottom) {
        // 使用丝滑的滚动效果
        chatBox.scrollTo({
          top: chatBox.scrollHeight,
          behavior: smooth ? "smooth" : "auto",
        });
        console.log(`[ScrollManager] 智能丝滑滚动到底部: ${selector}`);
        return true;
      }
      return false;
    },

    // 强制滚动到底部（不检查用户位置，总是滚动）
    forceScrollToBottom(selector = ".chat-box", smooth = true) {
      const chatBox = document.querySelector(selector);
      if (!chatBox) {
        console.warn(`[ScrollManager] 未找到滚动容器: ${selector}`);
        return;
      }

      const beforeScroll = {
        scrollTop: chatBox.scrollTop,
        scrollHeight: chatBox.scrollHeight,
        clientHeight: chatBox.clientHeight,
      };

      // 强制使用丝滑的滚动效果
      chatBox.scrollTo({
        top: chatBox.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });

      // 延迟检查滚动结果
      setTimeout(
        () => {
          const afterScroll = {
            scrollTop: chatBox.scrollTop,
            scrollHeight: chatBox.scrollHeight,
            clientHeight: chatBox.clientHeight,
          };
          const isAtBottom = Math.abs(afterScroll.scrollHeight - afterScroll.scrollTop - afterScroll.clientHeight) < 5;
          console.log(`[ScrollManager] 滚动完成 ${selector}:`, {
            before: beforeScroll,
            after: afterScroll,
            isAtBottom: isAtBottom,
          });
        },
        smooth ? 500 : 50
      );
    },

    // 消息加载完成后的滚动（多重检查确保滚动到底部）
    scrollToBottomAfterLoad(selector = ".chat-box", smooth = true) {
      const chatBox = document.querySelector(selector);
      if (!chatBox) {
        console.warn(`[ScrollManager] 未找到滚动容器: ${selector}`);
        return;
      }

      console.log(`[ScrollManager] 开始消息加载后多重滚动: ${selector}`);

      // 立即滚动一次（不平滑，快速定位）
      this.forceScrollToBottom(selector, false);

      // 使用Vue.nextTick后再滚动
      if (typeof Vue !== "undefined" && Vue.nextTick) {
        Vue.nextTick(() => {
          // 第一次平滑滚动
          this.forceScrollToBottom(selector, smooth);

          // 延迟150ms再次滚动，确保所有内容渲染完成
          setTimeout(() => {
            this.forceScrollToBottom(selector, smooth);
          }, 150);

          // 延迟400ms最后一次滚动，确保图片/视频等媒体内容加载完成
          setTimeout(() => {
            this.forceScrollToBottom(selector, smooth);
          }, 400);

          // 延迟800ms终极滚动，处理慢速网络情况
          setTimeout(() => {
            this.forceScrollToBottom(selector, smooth);
          }, 800);
        });
      } else {
        // 如果Vue不可用，使用多次延迟滚动
        setTimeout(() => this.forceScrollToBottom(selector, smooth), 50);
        setTimeout(() => this.forceScrollToBottom(selector, smooth), 200);
        setTimeout(() => this.forceScrollToBottom(selector, smooth), 450);
        setTimeout(() => this.forceScrollToBottom(selector, smooth), 850);
      }
    },
  };

  // 输入预览管理器
  const TypingManager = {
    // 防抖定时器
    typingTimer: null,
    // 当前输入状态
    isTyping: false,
    // 配置
    config: {
      debounceDelay: 300, // 防抖延迟（毫秒）
      stopTimeout: 3000, // 停止输入超时（毫秒）
      contentLimit: 100, // 内容长度限制
    },

    /**
     * 初始化输入监听
     * @param {string} inputSelector - 输入框选择器
     * @param {function} sendCallback - 发送回调函数
     */
    init(inputSelector, sendCallback) {
      const inputElement = document.querySelector(inputSelector);
      if (!inputElement) {
        console.warn("[TypingManager] 输入框未找到:", inputSelector);
        return;
      }

      let debounceTimer = null;

      inputElement.addEventListener("input", (e) => {
        const content = e.target.value;

        // 清除之前的防抖定时器
        clearTimeout(debounceTimer);

        // 防抖处理
        debounceTimer = setTimeout(() => {
          this.handleInput(content, sendCallback);
        }, this.config.debounceDelay);
      });

      // 监听失焦事件，停止输入状态
      inputElement.addEventListener("blur", () => {
        this.stopTyping(sendCallback);
      });

      console.log("[TypingManager] 输入监听初始化完成");
    },

    /**
     * 处理输入变化
     */
    handleInput(content, sendCallback) {
      // 限制内容长度
      if (content.length > this.config.contentLimit) {
        content = content.substring(0, this.config.contentLimit) + "...";
      }

      // 发送输入状态
      if (!this.isTyping && content.trim()) {
        // 开始输入
        this.sendTypingStatus("start", content, sendCallback);
        this.isTyping = true;
      } else if (content.trim()) {
        // 继续输入
        this.sendTypingStatus("typing", content, sendCallback);
      } else {
        // 内容为空，停止输入
        this.stopTyping(sendCallback);
        return;
      }

      // 重置停止定时器
      clearTimeout(this.typingTimer);
      this.typingTimer = setTimeout(() => {
        this.stopTyping(sendCallback);
      }, this.config.stopTimeout);
    },

    /**
     * 发送输入状态
     */
    sendTypingStatus(status, content, sendCallback) {
      if (typeof sendCallback === "function") {
        sendCallback(status, content);
      }
    },

    /**
     * 停止输入状态
     */
    stopTyping(sendCallback) {
      if (this.isTyping) {
        this.sendTypingStatus("stop", "", sendCallback);
        this.isTyping = false;
      }
      clearTimeout(this.typingTimer);
    },

    /**
     * 重置状态
     */
    reset() {
      this.isTyping = false;
      clearTimeout(this.typingTimer);
    },
  };

  // 回调系统工具函数
  const CallbackUtils = {
    /**
     * 检查是否存在指定的回调函数
     * @param {string} eventName 事件名称
     * @returns {boolean} 是否存在回调函数
     */
    hasCallback: function (eventName) {
      const callbackName = eventName + "Callback";
      return typeof global[callbackName] === "function";
    },

    /**
     * 获取所有已定义的回调函数列表
     * @returns {Array} 回调函数名称列表
     */
    getDefinedCallbacks: function () {
      const callbacks = [];
      for (const key in global) {
        if (key.endsWith("Callback") && typeof global[key] === "function") {
          callbacks.push(key);
        }
      }
      return callbacks;
    },

    /**
     * 手动触发回调函数
     * @param {string} eventName 事件名称
     * @param {*} data 传递给回调的数据
     */
    triggerCallback: function (eventName, data) {
      const callbackName = eventName + "Callback";
      if (typeof global[callbackName] === "function") {
        try {
          console.log(`[ChatCore] 手动触发回调: ${callbackName}`);
          global[callbackName](data);
        } catch (error) {
          console.error(`[ChatCore] 回调函数 ${callbackName} 执行错误:`, error);
        }
      } else {
        console.warn(`[ChatCore] 回调函数 ${callbackName} 未定义`);
      }
    },
  };

  // 公开API
  global.ChatCore = {
    connectWS,
    sendMsg,
    on,
    off,
    createGuid,
    recallMessage,
    resendMessage,
    cleanup,
    closeConnection,
    forceReconnect,
    getConnectionState: () => currentConnectionState,
    getQueueStatus: () => ({
      messageQueue: messageQueue.length,
      offlineQueue: offlineMessageQueue.length,
      isProcessing: isProcessingQueue,
    }),
    processOfflineQueue,
    ConnectionState,
    MessageStatus,
    ErrorType,
    ReadManager,
    NotificationManager,
    SoundManager, // 新增声音管理快捷方法
    WebSocketStatusManager, // 新增WebSocket状态管理器
    TransferManager, // 新增转接管理器
    MediaUploadManager, // 新增媒体上传管理器
    EmojiManager, // 新增表情管理器
    ScrollManager, // 新增滚动管理器
    TypingManager, // 新增输入预览管理器
    CallbackUtils, // 新增回调系统工具
    // 暴露WebSocket实例供外部使用
    get ws() {
      return ws;
    },
  };
})(window);
