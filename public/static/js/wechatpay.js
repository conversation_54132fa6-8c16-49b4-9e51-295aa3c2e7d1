var wsCache = new WebStorageCache();
$(function () {
    //console.log('begin');
    login();
    //history.pushState(null, null, 'http://m.yikayi.net/pay/#!/order/orderlist')
    $(".amount-box").on("touchstart", function (event) {
        $("#keyboard").show();
    });
    var kb = $("#keyboard").keyboard({
        onShow: function () {
            console.log("show kw,value");
        },
        onChange: function (val) {
            $("#keyboard-text").html(val);
        },
        onConfirm: function (value) {
            parseFloatvalue = parseFloat(value);
            if (!parseFloatvalue || !value) {
                return false;
            }
            $(".loading").show();
            $("#pay").html("正在<br>支付");
            //开始jsonp
            url = "/member_api/v1/pay/create_order";
            var data = {};
            data.bid = getQueryString('bid');
            data.total_fee = $("#keyboard-text").html();
            ajax({
                    url: url,
                    method: 'POST',
                    data: data,
                    complete: function () {
                        /* 移除loading */
                        $(".loading").hide();
                        //   alert('complete');
                    },
                    success: function (result) {
                        console.log(result);
                        if (result.code == 0) {
                            var options = result.data.options;
                            var bill_no = result.data.bill_no;
                            var obj = JSON.parse(options); //由JSON字符串转换为JSON对象
                            if (obj.pay_url) {
                                redirect(obj.pay_url);
                                return true;
                            }
                            console.log(obj);
                            jsApiCall(obj, bill_no);
                        } else {
                            alert(result.msg);
                            return false;
                        }
                    }
                }
            );
            console.log("onConfirm kw value:" + value);
            return true;
        }
    });
    kb.showKb();
    $("#display").on("touchstart", function (event) {
        event.preventDefault();
        kb.showKb();
    });
});

//init
function WeixinJSBridgeInit(f) {
    (typeof WeixinJSBridge == 'undefined' || (typeof WeixinJSBridge.invoke == 'undefined')) ? setTimeout('WeixinJSBridgeInit(' + f + ')', 200) : f()
}

//默认隐藏分享按钮
WeixinJSBridgeInit(function () {
    WeixinJSBridge.call("hideOptionMenu");
    WeixinJSBridge.call("hideToolbar");
});

//调用微信JS api 支付
function jsApiCall(options, bill_no) {
    console.log(options);
    console.log(options.appId);
    WeixinJSBridgeInit(function () {
        WeixinJSBridge.invoke(
            'getBrandWCPayRequest',
            {
                "appId": options.appId,
                "nonceStr": options.nonceStr,
                "package": options.package,
                "paySign": options.paySign,
                "signType": options.signType,
                "timeStamp": options.timeStamp
            },
            function (res) {
                var last = JSON.stringify(res); //将JSON对象转化为JSON字符
                //alert(last);
                // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回    ok，但并不保证它绝对可靠。
                //WeixinJSBridge.log(res.err_msg);
                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    //setTimeout('reload()', 1000);
                    //return true;
                    // var bill_no = options.package.replace('prepay_id=', '');
                    var bid = getQueryString('bid');
                    var location_url = "/member/pay/pay_success.html?bill_no=" + bill_no + '&appid=' + options.appId + '&bid=' + bid;
                    window.location.href = location_url;
                    return false;
                } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
                    $("#pay").html("确定<br>支付");
                    // alert('您取消了支付');
                } else if (res.err_msg == "get_brand_wcpay_request:fail") {
                    $("#pay").html("重新<br>支付");
                    alert('支付失败:~' + res.err_desc);
                } else {
                    $("#pay").html("确定<br>支付");
                    alert('状态未知~' + res.err_msg);
                }
            })
    });
}


function reload() {
    return window.location.reload();
}

function login() {
    url = "/member_api/v1/member/info/";
    var data = {};
    data.bid = getQueryString('bid');
    data = Zepto.param(data) || jQuery.param(data);
    ajax({
        url: url,
        data: data,
        complete: function () {
            /* 移除loading */
            $(".loading").hide();
            //   alert('complete');
        },
        success: function (result) {
            console.log(result);
            if (result.code == 0) {
                $("#value_blance").html(result.data.money);
                $("#member").show();
                return true;
            } else {
                alert(result.msg);
                return false;
            }
        }
    })
}
