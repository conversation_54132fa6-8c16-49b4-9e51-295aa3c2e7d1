/**
 * 客服SDK - 网页右下角客服入口（重构版）
 * 使用方法：
 * 1. 引入依赖: <script src="/static/js/chatCore.js"></script>
 *              <script src="/static/js/chatMember.js"></script>
 * 2. 引入SDK: <script src="/static/js/kefuSDK.js"></script>
 * 3. 初始化: KefuSDK.init({ bid: 'your_bid', customerId: 'customer_id' });
 */
(function (global) {
  "use strict";

  // 检查依赖
  if (typeof ChatCore === "undefined") {
    console.error("[KefuSDK] 缺少依赖：请先引入 chatCore.js");
    return;
  }

  // SDK配置
  const DEFAULT_CONFIG = {
    // 必需参数
    bid: "", // 商户ID
    customerId: "", // 客户ID
    customerName: "访客", // 客户名称
    avatar: "/static/img/default-avatar.png", // 客户头像

    // 可选参数
    position: "bottom-right", // 位置: bottom-right, bottom-left, top-right, top-left
    theme: "blue", // 主题色: blue, green, orange, red
    title: "在线客服", // 标题
    subtitle: "有什么可以帮您？", // 副标题

    // 样式配置
    zIndex: 9999,
    buttonSize: 60, // 按钮大小
    chatWidth: 380, // 聊天窗口宽度
    chatHeight: 520, // 聊天窗口高度

    // 功能配置
    autoOpen: false, // 是否自动打开
    showUnreadCount: true, // 是否显示未读消息数
    enableSound: true, // 是否启用声音提醒
    enableMinimize: true, // 是否允许最小化
  };

  // 主题配置
  const THEMES = {
    blue: { primary: "#1890ff", secondary: "#40a9ff" },
    green: { primary: "#52c41a", secondary: "#73d13d" },
    orange: { primary: "#fa8c16", secondary: "#ffa940" },
    red: { primary: "#f5222d", secondary: "#ff4d4f" },
  };

  // SDK主类 - 专注于UI绘制和界面管理
  class KefuSDK {
    constructor() {
      this.config = {};
      this.isInitialized = false;
      this.isOpen = false;
      this.unreadCount = 0;
      this.elements = {};
      this.chatVueApp = null; // Vue应用实例
    }

    // 初始化SDK
    init(options = {}) {
      if (this.isInitialized) {
        console.warn("[KefuSDK] SDK已经初始化");
        return;
      }

      // 合并配置
      this.config = Object.assign({}, DEFAULT_CONFIG, options);

      // 验证必需参数
      if (!this.config.bid) {
        throw new Error("[KefuSDK] bid参数是必需的");
      }
      if (!this.config.customerId) {
        throw new Error("[KefuSDK] customerId参数是必需的");
      }

      // 先设置全局配置（特别是挂载点），再创建UI
      this.setupGlobalConfig();

      // 创建UI
      this.createUI();

      // 立即初始化chatMember.js（在SDK环境中）
      this.initChatMemberForSDK();

      // 绑定事件
      this.bindEvents();

      // 标记为已初始化
      this.isInitialized = true;

      console.log("[KefuSDK] 初始化完成", this.config);

      // 自动打开
      if (this.config.autoOpen) {
        setTimeout(() => this.open(), 1000);
      }
    }

    // 设置全局配置供chatMember.js使用
    setupGlobalConfig() {
      // 设置Vue应用挂载点（必须在kefuConfig之前设置）
      window.kefuMountPoint = "#kefu-sdk-app #app";

      // 设置全局kefuConfig，复用chatMember.js的逻辑
      window.kefuConfig = {
        customerId: this.config.customerId,
        customerName: this.config.customerName,
        avatar: this.config.avatar,
        seller: this.config.bid,
        style: {
          box_color: THEMES[this.config.theme]?.primary || THEMES.blue.primary,
        },
      };

      console.log("[KefuSDK] 全局配置已设置:", {
        kefuMountPoint: window.kefuMountPoint,
        kefuConfig: window.kefuConfig,
      });
    }

    // 在SDK环境中初始化chatMember.js
    initChatMemberForSDK() {
      if (typeof window.init !== "function") {
        console.error("[KefuSDK] chatMember.js未正确加载，init函数不存在");
        return;
      }

      console.log("[KefuSDK] 在SDK环境中初始化chatMember.js");

      // 确保挂载点已设置
      if (window.kefuMountPoint !== "#kefu-sdk-app #app") {
        console.error("[KefuSDK] 挂载点设置错误:", window.kefuMountPoint);
        return;
      }

      // 调用chatMember.js的初始化
      window.init();
    }

    // 创建UI
    createUI() {
      this.createStyles();
      this.createButton();
      this.createChatWindow();
    }

    // 创建样式
    createStyles() {
      const theme = THEMES[this.config.theme] || THEMES.blue;
      const position = this.getPositionStyles();

      const styles = `
        .kefu-sdk-button {
          position: fixed;
          ${position.button}
          width: ${this.config.buttonSize}px;
          height: ${this.config.buttonSize}px;
          background: linear-gradient(135deg, ${theme.primary}, ${theme.secondary});
          border-radius: 50%;
          box-shadow: 0 4px 20px rgba(0,0,0,0.15);
          cursor: pointer;
          z-index: ${this.config.zIndex};
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          user-select: none;
        }
        
        .kefu-sdk-button:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 25px rgba(0,0,0,0.2);
        }
        
        .kefu-sdk-button-icon {
          width: 28px;
          height: 28px;
          fill: white;
        }
        
        .kefu-sdk-unread {
          position: absolute;
          top: -5px;
          right: -5px;
          background: #ff4d4f;
          color: white;
          border-radius: 10px;
          min-width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: bold;
        }
        
        .kefu-sdk-chat {
          position: fixed;
          ${position.chat}
          width: ${this.config.chatWidth}px;
          height: ${this.config.chatHeight}px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 8px 40px rgba(0,0,0,0.15);
          z-index: ${this.config.zIndex + 1};
          display: none;
          flex-direction: column;
          overflow: hidden;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .kefu-sdk-chat.open {
          display: flex;
          animation: kefuSlideIn 0.3s ease-out;
        }
        
        @keyframes kefuSlideIn {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        
        .kefu-sdk-header-btn {
          width: 28px;
          height: 28px;
          border: none;
          background: rgba(255,255,255,0.2);
          color: white;
          border-radius: 6px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s;
          margin-left: 5px;
        }
        
        .kefu-sdk-header-btn:hover {
          background: rgba(255,255,255,0.3);
        }
      `;

      const styleEl = document.createElement("style");
      styleEl.textContent = styles;
      document.head.appendChild(styleEl);
    }

    // 获取位置样式
    getPositionStyles() {
      const offset = 20;
      const positions = {
        "bottom-right": {
          button: `bottom: ${offset}px; right: ${offset}px;`,
          chat: `bottom: ${offset + this.config.buttonSize + 10}px; right: ${offset}px;`,
        },
        "bottom-left": {
          button: `bottom: ${offset}px; left: ${offset}px;`,
          chat: `bottom: ${offset + this.config.buttonSize + 10}px; left: ${offset}px;`,
        },
        "top-right": {
          button: `top: ${offset}px; right: ${offset}px;`,
          chat: `top: ${offset + this.config.buttonSize + 10}px; right: ${offset}px;`,
        },
        "top-left": {
          button: `top: ${offset}px; left: ${offset}px;`,
          chat: `top: ${offset + this.config.buttonSize + 10}px; left: ${offset}px;`,
        },
      };

      return positions[this.config.position] || positions["bottom-right"];
    }

    // 创建按钮
    createButton() {
      const button = document.createElement("div");
      button.className = "kefu-sdk-button";
      button.innerHTML = `
        <svg class="kefu-sdk-button-icon" viewBox="0 0 24 24">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
        </svg>
        <div class="kefu-sdk-unread" style="display: none;">0</div>
      `;

      document.body.appendChild(button);
      this.elements.button = button;
    }

    // 创建聊天窗口 - 复用现有的chat.html结构
    createChatWindow() {
      const chat = document.createElement("div");
      chat.className = "kefu-sdk-chat";
      chat.id = "kefu-sdk-app";

      // 引入现有的CSS样式
      const cssLink = document.createElement("link");
      cssLink.rel = "stylesheet";
      cssLink.href = "/static/css/member/kefu/index.css";
      document.head.appendChild(cssLink);

      // 使用与chat.html相同的结构，但简化一些不必要的功能
      chat.innerHTML = `
        <div id="app" style="height: 100%">
          <!-- 头部 -->
          <div class="layui-row chat-header" :style="{background: style.box_color}">
            <div v-if="kefuAvatar" class="layui-col-xs2 chat-header-avatar">
              <img :src="kefuAvatar" class="agent-avatar" />
            </div>
            <div class="layui-col-xs8 chat-header-title">{{ kefuName || '${this.config.title}' }}</div>
            <div class="layui-col-xs2 chat-header-tool">
              <button class="kefu-sdk-header-btn kefu-close" @click="closeChat">×</button>
            </div>
          </div>
          
          <!-- 聊天内容 -->
          <div class="layui-row chat-body">
            <div class="chat-box" ref="chatBox">
              <div v-for="msg in messages" :key="msg.guid || msg.local_id" class="chat-message">
                <div v-if="msg.is_recall == 1" class="system-msg">{{ recallMsgText(msg) }}</div>
                <div v-else-if="msg.is_system || msg.msg_type == 99" class="system-msg">{{ msg.content }}</div>
                <div v-else :class="(msg.from_guid === (kefuConfig && kefuConfig.customerId) || msg.from_type == 1) ? 'msg-row msg-right' : 'msg-row msg-left'">
                  <img class="msg-avatar" :src="msg.from_avatar" />
                  <div class="msg-bubble">
                    <div v-if="isImageMessage(msg.content)" class="msg-content">
                      <img :src="getImageUrl(msg.content)" class="msg-image" @click="previewImage(getImageUrl(msg.content))" alt="图片" />
                    </div>
                    <div v-else class="msg-content" v-html="msg.content.replace(/\\n/g, '<br>')"></div>
                    <div class="msg-info">
                      <span class="msg-time">{{ msg.send_time || '' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 输入区 -->
          <div class="layui-row chat-footer">
            <div class="text-holder">
              <textarea v-model="input" placeholder="请输入内容" @keydown.enter.exact.prevent="sendMessage($event)" ref="messageInput"></textarea>
            </div>
            <div class="send-bar">
              <div class="tool-box">
                <i class="layui-icon layui-icon-picture" @click="openImageUpload" title="图片"></i>
              </div>
              <div class="send-btn-div">
                <input type="button" value="发送" class="send-input" :disabled="!input.trim()" @click="sendMessage($event)" />
              </div>
            </div>
            <input type="file" id="hiddenImageInput" accept="image/*" style="display: none" @change="handleImageSelect" />
          </div>

          <!-- 图片预览遮罩 -->
          <div v-if="previewImageUrl" class="image-preview-overlay" @click="closeImagePreview">
            <img :src="previewImageUrl" class="image-preview-content" @click.stop alt="图片预览" />
          </div>
        </div>
        <audio src="/static/audio/default.wav" style="display: none" id="whisper-index-audio"></audio>
      `;

      document.body.appendChild(chat);
      this.elements.chat = chat;
    }

    // 绑定事件
    bindEvents() {
      // 按钮点击事件
      this.elements.button.addEventListener("click", () => {
        this.toggle();
      });
    }

    // 打开聊天窗口
    open() {
      if (this.isOpen) return;

      this.isOpen = true;
      this.elements.chat.classList.add("open");

      // 检查Vue应用是否已经初始化
      this.checkVueApp();

      // 清除未读计数
      this.setUnreadCount(0);

      console.log("[KefuSDK] 聊天窗口已打开");
    }

    // 关闭聊天窗口
    close() {
      if (!this.isOpen) return;

      this.isOpen = false;
      this.elements.chat.classList.remove("open");

      // 销毁Vue应用
      if (this.chatVueApp) {
        this.chatVueApp.$destroy();
        this.chatVueApp = null;
      }

      console.log("[KefuSDK] 聊天窗口已关闭");
    }

    // 切换聊天窗口
    toggle() {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    }

    // 检查Vue应用状态
    checkVueApp() {
      const sdkAppElement = document.querySelector("#kefu-sdk-app #app");

      if (sdkAppElement && sdkAppElement.__vue_app__) {
        this.chatVueApp = sdkAppElement.__vue_app__;
        console.log("[KefuSDK] ✅ Vue应用已就绪");
        this.validateChatInterface();
      } else if (window.vueApp) {
        this.chatVueApp = window.vueApp;
        console.log("[KefuSDK] ✅ 从全局变量获取Vue应用");
        this.validateChatInterface();
      } else {
        console.warn("[KefuSDK] ⚠️ Vue应用尚未就绪，等待初始化完成");
        // 等待一下再检查
        setTimeout(() => {
          this.checkVueApp();
        }, 500);
      }
    }

    // 初始化Vue应用 - 使用完整版chatMember.js
    initVueApp() {
      // 检查是否已经有Vue应用在SDK容器中
      const sdkAppElement = document.querySelector("#kefu-sdk-app #app");
      if (sdkAppElement && sdkAppElement.__vue_app__) {
        this.chatVueApp = sdkAppElement.__vue_app__;
        console.log("[KefuSDK] SDK容器中已有Vue应用实例");
        return;
      }

      console.log("[KefuSDK] 准备初始化完整版Vue应用到SDK容器");

      // 确保挂载点存在
      if (!sdkAppElement) {
        console.error("[KefuSDK] SDK挂载点不存在，无法初始化Vue应用");
        return;
      }

      // 使用chatMember.js的完整功能
      this.initWithChatMember();
    }

    // 使用chatMember.js初始化完整功能
    initWithChatMember() {
      // 确保chatMember.js已加载
      if (typeof window.init !== "function") {
        console.error("[KefuSDK] chatMember.js未正确加载，init函数不存在");
        return;
      }

      console.log("[KefuSDK] 使用chatMember.js初始化完整聊天功能");

      // 触发chatMember.js初始化
      window.init();

      // 等待Vue应用初始化完成
      setTimeout(() => {
        this.tryGetVueApp();
      }, 2000); // 给足够时间让chatMember.js完成初始化
    }

    // 尝试获取Vue应用实例
    tryGetVueApp() {
      console.log("[KefuSDK] 开始尝试获取Vue应用实例");

      // 首先检查SDK容器中的Vue应用
      const sdkAppElement = document.querySelector("#kefu-sdk-app #app");
      console.log("[KefuSDK] SDK容器元素:", sdkAppElement);

      if (sdkAppElement && sdkAppElement.__vue_app__) {
        this.chatVueApp = sdkAppElement.__vue_app__;
        console.log("[KefuSDK] ✅ 成功获取SDK容器中的Vue应用实例");
        this.validateChatInterface();
        return;
      }

      // 检查全局Vue应用实例
      console.log("[KefuSDK] 检查全局Vue应用实例:", window.vueApp);
      if (window.vueApp) {
        this.chatVueApp = window.vueApp;
        console.log("[KefuSDK] ✅ 从全局变量获取Vue应用实例");
        this.validateChatInterface();
        return;
      }

      // 检查页面中是否有其他Vue应用
      const pageAppElement = document.querySelector("#app");
      console.log("[KefuSDK] 页面应用元素:", pageAppElement);
      if (pageAppElement && pageAppElement.__vue_app__) {
        this.chatVueApp = pageAppElement.__vue_app__;
        console.log("[KefuSDK] ✅ 获取页面中的Vue应用实例");
        this.validateChatInterface();
        return;
      }

      console.error("[KefuSDK] ❌ 未能获取Vue应用实例，chatMember.js可能初始化失败");
      console.log("[KefuSDK] 调试信息:", {
        sdkContainer: !!sdkAppElement,
        globalVueApp: !!window.vueApp,
        pageContainer: !!pageAppElement,
        pageVueApp: !!(pageAppElement && pageAppElement.__vue_app__),
        kefuMountPoint: window.kefuMountPoint,
        initFunction: typeof window.init,
      });

      // 尝试再次初始化
      console.log("[KefuSDK] 尝试再次初始化chatMember.js");
      setTimeout(() => {
        if (typeof window.init === "function") {
          window.init();
          setTimeout(() => {
            const retryElement = document.querySelector("#kefu-sdk-app #app");
            if (retryElement && retryElement.__vue_app__) {
              this.chatVueApp = retryElement.__vue_app__;
              console.log("[KefuSDK] ✅ 重试成功，获取到Vue应用实例");
              this.validateChatInterface();
            } else {
              console.error("[KefuSDK] ❌ 重试失败，请检查chatMember.js是否正确加载");
            }
          }, 1000);
        }
      }, 500);
    }

    // 验证聊天界面是否正确加载
    validateChatInterface() {
      setTimeout(() => {
        const chatBox = document.querySelector("#kefu-sdk-app .chat-box");
        const messageList = document.querySelector("#kefu-sdk-app .message-list");
        const inputArea = document.querySelector("#kefu-sdk-app .input-area");

        console.log("[KefuSDK] 聊天界面验证:", {
          chatBox: !!chatBox,
          messageList: !!messageList,
          inputArea: !!inputArea,
        });

        if (!chatBox) {
          console.warn("[KefuSDK] 聊天界面可能未正确加载");
        }
      }, 500);
    }

    // 设置未读消息数
    setUnreadCount(count) {
      this.unreadCount = Math.max(0, count);
      const unreadEl = this.elements.button.querySelector(".kefu-sdk-unread");

      if (this.unreadCount > 0 && this.config.showUnreadCount) {
        unreadEl.textContent = this.unreadCount > 99 ? "99+" : this.unreadCount;
        unreadEl.style.display = "flex";
      } else {
        unreadEl.style.display = "none";
      }
    }

    // 销毁SDK
    destroy() {
      if (!this.isInitialized) return;

      // 关闭聊天窗口
      this.close();

      // 移除DOM元素
      if (this.elements.button) {
        this.elements.button.remove();
      }
      if (this.elements.chat) {
        this.elements.chat.remove();
      }

      // 重置状态
      this.isInitialized = false;
      this.isOpen = false;
      this.elements = {};

      console.log("[KefuSDK] SDK已销毁");
    }

    // 公共API方法
    show() {
      if (this.elements.button) {
        this.elements.button.style.display = "flex";
      }
    }

    hide() {
      if (this.elements.button) {
        this.elements.button.style.display = "none";
      }
      this.close();
    }

    // 获取未读消息数
    getUnreadCount() {
      return this.unreadCount;
    }
  }

  // 创建全局实例
  const kefuSDK = new KefuSDK();

  // 暴露到全局
  global.KefuSDK = {
    init: (options) => kefuSDK.init(options),
    open: () => kefuSDK.open(),
    close: () => kefuSDK.close(),
    toggle: () => kefuSDK.toggle(),
    show: () => kefuSDK.show(),
    hide: () => kefuSDK.hide(),
    destroy: () => kefuSDK.destroy(),
    getUnreadCount: () => kefuSDK.getUnreadCount(),
  };

  // 暴露实例供内部使用
  global.kefuSDK = kefuSDK;

  console.log("[KefuSDK] SDK已加载");
})(window);
