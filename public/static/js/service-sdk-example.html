<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>纯LayIM客服SDK示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: #1E9FFF;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .service-container {
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 600px;
            background: #f5f5f5;
        }
        .controls {
            margin: 20px 0;
        }
        .btn {
            background: #1E9FFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0078D4;
        }
        .info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>纯LayIM客服SDK示例</h1>
            <p>移除ChatCore依赖，使用LayIM原生通信机制</p>
        </div>

        <div class="info">
            <h3>🎯 重构优势</h3>
            <ul>
                <li>✅ 移除ChatCore.js依赖，避免双重通信</li>
                <li>✅ 使用LayIM原生WebSocket机制</li>
                <li>✅ 简化消息处理逻辑，避免重复监听</li>
                <li>✅ 统一事件处理，减少代码复杂度</li>
                <li>✅ 更好的性能和稳定性</li>
            </ul>
        </div>

        <div class="controls">
            <button class="btn" onclick="initSDK()">初始化SDK</button>
            <button class="btn" onclick="refreshSessions()">刷新会话</button>
            <button class="btn" onclick="destroySDK()">销毁SDK</button>
        </div>

        <div id="service-container" class="service-container"></div>
    </div>

    <!-- 引入LayUI -->
    <script src="/static/js/plugins/layuiadmin/layui/layui.js"></script>
    
    <!-- 引入纯LayIM版本SDK -->
    <script src="/static/js/service-sdk-layim.js"></script>

    <script>
        let serviceSDK = null;

        // 初始化SDK
        async function initSDK() {
            if (serviceSDK) {
                console.log("SDK已经初始化");
                return;
            }

            try {
                serviceSDK = new ServiceSDK({
                    apiUrl: 'http://127.0.0.1',
                    container: '#service-container',
                    width: '100%',
                    height: '600px',
                    kefuGuid: 'c2ddb0e9-a755-0cc6-eeb9-438170b07f19', // 替换为实际的客服GUID
                    bid: '26810245-d97e-81b6-c1cf-0215fd8f347c' // 替换为实际的商户ID
                });

                await serviceSDK.init();
                console.log("SDK初始化成功");
                
                // 显示成功消息
                if (window.layui && window.layui.layer) {
                    layui.layer.msg('SDK初始化成功', { icon: 1 });
                }
            } catch (error) {
                console.error("SDK初始化失败:", error);
                alert("SDK初始化失败: " + error.message);
            }
        }

        // 刷新会话
        async function refreshSessions() {
            if (!serviceSDK) {
                alert("请先初始化SDK");
                return;
            }

            try {
                await serviceSDK.refreshSessions();
                console.log("会话列表刷新成功");
                
                if (window.layui && window.layui.layer) {
                    layui.layer.msg('会话列表已刷新', { icon: 1 });
                }
            } catch (error) {
                console.error("刷新会话失败:", error);
                alert("刷新会话失败: " + error.message);
            }
        }

        // 销毁SDK
        function destroySDK() {
            if (!serviceSDK) {
                alert("SDK未初始化");
                return;
            }

            serviceSDK.destroy();
            serviceSDK = null;
            console.log("SDK已销毁");
            
            if (window.layui && window.layui.layer) {
                layui.layer.msg('SDK已销毁', { icon: 1 });
            }
        }

        // 页面加载完成后自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log("页面加载完成，准备初始化SDK");
            // 延迟初始化，确保LayUI加载完成
            setTimeout(initSDK, 1000);
        });

        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (serviceSDK) {
                serviceSDK.destroy();
            }
        });
    </script>
</body>
</html>
