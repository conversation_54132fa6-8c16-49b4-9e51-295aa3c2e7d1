
.adminj-upload-ul {
    border: dashed 0px #009;
}

.adminj-upload-ul li {
    list-style-type: none;
    float: left;
    display: inline;
    border: dashed 1px #009;
    list-style: none;
    margin: 5px;
    padding: 0px;
}

.adminj-upload-tools-div {
    display: block;
    position: absolute;
    pointer-events: none;
    border: solid coral 0px;
}

.adminj-upload-image-style {
    width: 100px;
    height: 80px;
}

/*
.list li {
    list-style-type: none;
    float: left;
    display: inline;
    border: dotted 1px #009;
    list-style: none;
    margin: 0px;
    padding: 0px;
} */


.layui-form-item {
    margin-bottom: 10px;
    clear: both;
    *zoom: 1;
}

/*.layui-form-radio {*/
/*    line-height: 28px;*/
/*    margin: 6px 0px 0 0;*/
/*    padding-right: 5px;*/
/*    cursor: pointer;*/
/*    font-size: 0;*/
/*}*/

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #337ab7;
    border: 1px solid #1d6fa5;
    color: #fff;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 3px 5px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: rgba(255, 255, 255, .7);
    float: right;
    margin-left: 5px;
    margin-right: -2px;
}

.select2-container .select2-search--inline {
    float: left;
    border: none;
}

.select2-container--default .select2-selection--multiple {
    background-color: white;
    border: 1px solid #eee;
    border-radius: 4px;
    cursor: text;
    min-height: 38px;
}

/**框架CSS START**/
.adminj-sort-item {
}

.adminj-propertiesdiv-sp {
    font-size: 12px;
    color: #6e7479;
    margin-top: 10px;
    margin-bottom: 0px;
}

.adminj-propertiesdiv-sp input {
    height: 30px;
}

.adminj-left-darg-button {
    width: 93%;
    cursor: move;
    border: dashed 1px #c7ced5;
    margin-top: 3px;
    height: 30px;
    line-height: 30px;
}

.adminj-mouse-hover-color {
    border: dashed 1px cornflowerblue;
}

.adminj-select-sort-item {
    position: relative;
    border: dashed 1px cornflowerblue;
    /*pointer-events: none;*/
}

.adminj-group-layout-border {
    border: dashed 1px #c7ced5;
    min-height: 38px;
}

.adminj-item-bottom-right-tools {
    position: absolute;
    height: 23px;
    line-height: 23px;
    background: #1d6fa5;
    right: 0;
    bottom: 0;
    cursor: pointer;
    z-index: 21;
    color: #fff;
}

.adminj-item-top-left-tools {
    position: absolute;
    line-height: 20px;
    background: #1d6fa5;
    left: 0;
    top: 0;
    cursor: pointer;
    z-index: 20;
    color: #fff;
    font-size: 11px;
}


.adminj-ds-left {
    width: 250px;
    padding-left: 5px;
    list-style-type: none;
    float: left;
    display: inline;
    border: dashed 0px #009;
    list-style: none;
    margin: 5px;
    padding: 0px;
    position: absolute;
    top: 36px;
}

.adminj-ds-center {
    margin-top: 5px;
    float: left;
    border: dashed cadetblue;
    padding: 3px;
    list-style-type: none;
    left: 260px;
    right: 275px;
    min-height: 800px;
    position: absolute;
    top: 36px;
}

.adminj-ds-right {
    float: left;
    width: 260px;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    min-height: 500px;
    list-style-type: none;
    right: 0px;
    position: absolute;
    top: 36px;
}

.adminj-ds-top-right {
    margin-top: 5px;
    width: 100%;
    text-align: right;
    position: absolute;
    right: 270px;
}

.adminj-ds-top-left {
    float: left;
    padding-left: 20px;
    line-height: 36px;
    position: absolute;
    font-weight: bold;
    font-size: 15px;
}