<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8" />
	<script type="text/javascript" src="layui/layui.all.js"></script>
	<link rel="stylesheet" href="layui/css/layui.css">
	<script type="text/javascript" src="tableSelect.js"></script>
	<title>TableSelect</title>
</head>

<body>
	<form class="layui-form" action="" style="padding:20px;">
		<div class="layui-form-item">
			<label class="layui-form-label">多选</label>
			<div class="layui-input-inline">
				<input type="text" name="" placeholder="请输入" autocomplete="off" class="layui-input" id="demo" value="刘晓军,张恒" ts-selected="002,003">
			</div>
			<div class="layui-form-mid layui-word-aux">本地演示数据，分页选中和其他页一样，这不是BUG</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">单选</label>
			<div class="layui-input-inline">
				<input type="text" name="" placeholder="请输入" autocomplete="off" class="layui-input" id="demo2">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">使用</label>
			<div class="layui-input-block">
<pre class="layui-code">
//开始使用
var tableSelect = layui.tableSelect;
tableSelect.render({
	elem: '#demo',	//定义输入框input对象 必填
	checkedKey: 'id', //表格的唯一建值，非常重要，影响到选中状态 必填
	searchKey: 'keyword',	//搜索输入框的name值 默认keyword
	searchPlaceholder: '关键词搜索',	//搜索输入框的提示文字 默认关键词搜索
	table: {	//定义表格参数，与LAYUI的TABLE模块一致，只是无需再定义表格elem
		url:'',
		cols: [[]]
	},
	done: function (elem, data) {
	//选择完后的回调，包含2个返回值 elem:返回之前input对象；data:表格返回的选中的数据 []
	//拿到data[]后 就按照业务需求做想做的事情啦~比如加个隐藏域放ID...
	}
})
//默认值
只需要在触发input上添加 ts-selected="1,2,3" 属性即可 值需与checkedKey对应
</pre>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">更新日志</label>
			<div class="layui-input-block">
				<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
					<legend>更新时间线</legend>
				</fieldset>
				<ul class="layui-timeline">
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis"></i>
						<div class="layui-timeline-content layui-text">
							<div class="layui-timeline-title">10/18 Add 记住选中 / 赋默认选中值</div>
						</div>
					</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis"></i>
						<div class="layui-timeline-content layui-text">
							<div class="layui-timeline-title">10/17 Add 多选分页选中</div>
						</div>
					</li>
					<li class="layui-timeline-item">
							<i class="layui-icon layui-timeline-axis"></i>
							<div class="layui-timeline-content layui-text">
								<div class="layui-timeline-title">10/08 Fix z-index / 边缘位置控制</div>
							</div>
						</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis"></i>
						<div class="layui-timeline-content layui-text">
							<div class="layui-timeline-title">09/01 Add 搜索控制 / Add 双击选择</div>
						</div>
					</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis"></i>
						<div class="layui-timeline-content layui-text">
							<div class="layui-timeline-title">08/31 完成初始版本</div>
						</div>
					</li>
					<li class="layui-timeline-item">
						<i class="layui-icon layui-timeline-axis"></i>
						<div class="layui-timeline-content layui-text">
							<div class="layui-timeline-title">嗯~有个理念~开始写代码</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
	</form>
</body>
<script type="text/javascript">
	var form = layui.form;
		form.render();
	var tableSelect = layui.tableSelect;

	tableSelect.render({
		elem: '#demo',
		searchKey: 'my',
		checkedKey: 'id',
		searchPlaceholder: '自定义文字和name',
		table: {
			url: 'table.json',
			cols: [[
				{ type: 'checkbox' },
				{ field: 'id', title: 'ID', width: 100 },
				{ field: 'username', title: '姓名', width: 300 },
				{ field: 'sex', title: '性别', width: 100 }
			]]
		},
		done: function (elem, data) {
			var NEWJSON = []
			layui.each(data.data, function (index, item) {
				NEWJSON.push(item.username)
			})
			elem.val(NEWJSON.join(","))
		}
	})


	tableSelect.render({
		elem: '#demo2',
		checkedKey: 'id',
		table: {
			url: 'table.json',
			cols: [[
				{ type: 'radio' },
				{ field: 'id', title: 'ID' },
				{ field: 'username', title: '姓名' },
				{ field: 'sex', title: '性别' }
			]]
		},
		done: function (elem, data) {
			var NEWJSON = []
			layui.each(data.data, function (index, item) {
				NEWJSON.push(item.username)
			})
			elem.val(NEWJSON.join(","))
		}
	})


</script>

</html>