/**
 * 纯LayIM客服SDK - 重构版本
 * 版本: 2.0.0
 * 特性: 移除ChatCore依赖，使用LayIM原生通信机制
 */

(function (window, document) {
  "use strict";

  class ServiceSDK {
    constructor(config = {}) {
      // 默认配置
      this.config = {
        apiUrl: config.apiUrl || "http://127.0.0.1",
        container: config.container || "#service-container",
        width: config.width || "100%",
        height: config.height || "600px",
        kefuGuid: config.kefuGuid || "",
        bid: config.bid || "",
        ...config,
      };

      // 状态管理
      this.initialized = false;
      this.layimInstance = null;
      this.shadowRoot = null;
      this.sessionList = [];
      this.loadedHistorySessions = []; // 已加载历史消息的会话

      // 用户信息
      this.user = {
        guid: this.config.kefuGuid,
        name: "客服",
        avatar: "/static/img/service.png",
        bid: this.config.bid,
      };

      console.log("[ServiceSDK] 纯LayIM版本初始化完成", this.config);
    }

    /**
     * 初始化SDK
     */
    async init() {
      if (this.initialized) {
        console.warn("[ServiceSDK] 已经初始化过了");
        return;
      }

      try {
        // 1. 设置容器
        this.setupContainer();

        // 2. 加载LayIM依赖
        await this.loadDependencies();

        // 3. 初始化LayIM
        await this.initLayIM();

        // 4. 初始化WebSocket连接
        await this.initWebSocket();

        this.initialized = true;
        console.log("[ServiceSDK] 初始化完成");
      } catch (error) {
        console.error("[ServiceSDK] 初始化失败:", error);
        throw error;
      }
    }

    /**
     * 设置容器
     */
    setupContainer() {
      const containerElement = typeof this.config.container === "string" ? document.querySelector(this.config.container) : this.config.container;

      if (!containerElement) {
        throw new Error(`容器元素未找到: ${this.config.container}`);
      }

      this.container = containerElement;

      // 设置容器样式
      this.container.style.width = this.config.width;
      this.container.style.height = this.config.height;
      this.container.innerHTML = '<div id="layim-container"></div>';

      console.log("[ServiceSDK] 容器设置完成");
    }

    /**
     * 加载LayIM依赖
     */
    async loadDependencies() {
      return new Promise((resolve, reject) => {
        // 检查LayUI是否已加载
        if (!window.layui) {
          // 使用与主页面相同的LayUI CDN路径
          this.loadScript("//file.yikayi.net/static/js/plugins/layuiadmin/layui/layui.js?v=2.10.3")
            .then(() => this.loadLayIM())
            .then(resolve)
            .catch(reject);
        } else {
          this.loadLayIM().then(resolve).catch(reject);
        }
      });
    }

    /**
     * 加载LayIM
     */
    loadLayIM() {
      return new Promise((resolve) => {
        // 检查LayIM模块是否已存在
        if (window.layui && window.layui.cache && window.layui.cache.modules && window.layui.cache.modules.layim) {
          console.log("[ServiceSDK] LayIM模块已存在，直接使用");
          resolve();
          return;
        }

        // 按照LayIM官方文档的标准方式配置
        window.layui
          .config({
            layimPath: "/static/layim/dist/", // 配置 layim.js 所在目录
            layimResPath: "/static/layim/dist/res/", // layim 资源文件所在目录
            version: "20250426",
          })
          .extend({
            layim: window.layui.cache.layimPath + "layim", // 配置 layim 组件所在的路径
          })
          .use("layim", function () {
            console.log("[ServiceSDK] LayIM模块加载成功");
            resolve();
          });
      });
    }

    /**
     * 初始化LayIM
     */
    async initLayIM() {
      const layim = window.layui.layim;

      // 获取会话数据
      const initData = await this.loadSessionsForLayIM();

      // 配置LayIM
      layim.config({
        init: initData,
        // 上传配置 - 暂时禁用以避免工具栏冲突
        // uploadImage: {
        //   url: "/admin_api/v1/file/upload",
        //   type: "post",
        // },
        // uploadFile: {
        //   url: "/admin_api/v1/file/upload",
        //   type: "post",
        // },
        // 功能配置
        isAudio: false,
        isVideo: false,
        notice: true,
        voice: false,
        copyright: false,
        title: "客服工作台",
        // 设置 iframe 页面地址
        pageurl: {
          // 消息盒子页面地址。若不开启，剔除该项即可
          msgbox: window.layui.cache.layimResPath + "html/msgbox.html",
          // 发现页面地址。若不开启，剔除该项即可
          find: window.layui.cache.layimResPath + "html/find.html",
          // 通过函数设置聊天记录页面地址。若不开启，剔除该项即可
          chatlog: function (data) {
            var receiver = data.receiver; // 当前聊天对象
            // 设置基础路径，此处以默认模板为例
            var url = window.layui.cache.layimResPath + "html/chatlog.html";
            return url + "?type=" + receiver.type + "&id=" + receiver.id;
          },
        },
        // 自定义工具栏
        tool: [
          {
            alias: "transfer",
            title: "转接会话",
            icon: "&#xe612;",
          },
          {
            alias: "close",
            title: "结束会话",
            icon: "&#x1006;",
          },
          {
            alias: "quickReply",
            title: "快捷回复",
            icon: "&#xe611;",
          },
        ],
      });

      // 先设置layimInstance
      this.layimInstance = layim;

      // 设置事件监听
      this.setupLayIMEvents(layim);

      // 注意：不使用extendChatTools，因为当前LayIM版本不支持
      console.log("[ServiceSDK] 使用传统工具栏配置");

      // 添加CSS样式来隐藏重复的文件上传元素
      this.addCustomStyles();

      console.log("[ServiceSDK] LayIM初始化完成");
    }

    /**
     * 添加自定义样式
     */
    addCustomStyles() {
      // 创建样式元素
      const style = document.createElement("style");
      style.innerHTML = `
        /* 隐藏LayIM默认的文件上传工具 */
        .layim-chat-tool span[layim-event="file"],
        .layim-chat-tool span[layim-event="image"],
        .layim-chat-tool .layim-tool-image,
        .layim-chat-tool .layim-tool-file {
          display: none !important;
        }
        /* 隐藏文件上传相关的input元素 */
        .layim-chat-tool input[type="file"] {
          display: none !important;
        }
        /* 隐藏可能的重复文件选择元素 */
        .layim-chat-tool span:not([title]):not([layim-event]) {
          display: none !important;
        }

        /* 聊天消息布局优化 */
        .layim-chat-main {
          padding: 10px !important;
        }

        /* 控制聊天图片大小 */
        .layim-chat-text img {
          max-width: 200px !important;
          max-height: 200px !important;
          border-radius: 4px;
          cursor: pointer;
        }

        /* 我的消息样式（右对齐） */
        .layim-chat-main .layim-chat-role-user {
          padding-left: 0 !important;
          padding-right: 52px !important;
          text-align: right;
        }

        .layim-chat-main .layim-chat-role-user .layim-chat-userinfo {
          left: auto !important;
          right: 3px !important;
        }

        .layim-chat-main .layim-chat-role-user .layim-chat-text {
          background-color: #5FB878 !important;
          color: white !important;
        }

        .layim-chat-main .layim-chat-role-user .layim-chat-text:after {
          left: auto !important;
          right: -9px !important;
          border-left-color: #5FB878 !important;
          border-right-color: transparent !important;
        }
      `;

      // 添加到head
      document.head.appendChild(style);
      console.log("[ServiceSDK] 自定义样式已添加");
    }

    // setupTraditionalToolEvents方法已移除，工具栏事件在setupLayIMEvents中处理

    /**
     * 设置LayIM事件监听
     */
    setupLayIMEvents(layim) {
      // 发送消息事件
      layim.on("sendMessage", (data) => {
        console.log("[ServiceSDK] LayIM发送消息事件:", data);
        console.log("[ServiceSDK] 完整数据结构:", JSON.stringify(data, null, 2));

        // 根据实际的LayIM数据结构获取消息内容和接收方
        let content = "";
        let sessionGuid = "";

        // 尝试不同的数据结构
        if (data.mine && data.mine.content) {
          content = data.mine.content;
        } else if (data.user && data.user.content) {
          content = data.user.content;
        } else if (data.content) {
          content = data.content;
        }

        if (data.to && data.to.id) {
          sessionGuid = data.to.id;
        } else if (data.receiver && data.receiver.id) {
          sessionGuid = data.receiver.id;
        } else if (data.id) {
          sessionGuid = data.id;
        }

        console.log("[ServiceSDK] 解析出的消息内容:", content);
        console.log("[ServiceSDK] 解析出的会话ID:", sessionGuid);

        if (!content) {
          console.error("[ServiceSDK] 无法获取消息内容");
          return;
        }

        if (!sessionGuid) {
          console.error("[ServiceSDK] 无法获取会话ID");
          return;
        }

        this.sendMessage(content, sessionGuid);
      });

      // 会话切换事件 - 支持多种事件名
      const chatEvents = ["chatChange", "chat", "tabChat", "openChat"];
      chatEvents.forEach((eventName) => {
        layim.on(eventName, (res) => {
          console.log(`[ServiceSDK] ${eventName}事件:`, res);
          if (res && res.data && res.data.id) {
            this.switchSession(res.data.id);
          }
        });
      });

      // 工具栏事件（传统方式，兼容旧版LayIM）
      layim.on("tool(transfer)", (obj) => {
        this.handleTransferSession(obj);
      });

      layim.on("tool(close)", (obj) => {
        this.handleCloseSession(obj);
      });

      layim.on("tool(quickReply)", (obj) => {
        this.handleQuickReply(obj);
      });

      // LayIM准备就绪
      layim.on("ready", () => {
        console.log("[ServiceSDK] LayIM准备就绪");
        this.onLayIMReady();
      });
    }

    /**
     * LayIM准备就绪回调
     */
    onLayIMReady() {
      // 可以在这里添加初始化完成后的逻辑
      console.log("[ServiceSDK] 客服系统准备就绪");
    }

    /**
     * 初始化WebSocket连接
     */
    async initWebSocket() {
      if (!window.ChatCore) {
        console.error("[ServiceSDK] ChatCore未加载，无法建立WebSocket连接");
        this.showNotification("WebSocket连接失败：ChatCore未加载", "error");
        return;
      }

      return new Promise((resolve, reject) => {
        try {
          console.log("[ServiceSDK] 开始建立WebSocket连接...");

          // 建立WebSocket连接
          window.ChatCore.connectWS(
            // onOpen回调
            () => {
              console.log("[ServiceSDK] WebSocket连接成功");
              this.showNotification("WebSocket连接成功", "success");

              // 发送客服初始化消息
              window.ChatCore.sendMsg("kefuInit", {
                kefu_guid: this.user.guid,
                name: this.user.name,
                avatar: this.user.avatar,
                bid: this.user.bid,
              })
                .then(() => {
                  console.log("[ServiceSDK] 客服初始化消息发送成功");
                })
                .catch((error) => {
                  console.error("[ServiceSDK] 客服初始化消息发送失败:", error);
                });

              // 设置WebSocket事件监听
              this.setupWebSocketEvents();

              resolve();
            },
            // onClose回调
            () => {
              console.log("[ServiceSDK] WebSocket连接关闭");
              this.showNotification("WebSocket连接已关闭", "error");
            },
            // onError回调
            (error) => {
              console.error("[ServiceSDK] WebSocket连接错误:", error);
              this.showNotification("WebSocket连接错误", "error");
              reject(error);
            }
          );
        } catch (error) {
          console.error("[ServiceSDK] WebSocket初始化失败:", error);
          this.showNotification("WebSocket初始化失败", "error");
          reject(error);
        }
      });
    }

    /**
     * 设置WebSocket事件监听
     */
    setupWebSocketEvents() {
      if (!window.ChatCore) return;

      // 监听新消息
      window.ChatCore.on("chatMessage", (msg) => {
        console.log("[ServiceSDK] 收到WebSocket消息:", msg);
        this.handleWebSocketMessage(msg);
      });

      // 监听会话更新
      window.ChatCore.on("sessionUpdate", (msg) => {
        console.log("[ServiceSDK] 会话更新:", msg);
        this.refreshSessions();
      });

      // 监听新会话
      window.ChatCore.on("newSession", (msg) => {
        console.log("[ServiceSDK] 新会话:", msg);
        this.refreshSessions();
      });

      console.log("[ServiceSDK] WebSocket事件监听已设置");
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(msg) {
      if (!msg.data || !this.layimInstance) return;

      const messageData = msg.data;

      // 只处理访客发送的消息（from_type === 1）
      if (messageData.from_type !== 1) {
        console.log("[ServiceSDK] 忽略非访客消息:", messageData);
        return;
      }

      // 转换为LayIM消息格式
      const layimMessage = {
        username: messageData.from_name || "访客",
        avatar: messageData.from_avatar || "/static/img/default_head.png",
        id: messageData.session_guid, // 使用会话ID作为聊天对象ID
        type: "friend",
        content: messageData.content,
        timestamp: messageData.send_time ? new Date(messageData.send_time).getTime() : new Date().getTime(),
        mine: false, // 访客消息不是当前用户发送的
      };

      console.log("[ServiceSDK] 通过LayIM显示新消息:", layimMessage);

      // 通过LayIM显示消息
      if (this.layimInstance) {
        this.layimInstance.getMessage(layimMessage);
      } else {
        console.warn("[ServiceSDK] LayIM实例未初始化，无法显示消息");
      }
    }

    /**
     * 加载会话数据供LayIM使用
     */
    async loadSessionsForLayIM() {
      try {
        const response = await this.ajax("/admin_api/v1/kefu/chat_list", {
          method: "POST",
        });

        console.log("[ServiceSDK] 会话列表API响应:", response);
        console.log("[ServiceSDK] 响应数据详情:", JSON.stringify(response, null, 2));

        if (response.code === 0 && response.data) {
          // 根据实际API结构解析数据：{session_list: [], queue_list: []}
          let sessionData = [];

          // 合并已接入会话和待接入会话
          if (response.data.session_list && Array.isArray(response.data.session_list)) {
            sessionData = sessionData.concat(response.data.session_list);
            console.log("[ServiceSDK] 找到已接入会话:", response.data.session_list.length, "个");
          }

          if (response.data.queue_list && Array.isArray(response.data.queue_list)) {
            sessionData = sessionData.concat(response.data.queue_list);
            console.log("[ServiceSDK] 找到待接入会话:", response.data.queue_list.length, "个");
          }

          this.sessionList = sessionData;
          console.log("[ServiceSDK] 最终解析到的会话数据:", this.sessionList.length, "个会话");
          console.log("[ServiceSDK] 会话详情:", this.sessionList);

          // 转换为LayIM格式
          const friends = this.sessionList.map((session) => ({
            username: session.member_name || "访客",
            id: session.guid,
            avatar: session.member_avatar || "/static/img/default_head.png",
            sign: session.last_message || "暂无消息",
            status: session.status === 0 ? "offline" : "online", // 0=待接入(离线), 1=进行中(在线)
          }));

          return {
            mine: {
              username: this.user.name || "客服",
              id: this.user.guid,
              status: "online",
              sign: "为您服务",
              avatar: this.user.avatar || "/static/img/service.png",
            },
            friend: [
              {
                groupname: "客服会话",
                id: 1,
                list: friends,
              },
            ],
            group: [],
          };
        } else {
          console.warn("[ServiceSDK] 会话列表数据格式异常:", response);
          console.warn("[ServiceSDK] 使用空数据初始化LayIM");
          this.sessionList = [];
          return this.getEmptyInitData();
        }
      } catch (error) {
        console.error("[ServiceSDK] 会话列表加载错误:", error);
        return this.getEmptyInitData();
      }
    }

    /**
     * 获取空的初始化数据
     */
    getEmptyInitData() {
      return {
        mine: {
          username: this.user.name || "客服",
          id: this.user.guid || "kefu-001",
          status: "online",
          sign: "为您服务",
          avatar: this.user.avatar || "/static/img/service.png",
        },
        friend: [
          {
            groupname: "客服会话",
            id: 1,
            list: [],
          },
        ],
        group: [],
      };
    }

    /**
     * 切换会话
     */
    switchSession(sessionGuid) {
      console.log("[ServiceSDK] 切换会话:", sessionGuid);

      // 检查是否已经加载过历史消息
      if (this.loadedHistorySessions.includes(sessionGuid)) {
        console.log("[ServiceSDK] 会话历史消息已加载，跳过重复加载");
        return;
      }

      // 加载历史消息
      this.loadChatHistory(sessionGuid)
        .then((messages) => {
          console.log("[ServiceSDK] 历史消息加载完成:", messages);

          if (messages && messages.length > 0 && this.layimInstance) {
            messages.forEach((msg) => {
              this.layimInstance.getMessage(msg);
            });
          }

          // 标记已加载
          this.loadedHistorySessions.push(sessionGuid);
        })
        .catch((err) => {
          console.error("[ServiceSDK] 历史消息加载失败:", err);
        });
    }

    /**
     * 加载聊天历史记录
     */
    async loadChatHistory(sessionGuid) {
      try {
        const response = await this.ajax("/admin_api/v1/kefu/message_list", {
          method: "POST",
          data: {
            session_guid: sessionGuid,
            page: 1,
            limit: 50,
          },
        });

        console.log("[ServiceSDK] 历史消息API响应:", response);
        console.log("[ServiceSDK] 历史消息数据详情:", JSON.stringify(response, null, 2));

        if (response.code === 0 && response.data && response.data.data) {
          console.log("[ServiceSDK] 找到历史消息数据:", response.data.data.length, "条消息");
          return response.data.data.map((msg) => {
            const isMyMessage = msg.from_type === 2; // 客服发送的消息

            return {
              username: msg.from_name || (msg.from_type === 1 ? "访客" : "客服"),
              avatar: msg.from_avatar || "/static/img/default_head.png",
              id: isMyMessage ? this.user.guid : sessionGuid,
              type: "friend",
              content: msg.content,
              timestamp: new Date(msg.create_time || msg.send_time).getTime(),
              mine: isMyMessage, // 明确标识是否是当前用户的消息
            };
          });
        } else {
          console.warn("[ServiceSDK] 历史消息数据格式异常或为空:", response);
          return [];
        }
      } catch (error) {
        console.error("[ServiceSDK] 历史消息加载错误:", error);
        return [];
      }
    }

    /**
     * 发送消息 - 使用WebSocket
     */
    sendMessage(content, sessionGuid) {
      console.log("[ServiceSDK] 发送消息:", { content, sessionGuid });
      console.log("[ServiceSDK] 当前会话列表:", this.sessionList);

      // 找到对应的会话
      const session = this.sessionList.find((s) => s.guid === sessionGuid);
      if (!session) {
        console.error("[ServiceSDK] 未找到会话:", sessionGuid);
        console.error(
          "[ServiceSDK] 可用会话ID列表:",
          this.sessionList.map((s) => s.guid)
        );
        return;
      }

      console.log("[ServiceSDK] 找到会话:", session);

      if (!window.ChatCore) {
        console.error("[ServiceSDK] ChatCore未加载，无法发送消息");
        this.showNotification("消息发送失败：WebSocket未连接", "error");
        return;
      }

      // 通过WebSocket发送消息 - 使用resendMessage方法
      const messageData = {
        session_guid: sessionGuid,
        content: content,
        from_type: 2, // 客服发送
        from_guid: this.user.guid,
        from_name: this.user.name,
        from_avatar: this.user.avatar,
        to_guid: session.member_guid,
        to_name: session.member_name || "访客",
        to_avatar: session.member_avatar || "/static/img/default_head.png",
        bid: session.bid || this.user.bid, // 使用会话的bid
        local_id: window.ChatCore ? window.ChatCore.createGuid() : Date.now().toString(),
      };

      console.log("[ServiceSDK] 发送消息数据:", messageData);
      console.log("[ServiceSDK] ChatCore可用性:", !!window.ChatCore);

      // 检查必要字段
      if (!messageData.session_guid) {
        console.error("[ServiceSDK] 缺少session_guid");
        return;
      }
      if (!messageData.to_guid) {
        console.error("[ServiceSDK] 缺少to_guid");
        return;
      }

      // 使用resendMessage方法，它有重试机制和回执监听
      window.ChatCore.resendMessage(messageData)
        .then((response) => {
          console.log("[ServiceSDK] 消息发送成功，收到回执:", response);
          this.showNotification("消息发送成功", "success");
        })
        .catch((error) => {
          console.error("[ServiceSDK] 消息发送失败:", error);
          console.error("[ServiceSDK] 错误详情:", error.message, error.stack);
          this.showNotification("消息发送失败: " + error.message, "error");
        });
    }

    /**
     * 处理转接会话
     */
    handleTransferSession(obj) {
      console.log("[ServiceSDK] 转接会话:", obj);

      const receiver = obj.data;
      if (!receiver || !receiver.id) {
        this.showNotification("请先选择要转接的会话", "error");
        return;
      }

      // 简单的转接对话框
      const targetKefu = prompt("请输入要转接的客服ID:");
      if (targetKefu) {
        this.ajax("/admin_api/v1/kefu/transfer_session", {
          method: "POST",
          data: {
            session_guid: receiver.id,
            from_user_guid: this.user.guid,
            to_user_guid: targetKefu,
            transfer_reason: "客服主动转接",
            bid: this.user.bid,
          },
        })
          .then((response) => {
            console.log("[ServiceSDK] 转接成功:", response);
            this.showNotification("转接成功", "success");
          })
          .catch((error) => {
            console.error("[ServiceSDK] 转接失败:", error);
            this.showNotification("转接失败", "error");
          });
      }
    }

    /**
     * 处理结束会话
     */
    handleCloseSession(obj) {
      console.log("[ServiceSDK] 结束会话:", obj);

      const receiver = obj.data;
      if (!receiver || !receiver.id) {
        this.showNotification("请先选择要结束的会话", "error");
        return;
      }

      if (confirm("确定要结束当前会话吗？")) {
        this.ajax("/admin_api/v1/kefu/close_session", {
          method: "POST",
          data: {
            session_guid: receiver.id,
            kefu_guid: this.user.guid,
            bid: this.user.bid,
          },
        })
          .then((response) => {
            console.log("[ServiceSDK] 会话结束成功:", response);
            this.showNotification("会话已结束", "success");
          })
          .catch((error) => {
            console.error("[ServiceSDK] 会话结束失败:", error);
            this.showNotification("会话结束失败", "error");
          });
      }
    }

    /**
     * 处理快捷回复
     */
    handleQuickReply(obj) {
      console.log("[ServiceSDK] 快捷回复:", obj);

      // 简单的快捷回复选项
      const quickReplies = ["您好，有什么可以帮助您的吗？", "请稍等，我为您查询一下。", "感谢您的咨询，还有其他问题吗？", "祝您生活愉快！"];

      let replyHtml = "<div style='padding: 10px;'>";
      replyHtml += "<h3>选择快捷回复:</h3>";
      quickReplies.forEach((reply) => {
        replyHtml += `<p><a href="javascript:;" onclick="ServiceSDK.insertQuickReply('${reply}')" style="color: #1E9FFF; text-decoration: none;">${reply}</a></p>`;
      });
      replyHtml += "</div>";

      window.layui.layer.open({
        type: 1,
        title: "快捷回复",
        content: replyHtml,
        area: ["400px", "300px"],
      });
    }

    /**
     * 插入快捷回复到输入框
     */
    insertQuickReply(text) {
      // 找到当前活跃的聊天输入框
      const textarea = document.querySelector(".layim-chat-textarea textarea");
      if (textarea) {
        textarea.value = text;
        textarea.focus();
      }
      window.layui.layer.closeAll();
    }

    /**
     * AJAX请求工具
     */
    ajax(url, options = {}) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        const method = options.method || "POST";
        const data = options.data || null;

        xhr.open(method, url, true);
        xhr.setRequestHeader("Content-Type", "application/json");

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText);
                resolve(response);
              } catch (e) {
                reject(new Error(`解析响应失败: ${e.message}`));
              }
            } else {
              reject(new Error(`请求失败: ${xhr.status} ${xhr.statusText}`));
            }
          }
        };

        xhr.onerror = () => reject(new Error("网络错误"));

        if (data) {
          xhr.send(JSON.stringify(data));
        } else {
          xhr.send();
        }
      });
    }

    /**
     * 加载脚本文件
     */
    loadScript(src) {
      return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }

    /**
     * 显示通知
     */
    showNotification(message, type = "info") {
      if (window.layui && window.layui.layer) {
        const icon = type === "success" ? 1 : type === "error" ? 2 : 0;
        window.layui.layer.msg(message, { icon: icon });
      } else {
        alert(message);
      }
    }

    /**
     * 刷新会话列表
     */
    async refreshSessions() {
      try {
        const initData = await this.loadSessionsForLayIM();
        if (this.layimInstance) {
          // 重新配置LayIM数据
          this.layimInstance.config({ init: initData });
          console.log("[ServiceSDK] 会话列表已刷新");
        }
      } catch (error) {
        console.error("[ServiceSDK] 刷新会话列表失败:", error);
      }
    }

    // 图片上传、文件上传、表情选择功能暂时移除
    // 这些功能需要LayIM的extendChatTools支持，当前版本不支持

    /**
     * 显示通知
     */
    showNotification(message, type = "info") {
      if (window.layui && window.layui.layer) {
        const iconMap = {
          success: 1,
          error: 2,
          warning: 3,
          info: 0,
        };
        window.layui.layer.msg(message, {
          icon: iconMap[type] || 0,
          time: 3000,
        });
      } else {
        console.log(`[ServiceSDK] ${type.toUpperCase()}: ${message}`);
      }
    }

    /**
     * 销毁SDK
     */
    destroy() {
      // 清理WebSocket连接
      if (window.ChatCore) {
        window.ChatCore.cleanup();
      }

      if (this.layimInstance) {
        // LayIM没有直接的销毁方法，清理容器
        if (this.container) {
          this.container.innerHTML = "";
        }
      }

      this.initialized = false;
      this.layimInstance = null;
      this.sessionList = [];
      this.loadedHistorySessions = [];

      console.log("[ServiceSDK] SDK已销毁");
    }
  }

  // 全局暴露
  window.ServiceSDK = ServiceSDK;

  // 静态方法 - 用于快捷回复
  ServiceSDK.insertQuickReply = function (text) {
    const textarea = document.querySelector(".layim-chat-textarea textarea");
    if (textarea) {
      textarea.value = text;
      textarea.focus();
    }
    window.layui.layer.closeAll();
  };

  console.log("[ServiceSDK] 纯LayIM版本SDK加载完成");
})(window, document);
