/**
 * 客服聊天SDK
 * 版本: 1.0.0
 * 作者: Augment Agent
 * 使用方式:
 * <script src="kefu-sdk.js"></script>
 * <script>
 *   KefuWidget.init({
 *     apiUrl: 'http://127.0.0.1',
 *     position: 'bottom-right',
 *     theme: 'blue'
 *   });
 * </script>
 */

(function (window, document) {
  "use strict";

  // 默认配置
  const DEFAULT_CONFIG = {
    apiUrl: "http://127.0.0.1",
    position: "bottom-right", // bottom-right, bottom-left, top-right, top-left
    theme: "blue", // blue, green, red, purple
    width: 380,
    height: 600,
    buttonSize: 60,
    autoOpen: false,
    showUnreadCount: true,
    draggable: true,
    zIndex: 999999,
    // 文本配置
    texts: {
      buttonTitle: "在线客服",
      chatTitle: "在线客服",
      placeholder: "请输入您的问题...",
      sendButton: "发送",
      closeButton: "关闭",
      minimizeButton: "最小化",
      connectingText: "正在连接客服...",
      offlineText: "客服暂时离线",
      welcomeText: "您好！有什么可以帮助您的吗？",
    },
  };

  // 主题配置
  const THEMES = {
    blue: { primary: "#1890ff", secondary: "#f0f8ff" },
    green: { primary: "#52c41a", secondary: "#f6ffed" },
    red: { primary: "#ff4d4f", secondary: "#fff2f0" },
    purple: { primary: "#722ed1", secondary: "#f9f0ff" },
  };

  class KefuWidget {
    constructor() {
      this.config = {};
      this.isInitialized = false;
      this.isVisible = false;
      this.isMinimized = true;
      this.unreadCount = 0;
      this.ws = null;
      this.memberGuid = null;
      this.sessionGuid = null;
      this.bid = null;

      // DOM元素
      this.container = null;
      this.button = null;
      this.chatWindow = null;
      this.messageList = null;
      this.inputBox = null;
    }

    /**
     * 初始化客服组件
     * @param {Object} options 配置选项
     */
    init(options = {}) {
      if (this.isInitialized) {
        console.warn("[KefuSDK] 已经初始化过了");
        return;
      }

      this.config = Object.assign({}, DEFAULT_CONFIG, options);
      this.isInitialized = true;

      // 等待DOM加载完成
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.render());
      } else {
        this.render();
      }

      console.log("[KefuSDK] 初始化完成", this.config);
    }

    /**
     * 渲染组件
     */
    render() {
      this.createStyles();
      this.createButton();
      this.createChatWindow();
      this.bindEvents();

      if (this.config.autoOpen) {
        this.show();
      }
    }

    /**
     * 创建样式
     */
    createStyles() {
      const theme = THEMES[this.config.theme] || THEMES.blue;
      const css = `
                .kefu-widget * { box-sizing: border-box; }
                .kefu-widget { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                
                .kefu-button {
                    position: fixed;
                    width: ${this.config.buttonSize}px;
                    height: ${this.config.buttonSize}px;
                    background: ${theme.primary};
                    border-radius: 50%;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: ${this.config.zIndex};
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 24px;
                    transition: all 0.3s ease;
                    user-select: none;
                }
                
                .kefu-button:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
                }
                
                .kefu-button.bottom-right { bottom: 20px; right: 20px; }
                .kefu-button.bottom-left { bottom: 20px; left: 20px; }
                .kefu-button.top-right { top: 20px; right: 20px; }
                .kefu-button.top-left { top: 20px; left: 20px; }
                
                .kefu-unread-badge {
                    position: absolute;
                    top: -5px;
                    right: -5px;
                    background: #ff4d4f;
                    color: white;
                    border-radius: 10px;
                    padding: 2px 6px;
                    font-size: 12px;
                    min-width: 18px;
                    text-align: center;
                    line-height: 14px;
                }
                
                .kefu-chat-window {
                    position: fixed;
                    width: ${this.config.width}px;
                    height: ${this.config.height}px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                    z-index: ${this.config.zIndex + 1};
                    display: none;
                    flex-direction: column;
                    overflow: hidden;
                    transition: all 0.3s ease;
                }
                
                .kefu-chat-window.bottom-right { bottom: 90px; right: 20px; }
                .kefu-chat-window.bottom-left { bottom: 90px; left: 20px; }
                .kefu-chat-window.top-right { top: 90px; right: 20px; }
                .kefu-chat-window.top-left { top: 90px; left: 20px; }
                
                .kefu-chat-header {
                    background: ${theme.primary};
                    color: white;
                    padding: 15px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: move;
                }
                
                .kefu-chat-title {
                    font-size: 16px;
                    font-weight: 500;
                    margin: 0;
                }
                
                .kefu-chat-controls {
                    display: flex;
                    gap: 10px;
                }
                
                .kefu-control-btn {
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    width: 24px;
                    height: 24px;
                    border-radius: 4px;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                
                .kefu-control-btn:hover {
                    background: rgba(255,255,255,0.3);
                }
                
                .kefu-message-list {
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                    background: #fafafa;
                }
                
                .kefu-message {
                    margin-bottom: 15px;
                    display: flex;
                    align-items: flex-start;
                    gap: 10px;
                }
                
                .kefu-message.mine {
                    flex-direction: row-reverse;
                }
                
                .kefu-message-avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: #ddd;
                    flex-shrink: 0;
                }
                
                .kefu-message-content {
                    background: white;
                    padding: 10px 15px;
                    border-radius: 18px;
                    max-width: 70%;
                    word-wrap: break-word;
                    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
                }
                
                .kefu-message.mine .kefu-message-content {
                    background: ${theme.primary};
                    color: white;
                }
                
                .kefu-input-area {
                    padding: 20px;
                    border-top: 1px solid #f0f0f0;
                    background: white;
                }
                
                .kefu-input-box {
                    width: 100%;
                    border: 1px solid #d9d9d9;
                    border-radius: 6px;
                    padding: 10px 15px;
                    font-size: 14px;
                    outline: none;
                    resize: none;
                    min-height: 40px;
                    max-height: 100px;
                }
                
                .kefu-input-box:focus {
                    border-color: ${theme.primary};
                    box-shadow: 0 0 0 2px ${theme.primary}20;
                }
                
                .kefu-send-btn {
                    background: ${theme.primary};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 20px;
                    margin-top: 10px;
                    cursor: pointer;
                    font-size: 14px;
                    float: right;
                    transition: background 0.2s;
                }
                
                .kefu-send-btn:hover {
                    background: ${theme.primary}dd;
                }
                
                .kefu-send-btn:disabled {
                    background: #ccc;
                    cursor: not-allowed;
                }
                
                @media (max-width: 480px) {
                    .kefu-chat-window {
                        width: calc(100vw - 20px) !important;
                        height: calc(100vh - 100px) !important;
                        bottom: 10px !important;
                        right: 10px !important;
                        left: 10px !important;
                    }
                }
            `;

      const style = document.createElement("style");
      style.textContent = css;
      document.head.appendChild(style);
    }

    /**
     * 创建悬浮按钮
     */
    createButton() {
      this.button = document.createElement("div");
      this.button.className = `kefu-widget kefu-button ${this.config.position}`;
      this.button.innerHTML = `
                💬
                <div class="kefu-unread-badge" style="display: none;">0</div>
            `;
      this.button.title = this.config.texts.buttonTitle;

      document.body.appendChild(this.button);
    }

    /**
     * 创建聊天窗口
     */
    createChatWindow() {
      this.chatWindow = document.createElement("div");
      this.chatWindow.className = `kefu-widget kefu-chat-window ${this.config.position}`;
      this.chatWindow.innerHTML = `
                <div class="kefu-chat-header">
                    <h3 class="kefu-chat-title">${this.config.texts.chatTitle}</h3>
                    <div class="kefu-chat-controls">
                        <button class="kefu-control-btn" data-action="minimize" title="${this.config.texts.minimizeButton}">−</button>
                        <button class="kefu-control-btn" data-action="close" title="${this.config.texts.closeButton}">×</button>
                    </div>
                </div>
                <div class="kefu-message-list"></div>
                <div class="kefu-input-area">
                    <textarea class="kefu-input-box" placeholder="${this.config.texts.placeholder}" rows="1"></textarea>
                    <button class="kefu-send-btn">${this.config.texts.sendButton}</button>
                    <div style="clear: both;"></div>
                </div>
            `;

      this.messageList = this.chatWindow.querySelector(".kefu-message-list");
      this.inputBox = this.chatWindow.querySelector(".kefu-input-box");

      document.body.appendChild(this.chatWindow);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
      // 点击按钮显示/隐藏聊天窗口
      this.button.addEventListener("click", () => {
        if (this.isVisible) {
          this.hide();
        } else {
          this.show();
        }
      });

      // 聊天窗口控制按钮
      this.chatWindow.addEventListener("click", (e) => {
        const action = e.target.dataset.action;
        if (action === "minimize") {
          this.minimize();
        } else if (action === "close") {
          this.hide();
        }
      });

      // 发送消息
      const sendBtn = this.chatWindow.querySelector(".kefu-send-btn");
      sendBtn.addEventListener("click", () => this.sendMessage());

      // 回车发送消息
      this.inputBox.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });

      // 自动调整输入框高度
      this.inputBox.addEventListener("input", () => {
        this.inputBox.style.height = "auto";
        this.inputBox.style.height = Math.min(this.inputBox.scrollHeight, 100) + "px";
      });

      // 拖拽功能
      if (this.config.draggable) {
        this.makeDraggable();
      }
    }

    /**
     * 显示聊天窗口
     */
    show() {
      this.chatWindow.style.display = "flex";
      this.isVisible = true;
      this.isMinimized = false;
      this.clearUnreadCount();

      // 初始化WebSocket连接
      if (!this.ws) {
        this.initWebSocket();
      }

      // 聚焦输入框
      setTimeout(() => {
        this.inputBox.focus();
      }, 100);
    }

    /**
     * 隐藏聊天窗口
     */
    hide() {
      this.chatWindow.style.display = "none";
      this.isVisible = false;
      this.isMinimized = true;
    }

    /**
     * 最小化聊天窗口
     */
    minimize() {
      this.hide();
    }

    /**
     * 发送消息
     */
    sendMessage() {
      const content = this.inputBox.value.trim();
      if (!content) return;

      // 显示消息
      this.addMessage({
        content: content,
        isMine: true,
        avatar: "",
        time: new Date(),
      });

      // 清空输入框
      this.inputBox.value = "";
      this.inputBox.style.height = "auto";

      // 通过WebSocket发送消息
      if (this.ws && this.sessionGuid) {
        this.ws.send(
          JSON.stringify({
            cmd: "chatMessage",
            data: {
              content: content,
              session_guid: this.sessionGuid,
              from_type: 1, // 访客发送
              from_guid: this.memberGuid,
              to_guid: "", // 由后端分配客服
              bid: this.bid,
              msg_type: 1,
            },
          })
        );
      }
    }

    /**
     * 添加消息到聊天列表
     */
    addMessage(message) {
      const messageEl = document.createElement("div");
      messageEl.className = `kefu-message ${message.isMine ? "mine" : ""}`;

      const avatar = message.avatar || (message.isMine ? "" : "/static/img/service.png");
      const time = message.time ? new Date(message.time).toLocaleTimeString() : "";

      messageEl.innerHTML = `
                <img class="kefu-message-avatar" src="${avatar}" alt="头像" onerror="this.style.display='none'">
                <div class="kefu-message-content">
                    ${this.escapeHtml(message.content)}
                    ${time ? `<div style="font-size: 11px; opacity: 0.7; margin-top: 5px;">${time}</div>` : ""}
                </div>
            `;

      this.messageList.appendChild(messageEl);
      this.scrollToBottom();

      // 如果窗口未显示，增加未读计数
      if (!this.isVisible && !message.isMine) {
        this.incrementUnreadCount();
      }
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      const wsUrl = this.config.apiUrl.replace("http", "ws") + ":2346";

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log("[KefuSDK] WebSocket连接成功");
          this.initUser();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleWebSocketMessage(data);
          } catch (e) {
            console.error("[KefuSDK] 解析WebSocket消息失败:", e);
          }
        };

        this.ws.onclose = () => {
          console.log("[KefuSDK] WebSocket连接关闭");
          this.ws = null;
          // 5秒后重连
          setTimeout(() => {
            if (this.isVisible) {
              this.initWebSocket();
            }
          }, 5000);
        };

        this.ws.onerror = (error) => {
          console.error("[KefuSDK] WebSocket错误:", error);
        };
      } catch (e) {
        console.error("[KefuSDK] WebSocket连接失败:", e);
      }
    }

    /**
     * 初始化用户
     */
    initUser() {
      // 生成或获取用户标识
      this.memberGuid = this.getOrCreateMemberGuid();
      this.bid = this.getBid();

      // 发送用户初始化消息
      this.ws.send(
        JSON.stringify({
          cmd: "userInit",
          data: {
            member_guid: this.memberGuid,
            bid: this.bid,
          },
        })
      );
    }

    /**
     * 处理WebSocket消息
     */
    handleWebSocketMessage(data) {
      console.log("[KefuSDK] 收到消息:", data);

      switch (data.cmd) {
        case "userInit":
          this.handleUserInit(data.data);
          break;
        case "chatMessage":
          this.handleChatMessage(data.data);
          break;
        case "robotMessage":
          this.handleRobotMessage(data.data);
          break;
        case "sessionUpdate":
          this.handleSessionUpdate(data.data);
          break;
        default:
          console.log("[KefuSDK] 未处理的消息类型:", data.cmd);
      }
    }

    /**
     * 处理用户初始化响应
     */
    handleUserInit(data) {
      if (data.code === 0) {
        // 连接成功，有会话
        this.sessionGuid = data.session.guid;
        this.addMessage({
          content: this.config.texts.welcomeText,
          isMine: false,
          avatar: "/static/img/service.png",
          time: new Date(),
        });
      } else if (data.code === 201) {
        // 暂无客服在线
        this.addMessage({
          content: data.msg || this.config.texts.offlineText,
          isMine: false,
          avatar: "/static/img/service.png",
          time: new Date(),
        });
      } else {
        // 其他错误
        this.addMessage({
          content: data.msg || "连接失败，请稍后重试",
          isMine: false,
          avatar: "/static/img/service.png",
          time: new Date(),
        });
      }
    }

    /**
     * 处理聊天消息
     */
    handleChatMessage(data) {
      this.addMessage({
        content: data.content,
        isMine: false,
        avatar: data.avatar || "/static/img/service.png",
        time: data.time,
      });
    }

    /**
     * 处理机器人消息
     */
    handleRobotMessage(data) {
      this.addMessage({
        content: data.content,
        isMine: false,
        avatar: data.avatar || "/static/img/robot.png",
        time: data.time,
      });
    }

    /**
     * 处理会话更新
     */
    handleSessionUpdate(data) {
      if (data.status === 2) {
        // 会话结束
        this.addMessage({
          content: "本次会话已结束，感谢您的咨询！",
          isMine: false,
          avatar: "/static/img/service.png",
          time: new Date(),
        });
      }
    }

    /**
     * 工具方法：获取或创建用户GUID
     */
    getOrCreateMemberGuid() {
      let guid = localStorage.getItem("kefu_member_guid");
      if (!guid) {
        guid = this.generateGuid();
        localStorage.setItem("kefu_member_guid", guid);
      }
      return guid;
    }

    /**
     * 工具方法：获取商户ID
     */
    getBid() {
      // 可以从配置中获取，或者从页面中获取
      return this.config.bid || "26810245-d97e-81b6-c1cf-0215fd8f347c";
    }

    /**
     * 工具方法：生成GUID
     */
    generateGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
    }

    /**
     * 工具方法：HTML转义
     */
    escapeHtml(text) {
      const div = document.createElement("div");
      div.textContent = text;
      return div.innerHTML;
    }

    /**
     * 工具方法：滚动到底部
     */
    scrollToBottom() {
      setTimeout(() => {
        this.messageList.scrollTop = this.messageList.scrollHeight;
      }, 100);
    }

    /**
     * 工具方法：增加未读计数
     */
    incrementUnreadCount() {
      this.unreadCount++;
      this.updateUnreadBadge();
    }

    /**
     * 工具方法：清除未读计数
     */
    clearUnreadCount() {
      this.unreadCount = 0;
      this.updateUnreadBadge();
    }

    /**
     * 工具方法：更新未读徽章
     */
    updateUnreadBadge() {
      const badge = this.button.querySelector(".kefu-unread-badge");
      if (this.unreadCount > 0) {
        badge.textContent = this.unreadCount > 99 ? "99+" : this.unreadCount;
        badge.style.display = "block";
      } else {
        badge.style.display = "none";
      }
    }

    /**
     * 工具方法：使窗口可拖拽
     */
    makeDraggable() {
      const header = this.chatWindow.querySelector(".kefu-chat-header");
      let isDragging = false;
      let startX, startY, startLeft, startTop;

      header.addEventListener("mousedown", (e) => {
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        const rect = this.chatWindow.getBoundingClientRect();
        startLeft = rect.left;
        startTop = rect.top;

        document.addEventListener("mousemove", onMouseMove);
        document.addEventListener("mouseup", onMouseUp);
        e.preventDefault();
      });

      const onMouseMove = (e) => {
        if (!isDragging) return;

        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        this.chatWindow.style.left = startLeft + deltaX + "px";
        this.chatWindow.style.top = startTop + deltaY + "px";
        this.chatWindow.style.right = "auto";
        this.chatWindow.style.bottom = "auto";
      };

      const onMouseUp = () => {
        isDragging = false;
        document.removeEventListener("mousemove", onMouseMove);
        document.removeEventListener("mouseup", onMouseUp);
      };
    }

    /**
     * 公共API：显示聊天窗口
     */
    open() {
      this.show();
    }

    /**
     * 公共API：隐藏聊天窗口
     */
    close() {
      this.hide();
    }

    /**
     * 公共API：发送消息
     */
    sendText(text) {
      if (text && text.trim()) {
        this.inputBox.value = text;
        this.sendMessage();
      }
    }

    /**
     * 公共API：设置用户信息
     */
    setUser(userInfo) {
      if (userInfo.memberGuid) {
        this.memberGuid = userInfo.memberGuid;
        localStorage.setItem("kefu_member_guid", userInfo.memberGuid);
      }
      if (userInfo.bid) {
        this.bid = userInfo.bid;
      }
    }

    /**
     * 公共API：销毁组件
     */
    destroy() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      if (this.button) {
        this.button.remove();
      }

      if (this.chatWindow) {
        this.chatWindow.remove();
      }

      this.isInitialized = false;
    }
  }

  // 创建全局实例
  window.KefuWidget = new KefuWidget();
})(window, document);
