!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):t.WaterMask=i()}(this,function(){"use strict";function o(t,i){for(var s=0;s<i.length;s++){var e=i[s];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}return function(){function s(t){!function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,s);var i={font:"",fontType:"Microsoft YaHei",size:24,container:document.querySelector("#watermask"),color:"#333",alpha:.2,rotate:340,scale:1,startX:20,startY:20,rows:10,cols:10,xGap:200,yGap:100,image:"",repeat:!0};this.options=Object.assign(i,t),this.canvas=this.options.container,this.ctx=this.canvas&&this.canvas.getContext("2d"),this.checkOptions(),this.initStyle(),this.autoResize(),this.render()}var t,i,e;return t=s,(i=[{key:"checkOptions",value:function(){if(!this.canvas)throw Error("Canvas element is not found, default selector is #watermask");if(!this.options.font&&!this.options.image)throw Error("Options.font or Options.image is required")}},{key:"initStyle",value:function(){this.canvas.style.position="fixed",this.canvas.style.left=0,this.canvas.style.top=0,this.canvas.style["pointer-events"]="none",this.canvas.style.overflow="hidden",this.canvas.style["z-index"]=99999}},{key:"autoResize",value:function(){var t=this;300===this.canvas.width&&150===this.canvas.height&&(this.canvas.width=window.innerWidth,this.canvas.height=window.innerHeight,window.addEventListener("resize",function(){t.canvas.width=window.innerWidth,t.canvas.height=window.innerHeight,t.ctx.clearRect(0,0,t.canvas.width,t.canvas.height),t.render()}))}},{key:"render",value:function(){this.ctx.globalAlpha=this.options.alpha,this.options.image?this.drawImage():this.drawFont()}},{key:"drawFont",value:function(){var t=this.options.font,i=this.canvas.width,s=this.canvas.height;if(this.ctx.font=this.options.size+"px "+this.options.fontType,this.options.repeat)for(var e=0;e<this.options.cols;e++)for(var o=0;o<this.options.rows;o++)this.ctx.save(),this.ctx.translate(this.options.startX+this.options.xGap*e,this.options.startY+this.options.yGap*o),this.ctx.rotate(this.options.rotate/180*Math.PI),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.fillText(t,0,0),this.ctx.restore();else this.ctx.save(),this.ctx.fillStyle=this.options.color,this.ctx.translate(i/2,s/2),this.ctx.rotate(this.options.rotate/180*Math.PI),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.textAlign="center",this.ctx.fillText(t,0,0),this.ctx.restore()}},{key:"drawImage",value:function(){var e=this,t=this.canvas.width,i=this.canvas.height;if(this.options.repeat)for(var s=function(s){for(var t=function(t){var i=new Image;i.src=e.options.image,i.onload=function(){this.ctx.save(),this.ctx.translate(this.options.startX+this.options.xGap*s,this.options.startY+this.options.yGap*t),this.ctx.rotate(this.options.rotate/180*Math.PI),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.drawImage(i,-i.width/2,-i.height/2),this.ctx.restore()}.bind(e)},i=0;i<e.options.rows;i++)t(i)},o=0;o<this.options.cols;o++)s(o);else{var n=new Image;n.src=this.options.image,n.onload=function(){this.ctx.save(),this.ctx.translate(t/2,i/2),this.ctx.rotate(this.options.rotate/180*Math.PI),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.drawImage(n,-n.width/2,-n.height/2),this.ctx.restore()}.bind(this)}}}])&&o(t.prototype,i),e&&o(t,e),s}()});
