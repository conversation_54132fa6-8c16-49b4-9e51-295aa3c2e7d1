/*
 * Responsively Lazy
 * https://ivopetkov.com/responsively-lazy/
 * Copyright (c) I<PERSON>
 * Free to use under the MIT license.
*/
var responsivelyLazy=void 0!==responsivelyLazy?responsivelyLazy:function(){if(void 0!==window.addEventListener&&void 0!==document.querySelectorAll){var e=-1!==document.cookie.indexOf("ivopetkov-responsively-lazy=debug"),n=null,t=null,A="srcset"in document.createElement("img"),r=null,i=null,o="undefined"!=typeof IntersectionObserver,l=!1,a=function(e){var n=0,t=0,A=e.getAttribute("data-responsively-lazy-threshold");if(null!==A)if("px"===A.substr(-2))n=t=parseInt(A.substr(0,A.length-2),10);else if("%"===A.substr(-1)){var o=parseInt(A.substr(0,A.length-1),10)/100;n=Math.floor(r*o),t=Math.floor(i*o)}var l=e.getBoundingClientRect(),a=l.top,s=l.left,u=l.width,d=l.height;if(0===a&&0===s&&0===u&&0===d)return 0;0===u&&(u=1),0===d&&(d=1);var v=function(e,n,t){return e<t&&e+n>0?Math.min(t,e+n)-Math.max(0,e):0};return v(s-n,u+2*n,r)*v(a-t,d+2*t,i)/((u+2*n)*(d+2*t))*100},s=function(e,n){for(var t=e.length,A=n;A<t;A++){var r=!1,i=e[A],o=document.createElement("script"),l=i.getAttribute("type");null!==l&&o.setAttribute("type",l);var a=i.getAttribute("src");if(null!==a&&(o.setAttribute("src",a),(void 0===i.async||!1===i.async)&&A+1<t&&(r=!0,o.addEventListener("load",(function(){s(e,A+1)})))),o.innerHTML=i.innerHTML,i.parentNode.insertBefore(o,i),i.parentNode.removeChild(i),r)break}},u=[],d=!1,v=function(){if(!d){d=!0;u=u.filter((function(e){return 2!==e[2]}));for(var e=0;e<u.length;e++)u[e][4]=a(u[e][1]);u.sort((function(e,n){return n[4]-e[4]}));var n=u.filter((function(e){return 1===e[3]})).length;for(e=0;e<u.length&&!(n>=3);e++){var t=u[e];0===t[3]&&(t[3]=1,t[2](),n++)}d=!1}},c=0,f=function(e,A){var r=[],i=A.getAttribute("data-responsively-lazy"),o=null;if(null!==i&&(i=i.trim()).length>0){i=i.split(",");for(var l=0;l<i.length;l++){for(var a=null,s=999998,d=!1,f=i[l].trim().split(" "),y=0;y<f.length;y++){var p=f[y],g=p.length;0!==g&&(null===a?a=p:"w"===p[g-1]?s=parseInt(p.substr(0,g-1),10):("webp"!==p||n)&&("avif"!==p||t)||(d=!0))}d||(-1===a.indexOf("%2F")&&-1===a.indexOf("%3F")||-1!==a.indexOf("/")||-1!==a.indexOf("?")||(a=decodeURIComponent(a)),r.push([a,s]),o<s&&(o=s))}r.sort((function(e,n){return e[1]-n[1]}));var h=[];for(l=0;l<r.length;l++){var m=r[l];l>0&&m[1]===h[h.length-1][1]||h.push([m[0],m[1]])}r=h}var b=A.getBoundingClientRect().width*(void 0!==window.devicePixelRatio?window.devicePixelRatio:1),w=null;for(l=0;l<r.length;l++){if((m=r[l])[1]>=b||m[1]===o){w=m;break}}if(null===w&&(w="img"===e?[A.getAttribute("src"),999999]:[null,999999]),void 0===A.responsivelyLazyOption&&(A.responsivelyLazyOption=["",0]),A.responsivelyLazyOption[1]<w[1]){A.responsivelyLazyOption=w;var L=w[0];if(null===L)return;!function(e,n,t){var A="i"+ ++c,r=null,i=function(){clearTimeout(r);for(var e=0;e<u.length;e++){var n=u[e];if(n[0]===A){n[3]=2;break}}v()},o=new Image;o.onload=function(){i(),t(!0)},o.onerror=function(){i(),t(!1)};u.push([A,e,function(){o.src=n,r=setTimeout(i,6e4)},0,0]),v()}(A,L,(function(n){if(n&&A.responsivelyLazyOption[0]===L){if("img"===e?L===A.getAttribute("src")?A.removeAttribute("srcset"):A.setAttribute("srcset",L):A.style.backgroundImage="url("+L+")",void 0===A.responsivelyLazyLoadDispached){A.responsivelyLazyLoadDispached=!0;var t=A.getAttribute("data-on-responsively-lazy-load");if(null!==t&&new Function(t).bind(A)(),"undefined"!=typeof Event){var r=new Event("responsively-lazy-load");A.dispatchEvent(r)}}}else A.responsivelyLazyOption=["",0]}))}},y=function(){r=window.innerWidth,i=window.innerHeight},p=function(e,n){if(void 0===e.responsivelyLazyDone&&(void 0!==n.ignoreThreshold&&n.ignoreThreshold||0!==a(e))){var t=e.getAttribute("data-responsively-lazy-type");if("background"!==t&&"html"!==t&&(t="img"),"html"===t){e.responsivelyLazyDone=!0,l=!0,e.innerHTML=e.getAttribute("data-responsively-lazy");var r=e.querySelectorAll("script");r.length>0&&s(r,0),l=!1}else"img"===t?A&&f(t,e):f(t,e)}},g=function(A,r){if(null!==n&&null!==t){if(void 0===r&&(r={}),e){var i="responsivelyLazy::run";console.time(i)}if(null!=A)null!==A.getAttribute("data-responsively-lazy")&&p(A,r);else for(var o=document.querySelectorAll("[data-responsively-lazy]"),l=0;l<o.length;l++){A=o[l];p(o[l],r)}e&&console.timeEnd(i)}},h=function(e,n){var t=new Image;t.onload=t.onerror=function(){n(t)},t.src="data:image/webp;base64,"+e},m=function(){null!==n&&null!==t&&g()};h("UklGRiQAAABXRUJQVlA4IBgAAAAwAQCdASoBAAEAD8D+JaQAA3AA/ua1AAA=",(function(e){n=1===e.width,m()})),h("AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUEAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAACAAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgSAAAAAAABNjb2xybmNseAABAA0AAIAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAChtZGF0EgAKBzgABpAQ0AIyExAAAAAP+j9adAx6kYPdyoRe9BA=",(function(e){t=1===e.width,m()})),y();var b=window.requestAnimationFrame||function(e){window.setTimeout(e,20)},w=!1,L=function(){w&&(w=!1,g()),b.call(null,L)};if(L(),o)var z=new IntersectionObserver((function(e){for(var n in e){var t=e[n];t.intersectionRatio>0&&p(t.target,{})}})),B=function(){for(var e=document.querySelectorAll("[data-responsively-lazy]"),n=0;n<e.length;n++){var t=e[n];void 0===t.responsivelyLazyObserver&&(t.responsivelyLazyObserver=!0,z.observe(t))}},E=null;var x=function(){o?(window.clearTimeout(E),E=window.setTimeout((function(){w=!0}),50)):w=!0},O=function(){for(var e=document.querySelectorAll("[data-responsively-lazy]"),n=0;n<e.length;n++)for(var t=e[n].parentNode;t&&"html"!==t.tagName.toLowerCase();)void 0===t.responsivelyLazyScroll&&(t.responsivelyLazyScroll=!0,t.addEventListener("scroll",x)),t=t.parentNode},I=!1,k=function(){I||(I=!0,window.addEventListener("resize",(function(){y(),x()})),window.addEventListener("scroll",x),window.addEventListener("load",x),window.addEventListener("orientationchange",(function(){y(),x()})),o&&B(),O(),"undefined"!=typeof MutationObserver&&new MutationObserver((function(){l||(o&&B(),O(),x())})).observe(document.querySelector("body"),{childList:!0,subtree:!0}))};document.addEventListener("readystatechange",()=>{k(),g()}),"complete"===document.readyState&&(k(),g())}else g=function(){};return{run:g}}();