(function ($) {
    let lockReconnect = false;//避免重复连接
    let ws = null;
    $.config = {
        url: '', //websocket服务端接口地址
        client_id: 'client_id', //客户端标识,允许重复
        pingTimeout: 10000, //每隔N毫秒发送一次心跳，如果收到任何后端消息定时器将会重置
        pongTimeout: 2000, //ping消息发送之后，N毫秒内没收到后端消息便会认为连接断开
        reconnectTimeout: 10000, //尝试重连的间隔时间(毫秒)
    };

    $.init = function (config) {
        //用户参数覆盖默认参数
        for (let key in config) {
            this.config[key] = config[key];
        }
        return this;
    };

    $.reconnect = function () {
        if (lockReconnect) return;
        lockReconnect = true;
        //没连接上会一直重连，设置延迟避免请求过多
        let timer = setTimeout(function () {
            $.log("readyState=" + ws.readyState);
            if (ws.readyState == 2 || ws.readyState == 3) {
                $.connect();
                lockReconnect = false;
            } else if (ws.readyState == 1) {
                clearTimeout(timer);
            } else {
                alert('未知的readyState:' + ws.readyState)
                return;
            }
            $.log("正在重连")
        }, $.config.reconnectTimeout); //这里设置重连间隔(ms)
    }
    //心跳检测
    $.heartCheck = {
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: function () {
            clearTimeout(this.timeoutObj);
            clearTimeout(this.serverTimeoutObj);
            return this;
        },
        start: function () {
            let self = this;
            this.timeoutObj = setTimeout(function () {
                $.send({type: 'ping'});// 发起ping
                self.serverTimeoutObj = setTimeout(function () {//如果超过一定时间还没重置，说明后端主动断开了
                    $.log("超过" + $.config.pongTimeout + "毫秒未收到服务端回应,关闭连接,自动触发重试");
                    ws.close();//如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
                }, $.config.pongTimeout)
            }, $.config.pingTimeout)
        }
    }
    /**
     * 连接webcocket
     */
    $.connect = function () {
        let protocol = (window.location.protocol == 'http:') ? 'ws:' : 'wss:';
        this.host = protocol + this.config.url;
        window.WebSocket = window.WebSocket || window.MozWebSocket;
        if (!window.WebSocket) { // 检测浏览器支持
            alert('Error: WebSocket is not supported .');
            return;
        }
        try {
            ws = new WebSocket(this.host);
        } catch (e) {
            $.log("连接发生错误~");
            $.log(e);
            $.reconnect();
        }
        ws.onopen = function () {
            let sendData = {type: 'login', client_id: $.config.client_id}; // 发起登陆
            $.send(sendData);
            $.log("成功连接到" + $.host);
            $.heartCheck.reset().start();//心跳检测重置
            $.onopen();
        };
        ws.onmessage = function (message) {
            $.heartCheck.reset().start(); //如果获取到消息，心跳检测重置,因为拿到任何消息都说明当前连接是正常的
            //Json转换成Object
            let obj = eval('(' + message.data + ')');
            switch (obj.type) {
                case 'ping':
                    $.log("收到服务端心跳请求:ping");//忽略心跳的信息，因为只要有消息进来，断线重连就会重置不会触发
                    $.send({type: 'pong'});// 回应pong
                    return;
                    break;
                case 'pong':
                    $.log("收到服务端心跳回应:pong");//忽略心跳的信息，因为只要有消息进来，断线重连就会重置不会触发
                    return;
                    break;
                case 'push':
                    $.log("收到服务端push消息");
                    break;
                default:
                    $.log("收到类型为" + obj.type + "数据");
            }
            $.onmessage(obj);
        };
        ws.onclose = function () {
            $.log("onclose");
            $.reconnect();//重连
            $.onclose();
        };
        ws.onerror = function (errorMsg) {
            $.log("onerror");
            $.reconnect();//重连
            $.onerror(errorMsg);
        }
        // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
        window.onbeforeunload = function (e) {
            $.log("onbeforeunload");
            ws.close();
            //return '您输入的内容尚未保存，确定离开此页面吗？';
        }
        // 强制退出
        window.onunload = function () {
            $.log("onunload");
            ws.close();
            //return '您输入的内容尚未保存，确定离开此页面吗!';
        }
        return this;
    }

    $.log = function (msg) {
        let now_time = formatDateTime('yyyy-MM-dd hh:mm:ss.S');
        if (typeof (msg) == "object" && Object.prototype.toString.call(msg).toLowerCase() == "[object object]" && !msg.length) {
            msg = JSON.stringify(msg)
        }
        console.log(now_time + '---' + msg);
    }
    /**
     * 自定义异常函数
     * @param {Object} errorMsg
     */
    $.error = function (errorMsg) {
        $.log("error 方法");
        this.onerror(errorMsg);
    }

    /**
     * 消息发送
     */
    $.send = function (message) {
        //$.log("send -------");
        //message.from = 'client';
        let sendData = JSON.stringify(message);
        if (ws.readyState == 1) {
            ws.send(sendData);
            return true;
        } else {
            $.log('please connect to the server first !!!');
            ws.close();
            return false;
        }
    }


    $.close = function () {
        $.log("close-------");
        if (ws != undefined && ws != null) {
            ws.close();
        } else {
            this.error("this socket is not available");
        }
    }


    /**
     * 消息回調
     * @param {Object} message
     */
    $.onmessage = function (message) {
        $.log("onmessage 回调方法");
    }


    /**
     * 链接回调函数
     */
    $.onopen = function () {
        $.log("onopen 回调方法");
    }


    /**
     * 关闭回调
     */
    $.onclose = function () {
        $.log("onclose 回调方法");
    }


    /**
     * 异常回调
     */
    $.onerror = function (message) {
        $.log("onerror 回调方法");
    }
})(wss = {});