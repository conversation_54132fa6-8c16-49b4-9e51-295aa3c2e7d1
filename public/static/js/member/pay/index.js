// 自助付款页面JavaScript

// 工具函数
function is_weixin() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf('micromessenger') !== -1;
}

function loadScript(src, callback) {
  var script = document.createElement('script');
  script.src = src;
  script.onload = callback || function() {};
  script.onerror = function() {
    console.error('Failed to load script:', src);
  };
  document.head.appendChild(script);
}

// 响应式适配脚本
function initFlexible() {
  !function (win) {
    function resize() {
      var domWidth = domEle.getBoundingClientRect().width;
      if (domWidth / v > 540) {
        domWidth = 540 * v;
      }
      win.rem = domWidth / 18.75;
      domEle.style.fontSize = win.rem + "px";
    }

    var v, initial_scale, timeCode, dom = win.document, domEle = dom.documentElement,
      viewport = dom.querySelector('meta[name="viewport"]'),
      flexible = dom.querySelector('meta[name="flexible"]');
    if (viewport) {
      var o = viewport.getAttribute("content").match(/initial\-scale=(["']?)([\d\.]+)\1?/);
      if (o) {
        initial_scale = parseFloat(o[2]);
        v = parseInt(1 / initial_scale);
      }
    } else {
      if (flexible) {
        var o = flexible.getAttribute("content").match(/initial\-dpr=(["']?)([\d\.]+)\1?/);
        if (o) {
          v = parseFloat(o[2]);
          initial_scale = parseFloat((1 / v).toFixed(2))
        }
      }
    }
    if (!v && !initial_scale) {
      var n = (win.navigator.appVersion.match(/android/gi), win.navigator.appVersion.match(/iphone/gi));
      v = win.devicePixelRatio;
      v = n ? v >= 3 ? 3 : v >= 2 ? 2 : 1 : 1, initial_scale = 1 / v
    }
    if (domEle.setAttribute("data-dpr", v), !viewport) {
      if (viewport = dom.createElement("meta"), viewport.setAttribute("name", "viewport"), viewport.setAttribute("content", "initial-scale=" + initial_scale + ", maximum-scale=" + initial_scale + ", minimum-scale=" + initial_scale + ", user-scalable=no"), domEle.firstElementChild) {
        domEle.firstElementChild.appendChild(viewport)
      } else {
        var m = dom.createElement("div");
        m.appendChild(viewport), dom.write(m.innerHTML)
      }
    }
    win.dpr = v;
    win.addEventListener("resize", function () {
      clearTimeout(timeCode), timeCode = setTimeout(resize, 300)
    }, false);
    win.addEventListener("pageshow", function (b) {
      b.persisted && (clearTimeout(timeCode), timeCode = setTimeout(resize, 300))
    }, false);
    resize();
  }(window);
}

// 加载支付SDK
function loadPaymentSDK() {
  if (is_weixin()) {
    loadScript('/static/js/wechatpay.js?v=20191203');
  } else {
    loadScript('https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js');
    loadScript('/static/js/alipay.js?v=20191201');
  }
}

// 加载调试工具
function loadDebugTool() {
  // 开发环境下加载调试工具
  if (window.location.hostname === 'localhost' ||
      window.location.hostname.indexOf('192.168') === 0 ||
      window.location.search.indexOf('debug=1') !== -1) {
    loadScript('/static/js/eruda.min.js?v=2.4.1', function () {
      if (window.eruda) {
        eruda.init();
      }
    });
  }
}

// 页面初始化
document.addEventListener("DOMContentLoaded", function () {
  // 初始化响应式适配
  initFlexible();

  // 加载支付SDK
  loadPaymentSDK();

  // 加载调试工具
  loadDebugTool();

  // 初始化Vue应用
  const { createApp } = Vue;

  const app = createApp({
    data() {
      return {
        // 页面状态
        loading: true,
        paying: false,
        showKeyboard: true,
        showOrderModal: false,
        showMessage: false,

        // 数据
        shopInfo: {},
        memberInfo: {},
        payConfig: {},

        // 输入金额
        inputAmount: "0",

        // 消息
        message: {
          text: "",
          type: "info", // info, success, error
        },

        // URL参数
        bid: "",
        storeGuid: "",
        memo: "",
        openid: "",
      };
    },

    computed: {
      // 格式化显示金额
      displayAmount() {
        return this.inputAmount === "0" ? "0" : this.inputAmount;
      },

      // 是否显示余额
      showBalance() {
        return this.memberInfo && this.memberInfo.balance !== undefined;
      },

      // 计算手续费
      serviceFee() {
        const amount = parseFloat(this.inputAmount) || 0;
        const rate = parseFloat(this.payConfig.service_rate) || 0;
        return ((amount * rate) / 100).toFixed(2);
      },

      // 实际支付金额
      actualAmount() {
        const amount = parseFloat(this.inputAmount) || 0;
        const fee = parseFloat(this.serviceFee) || 0;
        return (amount + fee).toFixed(2);
      },

      // 余额是否充足
      balanceEnough() {
        if (!this.memberInfo || this.memberInfo.balance === undefined) {
          return false;
        }
        const balance = parseFloat(this.memberInfo.balance) || 0;
        const actual = parseFloat(this.actualAmount) || 0;
        return balance >= actual;
      },

      // 是否可以支付
      canPay() {
        const amount = parseFloat(this.inputAmount) || 0;
        return amount > 0 &&
               this.inputAmount !== "0" &&
               !this.inputAmount.endsWith(".");
      },

      // 支付金额（用于显示）
      payAmount() {
        return parseFloat(this.inputAmount) || 0;
      },

      // 优惠信息
      couponInfo() {
        return {
          show: false,
          title: "优惠信息",
          amount: 0
        };
      },

      // 支付信息
      paymentInfo() {
        return {
          receiver: this.shopInfo.name || this.shopInfo.business_name || "微信充值"
        };
      },

      // 是否显示余额支付
      showBalancePayment() {
        return this.showBalance && this.memberInfo.balance > 0;
      },

      // 是否显示订单弹窗
      showOrder() {
        return this.showOrderModal;
      },

      // 消息显示
      message() {
        return this.showMessage ? this.message.text : "";
      },

      // 消息类型
      messageType() {
        return this.showMessage ? this.message.type : "";
      }
    },

    mounted() {
      this.initializePage();
    },

    methods: {
      // 初始化页面
      initializePage() {
        this.loading = true;

        // 获取URL参数和全局配置
        this.bid = this.getUrlParameter("bid") || (window.PAY_CONFIG && window.PAY_CONFIG.bid) || "";
        this.storeGuid = this.getUrlParameter("store_guid") || "";
        this.memo = this.getUrlParameter("memo") || "";
        this.openid = this.getUrlParameter("openid") || (window.PAY_CONFIG && window.PAY_CONFIG.openid) || "";

        // 设置页面标题
        document.title = "自助付款";

        // 加载配置信息
        this.loadPayConfig();

        console.log("自助付款页面初始化完成", {
          bid: this.bid,
          storeGuid: this.storeGuid,
          openid: this.openid
        });
      },

      // 获取URL参数
      getUrlParameter(name) {
        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
        const regex = new RegExp("[\\?&]" + name + "=([^&#]*)");
        const results = regex.exec(location.search);
        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
      },

      // 加载支付配置
      loadPayConfig() {
        // 先尝试从缓存获取
        const cacheKey = `pay_config_${this.bid}`;
        var wsCache = new WebStorageCache();
        const cached = wsCache.get(cacheKey);

        if (cached && cached.expire_time > Date.now()) {
          this.payConfig = cached.data;
          this.shopInfo = cached.data.store_info || {};
          this.loadMemberInfo();
          this.loadBusinessInfo(); // 添加业务信息加载
          return;
        }

        // 从服务器获取
        post_layui_member_api_v1(
          "/upay/get_config",
          {
            bid: this.bid,
            store_guid: this.storeGuid,
          },
          (result) => {
            if (result.data) {
              this.payConfig = result.data;
              this.shopInfo = result.data.store_info || {};

              // 缓存配置信息（缓存1小时）
              wsCache.set(cacheKey, {
                data: result.data,
                expire_time: Date.now() + 3600000,
              });

              this.loadMemberInfo();
              this.loadBusinessInfo(); // 添加业务信息加载
            } else {
              this.showErrorMessage(result.msg || "加载配置失败");
              this.loading = false;
            }
          }
        );
      },

      // 加载业务信息
      loadBusinessInfo() {
        const url = "/member_api/v1/business/info/";
        const data = {
          bid: this.bid
        };

        ajax({
          url: url,
          data: data,
          success: (result) => {
            console.log("Business info:", result);
            if (result.code === 0 && result.data) {
              // 更新店铺信息
              this.shopInfo = Object.assign(this.shopInfo, result.data);
              if (result.data.business_name) {
                this.shopInfo.name = result.data.business_name;
              }
            } else {
              console.warn("加载业务信息失败:", result.msg);
            }
          },
          error: (error) => {
            console.error("加载业务信息出错:", error);
          }
        });
      },

      // 加载会员信息
      loadMemberInfo() {
        post_layui_member_api_v1("/member/info", {}, (result) => {
          if (result.code === 1 && result.data) {
            this.memberInfo = result.data;
          }
          this.loading = false;
        });
      },

      // 数字键盘输入
      inputNumber(num) {
        // 如果是小数点
        if (num === '.') {
          // 如果当前为0，先添加0再添加小数点
          if (this.inputAmount === "0") {
            this.inputAmount = "0.";
          } else if (this.inputAmount.indexOf(".") === -1) {
            // 如果没有小数点，添加小数点
            this.inputAmount += ".";
          }
          return;
        }

        // 数字输入逻辑
        if (this.inputAmount === "0") {
          this.inputAmount = num.toString();
        } else {
          // 限制整数部分长度和小数部分长度
          const parts = this.inputAmount.split(".");
          if (parts.length === 1) {
            // 没有小数点，限制整数部分长度
            if (parts[0].length < 5) {
              this.inputAmount += num.toString();
            }
          } else {
            // 有小数点，限制小数部分长度
            if (parts[1].length < 2) {
              this.inputAmount += num.toString();
            }
          }
        }
        this.validateAmount();
        this.updatePayButtonState();
      },

      // 删除最后一位
      deleteNumber() {
        if (this.inputAmount.length > 1) {
          this.inputAmount = this.inputAmount.slice(0, -1);
        } else {
          this.inputAmount = "0";
        }
        this.validateAmount();
        this.updatePayButtonState();
      },

      // 更新支付按钮状态
      updatePayButtonState() {
        const amount = parseFloat(this.inputAmount) || 0;
        const isValid = amount > 0 &&
                       this.inputAmount !== "0" &&
                       !this.inputAmount.endsWith(".");

        // 更新按钮状态的视觉反馈
        const payButton = document.querySelector('.pay-key');
        if (payButton) {
          if (isValid) {
            payButton.classList.add('active');
          } else {
            payButton.classList.remove('active');
          }
        }
      },

      // 验证金额
      validateAmount() {
        // 限制小数点后两位
        const parts = this.inputAmount.split(".");
        if (parts.length > 1 && parts[1].length > 2) {
          this.inputAmount = parts[0] + "." + parts[1].substring(0, 2);
        }

        // 限制最大金额
        const amount = parseFloat(this.inputAmount) || 0;
        const maxAmount = parseFloat(this.payConfig.max_amount) || 99999;
        if (amount > maxAmount) {
          this.showErrorMessage(`单笔支付金额不能超过${maxAmount}元`);
          this.inputAmount = maxAmount.toString();
        }
      },

      // 隐藏键盘
      hideKeyboard() {
        this.showKeyboard = false;
      },

      // 显示键盘
      showKeyboardPanel() {
        this.showKeyboard = true;
      },

      // 确认支付
      confirmPayment() {
        const amount = parseFloat(this.inputAmount) || 0;

        // 验证金额
        if (amount <= 0) {
          this.showErrorMessage("请输入支付金额");
          return;
        }

        const minAmount = parseFloat(this.payConfig.min_amount) || 0.01;
        if (amount < minAmount) {
          this.showErrorMessage(`最小支付金额为${minAmount}元`);
          return;
        }

        // 显示订单确认弹窗
        this.showOrderModal = true;
      },

      // 隐藏支付订单弹窗
      hidePaymentOrder() {
        this.showOrderModal = false;
      },

      // 执行支付
      processPay() {
        this.paying = true;

        const payData = {
          bid: this.bid,
          store_guid: this.storeGuid,
          amount: this.inputAmount,
          memo: this.memo,
          pay_type: "balance", // 余额支付
        };

        post_layui_member_api_v1("/upay/pay", payData, (result) => {
          this.paying = false;

          if (result.code === 1) {
            this.showSuccessMessage("支付成功");

            // 延迟跳转或关闭页面
            setTimeout(() => {
              if (window.history.length > 1) {
                window.history.back();
              } else {
                window.close();
              }
            }, 2000);
          } else {
            this.showErrorMessage(result.msg || "支付失败");
          }

          this.showOrderModal = false;
        });
      },

      // 取消支付
      cancelPay() {
        this.showOrderModal = false;
      },

      // 格式化金额显示
      formatAmount(amount) {
        const num = parseFloat(amount) || 0;
        return num.toFixed(2);
      },

      // 显示成功消息
      showSuccessMessage(text) {
        this.message = {
          text: text,
          type: "success",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 显示错误消息
      showErrorMessage(text) {
        this.message = {
          text: text,
          type: "error",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 显示信息消息
      showInfoMessage(text) {
        this.message = {
          text: text,
          type: "info",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 处理错误
      handleError(result, defaultMsg) {
        const errorMsg = result && result.msg ? result.msg : defaultMsg;
        this.showErrorMessage(errorMsg);
        console.error("API Error:", result);
      },
    },
  });

  // 挂载Vue应用
  app.mount("#app");

  // 全局错误处理
  window.addEventListener("error", function (event) {
    console.error("页面错误:", event.error);
  });

  // 页面可见性变化处理
  document.addEventListener("visibilitychange", function () {
    if (document.hidden) {
      console.log("页面隐藏");
    } else {
      console.log("页面显示");
    }
  });

  console.log("自助付款页面完全加载完成");
});

// 页面完全加载后的回调
window.addEventListener('load', function() {
  console.log("所有资源加载完成");

  // 确保所有必要的全局函数都已定义
  if (typeof ajax === 'undefined') {
    console.warn('ajax函数未定义，请检查function.js是否正确加载');
  }

  if (typeof post_layui_member_api_v1 === 'undefined') {
    console.warn('post_layui_member_api_v1函数未定义，请检查相关API文件是否正确加载');
  }

  if (typeof WebStorageCache === 'undefined') {
    console.warn('WebStorageCache未定义，请检查web-storage-cache.min.js是否正确加载');
  }
});
