// 自助付款页面JavaScript
document.addEventListener("DOMContentLoaded", function () {
  // 初始化Vue应用
  const { createApp } = Vue;

  const app = createApp({
    data() {
      return {
        // 页面状态
        loading: true,
        paying: false,
        showKeyboard: true,
        showOrderModal: false,
        showMessage: false,

        // 数据
        shopInfo: {},
        memberInfo: {},
        payConfig: {},

        // 输入金额
        inputAmount: "0",

        // 消息
        message: {
          text: "",
          type: "info", // info, success, error
        },

        // URL参数
        bid: "",
        storeGuid: "",
        memo: "",
      };
    },

    computed: {
      // 格式化显示金额
      displayAmount() {
        return this.inputAmount === "0" ? "0" : this.inputAmount;
      },

      // 是否显示余额
      showBalance() {
        return this.memberInfo && this.memberInfo.balance !== undefined;
      },

      // 计算手续费
      serviceFee() {
        const amount = parseFloat(this.inputAmount) || 0;
        const rate = parseFloat(this.payConfig.service_rate) || 0;
        return ((amount * rate) / 100).toFixed(2);
      },

      // 实际支付金额
      actualAmount() {
        const amount = parseFloat(this.inputAmount) || 0;
        const fee = parseFloat(this.serviceFee) || 0;
        return (amount + fee).toFixed(2);
      },

      // 余额是否充足
      balanceEnough() {
        if (!this.memberInfo || this.memberInfo.balance === undefined) {
          return false;
        }
        const balance = parseFloat(this.memberInfo.balance) || 0;
        const actual = parseFloat(this.actualAmount) || 0;
        return balance >= actual;
      },
    },

    mounted() {
      this.initializePage();
    },

    methods: {
      // 初始化页面
      initializePage() {
        this.loading = true;

        // 获取URL参数
        this.bid = this.getUrlParameter("bid");
        this.storeGuid = this.getUrlParameter("store_guid");
        this.memo = this.getUrlParameter("memo");

        // 设置页面标题
        document.title = "自助付款";

        // 加载配置信息
        this.loadPayConfig();

        console.log("自助付款页面初始化完成");
      },

      // 获取URL参数
      getUrlParameter(name) {
        name = name.replace(/[\[]/, "\\[").replace(/[\]]/, "\\]");
        const regex = new RegExp("[\\?&]" + name + "=([^&#]*)");
        const results = regex.exec(location.search);
        return results === null ? "" : decodeURIComponent(results[1].replace(/\+/g, " "));
      },

      // 加载支付配置
      loadPayConfig() {
        // 先尝试从缓存获取
        const cacheKey = `pay_config_${this.bid}`;
        var wsCache = new WebStorageCache();
        const cached = wsCache.get(cacheKey);

        if (cached && cached.expire_time > Date.now()) {
          this.payConfig = cached.data;
          this.loadMemberInfo();
          return;
        }

        // 从服务器获取
        post_layui_member_api_v1(
          "/upay/get_config",
          {
            bid: this.bid,
            store_guid: this.storeGuid,
          },
          (result) => {
            if (result.data) {
              this.payConfig = result.data;
              this.shopInfo = result.data.store_info || {};

              // 缓存配置信息（缓存1小时）
              wsCache.set(cacheKey, {
                data: result.data,
                expire_time: Date.now() + 3600000,
              });

              this.loadMemberInfo();
            } else {
              this.showErrorMessage(result.msg || "加载配置失败");
              this.loading = false;
            }
          }
        );
      },

      // 加载会员信息
      loadMemberInfo() {
        post_layui_member_api_v1("/member/info", {}, (result) => {
          if (result.code === 1 && result.data) {
            this.memberInfo = result.data;
          }
          this.loading = false;
        });
      },

      // 数字键盘输入
      inputNumber(num) {
        if (this.inputAmount === "0") {
          this.inputAmount = num.toString();
        } else {
          this.inputAmount += num.toString();
        }
        this.validateAmount();
      },

      // 输入小数点
      inputDot() {
        if (this.inputAmount.indexOf(".") === -1) {
          this.inputAmount += ".";
        }
      },

      // 删除最后一位
      deleteLastChar() {
        if (this.inputAmount.length > 1) {
          this.inputAmount = this.inputAmount.slice(0, -1);
        } else {
          this.inputAmount = "0";
        }
        this.validateAmount();
      },

      // 验证金额
      validateAmount() {
        // 限制小数点后两位
        const parts = this.inputAmount.split(".");
        if (parts.length > 1 && parts[1].length > 2) {
          this.inputAmount = parts[0] + "." + parts[1].substring(0, 2);
        }

        // 限制最大金额
        const amount = parseFloat(this.inputAmount) || 0;
        const maxAmount = parseFloat(this.payConfig.max_amount) || 99999;
        if (amount > maxAmount) {
          this.showErrorMessage(`单笔支付金额不能超过${maxAmount}元`);
          this.inputAmount = maxAmount.toString();
        }
      },

      // 隐藏键盘
      hideKeyboard() {
        this.showKeyboard = false;
      },

      // 显示键盘
      showKeyboardPanel() {
        this.showKeyboard = true;
      },

      // 确认支付
      confirmPayment() {
        const amount = parseFloat(this.inputAmount) || 0;

        // 验证金额
        if (amount <= 0) {
          this.showErrorMessage("请输入支付金额");
          return;
        }

        const minAmount = parseFloat(this.payConfig.min_amount) || 0.01;
        if (amount < minAmount) {
          this.showErrorMessage(`最小支付金额为${minAmount}元`);
          return;
        }

        // 检查余额
        if (!this.balanceEnough) {
          this.showErrorMessage("余额不足，请先充值");
          return;
        }

        // 显示订单确认弹窗
        this.showOrderModal = true;
      },

      // 执行支付
      processPay() {
        this.paying = true;

        const payData = {
          bid: this.bid,
          store_guid: this.storeGuid,
          amount: this.inputAmount,
          memo: this.memo,
          pay_type: "balance", // 余额支付
        };

        post_layui_member_api_v1("/upay/pay", payData, (result) => {
          this.paying = false;

          if (result.code === 1) {
            this.showSuccessMessage("支付成功");

            // 延迟跳转或关闭页面
            setTimeout(() => {
              if (window.history.length > 1) {
                window.history.back();
              } else {
                window.close();
              }
            }, 2000);
          } else {
            this.showErrorMessage(result.msg || "支付失败");
          }

          this.showOrderModal = false;
        });
      },

      // 取消支付
      cancelPay() {
        this.showOrderModal = false;
      },

      // 格式化金额显示
      formatAmount(amount) {
        const num = parseFloat(amount) || 0;
        return num.toFixed(2);
      },

      // 显示成功消息
      showSuccessMessage(text) {
        this.message = {
          text: text,
          type: "success",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 显示错误消息
      showErrorMessage(text) {
        this.message = {
          text: text,
          type: "error",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 显示信息消息
      showInfoMessage(text) {
        this.message = {
          text: text,
          type: "info",
        };
        this.showMessage = true;
        setTimeout(() => {
          this.showMessage = false;
        }, 3000);
      },

      // 处理错误
      handleError(result, defaultMsg) {
        const errorMsg = result && result.msg ? result.msg : defaultMsg;
        this.showErrorMessage(errorMsg);
        console.error("API Error:", result);
      },
    },
  });

  // 挂载Vue应用
  app.mount("#app");

  // 全局错误处理
  window.addEventListener("error", function (event) {
    console.error("页面错误:", event.error);
  });

  // 页面可见性变化处理
  document.addEventListener("visibilitychange", function () {
    if (document.hidden) {
      console.log("页面隐藏");
    } else {
      console.log("页面显示");
    }
  });
});
