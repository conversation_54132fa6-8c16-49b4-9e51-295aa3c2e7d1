/**
 * 客服端SDK
 * 版本: 1.0.0
 * 作者: Augment Agent
 * 功能: 提供完整的客服工作台功能，包括LayIM聊天、会话管理、转接等
 * 使用方式:
 * <script src="service-sdk.js"></script>
 * <script>
 *   ServiceWidget.init({
 *     apiUrl: 'http://127.0.0.1',
 *     container: '#service-container',
 *     kefuGuid: 'kefu-guid',
 *     bid: 'business-id'
 *   });
 * </script>
 */

(function (window, document) {
  "use strict";

  // 默认配置
  const DEFAULT_CONFIG = {
    apiUrl: "http://127.0.0.1",
    container: "body",
    width: "100%",
    height: "600px",
    kefuGuid: "",
    bid: "",
    // CSS隔离配置
    useShadowDOM: true, // 优先使用Shadow DOM隔离
    // LayIM配置
    layimConfig: {
      uploadImage: {
        url: "/admin_api/v1/file/upload",
        type: "POST",
      },
      uploadFile: {
        url: "/admin_api/v1/file/upload",
        type: "post",
      },
      isAudio: false,
      isVideo: false,
      notice: true,
      contactsPanel: {
        // showFriend: true,
        showGroup: false,
        minStatus: true,
        done: function (elem) {
          // 设置面板相对右下角的偏移坐标
          // elem.css({
          //   margin: "-32px 0 0 -32px",
          // });
        },
      },
      voice: "/static/layim/voice/",
    },
    // 文本配置
    texts: {
      title: "客服工作台",
      noSessions: "当前没有活跃会话",
      connecting: "正在连接...",
      connected: "连接成功",
      disconnected: "连接断开",
      transferSuccess: "转接成功",
      sessionClosed: "会话已结束",
    },
  };

  class ServiceWidget {
    constructor() {
      this.config = {};
      this.isInitialized = false;
      this.layimInitialized = false;
      this.ws = null;
      this.layimInstance = null;
      this.sessionList = [];
      this.user = {};
      this.wsConnected = false;

      // DOM元素
      this.container = null;
      this.serviceContainer = null;
    }

    /**
     * 初始化客服组件
     * @param {Object} options 配置选项
     */
    init(options = {}) {
      if (this.isInitialized) {
        console.warn("[ServiceSDK] 已经初始化过了");
        return;
      }

      this.config = Object.assign({}, DEFAULT_CONFIG, options);
      this.user = {
        guid: this.config.kefuGuid,
        bid: this.config.bid,
      };

      // 立即设置LayIM资源路径（如果LayUI已存在）
      this.setLayIMResourcePath();

      // 设置全局拦截，确保资源路径正确
      this.setupGlobalLayIMPathIntercept();

      // 等待DOM加载完成
      if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => this.render());
      } else {
        this.render();
      }

      console.log("[ServiceSDK] 初始化完成", this.config);
    }

    /**
     * 渲染组件
     */
    render() {
      this.createContainer();
      this.loadDependencies().then(() => {
        this.initWebSocket();
        this.initLayIM();
        this.bindEvents();
        this.isInitialized = true;
      });
    }

    /**
     * 创建容器
     */
    createContainer() {
      const targetContainer = typeof this.config.container === "string" ? document.querySelector(this.config.container) : this.config.container;

      if (!targetContainer) {
        throw new Error("[ServiceSDK] 找不到容器元素");
      }

      this.container = targetContainer;

      // 创建Shadow DOM容器（CSS完全隔离）
      if (this.config.useShadowDOM !== false && targetContainer.attachShadow) {
        this.shadowRoot = targetContainer.attachShadow({ mode: "open" });
        this.serviceContainer = document.createElement("div");
        this.serviceContainer.className = "service-widget";
        this.shadowRoot.appendChild(this.serviceContainer);
        console.log("[ServiceSDK] 使用Shadow DOM，CSS完全隔离");
      } else {
        // 降级方案：普通DOM + CSS前缀
        this.serviceContainer = document.createElement("div");
        this.serviceContainer.className = "service-widget-isolated";
        this.container.appendChild(this.serviceContainer);
        console.log("[ServiceSDK] 使用CSS前缀隔离");
      }

      this.serviceContainer.style.cssText = `
                width: ${this.config.width};
                height: ${this.config.height};
                position: relative;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                box-sizing: border-box;
            `;

      // 添加隔离样式
      this.addIsolatedStyles();

      this.serviceContainer.innerHTML = `
                <div class="service-header" style="
                    background: #1890ff;
                    color: white;
                    padding: 15px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-radius: 8px 8px 0 0;
                ">
                    <h3 style="margin: 0; font-size: 16px;">${this.config.texts.title}</h3>
                    <div class="service-status" style="
                        padding: 4px 12px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 12px;
                        font-size: 12px;
                    ">${this.config.texts.connecting}</div>
                </div>
                <div class="service-toolbar" style="
                    background: #f8f9fa;
                    padding: 10px 20px;
                    border-bottom: 1px solid #e9ecef;
                    display: flex;
                    gap: 10px;
                ">
                    <button class="service-btn" data-action="refresh" style="
                        background: #52c41a;
                        color: white;
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">刷新会话</button>
                    <button class="service-btn" data-action="sessions" style="
                        background: #1890ff;
                        color: white;
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">会话列表</button>
                    <button class="service-btn" data-action="test" style="
                        background: #722ed1;
                        color: white;
                        border: none;
                        padding: 6px 12px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 12px;
                    ">测试消息</button>
                </div>
                <div class="layim-container" style="
                    height: calc(100% - 120px);
                    background: white;
                    border-radius: 0 0 8px 8px;
                    overflow: hidden;
                "></div>
                <div class="session-list-modal" style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0,0,0,0.5);
                    display: none;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                ">
                    <div class="session-list-content" style="
                        background: white;
                        border-radius: 8px;
                        padding: 20px;
                        max-width: 600px;
                        max-height: 80%;
                        overflow-y: auto;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0;">会话列表</h3>
                            <button class="close-modal" style="
                                background: none;
                                border: none;
                                font-size: 20px;
                                cursor: pointer;
                                color: #999;
                            ">&times;</button>
                        </div>
                        <div class="session-list-body"></div>
                    </div>
                </div>
            `;

      this.container.appendChild(this.serviceContainer);
    }

    /**
     * 检查资源是否已加载
     */
    isResourceLoaded(type, identifier) {
      if (type === "css") {
        // 检查CSS是否已加载
        const links = document.querySelectorAll('link[rel="stylesheet"]');
        return Array.from(links).some((link) => link.href.includes(identifier) || link.getAttribute("data-layui-css") === identifier);
      } else if (type === "js") {
        // 检查JS是否已加载
        const scripts = document.querySelectorAll("script[src]");
        return Array.from(scripts).some((script) => script.src.includes(identifier));
      }
      return false;
    }

    /**
     * 智能加载CSS（避免重复加载）
     */
    loadCSS(href, identifier) {
      if (this.isResourceLoaded("css", identifier)) {
        console.log(`[ServiceSDK] CSS已存在，跳过加载: ${identifier}`);
        return;
      }

      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = href;
      if (identifier) {
        link.setAttribute("data-layui-css", identifier);
      }
      document.head.appendChild(link);
      console.log(`[ServiceSDK] 加载CSS: ${href}`);
    }

    /**
     * 智能加载JS（避免重复加载）
     */
    loadScript(src, identifier) {
      return new Promise((resolve, reject) => {
        if (this.isResourceLoaded("js", identifier)) {
          console.log(`[ServiceSDK] JS已存在，跳过加载: ${identifier}`);
          resolve();
          return;
        }

        const script = document.createElement("script");
        script.src = src;
        script.onload = () => {
          console.log(`[ServiceSDK] 加载JS成功: ${src}`);

          // 如果是LayUI，立即设置资源路径
          if (identifier === "layui.js" && window.layui) {
            this.setLayIMResourcePath();
          }

          resolve();
        };
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }

    /**
     * 加载依赖库
     */
    async loadDependencies() {
      return new Promise((resolve, reject) => {
        // 检查LayUI是否已经存在
        const hasLayUI = window.layui && typeof window.layui.use === "function";
        const hasLayIM = window.layui && window.layui.layim && typeof window.layui.layim.config === "function";

        // 详细的资源检查日志
        const layuiCssLoaded = this.isResourceLoaded("css", "layui.css");
        const layimCssLoaded = this.isResourceLoaded("css", "layim.css");
        const layuiJsLoaded = this.isResourceLoaded("js", "layui.js");
        const layimJsLoaded = this.isResourceLoaded("js", "layim.js");

        console.log(`[ServiceSDK] 资源检查状态:`);
        console.log(`  - LayUI JS: ${hasLayUI} (文件: ${layuiJsLoaded})`);
        console.log(`  - LayIM JS: ${hasLayIM} (文件: ${layimJsLoaded})`);
        console.log(`  - LayUI CSS: ${layuiCssLoaded}`);
        console.log(`  - LayIM CSS: ${layimCssLoaded}`);

        if (hasLayUI && hasLayIM) {
          console.log("[ServiceSDK] LayUI和LayIM已存在，跳过加载");
          // 确保设置资源路径
          this.setLayIMResourcePath();
          resolve();
          return;
        }

        // 需要加载的资源列表
        const loadPromises = [];

        // 1. 加载LayUI CSS（如果未加载）
        if (!this.isResourceLoaded("css", "layui.css")) {
          this.loadCSS("/static/layim/demo/layui/css/layui.css", "layui.css");
        }

        // 2. 加载LayIM CSS（如果未加载）
        if (!this.isResourceLoaded("css", "layim.css")) {
          this.loadCSS("/static/layim/dist/res/layim.css", "layim.css");
        }

        // 3. 加载LayUI JS（如果未加载）
        if (!hasLayUI) {
          loadPromises.push(this.loadScript("/static/layim/demo/layui/layui.js", "layui.js"));
        }

        // 4. 加载LayIM JS（如果未加载）
        if (!hasLayIM) {
          // 在加载LayIM之前先设置资源路径
          loadPromises.push(
            this.loadScript("/static/layim/demo/layui/layui.js", "layui.js").then(() => {
              // 确保LayUI加载完成后再设置路径
              if (window.layui) {
                if (!window.layui.cache) {
                  window.layui.cache = {};
                }
                window.layui.cache.layimResPath = "/static/layim/dist/res/";
                console.log("[ServiceSDK] 预设LayIM资源路径");
              }
              // 然后加载LayIM
              return this.loadScript("/static/layim/dist/layim.js", "layim.js");
            })
          );
        } else {
          // 如果LayUI已存在但LayIM未加载，直接加载LayIM
          loadPromises.push(this.loadScript("/static/layim/dist/layim.js", "layim.js"));
        }

        // 如果没有需要加载的JS，直接resolve
        if (loadPromises.length === 0) {
          console.log("[ServiceSDK] 所有依赖已存在");
          resolve();
          return;
        }

        // 等待所有JS加载完成
        Promise.all(loadPromises)
          .then(() => {
            // 立即设置LayIM资源路径，在LayIM初始化之前
            this.setLayIMResourcePath();

            // 等待LayUI模块完全加载
            setTimeout(() => {
              // 再次确保资源路径设置
              this.setLayIMResourcePath();

              if (window.layui && window.layui.layim) {
                console.log("[ServiceSDK] 所有依赖加载完成");
                resolve();
              } else {
                console.error("[ServiceSDK] LayIM模块加载失败");
                reject(new Error("LayIM模块加载失败"));
              }
            }, 500);
          })
          .catch((error) => {
            console.error("[ServiceSDK] 依赖加载失败:", error);
            this.handleLoadError(error, resolve, reject);
          });
      });
    }

    /**
     * 设置LayIM资源路径
     */
    setLayIMResourcePath() {
      if (window.layui) {
        if (!window.layui.cache) {
          window.layui.cache = {};
        }
        window.layui.cache.layimResPath = "/static/layim/dist/res/";

        // 强制设置，防止被覆盖
        Object.defineProperty(window.layui.cache, "layimResPath", {
          value: "/static/layim/dist/res/",
          writable: true,
          configurable: true,
        });

        console.log("[ServiceSDK] LayIM资源路径已强制设置:", window.layui.cache.layimResPath);
      } else {
        console.warn("[ServiceSDK] LayUI未加载，无法设置资源路径");
      }
    }

    /**
     * 设置全局LayIM路径拦截
     */
    setupGlobalLayIMPathIntercept() {
      // 拦截可能的资源加载
      const originalCreateElement = document.createElement;
      document.createElement = function (tagName) {
        const element = originalCreateElement.call(this, tagName);

        if (tagName.toLowerCase() === "link" && element.rel === "stylesheet") {
          // 拦截CSS加载，修正LayIM CSS路径
          const originalSetAttribute = element.setAttribute;
          element.setAttribute = function (name, value) {
            if (name === "href" && value && value.includes("layim.css") && value.includes("undefined")) {
              value = "/static/layim/dist/res/layim.css";
              console.log("[ServiceSDK] 修正LayIM CSS路径:", value);
            }
            return originalSetAttribute.call(this, name, value);
          };
        }

        return element;
      };

      console.log("[ServiceSDK] 全局LayIM路径拦截已设置");
    }

    /**
     * 初始化WebSocket连接
     */
    initWebSocket() {
      // 优先尝试监听原生客服页面的消息
      this.tryListenToNativeKefuPage();

      // 检查是否已有全局ChatCore
      if (window.ChatCore) {
        console.log("[ServiceSDK] 发现已存在的ChatCore，复用连接");
        this.ws = window.ChatCore.ws;
        this.wsConnected = window.ChatCore.isConnected();
        this.bindWebSocketEvents();
        this.updateStatus(this.wsConnected ? "connected" : "connecting");

        // 检查WebSocket连接状态
        if (this.ws) {
          console.log(`[ServiceSDK] 复用WebSocket状态: ${this.ws.readyState} (0=连接中, 1=已连接, 2=关闭中, 3=已关闭)`);
          console.log(`[ServiceSDK] WebSocket URL: ${this.ws.url}`);
        }
        return;
      }

      // 动态加载ChatCore
      const script = document.createElement("script");
      script.src = "/static/js/chatCore.js";
      script.onload = () => {
        if (window.ChatCore) {
          // 使用connectWS方法，传入正确的回调函数
          const onConnected = () => {
            console.log("[ServiceSDK] ChatCore WebSocket连接成功");
            this.wsConnected = true;
            this.updateStatus("connected");

            // 检查WebSocket连接信息
            if (window.ChatCore.ws) {
              console.log(`[ServiceSDK] 新建WebSocket状态: ${window.ChatCore.ws.readyState}`);
              console.log(`[ServiceSDK] 新建WebSocket URL: ${window.ChatCore.ws.url}`);
            }
          };

          const onClosed = () => {
            console.log("[ServiceSDK] ChatCore WebSocket连接关闭");
            this.wsConnected = false;
            this.updateStatus("disconnected");
          };

          const onError = (error) => {
            console.error("[ServiceSDK] ChatCore WebSocket错误:", error);
            this.wsConnected = false;
            this.updateStatus("error");
          };

          // ChatCore的connectWS方法签名: connectWS(onOpen, onClose, onError)
          window.ChatCore.connectWS(onConnected, onClosed, onError);

          this.ws = window.ChatCore.ws;
          this.bindWebSocketEvents();
        } else {
          console.error("[ServiceSDK] ChatCore加载失败");
          this.showNotification("WebSocket连接模块初始化失败", "error");
        }
      };
      script.onerror = () => {
        console.error("[ServiceSDK] ChatCore文件加载失败，请检查 /static/js/chatCore.js 是否存在");
        this.showNotification("WebSocket连接模块加载失败", "error");
      };
      document.head.appendChild(script);
    }

    /**
     * 处理依赖加载错误
     */
    handleLoadError(error, resolve, reject) {
      console.error("[ServiceSDK] 依赖文件加载失败:", error);

      // 检查哪些资源缺失
      const missingResources = [];

      if (!this.isResourceLoaded("css", "layui.css")) {
        missingResources.push("LayUI CSS: /static/layim/demo/layui/css/layui.css");
      }

      if (!this.isResourceLoaded("css", "layim.css")) {
        missingResources.push("LayIM CSS: /static/layim/dist/res/layim.css");
      }

      if (!window.layui || typeof window.layui.use !== "function") {
        missingResources.push("LayUI JS: /static/layim/demo/layui/layui.js");
      }

      if (!window.layui || !window.layui.layim) {
        missingResources.push("LayIM JS: /static/layim/dist/layim.js");
      }

      if (missingResources.length > 0) {
        console.error("[ServiceSDK] 缺失的资源文件:");
        missingResources.forEach((resource) => console.error(`- ${resource}`));
      }

      // 显示错误提示
      this.showNotification(`LayIM依赖加载失败，缺失${missingResources.length}个文件`, "error");

      // 尝试使用已存在的LayUI/LayIM
      if (window.layui && window.layui.layim) {
        console.log("[ServiceSDK] 检测到已存在的LayIM，继续初始化");
        // 确保设置资源路径
        if (window.layui.cache) {
          window.layui.cache.layimResPath = "/static/layim/dist/res/";
        }
        resolve();
      } else {
        reject(error);
      }
    }

    /**
     * 绑定WebSocket事件
     */
    bindWebSocketEvents() {
      if (window.ChatCore) {
        // 连接状态变化
        window.ChatCore.on("connected", () => {
          this.wsConnected = true;
          this.updateStatus("connected");
          this.initKefu();
        });

        window.ChatCore.on("disconnected", () => {
          this.wsConnected = false;
          this.updateStatus("disconnected");
        });

        // 添加原始WebSocket消息拦截
        this.interceptWebSocketMessages();

        // 注释掉重复的ChatCore事件监听，使用WebSocket拦截器统一处理
        // window.ChatCore.on("chatMessage", (data) => {
        //   console.log("[ServiceSDK] 收到chatMessage:", data);
        //   this.handleIncomingMessage(data);
        // });

        // window.ChatCore.on("memberMessage", (data) => {
        //   console.log("[ServiceSDK] 收到memberMessage:", data);
        //   this.handleIncomingMessage(data);
        // });

        // window.ChatCore.on("message", (data) => {
        //   console.log("[ServiceSDK] 收到message:", data);
        //   this.handleIncomingMessage(data);
        // });

        // 新会话
        window.ChatCore.on("newSession", (data) => {
          console.log("[ServiceSDK] 收到newSession:", data);
          this.handleNewSession(data);
        });

        // 会话更新
        window.ChatCore.on("sessionUpdate", (data) => {
          console.log("[ServiceSDK] 收到sessionUpdate:", data);
          this.handleSessionUpdate(data);
        });

        // 添加调试监听，监听可能的消息类型
        const possibleMessageTypes = [
          "memberMessage",
          "userMessage",
          "customerMessage",
          "newMessage",
          "receiveMessage",
          "messageReceived",
          "kefuMessage",
          "memberChat",
          "customerChat",
          "messageFromMember",
          "messageToKefu",
        ];

        // 注释掉批量监听，避免重复处理
        // possibleMessageTypes.forEach((type) => {
        //   window.ChatCore.on(type, (data) => {
        //     console.log(`[ServiceSDK] 收到${type}消息:`, data);
        //     this.handleIncomingMessage(data);
        //   });
        // });
      }
    }

    /**
     * 拦截WebSocket原始消息
     */
    interceptWebSocketMessages() {
      if (window.ChatCore && window.ChatCore.ws) {
        const originalOnMessage = window.ChatCore.ws.onmessage;

        window.ChatCore.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log("[ServiceSDK] 拦截到原始WebSocket消息:", data);

            // 临时调试：记录所有消息类型
            if (data && data.cmd) {
              console.log(`[ServiceSDK] 🔍 所有消息类型记录: ${data.cmd}`, data);
            }

            // 检查是否是访客消息
            if (data && data.cmd && data.data) {
              console.log(`[ServiceSDK] 消息类型: ${data.cmd}`, data.data);

              // 检查多种可能的访客消息类型
              const isVisitorMessage =
                data.data.from_type === "member" ||
                data.data.from_type === 1 || // 访客类型
                data.data.from_type === "1" || // 字符串类型
                data.cmd === "memberMessage" ||
                data.cmd === "customerMessage" ||
                (data.cmd === "chatMessage" && data.data.from_type === 1) || // 访客发送的聊天消息
                data.cmd === "newMessage" ||
                (data.data.from_guid && data.data.from_guid !== this.user.guid && data.data.from_type !== 2); // 不是客服发送的

              // 只有在不是加载历史消息时才处理新消息
              if (isVisitorMessage && !this.isLoadingHistory) {
                console.log("[ServiceSDK] 检测到访客消息，直接处理");
                this.handleIncomingMessage(data);
              } else if (isVisitorMessage && this.isLoadingHistory) {
                console.log("[ServiceSDK] 跳过历史消息处理，避免重复通知");
              }
            }
          } catch (e) {
            console.error("[ServiceSDK] 解析WebSocket消息失败:", e);
          }

          // 调用原始处理函数
          if (originalOnMessage) {
            originalOnMessage.call(window.ChatCore.ws, event);
          }
        };

        console.log("[ServiceSDK] WebSocket消息拦截已设置");
      } else {
        // 如果WebSocket还没准备好，延迟设置
        setTimeout(() => {
          this.interceptWebSocketMessages();
        }, 1000);
      }
    }

    /**
     * 尝试监听原生客服页面的消息
     */
    tryListenToNativeKefuPage() {
      // 检查是否有原生客服页面的iframe或窗口
      const kefuFrames = document.querySelectorAll('iframe[src*="/admin/kefu"]');

      if (kefuFrames.length > 0) {
        console.log("[ServiceSDK] 发现原生客服页面iframe，尝试监听消息");

        // 注释掉iframe消息监听，避免重复处理
        // window.addEventListener("message", (event) => {
        //   if (event.data && event.data.type === "kefu-message") {
        //     console.log("[ServiceSDK] 收到来自原生客服页面的消息:", event.data);
        //     this.handleIncomingMessage(event.data.data);
        //   }
        // });
      }

      // 尝试打开一个隐藏的原生客服页面来监听消息
      this.createHiddenKefuListener();
    }

    /**
     * 创建隐藏的客服页面监听器
     */
    createHiddenKefuListener() {
      console.log("[ServiceSDK] 创建隐藏的客服页面监听器");

      const iframe = document.createElement("iframe");
      iframe.src = "/admin/kefu/chat";
      iframe.style.display = "none";
      iframe.style.width = "0";
      iframe.style.height = "0";
      iframe.style.border = "none";

      iframe.onload = () => {
        console.log("[ServiceSDK] 隐藏客服页面加载完成");

        // 监听来自隐藏iframe的消息
        window.addEventListener("message", (event) => {
          if (event.source === iframe.contentWindow && event.data && event.data.source === "kefu-iframe") {
            console.log("[ServiceSDK] 收到来自隐藏客服页面的消息:", event.data);

            if (event.data.type === "new-message") {
              // 注释掉，避免重复处理，使用WebSocket拦截器统一处理
              // this.handleIncomingMessage(event.data.data);
              console.log("[ServiceSDK] 跳过iframe消息处理，使用WebSocket拦截器");
            }
          }
        });
      };

      document.body.appendChild(iframe);
      this.hiddenKefuFrame = iframe;
    }

    /**
     * 初始化客服
     */
    initKefu() {
      if (window.ChatCore && this.wsConnected) {
        const initData = {
          kefu_guid: this.user.guid,
          name: "SDK客服", // 添加名称
          avatar: "/static/img/service.png", // 添加头像
          bid: this.user.bid,
        };

        console.log("[ServiceSDK] 发送客服初始化:", initData);
        window.ChatCore.sendMsg("kefuInit", initData);
      } else {
        console.warn("[ServiceSDK] 无法初始化客服 - ChatCore:", !!window.ChatCore, "连接状态:", this.wsConnected);
      }
    }

    /**
     * 初始化LayIM
     */
    initLayIM() {
      if (!window.layui || !window.layui.layim) {
        console.error("[ServiceSDK] LayIM未加载");
        return;
      }

      // 检查LayIM是否已经初始化过
      if (this.layimInitialized) {
        console.log("[ServiceSDK] LayIM已初始化，跳过重复初始化");
        return;
      }

      // 确保资源路径正确设置
      this.setLayIMResourcePath();

      const layim = window.layui.layim;

      // 加载会话数据
      this.loadSessionsForLayIM()
        .then((initData) => {
          // 初始化LayIM
          layim.config({
            init: initData,
            ...this.config.layimConfig,
            // 自定义工具栏
            tool: [
              {
                alias: "transfer",
                title: "转接会话",
                icon: "&#xe612;",
              },
              {
                alias: "close",
                title: "结束会话",
                icon: "&#x1006;",
              },
              {
                alias: "quickReply",
                title: "快捷回复",
                icon: "&#xe611;",
              },
            ],
          });

          // 监听LayIM发送消息事件
          layim.on("sendMessage", (data) => {
            console.log("[ServiceSDK] 发送消息:", data);
            const receiver = data && data.receiver;
            const user = data && data.user;
            if (receiver && receiver.id && user && user.content) {
              this.sendMessage(user.content, receiver.id);
            }
          });

          // 监听会话切换 - 添加防抖机制
          let switchSessionTimeout = null;
          layim.on("chatChange", (res) => {
            console.log("[ServiceSDK] 会话切换:", res);
            const chatData = res && res.data;
            if (chatData && chatData.id) {
              // 清除之前的定时器，防止重复触发
              if (switchSessionTimeout) {
                clearTimeout(switchSessionTimeout);
              }
              // 延迟执行，避免重复触发
              switchSessionTimeout = setTimeout(() => {
                this.switchSession(chatData.id);
                switchSessionTimeout = null;
              }, 100);
            }
          });

          // 监听工具栏点击
          layim.on("tool(transfer)", (obj) => {
            this.handleTransferSession(obj);
          });

          layim.on("tool(close)", (obj) => {
            this.handleCloseSession(obj);
          });

          layim.on("tool(quickReply)", (obj) => {
            this.handleQuickReply(obj);
          });

          this.layimInstance = layim;
          this.layimInitialized = true;
          console.log("[ServiceSDK] LayIM初始化完成");

          // 注释掉自动打开第一个会话，让用户手动选择
          // if (this.sessionList.length > 0) {
          //   this.openFirstSession();
          // }
        })
        .catch((error) => {
          console.error("[ServiceSDK] LayIM初始化失败:", error);
        });
    }

    /**
     * 加载会话数据供LayIM使用
     */
    loadSessionsForLayIM() {
      return new Promise((resolve, reject) => {
        console.log("[ServiceSDK] 开始加载会话列表...");

        this.ajax("/admin_api/v1/kefu/chat_list", {
          method: "POST",
        })
          .then((res) => {
            console.log("[ServiceSDK] 会话列表API响应:", res);

            // 检查响应格式
            if (!res || typeof res !== "object") {
              throw new Error("响应格式错误：不是有效的JSON对象");
            }

            if (res.code !== 0) {
              throw new Error(`API返回错误：${res.msg || "未知错误"}`);
            }

            if (!res.data) {
              console.warn("[ServiceSDK] 响应中缺少data字段，使用空数据");
              res.data = {};
            }

            const sessions = res.data.session_list || [];
            this.sessionList = sessions;
            console.log(`[ServiceSDK] 成功加载${sessions.length}个会话`);

            // 转换为LayIM好友格式
            const friends = sessions.map((session) => ({
              username: session.member_name || "访客",
              id: session.guid,
              avatar: session.member_avatar || "/static/img/default_head.png",
              sign: session.last_message || "暂无消息",
              status: session.status === 0 ? "online" : "offline",
            }));

            resolve({
              mine: {
                username: "客服",
                id: this.user.guid,
                status: "online",
                sign: "为您服务",
                avatar: "/static/img/service.png",
              },
              friend: [
                {
                  groupname: "客服会话",
                  id: 1,
                  list: friends,
                },
              ],
              group: [],
            });
          })
          .catch((error) => {
            console.error("[ServiceSDK] 加载会话列表失败:", error);

            // 提供降级方案：返回空的初始化数据
            const fallbackData = {
              mine: {
                username: "客服",
                id: this.user.guid || "kefu-001",
                status: "online",
                sign: "为您服务",
                avatar: "/static/img/service.png",
              },
              friend: [
                {
                  groupname: "客服会话",
                  id: 1,
                  list: [],
                },
              ],
              group: [],
            };

            console.log("[ServiceSDK] 使用降级数据初始化LayIM");
            resolve(fallbackData);
          });
      });
    }

    /**
     * 发送消息
     */
    sendMessage(content, sessionGuid) {
      if (!sessionGuid || !content.trim()) return;

      // 找到对应的会话信息，获取访客GUID
      const session = this.sessionList.find((s) => s.guid === sessionGuid);
      if (!session) {
        console.error("[ServiceSDK] 找不到会话信息:", sessionGuid);
        return;
      }

      // 通过WebSocket发送消息
      if (this.wsConnected && window.ChatCore) {
        const messageData = {
          content: content,
          session_guid: sessionGuid,
          from_type: 2, // 客服发送
          from_guid: this.user.guid, // 发送者GUID（客服）
          to_guid: session.member_guid, // 接收者GUID（访客）
          bid: this.user.bid, // 商户ID
          msg_type: 1, // 文本消息
        };

        console.log("[ServiceSDK] 发送消息数据:", messageData);
        window.ChatCore.sendMsg("chatMessage", messageData);
      }
    }

    /**
     * 处理收到的消息
     */
    handleIncomingMessage(data) {
      console.log("[ServiceSDK] 收到新消息:", data);

      if (this.layimInstance) {
        // 提取实际的消息数据
        const msgData = data.data || data;

        // 转换为LayIM消息格式
        const message = {
          username: msgData.member_name || msgData.from_name || msgData.from || "访客",
          avatar: msgData.member_avatar || msgData.from_avatar || "/static/img/default_head.png",
          id: msgData.sessionId || msgData.session_guid || msgData.guid || msgData.session_id || "visitor-session",
          type: "friend",
          content: msgData.content || msgData.message,
          timestamp: msgData.timestamp || new Date().getTime(),
        };

        console.log("[ServiceSDK] 转换后的消息格式:", message);

        // 检查必要字段
        if (message.id && message.content) {
          // 发送到LayIM
          this.layimInstance.getMessage(message);
          console.log("[ServiceSDK] 消息已发送到LayIM");

          // 显示通知
          this.showNotification(`收到来自 ${message.username} 的消息`, "info");

          // 通知主页面有新消息
          this.notifyParentWindow("new-message", {
            sessionId: msgData.sessionId || msgData.session_guid,
            message: message.content,
            from: message.username,
            timestamp: message.timestamp,
          });
        } else {
          console.warn("[ServiceSDK] 消息数据不完整:", message);
          console.warn("[ServiceSDK] 原始数据:", msgData);

          // 尝试使用默认ID
          if (message.content && !message.id) {
            message.id = msgData.sessionId || "default-session";
            console.log("[ServiceSDK] 使用默认ID重试:", message);
            this.layimInstance.getMessage(message);
          }
        }
      }
    }

    /**
     * 通知主页面
     */
    notifyParentWindow(type, data) {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage(
          {
            type: type,
            data: data,
            source: "service-sdk",
          },
          "*"
        );
        console.log(`[ServiceSDK] 已通知主页面: ${type}`, data);
      }
    }

    /**
     * 处理新会话
     */
    handleNewSession(data) {
      console.log("[ServiceSDK] 新会话:", data);
      // 刷新会话列表
      this.refreshSessions();
      // 显示通知
      this.showNotification("有新的访客会话", "info");

      // 通知主页面有新会话
      this.notifyParentWindow("new-session", {
        sessionId: data.session_guid,
        message: "有新的访客会话",
        timestamp: Date.now(),
      });
    }

    /**
     * 处理会话更新
     */
    handleSessionUpdate(data) {
      console.log("[ServiceSDK] 会话更新:", data);
      // 刷新会话列表
      this.refreshSessions();
    }

    /**
     * 切换会话
     */
    switchSession(sessionGuid) {
      console.log("[ServiceSDK] 切换会话:", sessionGuid);

      // 检查是否已经加载过这个会话的历史消息
      if (this.loadedHistorySessions && this.loadedHistorySessions.includes(sessionGuid)) {
        console.log("[ServiceSDK] 会话历史消息已加载，跳过重复加载:", sessionGuid);
        return;
      }

      // 初始化已加载会话记录
      if (!this.loadedHistorySessions) {
        this.loadedHistorySessions = [];
      }

      // 加载历史消息
      this.loadChatHistory(sessionGuid)
        .then((messages) => {
          console.log("[ServiceSDK] 历史消息加载完成:", messages);
          // 将历史消息添加到聊天窗口
          if (this.layimInstance && messages && messages.length > 0) {
            // 设置标记，表示正在加载历史消息，避免触发新消息处理
            this.isLoadingHistory = true;

            messages.forEach((msg) => {
              console.log("[ServiceSDK] 显示历史消息:", msg);
              // 标记为历史消息，不触发新消息通知
              msg.isHistory = true;
              this.layimInstance.getMessage(msg);
            });

            // 标记这个会话的历史消息已加载
            this.loadedHistorySessions.push(sessionGuid);

            // 延迟清除标记
            setTimeout(() => {
              this.isLoadingHistory = false;
            }, 1000);
          }
        })
        .catch((err) => {
          console.error("[ServiceSDK] 历史消息加载失败:", err);
        });
    }

    /**
     * 加载聊天历史记录
     */
    loadChatHistory(sessionGuid) {
      console.log("[ServiceSDK] 开始加载历史消息:", sessionGuid);

      return new Promise((resolve, reject) => {
        this.ajax("/admin_api/v1/kefu/message_list", {
          data: {
            session_guid: sessionGuid,
            page: 1,
            limit: 50,
          },
        })
          .then((response) => {
            console.log("[ServiceSDK] 历史消息API响应:", response);

            if (response.code === 0 && response.data && response.data.data) {
              console.log("[ServiceSDK] 当前用户ID:", this.user.guid);
              console.log("[ServiceSDK] 会话ID:", sessionGuid);

              const messages = response.data.data.map((msg) => {
                // 转换为LayIM格式
                // LayIM通过比较消息的id和当前用户id来判断是否为自己的消息
                const isMyMessage = msg.from_type === 2; // 客服发送的消息
                const messageId = isMyMessage ? this.user.guid : sessionGuid;

                console.log(`[ServiceSDK] 消息转换: from_type=${msg.from_type}, isMyMessage=${isMyMessage}, messageId=${messageId}, content=${msg.content}`);

                const messageObj = {
                  username: msg.from_type === 1 ? msg.from_name || "访客" : msg.from_name || "客服",
                  avatar: msg.from_type === 1 ? msg.from_avatar || "/static/img/default_head.png" : msg.from_avatar || "/static/img/default_head.png",
                  id: messageId, // 如果是我的消息，使用我的ID，否则使用会话ID
                  type: "friend",
                  content: msg.content,
                  timestamp: new Date(msg.send_time).getTime(),
                };

                // 如果是客服发送的消息，添加mine标记（尝试多种方式）
                if (isMyMessage) {
                  messageObj.mine = true;
                  messageObj.user = true;
                  // 确保使用当前用户的ID
                  messageObj.id = this.user.guid;
                }

                return messageObj;
              });

              console.log("[ServiceSDK] 转换后的历史消息:", messages);

              // 调试：检查LayIM内部的用户信息
              if (this.layimInstance && this.layimInstance.cache) {
                console.log("[ServiceSDK] LayIM缓存的用户信息:", this.layimInstance.cache());
              }

              resolve(messages);
            } else {
              console.warn("[ServiceSDK] 历史消息加载失败:", response);
              resolve([]);
            }
          })
          .catch((error) => {
            console.error("[ServiceSDK] 历史消息加载错误:", error);
            reject(error);
          });
      });
    }

    /**
     * 转接会话
     */
    handleTransferSession(obj) {
      console.log("[ServiceSDK] 转接会话:", obj);

      // 获取当前聊天对象
      const receiver = obj.data;
      if (!receiver || !receiver.id) {
        this.showNotification("请先选择要转接的会话", "error");
        return;
      }

      // 这里可以弹出转接对话框，选择目标客服
      const targetKefu = prompt("请输入要转接的客服ID:");
      if (targetKefu) {
        // 发送转接请求
        if (window.ChatCore && this.wsConnected) {
          window.ChatCore.sendMsg("transferSession", {
            session_guid: receiver.id,
            from_user_guid: this.user.guid,
            to_user_guid: targetKefu,
            transfer_reason: "客服主动转接",
            bid: this.user.bid,
          });
          this.showNotification("转接请求已发送", "success");
        }
      }
    }

    /**
     * 结束会话
     */
    handleCloseSession(obj) {
      console.log("[ServiceSDK] 结束会话:", obj);

      const receiver = obj.data;
      if (!receiver || !receiver.id) {
        this.showNotification("请先选择要结束的会话", "error");
        return;
      }

      if (confirm("确定要结束当前会话吗？")) {
        // 发送结束会话请求
        if (window.ChatCore && this.wsConnected) {
          window.ChatCore.sendMsg("closeSession", {
            session_guid: receiver.id,
            kefu_guid: this.user.guid,
            bid: this.user.bid,
          });
          this.showNotification("会话已结束", "success");
        }
      }
    }

    /**
     * 快捷回复
     */
    handleQuickReply(obj) {
      console.log("[ServiceSDK] 快捷回复:", obj);

      const receiver = obj.data;
      if (!receiver || !receiver.id) {
        this.showNotification("请先选择会话", "error");
        return;
      }

      // 预设的快捷回复
      const quickReplies = ["您好，有什么可以帮助您的吗？", "请稍等，我为您查询一下。", "感谢您的咨询，还有其他问题吗？", "很高兴为您服务！"];

      // 创建快捷回复选择界面
      this.showQuickReplyModal(quickReplies, (selectedReply) => {
        if (selectedReply) {
          // 发送选中的快捷回复
          this.sendMessage(selectedReply, receiver.id);
        }
      });
    }

    /**
     * 绑定事件
     */
    bindEvents() {
      // 工具栏按钮事件
      this.serviceContainer.addEventListener("click", (e) => {
        const action = e.target.dataset.action;
        switch (action) {
          case "refresh":
            this.refreshSessions();
            break;
          case "sessions":
            this.showSessionList();
            break;
          case "test":
            this.testReceiveMessage();
            break;
        }
      });

      // 会话列表模态框事件
      const modal = this.serviceContainer.querySelector(".session-list-modal");
      const closeBtn = modal.querySelector(".close-modal");

      closeBtn.addEventListener("click", () => {
        modal.style.display = "none";
      });

      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          modal.style.display = "none";
        }
      });
    }

    /**
     * 刷新会话列表
     */
    refreshSessions() {
      this.loadSessionsForLayIM()
        .then(() => {
          this.showNotification("会话列表已刷新", "success");
          console.log("[ServiceSDK] 会话列表已刷新");
        })
        .catch((error) => {
          console.error("[ServiceSDK] 刷新会话列表失败:", error);
          this.showNotification("刷新失败", "error");
        });
    }

    /**
     * 显示会话列表
     */
    showSessionList() {
      const modal = this.serviceContainer.querySelector(".session-list-modal");
      const listBody = modal.querySelector(".session-list-body");

      if (this.sessionList.length === 0) {
        listBody.innerHTML = `<p style="text-align: center; color: #999; padding: 40px;">${this.config.texts.noSessions}</p>`;
      } else {
        const listHTML = this.sessionList
          .map(
            (session) => `
                    <div style="
                        border: 1px solid #e9ecef;
                        border-radius: 6px;
                        padding: 15px;
                        margin-bottom: 10px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    ">
                        <div style="flex: 1;">
                            <div style="font-weight: 500; margin-bottom: 5px;">
                                ${session.member_name || "访客"}
                                <span style="color: #999; font-size: 12px;">(${session.guid})</span>
                            </div>
                            <div style="color: #666; font-size: 12px;">
                                开始时间: ${session.start_time || "未知"}
                            </div>
                            <div style="color: #666; font-size: 12px;">
                                最后消息: ${session.last_message || "暂无消息"}
                            </div>
                        </div>
                        <div>
                            <button onclick="window.ServiceWidget.openSessionChat('${session.guid}')" style="
                                background: #1890ff;
                                color: white;
                                border: none;
                                padding: 6px 12px;
                                border-radius: 4px;
                                cursor: pointer;
                                font-size: 12px;
                                margin-right: 5px;
                            ">打开聊天</button>
                        </div>
                    </div>
                `
          )
          .join("");

        listBody.innerHTML = listHTML;
      }

      modal.style.display = "flex";
    }

    /**
     * 打开指定会话聊天
     */
    openSessionChat(sessionGuid) {
      const session = this.sessionList.find((s) => s.guid === sessionGuid);
      if (session && this.layimInstance) {
        this.layimInstance.chat({
          username: session.member_name || "访客",
          type: "friend",
          avatar: session.member_avatar || "/static/img/default_head.png",
          id: session.guid,
        });

        // 关闭模态框
        const modal = this.serviceContainer.querySelector(".session-list-modal");
        modal.style.display = "none";
      }
    }

    /**
     * 打开第一个会话
     */
    openFirstSession() {
      if (this.sessionList.length === 0) {
        console.log("[ServiceSDK] 没有活跃会话");
        return;
      }

      const firstSession = this.sessionList[0];
      if (this.layimInstance) {
        this.layimInstance.chat({
          username: firstSession.member_name || "访客",
          type: "friend",
          avatar: firstSession.member_avatar || "/static/img/default_head.png",
          id: firstSession.guid,
        });
      }
    }

    /**
     * 测试接收消息
     */
    testReceiveMessage() {
      if (!this.layimInstance) {
        this.showNotification("LayIM未初始化", "error");
        return;
      }

      // 模拟消息数据
      const testMessage = {
        username: "测试访客",
        avatar: "/static/img/default_head.png",
        id: "test-session-001",
        type: "friend",
        content: "这是一条测试消息：" + new Date().toLocaleTimeString(),
        timestamp: new Date().getTime(),
      };

      console.log("[ServiceSDK] 发送测试消息:", testMessage);
      this.layimInstance.getMessage(testMessage);
      this.showNotification("已发送测试消息", "success");
    }

    /**
     * 显示快捷回复模态框
     */
    showQuickReplyModal(replies, callback) {
      const modal = document.createElement("div");
      modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

      const content = document.createElement("div");
      content.style.cssText = `
                background: white;
                border-radius: 8px;
                padding: 20px;
                max-width: 400px;
                max-height: 80%;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;

      content.innerHTML = `
                <h3 style="margin: 0 0 15px 0;">选择快捷回复</h3>
                ${replies
                  .map(
                    (reply, index) => `
                    <div class="quick-reply-item" data-index="${index}" style="
                        padding: 10px;
                        border: 1px solid #e9ecef;
                        border-radius: 4px;
                        margin-bottom: 8px;
                        cursor: pointer;
                        transition: background 0.2s;
                    " onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='white'">
                        ${reply}
                    </div>
                `
                  )
                  .join("")}
                <div style="text-align: right; margin-top: 15px;">
                    <button class="cancel-btn" style="
                        background: #ccc;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        margin-right: 10px;
                    ">取消</button>
                </div>
            `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      // 绑定事件
      content.addEventListener("click", (e) => {
        if (e.target.classList.contains("quick-reply-item")) {
          const index = parseInt(e.target.dataset.index);
          callback(replies[index]);
          document.body.removeChild(modal);
        } else if (e.target.classList.contains("cancel-btn")) {
          callback(null);
          document.body.removeChild(modal);
        }
      });

      modal.addEventListener("click", (e) => {
        if (e.target === modal) {
          callback(null);
          document.body.removeChild(modal);
        }
      });
    }

    /**
     * 更新状态显示
     */
    updateStatus(status) {
      const statusEl = this.serviceContainer.querySelector(".service-status");
      const statusTexts = {
        connecting: this.config.texts.connecting,
        connected: this.config.texts.connected,
        disconnected: this.config.texts.disconnected,
      };

      const statusColors = {
        connecting: "#faad14",
        connected: "#52c41a",
        disconnected: "#ff4d4f",
      };

      statusEl.textContent = statusTexts[status] || status;
      statusEl.style.background = statusColors[status] || "#ccc";
    }

    /**
     * 显示通知
     */
    showNotification(message, type = "info") {
      // 创建通知元素
      const notification = document.createElement("div");
      notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === "success" ? "#52c41a" : type === "error" ? "#ff4d4f" : "#1890ff"};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10001;
                font-size: 14px;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideInRight 0.3s ease;
            `;

      notification.textContent = message;
      document.body.appendChild(notification);

      // 3秒后自动移除
      setTimeout(() => {
        if (notification.parentNode) {
          notification.style.animation = "slideOutRight 0.3s ease";
          setTimeout(() => {
            document.body.removeChild(notification);
          }, 300);
        }
      }, 3000);

      // 添加动画样式
      if (!document.querySelector("#service-notification-styles")) {
        const style = document.createElement("style");
        style.id = "service-notification-styles";
        style.textContent = `
                    @keyframes slideInRight {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOutRight {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
        document.head.appendChild(style);
      }
    }

    /**
     * AJAX请求工具
     */
    ajax(url, options = {}) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        const method = options.method || "POST";
        const data = options.data || null;

        xhr.open(method, url, true);
        xhr.setRequestHeader("Content-Type", "application/json");

        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                // 检查响应内容
                if (!xhr.responseText) {
                  reject(new Error("服务器返回空响应"));
                  return;
                }

                const response = JSON.parse(xhr.responseText);
                resolve(response);
              } catch (e) {
                console.error("[ServiceSDK] JSON解析失败:", {
                  url: url,
                  status: xhr.status,
                  responseText: xhr.responseText,
                  error: e.message,
                });
                reject(new Error(`解析响应失败: ${e.message}`));
              }
            } else {
              console.error("[ServiceSDK] 请求失败:", {
                url: url,
                status: xhr.status,
                statusText: xhr.statusText,
                responseText: xhr.responseText,
              });
              reject(new Error(`请求失败: ${xhr.status} ${xhr.statusText}`));
            }
          }
        };

        xhr.onerror = () => reject(new Error("网络错误"));

        if (data) {
          xhr.send(JSON.stringify(data));
        } else {
          xhr.send();
        }
      });
    }

    /**
     * 公共API：刷新会话
     */
    refresh() {
      this.refreshSessions();
    }

    /**
     * 公共API：发送消息到指定会话
     */
    sendToSession(sessionGuid, content) {
      this.sendMessage(content, sessionGuid);
    }

    /**
     * 公共API：打开会话聊天
     */
    openChat(sessionGuid) {
      this.openSessionChat(sessionGuid);
    }

    /**
     * 公共API：获取会话列表
     */
    getSessions() {
      return this.sessionList;
    }

    /**
     * 公共API：获取连接状态
     */
    isConnected() {
      return this.wsConnected;
    }

    /**
     * 添加隔离样式
     */
    addIsolatedStyles() {
      const styles = `
        /* 重置所有样式，避免继承污染 */
        .service-widget, .service-widget * {
          all: unset;
          box-sizing: border-box;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 恢复必要的显示属性 */
        .service-widget {
          display: block;
          position: relative;
        }

        .service-widget div { display: block; }
        .service-widget button { display: inline-block; cursor: pointer; }
        .service-widget h3 { display: block; font-weight: bold; }
        .service-widget p { display: block; }
        .service-widget span { display: inline; }

        /* 防止外部样式影响 */
        .service-widget-isolated {
          isolation: isolate;
          contain: layout style;
        }
      `;

      const styleElement = document.createElement("style");
      styleElement.textContent = styles;

      if (this.shadowRoot) {
        // Shadow DOM：样式只在Shadow DOM内生效
        this.shadowRoot.appendChild(styleElement);
      } else {
        // 普通DOM：添加到head，使用CSS前缀隔离
        document.head.appendChild(styleElement);
      }
    }

    /**
     * 公共API：销毁组件
     */
    destroy() {
      if (this.shadowRoot) {
        // 清理Shadow DOM
        this.container.shadowRoot = null;
      } else if (this.serviceContainer && this.serviceContainer.parentNode) {
        this.serviceContainer.parentNode.removeChild(this.serviceContainer);
      }

      this.isInitialized = false;
      this.layimInstance = null;
      this.ws = null;
      this.sessionList = [];
    }
  }

  // 创建全局实例
  window.ServiceWidget = new ServiceWidget();
})(window, document);
