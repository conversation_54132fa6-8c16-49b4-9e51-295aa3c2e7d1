# 客服SDK使用说明（重构版）

## 概述

客服SDK是一个轻量级的JavaScript库，可以快速为任何网站添加在线客服功能。重构版SDK专注于UI界面绘制，复用现有的 `chatCore.js` 和 `chatMember.js` 业务逻辑，确保功能稳定性和代码复用性。

## 功能特性

- ✅ **即插即用**：引入依赖文件并初始化即可使用
- ✅ **悬浮按钮**：网页右下角（或其他位置）显示客服入口按钮
- ✅ **实时聊天**：复用现有WebSocket实时通信逻辑
- ✅ **未读提醒**：支持未读消息计数和声音提醒
- ✅ **多媒体支持**：支持图片消息（复用现有上传功能）
- ✅ **多主题**：内置蓝色、绿色、橙色、红色四种主题
- ✅ **响应式设计**：完美适配PC和移动端
- ✅ **业务复用**：复用现有聊天业务逻辑，功能稳定可靠
- ✅ **架构清晰**：UI层和业务层分离，便于维护扩展

## 快速开始

### 1. 引入依赖文件（必需）

```html
<!-- 引入核心依赖 -->
<script src="/static/js/chatCore.js"></script>
<script src="/static/js/chatMember.js"></script>
<!-- 引入客服SDK -->
<script src="/static/js/kefuSDK.js"></script>
```

### 2. 初始化SDK

```javascript
KefuSDK.init({
    bid: 'your_business_id',        // 必需：商户ID
    customerId: 'customer_123',     // 必需：客户ID
    customerName: '张三',           // 可选：客户名称
    theme: 'blue',                  // 可选：主题色
    position: 'bottom-right'        // 可选：位置
});
```

就这么简单！客服功能已经集成到您的网站了。

## 重构说明

### 架构设计

重构版SDK采用分层架构：

- **UI层（kefuSDK.js）**：负责界面绘制、样式管理、用户交互
- **业务层（chatMember.js）**：负责聊天逻辑、消息处理、状态管理  
- **核心层（chatCore.js）**：负责WebSocket通信、API调用、数据处理

### 优势

1. **代码复用**：复用现有成熟的聊天业务逻辑
2. **职责分离**：UI和业务逻辑分离，便于维护
3. **功能稳定**：基于已验证的聊天功能
4. **扩展性强**：可以轻松添加新的UI样式和交互

## 配置选项

### 必需参数

| 参数 | 类型 | 说明 |
|------|------|------|
| `bid` | String | 商户ID，用于标识不同的商户 |
| `customerId` | String | 客户ID，用于标识不同的访客 |

### 可选参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `customerName` | String | '访客' | 客户显示名称 |
| `avatar` | String | '/static/img/default-avatar.png' | 客户头像URL |
| `position` | String | 'bottom-right' | 按钮位置：bottom-right, bottom-left, top-right, top-left |
| `theme` | String | 'blue' | 主题色：blue, green, orange, red |
| `title` | String | '在线客服' | 聊天窗口标题 |
| `subtitle` | String | '有什么可以帮您？' | 聊天窗口副标题 |
| `buttonSize` | Number | 60 | 按钮大小（像素） |
| `chatWidth` | Number | 380 | 聊天窗口宽度（像素） |
| `chatHeight` | Number | 520 | 聊天窗口高度（像素） |
| `autoOpen` | Boolean | false | 是否自动打开聊天窗口 |
| `showUnreadCount` | Boolean | true | 是否显示未读消息数 |
| `enableSound` | Boolean | true | 是否启用声音提醒 |
| `enableMinimize` | Boolean | true | 是否允许最小化 |
| `zIndex` | Number | 9999 | CSS z-index值 |

## API方法

### 基础方法

```javascript
// 打开聊天窗口
KefuSDK.open();

// 关闭聊天窗口
KefuSDK.close();

// 切换聊天窗口状态
KefuSDK.toggle();

// 显示客服按钮
KefuSDK.show();

// 隐藏客服按钮
KefuSDK.hide();

// 销毁SDK
KefuSDK.destroy();

// 获取未读消息数
const unreadCount = KefuSDK.getUnreadCount();
```

## 使用示例

### 基础示例

```html
<!DOCTYPE html>
<html>
<head>
    <title>我的网站</title>
</head>
<body>
    <h1>欢迎来到我的网站</h1>
    <p>这里是网站内容...</p>

    <!-- 引入依赖 -->
    <script src="/static/js/chatCore.js"></script>
    <script src="/static/js/chatMember.js"></script>
    <!-- 引入客服SDK -->
    <script src="/static/js/kefuSDK.js"></script>
    <script>
        // 初始化客服功能
        KefuSDK.init({
            bid: 'my_business_001',
            customerId: 'visitor_' + Date.now(),
            customerName: '访客',
            theme: 'blue'
        });
    </script>
</body>
</html>
```

### 高级配置示例

```javascript
KefuSDK.init({
    // 必需参数
    bid: 'my_business_001',
    customerId: 'user_12345',
    
    // 用户信息
    customerName: '张三',
    avatar: 'https://example.com/avatar.jpg',
    
    // 外观配置
    theme: 'green',
    position: 'bottom-left',
    title: '专属客服',
    subtitle: '7x24小时在线服务',
    
    // 尺寸配置
    buttonSize: 70,
    chatWidth: 400,
    chatHeight: 600,
    
    // 功能配置
    autoOpen: false,
    showUnreadCount: true,
    enableSound: true,
    enableMinimize: true
});
```

## 依赖说明

### 必需依赖

1. **chatCore.js** - 核心通信和数据处理
2. **chatMember.js** - 会员端聊天业务逻辑
3. **Vue.js** - 前端框架（通常已在页面中引入）
4. **LayUI** - UI组件库（通常已在页面中引入）

### 可选依赖

- **相关CSS文件** - 聊天界面样式（SDK会自动引入）

## 注意事项

1. **依赖顺序**：必须按顺序引入 chatCore.js → chatMember.js → kefuSDK.js
2. **Vue实例**：确保页面已引入Vue.js
3. **WebSocket连接**：确保服务器支持WebSocket连接
4. **HTTPS环境**：在HTTPS环境下会自动使用WSS协议
5. **业务逻辑**：所有聊天业务逻辑由chatMember.js处理，SDK只负责UI

## 技术支持

如有问题或建议，请联系技术支持团队。

## 更新日志

### v2.0.0 (重构版)
- 重构架构，分离UI和业务逻辑
- 复用现有chatCore.js和chatMember.js
- 优化代码结构和可维护性
- 保持API兼容性
