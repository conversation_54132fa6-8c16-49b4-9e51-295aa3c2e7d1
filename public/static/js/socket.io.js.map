{"version": 3, "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///socket.io.js", "webpack:///webpack/bootstrap 3f4094f74bfa6df9f01a", "webpack:///./lib/index.js", "webpack:///./lib/url.js", "webpack:///./~/parseuri/index.js", "webpack:///./~/debug/src/browser.js", "webpack:///./~/process/browser.js", "webpack:///./~/debug/src/debug.js", "webpack:///./~/ms/index.js", "webpack:///./~/socket.io-parser/index.js", "webpack:///./~/component-emitter/index.js", "webpack:///./~/socket.io-parser/binary.js", "webpack:///./~/isarray/index.js", "webpack:///./~/socket.io-parser/is-buffer.js", "webpack:///./lib/manager.js", "webpack:///./~/engine.io-client/lib/index.js", "webpack:///./~/engine.io-client/lib/socket.js", "webpack:///./~/engine.io-client/lib/transports/index.js", "webpack:///./~/engine.io-client/lib/xmlhttprequest.js", "webpack:///./~/has-cors/index.js", "webpack:///./~/engine.io-client/lib/globalThis.browser.js", "webpack:///./~/engine.io-client/lib/transports/polling-xhr.js", "webpack:///./~/engine.io-client/lib/transports/polling.js", "webpack:///./~/engine.io-client/lib/transport.js", "webpack:///./~/engine.io-parser/lib/browser.js", "webpack:///./~/engine.io-parser/lib/keys.js", "webpack:///./~/has-binary2/index.js", "webpack:///./~/arraybuffer.slice/index.js", "webpack:///./~/after/index.js", "webpack:///./~/engine.io-parser/lib/utf8.js", "webpack:///./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack:///./~/blob/index.js", "webpack:///./~/parseqs/index.js", "webpack:///./~/component-inherit/index.js", "webpack:///./~/yeast/index.js", "webpack:///./~/engine.io-client/lib/transports/polling-jsonp.js", "webpack:///./~/engine.io-client/lib/transports/websocket.js", "webpack:///./~/indexof/index.js", "webpack:///./lib/socket.js", "webpack:///./~/to-array/index.js", "webpack:///./lib/on.js", "webpack:///./~/component-bind/index.js", "webpack:///./~/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "modules", "__webpack_require__", "moduleId", "installedModules", "id", "loaded", "call", "m", "c", "p", "lookup", "uri", "opts", "undefined", "io", "parsed", "url", "source", "path", "sameNamespace", "cache", "nsps", "newConnection", "forceNew", "multiplex", "debug", "Manager", "query", "socket", "parser", "managers", "protocol", "connect", "Socket", "loc", "obj", "location", "host", "char<PERSON>t", "test", "parseuri", "port", "ipv6", "indexOf", "href", "pathNames", "regx", "names", "replace", "split", "substr", "length", "splice", "query<PERSON><PERSON>", "data", "$0", "$1", "$2", "re", "parts", "str", "src", "b", "e", "substring", "exec", "i", "authority", "ipv6uri", "process", "useColors", "window", "type", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "console", "firebug", "exception", "table", "parseInt", "RegExp", "formatArgs", "args", "namespace", "humanize", "diff", "color", "index", "lastC", "log", "_typeof", "Function", "prototype", "apply", "arguments", "save", "namespaces", "storage", "removeItem", "load", "r", "env", "DEBUG", "localstorage", "localStorage", "Symbol", "iterator", "constructor", "chrome", "local", "colors", "formatters", "j", "v", "JSON", "stringify", "err", "message", "enable", "defaultSetTimout", "Error", "defaultClearTimeout", "runTimeout", "fun", "cachedSetTimeout", "setTimeout", "runClearTimeout", "marker", "cachedClearTimeout", "clearTimeout", "cleanUpNextTick", "draining", "currentQueue", "queue", "concat", "queueIndex", "drainQueue", "timeout", "len", "run", "<PERSON><PERSON>", "array", "noop", "nextTick", "Array", "push", "title", "browser", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "name", "binding", "cwd", "chdir", "dir", "umask", "selectColor", "hash", "charCodeAt", "Math", "abs", "createDebug", "enabled", "self", "curr", "Date", "ms", "prevTime", "prev", "coerce", "unshift", "format", "formatter", "val", "logFn", "bind", "destroy", "init", "instances", "skips", "instance", "disable", "stack", "parse", "String", "n", "parseFloat", "y", "d", "h", "s", "fmtShort", "round", "fmtLong", "plural", "floor", "ceil", "options", "isNaN", "Encoder", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "attachments", "nsp", "payload", "tryStringify", "ERROR_PACKET", "encodeAsBinary", "callback", "writeEncoding", "bloblessData", "deconstruction", "binary", "deconstructPacket", "pack", "packet", "buffers", "removeBlobs", "Decoder", "reconstructor", "decodeString", "Number", "types", "error", "buf", "next", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "ERROR", "isArray", "BinaryReconstructor", "reconPack", "msg", "Emitter", "isBuf", "CONNECT", "DISCONNECT", "EVENT", "ACK", "encode", "encoding", "add", "base64", "takeBinaryData", "finishedReconstruction", "binData", "reconstructPacket", "mixin", "key", "addEventListener", "event", "fn", "_callbacks", "removeEventListener", "callbacks", "cb", "slice", "hasListeners", "_deconstructPacket", "placeholder", "_placeholder", "num", "newData", "_reconstructPacket", "toString", "Object", "withNativeBlob", "Blob", "withNativeFile", "File", "packetData", "_removeBlobs", "cur<PERSON><PERSON>", "containingObject", "pendingBlobs", "fileReader", "FileReader", "onload", "result", "readAsA<PERSON>y<PERSON><PERSON>er", "arr", "with<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "Backoff", "min", "max", "jitter", "readyState", "connecting", "lastPing", "packetBuffer", "_parser", "encoder", "decoder", "autoConnect", "open", "eio", "has", "hasOwnProperty", "emitAll", "updateSocketIds", "generateId", "engine", "_reconnection", "_reconnectionAttempts", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "maybeReconnectOnOpen", "reconnecting", "attempts", "reconnect", "skipReconnect", "openSub", "onopen", "errorSub", "cleanup", "timer", "close", "onping", "onpong", "ondata", "ondecoded", "onerror", "onConnecting", "encodedPackets", "write", "processPacketQueue", "shift", "subsLength", "sub", "disconnect", "reset", "onclose", "reason", "delay", "duration", "onreconnect", "attempt", "hostname", "secure", "agent", "parseqs", "decode", "upgrade", "forceJSONP", "jsonp", "forceBase64", "enablesXDR", "withCredentials", "timestampParam", "timestampRequests", "transports", "transportOptions", "writeBuffer", "prevBufferLen", "policyPort", "rememberUpgrade", "binaryType", "onlyBinaryUpgrades", "perMessageDeflate", "threshold", "pfx", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "forceNode", "isReactNative", "product", "extraHeaders", "keys", "localAddress", "upgrades", "pingInterval", "pingTimeout", "pingIntervalTimer", "pingTimeoutTimer", "clone", "o", "priorWebsocketSuccess", "Transport", "createTransport", "EIO", "transport", "sid", "requestTimeout", "protocols", "setTransport", "onDrain", "onPacket", "onError", "onClose", "probe", "onTransportOpen", "upgradeLosesBinary", "supportsBinary", "failed", "send", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "onOpen", "l", "onHandshake", "setPing", "code", "filterUpgrades", "onHeartbeat", "ping", "sendPacket", "writable", "compress", "cleanupAndClose", "waitForUpgrade", "desc", "filteredUpgrades", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "XMLHttpRequest", "XHR", "JSONP", "websocket", "hasCORS", "globalThis", "XDomainRequest", "join", "empty", "Polling", "Request", "method", "async", "isBinary", "create", "unload<PERSON><PERSON><PERSON>", "requests", "abort", "inherit", "request", "doWrite", "req", "sendXhr", "doPoll", "onData", "pollXhr", "setDisableHeaderCheck", "setRequestHeader", "hasXDR", "onLoad", "responseText", "onreadystatechange", "contentType", "getResponseHeader", "responseType", "status", "requestsCount", "onSuccess", "fromError", "response", "attachEvent", "terminationEvent", "hasXHR2", "yeast", "doOpen", "poll", "onPause", "total", "decodePayload", "doClose", "packets", "callbackfn", "encodePayload", "schema", "b64", "description", "decodePacket", "encodeBase64Object", "encode<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBase64Packet", "contentArray", "Uint8Array", "result<PERSON><PERSON><PERSON>", "byteLength", "encodeBlobAsArrayBuffer", "fr", "encodePacket", "encodeBlob", "dontSendBlobs", "blob", "tryDecode", "utf8", "strict", "map", "ary", "each", "done", "after", "eachWithIndex", "el", "base64encoder", "hasBinary", "sliceBuffer", "isAndroid", "isPhantomJS", "pong", "packetslist", "utf8encode", "encoded", "readAsDataURL", "b64data", "fromCharCode", "typed", "basic", "btoa", "utf8decode", "decodeBase64Packet", "asArray", "rest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeOne", "doneCallback", "encodePayloadAsBlob", "encodePayloadAsArrayBuffer", "results", "decodePayloadAsBinary", "chr", "ret", "totalLength", "reduce", "acc", "resultArray", "bufferIndex", "for<PERSON>ach", "isString", "ab", "view", "lenStr", "binaryIdentifier", "size", "lengthAry", "bufferTail", "tailArray", "msg<PERSON><PERSON>th", "toJSON", "arraybuffer", "start", "end", "bytes", "abv", "ii", "count", "err_cb", "proxy", "bail", "ucs2decode", "string", "value", "extra", "output", "counter", "ucs2encode", "stringFromCharCode", "checkScalarValue", "codePoint", "toUpperCase", "createByte", "encodeCodePoint", "symbol", "codePoints", "byteString", "readContinuationByte", "byteIndex", "byteCount", "continuationByte", "byteArray", "decodeSymbol", "byte1", "byte2", "byte3", "byte4", "tmp", "chars", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "mapArrayBufferViews", "chunk", "copy", "set", "byteOffset", "BlobBuilderConstructor", "bb", "BlobBuilder", "part", "append", "getBlob", "BlobConstructor", "WebKitBlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "blobSupported", "a", "blobSupportsArrayBufferView", "blobBuilderSupported", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "alphabet", "decoded", "now", "seed", "JSONPPolling", "___eio", "script", "rNewline", "rEscapedNewline", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "complete", "initIframe", "html", "iframeId", "area", "className", "position", "top", "left", "target", "setAttribute", "action", "submit", "WS", "usingBrowserWebSocket", "BrowserWebSocket", "WebSocketImpl", "NodeWebSocket", "WebSocket", "MozWebSocket", "check", "headers", "ws", "supports", "addEventListeners", "onmessage", "ev", "json", "ids", "acks", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "connected", "disconnected", "flags", "toArray", "hasBin", "events", "connect_error", "connect_timeout", "reconnect_attempt", "reconnect_failed", "reconnect_error", "subEvents", "pop", "onpacket", "rootNamespaceError", "onconnect", "onevent", "onack", "ondisconnect", "ack", "sent", "emitBuffered", "list", "factor", "pow", "rand", "random", "deviation"], "mappings": "CAAA,SAAAA,EAAAC,GACA,gBAAAC,UAAA,gBAAAC,QACAA,OAAAD,QAAAD,IACA,kBAAAG,gBAAAC,IACAD,UAAAH,GACA,gBAAAC,SACAA,QAAA,GAAAD,IAEAD,EAAA,GAAAC,KACCK,KAAA,WACD,MCAgB,UAAUC,GCN1B,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAP,OAGA,IAAAC,GAAAO,EAAAD,IACAP,WACAS,GAAAF,EACAG,QAAA,EAUA,OANAL,GAAAE,GAAAI,KAAAV,EAAAD,QAAAC,IAAAD,QAAAM,GAGAL,EAAAS,QAAA,EAGAT,EAAAD,QAvBA,GAAAQ,KAqCA,OATAF,GAAAM,EAAAP,EAGAC,EAAAO,EAAAL,EAGAF,EAAAQ,EAAA,GAGAR,EAAA,KDgBM,SAAUL,EAAQD,EAASM,GEnBjC,QAAAS,GAAAC,EAAAC,GACA,gBAAAD,KACAC,EAAAD,EACAA,EAAAE,QAGAD,OAEA,IAQAE,GARAC,EAAAC,EAAAL,GACAM,EAAAF,EAAAE,OACAb,EAAAW,EAAAX,GACAc,EAAAH,EAAAG,KACAC,EAAAC,EAAAhB,IAAAc,IAAAE,GAAAhB,GAAAiB,KACAC,EAAAV,EAAAW,UAAAX,EAAA,0BACA,IAAAA,EAAAY,WAAAL,CAiBA,OAbAG,IACAG,EAAA,+BAAAR,GACAH,EAAAY,EAAAT,EAAAL,KAEAQ,EAAAhB,KACAqB,EAAA,yBAAAR,GACAG,EAAAhB,GAAAsB,EAAAT,EAAAL,IAEAE,EAAAM,EAAAhB,IAEAW,EAAAY,QAAAf,EAAAe,QACAf,EAAAe,MAAAZ,EAAAY,OAEAb,EAAAc,OAAAb,EAAAG,KAAAN,GA7DA,GAAAI,GAAAf,EAAA,GACA4B,EAAA5B,EAAA,GACAyB,EAAAzB,EAAA,IACAwB,EAAAxB,EAAA,sBAMAL,GAAAD,UAAAe,CAMA,IAAAU,GAAAzB,EAAAmC,WAuDAnC,GAAAoC,SAAAF,EAAAE,SASApC,EAAAqC,QAAAtB,EAQAf,EAAA+B,QAAAzB,EAAA,IACAN,EAAAsC,OAAAhC,EAAA,KF6DM,SAAUL,EAAQD,EAASM,GGnIjC,QAAAe,GAAAL,EAAAuB,GACA,GAAAC,GAAAxB,CAGAuB,MAAA,mBAAAE,oBACA,MAAAzB,MAAAuB,EAAAH,SAAA,KAAAG,EAAAG,MAGA,gBAAA1B,KACA,MAAAA,EAAA2B,OAAA,KAEA3B,EADA,MAAAA,EAAA2B,OAAA,GACAJ,EAAAH,SAAApB,EAEAuB,EAAAG,KAAA1B,GAIA,sBAAA4B,KAAA5B,KACAc,EAAA,uBAAAd,GAEAA,EADA,mBAAAuB,GACAA,EAAAH,SAAA,KAAApB,EAEA,WAAAA,GAKAc,EAAA,WAAAd,GACAwB,EAAAK,EAAA7B,IAIAwB,EAAAM,OACA,cAAAF,KAAAJ,EAAAJ,UACAI,EAAAM,KAAA,KACK,eAAAF,KAAAJ,EAAAJ,YACLI,EAAAM,KAAA,QAIAN,EAAAjB,KAAAiB,EAAAjB,MAAA,GAEA,IAAAwB,GAAAP,EAAAE,KAAAM,QAAA,UACAN,EAAAK,EAAA,IAAAP,EAAAE,KAAA,IAAAF,EAAAE,IAOA,OAJAF,GAAA/B,GAAA+B,EAAAJ,SAAA,MAAAM,EAAA,IAAAF,EAAAM,KAEAN,EAAAS,KAAAT,EAAAJ,SAAA,MAAAM,GAAAH,KAAAO,OAAAN,EAAAM,KAAA,OAAAN,EAAAM,MAEAN,EApEA,GAAAK,GAAAvC,EAAA,GACAwB,EAAAxB,EAAA,0BAMAL,GAAAD,QAAAqB,GH+NM,SAAUpB,EAAQD,GIhMxB,QAAAkD,GAAAV,EAAAjB,GACA,GAAA4B,GAAA,WACAC,EAAA7B,EAAA8B,QAAAF,EAAA,KAAAG,MAAA,IASA,OAPA,KAAA/B,EAAAgC,OAAA,UAAAhC,EAAAiC,QACAJ,EAAAK,OAAA,KAEA,KAAAlC,EAAAgC,OAAAhC,EAAAiC,OAAA,MACAJ,EAAAK,OAAAL,EAAAI,OAAA,KAGAJ,EAGA,QAAAM,GAAA1C,EAAAgB,GACA,GAAA2B,KAQA,OANA3B,GAAAqB,QAAA,qCAAAO,EAAAC,EAAAC,GACAD,IACAF,EAAAE,GAAAC,KAIAH,EA3DA,GAAAI,GAAA,0OAEAC,GACA,iIAGA/D,GAAAD,QAAA,SAAAiE,GACA,GAAAC,GAAAD,EACAE,EAAAF,EAAAjB,QAAA,KACAoB,EAAAH,EAAAjB,QAAA,IAEAmB,KAAA,GAAAC,IAAA,IACAH,IAAAI,UAAA,EAAAF,GAAAF,EAAAI,UAAAF,EAAAC,GAAAf,QAAA,UAAwEY,EAAAI,UAAAD,EAAAH,EAAAT,QAOxE,KAJA,GAAA5C,GAAAmD,EAAAO,KAAAL,GAAA,IACAjD,KACAuD,EAAA,GAEAA,KACAvD,EAAAgD,EAAAO,IAAA3D,EAAA2D,IAAA,EAaA,OAVAJ,KAAA,GAAAC,IAAA,IACApD,EAAAM,OAAA4C,EACAlD,EAAA0B,KAAA1B,EAAA0B,KAAA2B,UAAA,EAAArD,EAAA0B,KAAAc,OAAA,GAAAH,QAAA,KAAwE,KACxErC,EAAAwD,UAAAxD,EAAAwD,UAAAnB,QAAA,QAAAA,QAAA,QAAAA,QAAA,KAAkF,KAClFrC,EAAAyD,SAAA,GAGAzD,EAAAkC,YAAAlC,IAAA,MACAA,EAAA0C,WAAA1C,IAAA,OAEAA,IJ6QM,SAAUf,EAAQD,EAASM,IAEJ,SAASoE,GAAU,YK5QhD,SAASC,KAIP,QAAsB,mBAAXC,UAA0BA,OAAOF,SAAmC,aAAxBE,OAAOF,QAAQG,QAK7C,mBAAdC,aAA6BA,UAAUC,YAAaD,UAAUC,UAAUC,cAAcC,MAAM,4BAM3E,mBAAbC,WAA4BA,SAASC,iBAAmBD,SAASC,gBAAgBC,OAASF,SAASC,gBAAgBC,MAAMC,kBAEnH,mBAAXT,SAA0BA,OAAOU,UAAYV,OAAOU,QAAQC,SAAYX,OAAOU,QAAQE,WAAaZ,OAAOU,QAAQG,QAGrG,mBAAdX,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,mBAAqBS,SAASC,OAAO9B,GAAI,KAAO,IAE9H,mBAAdiB,YAA6BA,UAAUC,WAAaD,UAAUC,UAAUC,cAAcC,MAAM,uBAsBxG,QAASW,GAAWC,GAClB,GAAIlB,GAAYvE,KAAKuE,SASrB,IAPAkB,EAAK,IAAMlB,EAAY,KAAO,IAC1BvE,KAAK0F,WACJnB,EAAY,MAAQ,KACrBkB,EAAK,IACJlB,EAAY,MAAQ,KACrB,IAAM3E,EAAQ+F,SAAS3F,KAAK4F,MAE3BrB,EAAL,CAEA,GAAI9D,GAAI,UAAYT,KAAK6F,KACzBJ,GAAKpC,OAAO,EAAG,EAAG5C,EAAG,iBAKrB,IAAIqF,GAAQ,EACRC,EAAQ,CACZN,GAAK,GAAGxC,QAAQ,cAAe,SAAS4B,GAClC,OAASA,IACbiB,IACI,OAASjB,IAGXkB,EAAQD,MAIZL,EAAKpC,OAAO0C,EAAO,EAAGtF,IAUxB,QAASuF,KAGP,MAAO,+BAAoBd,SAApB,YAAAe,EAAoBf,WACtBA,QAAQc,KACRE,SAASC,UAAUC,MAAM7F,KAAK2E,QAAQc,IAAKd,QAASmB,WAU3D,QAASC,GAAKC,GACZ,IACM,MAAQA,EACV3G,EAAQ4G,QAAQC,WAAW,SAE3B7G,EAAQ4G,QAAQ9E,MAAQ6E,EAE1B,MAAMvC,KAUV,QAAS0C,KACP,GAAIC,EACJ,KACEA,EAAI/G,EAAQ4G,QAAQ9E,MACpB,MAAMsC,IAOR,OAJK2C,GAAwB,mBAAZrC,IAA2B,OAASA,KACnDqC,EAAIrC,EAAQsC,IAAIC,OAGXF,EAoBT,QAASG,KACP,IACE,MAAOtC,QAAOuC,aACd,MAAO/C,KLwHV,GAAIiC,GAA4B,kBAAXe,SAAoD,gBAApBA,QAAOC,SAAwB,SAAU7E,GAAO,aAAcA,IAAS,SAAUA,GAAO,MAAOA,IAAyB,kBAAX4E,SAAyB5E,EAAI8E,cAAgBF,QAAU5E,IAAQ4E,OAAOb,UAAY,eAAkB/D,GKnTvQxC,GAAUC,EAAOD,QAAUM,EAAQ,GACnCN,EAAQoG,IAAMA,EACdpG,EAAQ4F,WAAaA,EACrB5F,EAAQ0G,KAAOA,EACf1G,EAAQ8G,KAAOA,EACf9G,EAAQ2E,UAAYA,EACpB3E,EAAQ4G,QAAU,mBAAsBW,SACtB,mBAAsBA,QAAOX,QAC3BW,OAAOX,QAAQY,MACfN,IAMpBlH,EAAQyH,QACN,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAClE,UAAW,UAAW,UAAW,UAAW,UAAW,WAwCzDzH,EAAQ0H,WAAWC,EAAI,SAASC,GAC9B,IACE,MAAOC,MAAKC,UAAUF,GACtB,MAAOG,GACP,MAAO,+BAAiCA,EAAIC,UAqGhDhI,EAAQiI,OAAOnB,OLsTenG,KAAKX,EAASM,EAAoB,KAI1D,SAAUL,EAAQD,GMhexB,QAAAkI,KACA,SAAAC,OAAA,mCAEA,QAAAC,KACA,SAAAD,OAAA,qCAsBA,QAAAE,GAAAC,GACA,GAAAC,IAAAC,WAEA,MAAAA,YAAAF,EAAA,EAGA,KAAAC,IAAAL,IAAAK,IAAAC,WAEA,MADAD,GAAAC,WACAA,WAAAF,EAAA,EAEA,KAEA,MAAAC,GAAAD,EAAA,GACK,MAAAlE,GACL,IAEA,MAAAmE,GAAA5H,KAAA,KAAA2H,EAAA,GACS,MAAAlE,GAET,MAAAmE,GAAA5H,KAAAP,KAAAkI,EAAA,KAMA,QAAAG,GAAAC,GACA,GAAAC,IAAAC,aAEA,MAAAA,cAAAF,EAGA,KAAAC,IAAAP,IAAAO,IAAAC,aAEA,MADAD,GAAAC,aACAA,aAAAF,EAEA,KAEA,MAAAC,GAAAD,GACK,MAAAtE,GACL,IAEA,MAAAuE,GAAAhI,KAAA,KAAA+H,GACS,MAAAtE,GAGT,MAAAuE,GAAAhI,KAAAP,KAAAsI,KAYA,QAAAG,KACAC,GAAAC,IAGAD,GAAA,EACAC,EAAAvF,OACAwF,EAAAD,EAAAE,OAAAD,GAEAE,GAAA,EAEAF,EAAAxF,QACA2F,KAIA,QAAAA,KACA,IAAAL,EAAA,CAGA,GAAAM,GAAAf,EAAAQ,EACAC,IAAA,CAGA,KADA,GAAAO,GAAAL,EAAAxF,OACA6F,GAAA,CAGA,IAFAN,EAAAC,EACAA,OACAE,EAAAG,GACAN,GACAA,EAAAG,GAAAI,KAGAJ,IAAA,EACAG,EAAAL,EAAAxF,OAEAuF,EAAA,KACAD,GAAA,EACAL,EAAAW,IAiBA,QAAAG,GAAAjB,EAAAkB,GACApJ,KAAAkI,MACAlI,KAAAoJ,QAYA,QAAAC,MAhKA,GAOAlB,GACAI,EARAjE,EAAAzE,EAAAD,YAgBA,WACA,IAEAuI,EADA,kBAAAC,YACAA,WAEAN,EAEK,MAAA9D,GACLmE,EAAAL,EAEA,IAEAS,EADA,kBAAAC,cACAA,aAEAR,EAEK,MAAAhE,GACLuE,EAAAP,KAuDA,IAEAW,GAFAC,KACAF,GAAA,EAEAI,GAAA,CAyCAxE,GAAAgF,SAAA,SAAApB,GACA,GAAAzC,GAAA,GAAA8D,OAAAlD,UAAAjD,OAAA,EACA,IAAAiD,UAAAjD,OAAA,EACA,OAAAe,GAAA,EAAuBA,EAAAkC,UAAAjD,OAAsBe,IAC7CsB,EAAAtB,EAAA,GAAAkC,UAAAlC,EAGAyE,GAAAY,KAAA,GAAAL,GAAAjB,EAAAzC,IACA,IAAAmD,EAAAxF,QAAAsF,GACAT,EAAAc,IASAI,EAAAhD,UAAA+C,IAAA,WACAlJ,KAAAkI,IAAA9B,MAAA,KAAApG,KAAAoJ,QAEA9E,EAAAmF,MAAA,UACAnF,EAAAoF,SAAA,EACApF,EAAAsC,OACAtC,EAAAqF,QACArF,EAAAsF,QAAA,GACAtF,EAAAuF,YAIAvF,EAAAwF,GAAAT,EACA/E,EAAAyF,YAAAV,EACA/E,EAAA0F,KAAAX,EACA/E,EAAA2F,IAAAZ,EACA/E,EAAA4F,eAAAb,EACA/E,EAAA6F,mBAAAd,EACA/E,EAAA8F,KAAAf,EACA/E,EAAA+F,gBAAAhB,EACA/E,EAAAgG,oBAAAjB,EAEA/E,EAAAiG,UAAA,SAAAC,GAAqC,UAErClG,EAAAmG,QAAA,SAAAD,GACA,SAAAzC,OAAA,qCAGAzD,EAAAoG,IAAA,WAA2B,WAC3BpG,EAAAqG,MAAA,SAAAC,GACA,SAAA7C,OAAA,mCAEAzD,EAAAuG,MAAA,WAA4B,WNkftB,SAAUhL,EAAQD,EAASM,GAEhC,YOjoBD,SAAS4K,GAAYpF,GACnB,GAAcvB,GAAV4G,EAAO,CAEX,KAAK5G,IAAKuB,GACRqF,GAAUA,GAAQ,GAAKA,EAAQrF,EAAUsF,WAAW7G,GACpD4G,GAAQ,CAGV,OAAOnL,GAAQyH,OAAO4D,KAAKC,IAAIH,GAAQnL,EAAQyH,OAAOjE,QAWxD,QAAS+H,GAAYzF,GAInB,QAAShE,KAEP,GAAKA,EAAM0J,QAAX,CAEA,GAAIC,GAAO3J,EAGP4J,GAAQ,GAAIC,MACZC,EAAKF,GAAQG,GAAYH,EAC7BD,GAAKzF,KAAO4F,EACZH,EAAKK,KAAOD,EACZJ,EAAKC,KAAOA,EACZG,EAAWH,CAIX,KAAK,GADD7F,GAAO,GAAI8D,OAAMlD,UAAUjD,QACtBe,EAAI,EAAGA,EAAIsB,EAAKrC,OAAQe,IAC/BsB,EAAKtB,GAAKkC,UAAUlC,EAGtBsB,GAAK,GAAK7F,EAAQ+L,OAAOlG,EAAK,IAE1B,gBAAoBA,GAAK,IAE3BA,EAAKmG,QAAQ,KAIf,IAAI9F,GAAQ,CACZL,GAAK,GAAKA,EAAK,GAAGxC,QAAQ,gBAAiB,SAAS4B,EAAOgH,GAEzD,GAAc,OAAVhH,EAAgB,MAAOA,EAC3BiB,IACA,IAAIgG,GAAYlM,EAAQ0H,WAAWuE,EACnC,IAAI,kBAAsBC,GAAW,CACnC,GAAIC,GAAMtG,EAAKK,EACfjB,GAAQiH,EAAUvL,KAAK8K,EAAMU,GAG7BtG,EAAKpC,OAAOyC,EAAO,GACnBA,IAEF,MAAOjB,KAITjF,EAAQ4F,WAAWjF,KAAK8K,EAAM5F,EAE9B,IAAIuG,GAAQtK,EAAMsE,KAAOpG,EAAQoG,KAAOd,QAAQc,IAAIiG,KAAK/G,QACzD8G,GAAM5F,MAAMiF,EAAM5F,IAnDpB,GAAIgG,EAmEJ,OAbA/J,GAAMgE,UAAYA,EAClBhE,EAAM0J,QAAUxL,EAAQwL,QAAQ1F,GAChChE,EAAM6C,UAAY3E,EAAQ2E,YAC1B7C,EAAMmE,MAAQiF,EAAYpF,GAC1BhE,EAAMwK,QAAUA,EAGZ,kBAAsBtM,GAAQuM,MAChCvM,EAAQuM,KAAKzK,GAGf9B,EAAQwM,UAAU5C,KAAK9H,GAEhBA,EAGT,QAASwK,KACP,GAAIpG,GAAQlG,EAAQwM,UAAUxJ,QAAQ5C,KACtC,OAAI8F,MAAU,IACZlG,EAAQwM,UAAU/I,OAAOyC,EAAO,IACzB,GAcX,QAAS+B,GAAOtB,GACd3G,EAAQ0G,KAAKC,GAEb3G,EAAQoD,SACRpD,EAAQyM,QAER,IAAIlI,GACAjB,GAA+B,gBAAfqD,GAA0BA,EAAa,IAAIrD,MAAM,UACjE+F,EAAM/F,EAAME,MAEhB,KAAKe,EAAI,EAAGA,EAAI8E,EAAK9E,IACdjB,EAAMiB,KACXoC,EAAarD,EAAMiB,GAAGlB,QAAQ,MAAO,OACf,MAAlBsD,EAAW,GACb3G,EAAQyM,MAAM7C,KAAK,GAAIjE,QAAO,IAAMgB,EAAWpD,OAAO,GAAK,MAE3DvD,EAAQoD,MAAMwG,KAAK,GAAIjE,QAAO,IAAMgB,EAAa,MAIrD,KAAKpC,EAAI,EAAGA,EAAIvE,EAAQwM,UAAUhJ,OAAQe,IAAK,CAC7C,GAAImI,GAAW1M,EAAQwM,UAAUjI,EACjCmI,GAASlB,QAAUxL,EAAQwL,QAAQkB,EAAS5G,YAUhD,QAAS6G,KACP3M,EAAQiI,OAAO,IAWjB,QAASuD,GAAQZ,GACf,GAA8B,MAA1BA,EAAKA,EAAKpH,OAAS,GACrB,OAAO,CAET,IAAIe,GAAG8E,CACP,KAAK9E,EAAI,EAAG8E,EAAMrJ,EAAQyM,MAAMjJ,OAAQe,EAAI8E,EAAK9E,IAC/C,GAAIvE,EAAQyM,MAAMlI,GAAG3B,KAAKgI,GACxB,OAAO,CAGX,KAAKrG,EAAI,EAAG8E,EAAMrJ,EAAQoD,MAAMI,OAAQe,EAAI8E,EAAK9E,IAC/C,GAAIvE,EAAQoD,MAAMmB,GAAG3B,KAAKgI,GACxB,OAAO,CAGX,QAAO,EAWT,QAASmB,GAAOI,GACd,MAAIA,aAAehE,OAAcgE,EAAIS,OAAST,EAAInE,QAC3CmE,EAvNTnM,EAAUC,EAAOD,QAAUuL,EAAYzJ,MAAQyJ,EAAY,WAAaA,EACxEvL,EAAQ+L,OAASA,EACjB/L,EAAQ2M,QAAUA,EAClB3M,EAAQiI,OAASA,EACjBjI,EAAQwL,QAAUA,EAClBxL,EAAQ+F,SAAWzF,EAAQ,GAK3BN,EAAQwM,aAMRxM,EAAQoD,SACRpD,EAAQyM,SAQRzM,EAAQ0H,ePg3BF,SAAUzH,EAAQD,GQn2BxB,QAAA6M,GAAA5I,GAEA,GADAA,EAAA6I,OAAA7I,KACAA,EAAAT,OAAA,MAGA,GAAAyB,GAAA,wHAAAX,KACAL,EAEA,IAAAgB,EAAA,CAGA,GAAA8H,GAAAC,WAAA/H,EAAA,IACAJ,GAAAI,EAAA,UAAAD,aACA,QAAAH,GACA,YACA,WACA,UACA,SACA,QACA,MAAAkI,GAAAE,CACA,YACA,UACA,QACA,MAAAF,GAAAG,CACA,aACA,WACA,UACA,SACA,QACA,MAAAH,GAAAI,CACA,eACA,aACA,WACA,UACA,QACA,MAAAJ,GAAAnM,CACA,eACA,aACA,WACA,UACA,QACA,MAAAmM,GAAAK,CACA,oBACA,kBACA,YACA,WACA,SACA,MAAAL,EACA,SACA,UAYA,QAAAM,GAAAzB,GACA,MAAAA,IAAAsB,EACA7B,KAAAiC,MAAA1B,EAAAsB,GAAA,IAEAtB,GAAAuB,EACA9B,KAAAiC,MAAA1B,EAAAuB,GAAA,IAEAvB,GAAAhL,EACAyK,KAAAiC,MAAA1B,EAAAhL,GAAA,IAEAgL,GAAAwB,EACA/B,KAAAiC,MAAA1B,EAAAwB,GAAA,IAEAxB,EAAA,KAWA,QAAA2B,GAAA3B,GACA,MAAA4B,GAAA5B,EAAAsB,EAAA,QACAM,EAAA5B,EAAAuB,EAAA,SACAK,EAAA5B,EAAAhL,EAAA,WACA4M,EAAA5B,EAAAwB,EAAA,WACAxB,EAAA,MAOA,QAAA4B,GAAA5B,EAAAmB,EAAAnC,GACA,KAAAgB,EAAAmB,GAGA,MAAAnB,GAAA,IAAAmB,EACA1B,KAAAoC,MAAA7B,EAAAmB,GAAA,IAAAnC,EAEAS,KAAAqC,KAAA9B,EAAAmB,GAAA,IAAAnC,EAAA,IAlJA,GAAAwC,GAAA,IACAxM,EAAA,GAAAwM,EACAD,EAAA,GAAAvM,EACAsM,EAAA,GAAAC,EACAF,EAAA,OAAAC,CAgBAjN,GAAAD,QAAA,SAAAmM,EAAAwB,GACAA,OACA,IAAA9I,SAAAsH,EACA,eAAAtH,GAAAsH,EAAA3I,OAAA,EACA,MAAAqJ,GAAAV,EACG,eAAAtH,GAAA+I,MAAAzB,MAAA,EACH,MAAAwB,WAAAJ,EAAApB,GAAAkB,EAAAlB,EAEA,UAAAhE,OACA,wDACAN,KAAAC,UAAAqE,MR6gCM,SAAUlM,EAAQD,EAASM,GS97BjC,QAAAuN,MAiCA,QAAAC,GAAAtL,GAGA,GAAAyB,GAAA,GAAAzB,EAAAqC,IAmBA,IAhBA7E,EAAA+N,eAAAvL,EAAAqC,MAAA7E,EAAAgO,aAAAxL,EAAAqC,OACAZ,GAAAzB,EAAAyL,YAAA,KAKAzL,EAAA0L,KAAA,MAAA1L,EAAA0L,MACAjK,GAAAzB,EAAA0L,IAAA,KAIA,MAAA1L,EAAA/B,KACAwD,GAAAzB,EAAA/B,IAIA,MAAA+B,EAAAmB,KAAA,CACA,GAAAwK,GAAAC,EAAA5L,EAAAmB,KACA,IAAAwK,KAAA,EAGA,MAAAE,EAFApK,IAAAkK,EAOA,MADArM,GAAA,mBAAAU,EAAAyB,GACAA,EAGA,QAAAmK,GAAAnK,GACA,IACA,MAAA4D,MAAAC,UAAA7D,GACG,MAAAG,GACH,UAcA,QAAAkK,GAAA9L,EAAA+L,GAEA,QAAAC,GAAAC,GACA,GAAAC,GAAAC,EAAAC,kBAAAH,GACAI,EAAAf,EAAAY,EAAAI,QACAC,EAAAL,EAAAK,OAEAA,GAAA/C,QAAA6C,GACAN,EAAAQ,GAGAJ,EAAAK,YAAAxM,EAAAgM,GAUA,QAAAS,KACA7O,KAAA8O,cAAA,KAsDA,QAAAC,GAAAlL,GACA,GAAAM,GAAA,EAEAzD,GACA+D,KAAAuK,OAAAnL,EAAAtB,OAAA,IAGA,UAAA3C,EAAAqP,MAAAvO,EAAA+D,MACA,MAAAyK,GAAA,uBAAAxO,EAAA+D,KAIA,IAAA7E,EAAA+N,eAAAjN,EAAA+D,MAAA7E,EAAAgO,aAAAlN,EAAA+D,KAAA,CAEA,IADA,GAAA0K,GAAA,GACA,MAAAtL,EAAAtB,SAAA4B,KACAgL,GAAAtL,EAAAtB,OAAA4B,GACAA,GAAAN,EAAAT,UAEA,GAAA+L,GAAAH,OAAAG,IAAA,MAAAtL,EAAAtB,OAAA4B,GACA,SAAA4D,OAAA,sBAEArH,GAAAmN,YAAAmB,OAAAG,GAIA,SAAAtL,EAAAtB,OAAA4B,EAAA,GAEA,IADAzD,EAAAoN,IAAA,KACA3J,GAAA,CACA,GAAA1D,GAAAoD,EAAAtB,OAAA4B,EACA,UAAA1D,EAAA,KAEA,IADAC,EAAAoN,KAAArN,EACA0D,IAAAN,EAAAT,OAAA,UAGA1C,GAAAoN,IAAA,GAIA,IAAAsB,GAAAvL,EAAAtB,OAAA4B,EAAA,EACA,SAAAiL,GAAAJ,OAAAI,MAAA,CAEA,IADA1O,EAAAL,GAAA,KACA8D,GAAA,CACA,GAAA1D,GAAAoD,EAAAtB,OAAA4B,EACA,UAAA1D,GAAAuO,OAAAvO,MAAA,GACA0D,CACA,OAGA,GADAzD,EAAAL,IAAAwD,EAAAtB,OAAA4B,GACAA,IAAAN,EAAAT,OAAA,MAEA1C,EAAAL,GAAA2O,OAAAtO,EAAAL,IAIA,GAAAwD,EAAAtB,SAAA4B,GAAA,CACA,GAAA4J,GAAAsB,EAAAxL,EAAAV,OAAAgB,IACAmL,EAAAvB,KAAA,IAAArN,EAAA+D,OAAA7E,EAAA2P,OAAAC,EAAAzB,GACA,KAAAuB,EAGA,MAAAJ,GAAA,kBAFAxO,GAAA6C,KAAAwK,EAOA,MADArM,GAAA,mBAAAmC,EAAAnD,GACAA,EAGA,QAAA2O,GAAAxL,GACA,IACA,MAAA4D,MAAAgF,MAAA5I,GACG,MAAAG,GACH,UA0BA,QAAAyL,GAAAf,GACA1O,KAAA0P,UAAAhB,EACA1O,KAAA2O,WAkCA,QAAAO,GAAAS,GACA,OACAlL,KAAA7E,EAAA2P,MACAhM,KAAA,iBAAAoM,GAvZA,GAAAjO,GAAAxB,EAAA,uBACA0P,EAAA1P,EAAA,GACAqO,EAAArO,EAAA,GACAsP,EAAAtP,EAAA,IACA2P,EAAA3P,EAAA,GAQAN,GAAAoC,SAAA,EAQApC,EAAAqP,OACA,UACA,aACA,QACA,MACA,QACA,eACA,cASArP,EAAAkQ,QAAA,EAQAlQ,EAAAmQ,WAAA,EAQAnQ,EAAAoQ,MAAA,EAQApQ,EAAAqQ,IAAA,EAQArQ,EAAA2P,MAAA,EAQA3P,EAAA+N,aAAA,EAQA/N,EAAAgO,WAAA,EAQAhO,EAAA6N,UAQA7N,EAAAiP,SAUA,IAAAZ,GAAArO,EAAA2P,MAAA,gBAYA9B,GAAAtH,UAAA+J,OAAA,SAAA9N,EAAA+L,GAGA,GAFAzM,EAAA,qBAAAU,GAEAxC,EAAA+N,eAAAvL,EAAAqC,MAAA7E,EAAAgO,aAAAxL,EAAAqC,KACAyJ,EAAA9L,EAAA+L,OACG,CACH,GAAAgC,GAAAzC,EAAAtL,EACA+L,IAAAgC,MA8FAP,EAAAf,EAAA1I,WAUA0I,EAAA1I,UAAAiK,IAAA,SAAAhO,GACA,GAAAsM,EACA,oBAAAtM,GACAsM,EAAAK,EAAA3M,GACAxC,EAAA+N,eAAAe,EAAAjK,MAAA7E,EAAAgO,aAAAc,EAAAjK,MACAzE,KAAA8O,cAAA,GAAAW,GAAAf,GAGA,IAAA1O,KAAA8O,cAAAY,UAAA7B,aACA7N,KAAAoK,KAAA,UAAAsE,IAGA1O,KAAAoK,KAAA,UAAAsE,OAEG,KAAAmB,EAAAzN,OAAAiO,OAWH,SAAAtI,OAAA,iBAAA3F,EAVA,KAAApC,KAAA8O,cACA,SAAA/G,OAAA,mDAEA2G,GAAA1O,KAAA8O,cAAAwB,eAAAlO,GACAsM,IACA1O,KAAA8O,cAAA,KACA9O,KAAAoK,KAAA,UAAAsE,MAkGAG,EAAA1I,UAAA+F,QAAA,WACAlM,KAAA8O,eACA9O,KAAA8O,cAAAyB,0BA6BAd,EAAAtJ,UAAAmK,eAAA,SAAAE,GAEA,GADAxQ,KAAA2O,QAAAnF,KAAAgH,GACAxQ,KAAA2O,QAAAvL,SAAApD,KAAA0P,UAAA7B,YAAA,CACA,GAAAa,GAAAH,EAAAkC,kBAAAzQ,KAAA0P,UAAA1P,KAAA2O,QAEA,OADA3O,MAAAuQ,yBACA7B,EAEA,aASAe,EAAAtJ,UAAAoK,uBAAA,WACAvQ,KAAA0P,UAAA,KACA1P,KAAA2O,aT8jCM,SAAU9O,EAAQD,EAASM,GUr8CjC,QAAA0P,GAAAxN,GACA,GAAAA,EAAA,MAAAsO,GAAAtO,GAWA,QAAAsO,GAAAtO,GACA,OAAAuO,KAAAf,GAAAzJ,UACA/D,EAAAuO,GAAAf,EAAAzJ,UAAAwK,EAEA,OAAAvO,GAzBAvC,EAAAD,QAAAgQ,EAqCAA,EAAAzJ,UAAA2D,GACA8F,EAAAzJ,UAAAyK,iBAAA,SAAAC,EAAAC,GAIA,MAHA9Q,MAAA+Q,WAAA/Q,KAAA+Q,gBACA/Q,KAAA+Q,WAAA,IAAAF,GAAA7Q,KAAA+Q,WAAA,IAAAF,QACArH,KAAAsH,GACA9Q,MAaA4P,EAAAzJ,UAAA6D,KAAA,SAAA6G,EAAAC,GACA,QAAAhH,KACA9J,KAAAiK,IAAA4G,EAAA/G,GACAgH,EAAA1K,MAAApG,KAAAqG,WAKA,MAFAyD,GAAAgH,KACA9Q,KAAA8J,GAAA+G,EAAA/G,GACA9J,MAaA4P,EAAAzJ,UAAA8D,IACA2F,EAAAzJ,UAAA+D,eACA0F,EAAAzJ,UAAAgE,mBACAyF,EAAAzJ,UAAA6K,oBAAA,SAAAH,EAAAC,GAIA,GAHA9Q,KAAA+Q,WAAA/Q,KAAA+Q,eAGA,GAAA1K,UAAAjD,OAEA,MADApD,MAAA+Q,cACA/Q,IAIA,IAAAiR,GAAAjR,KAAA+Q,WAAA,IAAAF,EACA,KAAAI,EAAA,MAAAjR,KAGA,OAAAqG,UAAAjD,OAEA,aADApD,MAAA+Q,WAAA,IAAAF,GACA7Q,IAKA,QADAkR,GACA/M,EAAA,EAAiBA,EAAA8M,EAAA7N,OAAsBe,IAEvC,GADA+M,EAAAD,EAAA9M,GACA+M,IAAAJ,GAAAI,EAAAJ,OAAA,CACAG,EAAA5N,OAAAc,EAAA,EACA,OAUA,MAJA,KAAA8M,EAAA7N,cACApD,MAAA+Q,WAAA,IAAAF,GAGA7Q,MAWA4P,EAAAzJ,UAAAiE,KAAA,SAAAyG,GACA7Q,KAAA+Q,WAAA/Q,KAAA+Q,cAKA,QAHAtL,GAAA,GAAA8D,OAAAlD,UAAAjD,OAAA,GACA6N,EAAAjR,KAAA+Q,WAAA,IAAAF,GAEA1M,EAAA,EAAiBA,EAAAkC,UAAAjD,OAAsBe,IACvCsB,EAAAtB,EAAA,GAAAkC,UAAAlC,EAGA,IAAA8M,EAAA,CACAA,IAAAE,MAAA,EACA,QAAAhN,GAAA,EAAA8E,EAAAgI,EAAA7N,OAA2Ce,EAAA8E,IAAS9E,EACpD8M,EAAA9M,GAAAiC,MAAApG,KAAAyF,GAIA,MAAAzF,OAWA4P,EAAAzJ,UAAAoE,UAAA,SAAAsG,GAEA,MADA7Q,MAAA+Q,WAAA/Q,KAAA+Q,eACA/Q,KAAA+Q,WAAA,IAAAF,QAWAjB,EAAAzJ,UAAAiL,aAAA,SAAAP,GACA,QAAA7Q,KAAAuK,UAAAsG,GAAAzN,SV49CM,SAAUvD,EAAQD,EAASM,GW1mDjC,QAAAmR,GAAA9N,EAAAoL,GACA,IAAApL,EAAA,MAAAA,EAEA,IAAAsM,EAAAtM,GAAA,CACA,GAAA+N,IAAuBC,cAAA,EAAAC,IAAA7C,EAAAvL,OAEvB,OADAuL,GAAAnF,KAAAjG,GACA+N,EACG,GAAA9B,EAAAjM,GAAA,CAEH,OADAkO,GAAA,GAAAlI,OAAAhG,EAAAH,QACAe,EAAA,EAAmBA,EAAAZ,EAAAH,OAAiBe,IACpCsN,EAAAtN,GAAAkN,EAAA9N,EAAAY,GAAAwK,EAEA,OAAA8C,GACG,mBAAAlO,kBAAAgI,OAAA,CACH,GAAAkG,KACA,QAAAd,KAAApN,GACAkO,EAAAd,GAAAU,EAAA9N,EAAAoN,GAAAhC,EAEA,OAAA8C,GAEA,MAAAlO,GAkBA,QAAAmO,GAAAnO,EAAAoL,GACA,IAAApL,EAAA,MAAAA,EAEA,IAAAA,KAAAgO,aACA,MAAA5C,GAAApL,EAAAiO,IACG,IAAAhC,EAAAjM,GACH,OAAAY,GAAA,EAAmBA,EAAAZ,EAAAH,OAAiBe,IACpCZ,EAAAY,GAAAuN,EAAAnO,EAAAY,GAAAwK,OAEG,oBAAApL,GACH,OAAAoN,KAAApN,GACAA,EAAAoN,GAAAe,EAAAnO,EAAAoN,GAAAhC,EAIA,OAAApL,GA9EA,GAAAiM,GAAAtP,EAAA,IACA2P,EAAA3P,EAAA,IACAyR,EAAAC,OAAAzL,UAAAwL,SACAE,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAAH,EAAApR,KAAAuR,MACAC,EAAA,kBAAAC,OAAA,mBAAAA,OAAA,6BAAAL,EAAApR,KAAAyR,KAYApS,GAAA4O,kBAAA,SAAAE,GACA,GAAAC,MACAsD,EAAAvD,EAAAnL,KACAkL,EAAAC,CAGA,OAFAD,GAAAlL,KAAA8N,EAAAY,EAAAtD,GACAF,EAAAZ,YAAAc,EAAAvL,QACUsL,OAAAD,EAAAE,YAmCV/O,EAAA6Q,kBAAA,SAAA/B,EAAAC,GAGA,MAFAD,GAAAnL,KAAAmO,EAAAhD,EAAAnL,KAAAoL,GACAD,EAAAb,YAAA/M,OACA4N,GA+BA9O,EAAAgP,YAAA,SAAArL,EAAA4K,GACA,QAAA+D,GAAA9P,EAAA+P,EAAAC,GACA,IAAAhQ,EAAA,MAAAA,EAGA,IAAAyP,GAAAzP,YAAA0P,OACAC,GAAA3P,YAAA4P,MAAA,CACAK,GAGA,IAAAC,GAAA,GAAAC,WACAD,GAAAE,OAAA,WACAJ,EACAA,EAAAD,GAAAnS,KAAAyS,OAGApE,EAAArO,KAAAyS,SAIAJ,GACAlE,EAAAE,IAIAiE,EAAAI,kBAAAtQ,OACK,IAAAoN,EAAApN,GACL,OAAA+B,GAAA,EAAqBA,EAAA/B,EAAAgB,OAAgBe,IACrC+N,EAAA9P,EAAA+B,KAAA/B,OAEK,oBAAAA,KAAAyN,EAAAzN,GACL,OAAAuO,KAAAvO,GACA8P,EAAA9P,EAAAuO,KAAAvO,GAKA,GAAAiQ,GAAA,EACAhE,EAAA9K,CACA2O,GAAA7D,GACAgE,GACAlE,EAAAE,KXkpDM,SAAUxO,EAAQD,GY5xDxB,GAAA+R,MAAiBA,QAEjB9R,GAAAD,QAAA2J,MAAAiG,SAAA,SAAAmD,GACA,wBAAAhB,EAAApR,KAAAoS,KZoyDM,SAAU9S,EAAQD,GavxDxB,QAAAiQ,GAAAzN,GACA,MAAAwQ,IAAAC,OAAAC,SAAA1Q,IACA2Q,IAAA3Q,YAAA4Q,cAAAC,EAAA7Q,IAjBAvC,EAAAD,QAAAiQ,CAEA,IAAA+C,GAAA,kBAAAC,SAAA,kBAAAA,QAAAC,SACAC,EAAA,kBAAAC,aAEAC,EAAA,SAAA7Q,GACA,wBAAA4Q,aAAAC,OAAAD,YAAAC,OAAA7Q,KAAA8Q,iBAAAF,eb0zDM,SAAUnT,EAAQD,EAASM,Gc9xDjC,QAAAyB,GAAAf,EAAAC,GACA,KAAAb,eAAA2B,IAAA,UAAAA,GAAAf,EAAAC,EACAD,IAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAAE,QAEAD,QAEAA,EAAAM,KAAAN,EAAAM,MAAA,aACAnB,KAAAsB,QACAtB,KAAAmT,QACAnT,KAAAa,OACAb,KAAAoT,aAAAvS,EAAAuS,gBAAA,GACApT,KAAAqT,qBAAAxS,EAAAwS,sBAAAC,KACAtT,KAAAuT,kBAAA1S,EAAA0S,mBAAA,KACAvT,KAAAwT,qBAAA3S,EAAA2S,sBAAA,KACAxT,KAAAyT,oBAAA5S,EAAA4S,qBAAA,IACAzT,KAAA0T,QAAA,GAAAC,IACAC,IAAA5T,KAAAuT,oBACAM,IAAA7T,KAAAwT,uBACAM,OAAA9T,KAAAyT,wBAEAzT,KAAAgJ,QAAA,MAAAnI,EAAAmI,QAAA,IAAAnI,EAAAmI,SACAhJ,KAAA+T,WAAA,SACA/T,KAAAY,MACAZ,KAAAgU,cACAhU,KAAAiU,SAAA,KACAjU,KAAAmQ,UAAA,EACAnQ,KAAAkU,eACA,IAAAC,GAAAtT,EAAAiB,SACA9B,MAAAoU,QAAA,GAAAD,GAAA1G,QACAzN,KAAAqU,QAAA,GAAAF,GAAAtF,QACA7O,KAAAsU,YAAAzT,EAAAyT,eAAA,EACAtU,KAAAsU,aAAAtU,KAAAuU,OA/DA,GAAAC,GAAAtU,EAAA,IACAgC,EAAAhC,EAAA,IACA0P,EAAA1P,EAAA,GACA4B,EAAA5B,EAAA,GACA4J,EAAA5J,EAAA,IACA+L,EAAA/L,EAAA,IACAwB,EAAAxB,EAAA,+BACA0C,EAAA1C,EAAA,IACAyT,EAAAzT,EAAA,IAMAuU,EAAA7C,OAAAzL,UAAAuO,cAMA7U,GAAAD,QAAA+B,EAoDAA,EAAAwE,UAAAwO,QAAA,WACA3U,KAAAoK,KAAAhE,MAAApG,KAAAqG,UACA,QAAAyH,KAAA9N,MAAAsB,KACAmT,EAAAlU,KAAAP,KAAAsB,KAAAwM,IACA9N,KAAAsB,KAAAwM,GAAA1D,KAAAhE,MAAApG,KAAAsB,KAAAwM,GAAAzH,YAWA1E,EAAAwE,UAAAyO,gBAAA,WACA,OAAA9G,KAAA9N,MAAAsB,KACAmT,EAAAlU,KAAAP,KAAAsB,KAAAwM,KACA9N,KAAAsB,KAAAwM,GAAAzN,GAAAL,KAAA6U,WAAA/G,KAaAnM,EAAAwE,UAAA0O,WAAA,SAAA/G,GACA,aAAAA,EAAA,GAAAA,EAAA,KAAA9N,KAAA8U,OAAAzU,IAOAuP,EAAAjO,EAAAwE,WAUAxE,EAAAwE,UAAAiN,aAAA,SAAA5L,GACA,MAAAnB,WAAAjD,QACApD,KAAA+U,gBAAAvN,EACAxH,MAFAA,KAAA+U,eAaApT,EAAAwE,UAAAkN,qBAAA,SAAA7L,GACA,MAAAnB,WAAAjD,QACApD,KAAAgV,sBAAAxN,EACAxH,MAFAA,KAAAgV,uBAaArT,EAAAwE,UAAAoN,kBAAA,SAAA/L,GACA,MAAAnB,WAAAjD,QACApD,KAAAiV,mBAAAzN,EACAxH,KAAA0T,SAAA1T,KAAA0T,QAAAwB,OAAA1N,GACAxH,MAHAA,KAAAiV,oBAMAtT,EAAAwE,UAAAsN,oBAAA,SAAAjM,GACA,MAAAnB,WAAAjD,QACApD,KAAAmV,qBAAA3N,EACAxH,KAAA0T,SAAA1T,KAAA0T,QAAA0B,UAAA5N,GACAxH,MAHAA,KAAAmV,sBAcAxT,EAAAwE,UAAAqN,qBAAA,SAAAhM,GACA,MAAAnB,WAAAjD,QACApD,KAAAqV,sBAAA7N,EACAxH,KAAA0T,SAAA1T,KAAA0T,QAAA4B,OAAA9N,GACAxH,MAHAA,KAAAqV,uBAaA1T,EAAAwE,UAAA6C,QAAA,SAAAxB,GACA,MAAAnB,WAAAjD,QACApD,KAAAuV,SAAA/N,EACAxH,MAFAA,KAAAuV,UAYA5T,EAAAwE,UAAAqP,qBAAA,YAEAxV,KAAAyV,cAAAzV,KAAA+U,eAAA,IAAA/U,KAAA0T,QAAAgC,UAEA1V,KAAA2V,aAYAhU,EAAAwE,UAAAoO,KACA5S,EAAAwE,UAAAlE,QAAA,SAAA6O,EAAAjQ,GAEA,GADAa,EAAA,gBAAA1B,KAAA+T,aACA/T,KAAA+T,WAAAnR,QAAA,cAAA5C,KAEA0B,GAAA,aAAA1B,KAAAY,KACAZ,KAAA8U,OAAAN,EAAAxU,KAAAY,IAAAZ,KAAAa,KACA,IAAAgB,GAAA7B,KAAA8U,OACAzJ,EAAArL,IACAA,MAAA+T,WAAA,UACA/T,KAAA4V,eAAA,CAGA,IAAAC,GAAA/L,EAAAjI,EAAA,kBACAwJ,EAAAyK,SACAhF,SAIAiF,EAAAjM,EAAAjI,EAAA,iBAAA0B,GAKA,GAJA7B,EAAA,iBACA2J,EAAA2K,UACA3K,EAAA0I,WAAA,SACA1I,EAAAsJ,QAAA,gBAAApR,GACAuN,EAAA,CACA,GAAAnJ,GAAA,GAAAI,OAAA,mBACAJ,GAAApE,OACAuN,EAAAnJ,OAGA0D,GAAAmK,wBAKA,SAAAxV,KAAAuV,SAAA,CACA,GAAAvM,GAAAhJ,KAAAuV,QACA7T,GAAA,wCAAAsH,GAEA,IAAAA,GACA6M,EAAA3J,SAIA,IAAA+J,GAAA7N,WAAA,WACA1G,EAAA,qCAAAsH,GACA6M,EAAA3J,UACArK,EAAAqU,QACArU,EAAAuI,KAAA,mBACAiB,EAAAsJ,QAAA,kBAAA3L,IACKA,EAELhJ,MAAAmT,KAAA3J,MACA0C,QAAA,WACA1D,aAAAyN,MAQA,MAHAjW,MAAAmT,KAAA3J,KAAAqM,GACA7V,KAAAmT,KAAA3J,KAAAuM,GAEA/V,MASA2B,EAAAwE,UAAA2P,OAAA,WACApU,EAAA,QAGA1B,KAAAgW,UAGAhW,KAAA+T,WAAA,OACA/T,KAAAoK,KAAA,OAGA,IAAAvI,GAAA7B,KAAA8U,MACA9U,MAAAmT,KAAA3J,KAAAM,EAAAjI,EAAA,OAAAoK,EAAAjM,KAAA,YACAA,KAAAmT,KAAA3J,KAAAM,EAAAjI,EAAA,OAAAoK,EAAAjM,KAAA,YACAA,KAAAmT,KAAA3J,KAAAM,EAAAjI,EAAA,OAAAoK,EAAAjM,KAAA,YACAA,KAAAmT,KAAA3J,KAAAM,EAAAjI,EAAA,QAAAoK,EAAAjM,KAAA,aACAA,KAAAmT,KAAA3J,KAAAM,EAAAjI,EAAA,QAAAoK,EAAAjM,KAAA,aACAA,KAAAmT,KAAA3J,KAAAM,EAAA9J,KAAAqU,QAAA,UAAApI,EAAAjM,KAAA,gBASA2B,EAAAwE,UAAAgQ,OAAA,WACAnW,KAAAiU,SAAA,GAAA1I,MACAvL,KAAA2U,QAAA,SASAhT,EAAAwE,UAAAiQ,OAAA,WACApW,KAAA2U,QAAA,UAAApJ,MAAAvL,KAAAiU,WASAtS,EAAAwE,UAAAkQ,OAAA,SAAA9S,GACAvD,KAAAqU,QAAAjE,IAAA7M,IASA5B,EAAAwE,UAAAmQ,UAAA,SAAA5H,GACA1O,KAAAoK,KAAA,SAAAsE,IASA/M,EAAAwE,UAAAoQ,QAAA,SAAA5O,GACAjG,EAAA,QAAAiG,GACA3H,KAAA2U,QAAA,QAAAhN,IAUAhG,EAAAwE,UAAAtE,OAAA,SAAAiM,EAAAjN,GAiBA,QAAA2V,MACA5T,EAAAyI,EAAA2I,WAAAnS,IACAwJ,EAAA2I,WAAAxK,KAAA3H,GAlBA,GAAAA,GAAA7B,KAAAsB,KAAAwM,EACA,KAAAjM,EAAA,CACAA,EAAA,GAAAK,GAAAlC,KAAA8N,EAAAjN,GACAb,KAAAsB,KAAAwM,GAAAjM,CACA,IAAAwJ,GAAArL,IACA6B,GAAAiI,GAAA,aAAA0M,GACA3U,EAAAiI,GAAA,qBACAjI,EAAAxB,GAAAgL,EAAAwJ,WAAA/G,KAGA9N,KAAAsU,aAEAkC,IAUA,MAAA3U,IASAF,EAAAwE,UAAA+F,QAAA,SAAArK,GACA,GAAAiE,GAAAlD,EAAA5C,KAAAgU,WAAAnS,IACAiE,GAAA9F,KAAAgU,WAAA3Q,OAAAyC,EAAA,GACA9F,KAAAgU,WAAA5Q,QAEApD,KAAAkW,SAUAvU,EAAAwE,UAAAuI,OAAA,SAAAA,GACAhN,EAAA,oBAAAgN,EACA,IAAArD,GAAArL,IACA0O,GAAA9M,OAAA,IAAA8M,EAAAjK,OAAAiK,EAAAZ,KAAA,IAAAY,EAAA9M,OAEAyJ,EAAA8E,SAWA9E,EAAA6I,aAAA1K,KAAAkF,IATArD,EAAA8E,UAAA,EACAnQ,KAAAoU,QAAAlE,OAAAxB,EAAA,SAAA+H,GACA,OAAAtS,GAAA,EAAqBA,EAAAsS,EAAArT,OAA2Be,IAChDkH,EAAAyJ,OAAA4B,MAAAD,EAAAtS,GAAAuK,EAAAnB,QAEAlC,GAAA8E,UAAA,EACA9E,EAAAsL,yBAcAhV,EAAAwE,UAAAwQ,mBAAA,WACA,GAAA3W,KAAAkU,aAAA9Q,OAAA,IAAApD,KAAAmQ,SAAA,CACA,GAAA1B,GAAAzO,KAAAkU,aAAA0C,OACA5W,MAAA0O,OAAAD,KAUA9M,EAAAwE,UAAA6P,QAAA,WACAtU,EAAA,UAGA,QADAmV,GAAA7W,KAAAmT,KAAA/P,OACAe,EAAA,EAAiBA,EAAA0S,EAAgB1S,IAAA,CACjC,GAAA2S,GAAA9W,KAAAmT,KAAAyD,OACAE,GAAA5K,UAGAlM,KAAAkU,gBACAlU,KAAAmQ,UAAA,EACAnQ,KAAAiU,SAAA,KAEAjU,KAAAqU,QAAAnI,WASAvK,EAAAwE,UAAA+P,MACAvU,EAAAwE,UAAA4Q,WAAA,WACArV,EAAA,cACA1B,KAAA4V,eAAA,EACA5V,KAAAyV,cAAA,EACA,YAAAzV,KAAA+T,YAGA/T,KAAAgW,UAEAhW,KAAA0T,QAAAsD,QACAhX,KAAA+T,WAAA,SACA/T,KAAA8U,QAAA9U,KAAA8U,OAAAoB,SASAvU,EAAAwE,UAAA8Q,QAAA,SAAAC,GACAxV,EAAA,WAEA1B,KAAAgW,UACAhW,KAAA0T,QAAAsD,QACAhX,KAAA+T,WAAA,SACA/T,KAAAoK,KAAA,QAAA8M,GAEAlX,KAAA+U,gBAAA/U,KAAA4V,eACA5V,KAAA2V,aAUAhU,EAAAwE,UAAAwP,UAAA,WACA,GAAA3V,KAAAyV,cAAAzV,KAAA4V,cAAA,MAAA5V,KAEA,IAAAqL,GAAArL,IAEA,IAAAA,KAAA0T,QAAAgC,UAAA1V,KAAAgV,sBACAtT,EAAA,oBACA1B,KAAA0T,QAAAsD,QACAhX,KAAA2U,QAAA,oBACA3U,KAAAyV,cAAA,MACG,CACH,GAAA0B,GAAAnX,KAAA0T,QAAA0D,UACA1V,GAAA,0CAAAyV,GAEAnX,KAAAyV,cAAA,CACA,IAAAQ,GAAA7N,WAAA,WACAiD,EAAAuK,gBAEAlU,EAAA,wBACA2J,EAAAsJ,QAAA,oBAAAtJ,EAAAqI,QAAAgC,UACArK,EAAAsJ,QAAA,eAAAtJ,EAAAqI,QAAAgC,UAGArK,EAAAuK,eAEAvK,EAAAkJ,KAAA,SAAA5M,GACAA,GACAjG,EAAA,2BACA2J,EAAAoK,cAAA,EACApK,EAAAsK,YACAtK,EAAAsJ,QAAA,kBAAAhN,EAAApE,QAEA7B,EAAA,qBACA2J,EAAAgM,mBAGKF,EAELnX,MAAAmT,KAAA3J,MACA0C,QAAA,WACA1D,aAAAyN,QAYAtU,EAAAwE,UAAAkR,YAAA,WACA,GAAAC,GAAAtX,KAAA0T,QAAAgC,QACA1V,MAAAyV,cAAA,EACAzV,KAAA0T,QAAAsD,QACAhX,KAAA4U,kBACA5U,KAAA2U,QAAA,YAAA2C,Kdy0DM,SAAUzX,EAAQD,EAASM,Gev4EjCL,EAAAD,QAAAM,EAAA,IAQAL,EAAAD,QAAAkC,OAAA5B,EAAA,Kf+4EM,SAAUL,EAAQD,EAASM,GgB93EjC,QAAAgC,GAAAtB,EAAAC,GACA,MAAAb,gBAAAkC,IAEArB,QAEAD,GAAA,gBAAAA,KACAC,EAAAD,EACAA,EAAA,MAGAA,GACAA,EAAA6B,EAAA7B,GACAC,EAAA0W,SAAA3W,EAAA0B,KACAzB,EAAA2W,OAAA,UAAA5W,EAAAoB,UAAA,QAAApB,EAAAoB,SACAnB,EAAA6B,KAAA9B,EAAA8B,KACA9B,EAAAgB,QAAAf,EAAAe,MAAAhB,EAAAgB,QACGf,EAAAyB,OACHzB,EAAA0W,SAAA9U,EAAA5B,EAAAyB,YAGAtC,KAAAwX,OAAA,MAAA3W,EAAA2W,OAAA3W,EAAA2W,OACA,mBAAAnV,WAAA,WAAAA,SAAAL,SAEAnB,EAAA0W,WAAA1W,EAAA6B,OAEA7B,EAAA6B,KAAA1C,KAAAwX,OAAA,YAGAxX,KAAAyX,MAAA5W,EAAA4W,QAAA,EACAzX,KAAAuX,SAAA1W,EAAA0W,WACA,mBAAAlV,mBAAAkV,SAAA,aACAvX,KAAA0C,KAAA7B,EAAA6B,OAAA,mBAAAL,oBAAAK,KACAL,SAAAK,KACA1C,KAAAwX,OAAA,QACAxX,KAAA4B,MAAAf,EAAAe,UACA,gBAAA5B,MAAA4B,QAAA5B,KAAA4B,MAAA8V,EAAAC,OAAA3X,KAAA4B,QACA5B,KAAA4X,SAAA,IAAA/W,EAAA+W,QACA5X,KAAAmB,MAAAN,EAAAM,MAAA,cAAA8B,QAAA,cACAjD,KAAA6X,aAAAhX,EAAAgX,WACA7X,KAAA8X,OAAA,IAAAjX,EAAAiX,MACA9X,KAAA+X,cAAAlX,EAAAkX,YACA/X,KAAAgY,aAAAnX,EAAAmX,WACAhY,KAAAiY,iBAAA,IAAApX,EAAAoX,gBACAjY,KAAAkY,eAAArX,EAAAqX,gBAAA,IACAlY,KAAAmY,kBAAAtX,EAAAsX,kBACAnY,KAAAoY,WAAAvX,EAAAuX,aAAA,uBACApY,KAAAqY,iBAAAxX,EAAAwX,qBACArY,KAAA+T,WAAA,GACA/T,KAAAsY,eACAtY,KAAAuY,cAAA,EACAvY,KAAAwY,WAAA3X,EAAA2X,YAAA,IACAxY,KAAAyY,gBAAA5X,EAAA4X,kBAAA,EACAzY,KAAA0Y,WAAA,KACA1Y,KAAA2Y,mBAAA9X,EAAA8X,mBACA3Y,KAAA4Y,mBAAA,IAAA/X,EAAA+X,oBAAA/X,EAAA+X,wBAEA,IAAA5Y,KAAA4Y,oBAAA5Y,KAAA4Y,sBACA5Y,KAAA4Y,mBAAA,MAAA5Y,KAAA4Y,kBAAAC,YACA7Y,KAAA4Y,kBAAAC,UAAA,MAIA7Y,KAAA8Y,IAAAjY,EAAAiY,KAAA,KACA9Y,KAAA2Q,IAAA9P,EAAA8P,KAAA,KACA3Q,KAAA+Y,WAAAlY,EAAAkY,YAAA,KACA/Y,KAAAgZ,KAAAnY,EAAAmY,MAAA,KACAhZ,KAAAiZ,GAAApY,EAAAoY,IAAA,KACAjZ,KAAAkZ,QAAArY,EAAAqY,SAAA,KACAlZ,KAAAmZ,mBAAArY,SAAAD,EAAAsY,oBAAAtY,EAAAsY,mBACAnZ,KAAAoZ,YAAAvY,EAAAuY,UAGApZ,KAAAqZ,cAAA,mBAAA3U,YAAA,gBAAAA,WAAA4U,SAAA,gBAAA5U,UAAA4U,QAAA1U,eAGA,mBAAAyG,OAAArL,KAAAqZ,iBACAxY,EAAA0Y,cAAA3H,OAAA4H,KAAA3Y,EAAA0Y,cAAAnW,OAAA,IACApD,KAAAuZ,aAAA1Y,EAAA0Y,cAGA1Y,EAAA4Y,eACAzZ,KAAAyZ,aAAA5Y,EAAA4Y,eAKAzZ,KAAAK,GAAA,KACAL,KAAA0Z,SAAA,KACA1Z,KAAA2Z,aAAA,KACA3Z,KAAA4Z,YAAA,KAGA5Z,KAAA6Z,kBAAA,KACA7Z,KAAA8Z,iBAAA,SAEA9Z,MAAAuU,QA9FA,GAAArS,GAAAtB,EAAAC,GAsLA,QAAAkZ,GAAA3X,GACA,GAAA4X,KACA,QAAA7V,KAAA/B,GACAA,EAAAsS,eAAAvQ,KACA6V,EAAA7V,GAAA/B,EAAA+B,GAGA,OAAA6V,GApNA,GAAA5B,GAAAlY,EAAA,IACA0P,EAAA1P,EAAA,GACAwB,EAAAxB,EAAA,8BACA4F,EAAA5F,EAAA,IACA4B,EAAA5B,EAAA,IACAuC,EAAAvC,EAAA,GACAwX,EAAAxX,EAAA,GAMAL,GAAAD,QAAAsC,EA4GAA,EAAA+X,uBAAA,EAMArK,EAAA1N,EAAAiE,WAQAjE,EAAAF,SAAAF,EAAAE,SAOAE,WACAA,EAAAgY,UAAAha,EAAA,IACAgC,EAAAkW,WAAAlY,EAAA,IACAgC,EAAAJ,OAAA5B,EAAA,IAUAgC,EAAAiE,UAAAgU,gBAAA,SAAA3P,GACA9I,EAAA,0BAAA8I,EACA,IAAA5I,GAAAmY,EAAA/Z,KAAA4B,MAGAA,GAAAwY,IAAAtY,EAAAE,SAGAJ,EAAAyY,UAAA7P,CAGA,IAAA+C,GAAAvN,KAAAqY,iBAAA7N,MAGAxK,MAAAK,KAAAuB,EAAA0Y,IAAAta,KAAAK,GAEA,IAAAga,GAAA,GAAAjC,GAAA5N,IACA5I,QACAC,OAAA7B,KACAyX,MAAAlK,EAAAkK,OAAAzX,KAAAyX,MACAF,SAAAhK,EAAAgK,UAAAvX,KAAAuX,SACA7U,KAAA6K,EAAA7K,MAAA1C,KAAA0C,KACA8U,OAAAjK,EAAAiK,QAAAxX,KAAAwX,OACArW,KAAAoM,EAAApM,MAAAnB,KAAAmB,KACA0W,WAAAtK,EAAAsK,YAAA7X,KAAA6X,WACAC,MAAAvK,EAAAuK,OAAA9X,KAAA8X,MACAC,YAAAxK,EAAAwK,aAAA/X,KAAA+X,YACAC,WAAAzK,EAAAyK,YAAAhY,KAAAgY,WACAC,gBAAA1K,EAAA0K,iBAAAjY,KAAAiY,gBACAE,kBAAA5K,EAAA4K,mBAAAnY,KAAAmY,kBACAD,eAAA3K,EAAA2K,gBAAAlY,KAAAkY,eACAM,WAAAjL,EAAAiL,YAAAxY,KAAAwY,WACAM,IAAAvL,EAAAuL,KAAA9Y,KAAA8Y,IACAnI,IAAApD,EAAAoD,KAAA3Q,KAAA2Q,IACAoI,WAAAxL,EAAAwL,YAAA/Y,KAAA+Y,WACAC,KAAAzL,EAAAyL,MAAAhZ,KAAAgZ,KACAC,GAAA1L,EAAA0L,IAAAjZ,KAAAiZ,GACAC,QAAA3L,EAAA2L,SAAAlZ,KAAAkZ,QACAC,mBAAA5L,EAAA4L,oBAAAnZ,KAAAmZ,mBACAP,kBAAArL,EAAAqL,mBAAA5Y,KAAA4Y,kBACAW,aAAAhM,EAAAgM,cAAAvZ,KAAAuZ,aACAH,UAAA7L,EAAA6L,WAAApZ,KAAAoZ,UACAK,aAAAlM,EAAAkM,cAAAzZ,KAAAyZ,aACAc,eAAAhN,EAAAgN,gBAAAva,KAAAua,eACAC,UAAAjN,EAAAiN,WAAA,OACAnB,cAAArZ,KAAAqZ,eAGA,OAAAgB,IAkBAnY,EAAAiE,UAAAoO,KAAA,WACA,GAAA8F,EACA,IAAAra,KAAAyY,iBAAAvW,EAAA+X,uBAAAja,KAAAoY,WAAAxV,QAAA,kBACAyX,EAAA,gBACG,QAAAra,KAAAoY,WAAAhV,OAAA,CAEH,GAAAiI,GAAArL,IAIA,YAHAoI,YAAA,WACAiD,EAAAjB,KAAA,oCACK,GAGLiQ,EAAAra,KAAAoY,WAAA,GAEApY,KAAA+T,WAAA,SAGA,KACAsG,EAAAra,KAAAma,gBAAAE,GACG,MAAArW,GAGH,MAFAhE,MAAAoY,WAAAxB,YACA5W,MAAAuU,OAIA8F,EAAA9F,OACAvU,KAAAya,aAAAJ,IASAnY,EAAAiE,UAAAsU,aAAA,SAAAJ,GACA3Y,EAAA,uBAAA2Y,EAAA7P,KACA,IAAAa,GAAArL,IAEAA,MAAAqa,YACA3Y,EAAA,iCAAA1B,KAAAqa,UAAA7P,MACAxK,KAAAqa,UAAAlQ,sBAIAnK,KAAAqa,YAGAA,EACAvQ,GAAA,mBACAuB,EAAAqP,YAEA5Q,GAAA,kBAAA4E,GACArD,EAAAsP,SAAAjM,KAEA5E,GAAA,iBAAA9F,GACAqH,EAAAuP,QAAA5W,KAEA8F,GAAA,mBACAuB,EAAAwP,QAAA,sBAWA3Y,EAAAiE,UAAA2U,MAAA,SAAAtQ,GAQA,QAAAuQ,KACA,GAAA1P,EAAAsN,mBAAA,CACA,GAAAqC,IAAAhb,KAAAib,gBAAA5P,EAAAgP,UAAAY,cACAC,MAAAF,EAEAE,IAEAxZ,EAAA,8BAAA8I,GACA6P,EAAAc,OAAqB1W,KAAA,OAAAlB,KAAA,WACrB8W,EAAArQ,KAAA,kBAAA2F,GACA,IAAAuL,EACA,YAAAvL,EAAAlL,MAAA,UAAAkL,EAAApM,KAAA,CAIA,GAHA7B,EAAA,4BAAA8I,GACAa,EAAA+P,WAAA,EACA/P,EAAAjB,KAAA,YAAAiQ,IACAA,EAAA,MACAnY,GAAA+X,sBAAA,cAAAI,EAAA7P,KAEA9I,EAAA,iCAAA2J,EAAAgP,UAAA7P,MACAa,EAAAgP,UAAAgB,MAAA,WACAH,GACA,WAAA7P,EAAA0I,aACArS,EAAA,iDAEAsU,IAEA3K,EAAAoP,aAAAJ,GACAA,EAAAc,OAA2B1W,KAAA,aAC3B4G,EAAAjB,KAAA,UAAAiQ,GACAA,EAAA,KACAhP,EAAA+P,WAAA,EACA/P,EAAAiQ,eAEO,CACP5Z,EAAA,8BAAA8I,EACA,IAAA7C,GAAA,GAAAI,OAAA,cACAJ,GAAA0S,YAAA7P,KACAa,EAAAjB,KAAA,eAAAzC,OAKA,QAAA4T,KACAL,IAGAA,GAAA,EAEAlF,IAEAqE,EAAAnE,QACAmE,EAAA,MAIA,QAAA9D,GAAA5O,GACA,GAAAuH,GAAA,GAAAnH,OAAA,gBAAAJ,EACAuH,GAAAmL,YAAA7P,KAEA+Q,IAEA7Z,EAAA,mDAAA8I,EAAA7C,GAEA0D,EAAAjB,KAAA,eAAA8E,GAGA,QAAAsM,KACAjF,EAAA,oBAIA,QAAAU,KACAV,EAAA,iBAIA,QAAAkF,GAAAC,GACArB,GAAAqB,EAAAlR,OAAA6P,EAAA7P,OACA9I,EAAA,6BAAAga,EAAAlR,KAAA6P,EAAA7P,MACA+Q,KAKA,QAAAvF,KACAqE,EAAAnQ,eAAA,OAAA6Q,GACAV,EAAAnQ,eAAA,QAAAqM,GACA8D,EAAAnQ,eAAA,QAAAsR,GACAnQ,EAAAnB,eAAA,QAAA+M,GACA5L,EAAAnB,eAAA,YAAAuR,GAhGA/Z,EAAA,yBAAA8I,EACA,IAAA6P,GAAAra,KAAAma,gBAAA3P,GAA8CsQ,MAAA,IAC9CI,GAAA,EACA7P,EAAArL,IAEAkC,GAAA+X,uBAAA,EA8FAI,EAAArQ,KAAA,OAAA+Q,GACAV,EAAArQ,KAAA,QAAAuM,GACA8D,EAAArQ,KAAA,QAAAwR,GAEAxb,KAAAgK,KAAA,QAAAiN,GACAjX,KAAAgK,KAAA,YAAAyR,GAEApB,EAAA9F,QASArS,EAAAiE,UAAAwV,OAAA,WASA,GARAja,EAAA,eACA1B,KAAA+T,WAAA,OACA7R,EAAA+X,sBAAA,cAAAja,KAAAqa,UAAA7P,KACAxK,KAAAoK,KAAA,QACApK,KAAAsb,QAIA,SAAAtb,KAAA+T,YAAA/T,KAAA4X,SAAA5X,KAAAqa,UAAAgB,MAAA,CACA3Z,EAAA,0BACA,QAAAyC,GAAA,EAAAyX,EAAA5b,KAAA0Z,SAAAtW,OAA6Ce,EAAAyX,EAAOzX,IACpDnE,KAAA8a,MAAA9a,KAAA0Z,SAAAvV,MAWAjC,EAAAiE,UAAAwU,SAAA,SAAAjM,GACA,eAAA1O,KAAA+T,YAAA,SAAA/T,KAAA+T,YACA,YAAA/T,KAAA+T,WAQA,OAPArS,EAAA,uCAAAgN,EAAAjK,KAAAiK,EAAAnL,MAEAvD,KAAAoK,KAAA,SAAAsE,GAGA1O,KAAAoK,KAAA,aAEAsE,EAAAjK,MACA,WACAzE,KAAA6b,YAAApU,KAAAgF,MAAAiC,EAAAnL,MACA,MAEA,YACAvD,KAAA8b,UACA9b,KAAAoK,KAAA,OACA,MAEA,aACA,GAAAzC,GAAA,GAAAI,OAAA,eACAJ,GAAAoU,KAAArN,EAAAnL,KACAvD,KAAA4a,QAAAjT,EACA,MAEA,eACA3H,KAAAoK,KAAA,OAAAsE,EAAAnL,MACAvD,KAAAoK,KAAA,UAAAsE,EAAAnL,UAIA7B,GAAA,8CAAA1B,KAAA+T,aAWA7R,EAAAiE,UAAA0V,YAAA,SAAAtY,GACAvD,KAAAoK,KAAA,YAAA7G,GACAvD,KAAAK,GAAAkD,EAAA+W,IACAta,KAAAqa,UAAAzY,MAAA0Y,IAAA/W,EAAA+W,IACAta,KAAA0Z,SAAA1Z,KAAAgc,eAAAzY,EAAAmW,UACA1Z,KAAA2Z,aAAApW,EAAAoW,aACA3Z,KAAA4Z,YAAArW,EAAAqW,YACA5Z,KAAA2b,SAEA,WAAA3b,KAAA+T,aACA/T,KAAA8b,UAGA9b,KAAAkK,eAAA,YAAAlK,KAAAic,aACAjc,KAAA8J,GAAA,YAAA9J,KAAAic,eASA/Z,EAAAiE,UAAA8V,YAAA,SAAAjT,GACAR,aAAAxI,KAAA8Z,iBACA,IAAAzO,GAAArL,IACAqL,GAAAyO,iBAAA1R,WAAA,WACA,WAAAiD,EAAA0I,YACA1I,EAAAwP,QAAA,iBACG7R,GAAAqC,EAAAsO,aAAAtO,EAAAuO,cAUH1X,EAAAiE,UAAA2V,QAAA,WACA,GAAAzQ,GAAArL,IACAwI,cAAA6C,EAAAwO,mBACAxO,EAAAwO,kBAAAzR,WAAA,WACA1G,EAAA,mDAAA2J,EAAAuO,aACAvO,EAAA6Q,OACA7Q,EAAA4Q,YAAA5Q,EAAAuO,cACGvO,EAAAsO,eASHzX,EAAAiE,UAAA+V,KAAA,WACA,GAAA7Q,GAAArL,IACAA,MAAAmc,WAAA,kBACA9Q,EAAAjB,KAAA,WAUAlI,EAAAiE,UAAAuU,QAAA,WACA1a,KAAAsY,YAAAjV,OAAA,EAAArD,KAAAuY,eAKAvY,KAAAuY,cAAA,EAEA,IAAAvY,KAAAsY,YAAAlV,OACApD,KAAAoK,KAAA,SAEApK,KAAAsb,SAUApZ,EAAAiE,UAAAmV,MAAA,WACA,WAAAtb,KAAA+T,YAAA/T,KAAAqa,UAAA+B,WACApc,KAAAob,WAAApb,KAAAsY,YAAAlV,SACA1B,EAAA,gCAAA1B,KAAAsY,YAAAlV,QACApD,KAAAqa,UAAAc,KAAAnb,KAAAsY,aAGAtY,KAAAuY,cAAAvY,KAAAsY,YAAAlV,OACApD,KAAAoK,KAAA,WAcAlI,EAAAiE,UAAAuQ,MACAxU,EAAAiE,UAAAgV,KAAA,SAAAxL,EAAApC,EAAAuD,GAEA,MADA9Q,MAAAmc,WAAA,UAAAxM,EAAApC,EAAAuD,GACA9Q,MAaAkC,EAAAiE,UAAAgW,WAAA,SAAA1X,EAAAlB,EAAAgK,EAAAuD,GAWA,GAVA,kBAAAvN,KACAuN,EAAAvN,EACAA,EAAAzC,QAGA,kBAAAyM,KACAuD,EAAAvD,EACAA,EAAA,MAGA,YAAAvN,KAAA+T,YAAA,WAAA/T,KAAA+T,WAAA,CAIAxG,QACAA,EAAA8O,UAAA,IAAA9O,EAAA8O,QAEA,IAAA3N,IACAjK,OACAlB,OACAgK,UAEAvN,MAAAoK,KAAA,eAAAsE,GACA1O,KAAAsY,YAAA9O,KAAAkF,GACAoC,GAAA9Q,KAAAgK,KAAA,QAAA8G,GACA9Q,KAAAsb,UASApZ,EAAAiE,UAAA+P,MAAA,WAqBA,QAAAA,KACA7K,EAAAwP,QAAA,gBACAnZ,EAAA,+CACA2J,EAAAgP,UAAAnE,QAGA,QAAAoG,KACAjR,EAAAnB,eAAA,UAAAoS,GACAjR,EAAAnB,eAAA,eAAAoS,GACApG,IAGA,QAAAqG,KAEAlR,EAAArB,KAAA,UAAAsS,GACAjR,EAAArB,KAAA,eAAAsS,GAnCA,eAAAtc,KAAA+T,YAAA,SAAA/T,KAAA+T,WAAA,CACA/T,KAAA+T,WAAA,SAEA,IAAA1I,GAAArL,IAEAA,MAAAsY,YAAAlV,OACApD,KAAAgK,KAAA,mBACAhK,KAAAob,UACAmB,IAEArG,MAGKlW,KAAAob,UACLmB,IAEArG,IAsBA,MAAAlW,OASAkC,EAAAiE,UAAAyU,QAAA,SAAAjT,GACAjG,EAAA,kBAAAiG,GACAzF,EAAA+X,uBAAA,EACAja,KAAAoK,KAAA,QAAAzC,GACA3H,KAAA6a,QAAA,kBAAAlT,IASAzF,EAAAiE,UAAA0U,QAAA,SAAA3D,EAAAsF,GACA,eAAAxc,KAAA+T,YAAA,SAAA/T,KAAA+T,YAAA,YAAA/T,KAAA+T,WAAA,CACArS,EAAA,iCAAAwV,EACA,IAAA7L,GAAArL,IAGAwI,cAAAxI,KAAA6Z,mBACArR,aAAAxI,KAAA8Z,kBAGA9Z,KAAAqa,UAAAlQ,mBAAA,SAGAnK,KAAAqa,UAAAnE;AAGAlW,KAAAqa,UAAAlQ,qBAGAnK,KAAA+T,WAAA,SAGA/T,KAAAK,GAAA,KAGAL,KAAAoK,KAAA,QAAA8M,EAAAsF,GAIAnR,EAAAiN,eACAjN,EAAAkN,cAAA,IAYArW,EAAAiE,UAAA6V,eAAA,SAAAtC,GAEA,OADA+C,MACAtY,EAAA,EAAAoD,EAAAmS,EAAAtW,OAAsCe,EAAAoD,EAAOpD,KAC7C2B,EAAA9F,KAAAoY,WAAAsB,EAAAvV,KAAAsY,EAAAjT,KAAAkQ,EAAAvV,GAEA,OAAAsY,KhBg6EM,SAAU5c,EAAQD,EAASM,GiBnnGjC,QAAAwc,GAAA7b,GACA,GAAA8b,GACAC,GAAA,EACAC,GAAA,EACA/E,GAAA,IAAAjX,EAAAiX,KAEA,uBAAAzV,UAAA,CACA,GAAAya,GAAA,WAAAza,SAAAL,SACAU,EAAAL,SAAAK,IAGAA,KACAA,EAAAoa,EAAA,QAGAF,EAAA/b,EAAA0W,WAAAlV,SAAAkV,UAAA7U,IAAA7B,EAAA6B,KACAma,EAAAhc,EAAA2W,SAAAsF,EAOA,GAJAjc,EAAAkc,QAAAH,EACA/b,EAAAmc,QAAAH,EACAF,EAAA,GAAAM,GAAApc,GAEA,QAAA8b,KAAA9b,EAAAgX,WACA,UAAAqF,GAAArc,EAEA,KAAAiX,EAAA,SAAA/P,OAAA,iBACA,WAAAoV,GAAAtc,GA9CA,GAAAoc,GAAA/c,EAAA,IACAgd,EAAAhd,EAAA,IACAid,EAAAjd,EAAA,IACAkd,EAAAld,EAAA,GAMAN,GAAA8c,UACA9c,EAAAwd,ajBurGM,SAAUvd,EAAQD,EAASM,GkBnsGjC,GAAAmd,GAAAnd,EAAA,IACAod,EAAApd,EAAA,GAEAL,GAAAD,QAAA,SAAAiB,GACA,GAAAkc,GAAAlc,EAAAkc,QAIAC,EAAAnc,EAAAmc,QAIAhF,EAAAnX,EAAAmX,UAGA,KACA,sBAAAiF,mBAAAF,GAAAM,GACA,UAAAJ,gBAEG,MAAAjZ,IAKH,IACA,sBAAAuZ,kBAAAP,GAAAhF,EACA,UAAAuF,gBAEG,MAAAvZ,IAEH,IAAA+Y,EACA,IACA,WAAAO,GAAA,UAAAzU,OAAA,UAAA2U,KAAA,4BACK,MAAAxZ,OlB8sGC,SAAUnE,EAAQD,GmBxuGxB,IACAC,EAAAD,QAAA,mBAAAqd,iBACA,uBAAAA,gBACC,MAAAtV,GAGD9H,EAAAD,SAAA,InByvGM,SAAUC,EAAQD,GoBxwGxBC,EAAAD,QAAA,WACA,yBAAAyL,MACAA,KACG,mBAAA7G,QACHA,OAEA0B,SAAA,qBpBixGM,SAAUrG,EAAQD,EAASM,GqB/vGjC,QAAAud,MASA,QAAAP,GAAArc,GAKA,GAJA6c,EAAAnd,KAAAP,KAAAa,GACAb,KAAAua,eAAA1Z,EAAA0Z,eACAva,KAAAuZ,aAAA1Y,EAAA0Y,aAEA,mBAAAlX,UAAA,CACA,GAAAya,GAAA,WAAAza,SAAAL,SACAU,EAAAL,SAAAK,IAGAA,KACAA,EAAAoa,EAAA,QAGA9c,KAAA4c,GAAA,mBAAAva,WAAAxB,EAAA0W,WAAAlV,SAAAkV,UACA7U,IAAA7B,EAAA6B,KACA1C,KAAA6c,GAAAhc,EAAA2W,SAAAsF,GA8FA,QAAAa,GAAA9c,GACAb,KAAA4d,OAAA/c,EAAA+c,QAAA,MACA5d,KAAAY,IAAAC,EAAAD,IACAZ,KAAA4c,KAAA/b,EAAA+b,GACA5c,KAAA6c,KAAAhc,EAAAgc,GACA7c,KAAA6d,OAAA,IAAAhd,EAAAgd,MACA7d,KAAAuD,KAAAzC,SAAAD,EAAA0C,KAAA1C,EAAA0C,KAAA,KACAvD,KAAAyX,MAAA5W,EAAA4W,MACAzX,KAAA8d,SAAAjd,EAAAid,SACA9d,KAAAib,eAAApa,EAAAoa,eACAjb,KAAAgY,WAAAnX,EAAAmX,WACAhY,KAAAiY,gBAAApX,EAAAoX,gBACAjY,KAAAua,eAAA1Z,EAAA0Z,eAGAva,KAAA8Y,IAAAjY,EAAAiY,IACA9Y,KAAA2Q,IAAA9P,EAAA8P,IACA3Q,KAAA+Y,WAAAlY,EAAAkY,WACA/Y,KAAAgZ,KAAAnY,EAAAmY,KACAhZ,KAAAiZ,GAAApY,EAAAoY,GACAjZ,KAAAkZ,QAAArY,EAAAqY,QACAlZ,KAAAmZ,mBAAAtY,EAAAsY,mBAGAnZ,KAAAuZ,aAAA1Y,EAAA0Y,aAEAvZ,KAAA+d,SAkPA,QAAAC,KACA,OAAA7Z,KAAAwZ,GAAAM,SACAN,EAAAM,SAAAvJ,eAAAvQ,IACAwZ,EAAAM,SAAA9Z,GAAA+Z,QAxZA,GAAAjB,GAAA/c,EAAA,IACAwd,EAAAxd,EAAA,IACA0P,EAAA1P,EAAA,GACAie,EAAAje,EAAA,IACAwB,EAAAxB,EAAA,mCACAod,EAAApd,EAAA,GAuYA,IAjYAL,EAAAD,QAAAsd,EACArd,EAAAD,QAAA+d,UAuCAQ,EAAAjB,EAAAQ,GAMAR,EAAA/W,UAAA8U,gBAAA,EASAiC,EAAA/W,UAAAiY,QAAA,SAAAvd,GAuBA,MAtBAA,SACAA,EAAAD,IAAAZ,KAAAY,MACAC,EAAA+b,GAAA5c,KAAA4c,GACA/b,EAAAgc,GAAA7c,KAAA6c,GACAhc,EAAA4W,MAAAzX,KAAAyX,QAAA,EACA5W,EAAAoa,eAAAjb,KAAAib,eACApa,EAAAmX,WAAAhY,KAAAgY,WACAnX,EAAAoX,gBAAAjY,KAAAiY,gBAGApX,EAAAiY,IAAA9Y,KAAA8Y,IACAjY,EAAA8P,IAAA3Q,KAAA2Q,IACA9P,EAAAkY,WAAA/Y,KAAA+Y,WACAlY,EAAAmY,KAAAhZ,KAAAgZ,KACAnY,EAAAoY,GAAAjZ,KAAAiZ,GACApY,EAAAqY,QAAAlZ,KAAAkZ,QACArY,EAAAsY,mBAAAnZ,KAAAmZ,mBACAtY,EAAA0Z,eAAAva,KAAAua,eAGA1Z,EAAA0Y,aAAAvZ,KAAAuZ,aAEA,GAAAoE,GAAA9c,IAWAqc,EAAA/W,UAAAkY,QAAA,SAAA9a,EAAAuN,GACA,GAAAgN,GAAA,gBAAAva,IAAAzC,SAAAyC,EACA+a,EAAAte,KAAAoe,SAA0BR,OAAA,OAAAra,OAAAua,aAC1BzS,EAAArL,IACAse,GAAAxU,GAAA,UAAAgH,GACAwN,EAAAxU,GAAA,iBAAAnC,GACA0D,EAAAuP,QAAA,iBAAAjT,KAEA3H,KAAAue,QAAAD,GASApB,EAAA/W,UAAAqY,OAAA,WACA9c,EAAA,WACA,IAAA4c,GAAAte,KAAAoe,UACA/S,EAAArL,IACAse,GAAAxU,GAAA,gBAAAvG,GACA8H,EAAAoT,OAAAlb,KAEA+a,EAAAxU,GAAA,iBAAAnC,GACA0D,EAAAuP,QAAA,iBAAAjT,KAEA3H,KAAA0e,QAAAJ,GA2CA1O,EAAA+N,EAAAxX,WAQAwX,EAAAxX,UAAA4X,OAAA,WACA,GAAAld,IAAc4W,MAAAzX,KAAAyX,MAAAsF,QAAA/c,KAAA4c,GAAAI,QAAAhd,KAAA6c,GAAA7E,WAAAhY,KAAAgY,WAGdnX,GAAAiY,IAAA9Y,KAAA8Y,IACAjY,EAAA8P,IAAA3Q,KAAA2Q,IACA9P,EAAAkY,WAAA/Y,KAAA+Y,WACAlY,EAAAmY,KAAAhZ,KAAAgZ,KACAnY,EAAAoY,GAAAjZ,KAAAiZ,GACApY,EAAAqY,QAAAlZ,KAAAkZ,QACArY,EAAAsY,mBAAAnZ,KAAAmZ,kBAEA,IAAAwD,GAAA3c,KAAA2c,IAAA,GAAAM,GAAApc,GACAwK,EAAArL,IAEA,KACA0B,EAAA,kBAAA1B,KAAA4d,OAAA5d,KAAAY,KACA+b,EAAApI,KAAAvU,KAAA4d,OAAA5d,KAAAY,IAAAZ,KAAA6d,MACA,KACA,GAAA7d,KAAAuZ,aAAA,CACAoD,EAAAgC,uBAAAhC,EAAAgC,uBAAA,EACA,QAAAxa,KAAAnE,MAAAuZ,aACAvZ,KAAAuZ,aAAA7E,eAAAvQ,IACAwY,EAAAiC,iBAAAza,EAAAnE,KAAAuZ,aAAApV,KAIK,MAAAH,IAEL,YAAAhE,KAAA4d,OACA,IACA5d,KAAA8d,SACAnB,EAAAiC,iBAAA,2CAEAjC,EAAAiC,iBAAA,2CAEO,MAAA5a,IAGP,IACA2Y,EAAAiC,iBAAA,gBACK,MAAA5a,IAGL,mBAAA2Y,KACAA,EAAA1E,gBAAAjY,KAAAiY,iBAGAjY,KAAAua,iBACAoC,EAAA3T,QAAAhJ,KAAAua,gBAGAva,KAAA6e,UACAlC,EAAAnK,OAAA,WACAnH,EAAAyT,UAEAnC,EAAApG,QAAA,WACAlL,EAAAuP,QAAA+B,EAAAoC,gBAGApC,EAAAqC,mBAAA,WACA,OAAArC,EAAA5I,WACA,IACA,GAAAkL,GAAAtC,EAAAuC,kBAAA,iBACA7T,EAAA4P,gBAAA,6BAAAgE,GAAA,4CAAAA,KACAtC,EAAAwC,aAAA,eAEW,MAAAnb,IAEX,IAAA2Y,EAAA5I,aACA,MAAA4I,EAAAyC,QAAA,OAAAzC,EAAAyC,OACA/T,EAAAyT,SAIA1W,WAAA,WACAiD,EAAAuP,QAAA,gBAAA+B,GAAAyC,OAAAzC,EAAAyC,OAAA,IACW,KAKX1d,EAAA,cAAA1B,KAAAuD,MACAoZ,EAAAxB,KAAAnb,KAAAuD,MACG,MAAAS,GAOH,WAHAoE,YAAA,WACAiD,EAAAuP,QAAA5W,IACK,GAIL,mBAAAc,YACA9E,KAAA8F,MAAA6X,EAAA0B,gBACA1B,EAAAM,SAAAje,KAAA8F,OAAA9F,OAUA2d,EAAAxX,UAAAmZ,UAAA,WACAtf,KAAAoK,KAAA,WACApK,KAAAgW,WASA2H,EAAAxX,UAAAsY,OAAA,SAAAlb,GACAvD,KAAAoK,KAAA,OAAA7G,GACAvD,KAAAsf,aASA3B,EAAAxX,UAAAyU,QAAA,SAAAjT,GACA3H,KAAAoK,KAAA,QAAAzC,GACA3H,KAAAgW,SAAA,IASA2H,EAAAxX,UAAA6P,QAAA,SAAAuJ,GACA,sBAAAvf,MAAA2c,KAAA,OAAA3c,KAAA2c,IAAA,CAUA,GANA3c,KAAA6e,SACA7e,KAAA2c,IAAAnK,OAAAxS,KAAA2c,IAAApG,QAAAkH,EAEAzd,KAAA2c,IAAAqC,mBAAAvB,EAGA8B,EACA,IACAvf,KAAA2c,IAAAuB,QACK,MAAAla,IAGL,mBAAAc,iBACA6Y,GAAAM,SAAAje,KAAA8F,OAGA9F,KAAA2c,IAAA,OASAgB,EAAAxX,UAAA2Y,OAAA,WACA,GAAAvb,EACA,KACA,GAAA0b,EACA,KACAA,EAAAjf,KAAA2c,IAAAuC,kBAAA,gBACK,MAAAlb,IAELT,EADA,6BAAA0b,GAAA,4CAAAA,EACAjf,KAAA2c,IAAA6C,UAAAxf,KAAA2c,IAAAoC,aAEA/e,KAAA2c,IAAAoC,aAEG,MAAA/a,GACHhE,KAAA4a,QAAA5W,GAEA,MAAAT,GACAvD,KAAAye,OAAAlb,IAUAoa,EAAAxX,UAAA0Y,OAAA,WACA,yBAAAtB,kBAAAvd,KAAA6c,IAAA7c,KAAAgY,YASA2F,EAAAxX,UAAA+X,MAAA,WACAle,KAAAgW,WASA2H,EAAA0B,cAAA,EACA1B,EAAAM,YAEA,mBAAAnZ,UACA,qBAAA2a,aACAA,YAAA,WAAAzB,OACG,sBAAApN,kBAAA,CACH,GAAA8O,GAAA,cAAApC,GAAA,mBACA1M,kBAAA8O,EAAA1B,GAAA,KrBwyGM,SAAUne,EAAQD,EAASM,GsB7pHjC,QAAAwd,GAAA7c,GACA,GAAAkX,GAAAlX,KAAAkX,WACA4H,KAAA5H,IACA/X,KAAAib,gBAAA,GAEAf,EAAA3Z,KAAAP,KAAAa,GAnCA,GAAAqZ,GAAAha,EAAA,IACAwX,EAAAxX,EAAA,IACA4B,EAAA5B,EAAA,IACAie,EAAAje,EAAA,IACA0f,EAAA1f,EAAA,IACAwB,EAAAxB,EAAA,8BAMAL,GAAAD,QAAA8d,CAMA,IAAAiC,GAAA,WACA,GAAA1C,GAAA/c,EAAA,IACAyc,EAAA,GAAAM,IAAgCF,SAAA,GAChC,cAAAJ,EAAAwC,eAsBAhB,GAAAT,EAAAxD,GAMAwD,EAAAvX,UAAAqE,KAAA,UASAkT,EAAAvX,UAAA0Z,OAAA,WACA7f,KAAA8f,QAUApC,EAAAvX,UAAAkV,MAAA,SAAA0E,GAKA,QAAA1E,KACA3Z,EAAA,UACA2J,EAAA0I,WAAA,SACAgM,IAPA,GAAA1U,GAAArL,IAUA,IARAA,KAAA+T,WAAA,UAQA/T,KAAA0c,UAAA1c,KAAAoc,SAAA,CACA,GAAA4D,GAAA,CAEAhgB,MAAA0c,UACAhb,EAAA,+CACAse,IACAhgB,KAAAgK,KAAA,0BACAtI,EAAA,gCACAse,GAAA3E,OAIArb,KAAAoc,WACA1a,EAAA,+CACAse,IACAhgB,KAAAgK,KAAA,mBACAtI,EAAA,gCACAse,GAAA3E,WAIAA,MAUAqC,EAAAvX,UAAA2Z,KAAA,WACApe,EAAA,WACA1B,KAAA0c,SAAA,EACA1c,KAAAwe,SACAxe,KAAAoK,KAAA,SASAsT,EAAAvX,UAAAsY,OAAA,SAAAlb,GACA,GAAA8H,GAAArL,IACA0B,GAAA,sBAAA6B,EACA,IAAA4K,GAAA,SAAAO,EAAA5I,EAAAka,GAOA,MALA,YAAA3U,EAAA0I,YACA1I,EAAAsQ,SAIA,UAAAjN,EAAAjK,MACA4G,EAAAwP,WACA,OAIAxP,GAAAsP,SAAAjM,GAIA5M,GAAAme,cAAA1c,EAAAvD,KAAA6B,OAAA6W,WAAAvK,GAGA,WAAAnO,KAAA+T,aAEA/T,KAAA0c,SAAA,EACA1c,KAAAoK,KAAA,gBAEA,SAAApK,KAAA+T,WACA/T,KAAA8f,OAEApe,EAAA,uCAAA1B,KAAA+T,cAWA2J,EAAAvX,UAAA+Z,QAAA,WAGA,QAAAhK,KACAxU,EAAA,wBACA2J,EAAAqL,QAAiBjS,KAAA,WAJjB,GAAA4G,GAAArL,IAOA,UAAAA,KAAA+T,YACArS,EAAA,4BACAwU,MAIAxU,EAAA,wCACA1B,KAAAgK,KAAA,OAAAkM,KAYAwH,EAAAvX,UAAAuQ,MAAA,SAAAyJ,GACA,GAAA9U,GAAArL,IACAA,MAAAoc,UAAA,CACA,IAAAgE,GAAA,WACA/U,EAAA+Q,UAAA,EACA/Q,EAAAjB,KAAA,SAGAtI,GAAAue,cAAAF,EAAAngB,KAAAib,eAAA,SAAA1X,GACA8H,EAAAgT,QAAA9a,EAAA6c,MAUA1C,EAAAvX,UAAAvF,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACA0e,EAAAtgB,KAAAwX,OAAA,eACA9U,EAAA,IAGA,IAAA1C,KAAAmY,oBACAvW,EAAA5B,KAAAkY,gBAAA0H,KAGA5f,KAAAib,gBAAArZ,EAAA0Y,MACA1Y,EAAA2e,IAAA,GAGA3e,EAAA8V,EAAAxH,OAAAtO,GAGA5B,KAAA0C,OAAA,UAAA4d,GAAA,MAAAtR,OAAAhP,KAAA0C,OACA,SAAA4d,GAAA,KAAAtR,OAAAhP,KAAA0C,SACAA,EAAA,IAAA1C,KAAA0C,MAIAd,EAAAwB,SACAxB,EAAA,IAAAA,EAGA,IAAAe,GAAA3C,KAAAuX,SAAA3U,QAAA,SACA,OAAA0d,GAAA,OAAA3d,EAAA,IAAA3C,KAAAuX,SAAA,IAAAvX,KAAAuX,UAAA7U,EAAA1C,KAAAmB,KAAAS,ItBusHM,SAAU/B,EAAQD,EAASM,GuBt6HjC,QAAAga,GAAArZ,GACAb,KAAAmB,KAAAN,EAAAM,KACAnB,KAAAuX,SAAA1W,EAAA0W,SACAvX,KAAA0C,KAAA7B,EAAA6B,KACA1C,KAAAwX,OAAA3W,EAAA2W,OACAxX,KAAA4B,MAAAf,EAAAe,MACA5B,KAAAkY,eAAArX,EAAAqX,eACAlY,KAAAmY,kBAAAtX,EAAAsX,kBACAnY,KAAA+T,WAAA,GACA/T,KAAAyX,MAAA5W,EAAA4W,QAAA,EACAzX,KAAA6B,OAAAhB,EAAAgB,OACA7B,KAAAgY,WAAAnX,EAAAmX,WACAhY,KAAAiY,gBAAApX,EAAAoX,gBAGAjY,KAAA8Y,IAAAjY,EAAAiY,IACA9Y,KAAA2Q,IAAA9P,EAAA8P,IACA3Q,KAAA+Y,WAAAlY,EAAAkY,WACA/Y,KAAAgZ,KAAAnY,EAAAmY,KACAhZ,KAAAiZ,GAAApY,EAAAoY,GACAjZ,KAAAkZ,QAAArY,EAAAqY,QACAlZ,KAAAmZ,mBAAAtY,EAAAsY,mBACAnZ,KAAAoZ,UAAAvY,EAAAuY,UAGApZ,KAAAqZ,cAAAxY,EAAAwY,cAGArZ,KAAAuZ,aAAA1Y,EAAA0Y,aACAvZ,KAAAyZ,aAAA5Y,EAAA4Y,aA7CA,GAAA3X,GAAA5B,EAAA,IACA0P,EAAA1P,EAAA,EAMAL,GAAAD,QAAAsa,EA6CAtK,EAAAsK,EAAA/T,WAUA+T,EAAA/T,UAAAyU,QAAA,SAAAjL,EAAA6M,GACA,GAAA7U,GAAA,GAAAI,OAAA4H,EAIA,OAHAhI,GAAAlD,KAAA,iBACAkD,EAAA6Y,YAAAhE,EACAxc,KAAAoK,KAAA,QAAAzC,GACA3H,MASAka,EAAA/T,UAAAoO,KAAA,WAMA,MALA,WAAAvU,KAAA+T,YAAA,KAAA/T,KAAA+T,aACA/T,KAAA+T,WAAA,UACA/T,KAAA6f,UAGA7f,MASAka,EAAA/T,UAAA+P,MAAA,WAMA,MALA,YAAAlW,KAAA+T,YAAA,SAAA/T,KAAA+T,aACA/T,KAAAkgB,UACAlgB,KAAA6a,WAGA7a,MAUAka,EAAA/T,UAAAgV,KAAA,SAAAgF,GACA,YAAAngB,KAAA+T,WAGA,SAAAhM,OAAA,qBAFA/H,MAAA0W,MAAAyJ,IAYAjG,EAAA/T,UAAAwV,OAAA,WACA3b,KAAA+T,WAAA,OACA/T,KAAAoc,UAAA,EACApc,KAAAoK,KAAA,SAUA8P,EAAA/T,UAAAsY,OAAA,SAAAlb,GACA,GAAAmL,GAAA5M,EAAA2e,aAAAld,EAAAvD,KAAA6B,OAAA6W,WACA1Y,MAAA2a,SAAAjM,IAOAwL,EAAA/T,UAAAwU,SAAA,SAAAjM,GACA1O,KAAAoK,KAAA,SAAAsE,IASAwL,EAAA/T,UAAA0U,QAAA,WACA7a,KAAA+T,WAAA,SACA/T,KAAAoK,KAAA,WvBk8HM,SAAUvK,EAAQD,EAASM,GwBn+HjC,QAAAwgB,GAAAhS,EAAAP,GAEA,GAAAvG,GAAA,IAAAhI,EAAAugB,QAAAzR,EAAAjK,MAAAiK,EAAAnL,SACA,OAAA4K,GAAAvG,GAOA,QAAA+Y,GAAAjS,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAArb,GAAAghB,mBAAAlS,EAAAP,EAGA,IAAA5K,GAAAmL,EAAAnL,KACAsd,EAAA,GAAAC,YAAAvd,GACAwd,EAAA,GAAAD,YAAA,EAAAvd,EAAAyd,WAEAD,GAAA,GAAAZ,EAAAzR,EAAAjK,KACA,QAAAN,GAAA,EAAiBA,EAAA0c,EAAAzd,OAAyBe,IAC1C4c,EAAA5c,EAAA,GAAA0c,EAAA1c,EAGA,OAAAgK,GAAA4S,EAAA7N,QAGA,QAAA+N,GAAAvS,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAArb,GAAAghB,mBAAAlS,EAAAP,EAGA,IAAA+S,GAAA,GAAA3O,WAIA,OAHA2O,GAAA1O,OAAA,WACA5S,EAAAuhB,cAA0B1c,KAAAiK,EAAAjK,KAAAlB,KAAA2d,EAAAzO,QAAqCwI,GAAA,EAAA9M,IAE/D+S,EAAAxO,kBAAAhE,EAAAnL,MAGA,QAAA6d,GAAA1S,EAAAuM,EAAA9M,GACA,IAAA8M,EACA,MAAArb,GAAAghB,mBAAAlS,EAAAP,EAGA,IAAAkT,EACA,MAAAJ,GAAAvS,EAAAuM,EAAA9M,EAGA,IAAA/K,GAAA,GAAA0d,YAAA,EACA1d,GAAA,GAAA+c,EAAAzR,EAAAjK,KACA,IAAA6c,GAAA,GAAAxP,IAAA1O,EAAA8P,OAAAxE,EAAAnL,MAEA,OAAA4K,GAAAmT,GAkFA,QAAAC,GAAAhe,GACA,IACAA,EAAAie,EAAA7J,OAAApU,GAA8Bke,QAAA,IAC3B,MAAAzd,GACH,SAEA,MAAAT,GAgFA,QAAAme,GAAAC,EAAAC,EAAAC,GAWA,OAVApP,GAAA,GAAAlJ,OAAAoY,EAAAve,QACAgM,EAAA0S,EAAAH,EAAAve,OAAAye,GAEAE,EAAA,SAAA5d,EAAA6d,EAAA9Q,GACA0Q,EAAAI,EAAA,SAAA9S,EAAAS,GACA8C,EAAAtO,GAAAwL,EACAuB,EAAAhC,EAAAuD,MAIAtO,EAAA,EAAiBA,EAAAwd,EAAAve,OAAgBe,IACjC4d,EAAA5d,EAAAwd,EAAAxd,GAAAiL,GAlWA,GAMA6S,GANAzI,EAAAtZ,EAAA,IACAgiB,EAAAhiB,EAAA,IACAiiB,EAAAjiB,EAAA,IACA4hB,EAAA5hB,EAAA,IACAshB,EAAAthB,EAAA,GAGA,oBAAA8S,eACAiP,EAAA/hB,EAAA,IAUA,IAAAkiB,GAAA,mBAAA1d,YAAA,WAAAlC,KAAAkC,UAAAC,WAQA0d,EAAA,mBAAA3d,YAAA,aAAAlC,KAAAkC,UAAAC,WAMA0c,EAAAe,GAAAC,CAMAziB,GAAAoC,SAAA,CAMA,IAAAme,GAAAvgB,EAAAugB,SACA5L,KAAA,EACA2B,MAAA,EACAgG,KAAA,EACAoG,KAAA,EACA1a,QAAA,EACAgQ,QAAA,EACAvO,KAAA,GAGAkZ,EAAA/I,EAAA2G,GAMAxY,GAAWlD,KAAA,QAAAlB,KAAA,gBAMXuO,EAAA5R,EAAA,GAkBAN,GAAAuhB,aAAA,SAAAzS,EAAAuM,EAAAuH,EAAArU,GACA,kBAAA8M,KACA9M,EAAA8M,EACAA,GAAA,GAGA,kBAAAuH,KACArU,EAAAqU,EACAA,EAAA,KAGA,IAAAjf,GAAAzC,SAAA4N,EAAAnL,KACAzC,OACA4N,EAAAnL,KAAA2P,QAAAxE,EAAAnL,IAEA,uBAAAyP,cAAAzP,YAAAyP,aACA,MAAA2N,GAAAjS,EAAAuM,EAAA9M,EACG,uBAAA2D,IAAAvO,YAAAuO,GACH,MAAAsP,GAAA1S,EAAAuM,EAAA9M,EAIA,IAAA5K,KAAA8M,OACA,MAAAqQ,GAAAhS,EAAAP,EAIA,IAAAsU,GAAAtC,EAAAzR,EAAAjK,KAOA,OAJA3D,UAAA4N,EAAAnL,OACAkf,GAAAD,EAAAhB,EAAAtR,OAAAxD,OAAAgC,EAAAnL,OAA8Dke,QAAA,IAAgB/U,OAAAgC,EAAAnL,OAG9E4K,EAAA,GAAAsU,IAkEA7iB,EAAAghB,mBAAA,SAAAlS,EAAAP,GACA,GAAAvG,GAAA,IAAAhI,EAAAugB,QAAAzR,EAAAjK,KACA,uBAAAqN,IAAApD,EAAAnL,eAAAuO,GAAA,CACA,GAAAoP,GAAA,GAAA3O,WAKA,OAJA2O,GAAA1O,OAAA,WACA,GAAA+N,GAAAW,EAAAzO,OAAAvP,MAAA,OACAiL,GAAAvG,EAAA2Y,IAEAW,EAAAwB,cAAAhU,EAAAnL,MAGA,GAAAof,EACA,KACAA,EAAAjW,OAAAkW,aAAAxc,MAAA,QAAA0a,YAAApS,EAAAnL,OACG,MAAAS,GAIH,OAFA6e,GAAA,GAAA/B,YAAApS,EAAAnL,MACAuf,EAAA,GAAAvZ,OAAAsZ,EAAAzf,QACAe,EAAA,EAAmBA,EAAA0e,EAAAzf,OAAkBe,IACrC2e,EAAA3e,GAAA0e,EAAA1e,EAEAwe,GAAAjW,OAAAkW,aAAAxc,MAAA,KAAA0c,GAGA,MADAlb,IAAAmb,KAAAJ,GACAxU,EAAAvG,IAUAhI,EAAA6gB,aAAA,SAAAld,EAAAmV,EAAAsK,GACA,GAAAliB,SAAAyC,EACA,MAAAoE,EAGA,oBAAApE,GAAA,CACA,SAAAA,EAAAhB,OAAA,GACA,MAAA3C,GAAAqjB,mBAAA1f,EAAAJ,OAAA,GAAAuV,EAGA,IAAAsK,IACAzf,EAAAge,EAAAhe,GACAA,KAAA,GACA,MAAAoE,EAGA,IAAAlD,GAAAlB,EAAAhB,OAAA,EAEA,OAAAyM,QAAAvK,OAAA8d,EAAA9d,GAIAlB,EAAAH,OAAA,GACcqB,KAAA8d,EAAA9d,GAAAlB,OAAAU,UAAA,KAEAQ,KAAA8d,EAAA9d,IANdkD,EAUA,GAAAub,GAAA,GAAApC,YAAAvd,GACAkB,EAAAye,EAAA,GACAC,EAAAhB,EAAA5e,EAAA,EAIA,OAHAuO,IAAA,SAAA4G,IACAyK,EAAA,GAAArR,IAAAqR,MAEU1e,KAAA8d,EAAA9d,GAAAlB,KAAA4f,IAmBVvjB,EAAAqjB,mBAAA,SAAAtT,EAAA+I,GACA,GAAAjU,GAAA8d,EAAA5S,EAAApN,OAAA,GACA,KAAA0f,EACA,OAAYxd,OAAAlB,MAAoB8M,QAAA,EAAA9M,KAAAoM,EAAAxM,OAAA,IAGhC,IAAAI,GAAA0e,EAAAtK,OAAAhI,EAAAxM,OAAA,GAMA,OAJA,SAAAuV,GAAA5G,IACAvO,EAAA,GAAAuO,IAAAvO,MAGUkB,OAAAlB,SAmBV3D,EAAAygB,cAAA,SAAAF,EAAAlF,EAAA9M,GAoBA,QAAAiV,GAAAxb,GACA,MAAAA,GAAAxE,OAAA,IAAAwE,EAGA,QAAAyb,GAAA3U,EAAA4U,GACA1jB,EAAAuhB,aAAAzS,IAAAoP,GAAA7C,GAAA,WAAArT,GACA0b,EAAA,KAAAF,EAAAxb,MAzBA,kBAAAqT,KACA9M,EAAA8M,EACAA,EAAA,KAGA,IAAA6C,GAAAoE,EAAA/B,EAEA,OAAAlF,IAAA6C,EACAhM,IAAAuP,EACAzhB,EAAA2jB,oBAAApD,EAAAhS,GAGAvO,EAAA4jB,2BAAArD,EAAAhS,GAGAgS,EAAA/c,WAcAse,GAAAvB,EAAAkD,EAAA,SAAA1b,EAAA8b,GACA,MAAAtV,GAAAsV,EAAAjG,KAAA,OAdArP,EAAA,OA8CAvO,EAAAqgB,cAAA,SAAA1c,EAAAmV,EAAAvK,GACA,mBAAA5K,GACA,MAAA3D,GAAA8jB,sBAAAngB,EAAAmV,EAAAvK,EAGA,mBAAAuK,KACAvK,EAAAuK,EACAA,EAAA,KAGA,IAAAhK,EACA,SAAAnL,EAEA,MAAA4K,GAAAxG,EAAA,IAKA,QAFAgF,GAAAgD,EAAAvM,EAAA,GAEAe,EAAA,EAAAyX,EAAArY,EAAAH,OAAkCe,EAAAyX,EAAOzX,IAAA,CACzC,GAAAwf,GAAApgB,EAAAhB,OAAA4B,EAEA,UAAAwf,EAAA,CAKA,QAAAvgB,OAAAuJ,EAAAqC,OAAA5L,IAEA,MAAA+K,GAAAxG,EAAA,IAKA,IAFAgI,EAAApM,EAAAJ,OAAAgB,EAAA,EAAAwI,GAEAvJ,GAAAuM,EAAAvM,OAEA,MAAA+K,GAAAxG,EAAA,IAGA,IAAAgI,EAAAvM,OAAA,CAGA,GAFAsL,EAAA9O,EAAA6gB,aAAA9Q,EAAA+I,GAAA,GAEA/Q,EAAAlD,OAAAiK,EAAAjK,MAAAkD,EAAApE,OAAAmL,EAAAnL,KAEA,MAAA4K,GAAAxG,EAAA,IAGA,IAAAic,GAAAzV,EAAAO,EAAAvK,EAAAwI,EAAAiP,EACA,SAAAgI,EAAA,OAIAzf,GAAAwI,EACAvJ,EAAA,OA9BAA,IAAAugB,EAiCA,WAAAvgB,EAEA+K,EAAAxG,EAAA,KAFA,QAqBA/H,EAAA4jB,2BAAA,SAAArD,EAAAhS,GAKA,QAAAkV,GAAA3U,EAAA4U,GACA1jB,EAAAuhB,aAAAzS,GAAA,cAAAnL,GACA,MAAA+f,GAAA,KAAA/f,KANA,MAAA4c,GAAA/c,WAUAse,GAAAvB,EAAAkD,EAAA,SAAA1b,EAAA8O,GACA,GAAAoN,GAAApN,EAAAqN,OAAA,SAAAC,EAAArjB,GACA,GAAAuI,EAMA,OAJAA,GADA,gBAAAvI,GACAA,EAAA0C,OAEA1C,EAAAsgB,WAEA+C,EAAA9a,EAAA0I,WAAAvO,OAAA6F,EAAA,GACK,GAEL+a,EAAA,GAAAlD,YAAA+C,GAEAI,EAAA,CA8BA,OA7BAxN,GAAAyN,QAAA,SAAAxjB,GACA,GAAAyjB,GAAA,gBAAAzjB,GACA0jB,EAAA1jB,CACA,IAAAyjB,EAAA,CAEA,OADAE,GAAA,GAAAvD,YAAApgB,EAAA0C,QACAe,EAAA,EAAuBA,EAAAzD,EAAA0C,OAAce,IACrCkgB,EAAAlgB,GAAAzD,EAAAsK,WAAA7G,EAEAigB,GAAAC,EAAAnR,OAGAiR,EACAH,EAAAC,KAAA,EAEAD,EAAAC,KAAA,CAIA,QADAK,GAAAF,EAAApD,WAAArP,WACAxN,EAAA,EAAqBA,EAAAmgB,EAAAlhB,OAAmBe,IACxC6f,EAAAC,KAAA3e,SAAAgf,EAAAngB,GAEA6f,GAAAC,KAAA,GAGA,QADAI,GAAA,GAAAvD,YAAAsD,GACAjgB,EAAA,EAAqBA,EAAAkgB,EAAAjhB,OAAiBe,IACtC6f,EAAAC,KAAAI,EAAAlgB,KAIAgK,EAAA6V,EAAA9Q,UApDA/E,EAAA,GAAA6E,aAAA,KA4DApT,EAAA2jB,oBAAA,SAAApD,EAAAhS,GACA,QAAAkV,GAAA3U,EAAA4U,GACA1jB,EAAAuhB,aAAAzS,GAAA,cAAA+T,GACA,GAAA8B,GAAA,GAAAzD,YAAA,EAEA,IADAyD,EAAA,KACA,gBAAA9B,GAAA,CAEA,OADA4B,GAAA,GAAAvD,YAAA2B,EAAArf,QACAe,EAAA,EAAuBA,EAAAse,EAAArf,OAAoBe,IAC3CkgB,EAAAlgB,GAAAse,EAAAzX,WAAA7G,EAEAse,GAAA4B,EAAAnR,OACAqR,EAAA,KASA,OANAtb,GAAAwZ,YAAAzP,aACAyP,EAAAzB,WACAyB,EAAA+B,KAEAF,EAAArb,EAAA0I,WACA8S,EAAA,GAAA3D,YAAAwD,EAAAlhB,OAAA,GACAe,EAAA,EAAqBA,EAAAmgB,EAAAlhB,OAAmBe,IACxCsgB,EAAAtgB,GAAAmB,SAAAgf,EAAAngB,GAIA,IAFAsgB,EAAAH,EAAAlhB,QAAA,IAEA0O,EAAA,CACA,GAAAwP,GAAA,GAAAxP,IAAAyS,EAAArR,OAAAuR,EAAAvR,OAAAuP,GACAa,GAAA,KAAAhC,MAKAI,EAAAvB,EAAAkD,EAAA,SAAA1b,EAAA8b,GACA,MAAAtV,GAAA,GAAA2D,GAAA2R,OAaA7jB,EAAA8jB,sBAAA,SAAAngB,EAAAmV,EAAAvK,GACA,kBAAAuK,KACAvK,EAAAuK,EACAA,EAAA,KAMA,KAHA,GAAAgM,GAAAnhB,EACAoL,KAEA+V,EAAA1D,WAAA,IAKA,OAJA2D,GAAA,GAAA7D,YAAA4D,GACAP,EAAA,IAAAQ,EAAA,GACAC,EAAA,GAEAzgB,EAAA,EACA,MAAAwgB,EAAAxgB,GADqBA,IAAA,CAIrB,GAAAygB,EAAAxhB,OAAA,IACA,MAAA+K,GAAAxG,EAAA,IAGAid,IAAAD,EAAAxgB,GAGAugB,EAAAvC,EAAAuC,EAAA,EAAAE,EAAAxhB,QACAwhB,EAAAtf,SAAAsf,EAEA,IAAAjV,GAAAwS,EAAAuC,EAAA,EAAAE,EACA,IAAAT,EACA,IACAxU,EAAAjD,OAAAkW,aAAAxc,MAAA,QAAA0a,YAAAnR,IACO,MAAA3L,GAEP,GAAA6e,GAAA,GAAA/B,YAAAnR,EACAA,GAAA,EACA,QAAAxL,GAAA,EAAuBA,EAAA0e,EAAAzf,OAAkBe,IACzCwL,GAAAjD,OAAAkW,aAAAC,EAAA1e,IAKAwK,EAAAnF,KAAAmG,GACA+U,EAAAvC,EAAAuC,EAAAE,GAGA,GAAA5E,GAAArR,EAAAvL,MACAuL,GAAAuV,QAAA,SAAAhR,EAAA/O,GACAgK,EAAAvO,EAAA6gB,aAAAvN,EAAAwF,GAAA,GAAAvU,EAAA6b,OxB0mIM,SAAUngB,EAAQD,GyB5rJxBC,EAAAD,QAAAgS,OAAA4H,MAAA,SAAApX,GACA,GAAAuQ,MACA8B,EAAA7C,OAAAzL,UAAAuO,cAEA,QAAAvQ,KAAA/B,GACAqS,EAAAlU,KAAA6B,EAAA+B,IACAwO,EAAAnJ,KAAArF,EAGA,OAAAwO,KzB4sJM,SAAU9S,EAAQD,EAASM,G0BhsJjC,QAAAgiB,GAAA9f,GACA,IAAAA,GAAA,gBAAAA,GACA,QAGA,IAAAoN,EAAApN,GAAA,CACA,OAAA+B,GAAA,EAAAyX,EAAAxZ,EAAAgB,OAAmCe,EAAAyX,EAAOzX,IAC1C,GAAA+d,EAAA9f,EAAA+B,IACA,QAGA,UAGA,qBAAA0O,gBAAAC,UAAAD,OAAAC,SAAA1Q,IACA,kBAAA4Q,cAAA5Q,YAAA4Q,cACAnB,GAAAzP,YAAA0P,OACAC,GAAA3P,YAAA4P,MAEA,QAIA,IAAA5P,EAAAyiB,QAAA,kBAAAziB,GAAAyiB,QAAA,IAAAxe,UAAAjD,OACA,MAAA8e,GAAA9f,EAAAyiB,UAAA,EAGA,QAAAlU,KAAAvO,GACA,GAAAwP,OAAAzL,UAAAuO,eAAAnU,KAAA6B,EAAAuO,IAAAuR,EAAA9f,EAAAuO,IACA,QAIA,UAxDA,GAAAnB,GAAAtP,EAAA,IAEAyR,EAAAC,OAAAzL,UAAAwL,SACAE,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAAH,EAAApR,KAAAuR,MACAC,EAAA,kBAAAC,OACA,mBAAAA,OAAA,6BAAAL,EAAApR,KAAAyR,KAMAnS,GAAAD,QAAAsiB,G1BixJM,SAAUriB,EAAQD,G2B5xJxBC,EAAAD,QAAA,SAAAklB,EAAAC,EAAAC,GACA,GAAAC,GAAAH,EAAA9D,UAIA,IAHA+D,KAAA,EACAC,KAAAC,EAEAH,EAAA3T,MAA0B,MAAA2T,GAAA3T,MAAA4T,EAAAC,EAM1B,IAJAD,EAAA,IAAkBA,GAAAE,GAClBD,EAAA,IAAgBA,GAAAC,GAChBD,EAAAC,IAAoBD,EAAAC,GAEpBF,GAAAE,GAAAF,GAAAC,GAAA,IAAAC,EACA,UAAAjS,aAAA,EAKA,QAFAkS,GAAA,GAAApE,YAAAgE,GACArS,EAAA,GAAAqO,YAAAkE,EAAAD,GACA5gB,EAAA4gB,EAAAI,EAAA,EAA6BhhB,EAAA6gB,EAAS7gB,IAAAghB,IACtC1S,EAAA0S,GAAAD,EAAA/gB,EAEA,OAAAsO,GAAAS,S3B2yJM,SAAUrT,EAAQD,G4Bp0JxB,QAAAkiB,GAAAsD,EAAAjX,EAAAkX,GAOA,QAAAC,GAAA3d,EAAA8K,GACA,GAAA6S,EAAAF,OAAA,EACA,SAAArd,OAAA,iCAEAud,EAAAF,MAGAzd,GACA4d,GAAA,EACApX,EAAAxG,GAEAwG,EAAAkX,GACS,IAAAC,EAAAF,OAAAG,GACTpX,EAAA,KAAAsE,GAnBA,GAAA8S,IAAA,CAIA,OAHAF,MAAAhc,EACAic,EAAAF,QAEA,IAAAA,EAAAjX,IAAAmX,EAoBA,QAAAjc,MA3BAxJ,EAAAD,QAAAkiB,G5Bw2JM,SAAUjiB,EAAQD,G6Bn2JxB,QAAA4lB,GAAAC,GAMA,IALA,GAGAC,GACAC,EAJAC,KACAC,EAAA,EACAziB,EAAAqiB,EAAAriB,OAGAyiB,EAAAziB,GACAsiB,EAAAD,EAAAza,WAAA6a,KACAH,GAAA,OAAAA,GAAA,OAAAG,EAAAziB,GAEAuiB,EAAAF,EAAAza,WAAA6a,KACA,cAAAF,GACAC,EAAApc,OAAA,KAAAkc,IAAA,UAAAC,GAAA,QAIAC,EAAApc,KAAAkc,GACAG,MAGAD,EAAApc,KAAAkc,EAGA,OAAAE,GAIA,QAAAE,GAAA1c,GAKA,IAJA,GAEAsc,GAFAtiB,EAAAgG,EAAAhG,OACA0C,GAAA,EAEA8f,EAAA,KACA9f,EAAA1C,GACAsiB,EAAAtc,EAAAtD,GACA4f,EAAA,QACAA,GAAA,MACAE,GAAAG,EAAAL,IAAA,eACAA,EAAA,WAAAA,GAEAE,GAAAG,EAAAL,EAEA,OAAAE,GAGA,QAAAI,GAAAC,EAAAxE,GACA,GAAAwE,GAAA,OAAAA,GAAA,OACA,GAAAxE,EACA,KAAA1Z,OACA,oBAAAke,EAAAtU,SAAA,IAAAuU,cACA,yBAGA,UAEA,SAIA,QAAAC,GAAAF,EAAArP,GACA,MAAAmP,GAAAE,GAAArP,EAAA,QAGA,QAAAwP,GAAAH,EAAAxE,GACA,kBAAAwE,GACA,MAAAF,GAAAE,EAEA,IAAAI,GAAA,EAiBA,OAhBA,gBAAAJ,GACAI,EAAAN,EAAAE,GAAA,UAEA,eAAAA,IACAD,EAAAC,EAAAxE,KACAwE,EAAA,OAEAI,EAAAN,EAAAE,GAAA,WACAI,GAAAF,EAAAF,EAAA,IAEA,eAAAA,KACAI,EAAAN,EAAAE,GAAA,UACAI,GAAAF,EAAAF,EAAA,IACAI,GAAAF,EAAAF,EAAA,IAEAI,GAAAN,EAAA,GAAAE,EAAA,KAIA,QAAAzD,GAAAiD,EAAA5kB,GACAA,OAQA,KAPA,GAKAolB,GALAxE,GAAA,IAAA5gB,EAAA4gB,OAEA6E,EAAAd,EAAAC,GACAriB,EAAAkjB,EAAAljB,OACA0C,GAAA,EAEAygB,EAAA,KACAzgB,EAAA1C,GACA6iB,EAAAK,EAAAxgB,GACAygB,GAAAH,EAAAH,EAAAxE,EAEA,OAAA8E,GAKA,QAAAC,KACA,GAAAC,GAAAC,EACA,KAAA3e,OAAA,qBAGA,IAAA4e,GAAA,IAAAC,EAAAH,EAGA,IAFAA,IAEA,UAAAE,GACA,UAAAA,CAIA,MAAA5e,OAAA,6BAGA,QAAA8e,GAAApF,GACA,GAAAqF,GACAC,EACAC,EACAC,EACAhB,CAEA,IAAAQ,EAAAC,EACA,KAAA3e,OAAA,qBAGA,IAAA0e,GAAAC,EACA,QAQA,IAJAI,EAAA,IAAAF,EAAAH,GACAA,IAGA,QAAAK,GACA,MAAAA,EAIA,cAAAA,GAAA,CAGA,GAFAC,EAAAP,IACAP,GAAA,GAAAa,IAAA,EAAAC,EACAd,GAAA,IACA,MAAAA,EAEA,MAAAle,OAAA,6BAKA,aAAA+e,GAAA,CAIA,GAHAC,EAAAP,IACAQ,EAAAR,IACAP,GAAA,GAAAa,IAAA,GAAAC,GAAA,EAAAC,EACAf,GAAA,KACA,MAAAD,GAAAC,EAAAxE,GAAAwE,EAAA,KAEA,MAAAle,OAAA,6BAKA,aAAA+e,KACAC,EAAAP,IACAQ,EAAAR,IACAS,EAAAT,IACAP,GAAA,EAAAa,IAAA,GAAAC,GAAA,GACAC,GAAA,EAAAC,EACAhB,GAAA,OAAAA,GAAA,SACA,MAAAA,EAIA,MAAAle,OAAA,0BAMA,QAAAib,GAAAuD,EAAA1lB,GACAA,OACA,IAAA4gB,IAAA,IAAA5gB,EAAA4gB,MAEAmF,GAAApB,EAAAe,GACAG,EAAAE,EAAAxjB,OACAqjB,EAAA,CAGA,KAFA,GACAS,GADAZ,MAEAY,EAAAL,EAAApF,OAAA,GACA6E,EAAA9c,KAAA0d,EAEA,OAAApB,GAAAQ;AAxMA,GAyLAM,GACAF,EACAD,EA3LAV,EAAArZ,OAAAkW,YA2MA/iB,GAAAD,SACAgK,QAAA,QACAsG,OAAAsS,EACA7K,OAAAqL,I7Bg3JM,SAAUnjB,EAAQD,I8BzjKxB,SAAAunB,GACA,YAEAvnB,GAAAsQ,OAAA,SAAA4U,GACA,GACA3gB,GADA8gB,EAAA,GAAAnE,YAAAgE,GACA7b,EAAAgc,EAAA7hB,OAAAiN,EAAA,EAEA,KAAAlM,EAAA,EAAeA,EAAA8E,EAAS9E,GAAA,EACxBkM,GAAA8W,EAAAlC,EAAA9gB,IAAA,GACAkM,GAAA8W,GAAA,EAAAlC,EAAA9gB,KAAA,EAAA8gB,EAAA9gB,EAAA,OACAkM,GAAA8W,GAAA,GAAAlC,EAAA9gB,EAAA,OAAA8gB,EAAA9gB,EAAA,OACAkM,GAAA8W,EAAA,GAAAlC,EAAA9gB,EAAA,GASA,OANA8E,GAAA,MACAoH,IAAApM,UAAA,EAAAoM,EAAAjN,OAAA,OACK6F,EAAA,QACLoH,IAAApM,UAAA,EAAAoM,EAAAjN,OAAA,SAGAiN,GAGAzQ,EAAA+X,OAAA,SAAAtH,GACA,GACAlM,GACAijB,EAAAC,EAAAC,EAAAC,EAFAC,EAAA,IAAAnX,EAAAjN,OACA6F,EAAAoH,EAAAjN,OAAA1C,EAAA,CAGA,OAAA2P,IAAAjN,OAAA,KACAokB,IACA,MAAAnX,IAAAjN,OAAA,IACAokB,IAIA,IAAA1C,GAAA,GAAA9R,aAAAwU,GACAvC,EAAA,GAAAnE,YAAAgE,EAEA,KAAA3gB,EAAA,EAAeA,EAAA8E,EAAS9E,GAAA,EACxBijB,EAAAD,EAAAvkB,QAAAyN,EAAAlM,IACAkjB,EAAAF,EAAAvkB,QAAAyN,EAAAlM,EAAA,IACAmjB,EAAAH,EAAAvkB,QAAAyN,EAAAlM,EAAA,IACAojB,EAAAJ,EAAAvkB,QAAAyN,EAAAlM,EAAA,IAEA8gB,EAAAvkB,KAAA0mB,GAAA,EAAAC,GAAA,EACApC,EAAAvkB,MAAA,GAAA2mB,IAAA,EAAAC,GAAA,EACArC,EAAAvkB,MAAA,EAAA4mB,IAAA,KAAAC,CAGA,OAAAzC,KAEC,qE9BukKK,SAAUjlB,EAAQD,G+B9kKxB,QAAA6nB,GAAA9F,GACA,MAAAA,GAAAD,IAAA,SAAAgG,GACA,GAAAA,EAAAxU,iBAAAF,aAAA,CACA,GAAA7D,GAAAuY,EAAAxU,MAIA,IAAAwU,EAAA1G,aAAA7R,EAAA6R,WAAA,CACA,GAAA2G,GAAA,GAAA7G,YAAA4G,EAAA1G,WACA2G,GAAAC,IAAA,GAAA9G,YAAA3R,EAAAuY,EAAAG,WAAAH,EAAA1G,aACA7R,EAAAwY,EAAAzU,OAGA,MAAA/D,GAGA,MAAAuY,KAIA,QAAAI,GAAAnG,EAAApU,GACAA,OAEA,IAAAwa,GAAA,GAAAC,EAKA,OAJAP,GAAA9F,GAAAuC,QAAA,SAAA+D,GACAF,EAAAG,OAAAD,KAGA1a,EAAA,KAAAwa,EAAAI,QAAA5a,EAAA9I,MAAAsjB,EAAAI,UAGA,QAAAC,GAAAzG,EAAApU,GACA,UAAAuE,MAAA2V,EAAA9F,GAAApU,OA/EA,GAAAya,GAAA,mBAAAA,KACA,mBAAAK,qCACA,mBAAAC,6BACA,mBAAAC,gCAOAC,EAAA,WACA,IACA,GAAAC,GAAA,GAAA3W,OAAA,MACA,YAAA2W,EAAAjE,KACG,MAAAxgB,GACH,aASA0kB,EAAAF,GAAA,WACA,IACA,GAAAzkB,GAAA,GAAA+N,OAAA,GAAAgP,aAAA,OACA,YAAA/c,EAAAygB,KACG,MAAAxgB,GACH,aAQA2kB,EAAAX,GACAA,EAAA7hB,UAAA+hB,QACAF,EAAA7hB,UAAAgiB,OA2CA,oBAAArW,QACAgW,EAAA3hB,UAAA2L,KAAA3L,UACAiiB,EAAAjiB,UAAA2L,KAAA3L,WAGAtG,EAAAD,QAAA,WACA,MAAA4oB,GACAE,EAAA5W,KAAAsW,EACGO,EACHb,EAEA,W/B0oKM,SAAUjoB,EAAQD,GgCnuKxBA,EAAAsQ,OAAA,SAAA9N,GACA,GAAAyB,GAAA,EAEA,QAAAM,KAAA/B,GACAA,EAAAsS,eAAAvQ,KACAN,EAAAT,SAAAS,GAAA,KACAA,GAAA+kB,mBAAAzkB,GAAA,IAAAykB,mBAAAxmB,EAAA+B,IAIA,OAAAN,IAUAjE,EAAA+X,OAAA,SAAAkR,GAGA,OAFAC,MACAC,EAAAF,EAAA3lB,MAAA,KACAiB,EAAA,EAAAyX,EAAAmN,EAAA3lB,OAAmCe,EAAAyX,EAAOzX,IAAA,CAC1C,GAAA6kB,GAAAD,EAAA5kB,GAAAjB,MAAA,IACA4lB,GAAAG,mBAAAD,EAAA,KAAAC,mBAAAD,EAAA,IAEA,MAAAF,KhCmvKM,SAAUjpB,EAAQD,GiCrxKxBC,EAAAD,QAAA,SAAA6oB,EAAA1kB,GACA,GAAA+M,GAAA,YACAA,GAAA3K,UAAApC,EAAAoC,UACAsiB,EAAAtiB,UAAA,GAAA2K,GACA2X,EAAAtiB,UAAAe,YAAAuhB,IjC6xKM,SAAU5oB,EAAQD,GkClyKxB,YAgBA,SAAAsQ,GAAAsB,GACA,GAAAiR,GAAA,EAEA,GACAA,GAAAyG,EAAA1X,EAAApO,GAAAqf,EACAjR,EAAAvG,KAAAoC,MAAAmE,EAAApO,SACGoO,EAAA,EAEH,OAAAiR,GAUA,QAAA9K,GAAA9T,GACA,GAAAslB,GAAA,CAEA,KAAAhlB,EAAA,EAAaA,EAAAN,EAAAT,OAAgBe,IAC7BglB,IAAA/lB,EAAAse,EAAA7d,EAAAtB,OAAA4B,GAGA,OAAAglB,GASA,QAAAvJ,KACA,GAAAwJ,GAAAlZ,GAAA,GAAA3E,MAEA,OAAA6d,KAAA1d,GAAA2d,EAAA,EAAA3d,EAAA0d,GACAA,EAAA,IAAAlZ,EAAAmZ,KAMA,IA1DA,GAKA3d,GALAwd,EAAA,mEAAAhmB,MAAA,IACAE,EAAA,GACAse,KACA2H,EAAA,EACAllB,EAAA,EAsDMA,EAAAf,EAAYe,IAAAud,EAAAwH,EAAA/kB,KAKlByb,GAAA1P,SACA0P,EAAAjI,SACA9X,EAAAD,QAAAggB,GlCyyKM,SAAU/f,EAAQD,EAASM,GmC70KjC,QAAAud,MASA,QAAA6L,GAAAzoB,GACA6c,EAAAnd,KAAAP,KAAAa,GAEAb,KAAA4B,MAAA5B,KAAA4B,UAIAqP,IAEAA,EAAAqM,EAAAiM,OAAAjM,EAAAiM,YAIAvpB,KAAA8F,MAAAmL,EAAA7N,MAGA,IAAAiI,GAAArL,IACAiR,GAAAzH,KAAA,SAAAmG,GACAtE,EAAAoT,OAAA9O,KAIA3P,KAAA4B,MAAA2F,EAAAvH,KAAA8F,MAGA,kBAAA8K,mBACAA,iBAAA,0BACAvF,EAAAme,SAAAne,EAAAme,OAAAjT,QAAAkH,KACK,GAhEL,GAAAC,GAAAxd,EAAA,IACAie,EAAAje,EAAA,IACAod,EAAApd,EAAA,GAMAL,GAAAD,QAAA0pB,CAMA,IAOArY,GAPAwY,EAAA,MACAC,EAAA,MAyDAvL,GAAAmL,EAAA5L,GAMA4L,EAAAnjB,UAAA8U,gBAAA,EAQAqO,EAAAnjB,UAAA+Z,QAAA,WACAlgB,KAAAwpB,SACAxpB,KAAAwpB,OAAAG,WAAAC,YAAA5pB,KAAAwpB,QACAxpB,KAAAwpB,OAAA,MAGAxpB,KAAA6pB,OACA7pB,KAAA6pB,KAAAF,WAAAC,YAAA5pB,KAAA6pB,MACA7pB,KAAA6pB,KAAA,KACA7pB,KAAA8pB,OAAA,MAGApM,EAAAvX,UAAA+Z,QAAA3f,KAAAP,OASAspB,EAAAnjB,UAAAqY,OAAA,WACA,GAAAnT,GAAArL,KACAwpB,EAAA1kB,SAAAilB,cAAA,SAEA/pB,MAAAwpB,SACAxpB,KAAAwpB,OAAAG,WAAAC,YAAA5pB,KAAAwpB,QACAxpB,KAAAwpB,OAAA,MAGAA,EAAA3L,OAAA,EACA2L,EAAA1lB,IAAA9D,KAAAY,MACA4oB,EAAAjT,QAAA,SAAAvS,GACAqH,EAAAuP,QAAA,mBAAA5W,GAGA,IAAAgmB,GAAAllB,SAAAmlB,qBAAA,YACAD,GACAA,EAAAL,WAAAO,aAAAV,EAAAQ,IAEAllB,SAAAqlB,MAAArlB,SAAAslB,MAAAC,YAAAb,GAEAxpB,KAAAwpB,QAEA,IAAAc,GAAA,mBAAA5lB,YAAA,SAAAlC,KAAAkC,UAAAC,UAEA2lB,IACAliB,WAAA,WACA,GAAA0hB,GAAAhlB,SAAAilB,cAAA,SACAjlB,UAAAslB,KAAAC,YAAAP,GACAhlB,SAAAslB,KAAAR,YAAAE,IACK,MAYLR,EAAAnjB,UAAAkY,QAAA,SAAA9a,EAAAuN,GA0BA,QAAAyZ,KACAC,IACA1Z,IAGA,QAAA0Z,KACA,GAAAnf,EAAAye,OACA,IACAze,EAAAwe,KAAAD,YAAAve,EAAAye,QACO,MAAA9lB,GACPqH,EAAAuP,QAAA,qCAAA5W,GAIA,IAEA,GAAAymB,GAAA,oCAAApf,EAAAqf,SAAA,IACAZ,GAAAhlB,SAAAilB,cAAAU,GACK,MAAAzmB,GACL8lB,EAAAhlB,SAAAilB,cAAA,UACAD,EAAAtf,KAAAa,EAAAqf,SACAZ,EAAAhmB,IAAA,eAGAgmB,EAAAzpB,GAAAgL,EAAAqf,SAEArf,EAAAwe,KAAAQ,YAAAP,GACAze,EAAAye,SApDA,GAAAze,GAAArL,IAEA,KAAAA,KAAA6pB,KAAA,CACA,GAGAC,GAHAD,EAAA/kB,SAAAilB,cAAA,QACAY,EAAA7lB,SAAAilB,cAAA,YACA1pB,EAAAL,KAAA0qB,SAAA,cAAA1qB,KAAA8F,KAGA+jB,GAAAe,UAAA,WACAf,EAAA7kB,MAAA6lB,SAAA,WACAhB,EAAA7kB,MAAA8lB,IAAA,UACAjB,EAAA7kB,MAAA+lB,KAAA,UACAlB,EAAAmB,OAAA3qB,EACAwpB,EAAAjM,OAAA,OACAiM,EAAAoB,aAAA,0BACAN,EAAAngB,KAAA,IACAqf,EAAAQ,YAAAM,GACA7lB,SAAAslB,KAAAC,YAAAR,GAEA7pB,KAAA6pB,OACA7pB,KAAA2qB,OAGA3qB,KAAA6pB,KAAAqB,OAAAlrB,KAAAY,MAgCA4pB,IAIAjnB,IAAAN,QAAAymB,EAAA,QACA1pB,KAAA2qB,KAAAjF,MAAAniB,EAAAN,QAAAwmB,EAAA,MAEA,KACAzpB,KAAA6pB,KAAAsB,SACG,MAAAnnB,IAEHhE,KAAA8pB,OAAArK,YACAzf,KAAA8pB,OAAA9K,mBAAA,WACA,aAAA3T,EAAAye,OAAA/V,YACAwW,KAIAvqB,KAAA8pB,OAAAtX,OAAA+X,InCq3KM,SAAU1qB,EAAQD,EAASM,GoC1iLjC,QAAAkrB,GAAAvqB,GACA,GAAAkX,GAAAlX,KAAAkX,WACAA,KACA/X,KAAAib,gBAAA,GAEAjb,KAAA4Y,kBAAA/X,EAAA+X,kBACA5Y,KAAAqrB,sBAAAC,IAAAzqB,EAAAuY,UACApZ,KAAAwa,UAAA3Z,EAAA2Z,UACAxa,KAAAqrB,wBACAE,EAAAC,GAEAtR,EAAA3Z,KAAAP,KAAAa,GArDA,GAOAyqB,GAAAE,EAPAtR,EAAAha,EAAA,IACA4B,EAAA5B,EAAA,IACAwX,EAAAxX,EAAA,IACAie,EAAAje,EAAA,IACA0f,EAAA1f,EAAA,IACAwB,EAAAxB,EAAA,gCAUA,IANA,mBAAAurB,WACAH,EAAAG,UACC,mBAAApgB,QACDigB,EAAAjgB,KAAAogB,WAAApgB,KAAAqgB,cAGA,mBAAAlnB,QACA,IACAgnB,EAAAtrB,EAAA,IACG,MAAA8D,IASH,GAAAunB,GAAAD,GAAAE,CAMA3rB,GAAAD,QAAAwrB,EA2BAjN,EAAAiN,EAAAlR,GAQAkR,EAAAjlB,UAAAqE,KAAA,YAMA4gB,EAAAjlB,UAAA8U,gBAAA,EAQAmQ,EAAAjlB,UAAA0Z,OAAA,WACA,GAAA7f,KAAA2rB,QAAA,CAKA,GAAA/qB,GAAAZ,KAAAY,MACA4Z,EAAAxa,KAAAwa,UAEA3Z,IAEAb,MAAAqZ,gBACAxY,EAAA4W,MAAAzX,KAAAyX,MACA5W,EAAA+X,kBAAA5Y,KAAA4Y,kBAGA/X,EAAAiY,IAAA9Y,KAAA8Y,IACAjY,EAAA8P,IAAA3Q,KAAA2Q,IACA9P,EAAAkY,WAAA/Y,KAAA+Y,WACAlY,EAAAmY,KAAAhZ,KAAAgZ,KACAnY,EAAAoY,GAAAjZ,KAAAiZ,GACApY,EAAAqY,QAAAlZ,KAAAkZ,QACArY,EAAAsY,mBAAAnZ,KAAAmZ,oBAGAnZ,KAAAuZ,eACA1Y,EAAA+qB,QAAA5rB,KAAAuZ,cAEAvZ,KAAAyZ,eACA5Y,EAAA4Y,aAAAzZ,KAAAyZ,aAGA,KACAzZ,KAAA6rB,GACA7rB,KAAAqrB,wBAAArrB,KAAAqZ,cACAmB,EACA,GAAA+Q,GAAA3qB,EAAA4Z,GACA,GAAA+Q,GAAA3qB,GACA,GAAA2qB,GAAA3qB,EAAA4Z,EAAA3Z,GACG,MAAA8G,GACH,MAAA3H,MAAAoK,KAAA,QAAAzC,GAGA7G,SAAAd,KAAA6rB,GAAAnT,aACA1Y,KAAAib,gBAAA,GAGAjb,KAAA6rB,GAAAC,UAAA9rB,KAAA6rB,GAAAC,SAAAvd,QACAvO,KAAAib,gBAAA,EACAjb,KAAA6rB,GAAAnT,WAAA,cAEA1Y,KAAA6rB,GAAAnT,WAAA,cAGA1Y,KAAA+rB,sBASAX,EAAAjlB,UAAA4lB,kBAAA,WACA,GAAA1gB,GAAArL,IAEAA,MAAA6rB,GAAA/V,OAAA,WACAzK,EAAAsQ,UAEA3b,KAAA6rB,GAAA5U,QAAA,WACA5L,EAAAwP,WAEA7a,KAAA6rB,GAAAG,UAAA,SAAAC,GACA5gB,EAAAoT,OAAAwN,EAAA1oB,OAEAvD,KAAA6rB,GAAAtV,QAAA,SAAAvS,GACAqH,EAAAuP,QAAA,kBAAA5W,KAWAonB,EAAAjlB,UAAAuQ,MAAA,SAAAyJ,GA4CA,QAAA0B,KACAxW,EAAAjB,KAAA,SAIAhC,WAAA,WACAiD,EAAA+Q,UAAA,EACA/Q,EAAAjB,KAAA,UACK,GAnDL,GAAAiB,GAAArL,IACAA,MAAAoc,UAAA,CAKA,QADA4D,GAAAG,EAAA/c,OACAe,EAAA,EAAAyX,EAAAoE,EAA4B7b,EAAAyX,EAAOzX,KACnC,SAAAuK,GACA5M,EAAAqf,aAAAzS,EAAArD,EAAA4P,eAAA,SAAA1X,GACA,IAAA8H,EAAAggB,sBAAA,CAEA,GAAAxqB,KAKA,IAJA6N,EAAAnB,UACA1M,EAAAwb,SAAA3N,EAAAnB,QAAA8O,UAGAhR,EAAAuN,kBAAA,CACA,GAAA3P,GAAA,gBAAA1F,GAAAsP,OAAAmO,WAAAzd,KAAAH,MACA6F,GAAAoC,EAAAuN,kBAAAC,YACAhY,EAAAwb,UAAA,IAQA,IACAhR,EAAAggB,sBAEAhgB,EAAAwgB,GAAA1Q,KAAA5X,GAEA8H,EAAAwgB,GAAA1Q,KAAA5X,EAAA1C,GAES,MAAAmD,GACTtC,EAAA,2CAGAse,GAAA6B,OAEK1B,EAAAhc,KAqBLinB,EAAAjlB,UAAA0U,QAAA,WACAX,EAAA/T,UAAA0U,QAAAta,KAAAP,OASAorB,EAAAjlB,UAAA+Z,QAAA,WACA,mBAAAlgB,MAAA6rB,IACA7rB,KAAA6rB,GAAA3V,SAUAkV,EAAAjlB,UAAAvF,IAAA,WACA,GAAAgB,GAAA5B,KAAA4B,UACA0e,EAAAtgB,KAAAwX,OAAA,WACA9U,EAAA,EAGA1C,MAAA0C,OAAA,QAAA4d,GAAA,MAAAtR,OAAAhP,KAAA0C,OACA,OAAA4d,GAAA,KAAAtR,OAAAhP,KAAA0C,SACAA,EAAA,IAAA1C,KAAA0C,MAIA1C,KAAAmY,oBACAvW,EAAA5B,KAAAkY,gBAAA0H,KAIA5f,KAAAib,iBACArZ,EAAA2e,IAAA,GAGA3e,EAAA8V,EAAAxH,OAAAtO,GAGAA,EAAAwB,SACAxB,EAAA,IAAAA,EAGA,IAAAe,GAAA3C,KAAAuX,SAAA3U,QAAA,SACA,OAAA0d,GAAA,OAAA3d,EAAA,IAAA3C,KAAAuX,SAAA,IAAAvX,KAAAuX,UAAA7U,EAAA1C,KAAAmB,KAAAS,GAUAwpB,EAAAjlB,UAAAwlB,MAAA,WACA,SAAAJ,GAAA,gBAAAA,IAAAvrB,KAAAwK,OAAA4gB,EAAAjlB,UAAAqE,QpCgmLM,SAAU3K,EAAQD,KAMlB,SAAUC,EAAQD,GqC94LxB,GAAAgD,aAEA/C,GAAAD,QAAA,SAAA+S,EAAAvQ,GACA,GAAAQ,EAAA,MAAA+P,GAAA/P,QAAAR,EACA,QAAA+B,GAAA,EAAiBA,EAAAwO,EAAAvP,SAAgBe,EACjC,GAAAwO,EAAAxO,KAAA/B,EAAA,MAAA+B,EAEA,YrCs5LM,SAAUtE,EAAQD,EAASM,GsCv2LjC,QAAAgC,GAAAnB,EAAA+M,EAAAjN,GACAb,KAAAe,KACAf,KAAA8N,MACA9N,KAAAksB,KAAAlsB,KACAA,KAAAmsB,IAAA,EACAnsB,KAAAosB,QACApsB,KAAAqsB,iBACArsB,KAAAssB,cACAtsB,KAAAusB,WAAA,EACAvsB,KAAAwsB,cAAA,EACAxsB,KAAAysB,SACA5rB,KAAAe,QACA5B,KAAA4B,MAAAf,EAAAe,OAEA5B,KAAAe,GAAAuT,aAAAtU,KAAAuU,OAhEA,GAAAzS,GAAA5B,EAAA,GACA0P,EAAA1P,EAAA,GACAwsB,EAAAxsB,EAAA,IACA4J,EAAA5J,EAAA,IACA+L,EAAA/L,EAAA,IACAwB,EAAAxB,EAAA,8BACAwX,EAAAxX,EAAA,IACAysB,EAAAzsB,EAAA,GAMAL,GAAAD,UAAAsC,CASA,IAAA0qB,IACA3qB,QAAA,EACA4qB,cAAA,EACAC,gBAAA,EACA9Y,WAAA,EACA+C,WAAA,EACA7H,MAAA,EACAyG,UAAA,EACAoX,kBAAA,EACAC,iBAAA,EACAC,gBAAA,EACAxX,aAAA,EACAyG,KAAA,EACAoG,KAAA,GAOAlY,EAAAwF,EAAAzJ,UAAAiE,IA6BAwF,GAAA1N,EAAAiE,WAQAjE,EAAAiE,UAAA+mB,UAAA,WACA,IAAAltB,KAAAmT,KAAA,CAEA,GAAApS,GAAAf,KAAAe,EACAf,MAAAmT,MACArJ,EAAA/I,EAAA,OAAAkL,EAAAjM,KAAA,WACA8J,EAAA/I,EAAA,SAAAkL,EAAAjM,KAAA,aACA8J,EAAA/I,EAAA,QAAAkL,EAAAjM,KAAA,eAUAkC,EAAAiE,UAAAoO,KACArS,EAAAiE,UAAAlE,QAAA,WACA,MAAAjC,MAAAusB,UAAAvsB,MAEAA,KAAAktB,YACAltB,KAAAe,GAAA0U,cAAAzV,KAAAe,GAAAwT,OACA,SAAAvU,KAAAe,GAAAgT,YAAA/T,KAAA8V,SACA9V,KAAAoK,KAAA,cACApK,OAUAkC,EAAAiE,UAAAgV,KAAA,WACA,GAAA1V,GAAAinB,EAAArmB,UAGA,OAFAZ,GAAAmG,QAAA,WACA5L,KAAAoK,KAAAhE,MAAApG,KAAAyF,GACAzF,MAYAkC,EAAAiE,UAAAiE,KAAA,SAAA6hB,GACA,GAAAW,EAAAlY,eAAAuX,GAEA,MADA7hB,GAAAhE,MAAApG,KAAAqG,WACArG,IAGA,IAAAyF,GAAAinB,EAAArmB,WACAqI,GACAjK,MAAA3D,SAAAd,KAAAysB,MAAAle,OAAAvO,KAAAysB,MAAAle,OAAAoe,EAAAlnB,IAAA3D,EAAA6L,aAAA7L,EAAAkO,MACAzM,KAAAkC,EAqBA,OAlBAiJ,GAAAnB,WACAmB,EAAAnB,QAAA8O,UAAArc,KAAAysB,QAAA,IAAAzsB,KAAAysB,MAAApQ,SAGA,kBAAA5W,KAAArC,OAAA,KACA1B,EAAA,iCAAA1B,KAAAmsB,KACAnsB,KAAAosB,KAAApsB,KAAAmsB,KAAA1mB,EAAA0nB,MACAze,EAAArO,GAAAL,KAAAmsB,OAGAnsB,KAAAusB,UACAvsB,KAAA0O,UAEA1O,KAAAssB,WAAA9iB,KAAAkF,GAGA1O,KAAAysB,SAEAzsB,MAUAkC,EAAAiE,UAAAuI,OAAA,SAAAA,GACAA,EAAAZ,IAAA9N,KAAA8N,IACA9N,KAAAe,GAAA2N,WASAxM,EAAAiE,UAAA2P,OAAA,WAIA,GAHApU,EAAA,kCAGA,MAAA1B,KAAA8N,IACA,GAAA9N,KAAA4B,MAAA,CACA,GAAAA,GAAA,gBAAA5B,MAAA4B,MAAA8V,EAAAxH,OAAAlQ,KAAA4B,OAAA5B,KAAA4B,KACAF,GAAA,uCAAAE,GACA5B,KAAA0O,QAAmBjK,KAAA3C,EAAAgO,QAAAlO,cAEnB5B,MAAA0O,QAAmBjK,KAAA3C,EAAAgO,WAYnB5N,EAAAiE,UAAA8Q,QAAA,SAAAC,GACAxV,EAAA,aAAAwV,GACAlX,KAAAusB,WAAA,EACAvsB,KAAAwsB,cAAA,QACAxsB,MAAAK,GACAL,KAAAoK,KAAA,aAAA8M,IAUAhV,EAAAiE,UAAAinB,SAAA,SAAA1e,GACA,GAAAtN,GAAAsN,EAAAZ,MAAA9N,KAAA8N,IACAuf,EAAA3e,EAAAjK,OAAA3C,EAAAyN,OAAA,MAAAb,EAAAZ,GAEA,IAAA1M,GAAAisB,EAEA,OAAA3e,EAAAjK,MACA,IAAA3C,GAAAgO,QACA9P,KAAAstB,WACA,MAEA,KAAAxrB,GAAAkO,MACAhQ,KAAAutB,QAAA7e,EACA,MAEA,KAAA5M,GAAA6L,aACA3N,KAAAutB,QAAA7e,EACA,MAEA,KAAA5M,GAAAmO,IACAjQ,KAAAwtB,MAAA9e,EACA,MAEA,KAAA5M,GAAA8L,WACA5N,KAAAwtB,MAAA9e,EACA,MAEA,KAAA5M,GAAAiO,WACA/P,KAAAytB,cACA,MAEA,KAAA3rB,GAAAyN,MACAvP,KAAAoK,KAAA,QAAAsE,EAAAnL,QAYArB,EAAAiE,UAAAonB,QAAA,SAAA7e,GACA,GAAAjJ,GAAAiJ,EAAAnL,QACA7B,GAAA,oBAAA+D,GAEA,MAAAiJ,EAAArO,KACAqB,EAAA,mCACA+D,EAAA+D,KAAAxJ,KAAA0tB,IAAAhf,EAAArO,MAGAL,KAAAusB,UACAniB,EAAAhE,MAAApG,KAAAyF,GAEAzF,KAAAqsB,cAAA7iB,KAAA/D,IAUAvD,EAAAiE,UAAAunB,IAAA,SAAArtB,GACA,GAAAgL,GAAArL,KACA2tB,GAAA,CACA,mBAEA,IAAAA,EAAA,CACAA,GAAA,CACA,IAAAloB,GAAAinB,EAAArmB,UACA3E,GAAA,iBAAA+D,GAEA4F,EAAAqD,QACAjK,KAAAkoB,EAAAlnB,GAAA3D,EAAA8L,WAAA9L,EAAAmO,IACA5P,KACAkD,KAAAkC,OAYAvD,EAAAiE,UAAAqnB,MAAA,SAAA9e,GACA,GAAAgf,GAAA1tB,KAAAosB,KAAA1d,EAAArO,GACA,mBAAAqtB,IACAhsB,EAAA,yBAAAgN,EAAArO,GAAAqO,EAAAnL,MACAmqB,EAAAtnB,MAAApG,KAAA0O,EAAAnL,YACAvD,MAAAosB,KAAA1d,EAAArO,KAEAqB,EAAA,aAAAgN,EAAArO,KAUA6B,EAAAiE,UAAAmnB,UAAA,WACAttB,KAAAusB,WAAA,EACAvsB,KAAAwsB,cAAA,EACAxsB,KAAAoK,KAAA,WACApK,KAAA4tB,gBASA1rB,EAAAiE,UAAAynB,aAAA,WACA,GAAAzpB,EACA,KAAAA,EAAA,EAAaA,EAAAnE,KAAAqsB,cAAAjpB,OAA+Be,IAC5CiG,EAAAhE,MAAApG,UAAAqsB,cAAAloB,GAIA,KAFAnE,KAAAqsB,iBAEAloB,EAAA,EAAaA,EAAAnE,KAAAssB,WAAAlpB,OAA4Be,IACzCnE,KAAA0O,OAAA1O,KAAAssB,WAAAnoB,GAEAnE,MAAAssB,eASApqB,EAAAiE,UAAAsnB,aAAA,WACA/rB,EAAA,yBAAA1B,KAAA8N,KACA9N,KAAAkM,UACAlM,KAAAiX,QAAA,yBAWA/U,EAAAiE,UAAA+F,QAAA,WACA,GAAAlM,KAAAmT,KAAA,CAEA,OAAAhP,GAAA,EAAmBA,EAAAnE,KAAAmT,KAAA/P,OAAsBe,IACzCnE,KAAAmT,KAAAhP,GAAA+H,SAEAlM,MAAAmT,KAAA,KAGAnT,KAAAe,GAAAmL,QAAAlM,OAUAkC,EAAAiE,UAAA+P,MACAhU,EAAAiE,UAAA4Q,WAAA,WAaA,MAZA/W,MAAAusB,YACA7qB,EAAA,6BAAA1B,KAAA8N,KACA9N,KAAA0O,QAAiBjK,KAAA3C,EAAAiO,cAIjB/P,KAAAkM,UAEAlM,KAAAusB,WAEAvsB,KAAAiX,QAAA,wBAEAjX,MAWAkC,EAAAiE,UAAAkW,SAAA,SAAAA,GAEA,MADArc,MAAAysB,MAAApQ,WACArc,MAWAkC,EAAAiE,UAAAoI,OAAA,SAAAA,GAEA,MADAvO,MAAAysB,MAAAle,SACAvO,OtCs6LM,SAAUH,EAAQD,GuCx1MxB,QAAA8sB,GAAAmB,EAAA/nB,GACA,GAAAsD,KAEAtD,MAAA,CAEA,QAAA3B,GAAA2B,GAAA,EAA4B3B,EAAA0pB,EAAAzqB,OAAiBe,IAC7CiF,EAAAjF,EAAA2B,GAAA+nB,EAAA1pB,EAGA,OAAAiF,GAXAvJ,EAAAD,QAAA8sB,GvC62MM,SAAU7sB,EAAQD,GwC71MxB,QAAAkK,GAAA1H,EAAA6pB,EAAAnb,GAEA,MADA1O,GAAA0H,GAAAmiB,EAAAnb,IAEA5E,QAAA,WACA9J,EAAA8H,eAAA+hB,EAAAnb,KAfAjR,EAAAD,QAAAkK,GxCs4MM,SAAUjK,EAAQD,GyCv4MxB,GAAAuR,WAWAtR,GAAAD,QAAA,SAAAwC,EAAA0O,GAEA,GADA,gBAAAA,OAAA1O,EAAA0O,IACA,kBAAAA,GAAA,SAAA/I,OAAA,6BACA,IAAAtC,GAAA0L,EAAA5Q,KAAA8F,UAAA,EACA,mBACA,MAAAyK,GAAA1K,MAAAhE,EAAAqD,EAAAoD,OAAAsI,EAAA5Q,KAAA8F,gBzCo5MM,SAAUxG,EAAQD,G0Cr5MxB,QAAA+T,GAAA9S,GACAA,QACAb,KAAAwL,GAAA3K,EAAA+S,KAAA,IACA5T,KAAA6T,IAAAhT,EAAAgT,KAAA,IACA7T,KAAA8tB,OAAAjtB,EAAAitB,QAAA,EACA9tB,KAAA8T,OAAAjT,EAAAiT,OAAA,GAAAjT,EAAAiT,QAAA,EAAAjT,EAAAiT,OAAA,EACA9T,KAAA0V,SAAA,EApBA7V,EAAAD,QAAA+T,EA8BAA,EAAAxN,UAAAiR,SAAA,WACA,GAAA5L,GAAAxL,KAAAwL,GAAAP,KAAA8iB,IAAA/tB,KAAA8tB,OAAA9tB,KAAA0V,WACA,IAAA1V,KAAA8T,OAAA,CACA,GAAAka,GAAA/iB,KAAAgjB,SACAC,EAAAjjB,KAAAoC,MAAA2gB,EAAAhuB,KAAA8T,OAAAtI,EACAA,GAAA,MAAAP,KAAAoC,MAAA,GAAA2gB,IAAAxiB,EAAA0iB,EAAA1iB,EAAA0iB,EAEA,SAAAjjB,KAAA2I,IAAApI,EAAAxL,KAAA6T,MASAF,EAAAxN,UAAA6Q,MAAA,WACAhX,KAAA0V,SAAA,GASA/B,EAAAxN,UAAA+O,OAAA,SAAAtB,GACA5T,KAAAwL,GAAAoI,GASAD,EAAAxN,UAAAmP,OAAA,SAAAzB,GACA7T,KAAA6T,OASAF,EAAAxN,UAAAiP,UAAA,SAAAtB,GACA9T,KAAA8T", "file": "socket.io.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn \n\n\n// WEBPACK FOOTER //\n// webpack/universalModuleDefinition", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId])\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\texports: {},\n/******/ \t\t\tid: moduleId,\n/******/ \t\t\tloaded: false\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.loaded = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar url = __webpack_require__(1);\n\tvar parser = __webpack_require__(7);\n\tvar Manager = __webpack_require__(12);\n\tvar debug = __webpack_require__(3)('socket.io-client');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = lookup;\n\t\n\t/**\n\t * Managers cache.\n\t */\n\t\n\tvar cache = exports.managers = {};\n\t\n\t/**\n\t * Looks up an existing `Manager` for multiplexing.\n\t * If the user summons:\n\t *\n\t *   `io('http://localhost/a');`\n\t *   `io('http://localhost/b');`\n\t *\n\t * We reuse the existing instance based on same scheme/port/host,\n\t * and we initialize sockets for each namespace.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction lookup (uri, opts) {\n\t  if (typeof uri === 'object') {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t\n\t  opts = opts || {};\n\t\n\t  var parsed = url(uri);\n\t  var source = parsed.source;\n\t  var id = parsed.id;\n\t  var path = parsed.path;\n\t  var sameNamespace = cache[id] && path in cache[id].nsps;\n\t  var newConnection = opts.forceNew || opts['force new connection'] ||\n\t                      false === opts.multiplex || sameNamespace;\n\t\n\t  var io;\n\t\n\t  if (newConnection) {\n\t    debug('ignoring socket cache for %s', source);\n\t    io = Manager(source, opts);\n\t  } else {\n\t    if (!cache[id]) {\n\t      debug('new io instance for %s', source);\n\t      cache[id] = Manager(source, opts);\n\t    }\n\t    io = cache[id];\n\t  }\n\t  if (parsed.query && !opts.query) {\n\t    opts.query = parsed.query;\n\t  }\n\t  return io.socket(parsed.path, opts);\n\t}\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = parser.protocol;\n\t\n\t/**\n\t * `connect`.\n\t *\n\t * @param {String} uri\n\t * @api public\n\t */\n\t\n\texports.connect = lookup;\n\t\n\t/**\n\t * Expose constructors for standalone build.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Manager = __webpack_require__(12);\n\texports.Socket = __webpack_require__(37);\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parseuri = __webpack_require__(2);\n\tvar debug = __webpack_require__(3)('socket.io-client:url');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = url;\n\t\n\t/**\n\t * URL parser.\n\t *\n\t * @param {String} url\n\t * @param {Object} An object meant to mimic window.location.\n\t *                 Defaults to window.location.\n\t * @api public\n\t */\n\t\n\tfunction url (uri, loc) {\n\t  var obj = uri;\n\t\n\t  // default to window.location\n\t  loc = loc || (typeof location !== 'undefined' && location);\n\t  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\t\n\t  // relative path support\n\t  if ('string' === typeof uri) {\n\t    if ('/' === uri.charAt(0)) {\n\t      if ('/' === uri.charAt(1)) {\n\t        uri = loc.protocol + uri;\n\t      } else {\n\t        uri = loc.host + uri;\n\t      }\n\t    }\n\t\n\t    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\t      debug('protocol-less url %s', uri);\n\t      if ('undefined' !== typeof loc) {\n\t        uri = loc.protocol + '//' + uri;\n\t      } else {\n\t        uri = 'https://' + uri;\n\t      }\n\t    }\n\t\n\t    // parse\n\t    debug('parse %s', uri);\n\t    obj = parseuri(uri);\n\t  }\n\t\n\t  // make sure we treat `localhost:80` and `localhost` equally\n\t  if (!obj.port) {\n\t    if (/^(http|ws)$/.test(obj.protocol)) {\n\t      obj.port = '80';\n\t    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n\t      obj.port = '443';\n\t    }\n\t  }\n\t\n\t  obj.path = obj.path || '/';\n\t\n\t  var ipv6 = obj.host.indexOf(':') !== -1;\n\t  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\t\n\t  // define unique id\n\t  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n\t  // define href\n\t  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\t\n\t  return obj;\n\t}\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Parses an URI\n\t *\n\t * <AUTHOR> Levithan <stevenlevithan.com> (MIT license)\n\t * @api private\n\t */\n\t\n\tvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\t\n\tvar parts = [\n\t    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n\t];\n\t\n\tmodule.exports = function parseuri(str) {\n\t    var src = str,\n\t        b = str.indexOf('['),\n\t        e = str.indexOf(']');\n\t\n\t    if (b != -1 && e != -1) {\n\t        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n\t    }\n\t\n\t    var m = re.exec(str || ''),\n\t        uri = {},\n\t        i = 14;\n\t\n\t    while (i--) {\n\t        uri[parts[i]] = m[i] || '';\n\t    }\n\t\n\t    if (b != -1 && e != -1) {\n\t        uri.source = src;\n\t        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n\t        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n\t        uri.ipv6uri = true;\n\t    }\n\t\n\t    uri.pathNames = pathNames(uri, uri['path']);\n\t    uri.queryKey = queryKey(uri, uri['query']);\n\t\n\t    return uri;\n\t};\n\t\n\tfunction pathNames(obj, path) {\n\t    var regx = /\\/{2,9}/g,\n\t        names = path.replace(regx, \"/\").split(\"/\");\n\t\n\t    if (path.substr(0, 1) == '/' || path.length === 0) {\n\t        names.splice(0, 1);\n\t    }\n\t    if (path.substr(path.length - 1, 1) == '/') {\n\t        names.splice(names.length - 1, 1);\n\t    }\n\t\n\t    return names;\n\t}\n\t\n\tfunction queryKey(uri, query) {\n\t    var data = {};\n\t\n\t    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n\t        if ($1) {\n\t            data[$1] = $2;\n\t        }\n\t    });\n\t\n\t    return data;\n\t}\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* WEBPACK VAR INJECTION */(function(process) {'use strict';\n\t\n\tvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\t\n\t/**\n\t * This is the web browser implementation of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = __webpack_require__(5);\n\texports.log = log;\n\texports.formatArgs = formatArgs;\n\texports.save = save;\n\texports.load = load;\n\texports.useColors = useColors;\n\texports.storage = 'undefined' != typeof chrome && 'undefined' != typeof chrome.storage ? chrome.storage.local : localstorage();\n\t\n\t/**\n\t * Colors.\n\t */\n\t\n\texports.colors = ['#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC', '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF', '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC', '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF', '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC', '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033', '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366', '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933', '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC', '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF', '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'];\n\t\n\t/**\n\t * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n\t * and the Firebug extension (any Firefox version) are known\n\t * to support \"%c\" CSS customizations.\n\t *\n\t * TODO: add a `localStorage` variable to explicitly enable/disable colors\n\t */\n\t\n\tfunction useColors() {\n\t  // NB: In an Electron preload script, document will be defined but not fully\n\t  // initialized. Since we know we're in Chrome, we'll just detect this case\n\t  // explicitly\n\t  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n\t    return true;\n\t  }\n\t\n\t  // Internet Explorer and Edge do not support colors.\n\t  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t    return false;\n\t  }\n\t\n\t  // is webkit? http://stackoverflow.com/a/16459606/376773\n\t  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t  return typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance ||\n\t  // is firebug? http://stackoverflow.com/a/398120/376773\n\t  typeof window !== 'undefined' && window.console && (window.console.firebug || window.console.exception && window.console.table) ||\n\t  // is firefox >= v31?\n\t  // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31 ||\n\t  // double check webkit in userAgent just in case we are in a worker\n\t  typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/);\n\t}\n\t\n\t/**\n\t * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n\t */\n\t\n\texports.formatters.j = function (v) {\n\t  try {\n\t    return JSON.stringify(v);\n\t  } catch (err) {\n\t    return '[UnexpectedJSONParseError]: ' + err.message;\n\t  }\n\t};\n\t\n\t/**\n\t * Colorize log arguments if enabled.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction formatArgs(args) {\n\t  var useColors = this.useColors;\n\t\n\t  args[0] = (useColors ? '%c' : '') + this.namespace + (useColors ? ' %c' : ' ') + args[0] + (useColors ? '%c ' : ' ') + '+' + exports.humanize(this.diff);\n\t\n\t  if (!useColors) return;\n\t\n\t  var c = 'color: ' + this.color;\n\t  args.splice(1, 0, c, 'color: inherit');\n\t\n\t  // the final \"%c\" is somewhat tricky, because there could be other\n\t  // arguments passed either before or after the %c, so we need to\n\t  // figure out the correct index to insert the CSS into\n\t  var index = 0;\n\t  var lastC = 0;\n\t  args[0].replace(/%[a-zA-Z%]/g, function (match) {\n\t    if ('%%' === match) return;\n\t    index++;\n\t    if ('%c' === match) {\n\t      // we only are interested in the *last* %c\n\t      // (the user may have provided their own)\n\t      lastC = index;\n\t    }\n\t  });\n\t\n\t  args.splice(lastC, 0, c);\n\t}\n\t\n\t/**\n\t * Invokes `console.log()` when available.\n\t * No-op when `console.log` is not a \"function\".\n\t *\n\t * @api public\n\t */\n\t\n\tfunction log() {\n\t  // this hackery is required for IE8/9, where\n\t  // the `console.log` function doesn't have 'apply'\n\t  return 'object' === (typeof console === 'undefined' ? 'undefined' : _typeof(console)) && console.log && Function.prototype.apply.call(console.log, console, arguments);\n\t}\n\t\n\t/**\n\t * Save `namespaces`.\n\t *\n\t * @param {String} namespaces\n\t * @api private\n\t */\n\t\n\tfunction save(namespaces) {\n\t  try {\n\t    if (null == namespaces) {\n\t      exports.storage.removeItem('debug');\n\t    } else {\n\t      exports.storage.debug = namespaces;\n\t    }\n\t  } catch (e) {}\n\t}\n\t\n\t/**\n\t * Load `namespaces`.\n\t *\n\t * @return {String} returns the previously persisted debug modes\n\t * @api private\n\t */\n\t\n\tfunction load() {\n\t  var r;\n\t  try {\n\t    r = exports.storage.debug;\n\t  } catch (e) {}\n\t\n\t  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\t  if (!r && typeof process !== 'undefined' && 'env' in process) {\n\t    r = process.env.DEBUG;\n\t  }\n\t\n\t  return r;\n\t}\n\t\n\t/**\n\t * Enable namespaces listed in `localStorage.debug` initially.\n\t */\n\t\n\texports.enable(load());\n\t\n\t/**\n\t * Localstorage attempts to return the localstorage.\n\t *\n\t * This is necessary because safari throws\n\t * when a user disables cookies/localstorage\n\t * and you attempt to access it.\n\t *\n\t * @return {LocalStorage}\n\t * @api private\n\t */\n\t\n\tfunction localstorage() {\n\t  try {\n\t    return window.localStorage;\n\t  } catch (e) {}\n\t}\n\t/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(4)))\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\n\t// shim for using process in browser\n\tvar process = module.exports = {};\n\t\n\t// cached from whatever global is present so that test runners that stub it\n\t// don't break things.  But we need to wrap it in a try catch in case it is\n\t// wrapped in strict mode code which doesn't define any globals.  It's inside a\n\t// function because try/catches deoptimize in certain engines.\n\t\n\tvar cachedSetTimeout;\n\tvar cachedClearTimeout;\n\t\n\tfunction defaultSetTimout() {\n\t    throw new Error('setTimeout has not been defined');\n\t}\n\tfunction defaultClearTimeout () {\n\t    throw new Error('clearTimeout has not been defined');\n\t}\n\t(function () {\n\t    try {\n\t        if (typeof setTimeout === 'function') {\n\t            cachedSetTimeout = setTimeout;\n\t        } else {\n\t            cachedSetTimeout = defaultSetTimout;\n\t        }\n\t    } catch (e) {\n\t        cachedSetTimeout = defaultSetTimout;\n\t    }\n\t    try {\n\t        if (typeof clearTimeout === 'function') {\n\t            cachedClearTimeout = clearTimeout;\n\t        } else {\n\t            cachedClearTimeout = defaultClearTimeout;\n\t        }\n\t    } catch (e) {\n\t        cachedClearTimeout = defaultClearTimeout;\n\t    }\n\t} ())\n\tfunction runTimeout(fun) {\n\t    if (cachedSetTimeout === setTimeout) {\n\t        //normal enviroments in sane situations\n\t        return setTimeout(fun, 0);\n\t    }\n\t    // if setTimeout wasn't available but was latter defined\n\t    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n\t        cachedSetTimeout = setTimeout;\n\t        return setTimeout(fun, 0);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedSetTimeout(fun, 0);\n\t    } catch(e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n\t            return cachedSetTimeout.call(null, fun, 0);\n\t        } catch(e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n\t            return cachedSetTimeout.call(this, fun, 0);\n\t        }\n\t    }\n\t\n\t\n\t}\n\tfunction runClearTimeout(marker) {\n\t    if (cachedClearTimeout === clearTimeout) {\n\t        //normal enviroments in sane situations\n\t        return clearTimeout(marker);\n\t    }\n\t    // if clearTimeout wasn't available but was latter defined\n\t    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n\t        cachedClearTimeout = clearTimeout;\n\t        return clearTimeout(marker);\n\t    }\n\t    try {\n\t        // when when somebody has screwed with setTimeout but no I.E. maddness\n\t        return cachedClearTimeout(marker);\n\t    } catch (e){\n\t        try {\n\t            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n\t            return cachedClearTimeout.call(null, marker);\n\t        } catch (e){\n\t            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n\t            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n\t            return cachedClearTimeout.call(this, marker);\n\t        }\n\t    }\n\t\n\t\n\t\n\t}\n\tvar queue = [];\n\tvar draining = false;\n\tvar currentQueue;\n\tvar queueIndex = -1;\n\t\n\tfunction cleanUpNextTick() {\n\t    if (!draining || !currentQueue) {\n\t        return;\n\t    }\n\t    draining = false;\n\t    if (currentQueue.length) {\n\t        queue = currentQueue.concat(queue);\n\t    } else {\n\t        queueIndex = -1;\n\t    }\n\t    if (queue.length) {\n\t        drainQueue();\n\t    }\n\t}\n\t\n\tfunction drainQueue() {\n\t    if (draining) {\n\t        return;\n\t    }\n\t    var timeout = runTimeout(cleanUpNextTick);\n\t    draining = true;\n\t\n\t    var len = queue.length;\n\t    while(len) {\n\t        currentQueue = queue;\n\t        queue = [];\n\t        while (++queueIndex < len) {\n\t            if (currentQueue) {\n\t                currentQueue[queueIndex].run();\n\t            }\n\t        }\n\t        queueIndex = -1;\n\t        len = queue.length;\n\t    }\n\t    currentQueue = null;\n\t    draining = false;\n\t    runClearTimeout(timeout);\n\t}\n\t\n\tprocess.nextTick = function (fun) {\n\t    var args = new Array(arguments.length - 1);\n\t    if (arguments.length > 1) {\n\t        for (var i = 1; i < arguments.length; i++) {\n\t            args[i - 1] = arguments[i];\n\t        }\n\t    }\n\t    queue.push(new Item(fun, args));\n\t    if (queue.length === 1 && !draining) {\n\t        runTimeout(drainQueue);\n\t    }\n\t};\n\t\n\t// v8 likes predictible objects\n\tfunction Item(fun, array) {\n\t    this.fun = fun;\n\t    this.array = array;\n\t}\n\tItem.prototype.run = function () {\n\t    this.fun.apply(null, this.array);\n\t};\n\tprocess.title = 'browser';\n\tprocess.browser = true;\n\tprocess.env = {};\n\tprocess.argv = [];\n\tprocess.version = ''; // empty string to avoid regexp issues\n\tprocess.versions = {};\n\t\n\tfunction noop() {}\n\t\n\tprocess.on = noop;\n\tprocess.addListener = noop;\n\tprocess.once = noop;\n\tprocess.off = noop;\n\tprocess.removeListener = noop;\n\tprocess.removeAllListeners = noop;\n\tprocess.emit = noop;\n\tprocess.prependListener = noop;\n\tprocess.prependOnceListener = noop;\n\t\n\tprocess.listeners = function (name) { return [] }\n\t\n\tprocess.binding = function (name) {\n\t    throw new Error('process.binding is not supported');\n\t};\n\t\n\tprocess.cwd = function () { return '/' };\n\tprocess.chdir = function (dir) {\n\t    throw new Error('process.chdir is not supported');\n\t};\n\tprocess.umask = function() { return 0; };\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t'use strict';\n\t\n\t/**\n\t * This is the common logic for both the Node.js and web browser\n\t * implementations of `debug()`.\n\t *\n\t * Expose `debug()` as the module.\n\t */\n\t\n\texports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\n\texports.coerce = coerce;\n\texports.disable = disable;\n\texports.enable = enable;\n\texports.enabled = enabled;\n\texports.humanize = __webpack_require__(6);\n\t\n\t/**\n\t * Active `debug` instances.\n\t */\n\texports.instances = [];\n\t\n\t/**\n\t * The currently active debug mode names, and names to skip.\n\t */\n\t\n\texports.names = [];\n\texports.skips = [];\n\t\n\t/**\n\t * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t *\n\t * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t */\n\t\n\texports.formatters = {};\n\t\n\t/**\n\t * Select a color.\n\t * @param {String} namespace\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction selectColor(namespace) {\n\t  var hash = 0,\n\t      i;\n\t\n\t  for (i in namespace) {\n\t    hash = (hash << 5) - hash + namespace.charCodeAt(i);\n\t    hash |= 0; // Convert to 32bit integer\n\t  }\n\t\n\t  return exports.colors[Math.abs(hash) % exports.colors.length];\n\t}\n\t\n\t/**\n\t * Create a debugger with the given `namespace`.\n\t *\n\t * @param {String} namespace\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tfunction createDebug(namespace) {\n\t\n\t  var prevTime;\n\t\n\t  function debug() {\n\t    // disabled?\n\t    if (!debug.enabled) return;\n\t\n\t    var self = debug;\n\t\n\t    // set `diff` timestamp\n\t    var curr = +new Date();\n\t    var ms = curr - (prevTime || curr);\n\t    self.diff = ms;\n\t    self.prev = prevTime;\n\t    self.curr = curr;\n\t    prevTime = curr;\n\t\n\t    // turn the `arguments` into a proper Array\n\t    var args = new Array(arguments.length);\n\t    for (var i = 0; i < args.length; i++) {\n\t      args[i] = arguments[i];\n\t    }\n\t\n\t    args[0] = exports.coerce(args[0]);\n\t\n\t    if ('string' !== typeof args[0]) {\n\t      // anything else let's inspect with %O\n\t      args.unshift('%O');\n\t    }\n\t\n\t    // apply any `formatters` transformations\n\t    var index = 0;\n\t    args[0] = args[0].replace(/%([a-zA-Z%])/g, function (match, format) {\n\t      // if we encounter an escaped % then don't increase the array index\n\t      if (match === '%%') return match;\n\t      index++;\n\t      var formatter = exports.formatters[format];\n\t      if ('function' === typeof formatter) {\n\t        var val = args[index];\n\t        match = formatter.call(self, val);\n\t\n\t        // now we need to remove `args[index]` since it's inlined in the `format`\n\t        args.splice(index, 1);\n\t        index--;\n\t      }\n\t      return match;\n\t    });\n\t\n\t    // apply env-specific formatting (colors, etc.)\n\t    exports.formatArgs.call(self, args);\n\t\n\t    var logFn = debug.log || exports.log || console.log.bind(console);\n\t    logFn.apply(self, args);\n\t  }\n\t\n\t  debug.namespace = namespace;\n\t  debug.enabled = exports.enabled(namespace);\n\t  debug.useColors = exports.useColors();\n\t  debug.color = selectColor(namespace);\n\t  debug.destroy = destroy;\n\t\n\t  // env-specific initialization logic for debug instances\n\t  if ('function' === typeof exports.init) {\n\t    exports.init(debug);\n\t  }\n\t\n\t  exports.instances.push(debug);\n\t\n\t  return debug;\n\t}\n\t\n\tfunction destroy() {\n\t  var index = exports.instances.indexOf(this);\n\t  if (index !== -1) {\n\t    exports.instances.splice(index, 1);\n\t    return true;\n\t  } else {\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Enables a debug mode by namespaces. This can include modes\n\t * separated by a colon and wildcards.\n\t *\n\t * @param {String} namespaces\n\t * @api public\n\t */\n\t\n\tfunction enable(namespaces) {\n\t  exports.save(namespaces);\n\t\n\t  exports.names = [];\n\t  exports.skips = [];\n\t\n\t  var i;\n\t  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t  var len = split.length;\n\t\n\t  for (i = 0; i < len; i++) {\n\t    if (!split[i]) continue; // ignore empty strings\n\t    namespaces = split[i].replace(/\\*/g, '.*?');\n\t    if (namespaces[0] === '-') {\n\t      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t    } else {\n\t      exports.names.push(new RegExp('^' + namespaces + '$'));\n\t    }\n\t  }\n\t\n\t  for (i = 0; i < exports.instances.length; i++) {\n\t    var instance = exports.instances[i];\n\t    instance.enabled = exports.enabled(instance.namespace);\n\t  }\n\t}\n\t\n\t/**\n\t * Disable debug output.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction disable() {\n\t  exports.enable('');\n\t}\n\t\n\t/**\n\t * Returns true if the given mode name is enabled, false otherwise.\n\t *\n\t * @param {String} name\n\t * @return {Boolean}\n\t * @api public\n\t */\n\t\n\tfunction enabled(name) {\n\t  if (name[name.length - 1] === '*') {\n\t    return true;\n\t  }\n\t  var i, len;\n\t  for (i = 0, len = exports.skips.length; i < len; i++) {\n\t    if (exports.skips[i].test(name)) {\n\t      return false;\n\t    }\n\t  }\n\t  for (i = 0, len = exports.names.length; i < len; i++) {\n\t    if (exports.names[i].test(name)) {\n\t      return true;\n\t    }\n\t  }\n\t  return false;\n\t}\n\t\n\t/**\n\t * Coerce `val`.\n\t *\n\t * @param {Mixed} val\n\t * @return {Mixed}\n\t * @api private\n\t */\n\t\n\tfunction coerce(val) {\n\t  if (val instanceof Error) return val.stack || val.message;\n\t  return val;\n\t}\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Helpers.\n\t */\n\t\n\tvar s = 1000;\n\tvar m = s * 60;\n\tvar h = m * 60;\n\tvar d = h * 24;\n\tvar y = d * 365.25;\n\t\n\t/**\n\t * Parse or format the given `val`.\n\t *\n\t * Options:\n\t *\n\t *  - `long` verbose formatting [false]\n\t *\n\t * @param {String|Number} val\n\t * @param {Object} [options]\n\t * @throws {Error} throw an error if val is not a non-empty string or a number\n\t * @return {String|Number}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(val, options) {\n\t  options = options || {};\n\t  var type = typeof val;\n\t  if (type === 'string' && val.length > 0) {\n\t    return parse(val);\n\t  } else if (type === 'number' && isNaN(val) === false) {\n\t    return options.long ? fmtLong(val) : fmtShort(val);\n\t  }\n\t  throw new Error(\n\t    'val is not a non-empty string or a valid number. val=' +\n\t      JSON.stringify(val)\n\t  );\n\t};\n\t\n\t/**\n\t * Parse the given `str` and return milliseconds.\n\t *\n\t * @param {String} str\n\t * @return {Number}\n\t * @api private\n\t */\n\t\n\tfunction parse(str) {\n\t  str = String(str);\n\t  if (str.length > 100) {\n\t    return;\n\t  }\n\t  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n\t    str\n\t  );\n\t  if (!match) {\n\t    return;\n\t  }\n\t  var n = parseFloat(match[1]);\n\t  var type = (match[2] || 'ms').toLowerCase();\n\t  switch (type) {\n\t    case 'years':\n\t    case 'year':\n\t    case 'yrs':\n\t    case 'yr':\n\t    case 'y':\n\t      return n * y;\n\t    case 'days':\n\t    case 'day':\n\t    case 'd':\n\t      return n * d;\n\t    case 'hours':\n\t    case 'hour':\n\t    case 'hrs':\n\t    case 'hr':\n\t    case 'h':\n\t      return n * h;\n\t    case 'minutes':\n\t    case 'minute':\n\t    case 'mins':\n\t    case 'min':\n\t    case 'm':\n\t      return n * m;\n\t    case 'seconds':\n\t    case 'second':\n\t    case 'secs':\n\t    case 'sec':\n\t    case 's':\n\t      return n * s;\n\t    case 'milliseconds':\n\t    case 'millisecond':\n\t    case 'msecs':\n\t    case 'msec':\n\t    case 'ms':\n\t      return n;\n\t    default:\n\t      return undefined;\n\t  }\n\t}\n\t\n\t/**\n\t * Short format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtShort(ms) {\n\t  if (ms >= d) {\n\t    return Math.round(ms / d) + 'd';\n\t  }\n\t  if (ms >= h) {\n\t    return Math.round(ms / h) + 'h';\n\t  }\n\t  if (ms >= m) {\n\t    return Math.round(ms / m) + 'm';\n\t  }\n\t  if (ms >= s) {\n\t    return Math.round(ms / s) + 's';\n\t  }\n\t  return ms + 'ms';\n\t}\n\t\n\t/**\n\t * Long format for `ms`.\n\t *\n\t * @param {Number} ms\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tfunction fmtLong(ms) {\n\t  return plural(ms, d, 'day') ||\n\t    plural(ms, h, 'hour') ||\n\t    plural(ms, m, 'minute') ||\n\t    plural(ms, s, 'second') ||\n\t    ms + ' ms';\n\t}\n\t\n\t/**\n\t * Pluralization helper.\n\t */\n\t\n\tfunction plural(ms, n, name) {\n\t  if (ms < n) {\n\t    return;\n\t  }\n\t  if (ms < n * 1.5) {\n\t    return Math.floor(ms / n) + ' ' + name;\n\t  }\n\t  return Math.ceil(ms / n) + ' ' + name + 's';\n\t}\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar debug = __webpack_require__(3)('socket.io-parser');\n\tvar Emitter = __webpack_require__(8);\n\tvar binary = __webpack_require__(9);\n\tvar isArray = __webpack_require__(10);\n\tvar isBuf = __webpack_require__(11);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\texports.protocol = 4;\n\t\n\t/**\n\t * Packet types.\n\t *\n\t * @api public\n\t */\n\t\n\texports.types = [\n\t  'CONNECT',\n\t  'DISCONNECT',\n\t  'EVENT',\n\t  'ACK',\n\t  'ERROR',\n\t  'BINARY_EVENT',\n\t  'BINARY_ACK'\n\t];\n\t\n\t/**\n\t * Packet type `connect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.CONNECT = 0;\n\t\n\t/**\n\t * Packet type `disconnect`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.DISCONNECT = 1;\n\t\n\t/**\n\t * Packet type `event`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.EVENT = 2;\n\t\n\t/**\n\t * Packet type `ack`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ACK = 3;\n\t\n\t/**\n\t * Packet type `error`.\n\t *\n\t * @api public\n\t */\n\t\n\texports.ERROR = 4;\n\t\n\t/**\n\t * Packet type 'binary event'\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_EVENT = 5;\n\t\n\t/**\n\t * Packet type `binary ack`. For acks with binary arguments.\n\t *\n\t * @api public\n\t */\n\t\n\texports.BINARY_ACK = 6;\n\t\n\t/**\n\t * Encoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Encoder = Encoder;\n\t\n\t/**\n\t * Decoder constructor.\n\t *\n\t * @api public\n\t */\n\t\n\texports.Decoder = Decoder;\n\t\n\t/**\n\t * A socket.io Encoder instance\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Encoder() {}\n\t\n\tvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\t\n\t/**\n\t * Encode a packet as a single string if non-binary, or as a\n\t * buffer sequence, depending on packet type.\n\t *\n\t * @param {Object} obj - packet object\n\t * @param {Function} callback - function to handle encodings (likely engine.write)\n\t * @return Calls callback with Array of encodings\n\t * @api public\n\t */\n\t\n\tEncoder.prototype.encode = function(obj, callback){\n\t  debug('encoding packet %j', obj);\n\t\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    encodeAsBinary(obj, callback);\n\t  } else {\n\t    var encoding = encodeAsString(obj);\n\t    callback([encoding]);\n\t  }\n\t};\n\t\n\t/**\n\t * Encode packet as string.\n\t *\n\t * @param {Object} packet\n\t * @return {String} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsString(obj) {\n\t\n\t  // first is type\n\t  var str = '' + obj.type;\n\t\n\t  // attachments if we have them\n\t  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n\t    str += obj.attachments + '-';\n\t  }\n\t\n\t  // if we have a namespace other than `/`\n\t  // we append it followed by a comma `,`\n\t  if (obj.nsp && '/' !== obj.nsp) {\n\t    str += obj.nsp + ',';\n\t  }\n\t\n\t  // immediately followed by the id\n\t  if (null != obj.id) {\n\t    str += obj.id;\n\t  }\n\t\n\t  // json data\n\t  if (null != obj.data) {\n\t    var payload = tryStringify(obj.data);\n\t    if (payload !== false) {\n\t      str += payload;\n\t    } else {\n\t      return ERROR_PACKET;\n\t    }\n\t  }\n\t\n\t  debug('encoded %j as %s', obj, str);\n\t  return str;\n\t}\n\t\n\tfunction tryStringify(str) {\n\t  try {\n\t    return JSON.stringify(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Encode packet as 'buffer sequence' by removing blobs, and\n\t * deconstructing packet into object with placeholders and\n\t * a list of buffers.\n\t *\n\t * @param {Object} packet\n\t * @return {Buffer} encoded\n\t * @api private\n\t */\n\t\n\tfunction encodeAsBinary(obj, callback) {\n\t\n\t  function writeEncoding(bloblessData) {\n\t    var deconstruction = binary.deconstructPacket(bloblessData);\n\t    var pack = encodeAsString(deconstruction.packet);\n\t    var buffers = deconstruction.buffers;\n\t\n\t    buffers.unshift(pack); // add packet info to beginning of data list\n\t    callback(buffers); // write all the buffers\n\t  }\n\t\n\t  binary.removeBlobs(obj, writeEncoding);\n\t}\n\t\n\t/**\n\t * A socket.io Decoder instance\n\t *\n\t * @return {Object} decoder\n\t * @api public\n\t */\n\t\n\tfunction Decoder() {\n\t  this.reconstructor = null;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter` with Decoder.\n\t */\n\t\n\tEmitter(Decoder.prototype);\n\t\n\t/**\n\t * Decodes an encoded packet string into packet JSON.\n\t *\n\t * @param {String} obj - encoded packet\n\t * @return {Object} packet\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.add = function(obj) {\n\t  var packet;\n\t  if (typeof obj === 'string') {\n\t    packet = decodeString(obj);\n\t    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n\t      this.reconstructor = new BinaryReconstructor(packet);\n\t\n\t      // no attachments, labeled binary but no binary data to follow\n\t      if (this.reconstructor.reconPack.attachments === 0) {\n\t        this.emit('decoded', packet);\n\t      }\n\t    } else { // non-binary full packet\n\t      this.emit('decoded', packet);\n\t    }\n\t  } else if (isBuf(obj) || obj.base64) { // raw binary data\n\t    if (!this.reconstructor) {\n\t      throw new Error('got binary data when not reconstructing a packet');\n\t    } else {\n\t      packet = this.reconstructor.takeBinaryData(obj);\n\t      if (packet) { // received final buffer\n\t        this.reconstructor = null;\n\t        this.emit('decoded', packet);\n\t      }\n\t    }\n\t  } else {\n\t    throw new Error('Unknown type: ' + obj);\n\t  }\n\t};\n\t\n\t/**\n\t * Decode a packet String (JSON data)\n\t *\n\t * @param {String} str\n\t * @return {Object} packet\n\t * @api private\n\t */\n\t\n\tfunction decodeString(str) {\n\t  var i = 0;\n\t  // look up type\n\t  var p = {\n\t    type: Number(str.charAt(0))\n\t  };\n\t\n\t  if (null == exports.types[p.type]) {\n\t    return error('unknown packet type ' + p.type);\n\t  }\n\t\n\t  // look up attachments if type binary\n\t  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n\t    var buf = '';\n\t    while (str.charAt(++i) !== '-') {\n\t      buf += str.charAt(i);\n\t      if (i == str.length) break;\n\t    }\n\t    if (buf != Number(buf) || str.charAt(i) !== '-') {\n\t      throw new Error('Illegal attachments');\n\t    }\n\t    p.attachments = Number(buf);\n\t  }\n\t\n\t  // look up namespace (if any)\n\t  if ('/' === str.charAt(i + 1)) {\n\t    p.nsp = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (',' === c) break;\n\t      p.nsp += c;\n\t      if (i === str.length) break;\n\t    }\n\t  } else {\n\t    p.nsp = '/';\n\t  }\n\t\n\t  // look up id\n\t  var next = str.charAt(i + 1);\n\t  if ('' !== next && Number(next) == next) {\n\t    p.id = '';\n\t    while (++i) {\n\t      var c = str.charAt(i);\n\t      if (null == c || Number(c) != c) {\n\t        --i;\n\t        break;\n\t      }\n\t      p.id += str.charAt(i);\n\t      if (i === str.length) break;\n\t    }\n\t    p.id = Number(p.id);\n\t  }\n\t\n\t  // look up json data\n\t  if (str.charAt(++i)) {\n\t    var payload = tryParse(str.substr(i));\n\t    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n\t    if (isPayloadValid) {\n\t      p.data = payload;\n\t    } else {\n\t      return error('invalid payload');\n\t    }\n\t  }\n\t\n\t  debug('decoded %s as %j', str, p);\n\t  return p;\n\t}\n\t\n\tfunction tryParse(str) {\n\t  try {\n\t    return JSON.parse(str);\n\t  } catch(e){\n\t    return false;\n\t  }\n\t}\n\t\n\t/**\n\t * Deallocates a parser's resources\n\t *\n\t * @api public\n\t */\n\t\n\tDecoder.prototype.destroy = function() {\n\t  if (this.reconstructor) {\n\t    this.reconstructor.finishedReconstruction();\n\t  }\n\t};\n\t\n\t/**\n\t * A manager of a binary event's 'buffer sequence'. Should\n\t * be constructed whenever a packet of type BINARY_EVENT is\n\t * decoded.\n\t *\n\t * @param {Object} packet\n\t * @return {BinaryReconstructor} initialized reconstructor\n\t * @api private\n\t */\n\t\n\tfunction BinaryReconstructor(packet) {\n\t  this.reconPack = packet;\n\t  this.buffers = [];\n\t}\n\t\n\t/**\n\t * Method to be called when binary data received from connection\n\t * after a BINARY_EVENT packet.\n\t *\n\t * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n\t * @return {null | Object} returns null if more binary data is expected or\n\t *   a reconstructed packet object if all buffers have been received.\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n\t  this.buffers.push(binData);\n\t  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n\t    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n\t    this.finishedReconstruction();\n\t    return packet;\n\t  }\n\t  return null;\n\t};\n\t\n\t/**\n\t * Cleans up binary packet reconstruction variables.\n\t *\n\t * @api private\n\t */\n\t\n\tBinaryReconstructor.prototype.finishedReconstruction = function() {\n\t  this.reconPack = null;\n\t  this.buffers = [];\n\t};\n\t\n\tfunction error(msg) {\n\t  return {\n\t    type: exports.ERROR,\n\t    data: 'parser error: ' + msg\n\t  };\n\t}\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\r\n\t/**\r\n\t * Expose `Emitter`.\r\n\t */\r\n\t\r\n\tif (true) {\r\n\t  module.exports = Emitter;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Initialize a new `Emitter`.\r\n\t *\r\n\t * @api public\r\n\t */\r\n\t\r\n\tfunction Emitter(obj) {\r\n\t  if (obj) return mixin(obj);\r\n\t};\r\n\t\r\n\t/**\r\n\t * Mixin the emitter properties.\r\n\t *\r\n\t * @param {Object} obj\r\n\t * @return {Object}\r\n\t * @api private\r\n\t */\r\n\t\r\n\tfunction mixin(obj) {\r\n\t  for (var key in Emitter.prototype) {\r\n\t    obj[key] = Emitter.prototype[key];\r\n\t  }\r\n\t  return obj;\r\n\t}\r\n\t\r\n\t/**\r\n\t * Listen on the given `event` with `fn`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.on =\r\n\tEmitter.prototype.addEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n\t    .push(fn);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Adds an `event` listener that will be invoked a single\r\n\t * time then automatically removed.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.once = function(event, fn){\r\n\t  function on() {\r\n\t    this.off(event, on);\r\n\t    fn.apply(this, arguments);\r\n\t  }\r\n\t\r\n\t  on.fn = fn;\r\n\t  this.on(event, on);\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Remove the given callback for `event` or all\r\n\t * registered callbacks.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Function} fn\r\n\t * @return {Emitter}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.off =\r\n\tEmitter.prototype.removeListener =\r\n\tEmitter.prototype.removeAllListeners =\r\n\tEmitter.prototype.removeEventListener = function(event, fn){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  // all\r\n\t  if (0 == arguments.length) {\r\n\t    this._callbacks = {};\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // specific event\r\n\t  var callbacks = this._callbacks['$' + event];\r\n\t  if (!callbacks) return this;\r\n\t\r\n\t  // remove all handlers\r\n\t  if (1 == arguments.length) {\r\n\t    delete this._callbacks['$' + event];\r\n\t    return this;\r\n\t  }\r\n\t\r\n\t  // remove specific handler\r\n\t  var cb;\r\n\t  for (var i = 0; i < callbacks.length; i++) {\r\n\t    cb = callbacks[i];\r\n\t    if (cb === fn || cb.fn === fn) {\r\n\t      callbacks.splice(i, 1);\r\n\t      break;\r\n\t    }\r\n\t  }\r\n\t\r\n\t  // Remove event specific arrays for event types that no\r\n\t  // one is subscribed for to avoid memory leak.\r\n\t  if (callbacks.length === 0) {\r\n\t    delete this._callbacks['$' + event];\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Emit `event` with the given args.\r\n\t *\r\n\t * @param {String} event\r\n\t * @param {Mixed} ...\r\n\t * @return {Emitter}\r\n\t */\r\n\t\r\n\tEmitter.prototype.emit = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t\r\n\t  var args = new Array(arguments.length - 1)\r\n\t    , callbacks = this._callbacks['$' + event];\r\n\t\r\n\t  for (var i = 1; i < arguments.length; i++) {\r\n\t    args[i - 1] = arguments[i];\r\n\t  }\r\n\t\r\n\t  if (callbacks) {\r\n\t    callbacks = callbacks.slice(0);\r\n\t    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n\t      callbacks[i].apply(this, args);\r\n\t    }\r\n\t  }\r\n\t\r\n\t  return this;\r\n\t};\r\n\t\r\n\t/**\r\n\t * Return array of callbacks for `event`.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Array}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.listeners = function(event){\r\n\t  this._callbacks = this._callbacks || {};\r\n\t  return this._callbacks['$' + event] || [];\r\n\t};\r\n\t\r\n\t/**\r\n\t * Check if this emitter has `event` handlers.\r\n\t *\r\n\t * @param {String} event\r\n\t * @return {Boolean}\r\n\t * @api public\r\n\t */\r\n\t\r\n\tEmitter.prototype.hasListeners = function(event){\r\n\t  return !! this.listeners(event).length;\r\n\t};\r\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/*global Blob,File*/\n\t\n\t/**\n\t * Module requirements\n\t */\n\t\n\tvar isArray = __webpack_require__(10);\n\tvar isBuf = __webpack_require__(11);\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\n\tvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\t\n\t/**\n\t * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n\t * Anything with blobs or files should be fed through removeBlobs before coming\n\t * here.\n\t *\n\t * @param {Object} packet - socket.io event packet\n\t * @return {Object} with deconstructed packet and list of buffers\n\t * @api public\n\t */\n\t\n\texports.deconstructPacket = function(packet) {\n\t  var buffers = [];\n\t  var packetData = packet.data;\n\t  var pack = packet;\n\t  pack.data = _deconstructPacket(packetData, buffers);\n\t  pack.attachments = buffers.length; // number of binary 'attachments'\n\t  return {packet: pack, buffers: buffers};\n\t};\n\t\n\tfunction _deconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (isBuf(data)) {\n\t    var placeholder = { _placeholder: true, num: buffers.length };\n\t    buffers.push(data);\n\t    return placeholder;\n\t  } else if (isArray(data)) {\n\t    var newData = new Array(data.length);\n\t    for (var i = 0; i < data.length; i++) {\n\t      newData[i] = _deconstructPacket(data[i], buffers);\n\t    }\n\t    return newData;\n\t  } else if (typeof data === 'object' && !(data instanceof Date)) {\n\t    var newData = {};\n\t    for (var key in data) {\n\t      newData[key] = _deconstructPacket(data[key], buffers);\n\t    }\n\t    return newData;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Reconstructs a binary packet from its placeholder packet and buffers\n\t *\n\t * @param {Object} packet - event packet with placeholders\n\t * @param {Array} buffers - binary buffers to put in placeholder positions\n\t * @return {Object} reconstructed packet\n\t * @api public\n\t */\n\t\n\texports.reconstructPacket = function(packet, buffers) {\n\t  packet.data = _reconstructPacket(packet.data, buffers);\n\t  packet.attachments = undefined; // no longer useful\n\t  return packet;\n\t};\n\t\n\tfunction _reconstructPacket(data, buffers) {\n\t  if (!data) return data;\n\t\n\t  if (data && data._placeholder) {\n\t    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n\t  } else if (isArray(data)) {\n\t    for (var i = 0; i < data.length; i++) {\n\t      data[i] = _reconstructPacket(data[i], buffers);\n\t    }\n\t  } else if (typeof data === 'object') {\n\t    for (var key in data) {\n\t      data[key] = _reconstructPacket(data[key], buffers);\n\t    }\n\t  }\n\t\n\t  return data;\n\t}\n\t\n\t/**\n\t * Asynchronously removes Blobs or Files from data via\n\t * FileReader's readAsArrayBuffer method. Used before encoding\n\t * data as msgpack. Calls callback with the blobless data.\n\t *\n\t * @param {Object} data\n\t * @param {Function} callback\n\t * @api private\n\t */\n\t\n\texports.removeBlobs = function(data, callback) {\n\t  function _removeBlobs(obj, curKey, containingObject) {\n\t    if (!obj) return obj;\n\t\n\t    // convert any blob\n\t    if ((withNativeBlob && obj instanceof Blob) ||\n\t        (withNativeFile && obj instanceof File)) {\n\t      pendingBlobs++;\n\t\n\t      // async filereader\n\t      var fileReader = new FileReader();\n\t      fileReader.onload = function() { // this.result == arraybuffer\n\t        if (containingObject) {\n\t          containingObject[curKey] = this.result;\n\t        }\n\t        else {\n\t          bloblessData = this.result;\n\t        }\n\t\n\t        // if nothing pending its callback time\n\t        if(! --pendingBlobs) {\n\t          callback(bloblessData);\n\t        }\n\t      };\n\t\n\t      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n\t    } else if (isArray(obj)) { // handle array\n\t      for (var i = 0; i < obj.length; i++) {\n\t        _removeBlobs(obj[i], i, obj);\n\t      }\n\t    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n\t      for (var key in obj) {\n\t        _removeBlobs(obj[key], key, obj);\n\t      }\n\t    }\n\t  }\n\t\n\t  var pendingBlobs = 0;\n\t  var bloblessData = data;\n\t  _removeBlobs(bloblessData);\n\t  if (!pendingBlobs) {\n\t    callback(bloblessData);\n\t  }\n\t};\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports) {\n\n\tvar toString = {}.toString;\n\t\n\tmodule.exports = Array.isArray || function (arr) {\n\t  return toString.call(arr) == '[object Array]';\n\t};\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = isBuf;\n\t\n\tvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\n\tvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\t\n\tvar isView = function (obj) {\n\t  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n\t};\n\t\n\t/**\n\t * Returns true if obj is a buffer or an arraybuffer.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction isBuf(obj) {\n\t  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n\t          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n\t}\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar eio = __webpack_require__(13);\n\tvar Socket = __webpack_require__(37);\n\tvar Emitter = __webpack_require__(8);\n\tvar parser = __webpack_require__(7);\n\tvar on = __webpack_require__(39);\n\tvar bind = __webpack_require__(40);\n\tvar debug = __webpack_require__(3)('socket.io-client:manager');\n\tvar indexOf = __webpack_require__(36);\n\tvar Backoff = __webpack_require__(41);\n\t\n\t/**\n\t * IE6+ hasOwnProperty\n\t */\n\t\n\tvar has = Object.prototype.hasOwnProperty;\n\t\n\t/**\n\t * Module exports\n\t */\n\t\n\tmodule.exports = Manager;\n\t\n\t/**\n\t * `Manager` constructor.\n\t *\n\t * @param {String} engine instance or engine uri/opts\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Manager (uri, opts) {\n\t  if (!(this instanceof Manager)) return new Manager(uri, opts);\n\t  if (uri && ('object' === typeof uri)) {\n\t    opts = uri;\n\t    uri = undefined;\n\t  }\n\t  opts = opts || {};\n\t\n\t  opts.path = opts.path || '/socket.io';\n\t  this.nsps = {};\n\t  this.subs = [];\n\t  this.opts = opts;\n\t  this.reconnection(opts.reconnection !== false);\n\t  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n\t  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n\t  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n\t  this.randomizationFactor(opts.randomizationFactor || 0.5);\n\t  this.backoff = new Backoff({\n\t    min: this.reconnectionDelay(),\n\t    max: this.reconnectionDelayMax(),\n\t    jitter: this.randomizationFactor()\n\t  });\n\t  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n\t  this.readyState = 'closed';\n\t  this.uri = uri;\n\t  this.connecting = [];\n\t  this.lastPing = null;\n\t  this.encoding = false;\n\t  this.packetBuffer = [];\n\t  var _parser = opts.parser || parser;\n\t  this.encoder = new _parser.Encoder();\n\t  this.decoder = new _parser.Decoder();\n\t  this.autoConnect = opts.autoConnect !== false;\n\t  if (this.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Propagate given event to sockets and emit on `this`\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.emitAll = function () {\n\t  this.emit.apply(this, arguments);\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Update `socket.id` of all sockets\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.updateSocketIds = function () {\n\t  for (var nsp in this.nsps) {\n\t    if (has.call(this.nsps, nsp)) {\n\t      this.nsps[nsp].id = this.generateId(nsp);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * generate `socket.id` for the given `nsp`\n\t *\n\t * @param {String} nsp\n\t * @return {String}\n\t * @api private\n\t */\n\t\n\tManager.prototype.generateId = function (nsp) {\n\t  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n\t};\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Manager.prototype);\n\t\n\t/**\n\t * Sets the `reconnection` config.\n\t *\n\t * @param {Boolean} true/false if it should automatically reconnect\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnection = function (v) {\n\t  if (!arguments.length) return this._reconnection;\n\t  this._reconnection = !!v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the reconnection attempts config.\n\t *\n\t * @param {Number} max reconnection attempts before giving up\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionAttempts = function (v) {\n\t  if (!arguments.length) return this._reconnectionAttempts;\n\t  this._reconnectionAttempts = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelay = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelay;\n\t  this._reconnectionDelay = v;\n\t  this.backoff && this.backoff.setMin(v);\n\t  return this;\n\t};\n\t\n\tManager.prototype.randomizationFactor = function (v) {\n\t  if (!arguments.length) return this._randomizationFactor;\n\t  this._randomizationFactor = v;\n\t  this.backoff && this.backoff.setJitter(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the maximum delay between reconnections.\n\t *\n\t * @param {Number} delay\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.reconnectionDelayMax = function (v) {\n\t  if (!arguments.length) return this._reconnectionDelayMax;\n\t  this._reconnectionDelayMax = v;\n\t  this.backoff && this.backoff.setMax(v);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the connection timeout. `false` to disable\n\t *\n\t * @return {Manager} self or value\n\t * @api public\n\t */\n\t\n\tManager.prototype.timeout = function (v) {\n\t  if (!arguments.length) return this._timeout;\n\t  this._timeout = v;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Starts trying to reconnect if reconnection is enabled and we have not\n\t * started reconnecting yet\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.maybeReconnectOnOpen = function () {\n\t  // Only try to reconnect if it's the first time we're connecting\n\t  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n\t    // keeps reconnection from firing twice for the same reconnection loop\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Sets the current transport `socket`.\n\t *\n\t * @param {Function} optional, callback\n\t * @return {Manager} self\n\t * @api public\n\t */\n\t\n\tManager.prototype.open =\n\tManager.prototype.connect = function (fn, opts) {\n\t  debug('readyState %s', this.readyState);\n\t  if (~this.readyState.indexOf('open')) return this;\n\t\n\t  debug('opening %s', this.uri);\n\t  this.engine = eio(this.uri, this.opts);\n\t  var socket = this.engine;\n\t  var self = this;\n\t  this.readyState = 'opening';\n\t  this.skipReconnect = false;\n\t\n\t  // emit `open`\n\t  var openSub = on(socket, 'open', function () {\n\t    self.onopen();\n\t    fn && fn();\n\t  });\n\t\n\t  // emit `connect_error`\n\t  var errorSub = on(socket, 'error', function (data) {\n\t    debug('connect_error');\n\t    self.cleanup();\n\t    self.readyState = 'closed';\n\t    self.emitAll('connect_error', data);\n\t    if (fn) {\n\t      var err = new Error('Connection error');\n\t      err.data = data;\n\t      fn(err);\n\t    } else {\n\t      // Only do this if there is no fn to handle the error\n\t      self.maybeReconnectOnOpen();\n\t    }\n\t  });\n\t\n\t  // emit `connect_timeout`\n\t  if (false !== this._timeout) {\n\t    var timeout = this._timeout;\n\t    debug('connect attempt will timeout after %d', timeout);\n\t\n\t    if (timeout === 0) {\n\t      openSub.destroy(); // prevents a race condition with the 'open' event\n\t    }\n\t\n\t    // set timer\n\t    var timer = setTimeout(function () {\n\t      debug('connect attempt timed out after %d', timeout);\n\t      openSub.destroy();\n\t      socket.close();\n\t      socket.emit('error', 'timeout');\n\t      self.emitAll('connect_timeout', timeout);\n\t    }, timeout);\n\t\n\t    this.subs.push({\n\t      destroy: function () {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t\n\t  this.subs.push(openSub);\n\t  this.subs.push(errorSub);\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport open.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onopen = function () {\n\t  debug('open');\n\t\n\t  // clear old subs\n\t  this.cleanup();\n\t\n\t  // mark as open\n\t  this.readyState = 'open';\n\t  this.emit('open');\n\t\n\t  // add new subs\n\t  var socket = this.engine;\n\t  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n\t  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n\t  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n\t  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n\t  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n\t  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n\t};\n\t\n\t/**\n\t * Called upon a ping.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onping = function () {\n\t  this.lastPing = new Date();\n\t  this.emitAll('ping');\n\t};\n\t\n\t/**\n\t * Called upon a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onpong = function () {\n\t  this.emitAll('pong', new Date() - this.lastPing);\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondata = function (data) {\n\t  this.decoder.add(data);\n\t};\n\t\n\t/**\n\t * Called when parser fully decodes a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.ondecoded = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon socket error.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onerror = function (err) {\n\t  debug('error', err);\n\t  this.emitAll('error', err);\n\t};\n\t\n\t/**\n\t * Creates a new socket for the given `nsp`.\n\t *\n\t * @return {Socket}\n\t * @api public\n\t */\n\t\n\tManager.prototype.socket = function (nsp, opts) {\n\t  var socket = this.nsps[nsp];\n\t  if (!socket) {\n\t    socket = new Socket(this, nsp, opts);\n\t    this.nsps[nsp] = socket;\n\t    var self = this;\n\t    socket.on('connecting', onConnecting);\n\t    socket.on('connect', function () {\n\t      socket.id = self.generateId(nsp);\n\t    });\n\t\n\t    if (this.autoConnect) {\n\t      // manually call here since connecting event is fired before listening\n\t      onConnecting();\n\t    }\n\t  }\n\t\n\t  function onConnecting () {\n\t    if (!~indexOf(self.connecting, socket)) {\n\t      self.connecting.push(socket);\n\t    }\n\t  }\n\t\n\t  return socket;\n\t};\n\t\n\t/**\n\t * Called upon a socket close.\n\t *\n\t * @param {Socket} socket\n\t */\n\t\n\tManager.prototype.destroy = function (socket) {\n\t  var index = indexOf(this.connecting, socket);\n\t  if (~index) this.connecting.splice(index, 1);\n\t  if (this.connecting.length) return;\n\t\n\t  this.close();\n\t};\n\t\n\t/**\n\t * Writes a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tManager.prototype.packet = function (packet) {\n\t  debug('writing packet %j', packet);\n\t  var self = this;\n\t  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\t\n\t  if (!self.encoding) {\n\t    // encode, then write to engine with result\n\t    self.encoding = true;\n\t    this.encoder.encode(packet, function (encodedPackets) {\n\t      for (var i = 0; i < encodedPackets.length; i++) {\n\t        self.engine.write(encodedPackets[i], packet.options);\n\t      }\n\t      self.encoding = false;\n\t      self.processPacketQueue();\n\t    });\n\t  } else { // add packet to the queue\n\t    self.packetBuffer.push(packet);\n\t  }\n\t};\n\t\n\t/**\n\t * If packet buffer is non-empty, begins encoding the\n\t * next packet in line.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.processPacketQueue = function () {\n\t  if (this.packetBuffer.length > 0 && !this.encoding) {\n\t    var pack = this.packetBuffer.shift();\n\t    this.packet(pack);\n\t  }\n\t};\n\t\n\t/**\n\t * Clean up transport subscriptions and packet buffer.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.cleanup = function () {\n\t  debug('cleanup');\n\t\n\t  var subsLength = this.subs.length;\n\t  for (var i = 0; i < subsLength; i++) {\n\t    var sub = this.subs.shift();\n\t    sub.destroy();\n\t  }\n\t\n\t  this.packetBuffer = [];\n\t  this.encoding = false;\n\t  this.lastPing = null;\n\t\n\t  this.decoder.destroy();\n\t};\n\t\n\t/**\n\t * Close the current socket.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.close =\n\tManager.prototype.disconnect = function () {\n\t  debug('disconnect');\n\t  this.skipReconnect = true;\n\t  this.reconnecting = false;\n\t  if ('opening' === this.readyState) {\n\t    // `onclose` will not fire because\n\t    // an open event never happened\n\t    this.cleanup();\n\t  }\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  if (this.engine) this.engine.close();\n\t};\n\t\n\t/**\n\t * Called upon engine close.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onclose = function (reason) {\n\t  debug('onclose');\n\t\n\t  this.cleanup();\n\t  this.backoff.reset();\n\t  this.readyState = 'closed';\n\t  this.emit('close', reason);\n\t\n\t  if (this._reconnection && !this.skipReconnect) {\n\t    this.reconnect();\n\t  }\n\t};\n\t\n\t/**\n\t * Attempt a reconnection.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.reconnect = function () {\n\t  if (this.reconnecting || this.skipReconnect) return this;\n\t\n\t  var self = this;\n\t\n\t  if (this.backoff.attempts >= this._reconnectionAttempts) {\n\t    debug('reconnect failed');\n\t    this.backoff.reset();\n\t    this.emitAll('reconnect_failed');\n\t    this.reconnecting = false;\n\t  } else {\n\t    var delay = this.backoff.duration();\n\t    debug('will wait %dms before reconnect attempt', delay);\n\t\n\t    this.reconnecting = true;\n\t    var timer = setTimeout(function () {\n\t      if (self.skipReconnect) return;\n\t\n\t      debug('attempting reconnect');\n\t      self.emitAll('reconnect_attempt', self.backoff.attempts);\n\t      self.emitAll('reconnecting', self.backoff.attempts);\n\t\n\t      // check again for the case socket closed in above events\n\t      if (self.skipReconnect) return;\n\t\n\t      self.open(function (err) {\n\t        if (err) {\n\t          debug('reconnect attempt error');\n\t          self.reconnecting = false;\n\t          self.reconnect();\n\t          self.emitAll('reconnect_error', err.data);\n\t        } else {\n\t          debug('reconnect success');\n\t          self.onreconnect();\n\t        }\n\t      });\n\t    }, delay);\n\t\n\t    this.subs.push({\n\t      destroy: function () {\n\t        clearTimeout(timer);\n\t      }\n\t    });\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful reconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tManager.prototype.onreconnect = function () {\n\t  var attempt = this.backoff.attempts;\n\t  this.reconnecting = false;\n\t  this.backoff.reset();\n\t  this.updateSocketIds();\n\t  this.emitAll('reconnect', attempt);\n\t};\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\tmodule.exports = __webpack_require__(14);\n\t\n\t/**\n\t * Exports parser\n\t *\n\t * @api public\n\t *\n\t */\n\tmodule.exports.parser = __webpack_require__(22);\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar transports = __webpack_require__(15);\n\tvar Emitter = __webpack_require__(8);\n\tvar debug = __webpack_require__(3)('engine.io-client:socket');\n\tvar index = __webpack_require__(36);\n\tvar parser = __webpack_require__(22);\n\tvar parseuri = __webpack_require__(2);\n\tvar parseqs = __webpack_require__(30);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Socket;\n\t\n\t/**\n\t * Socket constructor.\n\t *\n\t * @param {String|Object} uri or options\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Socket (uri, opts) {\n\t  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\t\n\t  opts = opts || {};\n\t\n\t  if (uri && 'object' === typeof uri) {\n\t    opts = uri;\n\t    uri = null;\n\t  }\n\t\n\t  if (uri) {\n\t    uri = parseuri(uri);\n\t    opts.hostname = uri.host;\n\t    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n\t    opts.port = uri.port;\n\t    if (uri.query) opts.query = uri.query;\n\t  } else if (opts.host) {\n\t    opts.hostname = parseuri(opts.host).host;\n\t  }\n\t\n\t  this.secure = null != opts.secure ? opts.secure\n\t    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\t\n\t  if (opts.hostname && !opts.port) {\n\t    // if no port is specified manually, use the protocol default\n\t    opts.port = this.secure ? '443' : '80';\n\t  }\n\t\n\t  this.agent = opts.agent || false;\n\t  this.hostname = opts.hostname ||\n\t    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n\t  this.port = opts.port || (typeof location !== 'undefined' && location.port\n\t      ? location.port\n\t      : (this.secure ? 443 : 80));\n\t  this.query = opts.query || {};\n\t  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n\t  this.upgrade = false !== opts.upgrade;\n\t  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n\t  this.forceJSONP = !!opts.forceJSONP;\n\t  this.jsonp = false !== opts.jsonp;\n\t  this.forceBase64 = !!opts.forceBase64;\n\t  this.enablesXDR = !!opts.enablesXDR;\n\t  this.withCredentials = false !== opts.withCredentials;\n\t  this.timestampParam = opts.timestampParam || 't';\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.transports = opts.transports || ['polling', 'websocket'];\n\t  this.transportOptions = opts.transportOptions || {};\n\t  this.readyState = '';\n\t  this.writeBuffer = [];\n\t  this.prevBufferLen = 0;\n\t  this.policyPort = opts.policyPort || 843;\n\t  this.rememberUpgrade = opts.rememberUpgrade || false;\n\t  this.binaryType = null;\n\t  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n\t  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\t\n\t  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n\t  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n\t    this.perMessageDeflate.threshold = 1024;\n\t  }\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx || null;\n\t  this.key = opts.key || null;\n\t  this.passphrase = opts.passphrase || null;\n\t  this.cert = opts.cert || null;\n\t  this.ca = opts.ca || null;\n\t  this.ciphers = opts.ciphers || null;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n\t  this.forceNode = !!opts.forceNode;\n\t\n\t  // detect ReactNative environment\n\t  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\t\n\t  // other options for Node.js or ReactNative client\n\t  if (typeof self === 'undefined' || this.isReactNative) {\n\t    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n\t      this.extraHeaders = opts.extraHeaders;\n\t    }\n\t\n\t    if (opts.localAddress) {\n\t      this.localAddress = opts.localAddress;\n\t    }\n\t  }\n\t\n\t  // set on handshake\n\t  this.id = null;\n\t  this.upgrades = null;\n\t  this.pingInterval = null;\n\t  this.pingTimeout = null;\n\t\n\t  // set on heartbeat\n\t  this.pingIntervalTimer = null;\n\t  this.pingTimeoutTimer = null;\n\t\n\t  this.open();\n\t}\n\t\n\tSocket.priorWebsocketSuccess = false;\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Protocol version.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.protocol = parser.protocol; // this is an int\n\t\n\t/**\n\t * Expose deps for legacy compatibility\n\t * and standalone browser access.\n\t */\n\t\n\tSocket.Socket = Socket;\n\tSocket.Transport = __webpack_require__(21);\n\tSocket.transports = __webpack_require__(15);\n\tSocket.parser = __webpack_require__(22);\n\t\n\t/**\n\t * Creates transport of the given type.\n\t *\n\t * @param {String} transport name\n\t * @return {Transport}\n\t * @api private\n\t */\n\t\n\tSocket.prototype.createTransport = function (name) {\n\t  debug('creating transport \"%s\"', name);\n\t  var query = clone(this.query);\n\t\n\t  // append engine.io protocol identifier\n\t  query.EIO = parser.protocol;\n\t\n\t  // transport name\n\t  query.transport = name;\n\t\n\t  // per-transport options\n\t  var options = this.transportOptions[name] || {};\n\t\n\t  // session id if we already have one\n\t  if (this.id) query.sid = this.id;\n\t\n\t  var transport = new transports[name]({\n\t    query: query,\n\t    socket: this,\n\t    agent: options.agent || this.agent,\n\t    hostname: options.hostname || this.hostname,\n\t    port: options.port || this.port,\n\t    secure: options.secure || this.secure,\n\t    path: options.path || this.path,\n\t    forceJSONP: options.forceJSONP || this.forceJSONP,\n\t    jsonp: options.jsonp || this.jsonp,\n\t    forceBase64: options.forceBase64 || this.forceBase64,\n\t    enablesXDR: options.enablesXDR || this.enablesXDR,\n\t    withCredentials: options.withCredentials || this.withCredentials,\n\t    timestampRequests: options.timestampRequests || this.timestampRequests,\n\t    timestampParam: options.timestampParam || this.timestampParam,\n\t    policyPort: options.policyPort || this.policyPort,\n\t    pfx: options.pfx || this.pfx,\n\t    key: options.key || this.key,\n\t    passphrase: options.passphrase || this.passphrase,\n\t    cert: options.cert || this.cert,\n\t    ca: options.ca || this.ca,\n\t    ciphers: options.ciphers || this.ciphers,\n\t    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n\t    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n\t    extraHeaders: options.extraHeaders || this.extraHeaders,\n\t    forceNode: options.forceNode || this.forceNode,\n\t    localAddress: options.localAddress || this.localAddress,\n\t    requestTimeout: options.requestTimeout || this.requestTimeout,\n\t    protocols: options.protocols || void (0),\n\t    isReactNative: this.isReactNative\n\t  });\n\t\n\t  return transport;\n\t};\n\t\n\tfunction clone (obj) {\n\t  var o = {};\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      o[i] = obj[i];\n\t    }\n\t  }\n\t  return o;\n\t}\n\t\n\t/**\n\t * Initializes transport to use and starts probe.\n\t *\n\t * @api private\n\t */\n\tSocket.prototype.open = function () {\n\t  var transport;\n\t  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n\t    transport = 'websocket';\n\t  } else if (0 === this.transports.length) {\n\t    // Emit error on next tick so it can be listened to\n\t    var self = this;\n\t    setTimeout(function () {\n\t      self.emit('error', 'No transports available');\n\t    }, 0);\n\t    return;\n\t  } else {\n\t    transport = this.transports[0];\n\t  }\n\t  this.readyState = 'opening';\n\t\n\t  // Retry with the next transport if the transport is disabled (jsonp: false)\n\t  try {\n\t    transport = this.createTransport(transport);\n\t  } catch (e) {\n\t    this.transports.shift();\n\t    this.open();\n\t    return;\n\t  }\n\t\n\t  transport.open();\n\t  this.setTransport(transport);\n\t};\n\t\n\t/**\n\t * Sets the current transport. Disables the existing one (if any).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setTransport = function (transport) {\n\t  debug('setting transport %s', transport.name);\n\t  var self = this;\n\t\n\t  if (this.transport) {\n\t    debug('clearing existing transport %s', this.transport.name);\n\t    this.transport.removeAllListeners();\n\t  }\n\t\n\t  // set up transport\n\t  this.transport = transport;\n\t\n\t  // set up transport listeners\n\t  transport\n\t  .on('drain', function () {\n\t    self.onDrain();\n\t  })\n\t  .on('packet', function (packet) {\n\t    self.onPacket(packet);\n\t  })\n\t  .on('error', function (e) {\n\t    self.onError(e);\n\t  })\n\t  .on('close', function () {\n\t    self.onClose('transport close');\n\t  });\n\t};\n\t\n\t/**\n\t * Probes a transport.\n\t *\n\t * @param {String} transport name\n\t * @api private\n\t */\n\t\n\tSocket.prototype.probe = function (name) {\n\t  debug('probing transport \"%s\"', name);\n\t  var transport = this.createTransport(name, { probe: 1 });\n\t  var failed = false;\n\t  var self = this;\n\t\n\t  Socket.priorWebsocketSuccess = false;\n\t\n\t  function onTransportOpen () {\n\t    if (self.onlyBinaryUpgrades) {\n\t      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n\t      failed = failed || upgradeLosesBinary;\n\t    }\n\t    if (failed) return;\n\t\n\t    debug('probe transport \"%s\" opened', name);\n\t    transport.send([{ type: 'ping', data: 'probe' }]);\n\t    transport.once('packet', function (msg) {\n\t      if (failed) return;\n\t      if ('pong' === msg.type && 'probe' === msg.data) {\n\t        debug('probe transport \"%s\" pong', name);\n\t        self.upgrading = true;\n\t        self.emit('upgrading', transport);\n\t        if (!transport) return;\n\t        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\t\n\t        debug('pausing current transport \"%s\"', self.transport.name);\n\t        self.transport.pause(function () {\n\t          if (failed) return;\n\t          if ('closed' === self.readyState) return;\n\t          debug('changing transport and sending upgrade packet');\n\t\n\t          cleanup();\n\t\n\t          self.setTransport(transport);\n\t          transport.send([{ type: 'upgrade' }]);\n\t          self.emit('upgrade', transport);\n\t          transport = null;\n\t          self.upgrading = false;\n\t          self.flush();\n\t        });\n\t      } else {\n\t        debug('probe transport \"%s\" failed', name);\n\t        var err = new Error('probe error');\n\t        err.transport = transport.name;\n\t        self.emit('upgradeError', err);\n\t      }\n\t    });\n\t  }\n\t\n\t  function freezeTransport () {\n\t    if (failed) return;\n\t\n\t    // Any callback called by transport should be ignored since now\n\t    failed = true;\n\t\n\t    cleanup();\n\t\n\t    transport.close();\n\t    transport = null;\n\t  }\n\t\n\t  // Handle any error that happens while probing\n\t  function onerror (err) {\n\t    var error = new Error('probe error: ' + err);\n\t    error.transport = transport.name;\n\t\n\t    freezeTransport();\n\t\n\t    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\t\n\t    self.emit('upgradeError', error);\n\t  }\n\t\n\t  function onTransportClose () {\n\t    onerror('transport closed');\n\t  }\n\t\n\t  // When the socket is closed while we're probing\n\t  function onclose () {\n\t    onerror('socket closed');\n\t  }\n\t\n\t  // When the socket is upgraded while we're probing\n\t  function onupgrade (to) {\n\t    if (transport && to.name !== transport.name) {\n\t      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n\t      freezeTransport();\n\t    }\n\t  }\n\t\n\t  // Remove all listeners on the transport and on self\n\t  function cleanup () {\n\t    transport.removeListener('open', onTransportOpen);\n\t    transport.removeListener('error', onerror);\n\t    transport.removeListener('close', onTransportClose);\n\t    self.removeListener('close', onclose);\n\t    self.removeListener('upgrading', onupgrade);\n\t  }\n\t\n\t  transport.once('open', onTransportOpen);\n\t  transport.once('error', onerror);\n\t  transport.once('close', onTransportClose);\n\t\n\t  this.once('close', onclose);\n\t  this.once('upgrading', onupgrade);\n\t\n\t  transport.open();\n\t};\n\t\n\t/**\n\t * Called when connection is deemed open.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.onOpen = function () {\n\t  debug('socket open');\n\t  this.readyState = 'open';\n\t  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n\t  this.emit('open');\n\t  this.flush();\n\t\n\t  // we check for `readyState` in case an `open`\n\t  // listener already closed the socket\n\t  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n\t    debug('starting upgrade probes');\n\t    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n\t      this.probe(this.upgrades[i]);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Handles a packet.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onPacket = function (packet) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState ||\n\t      'closing' === this.readyState) {\n\t    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\t\n\t    this.emit('packet', packet);\n\t\n\t    // Socket is live - any packet counts\n\t    this.emit('heartbeat');\n\t\n\t    switch (packet.type) {\n\t      case 'open':\n\t        this.onHandshake(JSON.parse(packet.data));\n\t        break;\n\t\n\t      case 'pong':\n\t        this.setPing();\n\t        this.emit('pong');\n\t        break;\n\t\n\t      case 'error':\n\t        var err = new Error('server error');\n\t        err.code = packet.data;\n\t        this.onError(err);\n\t        break;\n\t\n\t      case 'message':\n\t        this.emit('data', packet.data);\n\t        this.emit('message', packet.data);\n\t        break;\n\t    }\n\t  } else {\n\t    debug('packet received with socket readyState \"%s\"', this.readyState);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon handshake completion.\n\t *\n\t * @param {Object} handshake obj\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHandshake = function (data) {\n\t  this.emit('handshake', data);\n\t  this.id = data.sid;\n\t  this.transport.query.sid = data.sid;\n\t  this.upgrades = this.filterUpgrades(data.upgrades);\n\t  this.pingInterval = data.pingInterval;\n\t  this.pingTimeout = data.pingTimeout;\n\t  this.onOpen();\n\t  // In case open handler closes socket\n\t  if ('closed' === this.readyState) return;\n\t  this.setPing();\n\t\n\t  // Prolong liveness of socket on heartbeat\n\t  this.removeListener('heartbeat', this.onHeartbeat);\n\t  this.on('heartbeat', this.onHeartbeat);\n\t};\n\t\n\t/**\n\t * Resets ping timeout.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onHeartbeat = function (timeout) {\n\t  clearTimeout(this.pingTimeoutTimer);\n\t  var self = this;\n\t  self.pingTimeoutTimer = setTimeout(function () {\n\t    if ('closed' === self.readyState) return;\n\t    self.onClose('ping timeout');\n\t  }, timeout || (self.pingInterval + self.pingTimeout));\n\t};\n\t\n\t/**\n\t * Pings server every `this.pingInterval` and expects response\n\t * within `this.pingTimeout` or closes connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.setPing = function () {\n\t  var self = this;\n\t  clearTimeout(self.pingIntervalTimer);\n\t  self.pingIntervalTimer = setTimeout(function () {\n\t    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n\t    self.ping();\n\t    self.onHeartbeat(self.pingTimeout);\n\t  }, self.pingInterval);\n\t};\n\t\n\t/**\n\t* Sends a ping packet.\n\t*\n\t* @api private\n\t*/\n\t\n\tSocket.prototype.ping = function () {\n\t  var self = this;\n\t  this.sendPacket('ping', function () {\n\t    self.emit('ping');\n\t  });\n\t};\n\t\n\t/**\n\t * Called on `drain` event\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onDrain = function () {\n\t  this.writeBuffer.splice(0, this.prevBufferLen);\n\t\n\t  // setting prevBufferLen = 0 is very important\n\t  // for example, when upgrading, upgrade packet is sent over,\n\t  // and a nonzero prevBufferLen could cause problems on `drain`\n\t  this.prevBufferLen = 0;\n\t\n\t  if (0 === this.writeBuffer.length) {\n\t    this.emit('drain');\n\t  } else {\n\t    this.flush();\n\t  }\n\t};\n\t\n\t/**\n\t * Flush write buffers.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.flush = function () {\n\t  if ('closed' !== this.readyState && this.transport.writable &&\n\t    !this.upgrading && this.writeBuffer.length) {\n\t    debug('flushing %d packets in socket', this.writeBuffer.length);\n\t    this.transport.send(this.writeBuffer);\n\t    // keep track of current length of writeBuffer\n\t    // splice writeBuffer and callbackBuffer on `drain`\n\t    this.prevBufferLen = this.writeBuffer.length;\n\t    this.emit('flush');\n\t  }\n\t};\n\t\n\t/**\n\t * Sends a message.\n\t *\n\t * @param {String} message.\n\t * @param {Function} callback function.\n\t * @param {Object} options.\n\t * @return {Socket} for chaining.\n\t * @api public\n\t */\n\t\n\tSocket.prototype.write =\n\tSocket.prototype.send = function (msg, options, fn) {\n\t  this.sendPacket('message', msg, options, fn);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {String} packet type.\n\t * @param {String} data.\n\t * @param {Object} options.\n\t * @param {Function} callback function.\n\t * @api private\n\t */\n\t\n\tSocket.prototype.sendPacket = function (type, data, options, fn) {\n\t  if ('function' === typeof data) {\n\t    fn = data;\n\t    data = undefined;\n\t  }\n\t\n\t  if ('function' === typeof options) {\n\t    fn = options;\n\t    options = null;\n\t  }\n\t\n\t  if ('closing' === this.readyState || 'closed' === this.readyState) {\n\t    return;\n\t  }\n\t\n\t  options = options || {};\n\t  options.compress = false !== options.compress;\n\t\n\t  var packet = {\n\t    type: type,\n\t    data: data,\n\t    options: options\n\t  };\n\t  this.emit('packetCreate', packet);\n\t  this.writeBuffer.push(packet);\n\t  if (fn) this.once('flush', fn);\n\t  this.flush();\n\t};\n\t\n\t/**\n\t * Closes the connection.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.readyState = 'closing';\n\t\n\t    var self = this;\n\t\n\t    if (this.writeBuffer.length) {\n\t      this.once('drain', function () {\n\t        if (this.upgrading) {\n\t          waitForUpgrade();\n\t        } else {\n\t          close();\n\t        }\n\t      });\n\t    } else if (this.upgrading) {\n\t      waitForUpgrade();\n\t    } else {\n\t      close();\n\t    }\n\t  }\n\t\n\t  function close () {\n\t    self.onClose('forced close');\n\t    debug('socket closing - telling transport to close');\n\t    self.transport.close();\n\t  }\n\t\n\t  function cleanupAndClose () {\n\t    self.removeListener('upgrade', cleanupAndClose);\n\t    self.removeListener('upgradeError', cleanupAndClose);\n\t    close();\n\t  }\n\t\n\t  function waitForUpgrade () {\n\t    // wait for upgrade to finish since we can't send packets while pausing a transport\n\t    self.once('upgrade', cleanupAndClose);\n\t    self.once('upgradeError', cleanupAndClose);\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Called upon transport error\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onError = function (err) {\n\t  debug('socket error %j', err);\n\t  Socket.priorWebsocketSuccess = false;\n\t  this.emit('error', err);\n\t  this.onClose('transport error', err);\n\t};\n\t\n\t/**\n\t * Called upon transport close.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onClose = function (reason, desc) {\n\t  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n\t    debug('socket close with reason: \"%s\"', reason);\n\t    var self = this;\n\t\n\t    // clear timers\n\t    clearTimeout(this.pingIntervalTimer);\n\t    clearTimeout(this.pingTimeoutTimer);\n\t\n\t    // stop event from firing again for transport\n\t    this.transport.removeAllListeners('close');\n\t\n\t    // ensure transport won't stay open\n\t    this.transport.close();\n\t\n\t    // ignore further transport communication\n\t    this.transport.removeAllListeners();\n\t\n\t    // set ready state\n\t    this.readyState = 'closed';\n\t\n\t    // clear session id\n\t    this.id = null;\n\t\n\t    // emit close event\n\t    this.emit('close', reason, desc);\n\t\n\t    // clean buffers after, so users can still\n\t    // grab the buffers on `close` event\n\t    self.writeBuffer = [];\n\t    self.prevBufferLen = 0;\n\t  }\n\t};\n\t\n\t/**\n\t * Filters upgrades, returning only those matching client transports.\n\t *\n\t * @param {Array} server upgrades\n\t * @api private\n\t *\n\t */\n\t\n\tSocket.prototype.filterUpgrades = function (upgrades) {\n\t  var filteredUpgrades = [];\n\t  for (var i = 0, j = upgrades.length; i < j; i++) {\n\t    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n\t  }\n\t  return filteredUpgrades;\n\t};\n\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(16);\n\tvar XHR = __webpack_require__(19);\n\tvar JSONP = __webpack_require__(33);\n\tvar websocket = __webpack_require__(34);\n\t\n\t/**\n\t * Export transports.\n\t */\n\t\n\texports.polling = polling;\n\texports.websocket = websocket;\n\t\n\t/**\n\t * Polling transport polymorphic constructor.\n\t * Decides on xhr vs jsonp based on feature detection.\n\t *\n\t * @api private\n\t */\n\t\n\tfunction polling (opts) {\n\t  var xhr;\n\t  var xd = false;\n\t  var xs = false;\n\t  var jsonp = false !== opts.jsonp;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    xd = opts.hostname !== location.hostname || port !== opts.port;\n\t    xs = opts.secure !== isSSL;\n\t  }\n\t\n\t  opts.xdomain = xd;\n\t  opts.xscheme = xs;\n\t  xhr = new XMLHttpRequest(opts);\n\t\n\t  if ('open' in xhr && !opts.forceJSONP) {\n\t    return new XHR(opts);\n\t  } else {\n\t    if (!jsonp) throw new Error('JSONP disabled');\n\t    return new JSONP(opts);\n\t  }\n\t}\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t// browser shim for xmlhttprequest module\n\t\n\tvar hasCORS = __webpack_require__(17);\n\tvar globalThis = __webpack_require__(18);\n\t\n\tmodule.exports = function (opts) {\n\t  var xdomain = opts.xdomain;\n\t\n\t  // scheme must be same when usign XDomainRequest\n\t  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n\t  var xscheme = opts.xscheme;\n\t\n\t  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n\t  // https://github.com/Automattic/engine.io-client/pull/217\n\t  var enablesXDR = opts.enablesXDR;\n\t\n\t  // XMLHttpRequest can be disabled on IE\n\t  try {\n\t    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n\t      return new XMLHttpRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  // Use XDomainRequest for IE8 if enablesXDR is true\n\t  // because loading bar keeps flashing when using jsonp-polling\n\t  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n\t  try {\n\t    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n\t      return new XDomainRequest();\n\t    }\n\t  } catch (e) { }\n\t\n\t  if (!xdomain) {\n\t    try {\n\t      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n\t    } catch (e) { }\n\t  }\n\t};\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t *\n\t * Logic borrowed from Modernizr:\n\t *\n\t *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n\t */\n\t\n\ttry {\n\t  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n\t    'withCredentials' in new XMLHttpRequest();\n\t} catch (err) {\n\t  // if XMLHttp support is disabled in IE then it will throw\n\t  // when trying to create\n\t  module.exports = false;\n\t}\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = (function () {\n\t  if (typeof self !== 'undefined') {\n\t    return self;\n\t  } else if (typeof window !== 'undefined') {\n\t    return window;\n\t  } else {\n\t    return Function('return this')(); // eslint-disable-line no-new-func\n\t  }\n\t})();\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global attachEvent */\n\t\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar XMLHttpRequest = __webpack_require__(16);\n\tvar Polling = __webpack_require__(20);\n\tvar Emitter = __webpack_require__(8);\n\tvar inherit = __webpack_require__(31);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling-xhr');\n\tvar globalThis = __webpack_require__(18);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = XHR;\n\tmodule.exports.Request = Request;\n\t\n\t/**\n\t * Empty function\n\t */\n\t\n\tfunction empty () {}\n\t\n\t/**\n\t * XHR Polling constructor.\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction XHR (opts) {\n\t  Polling.call(this, opts);\n\t  this.requestTimeout = opts.requestTimeout;\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  if (typeof location !== 'undefined') {\n\t    var isSSL = 'https:' === location.protocol;\n\t    var port = location.port;\n\t\n\t    // some user agents have empty `location.port`\n\t    if (!port) {\n\t      port = isSSL ? 443 : 80;\n\t    }\n\t\n\t    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n\t      port !== opts.port;\n\t    this.xs = opts.secure !== isSSL;\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(XHR, Polling);\n\t\n\t/**\n\t * XHR supports binary\n\t */\n\t\n\tXHR.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Creates a request.\n\t *\n\t * @param {String} method\n\t * @api private\n\t */\n\t\n\tXHR.prototype.request = function (opts) {\n\t  opts = opts || {};\n\t  opts.uri = this.uri();\n\t  opts.xd = this.xd;\n\t  opts.xs = this.xs;\n\t  opts.agent = this.agent || false;\n\t  opts.supportsBinary = this.supportsBinary;\n\t  opts.enablesXDR = this.enablesXDR;\n\t  opts.withCredentials = this.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  opts.requestTimeout = this.requestTimeout;\n\t\n\t  // other options for Node.js client\n\t  opts.extraHeaders = this.extraHeaders;\n\t\n\t  return new Request(opts);\n\t};\n\t\n\t/**\n\t * Sends data.\n\t *\n\t * @param {String} data to send.\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doWrite = function (data, fn) {\n\t  var isBinary = typeof data !== 'string' && data !== undefined;\n\t  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n\t  var self = this;\n\t  req.on('success', fn);\n\t  req.on('error', function (err) {\n\t    self.onError('xhr post error', err);\n\t  });\n\t  this.sendXhr = req;\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tXHR.prototype.doPoll = function () {\n\t  debug('xhr poll');\n\t  var req = this.request();\n\t  var self = this;\n\t  req.on('data', function (data) {\n\t    self.onData(data);\n\t  });\n\t  req.on('error', function (err) {\n\t    self.onError('xhr poll error', err);\n\t  });\n\t  this.pollXhr = req;\n\t};\n\t\n\t/**\n\t * Request constructor\n\t *\n\t * @param {Object} options\n\t * @api public\n\t */\n\t\n\tfunction Request (opts) {\n\t  this.method = opts.method || 'GET';\n\t  this.uri = opts.uri;\n\t  this.xd = !!opts.xd;\n\t  this.xs = !!opts.xs;\n\t  this.async = false !== opts.async;\n\t  this.data = undefined !== opts.data ? opts.data : null;\n\t  this.agent = opts.agent;\n\t  this.isBinary = opts.isBinary;\n\t  this.supportsBinary = opts.supportsBinary;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t  this.requestTimeout = opts.requestTimeout;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t\n\t  this.create();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Request.prototype);\n\t\n\t/**\n\t * Creates the XHR object and sends the request.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.create = function () {\n\t  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\t\n\t  // SSL options for Node.js client\n\t  opts.pfx = this.pfx;\n\t  opts.key = this.key;\n\t  opts.passphrase = this.passphrase;\n\t  opts.cert = this.cert;\n\t  opts.ca = this.ca;\n\t  opts.ciphers = this.ciphers;\n\t  opts.rejectUnauthorized = this.rejectUnauthorized;\n\t\n\t  var xhr = this.xhr = new XMLHttpRequest(opts);\n\t  var self = this;\n\t\n\t  try {\n\t    debug('xhr open %s: %s', this.method, this.uri);\n\t    xhr.open(this.method, this.uri, this.async);\n\t    try {\n\t      if (this.extraHeaders) {\n\t        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n\t        for (var i in this.extraHeaders) {\n\t          if (this.extraHeaders.hasOwnProperty(i)) {\n\t            xhr.setRequestHeader(i, this.extraHeaders[i]);\n\t          }\n\t        }\n\t      }\n\t    } catch (e) {}\n\t\n\t    if ('POST' === this.method) {\n\t      try {\n\t        if (this.isBinary) {\n\t          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n\t        } else {\n\t          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n\t        }\n\t      } catch (e) {}\n\t    }\n\t\n\t    try {\n\t      xhr.setRequestHeader('Accept', '*/*');\n\t    } catch (e) {}\n\t\n\t    // ie6 check\n\t    if ('withCredentials' in xhr) {\n\t      xhr.withCredentials = this.withCredentials;\n\t    }\n\t\n\t    if (this.requestTimeout) {\n\t      xhr.timeout = this.requestTimeout;\n\t    }\n\t\n\t    if (this.hasXDR()) {\n\t      xhr.onload = function () {\n\t        self.onLoad();\n\t      };\n\t      xhr.onerror = function () {\n\t        self.onError(xhr.responseText);\n\t      };\n\t    } else {\n\t      xhr.onreadystatechange = function () {\n\t        if (xhr.readyState === 2) {\n\t          try {\n\t            var contentType = xhr.getResponseHeader('Content-Type');\n\t            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t              xhr.responseType = 'arraybuffer';\n\t            }\n\t          } catch (e) {}\n\t        }\n\t        if (4 !== xhr.readyState) return;\n\t        if (200 === xhr.status || 1223 === xhr.status) {\n\t          self.onLoad();\n\t        } else {\n\t          // make sure the `error` event handler that's user-set\n\t          // does not throw in the same tick and gets caught here\n\t          setTimeout(function () {\n\t            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n\t          }, 0);\n\t        }\n\t      };\n\t    }\n\t\n\t    debug('xhr data %s', this.data);\n\t    xhr.send(this.data);\n\t  } catch (e) {\n\t    // Need to defer since .create() is called directly fhrom the constructor\n\t    // and thus the 'error' event can only be only bound *after* this exception\n\t    // occurs.  Therefore, also, we cannot throw here at all.\n\t    setTimeout(function () {\n\t      self.onError(e);\n\t    }, 0);\n\t    return;\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    this.index = Request.requestsCount++;\n\t    Request.requests[this.index] = this;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon successful response.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onSuccess = function () {\n\t  this.emit('success');\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Called if we have data.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onData = function (data) {\n\t  this.emit('data', data);\n\t  this.onSuccess();\n\t};\n\t\n\t/**\n\t * Called upon error.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onError = function (err) {\n\t  this.emit('error', err);\n\t  this.cleanup(true);\n\t};\n\t\n\t/**\n\t * Cleans up house.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.cleanup = function (fromError) {\n\t  if ('undefined' === typeof this.xhr || null === this.xhr) {\n\t    return;\n\t  }\n\t  // xmlhttprequest\n\t  if (this.hasXDR()) {\n\t    this.xhr.onload = this.xhr.onerror = empty;\n\t  } else {\n\t    this.xhr.onreadystatechange = empty;\n\t  }\n\t\n\t  if (fromError) {\n\t    try {\n\t      this.xhr.abort();\n\t    } catch (e) {}\n\t  }\n\t\n\t  if (typeof document !== 'undefined') {\n\t    delete Request.requests[this.index];\n\t  }\n\t\n\t  this.xhr = null;\n\t};\n\t\n\t/**\n\t * Called upon load.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.onLoad = function () {\n\t  var data;\n\t  try {\n\t    var contentType;\n\t    try {\n\t      contentType = this.xhr.getResponseHeader('Content-Type');\n\t    } catch (e) {}\n\t    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n\t      data = this.xhr.response || this.xhr.responseText;\n\t    } else {\n\t      data = this.xhr.responseText;\n\t    }\n\t  } catch (e) {\n\t    this.onError(e);\n\t  }\n\t  if (null != data) {\n\t    this.onData(data);\n\t  }\n\t};\n\t\n\t/**\n\t * Check if it has XDomainRequest.\n\t *\n\t * @api private\n\t */\n\t\n\tRequest.prototype.hasXDR = function () {\n\t  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n\t};\n\t\n\t/**\n\t * Aborts the request.\n\t *\n\t * @api public\n\t */\n\t\n\tRequest.prototype.abort = function () {\n\t  this.cleanup();\n\t};\n\t\n\t/**\n\t * Aborts pending requests when unloading the window. This is needed to prevent\n\t * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n\t * emitted.\n\t */\n\t\n\tRequest.requestsCount = 0;\n\tRequest.requests = {};\n\t\n\tif (typeof document !== 'undefined') {\n\t  if (typeof attachEvent === 'function') {\n\t    attachEvent('onunload', unloadHandler);\n\t  } else if (typeof addEventListener === 'function') {\n\t    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n\t    addEventListener(terminationEvent, unloadHandler, false);\n\t  }\n\t}\n\t\n\tfunction unloadHandler () {\n\t  for (var i in Request.requests) {\n\t    if (Request.requests.hasOwnProperty(i)) {\n\t      Request.requests[i].abort();\n\t    }\n\t  }\n\t}\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(21);\n\tvar parseqs = __webpack_require__(30);\n\tvar parser = __webpack_require__(22);\n\tvar inherit = __webpack_require__(31);\n\tvar yeast = __webpack_require__(32);\n\tvar debug = __webpack_require__(3)('engine.io-client:polling');\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Polling;\n\t\n\t/**\n\t * Is XHR2 supported?\n\t */\n\t\n\tvar hasXHR2 = (function () {\n\t  var XMLHttpRequest = __webpack_require__(16);\n\t  var xhr = new XMLHttpRequest({ xdomain: false });\n\t  return null != xhr.responseType;\n\t})();\n\t\n\t/**\n\t * Polling interface.\n\t *\n\t * @param {Object} opts\n\t * @api private\n\t */\n\t\n\tfunction Polling (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (!hasXHR2 || forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(Polling, Transport);\n\t\n\t/**\n\t * Transport name.\n\t */\n\t\n\tPolling.prototype.name = 'polling';\n\t\n\t/**\n\t * Opens the socket (triggers polling). We write a PING message to determine\n\t * when the transport is open.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doOpen = function () {\n\t  this.poll();\n\t};\n\t\n\t/**\n\t * Pauses polling.\n\t *\n\t * @param {Function} callback upon buffers are flushed and transport is paused\n\t * @api private\n\t */\n\t\n\tPolling.prototype.pause = function (onPause) {\n\t  var self = this;\n\t\n\t  this.readyState = 'pausing';\n\t\n\t  function pause () {\n\t    debug('paused');\n\t    self.readyState = 'paused';\n\t    onPause();\n\t  }\n\t\n\t  if (this.polling || !this.writable) {\n\t    var total = 0;\n\t\n\t    if (this.polling) {\n\t      debug('we are currently polling - waiting to pause');\n\t      total++;\n\t      this.once('pollComplete', function () {\n\t        debug('pre-pause polling complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t\n\t    if (!this.writable) {\n\t      debug('we are currently writing - waiting to pause');\n\t      total++;\n\t      this.once('drain', function () {\n\t        debug('pre-pause writing complete');\n\t        --total || pause();\n\t      });\n\t    }\n\t  } else {\n\t    pause();\n\t  }\n\t};\n\t\n\t/**\n\t * Starts polling cycle.\n\t *\n\t * @api public\n\t */\n\t\n\tPolling.prototype.poll = function () {\n\t  debug('polling');\n\t  this.polling = true;\n\t  this.doPoll();\n\t  this.emit('poll');\n\t};\n\t\n\t/**\n\t * Overloads onData to detect payloads.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.onData = function (data) {\n\t  var self = this;\n\t  debug('polling got data %s', data);\n\t  var callback = function (packet, index, total) {\n\t    // if its the first message we consider the transport open\n\t    if ('opening' === self.readyState) {\n\t      self.onOpen();\n\t    }\n\t\n\t    // if its a close packet, we close the ongoing requests\n\t    if ('close' === packet.type) {\n\t      self.onClose();\n\t      return false;\n\t    }\n\t\n\t    // otherwise bypass onData and handle the message\n\t    self.onPacket(packet);\n\t  };\n\t\n\t  // decode payload\n\t  parser.decodePayload(data, this.socket.binaryType, callback);\n\t\n\t  // if an event did not trigger closing\n\t  if ('closed' !== this.readyState) {\n\t    // if we got data we're not polling\n\t    this.polling = false;\n\t    this.emit('pollComplete');\n\t\n\t    if ('open' === this.readyState) {\n\t      this.poll();\n\t    } else {\n\t      debug('ignoring poll - transport state \"%s\"', this.readyState);\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * For polling, send a close packet.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.doClose = function () {\n\t  var self = this;\n\t\n\t  function close () {\n\t    debug('writing close packet');\n\t    self.write([{ type: 'close' }]);\n\t  }\n\t\n\t  if ('open' === this.readyState) {\n\t    debug('transport open - closing');\n\t    close();\n\t  } else {\n\t    // in case we're trying to close while\n\t    // handshaking is in progress (GH-164)\n\t    debug('transport not open - deferring close');\n\t    this.once('open', close);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes a packets payload.\n\t *\n\t * @param {Array} data packets\n\t * @param {Function} drain callback\n\t * @api private\n\t */\n\t\n\tPolling.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t  var callbackfn = function () {\n\t    self.writable = true;\n\t    self.emit('drain');\n\t  };\n\t\n\t  parser.encodePayload(packets, this.supportsBinary, function (data) {\n\t    self.doWrite(data, callbackfn);\n\t  });\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tPolling.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'https' : 'http';\n\t  var port = '';\n\t\n\t  // cache busting is forced\n\t  if (false !== this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  if (!this.supportsBinary && !query.sid) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n\t     ('http' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(22);\n\tvar Emitter = __webpack_require__(8);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = Transport;\n\t\n\t/**\n\t * Transport abstract constructor.\n\t *\n\t * @param {Object} options.\n\t * @api private\n\t */\n\t\n\tfunction Transport (opts) {\n\t  this.path = opts.path;\n\t  this.hostname = opts.hostname;\n\t  this.port = opts.port;\n\t  this.secure = opts.secure;\n\t  this.query = opts.query;\n\t  this.timestampParam = opts.timestampParam;\n\t  this.timestampRequests = opts.timestampRequests;\n\t  this.readyState = '';\n\t  this.agent = opts.agent || false;\n\t  this.socket = opts.socket;\n\t  this.enablesXDR = opts.enablesXDR;\n\t  this.withCredentials = opts.withCredentials;\n\t\n\t  // SSL options for Node.js client\n\t  this.pfx = opts.pfx;\n\t  this.key = opts.key;\n\t  this.passphrase = opts.passphrase;\n\t  this.cert = opts.cert;\n\t  this.ca = opts.ca;\n\t  this.ciphers = opts.ciphers;\n\t  this.rejectUnauthorized = opts.rejectUnauthorized;\n\t  this.forceNode = opts.forceNode;\n\t\n\t  // results of ReactNative environment detection\n\t  this.isReactNative = opts.isReactNative;\n\t\n\t  // other options for Node.js client\n\t  this.extraHeaders = opts.extraHeaders;\n\t  this.localAddress = opts.localAddress;\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Transport.prototype);\n\t\n\t/**\n\t * Emits an error.\n\t *\n\t * @param {String} str\n\t * @return {Transport} for chaining\n\t * @api public\n\t */\n\t\n\tTransport.prototype.onError = function (msg, desc) {\n\t  var err = new Error(msg);\n\t  err.type = 'TransportError';\n\t  err.description = desc;\n\t  this.emit('error', err);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Opens the transport.\n\t *\n\t * @api public\n\t */\n\t\n\tTransport.prototype.open = function () {\n\t  if ('closed' === this.readyState || '' === this.readyState) {\n\t    this.readyState = 'opening';\n\t    this.doOpen();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Closes the transport.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.close = function () {\n\t  if ('opening' === this.readyState || 'open' === this.readyState) {\n\t    this.doClose();\n\t    this.onClose();\n\t  }\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends multiple packets.\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\tTransport.prototype.send = function (packets) {\n\t  if ('open' === this.readyState) {\n\t    this.write(packets);\n\t  } else {\n\t    throw new Error('Transport not open');\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon open\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onOpen = function () {\n\t  this.readyState = 'open';\n\t  this.writable = true;\n\t  this.emit('open');\n\t};\n\t\n\t/**\n\t * Called with data.\n\t *\n\t * @param {String} data\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onData = function (data) {\n\t  var packet = parser.decodePacket(data, this.socket.binaryType);\n\t  this.onPacket(packet);\n\t};\n\t\n\t/**\n\t * Called with a decoded packet.\n\t */\n\t\n\tTransport.prototype.onPacket = function (packet) {\n\t  this.emit('packet', packet);\n\t};\n\t\n\t/**\n\t * Called upon close.\n\t *\n\t * @api private\n\t */\n\t\n\tTransport.prototype.onClose = function () {\n\t  this.readyState = 'closed';\n\t  this.emit('close');\n\t};\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar keys = __webpack_require__(23);\n\tvar hasBinary = __webpack_require__(24);\n\tvar sliceBuffer = __webpack_require__(25);\n\tvar after = __webpack_require__(26);\n\tvar utf8 = __webpack_require__(27);\n\t\n\tvar base64encoder;\n\tif (typeof ArrayBuffer !== 'undefined') {\n\t  base64encoder = __webpack_require__(28);\n\t}\n\t\n\t/**\n\t * Check if we are running an android browser. That requires us to use\n\t * ArrayBuffer with polling transports...\n\t *\n\t * http://ghinda.net/jpeg-blob-ajax-android/\n\t */\n\t\n\tvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\t\n\t/**\n\t * Check if we are running in PhantomJS.\n\t * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n\t * https://github.com/ariya/phantomjs/issues/11395\n\t * @type boolean\n\t */\n\tvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\t\n\t/**\n\t * When true, avoids using Blobs to encode payloads.\n\t * @type boolean\n\t */\n\tvar dontSendBlobs = isAndroid || isPhantomJS;\n\t\n\t/**\n\t * Current protocol version.\n\t */\n\t\n\texports.protocol = 3;\n\t\n\t/**\n\t * Packet types.\n\t */\n\t\n\tvar packets = exports.packets = {\n\t    open:     0    // non-ws\n\t  , close:    1    // non-ws\n\t  , ping:     2\n\t  , pong:     3\n\t  , message:  4\n\t  , upgrade:  5\n\t  , noop:     6\n\t};\n\t\n\tvar packetslist = keys(packets);\n\t\n\t/**\n\t * Premade error packet.\n\t */\n\t\n\tvar err = { type: 'error', data: 'parser error' };\n\t\n\t/**\n\t * Create a blob api even for blob builder when vendor prefixes exist\n\t */\n\t\n\tvar Blob = __webpack_require__(29);\n\t\n\t/**\n\t * Encodes a packet.\n\t *\n\t *     <packet type id> [ <data> ]\n\t *\n\t * Example:\n\t *\n\t *     5hello world\n\t *     3\n\t *     4\n\t *\n\t * Binary is encoded in an identical principle\n\t *\n\t * @api private\n\t */\n\t\n\texports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = false;\n\t  }\n\t\n\t  if (typeof utf8encode === 'function') {\n\t    callback = utf8encode;\n\t    utf8encode = null;\n\t  }\n\t\n\t  var data = (packet.data === undefined)\n\t    ? undefined\n\t    : packet.data.buffer || packet.data;\n\t\n\t  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n\t    return encodeArrayBuffer(packet, supportsBinary, callback);\n\t  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n\t    return encodeBlob(packet, supportsBinary, callback);\n\t  }\n\t\n\t  // might be an object with { base64: true, data: dataAsBase64String }\n\t  if (data && data.base64) {\n\t    return encodeBase64Object(packet, callback);\n\t  }\n\t\n\t  // Sending data as a utf-8 string\n\t  var encoded = packets[packet.type];\n\t\n\t  // data fragment is optional\n\t  if (undefined !== packet.data) {\n\t    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n\t  }\n\t\n\t  return callback('' + encoded);\n\t\n\t};\n\t\n\tfunction encodeBase64Object(packet, callback) {\n\t  // packet data is an object { base64: true, data: dataAsBase64String }\n\t  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n\t  return callback(message);\n\t}\n\t\n\t/**\n\t * Encode packet helpers for binary types\n\t */\n\t\n\tfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var data = packet.data;\n\t  var contentArray = new Uint8Array(data);\n\t  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\t\n\t  resultBuffer[0] = packets[packet.type];\n\t  for (var i = 0; i < contentArray.length; i++) {\n\t    resultBuffer[i+1] = contentArray[i];\n\t  }\n\t\n\t  return callback(resultBuffer.buffer);\n\t}\n\t\n\tfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  var fr = new FileReader();\n\t  fr.onload = function() {\n\t    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n\t  };\n\t  return fr.readAsArrayBuffer(packet.data);\n\t}\n\t\n\tfunction encodeBlob(packet, supportsBinary, callback) {\n\t  if (!supportsBinary) {\n\t    return exports.encodeBase64Packet(packet, callback);\n\t  }\n\t\n\t  if (dontSendBlobs) {\n\t    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n\t  }\n\t\n\t  var length = new Uint8Array(1);\n\t  length[0] = packets[packet.type];\n\t  var blob = new Blob([length.buffer, packet.data]);\n\t\n\t  return callback(blob);\n\t}\n\t\n\t/**\n\t * Encodes a packet with binary data in a base64 string\n\t *\n\t * @param {Object} packet, has `type` and `data`\n\t * @return {String} base64 encoded message\n\t */\n\t\n\texports.encodeBase64Packet = function(packet, callback) {\n\t  var message = 'b' + exports.packets[packet.type];\n\t  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n\t    var fr = new FileReader();\n\t    fr.onload = function() {\n\t      var b64 = fr.result.split(',')[1];\n\t      callback(message + b64);\n\t    };\n\t    return fr.readAsDataURL(packet.data);\n\t  }\n\t\n\t  var b64data;\n\t  try {\n\t    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n\t  } catch (e) {\n\t    // iPhone Safari doesn't let you apply with typed arrays\n\t    var typed = new Uint8Array(packet.data);\n\t    var basic = new Array(typed.length);\n\t    for (var i = 0; i < typed.length; i++) {\n\t      basic[i] = typed[i];\n\t    }\n\t    b64data = String.fromCharCode.apply(null, basic);\n\t  }\n\t  message += btoa(b64data);\n\t  return callback(message);\n\t};\n\t\n\t/**\n\t * Decodes a packet. Changes format to Blob if requested.\n\t *\n\t * @return {Object} with `type` and `data` (if any)\n\t * @api private\n\t */\n\t\n\texports.decodePacket = function (data, binaryType, utf8decode) {\n\t  if (data === undefined) {\n\t    return err;\n\t  }\n\t  // String data\n\t  if (typeof data === 'string') {\n\t    if (data.charAt(0) === 'b') {\n\t      return exports.decodeBase64Packet(data.substr(1), binaryType);\n\t    }\n\t\n\t    if (utf8decode) {\n\t      data = tryDecode(data);\n\t      if (data === false) {\n\t        return err;\n\t      }\n\t    }\n\t    var type = data.charAt(0);\n\t\n\t    if (Number(type) != type || !packetslist[type]) {\n\t      return err;\n\t    }\n\t\n\t    if (data.length > 1) {\n\t      return { type: packetslist[type], data: data.substring(1) };\n\t    } else {\n\t      return { type: packetslist[type] };\n\t    }\n\t  }\n\t\n\t  var asArray = new Uint8Array(data);\n\t  var type = asArray[0];\n\t  var rest = sliceBuffer(data, 1);\n\t  if (Blob && binaryType === 'blob') {\n\t    rest = new Blob([rest]);\n\t  }\n\t  return { type: packetslist[type], data: rest };\n\t};\n\t\n\tfunction tryDecode(data) {\n\t  try {\n\t    data = utf8.decode(data, { strict: false });\n\t  } catch (e) {\n\t    return false;\n\t  }\n\t  return data;\n\t}\n\t\n\t/**\n\t * Decodes a packet encoded in a base64 string\n\t *\n\t * @param {String} base64 encoded message\n\t * @return {Object} with `type` and `data` (if any)\n\t */\n\t\n\texports.decodeBase64Packet = function(msg, binaryType) {\n\t  var type = packetslist[msg.charAt(0)];\n\t  if (!base64encoder) {\n\t    return { type: type, data: { base64: true, data: msg.substr(1) } };\n\t  }\n\t\n\t  var data = base64encoder.decode(msg.substr(1));\n\t\n\t  if (binaryType === 'blob' && Blob) {\n\t    data = new Blob([data]);\n\t  }\n\t\n\t  return { type: type, data: data };\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload).\n\t *\n\t *     <length>:data\n\t *\n\t * Example:\n\t *\n\t *     11:hello world2:hi\n\t *\n\t * If any contents are binary, they will be encoded as base64 strings. Base64\n\t * encoded strings are marked with a b before the length specifier\n\t *\n\t * @param {Array} packets\n\t * @api private\n\t */\n\t\n\texports.encodePayload = function (packets, supportsBinary, callback) {\n\t  if (typeof supportsBinary === 'function') {\n\t    callback = supportsBinary;\n\t    supportsBinary = null;\n\t  }\n\t\n\t  var isBinary = hasBinary(packets);\n\t\n\t  if (supportsBinary && isBinary) {\n\t    if (Blob && !dontSendBlobs) {\n\t      return exports.encodePayloadAsBlob(packets, callback);\n\t    }\n\t\n\t    return exports.encodePayloadAsArrayBuffer(packets, callback);\n\t  }\n\t\n\t  if (!packets.length) {\n\t    return callback('0:');\n\t  }\n\t\n\t  function setLengthHeader(message) {\n\t    return message.length + ':' + message;\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n\t      doneCallback(null, setLengthHeader(message));\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(results.join(''));\n\t  });\n\t};\n\t\n\t/**\n\t * Async array map using after\n\t */\n\t\n\tfunction map(ary, each, done) {\n\t  var result = new Array(ary.length);\n\t  var next = after(ary.length, done);\n\t\n\t  var eachWithIndex = function(i, el, cb) {\n\t    each(el, function(error, msg) {\n\t      result[i] = msg;\n\t      cb(error, result);\n\t    });\n\t  };\n\t\n\t  for (var i = 0; i < ary.length; i++) {\n\t    eachWithIndex(i, ary[i], next);\n\t  }\n\t}\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Possible binary contents are\n\t * decoded from their base64 representation\n\t *\n\t * @param {String} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayload = function (data, binaryType, callback) {\n\t  if (typeof data !== 'string') {\n\t    return exports.decodePayloadAsBinary(data, binaryType, callback);\n\t  }\n\t\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var packet;\n\t  if (data === '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t  var length = '', n, msg;\n\t\n\t  for (var i = 0, l = data.length; i < l; i++) {\n\t    var chr = data.charAt(i);\n\t\n\t    if (chr !== ':') {\n\t      length += chr;\n\t      continue;\n\t    }\n\t\n\t    if (length === '' || (length != (n = Number(length)))) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    msg = data.substr(i + 1, n);\n\t\n\t    if (length != msg.length) {\n\t      // parser error - ignoring payload\n\t      return callback(err, 0, 1);\n\t    }\n\t\n\t    if (msg.length) {\n\t      packet = exports.decodePacket(msg, binaryType, false);\n\t\n\t      if (err.type === packet.type && err.data === packet.data) {\n\t        // parser error in individual packet - ignoring payload\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      var ret = callback(packet, i + n, l);\n\t      if (false === ret) return;\n\t    }\n\t\n\t    // advance cursor\n\t    i += n;\n\t    length = '';\n\t  }\n\t\n\t  if (length !== '') {\n\t    // parser error - ignoring payload\n\t    return callback(err, 0, 1);\n\t  }\n\t\n\t};\n\t\n\t/**\n\t * Encodes multiple messages (payload) as binary.\n\t *\n\t * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n\t * 255><data>\n\t *\n\t * Example:\n\t * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n\t *\n\t * @param {Array} packets\n\t * @return {ArrayBuffer} encoded payload\n\t * @api private\n\t */\n\t\n\texports.encodePayloadAsArrayBuffer = function(packets, callback) {\n\t  if (!packets.length) {\n\t    return callback(new ArrayBuffer(0));\n\t  }\n\t\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(data) {\n\t      return doneCallback(null, data);\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, encodedPackets) {\n\t    var totalLength = encodedPackets.reduce(function(acc, p) {\n\t      var len;\n\t      if (typeof p === 'string'){\n\t        len = p.length;\n\t      } else {\n\t        len = p.byteLength;\n\t      }\n\t      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n\t    }, 0);\n\t\n\t    var resultArray = new Uint8Array(totalLength);\n\t\n\t    var bufferIndex = 0;\n\t    encodedPackets.forEach(function(p) {\n\t      var isString = typeof p === 'string';\n\t      var ab = p;\n\t      if (isString) {\n\t        var view = new Uint8Array(p.length);\n\t        for (var i = 0; i < p.length; i++) {\n\t          view[i] = p.charCodeAt(i);\n\t        }\n\t        ab = view.buffer;\n\t      }\n\t\n\t      if (isString) { // not true binary\n\t        resultArray[bufferIndex++] = 0;\n\t      } else { // true binary\n\t        resultArray[bufferIndex++] = 1;\n\t      }\n\t\n\t      var lenStr = ab.byteLength.toString();\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n\t      }\n\t      resultArray[bufferIndex++] = 255;\n\t\n\t      var view = new Uint8Array(ab);\n\t      for (var i = 0; i < view.length; i++) {\n\t        resultArray[bufferIndex++] = view[i];\n\t      }\n\t    });\n\t\n\t    return callback(resultArray.buffer);\n\t  });\n\t};\n\t\n\t/**\n\t * Encode as Blob\n\t */\n\t\n\texports.encodePayloadAsBlob = function(packets, callback) {\n\t  function encodeOne(packet, doneCallback) {\n\t    exports.encodePacket(packet, true, true, function(encoded) {\n\t      var binaryIdentifier = new Uint8Array(1);\n\t      binaryIdentifier[0] = 1;\n\t      if (typeof encoded === 'string') {\n\t        var view = new Uint8Array(encoded.length);\n\t        for (var i = 0; i < encoded.length; i++) {\n\t          view[i] = encoded.charCodeAt(i);\n\t        }\n\t        encoded = view.buffer;\n\t        binaryIdentifier[0] = 0;\n\t      }\n\t\n\t      var len = (encoded instanceof ArrayBuffer)\n\t        ? encoded.byteLength\n\t        : encoded.size;\n\t\n\t      var lenStr = len.toString();\n\t      var lengthAry = new Uint8Array(lenStr.length + 1);\n\t      for (var i = 0; i < lenStr.length; i++) {\n\t        lengthAry[i] = parseInt(lenStr[i]);\n\t      }\n\t      lengthAry[lenStr.length] = 255;\n\t\n\t      if (Blob) {\n\t        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n\t        doneCallback(null, blob);\n\t      }\n\t    });\n\t  }\n\t\n\t  map(packets, encodeOne, function(err, results) {\n\t    return callback(new Blob(results));\n\t  });\n\t};\n\t\n\t/*\n\t * Decodes data when a payload is maybe expected. Strings are decoded by\n\t * interpreting each byte as a key code for entries marked to start with 0. See\n\t * description of encodePayloadAsBinary\n\t *\n\t * @param {ArrayBuffer} data, callback method\n\t * @api public\n\t */\n\t\n\texports.decodePayloadAsBinary = function (data, binaryType, callback) {\n\t  if (typeof binaryType === 'function') {\n\t    callback = binaryType;\n\t    binaryType = null;\n\t  }\n\t\n\t  var bufferTail = data;\n\t  var buffers = [];\n\t\n\t  while (bufferTail.byteLength > 0) {\n\t    var tailArray = new Uint8Array(bufferTail);\n\t    var isString = tailArray[0] === 0;\n\t    var msgLength = '';\n\t\n\t    for (var i = 1; ; i++) {\n\t      if (tailArray[i] === 255) break;\n\t\n\t      // 310 = char length of Number.MAX_VALUE\n\t      if (msgLength.length > 310) {\n\t        return callback(err, 0, 1);\n\t      }\n\t\n\t      msgLength += tailArray[i];\n\t    }\n\t\n\t    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n\t    msgLength = parseInt(msgLength);\n\t\n\t    var msg = sliceBuffer(bufferTail, 0, msgLength);\n\t    if (isString) {\n\t      try {\n\t        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n\t      } catch (e) {\n\t        // iPhone Safari doesn't let you apply to typed arrays\n\t        var typed = new Uint8Array(msg);\n\t        msg = '';\n\t        for (var i = 0; i < typed.length; i++) {\n\t          msg += String.fromCharCode(typed[i]);\n\t        }\n\t      }\n\t    }\n\t\n\t    buffers.push(msg);\n\t    bufferTail = sliceBuffer(bufferTail, msgLength);\n\t  }\n\t\n\t  var total = buffers.length;\n\t  buffers.forEach(function(buffer, i) {\n\t    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n\t  });\n\t};\n\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Gets the keys for an object.\n\t *\n\t * @return {Array} keys\n\t * @api private\n\t */\n\t\n\tmodule.exports = Object.keys || function keys (obj){\n\t  var arr = [];\n\t  var has = Object.prototype.hasOwnProperty;\n\t\n\t  for (var i in obj) {\n\t    if (has.call(obj, i)) {\n\t      arr.push(i);\n\t    }\n\t  }\n\t  return arr;\n\t};\n\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/* global Blob File */\n\t\n\t/*\n\t * Module requirements.\n\t */\n\t\n\tvar isArray = __webpack_require__(10);\n\t\n\tvar toString = Object.prototype.toString;\n\tvar withNativeBlob = typeof Blob === 'function' ||\n\t                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\n\tvar withNativeFile = typeof File === 'function' ||\n\t                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = hasBinary;\n\t\n\t/**\n\t * Checks for binary data.\n\t *\n\t * Supports Buffer, ArrayBuffer, Blob and File.\n\t *\n\t * @param {Object} anything\n\t * @api public\n\t */\n\t\n\tfunction hasBinary (obj) {\n\t  if (!obj || typeof obj !== 'object') {\n\t    return false;\n\t  }\n\t\n\t  if (isArray(obj)) {\n\t    for (var i = 0, l = obj.length; i < l; i++) {\n\t      if (hasBinary(obj[i])) {\n\t        return true;\n\t      }\n\t    }\n\t    return false;\n\t  }\n\t\n\t  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n\t    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n\t    (withNativeBlob && obj instanceof Blob) ||\n\t    (withNativeFile && obj instanceof File)\n\t  ) {\n\t    return true;\n\t  }\n\t\n\t  // see: https://github.com/Automattic/has-binary/pull/4\n\t  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n\t    return hasBinary(obj.toJSON(), true);\n\t  }\n\t\n\t  for (var key in obj) {\n\t    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n\t      return true;\n\t    }\n\t  }\n\t\n\t  return false;\n\t}\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * An abstraction for slicing an arraybuffer even when\n\t * ArrayBuffer.prototype.slice is not supported\n\t *\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(arraybuffer, start, end) {\n\t  var bytes = arraybuffer.byteLength;\n\t  start = start || 0;\n\t  end = end || bytes;\n\t\n\t  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\t\n\t  if (start < 0) { start += bytes; }\n\t  if (end < 0) { end += bytes; }\n\t  if (end > bytes) { end = bytes; }\n\t\n\t  if (start >= bytes || start >= end || bytes === 0) {\n\t    return new ArrayBuffer(0);\n\t  }\n\t\n\t  var abv = new Uint8Array(arraybuffer);\n\t  var result = new Uint8Array(end - start);\n\t  for (var i = start, ii = 0; i < end; i++, ii++) {\n\t    result[ii] = abv[i];\n\t  }\n\t  return result.buffer;\n\t};\n\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = after\n\t\n\tfunction after(count, callback, err_cb) {\n\t    var bail = false\n\t    err_cb = err_cb || noop\n\t    proxy.count = count\n\t\n\t    return (count === 0) ? callback() : proxy\n\t\n\t    function proxy(err, result) {\n\t        if (proxy.count <= 0) {\n\t            throw new Error('after called too many times')\n\t        }\n\t        --proxy.count\n\t\n\t        // after first error, rest are passed to err_cb\n\t        if (err) {\n\t            bail = true\n\t            callback(err)\n\t            // future error callbacks will go to error handler\n\t            callback = err_cb\n\t        } else if (proxy.count === 0 && !bail) {\n\t            callback(null, result)\n\t        }\n\t    }\n\t}\n\t\n\tfunction noop() {}\n\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports) {\n\n\t/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\t\n\tvar stringFromCharCode = String.fromCharCode;\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2decode(string) {\n\t\tvar output = [];\n\t\tvar counter = 0;\n\t\tvar length = string.length;\n\t\tvar value;\n\t\tvar extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\t\n\t// Taken from https://mths.be/punycode\n\tfunction ucs2encode(array) {\n\t\tvar length = array.length;\n\t\tvar index = -1;\n\t\tvar value;\n\t\tvar output = '';\n\t\twhile (++index < length) {\n\t\t\tvalue = array[index];\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t}\n\t\treturn output;\n\t}\n\t\n\tfunction checkScalarValue(codePoint, strict) {\n\t\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\t\tif (strict) {\n\t\t\t\tthrow Error(\n\t\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t\t' is not a scalar value'\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction createByte(codePoint, shift) {\n\t\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n\t}\n\t\n\tfunction encodeCodePoint(codePoint, strict) {\n\t\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\t\treturn stringFromCharCode(codePoint);\n\t\t}\n\t\tvar symbol = '';\n\t\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t\t}\n\t\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\t\tcodePoint = 0xFFFD;\n\t\t\t}\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\t\tsymbol += createByte(codePoint, 12);\n\t\t\tsymbol += createByte(codePoint, 6);\n\t\t}\n\t\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\t\treturn symbol;\n\t}\n\t\n\tfunction utf8encode(string, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tvar codePoints = ucs2decode(string);\n\t\tvar length = codePoints.length;\n\t\tvar index = -1;\n\t\tvar codePoint;\n\t\tvar byteString = '';\n\t\twhile (++index < length) {\n\t\t\tcodePoint = codePoints[index];\n\t\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t\t}\n\t\treturn byteString;\n\t}\n\t\n\t/*--------------------------------------------------------------------------*/\n\t\n\tfunction readContinuationByte() {\n\t\tif (byteIndex >= byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\tif ((continuationByte & 0xC0) == 0x80) {\n\t\t\treturn continuationByte & 0x3F;\n\t\t}\n\t\n\t\t// If we end up here, it’s not a continuation byte\n\t\tthrow Error('Invalid continuation byte');\n\t}\n\t\n\tfunction decodeSymbol(strict) {\n\t\tvar byte1;\n\t\tvar byte2;\n\t\tvar byte3;\n\t\tvar byte4;\n\t\tvar codePoint;\n\t\n\t\tif (byteIndex > byteCount) {\n\t\t\tthrow Error('Invalid byte index');\n\t\t}\n\t\n\t\tif (byteIndex == byteCount) {\n\t\t\treturn false;\n\t\t}\n\t\n\t\t// Read first byte\n\t\tbyte1 = byteArray[byteIndex] & 0xFF;\n\t\tbyteIndex++;\n\t\n\t\t// 1-byte sequence (no continuation bytes)\n\t\tif ((byte1 & 0x80) == 0) {\n\t\t\treturn byte1;\n\t\t}\n\t\n\t\t// 2-byte sequence\n\t\tif ((byte1 & 0xE0) == 0xC0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\t\tif (codePoint >= 0x80) {\n\t\t\t\treturn codePoint;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 3-byte sequence (may include unpaired surrogates)\n\t\tif ((byte1 & 0xF0) == 0xE0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\t\tif (codePoint >= 0x0800) {\n\t\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t\t} else {\n\t\t\t\tthrow Error('Invalid continuation byte');\n\t\t\t}\n\t\t}\n\t\n\t\t// 4-byte sequence\n\t\tif ((byte1 & 0xF8) == 0xF0) {\n\t\t\tbyte2 = readContinuationByte();\n\t\t\tbyte3 = readContinuationByte();\n\t\t\tbyte4 = readContinuationByte();\n\t\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t\t(byte3 << 0x06) | byte4;\n\t\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\t\treturn codePoint;\n\t\t\t}\n\t\t}\n\t\n\t\tthrow Error('Invalid UTF-8 detected');\n\t}\n\t\n\tvar byteArray;\n\tvar byteCount;\n\tvar byteIndex;\n\tfunction utf8decode(byteString, opts) {\n\t\topts = opts || {};\n\t\tvar strict = false !== opts.strict;\n\t\n\t\tbyteArray = ucs2decode(byteString);\n\t\tbyteCount = byteArray.length;\n\t\tbyteIndex = 0;\n\t\tvar codePoints = [];\n\t\tvar tmp;\n\t\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\t\tcodePoints.push(tmp);\n\t\t}\n\t\treturn ucs2encode(codePoints);\n\t}\n\t\n\tmodule.exports = {\n\t\tversion: '2.1.2',\n\t\tencode: utf8encode,\n\t\tdecode: utf8decode\n\t};\n\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports) {\n\n\t/*\n\t * base64-arraybuffer\n\t * https://github.com/niklasvh/base64-arraybuffer\n\t *\n\t * Copyright (c) 2012 Niklas von Hertzen\n\t * Licensed under the MIT license.\n\t */\n\t(function(chars){\n\t  \"use strict\";\n\t\n\t  exports.encode = function(arraybuffer) {\n\t    var bytes = new Uint8Array(arraybuffer),\n\t    i, len = bytes.length, base64 = \"\";\n\t\n\t    for (i = 0; i < len; i+=3) {\n\t      base64 += chars[bytes[i] >> 2];\n\t      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n\t      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n\t      base64 += chars[bytes[i + 2] & 63];\n\t    }\n\t\n\t    if ((len % 3) === 2) {\n\t      base64 = base64.substring(0, base64.length - 1) + \"=\";\n\t    } else if (len % 3 === 1) {\n\t      base64 = base64.substring(0, base64.length - 2) + \"==\";\n\t    }\n\t\n\t    return base64;\n\t  };\n\t\n\t  exports.decode =  function(base64) {\n\t    var bufferLength = base64.length * 0.75,\n\t    len = base64.length, i, p = 0,\n\t    encoded1, encoded2, encoded3, encoded4;\n\t\n\t    if (base64[base64.length - 1] === \"=\") {\n\t      bufferLength--;\n\t      if (base64[base64.length - 2] === \"=\") {\n\t        bufferLength--;\n\t      }\n\t    }\n\t\n\t    var arraybuffer = new ArrayBuffer(bufferLength),\n\t    bytes = new Uint8Array(arraybuffer);\n\t\n\t    for (i = 0; i < len; i+=4) {\n\t      encoded1 = chars.indexOf(base64[i]);\n\t      encoded2 = chars.indexOf(base64[i+1]);\n\t      encoded3 = chars.indexOf(base64[i+2]);\n\t      encoded4 = chars.indexOf(base64[i+3]);\n\t\n\t      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n\t      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n\t      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n\t    }\n\t\n\t    return arraybuffer;\n\t  };\n\t})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports) {\n\n\t/**\r\n\t * Create a blob builder even when vendor prefixes exist\r\n\t */\r\n\t\r\n\tvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n\t  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n\t  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n\t  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n\t  false;\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor is supported\r\n\t */\r\n\t\r\n\tvar blobSupported = (function() {\r\n\t  try {\r\n\t    var a = new Blob(['hi']);\r\n\t    return a.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if Blob constructor supports ArrayBufferViews\r\n\t * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n\t */\r\n\t\r\n\tvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n\t  try {\r\n\t    var b = new Blob([new Uint8Array([1,2])]);\r\n\t    return b.size === 2;\r\n\t  } catch(e) {\r\n\t    return false;\r\n\t  }\r\n\t})();\r\n\t\r\n\t/**\r\n\t * Check if BlobBuilder is supported\r\n\t */\r\n\t\r\n\tvar blobBuilderSupported = BlobBuilder\r\n\t  && BlobBuilder.prototype.append\r\n\t  && BlobBuilder.prototype.getBlob;\r\n\t\r\n\t/**\r\n\t * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n\t * Used by BlobBuilder constructor and old browsers that didn't\r\n\t * support it in the Blob constructor.\r\n\t */\r\n\t\r\n\tfunction mapArrayBufferViews(ary) {\r\n\t  return ary.map(function(chunk) {\r\n\t    if (chunk.buffer instanceof ArrayBuffer) {\r\n\t      var buf = chunk.buffer;\r\n\t\r\n\t      // if this is a subarray, make a copy so we only\r\n\t      // include the subarray region from the underlying buffer\r\n\t      if (chunk.byteLength !== buf.byteLength) {\r\n\t        var copy = new Uint8Array(chunk.byteLength);\r\n\t        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n\t        buf = copy.buffer;\r\n\t      }\r\n\t\r\n\t      return buf;\r\n\t    }\r\n\t\r\n\t    return chunk;\r\n\t  });\r\n\t}\r\n\t\r\n\tfunction BlobBuilderConstructor(ary, options) {\r\n\t  options = options || {};\r\n\t\r\n\t  var bb = new BlobBuilder();\r\n\t  mapArrayBufferViews(ary).forEach(function(part) {\r\n\t    bb.append(part);\r\n\t  });\r\n\t\r\n\t  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n\t};\r\n\t\r\n\tfunction BlobConstructor(ary, options) {\r\n\t  return new Blob(mapArrayBufferViews(ary), options || {});\r\n\t};\r\n\t\r\n\tif (typeof Blob !== 'undefined') {\r\n\t  BlobBuilderConstructor.prototype = Blob.prototype;\r\n\t  BlobConstructor.prototype = Blob.prototype;\r\n\t}\r\n\t\r\n\tmodule.exports = (function() {\r\n\t  if (blobSupported) {\r\n\t    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n\t  } else if (blobBuilderSupported) {\r\n\t    return BlobBuilderConstructor;\r\n\t  } else {\r\n\t    return undefined;\r\n\t  }\r\n\t})();\r\n\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Compiles a querystring\n\t * Returns string representation of the object\n\t *\n\t * @param {Object}\n\t * @api private\n\t */\n\t\n\texports.encode = function (obj) {\n\t  var str = '';\n\t\n\t  for (var i in obj) {\n\t    if (obj.hasOwnProperty(i)) {\n\t      if (str.length) str += '&';\n\t      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n\t    }\n\t  }\n\t\n\t  return str;\n\t};\n\t\n\t/**\n\t * Parses a simple querystring into an object\n\t *\n\t * @param {String} qs\n\t * @api private\n\t */\n\t\n\texports.decode = function(qs){\n\t  var qry = {};\n\t  var pairs = qs.split('&');\n\t  for (var i = 0, l = pairs.length; i < l; i++) {\n\t    var pair = pairs[i].split('=');\n\t    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n\t  }\n\t  return qry;\n\t};\n\n\n/***/ }),\n/* 31 */\n/***/ (function(module, exports) {\n\n\t\n\tmodule.exports = function(a, b){\n\t  var fn = function(){};\n\t  fn.prototype = b.prototype;\n\t  a.prototype = new fn;\n\t  a.prototype.constructor = a;\n\t};\n\n/***/ }),\n/* 32 */\n/***/ (function(module, exports) {\n\n\t'use strict';\n\t\n\tvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n\t  , length = 64\n\t  , map = {}\n\t  , seed = 0\n\t  , i = 0\n\t  , prev;\n\t\n\t/**\n\t * Return a string representing the specified number.\n\t *\n\t * @param {Number} num The number to convert.\n\t * @returns {String} The string representation of the number.\n\t * @api public\n\t */\n\tfunction encode(num) {\n\t  var encoded = '';\n\t\n\t  do {\n\t    encoded = alphabet[num % length] + encoded;\n\t    num = Math.floor(num / length);\n\t  } while (num > 0);\n\t\n\t  return encoded;\n\t}\n\t\n\t/**\n\t * Return the integer value specified by the given string.\n\t *\n\t * @param {String} str The string to convert.\n\t * @returns {Number} The integer value represented by the string.\n\t * @api public\n\t */\n\tfunction decode(str) {\n\t  var decoded = 0;\n\t\n\t  for (i = 0; i < str.length; i++) {\n\t    decoded = decoded * length + map[str.charAt(i)];\n\t  }\n\t\n\t  return decoded;\n\t}\n\t\n\t/**\n\t * Yeast: A tiny growing id generator.\n\t *\n\t * @returns {String} A unique id.\n\t * @api public\n\t */\n\tfunction yeast() {\n\t  var now = encode(+new Date());\n\t\n\t  if (now !== prev) return seed = 0, prev = now;\n\t  return now +'.'+ encode(seed++);\n\t}\n\t\n\t//\n\t// Map each character to its index.\n\t//\n\tfor (; i < length; i++) map[alphabet[i]] = i;\n\t\n\t//\n\t// Expose the `yeast`, `encode` and `decode` functions.\n\t//\n\tyeast.encode = encode;\n\tyeast.decode = decode;\n\tmodule.exports = yeast;\n\n\n/***/ }),\n/* 33 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module requirements.\n\t */\n\t\n\tvar Polling = __webpack_require__(20);\n\tvar inherit = __webpack_require__(31);\n\tvar globalThis = __webpack_require__(18);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = JSONPPolling;\n\t\n\t/**\n\t * Cached regular expressions.\n\t */\n\t\n\tvar rNewline = /\\n/g;\n\tvar rEscapedNewline = /\\\\n/g;\n\t\n\t/**\n\t * Global JSONP callbacks.\n\t */\n\t\n\tvar callbacks;\n\t\n\t/**\n\t * Noop.\n\t */\n\t\n\tfunction empty () { }\n\t\n\t/**\n\t * JSONP Polling constructor.\n\t *\n\t * @param {Object} opts.\n\t * @api public\n\t */\n\t\n\tfunction JSONPPolling (opts) {\n\t  Polling.call(this, opts);\n\t\n\t  this.query = this.query || {};\n\t\n\t  // define global callbacks array if not present\n\t  // we do this here (lazily) to avoid unneeded global pollution\n\t  if (!callbacks) {\n\t    // we need to consider multiple engines in the same page\n\t    callbacks = globalThis.___eio = (globalThis.___eio || []);\n\t  }\n\t\n\t  // callback identifier\n\t  this.index = callbacks.length;\n\t\n\t  // add callback to jsonp global\n\t  var self = this;\n\t  callbacks.push(function (msg) {\n\t    self.onData(msg);\n\t  });\n\t\n\t  // append to query string\n\t  this.query.j = this.index;\n\t\n\t  // prevent spurious errors from being emitted when the window is unloaded\n\t  if (typeof addEventListener === 'function') {\n\t    addEventListener('beforeunload', function () {\n\t      if (self.script) self.script.onerror = empty;\n\t    }, false);\n\t  }\n\t}\n\t\n\t/**\n\t * Inherits from Polling.\n\t */\n\t\n\tinherit(JSONPPolling, Polling);\n\t\n\t/*\n\t * JSONP only supports binary as base64 encoded strings\n\t */\n\t\n\tJSONPPolling.prototype.supportsBinary = false;\n\t\n\t/**\n\t * Closes the socket.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doClose = function () {\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  if (this.form) {\n\t    this.form.parentNode.removeChild(this.form);\n\t    this.form = null;\n\t    this.iframe = null;\n\t  }\n\t\n\t  Polling.prototype.doClose.call(this);\n\t};\n\t\n\t/**\n\t * Starts a poll cycle.\n\t *\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doPoll = function () {\n\t  var self = this;\n\t  var script = document.createElement('script');\n\t\n\t  if (this.script) {\n\t    this.script.parentNode.removeChild(this.script);\n\t    this.script = null;\n\t  }\n\t\n\t  script.async = true;\n\t  script.src = this.uri();\n\t  script.onerror = function (e) {\n\t    self.onError('jsonp poll error', e);\n\t  };\n\t\n\t  var insertAt = document.getElementsByTagName('script')[0];\n\t  if (insertAt) {\n\t    insertAt.parentNode.insertBefore(script, insertAt);\n\t  } else {\n\t    (document.head || document.body).appendChild(script);\n\t  }\n\t  this.script = script;\n\t\n\t  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\t\n\t  if (isUAgecko) {\n\t    setTimeout(function () {\n\t      var iframe = document.createElement('iframe');\n\t      document.body.appendChild(iframe);\n\t      document.body.removeChild(iframe);\n\t    }, 100);\n\t  }\n\t};\n\t\n\t/**\n\t * Writes with a hidden iframe.\n\t *\n\t * @param {String} data to send\n\t * @param {Function} called upon flush.\n\t * @api private\n\t */\n\t\n\tJSONPPolling.prototype.doWrite = function (data, fn) {\n\t  var self = this;\n\t\n\t  if (!this.form) {\n\t    var form = document.createElement('form');\n\t    var area = document.createElement('textarea');\n\t    var id = this.iframeId = 'eio_iframe_' + this.index;\n\t    var iframe;\n\t\n\t    form.className = 'socketio';\n\t    form.style.position = 'absolute';\n\t    form.style.top = '-1000px';\n\t    form.style.left = '-1000px';\n\t    form.target = id;\n\t    form.method = 'POST';\n\t    form.setAttribute('accept-charset', 'utf-8');\n\t    area.name = 'd';\n\t    form.appendChild(area);\n\t    document.body.appendChild(form);\n\t\n\t    this.form = form;\n\t    this.area = area;\n\t  }\n\t\n\t  this.form.action = this.uri();\n\t\n\t  function complete () {\n\t    initIframe();\n\t    fn();\n\t  }\n\t\n\t  function initIframe () {\n\t    if (self.iframe) {\n\t      try {\n\t        self.form.removeChild(self.iframe);\n\t      } catch (e) {\n\t        self.onError('jsonp polling iframe removal error', e);\n\t      }\n\t    }\n\t\n\t    try {\n\t      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n\t      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n\t      iframe = document.createElement(html);\n\t    } catch (e) {\n\t      iframe = document.createElement('iframe');\n\t      iframe.name = self.iframeId;\n\t      iframe.src = 'javascript:0';\n\t    }\n\t\n\t    iframe.id = self.iframeId;\n\t\n\t    self.form.appendChild(iframe);\n\t    self.iframe = iframe;\n\t  }\n\t\n\t  initIframe();\n\t\n\t  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n\t  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n\t  data = data.replace(rEscapedNewline, '\\\\\\n');\n\t  this.area.value = data.replace(rNewline, '\\\\n');\n\t\n\t  try {\n\t    this.form.submit();\n\t  } catch (e) {}\n\t\n\t  if (this.iframe.attachEvent) {\n\t    this.iframe.onreadystatechange = function () {\n\t      if (self.iframe.readyState === 'complete') {\n\t        complete();\n\t      }\n\t    };\n\t  } else {\n\t    this.iframe.onload = complete;\n\t  }\n\t};\n\n\n/***/ }),\n/* 34 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar Transport = __webpack_require__(21);\n\tvar parser = __webpack_require__(22);\n\tvar parseqs = __webpack_require__(30);\n\tvar inherit = __webpack_require__(31);\n\tvar yeast = __webpack_require__(32);\n\tvar debug = __webpack_require__(3)('engine.io-client:websocket');\n\t\n\tvar BrowserWebSocket, NodeWebSocket;\n\t\n\tif (typeof WebSocket !== 'undefined') {\n\t  BrowserWebSocket = WebSocket;\n\t} else if (typeof self !== 'undefined') {\n\t  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n\t}\n\t\n\tif (typeof window === 'undefined') {\n\t  try {\n\t    NodeWebSocket = __webpack_require__(35);\n\t  } catch (e) { }\n\t}\n\t\n\t/**\n\t * Get either the `WebSocket` or `MozWebSocket` globals\n\t * in the browser or try to resolve WebSocket-compatible\n\t * interface exposed by `ws` for Node-like environment.\n\t */\n\t\n\tvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = WS;\n\t\n\t/**\n\t * WebSocket transport constructor.\n\t *\n\t * @api {Object} connection options\n\t * @api public\n\t */\n\t\n\tfunction WS (opts) {\n\t  var forceBase64 = (opts && opts.forceBase64);\n\t  if (forceBase64) {\n\t    this.supportsBinary = false;\n\t  }\n\t  this.perMessageDeflate = opts.perMessageDeflate;\n\t  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n\t  this.protocols = opts.protocols;\n\t  if (!this.usingBrowserWebSocket) {\n\t    WebSocketImpl = NodeWebSocket;\n\t  }\n\t  Transport.call(this, opts);\n\t}\n\t\n\t/**\n\t * Inherits from Transport.\n\t */\n\t\n\tinherit(WS, Transport);\n\t\n\t/**\n\t * Transport name.\n\t *\n\t * @api public\n\t */\n\t\n\tWS.prototype.name = 'websocket';\n\t\n\t/*\n\t * WebSockets support binary\n\t */\n\t\n\tWS.prototype.supportsBinary = true;\n\t\n\t/**\n\t * Opens socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doOpen = function () {\n\t  if (!this.check()) {\n\t    // let probe timeout\n\t    return;\n\t  }\n\t\n\t  var uri = this.uri();\n\t  var protocols = this.protocols;\n\t\n\t  var opts = {};\n\t\n\t  if (!this.isReactNative) {\n\t    opts.agent = this.agent;\n\t    opts.perMessageDeflate = this.perMessageDeflate;\n\t\n\t    // SSL options for Node.js client\n\t    opts.pfx = this.pfx;\n\t    opts.key = this.key;\n\t    opts.passphrase = this.passphrase;\n\t    opts.cert = this.cert;\n\t    opts.ca = this.ca;\n\t    opts.ciphers = this.ciphers;\n\t    opts.rejectUnauthorized = this.rejectUnauthorized;\n\t  }\n\t\n\t  if (this.extraHeaders) {\n\t    opts.headers = this.extraHeaders;\n\t  }\n\t  if (this.localAddress) {\n\t    opts.localAddress = this.localAddress;\n\t  }\n\t\n\t  try {\n\t    this.ws =\n\t      this.usingBrowserWebSocket && !this.isReactNative\n\t        ? protocols\n\t          ? new WebSocketImpl(uri, protocols)\n\t          : new WebSocketImpl(uri)\n\t        : new WebSocketImpl(uri, protocols, opts);\n\t  } catch (err) {\n\t    return this.emit('error', err);\n\t  }\n\t\n\t  if (this.ws.binaryType === undefined) {\n\t    this.supportsBinary = false;\n\t  }\n\t\n\t  if (this.ws.supports && this.ws.supports.binary) {\n\t    this.supportsBinary = true;\n\t    this.ws.binaryType = 'nodebuffer';\n\t  } else {\n\t    this.ws.binaryType = 'arraybuffer';\n\t  }\n\t\n\t  this.addEventListeners();\n\t};\n\t\n\t/**\n\t * Adds event listeners to the socket\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.addEventListeners = function () {\n\t  var self = this;\n\t\n\t  this.ws.onopen = function () {\n\t    self.onOpen();\n\t  };\n\t  this.ws.onclose = function () {\n\t    self.onClose();\n\t  };\n\t  this.ws.onmessage = function (ev) {\n\t    self.onData(ev.data);\n\t  };\n\t  this.ws.onerror = function (e) {\n\t    self.onError('websocket error', e);\n\t  };\n\t};\n\t\n\t/**\n\t * Writes data to socket.\n\t *\n\t * @param {Array} array of packets.\n\t * @api private\n\t */\n\t\n\tWS.prototype.write = function (packets) {\n\t  var self = this;\n\t  this.writable = false;\n\t\n\t  // encodePacket efficient as it uses WS framing\n\t  // no need for encodePayload\n\t  var total = packets.length;\n\t  for (var i = 0, l = total; i < l; i++) {\n\t    (function (packet) {\n\t      parser.encodePacket(packet, self.supportsBinary, function (data) {\n\t        if (!self.usingBrowserWebSocket) {\n\t          // always create a new object (GH-437)\n\t          var opts = {};\n\t          if (packet.options) {\n\t            opts.compress = packet.options.compress;\n\t          }\n\t\n\t          if (self.perMessageDeflate) {\n\t            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n\t            if (len < self.perMessageDeflate.threshold) {\n\t              opts.compress = false;\n\t            }\n\t          }\n\t        }\n\t\n\t        // Sometimes the websocket has already been closed but the browser didn't\n\t        // have a chance of informing us about it yet, in that case send will\n\t        // throw an error\n\t        try {\n\t          if (self.usingBrowserWebSocket) {\n\t            // TypeError is thrown when passing the second argument on Safari\n\t            self.ws.send(data);\n\t          } else {\n\t            self.ws.send(data, opts);\n\t          }\n\t        } catch (e) {\n\t          debug('websocket closed before onclose event');\n\t        }\n\t\n\t        --total || done();\n\t      });\n\t    })(packets[i]);\n\t  }\n\t\n\t  function done () {\n\t    self.emit('flush');\n\t\n\t    // fake drain\n\t    // defer to next tick to allow Socket to clear writeBuffer\n\t    setTimeout(function () {\n\t      self.writable = true;\n\t      self.emit('drain');\n\t    }, 0);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon close\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.onClose = function () {\n\t  Transport.prototype.onClose.call(this);\n\t};\n\t\n\t/**\n\t * Closes socket.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.doClose = function () {\n\t  if (typeof this.ws !== 'undefined') {\n\t    this.ws.close();\n\t  }\n\t};\n\t\n\t/**\n\t * Generates uri for connection.\n\t *\n\t * @api private\n\t */\n\t\n\tWS.prototype.uri = function () {\n\t  var query = this.query || {};\n\t  var schema = this.secure ? 'wss' : 'ws';\n\t  var port = '';\n\t\n\t  // avoid port if default for schema\n\t  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n\t    ('ws' === schema && Number(this.port) !== 80))) {\n\t    port = ':' + this.port;\n\t  }\n\t\n\t  // append timestamp to URI\n\t  if (this.timestampRequests) {\n\t    query[this.timestampParam] = yeast();\n\t  }\n\t\n\t  // communicate binary support capabilities\n\t  if (!this.supportsBinary) {\n\t    query.b64 = 1;\n\t  }\n\t\n\t  query = parseqs.encode(query);\n\t\n\t  // prepend ? to query\n\t  if (query.length) {\n\t    query = '?' + query;\n\t  }\n\t\n\t  var ipv6 = this.hostname.indexOf(':') !== -1;\n\t  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n\t};\n\t\n\t/**\n\t * Feature detection for WebSocket.\n\t *\n\t * @return {Boolean} whether this transport is available.\n\t * @api public\n\t */\n\t\n\tWS.prototype.check = function () {\n\t  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n\t};\n\n\n/***/ }),\n/* 35 */\n/***/ (function(module, exports) {\n\n\t/* (ignored) */\n\n/***/ }),\n/* 36 */\n/***/ (function(module, exports) {\n\n\t\n\tvar indexOf = [].indexOf;\n\t\n\tmodule.exports = function(arr, obj){\n\t  if (indexOf) return arr.indexOf(obj);\n\t  for (var i = 0; i < arr.length; ++i) {\n\t    if (arr[i] === obj) return i;\n\t  }\n\t  return -1;\n\t};\n\n/***/ }),\n/* 37 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\t\n\t/**\n\t * Module dependencies.\n\t */\n\t\n\tvar parser = __webpack_require__(7);\n\tvar Emitter = __webpack_require__(8);\n\tvar toArray = __webpack_require__(38);\n\tvar on = __webpack_require__(39);\n\tvar bind = __webpack_require__(40);\n\tvar debug = __webpack_require__(3)('socket.io-client:socket');\n\tvar parseqs = __webpack_require__(30);\n\tvar hasBin = __webpack_require__(24);\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = exports = Socket;\n\t\n\t/**\n\t * Internal events (blacklisted).\n\t * These events can't be emitted by the user.\n\t *\n\t * @api private\n\t */\n\t\n\tvar events = {\n\t  connect: 1,\n\t  connect_error: 1,\n\t  connect_timeout: 1,\n\t  connecting: 1,\n\t  disconnect: 1,\n\t  error: 1,\n\t  reconnect: 1,\n\t  reconnect_attempt: 1,\n\t  reconnect_failed: 1,\n\t  reconnect_error: 1,\n\t  reconnecting: 1,\n\t  ping: 1,\n\t  pong: 1\n\t};\n\t\n\t/**\n\t * Shortcut to `Emitter#emit`.\n\t */\n\t\n\tvar emit = Emitter.prototype.emit;\n\t\n\t/**\n\t * `Socket` constructor.\n\t *\n\t * @api public\n\t */\n\t\n\tfunction Socket (io, nsp, opts) {\n\t  this.io = io;\n\t  this.nsp = nsp;\n\t  this.json = this; // compat\n\t  this.ids = 0;\n\t  this.acks = {};\n\t  this.receiveBuffer = [];\n\t  this.sendBuffer = [];\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  this.flags = {};\n\t  if (opts && opts.query) {\n\t    this.query = opts.query;\n\t  }\n\t  if (this.io.autoConnect) this.open();\n\t}\n\t\n\t/**\n\t * Mix in `Emitter`.\n\t */\n\t\n\tEmitter(Socket.prototype);\n\t\n\t/**\n\t * Subscribe to open, close and packet events\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.subEvents = function () {\n\t  if (this.subs) return;\n\t\n\t  var io = this.io;\n\t  this.subs = [\n\t    on(io, 'open', bind(this, 'onopen')),\n\t    on(io, 'packet', bind(this, 'onpacket')),\n\t    on(io, 'close', bind(this, 'onclose'))\n\t  ];\n\t};\n\t\n\t/**\n\t * \"Opens\" the socket.\n\t *\n\t * @api public\n\t */\n\t\n\tSocket.prototype.open =\n\tSocket.prototype.connect = function () {\n\t  if (this.connected) return this;\n\t\n\t  this.subEvents();\n\t  if (!this.io.reconnecting) this.io.open(); // ensure open\n\t  if ('open' === this.io.readyState) this.onopen();\n\t  this.emit('connecting');\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a `message` event.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.send = function () {\n\t  var args = toArray(arguments);\n\t  args.unshift('message');\n\t  this.emit.apply(this, args);\n\t  return this;\n\t};\n\t\n\t/**\n\t * Override `emit`.\n\t * If the event is in `events`, it's emitted normally.\n\t *\n\t * @param {String} event name\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.emit = function (ev) {\n\t  if (events.hasOwnProperty(ev)) {\n\t    emit.apply(this, arguments);\n\t    return this;\n\t  }\n\t\n\t  var args = toArray(arguments);\n\t  var packet = {\n\t    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n\t    data: args\n\t  };\n\t\n\t  packet.options = {};\n\t  packet.options.compress = !this.flags || false !== this.flags.compress;\n\t\n\t  // event ack callback\n\t  if ('function' === typeof args[args.length - 1]) {\n\t    debug('emitting packet with ack id %d', this.ids);\n\t    this.acks[this.ids] = args.pop();\n\t    packet.id = this.ids++;\n\t  }\n\t\n\t  if (this.connected) {\n\t    this.packet(packet);\n\t  } else {\n\t    this.sendBuffer.push(packet);\n\t  }\n\t\n\t  this.flags = {};\n\t\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sends a packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.packet = function (packet) {\n\t  packet.nsp = this.nsp;\n\t  this.io.packet(packet);\n\t};\n\t\n\t/**\n\t * Called upon engine `open`.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onopen = function () {\n\t  debug('transport is open - connecting');\n\t\n\t  // write connect packet if necessary\n\t  if ('/' !== this.nsp) {\n\t    if (this.query) {\n\t      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n\t      debug('sending connect packet with query %s', query);\n\t      this.packet({type: parser.CONNECT, query: query});\n\t    } else {\n\t      this.packet({type: parser.CONNECT});\n\t    }\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon engine `close`.\n\t *\n\t * @param {String} reason\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onclose = function (reason) {\n\t  debug('close (%s)', reason);\n\t  this.connected = false;\n\t  this.disconnected = true;\n\t  delete this.id;\n\t  this.emit('disconnect', reason);\n\t};\n\t\n\t/**\n\t * Called with socket packet.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onpacket = function (packet) {\n\t  var sameNamespace = packet.nsp === this.nsp;\n\t  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\t\n\t  if (!sameNamespace && !rootNamespaceError) return;\n\t\n\t  switch (packet.type) {\n\t    case parser.CONNECT:\n\t      this.onconnect();\n\t      break;\n\t\n\t    case parser.EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.BINARY_EVENT:\n\t      this.onevent(packet);\n\t      break;\n\t\n\t    case parser.ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.BINARY_ACK:\n\t      this.onack(packet);\n\t      break;\n\t\n\t    case parser.DISCONNECT:\n\t      this.ondisconnect();\n\t      break;\n\t\n\t    case parser.ERROR:\n\t      this.emit('error', packet.data);\n\t      break;\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon a server event.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onevent = function (packet) {\n\t  var args = packet.data || [];\n\t  debug('emitting event %j', args);\n\t\n\t  if (null != packet.id) {\n\t    debug('attaching ack callback to event');\n\t    args.push(this.ack(packet.id));\n\t  }\n\t\n\t  if (this.connected) {\n\t    emit.apply(this, args);\n\t  } else {\n\t    this.receiveBuffer.push(args);\n\t  }\n\t};\n\t\n\t/**\n\t * Produces an ack callback to emit with an event.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ack = function (id) {\n\t  var self = this;\n\t  var sent = false;\n\t  return function () {\n\t    // prevent double callbacks\n\t    if (sent) return;\n\t    sent = true;\n\t    var args = toArray(arguments);\n\t    debug('sending ack %j', args);\n\t\n\t    self.packet({\n\t      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n\t      id: id,\n\t      data: args\n\t    });\n\t  };\n\t};\n\t\n\t/**\n\t * Called upon a server acknowlegement.\n\t *\n\t * @param {Object} packet\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onack = function (packet) {\n\t  var ack = this.acks[packet.id];\n\t  if ('function' === typeof ack) {\n\t    debug('calling ack %s with %j', packet.id, packet.data);\n\t    ack.apply(this, packet.data);\n\t    delete this.acks[packet.id];\n\t  } else {\n\t    debug('bad ack %s', packet.id);\n\t  }\n\t};\n\t\n\t/**\n\t * Called upon server connect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.onconnect = function () {\n\t  this.connected = true;\n\t  this.disconnected = false;\n\t  this.emit('connect');\n\t  this.emitBuffered();\n\t};\n\t\n\t/**\n\t * Emit buffered events (received and emitted).\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.emitBuffered = function () {\n\t  var i;\n\t  for (i = 0; i < this.receiveBuffer.length; i++) {\n\t    emit.apply(this, this.receiveBuffer[i]);\n\t  }\n\t  this.receiveBuffer = [];\n\t\n\t  for (i = 0; i < this.sendBuffer.length; i++) {\n\t    this.packet(this.sendBuffer[i]);\n\t  }\n\t  this.sendBuffer = [];\n\t};\n\t\n\t/**\n\t * Called upon server disconnect.\n\t *\n\t * @api private\n\t */\n\t\n\tSocket.prototype.ondisconnect = function () {\n\t  debug('server disconnect (%s)', this.nsp);\n\t  this.destroy();\n\t  this.onclose('io server disconnect');\n\t};\n\t\n\t/**\n\t * Called upon forced client/server side disconnections,\n\t * this method ensures the manager stops tracking us and\n\t * that reconnections don't get triggered for this.\n\t *\n\t * @api private.\n\t */\n\t\n\tSocket.prototype.destroy = function () {\n\t  if (this.subs) {\n\t    // clean subscriptions to avoid reconnections\n\t    for (var i = 0; i < this.subs.length; i++) {\n\t      this.subs[i].destroy();\n\t    }\n\t    this.subs = null;\n\t  }\n\t\n\t  this.io.destroy(this);\n\t};\n\t\n\t/**\n\t * Disconnects the socket manually.\n\t *\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.close =\n\tSocket.prototype.disconnect = function () {\n\t  if (this.connected) {\n\t    debug('performing disconnect (%s)', this.nsp);\n\t    this.packet({ type: parser.DISCONNECT });\n\t  }\n\t\n\t  // remove socket from pool\n\t  this.destroy();\n\t\n\t  if (this.connected) {\n\t    // fire events\n\t    this.onclose('io client disconnect');\n\t  }\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the compress flag.\n\t *\n\t * @param {Boolean} if `true`, compresses the sending data\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.compress = function (compress) {\n\t  this.flags.compress = compress;\n\t  return this;\n\t};\n\t\n\t/**\n\t * Sets the binary flag\n\t *\n\t * @param {Boolean} whether the emitted data contains binary\n\t * @return {Socket} self\n\t * @api public\n\t */\n\t\n\tSocket.prototype.binary = function (binary) {\n\t  this.flags.binary = binary;\n\t  return this;\n\t};\n\n\n/***/ }),\n/* 38 */\n/***/ (function(module, exports) {\n\n\tmodule.exports = toArray\n\t\n\tfunction toArray(list, index) {\n\t    var array = []\n\t\n\t    index = index || 0\n\t\n\t    for (var i = index || 0; i < list.length; i++) {\n\t        array[i - index] = list[i]\n\t    }\n\t\n\t    return array\n\t}\n\n\n/***/ }),\n/* 39 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Module exports.\n\t */\n\t\n\tmodule.exports = on;\n\t\n\t/**\n\t * Helper for subscriptions.\n\t *\n\t * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n\t * @param {String} event name\n\t * @param {Function} callback\n\t * @api public\n\t */\n\t\n\tfunction on (obj, ev, fn) {\n\t  obj.on(ev, fn);\n\t  return {\n\t    destroy: function () {\n\t      obj.removeListener(ev, fn);\n\t    }\n\t  };\n\t}\n\n\n/***/ }),\n/* 40 */\n/***/ (function(module, exports) {\n\n\t/**\n\t * Slice reference.\n\t */\n\t\n\tvar slice = [].slice;\n\t\n\t/**\n\t * Bind `obj` to `fn`.\n\t *\n\t * @param {Object} obj\n\t * @param {Function|String} fn or string\n\t * @return {Function}\n\t * @api public\n\t */\n\t\n\tmodule.exports = function(obj, fn){\n\t  if ('string' == typeof fn) fn = obj[fn];\n\t  if ('function' != typeof fn) throw new Error('bind() requires a function');\n\t  var args = slice.call(arguments, 2);\n\t  return function(){\n\t    return fn.apply(obj, args.concat(slice.call(arguments)));\n\t  }\n\t};\n\n\n/***/ }),\n/* 41 */\n/***/ (function(module, exports) {\n\n\t\n\t/**\n\t * Expose `Backoff`.\n\t */\n\t\n\tmodule.exports = Backoff;\n\t\n\t/**\n\t * Initialize backoff timer with `opts`.\n\t *\n\t * - `min` initial timeout in milliseconds [100]\n\t * - `max` max timeout [10000]\n\t * - `jitter` [0]\n\t * - `factor` [2]\n\t *\n\t * @param {Object} opts\n\t * @api public\n\t */\n\t\n\tfunction Backoff(opts) {\n\t  opts = opts || {};\n\t  this.ms = opts.min || 100;\n\t  this.max = opts.max || 10000;\n\t  this.factor = opts.factor || 2;\n\t  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n\t  this.attempts = 0;\n\t}\n\t\n\t/**\n\t * Return the backoff duration.\n\t *\n\t * @return {Number}\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.duration = function(){\n\t  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n\t  if (this.jitter) {\n\t    var rand =  Math.random();\n\t    var deviation = Math.floor(rand * this.jitter * ms);\n\t    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n\t  }\n\t  return Math.min(ms, this.max) | 0;\n\t};\n\t\n\t/**\n\t * Reset the number of attempts.\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.reset = function(){\n\t  this.attempts = 0;\n\t};\n\t\n\t/**\n\t * Set the minimum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMin = function(min){\n\t  this.ms = min;\n\t};\n\t\n\t/**\n\t * Set the maximum duration\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setMax = function(max){\n\t  this.max = max;\n\t};\n\t\n\t/**\n\t * Set the jitter\n\t *\n\t * @api public\n\t */\n\t\n\tBackoff.prototype.setJitter = function(jitter){\n\t  this.jitter = jitter;\n\t};\n\t\n\n\n/***/ })\n/******/ ])\n});\n;\n\n\n// WEBPACK FOOTER //\n// socket.io.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 3f4094f74bfa6df9f01a", "\n/**\n * Module dependencies.\n */\n\nvar url = require('./url');\nvar parser = require('socket.io-parser');\nvar Manager = require('./manager');\nvar debug = require('debug')('socket.io-client');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = lookup;\n\n/**\n * Managers cache.\n */\n\nvar cache = exports.managers = {};\n\n/**\n * Looks up an existing `Manager` for multiplexing.\n * If the user summons:\n *\n *   `io('http://localhost/a');`\n *   `io('http://localhost/b');`\n *\n * We reuse the existing instance based on same scheme/port/host,\n * and we initialize sockets for each namespace.\n *\n * @api public\n */\n\nfunction lookup (uri, opts) {\n  if (typeof uri === 'object') {\n    opts = uri;\n    uri = undefined;\n  }\n\n  opts = opts || {};\n\n  var parsed = url(uri);\n  var source = parsed.source;\n  var id = parsed.id;\n  var path = parsed.path;\n  var sameNamespace = cache[id] && path in cache[id].nsps;\n  var newConnection = opts.forceNew || opts['force new connection'] ||\n                      false === opts.multiplex || sameNamespace;\n\n  var io;\n\n  if (newConnection) {\n    debug('ignoring socket cache for %s', source);\n    io = Manager(source, opts);\n  } else {\n    if (!cache[id]) {\n      debug('new io instance for %s', source);\n      cache[id] = Manager(source, opts);\n    }\n    io = cache[id];\n  }\n  if (parsed.query && !opts.query) {\n    opts.query = parsed.query;\n  }\n  return io.socket(parsed.path, opts);\n}\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = parser.protocol;\n\n/**\n * `connect`.\n *\n * @param {String} uri\n * @api public\n */\n\nexports.connect = lookup;\n\n/**\n * Expose constructors for standalone build.\n *\n * @api public\n */\n\nexports.Manager = require('./manager');\nexports.Socket = require('./socket');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/index.js\n// module id = 0\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parseuri = require('parseuri');\nvar debug = require('debug')('socket.io-client:url');\n\n/**\n * Module exports.\n */\n\nmodule.exports = url;\n\n/**\n * URL parser.\n *\n * @param {String} url\n * @param {Object} An object meant to mimic window.location.\n *                 Defaults to window.location.\n * @api public\n */\n\nfunction url (uri, loc) {\n  var obj = uri;\n\n  // default to window.location\n  loc = loc || (typeof location !== 'undefined' && location);\n  if (null == uri) uri = loc.protocol + '//' + loc.host;\n\n  // relative path support\n  if ('string' === typeof uri) {\n    if ('/' === uri.charAt(0)) {\n      if ('/' === uri.charAt(1)) {\n        uri = loc.protocol + uri;\n      } else {\n        uri = loc.host + uri;\n      }\n    }\n\n    if (!/^(https?|wss?):\\/\\//.test(uri)) {\n      debug('protocol-less url %s', uri);\n      if ('undefined' !== typeof loc) {\n        uri = loc.protocol + '//' + uri;\n      } else {\n        uri = 'https://' + uri;\n      }\n    }\n\n    // parse\n    debug('parse %s', uri);\n    obj = parseuri(uri);\n  }\n\n  // make sure we treat `localhost:80` and `localhost` equally\n  if (!obj.port) {\n    if (/^(http|ws)$/.test(obj.protocol)) {\n      obj.port = '80';\n    } else if (/^(http|ws)s$/.test(obj.protocol)) {\n      obj.port = '443';\n    }\n  }\n\n  obj.path = obj.path || '/';\n\n  var ipv6 = obj.host.indexOf(':') !== -1;\n  var host = ipv6 ? '[' + obj.host + ']' : obj.host;\n\n  // define unique id\n  obj.id = obj.protocol + '://' + host + ':' + obj.port;\n  // define href\n  obj.href = obj.protocol + '://' + host + (loc && loc.port === obj.port ? '' : (':' + obj.port));\n\n  return obj;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/url.js\n// module id = 1\n// module chunks = 0", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseuri/index.js\n// module id = 2\n// module chunks = 0", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  '#0000CC', '#0000FF', '#0033CC', '#0033FF', '#0066CC', '#0066FF', '#0099CC',\n  '#0099FF', '#00CC00', '#00CC33', '#00CC66', '#00CC99', '#00CCCC', '#00CCFF',\n  '#3300CC', '#3300FF', '#3333CC', '#3333FF', '#3366CC', '#3366FF', '#3399CC',\n  '#3399FF', '#33CC00', '#33CC33', '#33CC66', '#33CC99', '#33CCCC', '#33CCFF',\n  '#6600CC', '#6600FF', '#6633CC', '#6633FF', '#66CC00', '#66CC33', '#9900CC',\n  '#9900FF', '#9933CC', '#9933FF', '#99CC00', '#99CC33', '#CC0000', '#CC0033',\n  '#CC0066', '#CC0099', '#CC00CC', '#CC00FF', '#CC3300', '#CC3333', '#CC3366',\n  '#CC3399', '#CC33CC', '#CC33FF', '#CC6600', '#CC6633', '#CC9900', '#CC9933',\n  '#CCCC00', '#CCCC33', '#FF0000', '#FF0033', '#FF0066', '#FF0099', '#FF00CC',\n  '#FF00FF', '#FF3300', '#FF3333', '#FF3366', '#FF3399', '#FF33CC', '#FF33FF',\n  '#FF6600', '#FF6633', '#FF9900', '#FF9933', '#FFCC00', '#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // Internet Explorer and Edge do not support colors.\n  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n    return false;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n\n\n\n// WEBPACK FOOTER //\n// ./~/debug/src/browser.js", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/process/browser.js\n// module id = 4\n// module chunks = 0", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * Active `debug` instances.\n */\nexports.instances = [];\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  var prevTime;\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n  debug.destroy = destroy;\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  exports.instances.push(debug);\n\n  return debug;\n}\n\nfunction destroy () {\n  var index = exports.instances.indexOf(this);\n  if (index !== -1) {\n    exports.instances.splice(index, 1);\n    return true;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var i;\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n\n  for (i = 0; i < exports.instances.length; i++) {\n    var instance = exports.instances[i];\n    instance.enabled = exports.enabled(instance.namespace);\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  if (name[name.length - 1] === '*') {\n    return true;\n  }\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n\n\n\n// WEBPACK FOOTER //\n// ./~/debug/src/debug.js", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/ms/index.js\n// module id = 6\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar debug = require('debug')('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar binary = require('./binary');\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nexports.protocol = 4;\n\n/**\n * Packet types.\n *\n * @api public\n */\n\nexports.types = [\n  'CONNECT',\n  'DISCONNECT',\n  'EVENT',\n  'ACK',\n  'ERROR',\n  'BINARY_EVENT',\n  'BINARY_ACK'\n];\n\n/**\n * Packet type `connect`.\n *\n * @api public\n */\n\nexports.CONNECT = 0;\n\n/**\n * Packet type `disconnect`.\n *\n * @api public\n */\n\nexports.DISCONNECT = 1;\n\n/**\n * Packet type `event`.\n *\n * @api public\n */\n\nexports.EVENT = 2;\n\n/**\n * Packet type `ack`.\n *\n * @api public\n */\n\nexports.ACK = 3;\n\n/**\n * Packet type `error`.\n *\n * @api public\n */\n\nexports.ERROR = 4;\n\n/**\n * Packet type 'binary event'\n *\n * @api public\n */\n\nexports.BINARY_EVENT = 5;\n\n/**\n * Packet type `binary ack`. For acks with binary arguments.\n *\n * @api public\n */\n\nexports.BINARY_ACK = 6;\n\n/**\n * Encoder constructor.\n *\n * @api public\n */\n\nexports.Encoder = Encoder;\n\n/**\n * Decoder constructor.\n *\n * @api public\n */\n\nexports.Decoder = Decoder;\n\n/**\n * A socket.io Encoder instance\n *\n * @api public\n */\n\nfunction Encoder() {}\n\nvar ERROR_PACKET = exports.ERROR + '\"encode error\"';\n\n/**\n * Encode a packet as a single string if non-binary, or as a\n * buffer sequence, depending on packet type.\n *\n * @param {Object} obj - packet object\n * @param {Function} callback - function to handle encodings (likely engine.write)\n * @return Calls callback with Array of encodings\n * @api public\n */\n\nEncoder.prototype.encode = function(obj, callback){\n  debug('encoding packet %j', obj);\n\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    encodeAsBinary(obj, callback);\n  } else {\n    var encoding = encodeAsString(obj);\n    callback([encoding]);\n  }\n};\n\n/**\n * Encode packet as string.\n *\n * @param {Object} packet\n * @return {String} encoded\n * @api private\n */\n\nfunction encodeAsString(obj) {\n\n  // first is type\n  var str = '' + obj.type;\n\n  // attachments if we have them\n  if (exports.BINARY_EVENT === obj.type || exports.BINARY_ACK === obj.type) {\n    str += obj.attachments + '-';\n  }\n\n  // if we have a namespace other than `/`\n  // we append it followed by a comma `,`\n  if (obj.nsp && '/' !== obj.nsp) {\n    str += obj.nsp + ',';\n  }\n\n  // immediately followed by the id\n  if (null != obj.id) {\n    str += obj.id;\n  }\n\n  // json data\n  if (null != obj.data) {\n    var payload = tryStringify(obj.data);\n    if (payload !== false) {\n      str += payload;\n    } else {\n      return ERROR_PACKET;\n    }\n  }\n\n  debug('encoded %j as %s', obj, str);\n  return str;\n}\n\nfunction tryStringify(str) {\n  try {\n    return JSON.stringify(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Encode packet as 'buffer sequence' by removing blobs, and\n * deconstructing packet into object with placeholders and\n * a list of buffers.\n *\n * @param {Object} packet\n * @return {Buffer} encoded\n * @api private\n */\n\nfunction encodeAsBinary(obj, callback) {\n\n  function writeEncoding(bloblessData) {\n    var deconstruction = binary.deconstructPacket(bloblessData);\n    var pack = encodeAsString(deconstruction.packet);\n    var buffers = deconstruction.buffers;\n\n    buffers.unshift(pack); // add packet info to beginning of data list\n    callback(buffers); // write all the buffers\n  }\n\n  binary.removeBlobs(obj, writeEncoding);\n}\n\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n * @api public\n */\n\nfunction Decoder() {\n  this.reconstructor = null;\n}\n\n/**\n * Mix in `Emitter` with Decoder.\n */\n\nEmitter(Decoder.prototype);\n\n/**\n * Decodes an encoded packet string into packet JSON.\n *\n * @param {String} obj - encoded packet\n * @return {Object} packet\n * @api public\n */\n\nDecoder.prototype.add = function(obj) {\n  var packet;\n  if (typeof obj === 'string') {\n    packet = decodeString(obj);\n    if (exports.BINARY_EVENT === packet.type || exports.BINARY_ACK === packet.type) { // binary packet's json\n      this.reconstructor = new BinaryReconstructor(packet);\n\n      // no attachments, labeled binary but no binary data to follow\n      if (this.reconstructor.reconPack.attachments === 0) {\n        this.emit('decoded', packet);\n      }\n    } else { // non-binary full packet\n      this.emit('decoded', packet);\n    }\n  } else if (isBuf(obj) || obj.base64) { // raw binary data\n    if (!this.reconstructor) {\n      throw new Error('got binary data when not reconstructing a packet');\n    } else {\n      packet = this.reconstructor.takeBinaryData(obj);\n      if (packet) { // received final buffer\n        this.reconstructor = null;\n        this.emit('decoded', packet);\n      }\n    }\n  } else {\n    throw new Error('Unknown type: ' + obj);\n  }\n};\n\n/**\n * Decode a packet String (JSON data)\n *\n * @param {String} str\n * @return {Object} packet\n * @api private\n */\n\nfunction decodeString(str) {\n  var i = 0;\n  // look up type\n  var p = {\n    type: Number(str.charAt(0))\n  };\n\n  if (null == exports.types[p.type]) {\n    return error('unknown packet type ' + p.type);\n  }\n\n  // look up attachments if type binary\n  if (exports.BINARY_EVENT === p.type || exports.BINARY_ACK === p.type) {\n    var buf = '';\n    while (str.charAt(++i) !== '-') {\n      buf += str.charAt(i);\n      if (i == str.length) break;\n    }\n    if (buf != Number(buf) || str.charAt(i) !== '-') {\n      throw new Error('Illegal attachments');\n    }\n    p.attachments = Number(buf);\n  }\n\n  // look up namespace (if any)\n  if ('/' === str.charAt(i + 1)) {\n    p.nsp = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (',' === c) break;\n      p.nsp += c;\n      if (i === str.length) break;\n    }\n  } else {\n    p.nsp = '/';\n  }\n\n  // look up id\n  var next = str.charAt(i + 1);\n  if ('' !== next && Number(next) == next) {\n    p.id = '';\n    while (++i) {\n      var c = str.charAt(i);\n      if (null == c || Number(c) != c) {\n        --i;\n        break;\n      }\n      p.id += str.charAt(i);\n      if (i === str.length) break;\n    }\n    p.id = Number(p.id);\n  }\n\n  // look up json data\n  if (str.charAt(++i)) {\n    var payload = tryParse(str.substr(i));\n    var isPayloadValid = payload !== false && (p.type === exports.ERROR || isArray(payload));\n    if (isPayloadValid) {\n      p.data = payload;\n    } else {\n      return error('invalid payload');\n    }\n  }\n\n  debug('decoded %s as %j', str, p);\n  return p;\n}\n\nfunction tryParse(str) {\n  try {\n    return JSON.parse(str);\n  } catch(e){\n    return false;\n  }\n}\n\n/**\n * Deallocates a parser's resources\n *\n * @api public\n */\n\nDecoder.prototype.destroy = function() {\n  if (this.reconstructor) {\n    this.reconstructor.finishedReconstruction();\n  }\n};\n\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n * @api private\n */\n\nfunction BinaryReconstructor(packet) {\n  this.reconPack = packet;\n  this.buffers = [];\n}\n\n/**\n * Method to be called when binary data received from connection\n * after a BINARY_EVENT packet.\n *\n * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n * @return {null | Object} returns null if more binary data is expected or\n *   a reconstructed packet object if all buffers have been received.\n * @api private\n */\n\nBinaryReconstructor.prototype.takeBinaryData = function(binData) {\n  this.buffers.push(binData);\n  if (this.buffers.length === this.reconPack.attachments) { // done with buffer list\n    var packet = binary.reconstructPacket(this.reconPack, this.buffers);\n    this.finishedReconstruction();\n    return packet;\n  }\n  return null;\n};\n\n/**\n * Cleans up binary packet reconstruction variables.\n *\n * @api private\n */\n\nBinaryReconstructor.prototype.finishedReconstruction = function() {\n  this.reconPack = null;\n  this.buffers = [];\n};\n\nfunction error(msg) {\n  return {\n    type: exports.ERROR,\n    data: 'parser error: ' + msg\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/index.js\n// module id = 7\n// module chunks = 0", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-emitter/index.js\n// module id = 8\n// module chunks = 0", "/*global Blob,File*/\n\n/**\n * Module requirements\n */\n\nvar isArray = require('isarray');\nvar isBuf = require('./is-buffer');\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' || (typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]');\nvar withNativeFile = typeof File === 'function' || (typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]');\n\n/**\n * Replaces every Buffer | ArrayBuffer in packet with a numbered placeholder.\n * Anything with blobs or files should be fed through removeBlobs before coming\n * here.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @api public\n */\n\nexports.deconstructPacket = function(packet) {\n  var buffers = [];\n  var packetData = packet.data;\n  var pack = packet;\n  pack.data = _deconstructPacket(packetData, buffers);\n  pack.attachments = buffers.length; // number of binary 'attachments'\n  return {packet: pack, buffers: buffers};\n};\n\nfunction _deconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (isBuf(data)) {\n    var placeholder = { _placeholder: true, num: buffers.length };\n    buffers.push(data);\n    return placeholder;\n  } else if (isArray(data)) {\n    var newData = new Array(data.length);\n    for (var i = 0; i < data.length; i++) {\n      newData[i] = _deconstructPacket(data[i], buffers);\n    }\n    return newData;\n  } else if (typeof data === 'object' && !(data instanceof Date)) {\n    var newData = {};\n    for (var key in data) {\n      newData[key] = _deconstructPacket(data[key], buffers);\n    }\n    return newData;\n  }\n  return data;\n}\n\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @api public\n */\n\nexports.reconstructPacket = function(packet, buffers) {\n  packet.data = _reconstructPacket(packet.data, buffers);\n  packet.attachments = undefined; // no longer useful\n  return packet;\n};\n\nfunction _reconstructPacket(data, buffers) {\n  if (!data) return data;\n\n  if (data && data._placeholder) {\n    return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n  } else if (isArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      data[i] = _reconstructPacket(data[i], buffers);\n    }\n  } else if (typeof data === 'object') {\n    for (var key in data) {\n      data[key] = _reconstructPacket(data[key], buffers);\n    }\n  }\n\n  return data;\n}\n\n/**\n * Asynchronously removes Blobs or Files from data via\n * FileReader's readAsArrayBuffer method. Used before encoding\n * data as msgpack. Calls callback with the blobless data.\n *\n * @param {Object} data\n * @param {Function} callback\n * @api private\n */\n\nexports.removeBlobs = function(data, callback) {\n  function _removeBlobs(obj, curKey, containingObject) {\n    if (!obj) return obj;\n\n    // convert any blob\n    if ((withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File)) {\n      pendingBlobs++;\n\n      // async filereader\n      var fileReader = new FileReader();\n      fileReader.onload = function() { // this.result == arraybuffer\n        if (containingObject) {\n          containingObject[curKey] = this.result;\n        }\n        else {\n          bloblessData = this.result;\n        }\n\n        // if nothing pending its callback time\n        if(! --pendingBlobs) {\n          callback(bloblessData);\n        }\n      };\n\n      fileReader.readAsArrayBuffer(obj); // blob -> arraybuffer\n    } else if (isArray(obj)) { // handle array\n      for (var i = 0; i < obj.length; i++) {\n        _removeBlobs(obj[i], i, obj);\n      }\n    } else if (typeof obj === 'object' && !isBuf(obj)) { // and object\n      for (var key in obj) {\n        _removeBlobs(obj[key], key, obj);\n      }\n    }\n  }\n\n  var pendingBlobs = 0;\n  var bloblessData = data;\n  _removeBlobs(bloblessData);\n  if (!pendingBlobs) {\n    callback(bloblessData);\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/binary.js\n// module id = 9\n// module chunks = 0", "var toString = {}.toString;\n\nmodule.exports = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/isarray/index.js\n// module id = 10\n// module chunks = 0", "\nmodule.exports = isBuf;\n\nvar withNativeBuffer = typeof Buffer === 'function' && typeof Buffer.isBuffer === 'function';\nvar withNativeArrayBuffer = typeof ArrayBuffer === 'function';\n\nvar isView = function (obj) {\n  return typeof ArrayBuffer.isView === 'function' ? ArrayBuffer.isView(obj) : (obj.buffer instanceof ArrayBuffer);\n};\n\n/**\n * Returns true if obj is a buffer or an arraybuffer.\n *\n * @api private\n */\n\nfunction isBuf(obj) {\n  return (withNativeBuffer && Buffer.isBuffer(obj)) ||\n          (withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj)));\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/socket.io-parser/is-buffer.js\n// module id = 11\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar eio = require('engine.io-client');\nvar Socket = require('./socket');\nvar Emitter = require('component-emitter');\nvar parser = require('socket.io-parser');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:manager');\nvar indexOf = require('indexof');\nvar Backoff = require('backo2');\n\n/**\n * IE6+ hasOwnProperty\n */\n\nvar has = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports\n */\n\nmodule.exports = Manager;\n\n/**\n * `Manager` constructor.\n *\n * @param {String} engine instance or engine uri/opts\n * @param {Object} options\n * @api public\n */\n\nfunction Manager (uri, opts) {\n  if (!(this instanceof Manager)) return new Manager(uri, opts);\n  if (uri && ('object' === typeof uri)) {\n    opts = uri;\n    uri = undefined;\n  }\n  opts = opts || {};\n\n  opts.path = opts.path || '/socket.io';\n  this.nsps = {};\n  this.subs = [];\n  this.opts = opts;\n  this.reconnection(opts.reconnection !== false);\n  this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n  this.reconnectionDelay(opts.reconnectionDelay || 1000);\n  this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n  this.randomizationFactor(opts.randomizationFactor || 0.5);\n  this.backoff = new Backoff({\n    min: this.reconnectionDelay(),\n    max: this.reconnectionDelayMax(),\n    jitter: this.randomizationFactor()\n  });\n  this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n  this.readyState = 'closed';\n  this.uri = uri;\n  this.connecting = [];\n  this.lastPing = null;\n  this.encoding = false;\n  this.packetBuffer = [];\n  var _parser = opts.parser || parser;\n  this.encoder = new _parser.Encoder();\n  this.decoder = new _parser.Decoder();\n  this.autoConnect = opts.autoConnect !== false;\n  if (this.autoConnect) this.open();\n}\n\n/**\n * Propagate given event to sockets and emit on `this`\n *\n * @api private\n */\n\nManager.prototype.emitAll = function () {\n  this.emit.apply(this, arguments);\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].emit.apply(this.nsps[nsp], arguments);\n    }\n  }\n};\n\n/**\n * Update `socket.id` of all sockets\n *\n * @api private\n */\n\nManager.prototype.updateSocketIds = function () {\n  for (var nsp in this.nsps) {\n    if (has.call(this.nsps, nsp)) {\n      this.nsps[nsp].id = this.generateId(nsp);\n    }\n  }\n};\n\n/**\n * generate `socket.id` for the given `nsp`\n *\n * @param {String} nsp\n * @return {String}\n * @api private\n */\n\nManager.prototype.generateId = function (nsp) {\n  return (nsp === '/' ? '' : (nsp + '#')) + this.engine.id;\n};\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Manager.prototype);\n\n/**\n * Sets the `reconnection` config.\n *\n * @param {Boolean} true/false if it should automatically reconnect\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnection = function (v) {\n  if (!arguments.length) return this._reconnection;\n  this._reconnection = !!v;\n  return this;\n};\n\n/**\n * Sets the reconnection attempts config.\n *\n * @param {Number} max reconnection attempts before giving up\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionAttempts = function (v) {\n  if (!arguments.length) return this._reconnectionAttempts;\n  this._reconnectionAttempts = v;\n  return this;\n};\n\n/**\n * Sets the delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelay = function (v) {\n  if (!arguments.length) return this._reconnectionDelay;\n  this._reconnectionDelay = v;\n  this.backoff && this.backoff.setMin(v);\n  return this;\n};\n\nManager.prototype.randomizationFactor = function (v) {\n  if (!arguments.length) return this._randomizationFactor;\n  this._randomizationFactor = v;\n  this.backoff && this.backoff.setJitter(v);\n  return this;\n};\n\n/**\n * Sets the maximum delay between reconnections.\n *\n * @param {Number} delay\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.reconnectionDelayMax = function (v) {\n  if (!arguments.length) return this._reconnectionDelayMax;\n  this._reconnectionDelayMax = v;\n  this.backoff && this.backoff.setMax(v);\n  return this;\n};\n\n/**\n * Sets the connection timeout. `false` to disable\n *\n * @return {Manager} self or value\n * @api public\n */\n\nManager.prototype.timeout = function (v) {\n  if (!arguments.length) return this._timeout;\n  this._timeout = v;\n  return this;\n};\n\n/**\n * Starts trying to reconnect if reconnection is enabled and we have not\n * started reconnecting yet\n *\n * @api private\n */\n\nManager.prototype.maybeReconnectOnOpen = function () {\n  // Only try to reconnect if it's the first time we're connecting\n  if (!this.reconnecting && this._reconnection && this.backoff.attempts === 0) {\n    // keeps reconnection from firing twice for the same reconnection loop\n    this.reconnect();\n  }\n};\n\n/**\n * Sets the current transport `socket`.\n *\n * @param {Function} optional, callback\n * @return {Manager} self\n * @api public\n */\n\nManager.prototype.open =\nManager.prototype.connect = function (fn, opts) {\n  debug('readyState %s', this.readyState);\n  if (~this.readyState.indexOf('open')) return this;\n\n  debug('opening %s', this.uri);\n  this.engine = eio(this.uri, this.opts);\n  var socket = this.engine;\n  var self = this;\n  this.readyState = 'opening';\n  this.skipReconnect = false;\n\n  // emit `open`\n  var openSub = on(socket, 'open', function () {\n    self.onopen();\n    fn && fn();\n  });\n\n  // emit `connect_error`\n  var errorSub = on(socket, 'error', function (data) {\n    debug('connect_error');\n    self.cleanup();\n    self.readyState = 'closed';\n    self.emitAll('connect_error', data);\n    if (fn) {\n      var err = new Error('Connection error');\n      err.data = data;\n      fn(err);\n    } else {\n      // Only do this if there is no fn to handle the error\n      self.maybeReconnectOnOpen();\n    }\n  });\n\n  // emit `connect_timeout`\n  if (false !== this._timeout) {\n    var timeout = this._timeout;\n    debug('connect attempt will timeout after %d', timeout);\n\n    if (timeout === 0) {\n      openSub.destroy(); // prevents a race condition with the 'open' event\n    }\n\n    // set timer\n    var timer = setTimeout(function () {\n      debug('connect attempt timed out after %d', timeout);\n      openSub.destroy();\n      socket.close();\n      socket.emit('error', 'timeout');\n      self.emitAll('connect_timeout', timeout);\n    }, timeout);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n\n  this.subs.push(openSub);\n  this.subs.push(errorSub);\n\n  return this;\n};\n\n/**\n * Called upon transport open.\n *\n * @api private\n */\n\nManager.prototype.onopen = function () {\n  debug('open');\n\n  // clear old subs\n  this.cleanup();\n\n  // mark as open\n  this.readyState = 'open';\n  this.emit('open');\n\n  // add new subs\n  var socket = this.engine;\n  this.subs.push(on(socket, 'data', bind(this, 'ondata')));\n  this.subs.push(on(socket, 'ping', bind(this, 'onping')));\n  this.subs.push(on(socket, 'pong', bind(this, 'onpong')));\n  this.subs.push(on(socket, 'error', bind(this, 'onerror')));\n  this.subs.push(on(socket, 'close', bind(this, 'onclose')));\n  this.subs.push(on(this.decoder, 'decoded', bind(this, 'ondecoded')));\n};\n\n/**\n * Called upon a ping.\n *\n * @api private\n */\n\nManager.prototype.onping = function () {\n  this.lastPing = new Date();\n  this.emitAll('ping');\n};\n\n/**\n * Called upon a packet.\n *\n * @api private\n */\n\nManager.prototype.onpong = function () {\n  this.emitAll('pong', new Date() - this.lastPing);\n};\n\n/**\n * Called with data.\n *\n * @api private\n */\n\nManager.prototype.ondata = function (data) {\n  this.decoder.add(data);\n};\n\n/**\n * Called when parser fully decodes a packet.\n *\n * @api private\n */\n\nManager.prototype.ondecoded = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon socket error.\n *\n * @api private\n */\n\nManager.prototype.onerror = function (err) {\n  debug('error', err);\n  this.emitAll('error', err);\n};\n\n/**\n * Creates a new socket for the given `nsp`.\n *\n * @return {Socket}\n * @api public\n */\n\nManager.prototype.socket = function (nsp, opts) {\n  var socket = this.nsps[nsp];\n  if (!socket) {\n    socket = new Socket(this, nsp, opts);\n    this.nsps[nsp] = socket;\n    var self = this;\n    socket.on('connecting', onConnecting);\n    socket.on('connect', function () {\n      socket.id = self.generateId(nsp);\n    });\n\n    if (this.autoConnect) {\n      // manually call here since connecting event is fired before listening\n      onConnecting();\n    }\n  }\n\n  function onConnecting () {\n    if (!~indexOf(self.connecting, socket)) {\n      self.connecting.push(socket);\n    }\n  }\n\n  return socket;\n};\n\n/**\n * Called upon a socket close.\n *\n * @param {Socket} socket\n */\n\nManager.prototype.destroy = function (socket) {\n  var index = indexOf(this.connecting, socket);\n  if (~index) this.connecting.splice(index, 1);\n  if (this.connecting.length) return;\n\n  this.close();\n};\n\n/**\n * Writes a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nManager.prototype.packet = function (packet) {\n  debug('writing packet %j', packet);\n  var self = this;\n  if (packet.query && packet.type === 0) packet.nsp += '?' + packet.query;\n\n  if (!self.encoding) {\n    // encode, then write to engine with result\n    self.encoding = true;\n    this.encoder.encode(packet, function (encodedPackets) {\n      for (var i = 0; i < encodedPackets.length; i++) {\n        self.engine.write(encodedPackets[i], packet.options);\n      }\n      self.encoding = false;\n      self.processPacketQueue();\n    });\n  } else { // add packet to the queue\n    self.packetBuffer.push(packet);\n  }\n};\n\n/**\n * If packet buffer is non-empty, begins encoding the\n * next packet in line.\n *\n * @api private\n */\n\nManager.prototype.processPacketQueue = function () {\n  if (this.packetBuffer.length > 0 && !this.encoding) {\n    var pack = this.packetBuffer.shift();\n    this.packet(pack);\n  }\n};\n\n/**\n * Clean up transport subscriptions and packet buffer.\n *\n * @api private\n */\n\nManager.prototype.cleanup = function () {\n  debug('cleanup');\n\n  var subsLength = this.subs.length;\n  for (var i = 0; i < subsLength; i++) {\n    var sub = this.subs.shift();\n    sub.destroy();\n  }\n\n  this.packetBuffer = [];\n  this.encoding = false;\n  this.lastPing = null;\n\n  this.decoder.destroy();\n};\n\n/**\n * Close the current socket.\n *\n * @api private\n */\n\nManager.prototype.close =\nManager.prototype.disconnect = function () {\n  debug('disconnect');\n  this.skipReconnect = true;\n  this.reconnecting = false;\n  if ('opening' === this.readyState) {\n    // `onclose` will not fire because\n    // an open event never happened\n    this.cleanup();\n  }\n  this.backoff.reset();\n  this.readyState = 'closed';\n  if (this.engine) this.engine.close();\n};\n\n/**\n * Called upon engine close.\n *\n * @api private\n */\n\nManager.prototype.onclose = function (reason) {\n  debug('onclose');\n\n  this.cleanup();\n  this.backoff.reset();\n  this.readyState = 'closed';\n  this.emit('close', reason);\n\n  if (this._reconnection && !this.skipReconnect) {\n    this.reconnect();\n  }\n};\n\n/**\n * Attempt a reconnection.\n *\n * @api private\n */\n\nManager.prototype.reconnect = function () {\n  if (this.reconnecting || this.skipReconnect) return this;\n\n  var self = this;\n\n  if (this.backoff.attempts >= this._reconnectionAttempts) {\n    debug('reconnect failed');\n    this.backoff.reset();\n    this.emitAll('reconnect_failed');\n    this.reconnecting = false;\n  } else {\n    var delay = this.backoff.duration();\n    debug('will wait %dms before reconnect attempt', delay);\n\n    this.reconnecting = true;\n    var timer = setTimeout(function () {\n      if (self.skipReconnect) return;\n\n      debug('attempting reconnect');\n      self.emitAll('reconnect_attempt', self.backoff.attempts);\n      self.emitAll('reconnecting', self.backoff.attempts);\n\n      // check again for the case socket closed in above events\n      if (self.skipReconnect) return;\n\n      self.open(function (err) {\n        if (err) {\n          debug('reconnect attempt error');\n          self.reconnecting = false;\n          self.reconnect();\n          self.emitAll('reconnect_error', err.data);\n        } else {\n          debug('reconnect success');\n          self.onreconnect();\n        }\n      });\n    }, delay);\n\n    this.subs.push({\n      destroy: function () {\n        clearTimeout(timer);\n      }\n    });\n  }\n};\n\n/**\n * Called upon successful reconnect.\n *\n * @api private\n */\n\nManager.prototype.onreconnect = function () {\n  var attempt = this.backoff.attempts;\n  this.reconnecting = false;\n  this.backoff.reset();\n  this.updateSocketIds();\n  this.emitAll('reconnect', attempt);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/manager.js\n// module id = 12\n// module chunks = 0", "\nmodule.exports = require('./socket');\n\n/**\n * Exports parser\n *\n * @api public\n *\n */\nmodule.exports.parser = require('engine.io-parser');\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/index.js\n// module id = 13\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar transports = require('./transports/index');\nvar Emitter = require('component-emitter');\nvar debug = require('debug')('engine.io-client:socket');\nvar index = require('indexof');\nvar parser = require('engine.io-parser');\nvar parseuri = require('parseuri');\nvar parseqs = require('parseqs');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Socket;\n\n/**\n * Socket constructor.\n *\n * @param {String|Object} uri or options\n * @param {Object} options\n * @api public\n */\n\nfunction Socket (uri, opts) {\n  if (!(this instanceof Socket)) return new Socket(uri, opts);\n\n  opts = opts || {};\n\n  if (uri && 'object' === typeof uri) {\n    opts = uri;\n    uri = null;\n  }\n\n  if (uri) {\n    uri = parseuri(uri);\n    opts.hostname = uri.host;\n    opts.secure = uri.protocol === 'https' || uri.protocol === 'wss';\n    opts.port = uri.port;\n    if (uri.query) opts.query = uri.query;\n  } else if (opts.host) {\n    opts.hostname = parseuri(opts.host).host;\n  }\n\n  this.secure = null != opts.secure ? opts.secure\n    : (typeof location !== 'undefined' && 'https:' === location.protocol);\n\n  if (opts.hostname && !opts.port) {\n    // if no port is specified manually, use the protocol default\n    opts.port = this.secure ? '443' : '80';\n  }\n\n  this.agent = opts.agent || false;\n  this.hostname = opts.hostname ||\n    (typeof location !== 'undefined' ? location.hostname : 'localhost');\n  this.port = opts.port || (typeof location !== 'undefined' && location.port\n      ? location.port\n      : (this.secure ? 443 : 80));\n  this.query = opts.query || {};\n  if ('string' === typeof this.query) this.query = parseqs.decode(this.query);\n  this.upgrade = false !== opts.upgrade;\n  this.path = (opts.path || '/engine.io').replace(/\\/$/, '') + '/';\n  this.forceJSONP = !!opts.forceJSONP;\n  this.jsonp = false !== opts.jsonp;\n  this.forceBase64 = !!opts.forceBase64;\n  this.enablesXDR = !!opts.enablesXDR;\n  this.withCredentials = false !== opts.withCredentials;\n  this.timestampParam = opts.timestampParam || 't';\n  this.timestampRequests = opts.timestampRequests;\n  this.transports = opts.transports || ['polling', 'websocket'];\n  this.transportOptions = opts.transportOptions || {};\n  this.readyState = '';\n  this.writeBuffer = [];\n  this.prevBufferLen = 0;\n  this.policyPort = opts.policyPort || 843;\n  this.rememberUpgrade = opts.rememberUpgrade || false;\n  this.binaryType = null;\n  this.onlyBinaryUpgrades = opts.onlyBinaryUpgrades;\n  this.perMessageDeflate = false !== opts.perMessageDeflate ? (opts.perMessageDeflate || {}) : false;\n\n  if (true === this.perMessageDeflate) this.perMessageDeflate = {};\n  if (this.perMessageDeflate && null == this.perMessageDeflate.threshold) {\n    this.perMessageDeflate.threshold = 1024;\n  }\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx || null;\n  this.key = opts.key || null;\n  this.passphrase = opts.passphrase || null;\n  this.cert = opts.cert || null;\n  this.ca = opts.ca || null;\n  this.ciphers = opts.ciphers || null;\n  this.rejectUnauthorized = opts.rejectUnauthorized === undefined ? true : opts.rejectUnauthorized;\n  this.forceNode = !!opts.forceNode;\n\n  // detect ReactNative environment\n  this.isReactNative = (typeof navigator !== 'undefined' && typeof navigator.product === 'string' && navigator.product.toLowerCase() === 'reactnative');\n\n  // other options for Node.js or ReactNative client\n  if (typeof self === 'undefined' || this.isReactNative) {\n    if (opts.extraHeaders && Object.keys(opts.extraHeaders).length > 0) {\n      this.extraHeaders = opts.extraHeaders;\n    }\n\n    if (opts.localAddress) {\n      this.localAddress = opts.localAddress;\n    }\n  }\n\n  // set on handshake\n  this.id = null;\n  this.upgrades = null;\n  this.pingInterval = null;\n  this.pingTimeout = null;\n\n  // set on heartbeat\n  this.pingIntervalTimer = null;\n  this.pingTimeoutTimer = null;\n\n  this.open();\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nSocket.Socket = Socket;\nSocket.Transport = require('./transport');\nSocket.transports = require('./transports/index');\nSocket.parser = require('engine.io-parser');\n\n/**\n * Creates transport of the given type.\n *\n * @param {String} transport name\n * @return {Transport}\n * @api private\n */\n\nSocket.prototype.createTransport = function (name) {\n  debug('creating transport \"%s\"', name);\n  var query = clone(this.query);\n\n  // append engine.io protocol identifier\n  query.EIO = parser.protocol;\n\n  // transport name\n  query.transport = name;\n\n  // per-transport options\n  var options = this.transportOptions[name] || {};\n\n  // session id if we already have one\n  if (this.id) query.sid = this.id;\n\n  var transport = new transports[name]({\n    query: query,\n    socket: this,\n    agent: options.agent || this.agent,\n    hostname: options.hostname || this.hostname,\n    port: options.port || this.port,\n    secure: options.secure || this.secure,\n    path: options.path || this.path,\n    forceJSONP: options.forceJSONP || this.forceJSONP,\n    jsonp: options.jsonp || this.jsonp,\n    forceBase64: options.forceBase64 || this.forceBase64,\n    enablesXDR: options.enablesXDR || this.enablesXDR,\n    withCredentials: options.withCredentials || this.withCredentials,\n    timestampRequests: options.timestampRequests || this.timestampRequests,\n    timestampParam: options.timestampParam || this.timestampParam,\n    policyPort: options.policyPort || this.policyPort,\n    pfx: options.pfx || this.pfx,\n    key: options.key || this.key,\n    passphrase: options.passphrase || this.passphrase,\n    cert: options.cert || this.cert,\n    ca: options.ca || this.ca,\n    ciphers: options.ciphers || this.ciphers,\n    rejectUnauthorized: options.rejectUnauthorized || this.rejectUnauthorized,\n    perMessageDeflate: options.perMessageDeflate || this.perMessageDeflate,\n    extraHeaders: options.extraHeaders || this.extraHeaders,\n    forceNode: options.forceNode || this.forceNode,\n    localAddress: options.localAddress || this.localAddress,\n    requestTimeout: options.requestTimeout || this.requestTimeout,\n    protocols: options.protocols || void (0),\n    isReactNative: this.isReactNative\n  });\n\n  return transport;\n};\n\nfunction clone (obj) {\n  var o = {};\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\n/**\n * Initializes transport to use and starts probe.\n *\n * @api private\n */\nSocket.prototype.open = function () {\n  var transport;\n  if (this.rememberUpgrade && Socket.priorWebsocketSuccess && this.transports.indexOf('websocket') !== -1) {\n    transport = 'websocket';\n  } else if (0 === this.transports.length) {\n    // Emit error on next tick so it can be listened to\n    var self = this;\n    setTimeout(function () {\n      self.emit('error', 'No transports available');\n    }, 0);\n    return;\n  } else {\n    transport = this.transports[0];\n  }\n  this.readyState = 'opening';\n\n  // Retry with the next transport if the transport is disabled (jsonp: false)\n  try {\n    transport = this.createTransport(transport);\n  } catch (e) {\n    this.transports.shift();\n    this.open();\n    return;\n  }\n\n  transport.open();\n  this.setTransport(transport);\n};\n\n/**\n * Sets the current transport. Disables the existing one (if any).\n *\n * @api private\n */\n\nSocket.prototype.setTransport = function (transport) {\n  debug('setting transport %s', transport.name);\n  var self = this;\n\n  if (this.transport) {\n    debug('clearing existing transport %s', this.transport.name);\n    this.transport.removeAllListeners();\n  }\n\n  // set up transport\n  this.transport = transport;\n\n  // set up transport listeners\n  transport\n  .on('drain', function () {\n    self.onDrain();\n  })\n  .on('packet', function (packet) {\n    self.onPacket(packet);\n  })\n  .on('error', function (e) {\n    self.onError(e);\n  })\n  .on('close', function () {\n    self.onClose('transport close');\n  });\n};\n\n/**\n * Probes a transport.\n *\n * @param {String} transport name\n * @api private\n */\n\nSocket.prototype.probe = function (name) {\n  debug('probing transport \"%s\"', name);\n  var transport = this.createTransport(name, { probe: 1 });\n  var failed = false;\n  var self = this;\n\n  Socket.priorWebsocketSuccess = false;\n\n  function onTransportOpen () {\n    if (self.onlyBinaryUpgrades) {\n      var upgradeLosesBinary = !this.supportsBinary && self.transport.supportsBinary;\n      failed = failed || upgradeLosesBinary;\n    }\n    if (failed) return;\n\n    debug('probe transport \"%s\" opened', name);\n    transport.send([{ type: 'ping', data: 'probe' }]);\n    transport.once('packet', function (msg) {\n      if (failed) return;\n      if ('pong' === msg.type && 'probe' === msg.data) {\n        debug('probe transport \"%s\" pong', name);\n        self.upgrading = true;\n        self.emit('upgrading', transport);\n        if (!transport) return;\n        Socket.priorWebsocketSuccess = 'websocket' === transport.name;\n\n        debug('pausing current transport \"%s\"', self.transport.name);\n        self.transport.pause(function () {\n          if (failed) return;\n          if ('closed' === self.readyState) return;\n          debug('changing transport and sending upgrade packet');\n\n          cleanup();\n\n          self.setTransport(transport);\n          transport.send([{ type: 'upgrade' }]);\n          self.emit('upgrade', transport);\n          transport = null;\n          self.upgrading = false;\n          self.flush();\n        });\n      } else {\n        debug('probe transport \"%s\" failed', name);\n        var err = new Error('probe error');\n        err.transport = transport.name;\n        self.emit('upgradeError', err);\n      }\n    });\n  }\n\n  function freezeTransport () {\n    if (failed) return;\n\n    // Any callback called by transport should be ignored since now\n    failed = true;\n\n    cleanup();\n\n    transport.close();\n    transport = null;\n  }\n\n  // Handle any error that happens while probing\n  function onerror (err) {\n    var error = new Error('probe error: ' + err);\n    error.transport = transport.name;\n\n    freezeTransport();\n\n    debug('probe transport \"%s\" failed because of error: %s', name, err);\n\n    self.emit('upgradeError', error);\n  }\n\n  function onTransportClose () {\n    onerror('transport closed');\n  }\n\n  // When the socket is closed while we're probing\n  function onclose () {\n    onerror('socket closed');\n  }\n\n  // When the socket is upgraded while we're probing\n  function onupgrade (to) {\n    if (transport && to.name !== transport.name) {\n      debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n      freezeTransport();\n    }\n  }\n\n  // Remove all listeners on the transport and on self\n  function cleanup () {\n    transport.removeListener('open', onTransportOpen);\n    transport.removeListener('error', onerror);\n    transport.removeListener('close', onTransportClose);\n    self.removeListener('close', onclose);\n    self.removeListener('upgrading', onupgrade);\n  }\n\n  transport.once('open', onTransportOpen);\n  transport.once('error', onerror);\n  transport.once('close', onTransportClose);\n\n  this.once('close', onclose);\n  this.once('upgrading', onupgrade);\n\n  transport.open();\n};\n\n/**\n * Called when connection is deemed open.\n *\n * @api public\n */\n\nSocket.prototype.onOpen = function () {\n  debug('socket open');\n  this.readyState = 'open';\n  Socket.priorWebsocketSuccess = 'websocket' === this.transport.name;\n  this.emit('open');\n  this.flush();\n\n  // we check for `readyState` in case an `open`\n  // listener already closed the socket\n  if ('open' === this.readyState && this.upgrade && this.transport.pause) {\n    debug('starting upgrade probes');\n    for (var i = 0, l = this.upgrades.length; i < l; i++) {\n      this.probe(this.upgrades[i]);\n    }\n  }\n};\n\n/**\n * Handles a packet.\n *\n * @api private\n */\n\nSocket.prototype.onPacket = function (packet) {\n  if ('opening' === this.readyState || 'open' === this.readyState ||\n      'closing' === this.readyState) {\n    debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\n    this.emit('packet', packet);\n\n    // Socket is live - any packet counts\n    this.emit('heartbeat');\n\n    switch (packet.type) {\n      case 'open':\n        this.onHandshake(JSON.parse(packet.data));\n        break;\n\n      case 'pong':\n        this.setPing();\n        this.emit('pong');\n        break;\n\n      case 'error':\n        var err = new Error('server error');\n        err.code = packet.data;\n        this.onError(err);\n        break;\n\n      case 'message':\n        this.emit('data', packet.data);\n        this.emit('message', packet.data);\n        break;\n    }\n  } else {\n    debug('packet received with socket readyState \"%s\"', this.readyState);\n  }\n};\n\n/**\n * Called upon handshake completion.\n *\n * @param {Object} handshake obj\n * @api private\n */\n\nSocket.prototype.onHandshake = function (data) {\n  this.emit('handshake', data);\n  this.id = data.sid;\n  this.transport.query.sid = data.sid;\n  this.upgrades = this.filterUpgrades(data.upgrades);\n  this.pingInterval = data.pingInterval;\n  this.pingTimeout = data.pingTimeout;\n  this.onOpen();\n  // In case open handler closes socket\n  if ('closed' === this.readyState) return;\n  this.setPing();\n\n  // Prolong liveness of socket on heartbeat\n  this.removeListener('heartbeat', this.onHeartbeat);\n  this.on('heartbeat', this.onHeartbeat);\n};\n\n/**\n * Resets ping timeout.\n *\n * @api private\n */\n\nSocket.prototype.onHeartbeat = function (timeout) {\n  clearTimeout(this.pingTimeoutTimer);\n  var self = this;\n  self.pingTimeoutTimer = setTimeout(function () {\n    if ('closed' === self.readyState) return;\n    self.onClose('ping timeout');\n  }, timeout || (self.pingInterval + self.pingTimeout));\n};\n\n/**\n * Pings server every `this.pingInterval` and expects response\n * within `this.pingTimeout` or closes connection.\n *\n * @api private\n */\n\nSocket.prototype.setPing = function () {\n  var self = this;\n  clearTimeout(self.pingIntervalTimer);\n  self.pingIntervalTimer = setTimeout(function () {\n    debug('writing ping packet - expecting pong within %sms', self.pingTimeout);\n    self.ping();\n    self.onHeartbeat(self.pingTimeout);\n  }, self.pingInterval);\n};\n\n/**\n* Sends a ping packet.\n*\n* @api private\n*/\n\nSocket.prototype.ping = function () {\n  var self = this;\n  this.sendPacket('ping', function () {\n    self.emit('ping');\n  });\n};\n\n/**\n * Called on `drain` event\n *\n * @api private\n */\n\nSocket.prototype.onDrain = function () {\n  this.writeBuffer.splice(0, this.prevBufferLen);\n\n  // setting prevBufferLen = 0 is very important\n  // for example, when upgrading, upgrade packet is sent over,\n  // and a nonzero prevBufferLen could cause problems on `drain`\n  this.prevBufferLen = 0;\n\n  if (0 === this.writeBuffer.length) {\n    this.emit('drain');\n  } else {\n    this.flush();\n  }\n};\n\n/**\n * Flush write buffers.\n *\n * @api private\n */\n\nSocket.prototype.flush = function () {\n  if ('closed' !== this.readyState && this.transport.writable &&\n    !this.upgrading && this.writeBuffer.length) {\n    debug('flushing %d packets in socket', this.writeBuffer.length);\n    this.transport.send(this.writeBuffer);\n    // keep track of current length of writeBuffer\n    // splice writeBuffer and callbackBuffer on `drain`\n    this.prevBufferLen = this.writeBuffer.length;\n    this.emit('flush');\n  }\n};\n\n/**\n * Sends a message.\n *\n * @param {String} message.\n * @param {Function} callback function.\n * @param {Object} options.\n * @return {Socket} for chaining.\n * @api public\n */\n\nSocket.prototype.write =\nSocket.prototype.send = function (msg, options, fn) {\n  this.sendPacket('message', msg, options, fn);\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {String} packet type.\n * @param {String} data.\n * @param {Object} options.\n * @param {Function} callback function.\n * @api private\n */\n\nSocket.prototype.sendPacket = function (type, data, options, fn) {\n  if ('function' === typeof data) {\n    fn = data;\n    data = undefined;\n  }\n\n  if ('function' === typeof options) {\n    fn = options;\n    options = null;\n  }\n\n  if ('closing' === this.readyState || 'closed' === this.readyState) {\n    return;\n  }\n\n  options = options || {};\n  options.compress = false !== options.compress;\n\n  var packet = {\n    type: type,\n    data: data,\n    options: options\n  };\n  this.emit('packetCreate', packet);\n  this.writeBuffer.push(packet);\n  if (fn) this.once('flush', fn);\n  this.flush();\n};\n\n/**\n * Closes the connection.\n *\n * @api private\n */\n\nSocket.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.readyState = 'closing';\n\n    var self = this;\n\n    if (this.writeBuffer.length) {\n      this.once('drain', function () {\n        if (this.upgrading) {\n          waitForUpgrade();\n        } else {\n          close();\n        }\n      });\n    } else if (this.upgrading) {\n      waitForUpgrade();\n    } else {\n      close();\n    }\n  }\n\n  function close () {\n    self.onClose('forced close');\n    debug('socket closing - telling transport to close');\n    self.transport.close();\n  }\n\n  function cleanupAndClose () {\n    self.removeListener('upgrade', cleanupAndClose);\n    self.removeListener('upgradeError', cleanupAndClose);\n    close();\n  }\n\n  function waitForUpgrade () {\n    // wait for upgrade to finish since we can't send packets while pausing a transport\n    self.once('upgrade', cleanupAndClose);\n    self.once('upgradeError', cleanupAndClose);\n  }\n\n  return this;\n};\n\n/**\n * Called upon transport error\n *\n * @api private\n */\n\nSocket.prototype.onError = function (err) {\n  debug('socket error %j', err);\n  Socket.priorWebsocketSuccess = false;\n  this.emit('error', err);\n  this.onClose('transport error', err);\n};\n\n/**\n * Called upon transport close.\n *\n * @api private\n */\n\nSocket.prototype.onClose = function (reason, desc) {\n  if ('opening' === this.readyState || 'open' === this.readyState || 'closing' === this.readyState) {\n    debug('socket close with reason: \"%s\"', reason);\n    var self = this;\n\n    // clear timers\n    clearTimeout(this.pingIntervalTimer);\n    clearTimeout(this.pingTimeoutTimer);\n\n    // stop event from firing again for transport\n    this.transport.removeAllListeners('close');\n\n    // ensure transport won't stay open\n    this.transport.close();\n\n    // ignore further transport communication\n    this.transport.removeAllListeners();\n\n    // set ready state\n    this.readyState = 'closed';\n\n    // clear session id\n    this.id = null;\n\n    // emit close event\n    this.emit('close', reason, desc);\n\n    // clean buffers after, so users can still\n    // grab the buffers on `close` event\n    self.writeBuffer = [];\n    self.prevBufferLen = 0;\n  }\n};\n\n/**\n * Filters upgrades, returning only those matching client transports.\n *\n * @param {Array} server upgrades\n * @api private\n *\n */\n\nSocket.prototype.filterUpgrades = function (upgrades) {\n  var filteredUpgrades = [];\n  for (var i = 0, j = upgrades.length; i < j; i++) {\n    if (~index(this.transports, upgrades[i])) filteredUpgrades.push(upgrades[i]);\n  }\n  return filteredUpgrades;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/socket.js\n// module id = 14\n// module chunks = 0", "/**\n * Module dependencies\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar XHR = require('./polling-xhr');\nvar JSONP = require('./polling-jsonp');\nvar websocket = require('./websocket');\n\n/**\n * Export transports.\n */\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling (opts) {\n  var xhr;\n  var xd = false;\n  var xs = false;\n  var jsonp = false !== opts.jsonp;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if ('open' in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error('JSONP disabled');\n    return new JSONP(opts);\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/index.js\n// module id = 15\n// module chunks = 0", "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\nvar globalThis = require('./globalThis');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/xmlhttprequest.js\n// module id = 16\n// module chunks = 0", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-cors/index.js\n// module id = 17\n// module chunks = 0", "module.exports = (function () {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else {\n    return Function('return this')(); // eslint-disable-line no-new-func\n  }\n})();\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/globalThis.browser.js\n// module id = 18\n// module chunks = 0", "/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('xmlhttprequest-ssl');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n  debug('xhr poll');\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n    debug('xhr open %s: %s', this.method, this.uri);\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n\n    debug('xhr data %s', this.data);\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-xhr.js\n// module id = 19\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('xmlhttprequest-ssl');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n    debug('paused');\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n      debug('we are currently polling - waiting to pause');\n      total++;\n      this.once('pollComplete', function () {\n        debug('pre-pause polling complete');\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n      debug('we are currently writing - waiting to pause');\n      total++;\n      this.once('drain', function () {\n        debug('pre-pause writing complete');\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n  debug('polling');\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n  debug('polling got data %s', data);\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState) {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n      debug('ignoring poll - transport state \"%s\"', this.readyState);\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n    debug('writing close packet');\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n    debug('transport open - closing');\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n    debug('transport not open - deferring close');\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling.js\n// module id = 20\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar parser = require('engine.io-parser');\nvar Emitter = require('component-emitter');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Transport;\n\n/**\n * Transport abstract constructor.\n *\n * @param {Object} options.\n * @api private\n */\n\nfunction Transport (opts) {\n  this.path = opts.path;\n  this.hostname = opts.hostname;\n  this.port = opts.port;\n  this.secure = opts.secure;\n  this.query = opts.query;\n  this.timestampParam = opts.timestampParam;\n  this.timestampRequests = opts.timestampRequests;\n  this.readyState = '';\n  this.agent = opts.agent || false;\n  this.socket = opts.socket;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n  this.forceNode = opts.forceNode;\n\n  // results of ReactNative environment detection\n  this.isReactNative = opts.isReactNative;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.localAddress = opts.localAddress;\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Transport.prototype);\n\n/**\n * Emits an error.\n *\n * @param {String} str\n * @return {Transport} for chaining\n * @api public\n */\n\nTransport.prototype.onError = function (msg, desc) {\n  var err = new Error(msg);\n  err.type = 'TransportError';\n  err.description = desc;\n  this.emit('error', err);\n  return this;\n};\n\n/**\n * Opens the transport.\n *\n * @api public\n */\n\nTransport.prototype.open = function () {\n  if ('closed' === this.readyState || '' === this.readyState) {\n    this.readyState = 'opening';\n    this.doOpen();\n  }\n\n  return this;\n};\n\n/**\n * Closes the transport.\n *\n * @api private\n */\n\nTransport.prototype.close = function () {\n  if ('opening' === this.readyState || 'open' === this.readyState) {\n    this.doClose();\n    this.onClose();\n  }\n\n  return this;\n};\n\n/**\n * Sends multiple packets.\n *\n * @param {Array} packets\n * @api private\n */\n\nTransport.prototype.send = function (packets) {\n  if ('open' === this.readyState) {\n    this.write(packets);\n  } else {\n    throw new Error('Transport not open');\n  }\n};\n\n/**\n * Called upon open\n *\n * @api private\n */\n\nTransport.prototype.onOpen = function () {\n  this.readyState = 'open';\n  this.writable = true;\n  this.emit('open');\n};\n\n/**\n * Called with data.\n *\n * @param {String} data\n * @api private\n */\n\nTransport.prototype.onData = function (data) {\n  var packet = parser.decodePacket(data, this.socket.binaryType);\n  this.onPacket(packet);\n};\n\n/**\n * Called with a decoded packet.\n */\n\nTransport.prototype.onPacket = function (packet) {\n  this.emit('packet', packet);\n};\n\n/**\n * Called upon close.\n *\n * @api private\n */\n\nTransport.prototype.onClose = function () {\n  this.readyState = 'closed';\n  this.emit('close');\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transport.js\n// module id = 21\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar keys = require('./keys');\nvar hasBinary = require('has-binary2');\nvar sliceBuffer = require('arraybuffer.slice');\nvar after = require('after');\nvar utf8 = require('./utf8');\n\nvar base64encoder;\nif (typeof ArrayBuffer !== 'undefined') {\n  base64encoder = require('base64-arraybuffer');\n}\n\n/**\n * Check if we are running an android browser. That requires us to use\n * ArrayBuffer with polling transports...\n *\n * http://ghinda.net/jpeg-blob-ajax-android/\n */\n\nvar isAndroid = typeof navigator !== 'undefined' && /Android/i.test(navigator.userAgent);\n\n/**\n * Check if we are running in PhantomJS.\n * Uploading a Blob with PhantomJS does not work correctly, as reported here:\n * https://github.com/ariya/phantomjs/issues/11395\n * @type boolean\n */\nvar isPhantomJS = typeof navigator !== 'undefined' && /PhantomJS/i.test(navigator.userAgent);\n\n/**\n * When true, avoids using Blobs to encode payloads.\n * @type boolean\n */\nvar dontSendBlobs = isAndroid || isPhantomJS;\n\n/**\n * Current protocol version.\n */\n\nexports.protocol = 3;\n\n/**\n * Packet types.\n */\n\nvar packets = exports.packets = {\n    open:     0    // non-ws\n  , close:    1    // non-ws\n  , ping:     2\n  , pong:     3\n  , message:  4\n  , upgrade:  5\n  , noop:     6\n};\n\nvar packetslist = keys(packets);\n\n/**\n * Premade error packet.\n */\n\nvar err = { type: 'error', data: 'parser error' };\n\n/**\n * Create a blob api even for blob builder when vendor prefixes exist\n */\n\nvar Blob = require('blob');\n\n/**\n * Encodes a packet.\n *\n *     <packet type id> [ <data> ]\n *\n * Example:\n *\n *     5hello world\n *     3\n *     4\n *\n * Binary is encoded in an identical principle\n *\n * @api private\n */\n\nexports.encodePacket = function (packet, supportsBinary, utf8encode, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = false;\n  }\n\n  if (typeof utf8encode === 'function') {\n    callback = utf8encode;\n    utf8encode = null;\n  }\n\n  var data = (packet.data === undefined)\n    ? undefined\n    : packet.data.buffer || packet.data;\n\n  if (typeof ArrayBuffer !== 'undefined' && data instanceof ArrayBuffer) {\n    return encodeArrayBuffer(packet, supportsBinary, callback);\n  } else if (typeof Blob !== 'undefined' && data instanceof Blob) {\n    return encodeBlob(packet, supportsBinary, callback);\n  }\n\n  // might be an object with { base64: true, data: dataAsBase64String }\n  if (data && data.base64) {\n    return encodeBase64Object(packet, callback);\n  }\n\n  // Sending data as a utf-8 string\n  var encoded = packets[packet.type];\n\n  // data fragment is optional\n  if (undefined !== packet.data) {\n    encoded += utf8encode ? utf8.encode(String(packet.data), { strict: false }) : String(packet.data);\n  }\n\n  return callback('' + encoded);\n\n};\n\nfunction encodeBase64Object(packet, callback) {\n  // packet data is an object { base64: true, data: dataAsBase64String }\n  var message = 'b' + exports.packets[packet.type] + packet.data.data;\n  return callback(message);\n}\n\n/**\n * Encode packet helpers for binary types\n */\n\nfunction encodeArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var data = packet.data;\n  var contentArray = new Uint8Array(data);\n  var resultBuffer = new Uint8Array(1 + data.byteLength);\n\n  resultBuffer[0] = packets[packet.type];\n  for (var i = 0; i < contentArray.length; i++) {\n    resultBuffer[i+1] = contentArray[i];\n  }\n\n  return callback(resultBuffer.buffer);\n}\n\nfunction encodeBlobAsArrayBuffer(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  var fr = new FileReader();\n  fr.onload = function() {\n    exports.encodePacket({ type: packet.type, data: fr.result }, supportsBinary, true, callback);\n  };\n  return fr.readAsArrayBuffer(packet.data);\n}\n\nfunction encodeBlob(packet, supportsBinary, callback) {\n  if (!supportsBinary) {\n    return exports.encodeBase64Packet(packet, callback);\n  }\n\n  if (dontSendBlobs) {\n    return encodeBlobAsArrayBuffer(packet, supportsBinary, callback);\n  }\n\n  var length = new Uint8Array(1);\n  length[0] = packets[packet.type];\n  var blob = new Blob([length.buffer, packet.data]);\n\n  return callback(blob);\n}\n\n/**\n * Encodes a packet with binary data in a base64 string\n *\n * @param {Object} packet, has `type` and `data`\n * @return {String} base64 encoded message\n */\n\nexports.encodeBase64Packet = function(packet, callback) {\n  var message = 'b' + exports.packets[packet.type];\n  if (typeof Blob !== 'undefined' && packet.data instanceof Blob) {\n    var fr = new FileReader();\n    fr.onload = function() {\n      var b64 = fr.result.split(',')[1];\n      callback(message + b64);\n    };\n    return fr.readAsDataURL(packet.data);\n  }\n\n  var b64data;\n  try {\n    b64data = String.fromCharCode.apply(null, new Uint8Array(packet.data));\n  } catch (e) {\n    // iPhone Safari doesn't let you apply with typed arrays\n    var typed = new Uint8Array(packet.data);\n    var basic = new Array(typed.length);\n    for (var i = 0; i < typed.length; i++) {\n      basic[i] = typed[i];\n    }\n    b64data = String.fromCharCode.apply(null, basic);\n  }\n  message += btoa(b64data);\n  return callback(message);\n};\n\n/**\n * Decodes a packet. Changes format to Blob if requested.\n *\n * @return {Object} with `type` and `data` (if any)\n * @api private\n */\n\nexports.decodePacket = function (data, binaryType, utf8decode) {\n  if (data === undefined) {\n    return err;\n  }\n  // String data\n  if (typeof data === 'string') {\n    if (data.charAt(0) === 'b') {\n      return exports.decodeBase64Packet(data.substr(1), binaryType);\n    }\n\n    if (utf8decode) {\n      data = tryDecode(data);\n      if (data === false) {\n        return err;\n      }\n    }\n    var type = data.charAt(0);\n\n    if (Number(type) != type || !packetslist[type]) {\n      return err;\n    }\n\n    if (data.length > 1) {\n      return { type: packetslist[type], data: data.substring(1) };\n    } else {\n      return { type: packetslist[type] };\n    }\n  }\n\n  var asArray = new Uint8Array(data);\n  var type = asArray[0];\n  var rest = sliceBuffer(data, 1);\n  if (Blob && binaryType === 'blob') {\n    rest = new Blob([rest]);\n  }\n  return { type: packetslist[type], data: rest };\n};\n\nfunction tryDecode(data) {\n  try {\n    data = utf8.decode(data, { strict: false });\n  } catch (e) {\n    return false;\n  }\n  return data;\n}\n\n/**\n * Decodes a packet encoded in a base64 string\n *\n * @param {String} base64 encoded message\n * @return {Object} with `type` and `data` (if any)\n */\n\nexports.decodeBase64Packet = function(msg, binaryType) {\n  var type = packetslist[msg.charAt(0)];\n  if (!base64encoder) {\n    return { type: type, data: { base64: true, data: msg.substr(1) } };\n  }\n\n  var data = base64encoder.decode(msg.substr(1));\n\n  if (binaryType === 'blob' && Blob) {\n    data = new Blob([data]);\n  }\n\n  return { type: type, data: data };\n};\n\n/**\n * Encodes multiple messages (payload).\n *\n *     <length>:data\n *\n * Example:\n *\n *     11:hello world2:hi\n *\n * If any contents are binary, they will be encoded as base64 strings. Base64\n * encoded strings are marked with a b before the length specifier\n *\n * @param {Array} packets\n * @api private\n */\n\nexports.encodePayload = function (packets, supportsBinary, callback) {\n  if (typeof supportsBinary === 'function') {\n    callback = supportsBinary;\n    supportsBinary = null;\n  }\n\n  var isBinary = hasBinary(packets);\n\n  if (supportsBinary && isBinary) {\n    if (Blob && !dontSendBlobs) {\n      return exports.encodePayloadAsBlob(packets, callback);\n    }\n\n    return exports.encodePayloadAsArrayBuffer(packets, callback);\n  }\n\n  if (!packets.length) {\n    return callback('0:');\n  }\n\n  function setLengthHeader(message) {\n    return message.length + ':' + message;\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, !isBinary ? false : supportsBinary, false, function(message) {\n      doneCallback(null, setLengthHeader(message));\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(results.join(''));\n  });\n};\n\n/**\n * Async array map using after\n */\n\nfunction map(ary, each, done) {\n  var result = new Array(ary.length);\n  var next = after(ary.length, done);\n\n  var eachWithIndex = function(i, el, cb) {\n    each(el, function(error, msg) {\n      result[i] = msg;\n      cb(error, result);\n    });\n  };\n\n  for (var i = 0; i < ary.length; i++) {\n    eachWithIndex(i, ary[i], next);\n  }\n}\n\n/*\n * Decodes data when a payload is maybe expected. Possible binary contents are\n * decoded from their base64 representation\n *\n * @param {String} data, callback method\n * @api public\n */\n\nexports.decodePayload = function (data, binaryType, callback) {\n  if (typeof data !== 'string') {\n    return exports.decodePayloadAsBinary(data, binaryType, callback);\n  }\n\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var packet;\n  if (data === '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n  var length = '', n, msg;\n\n  for (var i = 0, l = data.length; i < l; i++) {\n    var chr = data.charAt(i);\n\n    if (chr !== ':') {\n      length += chr;\n      continue;\n    }\n\n    if (length === '' || (length != (n = Number(length)))) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    msg = data.substr(i + 1, n);\n\n    if (length != msg.length) {\n      // parser error - ignoring payload\n      return callback(err, 0, 1);\n    }\n\n    if (msg.length) {\n      packet = exports.decodePacket(msg, binaryType, false);\n\n      if (err.type === packet.type && err.data === packet.data) {\n        // parser error in individual packet - ignoring payload\n        return callback(err, 0, 1);\n      }\n\n      var ret = callback(packet, i + n, l);\n      if (false === ret) return;\n    }\n\n    // advance cursor\n    i += n;\n    length = '';\n  }\n\n  if (length !== '') {\n    // parser error - ignoring payload\n    return callback(err, 0, 1);\n  }\n\n};\n\n/**\n * Encodes multiple messages (payload) as binary.\n *\n * <1 = binary, 0 = string><number from 0-9><number from 0-9>[...]<number\n * 255><data>\n *\n * Example:\n * 1 3 255 1 2 3, if the binary contents are interpreted as 8 bit integers\n *\n * @param {Array} packets\n * @return {ArrayBuffer} encoded payload\n * @api private\n */\n\nexports.encodePayloadAsArrayBuffer = function(packets, callback) {\n  if (!packets.length) {\n    return callback(new ArrayBuffer(0));\n  }\n\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(data) {\n      return doneCallback(null, data);\n    });\n  }\n\n  map(packets, encodeOne, function(err, encodedPackets) {\n    var totalLength = encodedPackets.reduce(function(acc, p) {\n      var len;\n      if (typeof p === 'string'){\n        len = p.length;\n      } else {\n        len = p.byteLength;\n      }\n      return acc + len.toString().length + len + 2; // string/binary identifier + separator = 2\n    }, 0);\n\n    var resultArray = new Uint8Array(totalLength);\n\n    var bufferIndex = 0;\n    encodedPackets.forEach(function(p) {\n      var isString = typeof p === 'string';\n      var ab = p;\n      if (isString) {\n        var view = new Uint8Array(p.length);\n        for (var i = 0; i < p.length; i++) {\n          view[i] = p.charCodeAt(i);\n        }\n        ab = view.buffer;\n      }\n\n      if (isString) { // not true binary\n        resultArray[bufferIndex++] = 0;\n      } else { // true binary\n        resultArray[bufferIndex++] = 1;\n      }\n\n      var lenStr = ab.byteLength.toString();\n      for (var i = 0; i < lenStr.length; i++) {\n        resultArray[bufferIndex++] = parseInt(lenStr[i]);\n      }\n      resultArray[bufferIndex++] = 255;\n\n      var view = new Uint8Array(ab);\n      for (var i = 0; i < view.length; i++) {\n        resultArray[bufferIndex++] = view[i];\n      }\n    });\n\n    return callback(resultArray.buffer);\n  });\n};\n\n/**\n * Encode as Blob\n */\n\nexports.encodePayloadAsBlob = function(packets, callback) {\n  function encodeOne(packet, doneCallback) {\n    exports.encodePacket(packet, true, true, function(encoded) {\n      var binaryIdentifier = new Uint8Array(1);\n      binaryIdentifier[0] = 1;\n      if (typeof encoded === 'string') {\n        var view = new Uint8Array(encoded.length);\n        for (var i = 0; i < encoded.length; i++) {\n          view[i] = encoded.charCodeAt(i);\n        }\n        encoded = view.buffer;\n        binaryIdentifier[0] = 0;\n      }\n\n      var len = (encoded instanceof ArrayBuffer)\n        ? encoded.byteLength\n        : encoded.size;\n\n      var lenStr = len.toString();\n      var lengthAry = new Uint8Array(lenStr.length + 1);\n      for (var i = 0; i < lenStr.length; i++) {\n        lengthAry[i] = parseInt(lenStr[i]);\n      }\n      lengthAry[lenStr.length] = 255;\n\n      if (Blob) {\n        var blob = new Blob([binaryIdentifier.buffer, lengthAry.buffer, encoded]);\n        doneCallback(null, blob);\n      }\n    });\n  }\n\n  map(packets, encodeOne, function(err, results) {\n    return callback(new Blob(results));\n  });\n};\n\n/*\n * Decodes data when a payload is maybe expected. Strings are decoded by\n * interpreting each byte as a key code for entries marked to start with 0. See\n * description of encodePayloadAsBinary\n *\n * @param {ArrayBuffer} data, callback method\n * @api public\n */\n\nexports.decodePayloadAsBinary = function (data, binaryType, callback) {\n  if (typeof binaryType === 'function') {\n    callback = binaryType;\n    binaryType = null;\n  }\n\n  var bufferTail = data;\n  var buffers = [];\n\n  while (bufferTail.byteLength > 0) {\n    var tailArray = new Uint8Array(bufferTail);\n    var isString = tailArray[0] === 0;\n    var msgLength = '';\n\n    for (var i = 1; ; i++) {\n      if (tailArray[i] === 255) break;\n\n      // 310 = char length of Number.MAX_VALUE\n      if (msgLength.length > 310) {\n        return callback(err, 0, 1);\n      }\n\n      msgLength += tailArray[i];\n    }\n\n    bufferTail = sliceBuffer(bufferTail, 2 + msgLength.length);\n    msgLength = parseInt(msgLength);\n\n    var msg = sliceBuffer(bufferTail, 0, msgLength);\n    if (isString) {\n      try {\n        msg = String.fromCharCode.apply(null, new Uint8Array(msg));\n      } catch (e) {\n        // iPhone Safari doesn't let you apply to typed arrays\n        var typed = new Uint8Array(msg);\n        msg = '';\n        for (var i = 0; i < typed.length; i++) {\n          msg += String.fromCharCode(typed[i]);\n        }\n      }\n    }\n\n    buffers.push(msg);\n    bufferTail = sliceBuffer(bufferTail, msgLength);\n  }\n\n  var total = buffers.length;\n  buffers.forEach(function(buffer, i) {\n    callback(exports.decodePacket(buffer, binaryType, true), i, total);\n  });\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/browser.js\n// module id = 22\n// module chunks = 0", "\n/**\n * Gets the keys for an object.\n *\n * @return {Array} keys\n * @api private\n */\n\nmodule.exports = Object.keys || function keys (obj){\n  var arr = [];\n  var has = Object.prototype.hasOwnProperty;\n\n  for (var i in obj) {\n    if (has.call(obj, i)) {\n      arr.push(i);\n    }\n  }\n  return arr;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/keys.js\n// module id = 23\n// module chunks = 0", "/* global Blob File */\n\n/*\n * Module requirements.\n */\n\nvar isArray = require('isarray');\n\nvar toString = Object.prototype.toString;\nvar withNativeBlob = typeof Blob === 'function' ||\n                        typeof Blob !== 'undefined' && toString.call(Blob) === '[object BlobConstructor]';\nvar withNativeFile = typeof File === 'function' ||\n                        typeof File !== 'undefined' && toString.call(File) === '[object FileConstructor]';\n\n/**\n * Module exports.\n */\n\nmodule.exports = hasBinary;\n\n/**\n * Checks for binary data.\n *\n * Supports Buffer, ArrayBuffer, Blob and File.\n *\n * @param {Object} anything\n * @api public\n */\n\nfunction hasBinary (obj) {\n  if (!obj || typeof obj !== 'object') {\n    return false;\n  }\n\n  if (isArray(obj)) {\n    for (var i = 0, l = obj.length; i < l; i++) {\n      if (hasBinary(obj[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  if ((typeof Buffer === 'function' && Buffer.isBuffer && Buffer.isBuffer(obj)) ||\n    (typeof ArrayBuffer === 'function' && obj instanceof ArrayBuffer) ||\n    (withNativeBlob && obj instanceof Blob) ||\n    (withNativeFile && obj instanceof File)\n  ) {\n    return true;\n  }\n\n  // see: https://github.com/Automattic/has-binary/pull/4\n  if (obj.toJSON && typeof obj.toJSON === 'function' && arguments.length === 1) {\n    return hasBinary(obj.toJSON(), true);\n  }\n\n  for (var key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/has-binary2/index.js\n// module id = 24\n// module chunks = 0", "/**\n * An abstraction for slicing an arraybuffer even when\n * ArrayBuffer.prototype.slice is not supported\n *\n * @api public\n */\n\nmodule.exports = function(arraybuffer, start, end) {\n  var bytes = arraybuffer.byteLength;\n  start = start || 0;\n  end = end || bytes;\n\n  if (arraybuffer.slice) { return arraybuffer.slice(start, end); }\n\n  if (start < 0) { start += bytes; }\n  if (end < 0) { end += bytes; }\n  if (end > bytes) { end = bytes; }\n\n  if (start >= bytes || start >= end || bytes === 0) {\n    return new ArrayBuffer(0);\n  }\n\n  var abv = new Uint8Array(arraybuffer);\n  var result = new Uint8Array(end - start);\n  for (var i = start, ii = 0; i < end; i++, ii++) {\n    result[ii] = abv[i];\n  }\n  return result.buffer;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/arraybuffer.slice/index.js\n// module id = 25\n// module chunks = 0", "module.exports = after\n\nfunction after(count, callback, err_cb) {\n    var bail = false\n    err_cb = err_cb || noop\n    proxy.count = count\n\n    return (count === 0) ? callback() : proxy\n\n    function proxy(err, result) {\n        if (proxy.count <= 0) {\n            throw new Error('after called too many times')\n        }\n        --proxy.count\n\n        // after first error, rest are passed to err_cb\n        if (err) {\n            bail = true\n            callback(err)\n            // future error callbacks will go to error handler\n            callback = err_cb\n        } else if (proxy.count === 0 && !bail) {\n            callback(null, result)\n        }\n    }\n}\n\nfunction noop() {}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/after/index.js\n// module id = 26\n// module chunks = 0", "/*! https://mths.be/utf8js v2.1.2 by @mathias */\n\nvar stringFromCharCode = String.fromCharCode;\n\n// Taken from https://mths.be/punycode\nfunction ucs2decode(string) {\n\tvar output = [];\n\tvar counter = 0;\n\tvar length = string.length;\n\tvar value;\n\tvar extra;\n\twhile (counter < length) {\n\t\tvalue = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// high surrogate, and there is a next character\n\t\t\textra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n// Taken from https://mths.be/punycode\nfunction ucs2encode(array) {\n\tvar length = array.length;\n\tvar index = -1;\n\tvar value;\n\tvar output = '';\n\twhile (++index < length) {\n\t\tvalue = array[index];\n\t\tif (value > 0xFFFF) {\n\t\t\tvalue -= 0x10000;\n\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t}\n\t\toutput += stringFromCharCode(value);\n\t}\n\treturn output;\n}\n\nfunction checkScalarValue(codePoint, strict) {\n\tif (codePoint >= 0xD800 && codePoint <= 0xDFFF) {\n\t\tif (strict) {\n\t\t\tthrow Error(\n\t\t\t\t'Lone surrogate U+' + codePoint.toString(16).toUpperCase() +\n\t\t\t\t' is not a scalar value'\n\t\t\t);\n\t\t}\n\t\treturn false;\n\t}\n\treturn true;\n}\n/*--------------------------------------------------------------------------*/\n\nfunction createByte(codePoint, shift) {\n\treturn stringFromCharCode(((codePoint >> shift) & 0x3F) | 0x80);\n}\n\nfunction encodeCodePoint(codePoint, strict) {\n\tif ((codePoint & 0xFFFFFF80) == 0) { // 1-byte sequence\n\t\treturn stringFromCharCode(codePoint);\n\t}\n\tvar symbol = '';\n\tif ((codePoint & 0xFFFFF800) == 0) { // 2-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 6) & 0x1F) | 0xC0);\n\t}\n\telse if ((codePoint & 0xFFFF0000) == 0) { // 3-byte sequence\n\t\tif (!checkScalarValue(codePoint, strict)) {\n\t\t\tcodePoint = 0xFFFD;\n\t\t}\n\t\tsymbol = stringFromCharCode(((codePoint >> 12) & 0x0F) | 0xE0);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\telse if ((codePoint & 0xFFE00000) == 0) { // 4-byte sequence\n\t\tsymbol = stringFromCharCode(((codePoint >> 18) & 0x07) | 0xF0);\n\t\tsymbol += createByte(codePoint, 12);\n\t\tsymbol += createByte(codePoint, 6);\n\t}\n\tsymbol += stringFromCharCode((codePoint & 0x3F) | 0x80);\n\treturn symbol;\n}\n\nfunction utf8encode(string, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tvar codePoints = ucs2decode(string);\n\tvar length = codePoints.length;\n\tvar index = -1;\n\tvar codePoint;\n\tvar byteString = '';\n\twhile (++index < length) {\n\t\tcodePoint = codePoints[index];\n\t\tbyteString += encodeCodePoint(codePoint, strict);\n\t}\n\treturn byteString;\n}\n\n/*--------------------------------------------------------------------------*/\n\nfunction readContinuationByte() {\n\tif (byteIndex >= byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tvar continuationByte = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\tif ((continuationByte & 0xC0) == 0x80) {\n\t\treturn continuationByte & 0x3F;\n\t}\n\n\t// If we end up here, it’s not a continuation byte\n\tthrow Error('Invalid continuation byte');\n}\n\nfunction decodeSymbol(strict) {\n\tvar byte1;\n\tvar byte2;\n\tvar byte3;\n\tvar byte4;\n\tvar codePoint;\n\n\tif (byteIndex > byteCount) {\n\t\tthrow Error('Invalid byte index');\n\t}\n\n\tif (byteIndex == byteCount) {\n\t\treturn false;\n\t}\n\n\t// Read first byte\n\tbyte1 = byteArray[byteIndex] & 0xFF;\n\tbyteIndex++;\n\n\t// 1-byte sequence (no continuation bytes)\n\tif ((byte1 & 0x80) == 0) {\n\t\treturn byte1;\n\t}\n\n\t// 2-byte sequence\n\tif ((byte1 & 0xE0) == 0xC0) {\n\t\tbyte2 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x1F) << 6) | byte2;\n\t\tif (codePoint >= 0x80) {\n\t\t\treturn codePoint;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 3-byte sequence (may include unpaired surrogates)\n\tif ((byte1 & 0xF0) == 0xE0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x0F) << 12) | (byte2 << 6) | byte3;\n\t\tif (codePoint >= 0x0800) {\n\t\t\treturn checkScalarValue(codePoint, strict) ? codePoint : 0xFFFD;\n\t\t} else {\n\t\t\tthrow Error('Invalid continuation byte');\n\t\t}\n\t}\n\n\t// 4-byte sequence\n\tif ((byte1 & 0xF8) == 0xF0) {\n\t\tbyte2 = readContinuationByte();\n\t\tbyte3 = readContinuationByte();\n\t\tbyte4 = readContinuationByte();\n\t\tcodePoint = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0C) |\n\t\t\t(byte3 << 0x06) | byte4;\n\t\tif (codePoint >= 0x010000 && codePoint <= 0x10FFFF) {\n\t\t\treturn codePoint;\n\t\t}\n\t}\n\n\tthrow Error('Invalid UTF-8 detected');\n}\n\nvar byteArray;\nvar byteCount;\nvar byteIndex;\nfunction utf8decode(byteString, opts) {\n\topts = opts || {};\n\tvar strict = false !== opts.strict;\n\n\tbyteArray = ucs2decode(byteString);\n\tbyteCount = byteArray.length;\n\tbyteIndex = 0;\n\tvar codePoints = [];\n\tvar tmp;\n\twhile ((tmp = decodeSymbol(strict)) !== false) {\n\t\tcodePoints.push(tmp);\n\t}\n\treturn ucs2encode(codePoints);\n}\n\nmodule.exports = {\n\tversion: '2.1.2',\n\tencode: utf8encode,\n\tdecode: utf8decode\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/lib/utf8.js\n// module id = 27\n// module chunks = 0", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-parser/~/base64-arraybuffer/lib/base64-arraybuffer.js\n// module id = 28\n// module chunks = 0", "/**\r\n * Create a blob builder even when vendor prefixes exist\r\n */\r\n\r\nvar BlobBuilder = typeof BlobBuilder !== 'undefined' ? BlobBuilder :\r\n  typeof WebKitBlobBuilder !== 'undefined' ? WebKitBlobBuilder :\r\n  typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder :\r\n  typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : \r\n  false;\r\n\r\n/**\r\n * Check if Blob constructor is supported\r\n */\r\n\r\nvar blobSupported = (function() {\r\n  try {\r\n    var a = new Blob(['hi']);\r\n    return a.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if Blob constructor supports ArrayBufferViews\r\n * Fails in Safari 6, so we need to map to ArrayBuffers there.\r\n */\r\n\r\nvar blobSupportsArrayBufferView = blobSupported && (function() {\r\n  try {\r\n    var b = new Blob([new Uint8Array([1,2])]);\r\n    return b.size === 2;\r\n  } catch(e) {\r\n    return false;\r\n  }\r\n})();\r\n\r\n/**\r\n * Check if BlobBuilder is supported\r\n */\r\n\r\nvar blobBuilderSupported = BlobBuilder\r\n  && BlobBuilder.prototype.append\r\n  && BlobBuilder.prototype.getBlob;\r\n\r\n/**\r\n * Helper function that maps ArrayBufferViews to ArrayBuffers\r\n * Used by BlobBuilder constructor and old browsers that didn't\r\n * support it in the Blob constructor.\r\n */\r\n\r\nfunction mapArrayBufferViews(ary) {\r\n  return ary.map(function(chunk) {\r\n    if (chunk.buffer instanceof ArrayBuffer) {\r\n      var buf = chunk.buffer;\r\n\r\n      // if this is a subarray, make a copy so we only\r\n      // include the subarray region from the underlying buffer\r\n      if (chunk.byteLength !== buf.byteLength) {\r\n        var copy = new Uint8Array(chunk.byteLength);\r\n        copy.set(new Uint8Array(buf, chunk.byteOffset, chunk.byteLength));\r\n        buf = copy.buffer;\r\n      }\r\n\r\n      return buf;\r\n    }\r\n\r\n    return chunk;\r\n  });\r\n}\r\n\r\nfunction BlobBuilderConstructor(ary, options) {\r\n  options = options || {};\r\n\r\n  var bb = new BlobBuilder();\r\n  mapArrayBufferViews(ary).forEach(function(part) {\r\n    bb.append(part);\r\n  });\r\n\r\n  return (options.type) ? bb.getBlob(options.type) : bb.getBlob();\r\n};\r\n\r\nfunction BlobConstructor(ary, options) {\r\n  return new Blob(mapArrayBufferViews(ary), options || {});\r\n};\r\n\r\nif (typeof Blob !== 'undefined') {\r\n  BlobBuilderConstructor.prototype = Blob.prototype;\r\n  BlobConstructor.prototype = Blob.prototype;\r\n}\r\n\r\nmodule.exports = (function() {\r\n  if (blobSupported) {\r\n    return blobSupportsArrayBufferView ? Blob : BlobConstructor;\r\n  } else if (blobBuilderSupported) {\r\n    return BlobBuilderConstructor;\r\n  } else {\r\n    return undefined;\r\n  }\r\n})();\r\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/blob/index.js\n// module id = 29\n// module chunks = 0", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/parseqs/index.js\n// module id = 30\n// module chunks = 0", "\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-inherit/index.js\n// module id = 31\n// module chunks = 0", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/yeast/index.js\n// module id = 32\n// module chunks = 0", "/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    callbacks = globalThis.___eio = (globalThis.___eio || []);\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/polling-jsonp.js\n// module id = 33\n// module chunks = 0", "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\n\nvar BrowserWebSocket, NodeWebSocket;\n\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\n\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n\n  var opts = {};\n\n  if (!this.isReactNative) {\n    opts.agent = this.agent;\n    opts.perMessageDeflate = this.perMessageDeflate;\n\n    // SSL options for Node.js client\n    opts.pfx = this.pfx;\n    opts.key = this.key;\n    opts.passphrase = this.passphrase;\n    opts.cert = this.cert;\n    opts.ca = this.ca;\n    opts.ciphers = this.ciphers;\n    opts.rejectUnauthorized = this.rejectUnauthorized;\n  }\n\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws =\n      this.usingBrowserWebSocket && !this.isReactNative\n        ? protocols\n          ? new WebSocketImpl(uri, protocols)\n          : new WebSocketImpl(uri)\n        : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug('websocket closed before onclose event');\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/engine.io-client/lib/transports/websocket.js\n// module id = 34\n// module chunks = 0", "\nvar indexOf = [].indexOf;\n\nmodule.exports = function(arr, obj){\n  if (indexOf) return arr.indexOf(obj);\n  for (var i = 0; i < arr.length; ++i) {\n    if (arr[i] === obj) return i;\n  }\n  return -1;\n};\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/indexof/index.js\n// module id = 36\n// module chunks = 0", "\n/**\n * Module dependencies.\n */\n\nvar parser = require('socket.io-parser');\nvar Emitter = require('component-emitter');\nvar toArray = require('to-array');\nvar on = require('./on');\nvar bind = require('component-bind');\nvar debug = require('debug')('socket.io-client:socket');\nvar parseqs = require('parseqs');\nvar hasBin = require('has-binary2');\n\n/**\n * Module exports.\n */\n\nmodule.exports = exports = Socket;\n\n/**\n * Internal events (blacklisted).\n * These events can't be emitted by the user.\n *\n * @api private\n */\n\nvar events = {\n  connect: 1,\n  connect_error: 1,\n  connect_timeout: 1,\n  connecting: 1,\n  disconnect: 1,\n  error: 1,\n  reconnect: 1,\n  reconnect_attempt: 1,\n  reconnect_failed: 1,\n  reconnect_error: 1,\n  reconnecting: 1,\n  ping: 1,\n  pong: 1\n};\n\n/**\n * Shortcut to `Emitter#emit`.\n */\n\nvar emit = Emitter.prototype.emit;\n\n/**\n * `Socket` constructor.\n *\n * @api public\n */\n\nfunction Socket (io, nsp, opts) {\n  this.io = io;\n  this.nsp = nsp;\n  this.json = this; // compat\n  this.ids = 0;\n  this.acks = {};\n  this.receiveBuffer = [];\n  this.sendBuffer = [];\n  this.connected = false;\n  this.disconnected = true;\n  this.flags = {};\n  if (opts && opts.query) {\n    this.query = opts.query;\n  }\n  if (this.io.autoConnect) this.open();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Socket.prototype);\n\n/**\n * Subscribe to open, close and packet events\n *\n * @api private\n */\n\nSocket.prototype.subEvents = function () {\n  if (this.subs) return;\n\n  var io = this.io;\n  this.subs = [\n    on(io, 'open', bind(this, 'onopen')),\n    on(io, 'packet', bind(this, 'onpacket')),\n    on(io, 'close', bind(this, 'onclose'))\n  ];\n};\n\n/**\n * \"Opens\" the socket.\n *\n * @api public\n */\n\nSocket.prototype.open =\nSocket.prototype.connect = function () {\n  if (this.connected) return this;\n\n  this.subEvents();\n  if (!this.io.reconnecting) this.io.open(); // ensure open\n  if ('open' === this.io.readyState) this.onopen();\n  this.emit('connecting');\n  return this;\n};\n\n/**\n * Sends a `message` event.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.send = function () {\n  var args = toArray(arguments);\n  args.unshift('message');\n  this.emit.apply(this, args);\n  return this;\n};\n\n/**\n * Override `emit`.\n * If the event is in `events`, it's emitted normally.\n *\n * @param {String} event name\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.emit = function (ev) {\n  if (events.hasOwnProperty(ev)) {\n    emit.apply(this, arguments);\n    return this;\n  }\n\n  var args = toArray(arguments);\n  var packet = {\n    type: (this.flags.binary !== undefined ? this.flags.binary : hasBin(args)) ? parser.BINARY_EVENT : parser.EVENT,\n    data: args\n  };\n\n  packet.options = {};\n  packet.options.compress = !this.flags || false !== this.flags.compress;\n\n  // event ack callback\n  if ('function' === typeof args[args.length - 1]) {\n    debug('emitting packet with ack id %d', this.ids);\n    this.acks[this.ids] = args.pop();\n    packet.id = this.ids++;\n  }\n\n  if (this.connected) {\n    this.packet(packet);\n  } else {\n    this.sendBuffer.push(packet);\n  }\n\n  this.flags = {};\n\n  return this;\n};\n\n/**\n * Sends a packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.packet = function (packet) {\n  packet.nsp = this.nsp;\n  this.io.packet(packet);\n};\n\n/**\n * Called upon engine `open`.\n *\n * @api private\n */\n\nSocket.prototype.onopen = function () {\n  debug('transport is open - connecting');\n\n  // write connect packet if necessary\n  if ('/' !== this.nsp) {\n    if (this.query) {\n      var query = typeof this.query === 'object' ? parseqs.encode(this.query) : this.query;\n      debug('sending connect packet with query %s', query);\n      this.packet({type: parser.CONNECT, query: query});\n    } else {\n      this.packet({type: parser.CONNECT});\n    }\n  }\n};\n\n/**\n * Called upon engine `close`.\n *\n * @param {String} reason\n * @api private\n */\n\nSocket.prototype.onclose = function (reason) {\n  debug('close (%s)', reason);\n  this.connected = false;\n  this.disconnected = true;\n  delete this.id;\n  this.emit('disconnect', reason);\n};\n\n/**\n * Called with socket packet.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onpacket = function (packet) {\n  var sameNamespace = packet.nsp === this.nsp;\n  var rootNamespaceError = packet.type === parser.ERROR && packet.nsp === '/';\n\n  if (!sameNamespace && !rootNamespaceError) return;\n\n  switch (packet.type) {\n    case parser.CONNECT:\n      this.onconnect();\n      break;\n\n    case parser.EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.BINARY_EVENT:\n      this.onevent(packet);\n      break;\n\n    case parser.ACK:\n      this.onack(packet);\n      break;\n\n    case parser.BINARY_ACK:\n      this.onack(packet);\n      break;\n\n    case parser.DISCONNECT:\n      this.ondisconnect();\n      break;\n\n    case parser.ERROR:\n      this.emit('error', packet.data);\n      break;\n  }\n};\n\n/**\n * Called upon a server event.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onevent = function (packet) {\n  var args = packet.data || [];\n  debug('emitting event %j', args);\n\n  if (null != packet.id) {\n    debug('attaching ack callback to event');\n    args.push(this.ack(packet.id));\n  }\n\n  if (this.connected) {\n    emit.apply(this, args);\n  } else {\n    this.receiveBuffer.push(args);\n  }\n};\n\n/**\n * Produces an ack callback to emit with an event.\n *\n * @api private\n */\n\nSocket.prototype.ack = function (id) {\n  var self = this;\n  var sent = false;\n  return function () {\n    // prevent double callbacks\n    if (sent) return;\n    sent = true;\n    var args = toArray(arguments);\n    debug('sending ack %j', args);\n\n    self.packet({\n      type: hasBin(args) ? parser.BINARY_ACK : parser.ACK,\n      id: id,\n      data: args\n    });\n  };\n};\n\n/**\n * Called upon a server acknowlegement.\n *\n * @param {Object} packet\n * @api private\n */\n\nSocket.prototype.onack = function (packet) {\n  var ack = this.acks[packet.id];\n  if ('function' === typeof ack) {\n    debug('calling ack %s with %j', packet.id, packet.data);\n    ack.apply(this, packet.data);\n    delete this.acks[packet.id];\n  } else {\n    debug('bad ack %s', packet.id);\n  }\n};\n\n/**\n * Called upon server connect.\n *\n * @api private\n */\n\nSocket.prototype.onconnect = function () {\n  this.connected = true;\n  this.disconnected = false;\n  this.emit('connect');\n  this.emitBuffered();\n};\n\n/**\n * Emit buffered events (received and emitted).\n *\n * @api private\n */\n\nSocket.prototype.emitBuffered = function () {\n  var i;\n  for (i = 0; i < this.receiveBuffer.length; i++) {\n    emit.apply(this, this.receiveBuffer[i]);\n  }\n  this.receiveBuffer = [];\n\n  for (i = 0; i < this.sendBuffer.length; i++) {\n    this.packet(this.sendBuffer[i]);\n  }\n  this.sendBuffer = [];\n};\n\n/**\n * Called upon server disconnect.\n *\n * @api private\n */\n\nSocket.prototype.ondisconnect = function () {\n  debug('server disconnect (%s)', this.nsp);\n  this.destroy();\n  this.onclose('io server disconnect');\n};\n\n/**\n * Called upon forced client/server side disconnections,\n * this method ensures the manager stops tracking us and\n * that reconnections don't get triggered for this.\n *\n * @api private.\n */\n\nSocket.prototype.destroy = function () {\n  if (this.subs) {\n    // clean subscriptions to avoid reconnections\n    for (var i = 0; i < this.subs.length; i++) {\n      this.subs[i].destroy();\n    }\n    this.subs = null;\n  }\n\n  this.io.destroy(this);\n};\n\n/**\n * Disconnects the socket manually.\n *\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.close =\nSocket.prototype.disconnect = function () {\n  if (this.connected) {\n    debug('performing disconnect (%s)', this.nsp);\n    this.packet({ type: parser.DISCONNECT });\n  }\n\n  // remove socket from pool\n  this.destroy();\n\n  if (this.connected) {\n    // fire events\n    this.onclose('io client disconnect');\n  }\n  return this;\n};\n\n/**\n * Sets the compress flag.\n *\n * @param {Boolean} if `true`, compresses the sending data\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.compress = function (compress) {\n  this.flags.compress = compress;\n  return this;\n};\n\n/**\n * Sets the binary flag\n *\n * @param {Boolean} whether the emitted data contains binary\n * @return {Socket} self\n * @api public\n */\n\nSocket.prototype.binary = function (binary) {\n  this.flags.binary = binary;\n  return this;\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/socket.js\n// module id = 37\n// module chunks = 0", "module.exports = toArray\n\nfunction toArray(list, index) {\n    var array = []\n\n    index = index || 0\n\n    for (var i = index || 0; i < list.length; i++) {\n        array[i - index] = list[i]\n    }\n\n    return array\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/to-array/index.js\n// module id = 38\n// module chunks = 0", "\n/**\n * Module exports.\n */\n\nmodule.exports = on;\n\n/**\n * Helper for subscriptions.\n *\n * @param {Object|EventEmitter} obj with `Emitter` mixin or `EventEmitter`\n * @param {String} event name\n * @param {Function} callback\n * @api public\n */\n\nfunction on (obj, ev, fn) {\n  obj.on(ev, fn);\n  return {\n    destroy: function () {\n      obj.removeListener(ev, fn);\n    }\n  };\n}\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./lib/on.js\n// module id = 39\n// module chunks = 0", "/**\n * Slice reference.\n */\n\nvar slice = [].slice;\n\n/**\n * Bind `obj` to `fn`.\n *\n * @param {Object} obj\n * @param {Function|String} fn or string\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function(obj, fn){\n  if ('string' == typeof fn) fn = obj[fn];\n  if ('function' != typeof fn) throw new Error('bind() requires a function');\n  var args = slice.call(arguments, 2);\n  return function(){\n    return fn.apply(obj, args.concat(slice.call(arguments)));\n  }\n};\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/component-bind/index.js\n// module id = 40\n// module chunks = 0", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./~/backo2/index.js\n// module id = 41\n// module chunks = 0"], "sourceRoot": ""}