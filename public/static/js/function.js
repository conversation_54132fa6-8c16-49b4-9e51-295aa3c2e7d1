/**
 * 构建详细的AJAX错误信息
 * @param {Object} xhr - XMLHttpRequest对象
 * @param {string} status - 错误状态
 * @param {string} error - 错误信息
 * @returns {string} 格式化的错误信息
 */
function buildDetailedErrorMessage(xhr, status, error) {
  let errorMsg = "网络错误";

  // 根据HTTP状态码显示具体错误
  if (xhr.status) {
    switch (xhr.status) {
      case 400:
        errorMsg = "请求参数错误(400)";
        break;
      case 401:
        errorMsg = "未授权访问(401)";
        break;
      case 403:
        errorMsg = "访问被禁止(403)";
        break;
      case 404:
        errorMsg = "请求地址不存在(404)";
        break;
      case 405:
        errorMsg = "请求方法不允许(405)";
        break;
      case 408:
        errorMsg = "请求超时(408)";
        break;
      case 500:
        errorMsg = "服务器内部错误(500)";
        break;
      case 502:
        errorMsg = "网关错误(502)";
        break;
      case 503:
        errorMsg = "服务不可用(503)";
        break;
      case 504:
        errorMsg = "网关超时(504)";
        break;
      default:
        errorMsg = `网络错误(${xhr.status})`;
    }

    // 如果服务器返回了错误信息，尝试解析并显示
    if (xhr.responseText) {
      try {
        const response = JSON.parse(xhr.responseText);
        if (response.msg || response.message) {
          errorMsg += `: ${response.msg || response.message}`;
        }
      } catch (e) {
        // 如果不是JSON格式，显示原始错误文本（截取前50个字符）
        if (xhr.responseText.length > 50) {
          errorMsg += `: ${xhr.responseText.substring(0, 50)}...`;
        } else if (xhr.responseText.trim()) {
          errorMsg += `: ${xhr.responseText}`;
        }
      }
    }
  } else if (status === 'timeout') {
    errorMsg = "请求超时，请检查网络连接";
  } else if (status === 'abort') {
    errorMsg = "请求被取消";
  } else if (status === 'parsererror') {
    errorMsg = "数据解析错误";
  } else if (error) {
    errorMsg = `网络错误: ${error}`;
  }

  return errorMsg;
}

function getFormJson(frm) {
  var o = {};
  var a = $(frm).serializeArray();
  $.each(a, function () {
    if (o[this.name] !== undefined) {
      if (!o[this.name].push) {
        o[this.name] = [o[this.name]];
      }
      o[this.name].push(this.value || "");
    } else {
      o[this.name] = this.value || "";
    }
  });

  return o;
}

function parseQueryString(url) {
  var url = url ? url : window.location.href;
  var reg_url = /^[^\?]+\?([\w\W]+)$/,
    reg_para = /([^&=]+)=([\w\W]*?)(&|$|#)/g,
    arr_url = reg_url.exec(url),
    ret = {};
  if (arr_url && arr_url[1]) {
    var str_para = arr_url[1],
      result;
    while ((result = reg_para.exec(str_para)) != null) {
      ret[result[1]] = result[2];
    }
  }
  return ret;
}

function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return "";
}

//设置cookie

function setCookie(name, value, days) {
  days = arguments[2] ? arguments[2] : 1; //设置参数exdays的默认值为1
  var exp = new Date();
  exp.setTime(exp.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = name + "=" + decodeURIComponent(value) + ";expires=" + exp.toUTCString() + ";path=/";
}

//读取cookies
function getCookie(name) {
  var arr,
    reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
  if ((arr = document.cookie.match(reg))) return decodeURIComponent(arr[2]);
  else return "";
}

//删除cookies
function delCookie(name) {
  var exp = new Date();
  exp.setTime(exp.getTime() - 1);
  var cval = getCookie(name);
  if (cval != null) {
    document.cookie = name + "=" + cval + ";expires=" + exp.toUTCString() + ";path=/";
  }
}

//清除所有cookie函数
function clearCookie() {
  var keys = document.cookie.match(/[^ =;]+(?=\=)/g);
  if (keys) {
    for (var i = keys.length; i--; ) document.cookie = keys[i] + "=0;expires=" + new Date(0).toUTCString() + ";path=/";
  }
}

//HTML转义
function HTMLEncode(html) {
  var temp = document.createElement("div");
  temp.textContent != null ? (temp.textContent = html) : (temp.innerText = html);
  var output = temp.innerHTML;
  temp = null;
  return output;
}

function toUtf8(str) {
  var out, i, len, c;
  out = "";
  len = str.length;
  for (i = 0; i < len; i++) {
    c = str.charCodeAt(i);
    if (c >= 0x0001 && c <= 0x007f) {
      out += str.charAt(i);
    } else if (c > 0x07ff) {
      out += String.fromCharCode(0xe0 | ((c >> 12) & 0x0f));
      out += String.fromCharCode(0x80 | ((c >> 6) & 0x3f));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
    } else {
      out += String.fromCharCode(0xc0 | ((c >> 6) & 0x1f));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
    }
  }
  return out;
}

//HTML反转义
function HTMLDecode(text) {
  var temp = document.createElement("div");
  temp.innerHTML = text;
  var output = temp.innerText || temp.textContent;
  temp = null;
  return output;
}

function get_form_data(id) {
  var d = {};
  var t = $("#" + id).serializeArray();
  $.each(t, function () {
    d[this.name] = this.value;
  });
  //console.log(JSON.stringify(d));
  return d;
}

function has_auth(auth) {
  var wsCache = new WebStorageCache();
  var user_permission = wsCache.get("user_permission");
  if (isEmpty(user_permission)) {
    return false;
  }
  return $.inArray(auth, user_permission) !== -1;
}

/**
 * 获取表单数据
 * @param {*} itemForm dom对象
 */
function getFormDataArray(itemForm) {
  var nameIndex = {}, //数组 name 索引
    field = {},
    fieldElem = itemForm.find("input,select,textarea"); //获取所有表单域

  layui.each(fieldElem, function (_, item) {
    item.name = (item.name || "").replace(/^\s*|\s*&/, "");

    if (!item.name) return;

    //用于支持数组 name
    if (/^.*\[\]$/.test(item.name)) {
      var key = item.name.match(/^(.*)\[\]$/g)[0];
      nameIndex[key] = nameIndex[key] | 0;
      item.name = item.name.replace(/^(.*)\[\]$/, "$1[" + nameIndex[key]++ + "]");
    }

    if (/^checkbox|radio$/.test(item.type) && !item.checked) return;
    field[item.name] = item.value;
  });

  return field;
}

function show_readonly() {
  let domain = document.domain;
  if (domain.indexOf("readonly") != -1) {
    $(".show_readonly").show();
  }
}

function init_permission() {
  var wsCache = new WebStorageCache();
  var permission = wsCache.get("user_permission");
  $("button[auth],a[auth],div[auth],li[auth],input[auth]").each(function () {
    //枚举所有 包含[auth]属性的元素
    var _this = $(this);
    var auth = _this.attr("auth"); //获取按钮中的auth属性的值
    if (auth.indexOf("|") > -1) {
      var authList = auth.split("|");
      // 查看authList和permission是否有交集
      var has_auth = permission.filter((x) => authList.includes(x)).length > 0;
    } else {
      var has_auth = $.inArray(auth, permission) > -1;
    }
    if (!has_auth) {
      _this.remove();
    }
  });
  var business_permission = wsCache.get("business_permission");
  $("button[auth_admin],a[auth_admin],div[auth_admin],li[auth_admin]").each(function () {
    //枚举所有 包含[auth]属性的元素
    var _this = $(this);
    var auth_admin = _this.attr("auth_admin"); //获取按钮中的auth属性的值
    if ($.inArray(auth_admin, business_permission) == -1) {
      _this.remove();
    }
  });
  // // let width = $('.layui-form.layui-border-box.layui-table-view').find('td:last-child').width();
  // let width = $('.layui-table-fixed.layui-table-fixed-r').find('.layui-table-body').find('td:last-child').width();
  // console.log('宽度调成: ' + width);
  // $('.layui-form.layui-border-box.layui-table-view').find('th:last-child').find('div').width(Math.max(36,width - 30));
  // $('.layui-table-fixed.layui-table-fixed-r').find('th:last-child').find('div').width(Math.max(36, width - 30));
}

function ajax(opts) {
  //一.设置默认参数
  var defaults = {
    method: "POST",
    url: "",
    data: "",
    username: "",
    password: "",
    processData: true,
    scriptCharset: "",
    async: true,
    cache: true,
    global: true,
    contentType: "application/x-www-form-urlencoded",
    timeout: 60000,
    dataType: "json",
    jsonp: "callback", //jsonpCallback: "jsonpReturn",
    beforeSend: function (XMLHttpRequest) {
      //console.log('beforeSend');
      //console.log(XMLHttpRequest);
      //XMLHttpRequest;   //调用本次ajax请求时传递的options参数
    },
    complete: function (XMLHttpRequest, textStatus) {
      //console.log('complete');
      //console.log(XMLHttpRequest);
      //console.log(textStatus);
      // this;    //调用本次ajax请求时传递的options参数
    },
    dataFilter: function (data, type) {
      //返回处理后的数据
      //console.log('dataFilter');
      return data;
    },
    success: function (data, textStatus) {}, //调用出错执行的函数
    error: function (XMLHttpRequest, textStatus, errorThrown) {
      alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")");
      //console.log(XMLHttpRequest.status);
      //console.log(textStatus);
      //console.log(errorThrown);
      return false;
    },
  };

  //二.用户参数覆盖默认参数
  for (var key in opts) {
    defaults[key] = opts[key];
  }
  //三.对数据进行处理
  // alert(typeof defaults.data);
  // if (typeof defaults.data === 'object') {    //处理 data
  //     // var str = '';
  //     // for (var key in defaults.data) {
  //     //     str += key + '=' + defaults.data[key] + '&';
  //     // }
  //     // defaults.data = str.substring(0, str.length - 1);
  //     defaults.data = jQuery.param(data);
  // }
  defaults.method = defaults.method.toUpperCase(); //处理 method
  defaults.type = defaults.method; //method在JQ1.9版本以前是可行的。在1.9之后的版本，应该使用type:POST
  defaults.cache = defaults.cache ? "" : "&" + new Date().getTime(); //处理 cache
  $.ajax(defaults);
}

function loadScript(url, callback) {
  const script = document.createElement("script");
  script.type = "text/javascript";
  // 处理IE
  if (script.readyState) {
    script.onreadystatechange = function () {
      if (script.readyState === "loaded" || script.readyState === "complete") {
        script.onreadystatechange = null;
        callback();
      }
    };
  } else {
    // 处理其他浏览器的情况
    script.onload = function () {
      callback();
    };
  }
  script.src = url;
  document.body.append(script);
}

function loadJs(id, newJS) {
  var oldjs = null;
  var t = null;
  var oldjs = document.getElementById(id);
  if (oldjs) oldjs.parentNode.removeChild(oldjs);
  var scriptObj = document.createElement("script");
  scriptObj.src = newJS;
  scriptObj.type = "text/javascript";
  scriptObj.id = id;
  document.getElementsByTagName("head")[0].appendChild(scriptObj);
}

function render_template(data, callback) {
  if (!$("#content").length) {
    $("body").append('<div id="content"></div>');
  }
  var html = template("app", data);
  $("#content").html(html);
  //重新渲染form和绑定事件
  var new_element = document.createElement("script");
  new_element.setAttribute("type", "text/javascript");
  new_element.setAttribute("src", "/static/js/plugins/fslayui2/js/frame.js?v=2.2.1");
  document.body.appendChild(new_element);
  layui.use("form", function () {
    var form = layui.form; //只有执行了这一步，部分表单元素才会自动修饰成功
    form.render();
  });
  layui.use("element", function () {
    var element = layui.element;
    element.init();
  });
  init_permission();
  show_readonly();
  typeof callback === "function" && callback();
}

function uuid() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  var uuid = s.join("");
  return uuid;
}

function toDecimal2(x) {
  var f = parseFloat(x);
  if (isNaN(f)) {
    return false;
  }
  var f = Math.round(x * 100) / 100;
  var s = f.toString();
  var rs = s.indexOf(".");
  if (rs < 0) {
    rs = s.length;
    s += ".";
  }
  while (s.length <= rs + 2) {
    s += "0";
  }
  return s;
}

function redirect(url, secs) {
  if (url.indexOf("?") == -1) {
    url = url + "?bid=" + getCookie("bid");
  }
  secs = arguments[1] ? arguments[1] : 0; //设置参数secs的默认值为0
  if (--secs > 0) {
    setTimeout("redirect('" + url + "'," + secs + ")", 1000);
  } else {
    window.location.href = url;
    return true;
  }
}

function reload(secs) {
  secs = arguments[0] ? arguments[0] : 0; //设置参数secs的默认值为0
  setTimeout("window.location.reload()", 1000 * secs);
}

function getQueryStringOrCookie(name) {
  return getQueryString(name) ? getQueryString(name) : getCookie(name);
}

//加法函数
function accAdd(arg1, arg2) {
  var r1, r2, m;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
}

//给Number类型增加一个add方法，，使用时直接用 .add 即可完成计算。
Number.prototype.add = function (arg) {
  return accAdd(arg, this);
};

//减法函数
function Subtr(arg1, arg2) {
  var r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //last modify by deeka
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

//给Number类型增加一个sub方法，，使用时直接用 .sub 即可完成计算。
Number.prototype.sub = function (arg) {
  return Subtr(this, arg);
};

//乘法函数
function accMul(arg1, arg2) {
  var m = 0,
    s1 = arg1.toString(),
    s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) {}
  try {
    m += s2.split(".")[1].length;
  } catch (e) {}
  return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m);
}

//给Number类型增加一个mul方法，使用时直接用 .mul 即可完成计算。
Number.prototype.mul = function (arg) {
  return accMul(arg, this);
};

//除法函数
function accDiv(arg1, arg2) {
  var t1 = 0,
    t2 = 0,
    r1,
    r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {}
  with (Math) {
    r1 = Number(arg1.toString().replace(".", ""));
    r2 = Number(arg2.toString().replace(".", ""));
    return (r1 / r2) * pow(10, t2 - t1);
  }
}

//给Number类型增加一个div方法，，使用时直接用 .div 即可完成计算。
Number.prototype.div = function (arg) {
  return accDiv(this, arg);
};

//数字四舍五入（保留n位小数）
function getFloat(number, n) {
  n = n ? parseInt(n) : 0;
  if (n <= 0) return Math.round(number);
  number = Math.round(number * Math.pow(10, n)) / Math.pow(10, n);
  return number;
}

function get_device_type() {
  if (is_weixin()) {
    return "wechat";
  } else if (is_alipay()) {
    return "alipay";
  } else if (is_miniapp()) {
    return "miniapp";
  } else if (is_mobile()) {
    return "wap";
  } else {
    return "web";
  }
}

function is_weixin() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf("micromessenger") != -1;
}

function is_alipay() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf("alipayclient") != -1;
}

function is_miniapp() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf("miniprogram") != -1;
}

function is_ios() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf("iphone") != -1 || ua.indexOf("ipad") != -1;
}

function is_android() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.indexOf("android") != -1;
}

function is_mobile() {
  var sUserAgent = navigator.userAgent.toLowerCase(),
    bIsIpad = sUserAgent.match(/ipad/i) == "ipad",
    bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os",
    bIsMidp = sUserAgent.match(/midp/i) == "midp",
    bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******",
    bIsUc = sUserAgent.match(/ucweb/i) == "ucweb",
    bIsAndroid = sUserAgent.match(/android/i) == "android",
    bIsCE = sUserAgent.match(/windows ce/i) == "windows ce",
    bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile",
    bIsWebview = sUserAgent.match(/webview/i) == "webview";
  return bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM;
}

function is_id_card_number(sId) {
  if (!/(^\d{15}$)|(^\d{17}(\d|X|x)$)/.test(sId)) {
    console.log("你输入的身份证长度或格式错误");
    return false;
  }
  //身份证城市
  var aCity = {
    11: "北京",
    12: "天津",
    13: "河北",
    14: "山西",
    15: "内蒙古",
    21: "辽宁",
    22: "吉林",
    23: "黑龙江",
    31: "上海",
    32: "江苏",
    33: "浙江",
    34: "安徽",
    35: "福建",
    36: "江西",
    37: "山东",
    41: "河南",
    42: "湖北",
    43: "湖南",
    44: "广东",
    45: "广西",
    46: "海南",
    50: "重庆",
    51: "四川",
    52: "贵州",
    53: "云南",
    54: "西藏",
    61: "陕西",
    62: "甘肃",
    63: "青海",
    64: "宁夏",
    65: "新疆",
    71: "台湾",
    81: "香港",
    82: "澳门",
    91: "国外",
  };
  if (!aCity[parseInt(sId.substr(0, 2))]) {
    console.log("你的身份证地区非法");
    return false;
  }
  // 出生日期验证
  var sBirthday = (sId.substr(6, 4) + "-" + Number(sId.substr(10, 2)) + "-" + Number(sId.substr(12, 2))).replace(/-/g, "/"),
    d = new Date(sBirthday);
  if (sBirthday != d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate()) {
    console.log("身份证上的出生日期非法");
    return false;
  }
  // 身份证号码校验
  var sum = 0,
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
    codes = "10X98765432";
  for (var i = 0; i < sId.length - 1; i++) {
    sum += sId[i] * weights[i];
  }
  var last = codes[sum % 11]; //计算出来的最后一位身份证号码
  if (sId[sId.length - 1] != last) {
    console.log("你输入的身份证号非法");
    return false;
  }
  return true;
}

function is_phone(str) {
  var reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
  if (!reg.test(str)) {
    return false;
  } else {
    return true;
  }
}

var os = (function () {
  var ua = navigator.userAgent,
    isWindowsPhone = /(?:Windows Phone)/.test(ua),
    isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
    isAndroid = /(?:Android)/.test(ua),
    isFireFox = /(?:Firefox)/.test(ua),
    isChrome = /(?:Chrome|CriOS)/.test(ua),
    isTablet = /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox && /(?:Tablet)/.test(ua)),
    isPhone = /(?:iPhone)/.test(ua) && !isTablet,
    isPc = !isPhone && !isAndroid && !isSymbian;
  return {
    isTablet: isTablet,
    isPhone: isPhone,
    isAndroid: isAndroid,
    isPc: isPc,
  };
})();

function show_layui_form_tpl(data) {
  var path = window.location.pathname;
  var path_info = path.split("/");
  var location_url = location.search; //获取url中"?"符后的字串
  var url = "/" + path_info[1] + "/" + path_info[2] + "/index";
  if (location_url.indexOf("?") != -1) {
    var str_after = location_url.split("?")[1];
    url = url + "?" + str_after;
  }
  var defaults = {
    // module: path_info[1],
    // controller: path_info[2],
    url: url,
    checkbox: false,
    number: false,
    listen: 1,
    toolbar_width: "auto",
    is_page: 1,
    date_input: { field: "create_time", placeholder: "时间" },
  };
  //二.用户参数覆盖默认参数
  for (var key in data) {
    defaults[key] = data[key];
  }

  //console.log(defaults);
  if (!$("#table").length) {
    $("body").append('<div id="table"></div>');
  }
  var htmls = "";
  htmls += "<div class='layui-fluid min-width-1120'>";
  htmls += "<div class='layui-card'>";
  htmls += "<div class='layui-card-body'>";
  htmls += "<div id='layui_form_query'>";
  htmls += "</div>";
  htmls += "</div>";
  htmls += "</div>";
  htmls += "</div>";
  $("#table").html(htmls);
  var html = template("layui_form_query_tpl", defaults);
  $("#layui_form_query").html(html);
  init_permission();
}

function render_template_beta(data, id) {
  let tpl_id = id + "_tpl";
  var html = template(tpl_id, data);
  // $("#" + id).html(html);
  const element = document.getElementById(id);
  if (element) {
    element.innerHTML = html;
  }
  //表单必填自动打星号
  $('[lay-verify^="required"]').each(function () {
    let _this = $(this);
    _this.parent().prev().addClass("required");
  });
}

function export_note(module, controller, export_url) {
  var p = $("#query_form").serialize();
  window.location.href = "/" + module + "/" + controller + "/" + export_url + "?" + p;
  return true;
}

function export_note_url(export_url) {
  var params = $("#query_form").serialize();
  window.location.href = export_url + "?" + params;
  return true;
}

function get_checkbox_values(checkbox_name) {
  var checks = document.getElementsByName(checkbox_name);
  var ids = "";
  if (checks && checks.length > 0) {
    for (var i = 0; i < checks.length; i++) {
      if (checks[i].checked) {
        if (ids.length > 0) ids += ",";
        ids += checks[i].value;
      }
    }
  }
  return ids;
}

function post_weui_admin_api_v1(url, data, success, failed) {
  let api_url = "/admin_api/v1" + url;
  let access_token;
  let access_token_name = "business_user_token";
  let wsCache = new WebStorageCache();
  if (getQueryString(access_token_name)) {
    access_token = getQueryString(access_token_name);
  } else if (wsCache.get(access_token_name)) {
    access_token = wsCache.get(access_token_name);
  } else if (getCookie(access_token_name)) {
    access_token = getCookie(access_token_name);
  }
  if (access_token) {
    let link_str = url.indexOf("?") == -1 ? "?" : "&";
    api_url += link_str + access_token_name + "=" + access_token;
  }
  post_weui(api_url, data, success, failed);
}

function post_layui_admin_api_v1(url, data, success, failed) {
  let api_url = "/admin_api/v1" + url;
  let access_token;
  let access_token_name = "business_user_token";
  let wsCache = new WebStorageCache();
  if (getQueryString(access_token_name)) {
    access_token = getQueryString(access_token_name);
  } else if (wsCache.get(access_token_name)) {
    access_token = wsCache.get(access_token_name);
  } else if (getCookie(access_token_name)) {
    access_token = getCookie(access_token_name);
  }
  if (access_token) {
    let link_str = url.indexOf("?") == -1 ? "?" : "&";
    api_url += link_str + access_token_name + "=" + access_token;
  }
  post_layui(api_url, data, success, failed);
}

function post_weui_member_api_v1(url, data, success, failed) {
  post_weui("/member_api/v1" + url, data, success, failed);
}

function post_layui_member_api_v1(url, data, success, failed) {
  post_layui("/member_api/v1" + url, data, success, failed);
}

function post_layui_api_api_v1(url, data, success, failed) {
  post_layui("/api/v1" + url, data, success, failed);
}

function post_jqweui_member_api_v1(url, data, success, failed) {
  ajax({
    url: "/member_api/v1" + url,
    data: data,
    beforeSend: function (XMLHttpRequest) {
      $.showLoading("加载中");
    },
    complete: function () {
      $.hideLoading();
    },
    success: function (result) {
      if (result.code === 0) {
        if (success) {
          success(result);
        } else {
          $.alert(result.msg, "提示");
        }
      } else {
        if (failed) {
          failed(result);
        } else {
          $.alert(result.msg, "提示");
        }
      }
    }, //调用出错执行的函数
    error: function (XMLHttpRequest, textStatus, errorThrown) {
      console.log(XMLHttpRequest.status);
      console.log(XMLHttpRequest.responseJSON);
      console.log(textStatus);
      console.log(errorThrown);
      let message = XMLHttpRequest.responseJSON && XMLHttpRequest.responseJSON.message ? XMLHttpRequest.responseJSON.message : "";
      alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")\r\n" + message);
      return false;
    },
  });
}

function post_layui(url, data, success, failed) {
  layui.use("layer", function () {
    var layer = layui.layer;
    // var index = layer.msg('加载中', {
    //     icon: 16, shade: 0.01, time: false
    // });
    let index;
    // 检查 _loading 字段
    if (data._loading === false) {
      delete data._loading; // 剔除 _loading 字段
    } else {
      index = layer.load(2);
    }

    ajax({
      url: url,
      data: data,
      complete: function () {
        index && layer.close(index);
      },
      success: function (result) {
        // console.log(result);
        if (result.code === 0) {
          if (success) {
            success(result);
          } else {
            layer.msg(result.msg, {
              time: 2000, //2秒关闭（如果不配置，默认是3秒）
            });
          }
        } else {
          if (failed) {
            failed(result);
          } else {
            layer.alert(result.msg);
          }
        }
      }, //调用出错执行的函数
      error: function (XMLHttpRequest, textStatus, errorThrown) {
        let message = XMLHttpRequest.responseJSON && XMLHttpRequest.responseJSON.message ? XMLHttpRequest.responseJSON.message : "";
        layer.alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")\r\n" + message);
        //console.log(XMLHttpRequest.status);
        //console.log(textStatus);
        //console.log(errorThrown);
        return false;
      },
    });
  });
}

function post_weui(url, data, success, failed) {
  //不自动关闭的loading层
  var loading = weui.loading("加载中");
  ajax({
    url: url,
    data: data,
    beforeSend: function () {},
    success: function (result) {
      //console.log(result);
      if (result.code === 0) {
        if (success) {
          success(result);
        } else {
          weui.alert(result.msg);
        }
      } else {
        if (failed) {
          failed(result);
        } else {
          weui.alert(result.msg);
        }
      }
    }, //调用执行后调用的函数
    complete: function (XMLHttpRequest, textStatus) {
      //console.log(XMLHttpRequest.status);
      //console.log(textStatus);
      loading.hide(); //关闭loading
    }, //调用出错执行的函数
    error: function (XMLHttpRequest, textStatus, errorThrown) {
      let message = XMLHttpRequest.responseJSON && XMLHttpRequest.responseJSON.message ? XMLHttpRequest.responseJSON.message : "";
      weui.alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")\r\n" + message);
      //console.log(XMLHttpRequest.status);
      //console.log(textStatus);
      //console.log(errorThrown);
      return false;
      //请求出错处理
    },
  });
}

function post_hui(url, data, success, failed) {
  hui.loading("正在加载");
  ajax({
    url: "/member_api/v1" + url,
    data: data,
    complete: function () {
      // hui.loading(false, true);
      hui.closeLoading();
    },
    success: function (result) {
      if (result.code === 0) {
        if (success) {
          success(result);
        } else {
          hui.alert(result.msg);
        }
      } else {
        if (failed) {
          failed(result);
        } else {
          hui.alert(result.msg);
        }
      }
    }, //调用出错执行的函数
    error: function (XMLHttpRequest, textStatus, errorThrown) {
      hui.alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")");
      //console.log(XMLHttpRequest.status);
      //console.log(textStatus);
      //console.log(errorThrown);
      return false;
    },
  });
}

function ajax_api(url, data, success, failed) {
  ajax({
    url: url,
    data: data,
    beforeSend: function () {},
    success: function (result) {
      //console.log(result);
      if (result.code === 0) {
        if (success) {
          success(result);
        } else {
          alert(result.msg);
        }
      } else {
        if (failed) {
          failed(result);
        } else {
          alert(result.msg);
        }
      }
    }, //调用执行后调用的函数
    complete: function (XMLHttpRequest, textStatus) {
      //console.log(XMLHttpRequest.status);
      console.log(textStatus);
      // loading.hide();//关闭loading
    }, //调用出错执行的函数
    error: function (XMLHttpRequest, textStatus, errorThrown) {
      let message = XMLHttpRequest.responseJSON && XMLHttpRequest.responseJSON.message ? XMLHttpRequest.responseJSON.message : "";
      alert("后台出错了(" + errorThrown + " " + XMLHttpRequest.status + ")\r\n" + message);
      console.log(XMLHttpRequest.status);
      console.log(textStatus);
      console.log(errorThrown);
      return false;
      //请求出错处理
    },
  });
}

function ajax_admin_api_v1(url, data, success, failed) {
  //不自动关闭的loading层
  // var loading = weui.loading('加载中');
  ajax_api("/admin_api/v1" + url, data, success, failed);
}

function ajax_member_api_v1(url, data, success, failed) {
  ajax_api("/member_api/v1" + url, data, success, failed);
}

/**
 * Vant UI 框架的请求封装（支持回调和Promise两种风格）
 * 使用 vant.showLoadingToast 和 vant.showToast
 *
 * 回调风格：post_vant_member_api_v1(url, data, success, failed)
 * Promise风格：post_vant_member_api_v1(url, data).then().catch()
 */
function post_vant_member_api_v1(url, data, success, failed) {
  // 如果没有传回调函数，返回Promise
  if (!success && !failed) {
    return new Promise((resolve, reject) => {
      post_vant_member_api_v1(url, data, resolve, reject);
    });
  }

  // 显示加载提示
  const loadingToast = window.vant.showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0,
  });

  // 使用现有的ajax方法
  ajax({
    url: "/member_api/v1" + url,
    data: data,
    method: "POST",
    success: function (result) {
      // 关闭加载提示
      loadingToast.close();

      console.log("API响应:", result);

      if (result.code === 0) {
        // 成功回调
        if (success && typeof success === "function") {
          success(result);
        }
      } else {
        // 失败处理
        const errorMsg = result.msg || "请求失败";
        window.vant.showToast(errorMsg);

        if (failed && typeof failed === "function") {
          failed(result);
        }
      }
    },
    error: function (xhr, status, error) {
      // 关闭加载提示
      loadingToast.close();

      console.error("请求错误:", xhr, status, error);

      // 使用通用函数构建详细的错误信息
      const errorMsg = buildDetailedErrorMessage(xhr, status, error);

      window.vant.showToast(errorMsg);

      if (failed && typeof failed === "function") {
        failed({ code: xhr.status || -1, msg: errorMsg });
      }
    },
  });
}

/**
 * Vant UI 框架的静默请求封装（不显示loading，支持回调和Promise两种风格）
 *
 * 回调风格：ajax_vant_member_api_v1(url, data, success, failed)
 * Promise风格：ajax_vant_member_api_v1(url, data).then().catch()
 */
function ajax_vant_member_api_v1(url, data, success, failed) {
  // 如果没有传回调函数，返回Promise
  if (!success && !failed) {
    return new Promise((resolve, reject) => {
      ajax_vant_member_api_v1(url, data, resolve, reject);
    });
  }

  // 使用现有的ajax方法
  ajax({
    url: "/member_api/v1" + url,
    data: data,
    method: "POST",
    success: function (result) {
      console.log("API响应:", result);

      if (result.code === 0) {
        if (success && typeof success === "function") {
          success(result);
        }
      } else {
        if (failed && typeof failed === "function") {
          failed(result);
        }
      }
    },
    error: function (xhr, status, error) {
      console.error("请求错误:", xhr, status, error);

      // 使用通用函数构建详细的错误信息
      const errorMsg = buildDetailedErrorMessage(xhr, status, error);

      if (failed && typeof failed === "function") {
        failed({ code: xhr.status || -1, msg: errorMsg });
      }
    },
  });
}

function url_encode(clearString) {
  var output = "";
  var x = 0;

  clearString = utf16to8(clearString.toString());
  var regex = /(^[a-zA-Z0-9-_.]*)/;

  while (x < clearString.length) {
    var match = regex.exec(clearString.substr(x));
    if (match != null && match.length > 1 && match[1] != "") {
      output += match[1];
      x += match[1].length;
    } else {
      if (clearString[x] == " ") output += "+";
      else {
        var charCode = clearString.charCodeAt(x);
        var hexVal = charCode.toString(16);
        output += "%" + (hexVal.length < 2 ? "0" : "") + hexVal.toUpperCase();
      }
      x++;
    }
  }

  function utf16to8(str) {
    var out, i, len, c;

    out = "";
    len = str.length;
    for (i = 0; i < len; i++) {
      c = str.charCodeAt(i);
      if (c >= 0x0001 && c <= 0x007f) {
        out += str.charAt(i);
      } else if (c > 0x07ff) {
        out += String.fromCharCode(0xe0 | ((c >> 12) & 0x0f));
        out += String.fromCharCode(0x80 | ((c >> 6) & 0x3f));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
      } else {
        out += String.fromCharCode(0xc0 | ((c >> 6) & 0x1f));
        out += String.fromCharCode(0x80 | ((c >> 0) & 0x3f));
      }
    }
    return out;
  }

  return output;
}

function url_decode(encodedString) {
  var output = encodedString;
  var binVal, thisString;
  var myregexp = /(%[^%]{2})/;

  function utf8to16(str) {
    var out, i, len, c;
    var char2, char3;

    out = "";
    len = str.length;
    i = 0;
    while (i < len) {
      c = str.charCodeAt(i++);
      switch (c >> 4) {
        case 0:
        case 1:
        case 2:
        case 3:
        case 4:
        case 5:
        case 6:
        case 7:
          out += str.charAt(i - 1);
          break;
        case 12:
        case 13:
          char2 = str.charCodeAt(i++);
          out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
          break;
        case 14:
          char2 = str.charCodeAt(i++);
          char3 = str.charCodeAt(i++);
          out += String.fromCharCode(((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | ((char3 & 0x3f) << 0));
          break;
      }
    }
    return out;
  }

  while ((match = myregexp.exec(output)) != null && match.length > 1 && match[1] != "") {
    binVal = parseInt(match[1].substr(1), 16);
    thisString = String.fromCharCode(binVal);
    output = output.replace(match[1], thisString);
  }

  //output = utf8to16(output);
  output = output.replace(/\\+/g, " ");
  output = utf8to16(output);
  return output;
}

$.fn.serializeObject = function () {
  var o = {};
  var a = this.serializeArray();
  $.each(a, function () {
    if (o[this.name]) {
      if (!o[this.name].push) {
        o[this.name] = [o[this.name]];
      }
      o[this.name].push(this.value || "");
    } else {
      o[this.name] = this.value || "";
    }
  });
  return o;
};

function isEmpty(obj) {
  if (obj === null || obj == undefined || obj === "") {
    return true;
  }
  if (!obj && obj !== 0 && obj !== "") {
    return false;
  }
  if (Array.prototype.isPrototypeOf(obj) && obj.length === 0) {
    return true;
  }
  if (Object.prototype.isPrototypeOf(obj) && Object.keys(obj).length === 0) {
    return true;
  }
  return false;
}

/**
 * 检测微信JsAPI
 * @param callback
 */
function detectWeixinApi(callback) {
  if (typeof window.WeixinJSBridge == "undefined" || typeof window.WeixinJSBridge.invoke == "undefined") {
    setTimeout(function () {
      detectWeixinApi(callback);
    }, 200);
  } else {
    callback();
  }
}

function broadcast(file_path) {
  file_path = arguments[0] ? arguments[0] : "/statics/order.wav"; //设置参数secs的默认值为0
  if (!document.getElementById("audio")) {
    //添加audio标签
    let f = document.createDocumentFragment();
    let audio = document.createElement("audio");
    audio.style = "display:none";
    audio.id = "audio";
    audio.controls = "controls";
    audio.autoplay = "autoplay";
    let source = document.createElement("source");
    source.id = "tts_source";
    source.type = "audio/mpeg";
    source.src = file_path;
    audio.appendChild(source);
    f.appendChild(audio);
    document.body.appendChild(f);
  }
  let tts_source = document.getElementById("tts_source");
  return tts_source.parentNode.load();
}

function tts(content) {
  let url = "https://tts.baidu.com/text2audio/text2audio?lan=zh&ie=UTF-8&spd=8&text=";
  if (!document.getElementById("audio")) {
    //百度TTS地址
    //添加audio标签
    let f = document.createDocumentFragment();
    let audio = document.createElement("audio");
    audio.style = "display:none";
    audio.id = "audio";
    audio.controls = "controls";
    audio.autoplay = "autoplay";
    let source = document.createElement("source");
    source.id = "tts_source";
    source.type = "audio/mpeg";
    source.src = url;
    audio.appendChild(source);
    f.appendChild(audio);
    document.body.appendChild(f);
  }
  let tts_source = document.getElementById("tts_source");
  tts_source.src = url + content;
  return tts_source.parentNode.load();
}

//格式化时间方法
function formatDateTime(format) {
  let date = new Date();
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss.S";
  }

  let o = {
    "M+": date.getMonth() + 1, // month
    "d+": date.getDate(), // day
    "H+": date.getHours(), // hour
    "h+": date.getHours(), // hour
    "m+": date.getMinutes(), // minute
    "s+": date.getSeconds(), // second
    "q+": Math.floor((date.getMonth() + 3) / 3), // quarter
    "S+": date.getMilliseconds(),
  };

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      let str = "" + o[k];
      if (k === "S+") {
        let str = "" + o[k];
        format = format.replace(RegExp.$1, str.length === 3 ? o[k] : (o[k] + "000").substring(0, 3));
      } else {
        format = format.replace(RegExp.$1, str.length == 2 ? o[k] : ("00" + o[k]).substr(str.length));
      }
    }
  }
  return format;
}

/**
 * 获取表单数据
 * @param {*} filter lay-filter属性值
 * @param {*} itemForm dom对象
 */
function getFormData(filter, itemForm) {
  itemForm = itemForm || $('.layui-form[lay-filter="' + filter + '"]').eq(0);

  var nameIndex = {}, //数组 name 索引
    field = {},
    fieldElem = itemForm.find("input,select,textarea"); //获取所有表单域

  layui.each(fieldElem, function (_, item) {
    item.name = (item.name || "").replace(/^\s*|\s*&/, "");

    if (!item.name) return;

    //用于支持数组 name
    if (/^.*\[\]$/.test(item.name)) {
      var key = item.name.match(/^(.*)\[\]$/g)[0];
      nameIndex[key] = nameIndex[key] | 0;
      item.name = item.name.replace(/^(.*)\[\]$/, "$1[" + nameIndex[key]++ + "]");
    }

    if (/^checkbox|radio$/.test(item.type) && !item.checked) return;
    field[item.name] = item.value;
  });

  return field;
}
/**
 *对Date的扩展，将 Date 转化为指定格式的String
 *月(M)、日(d)、小时(h)、分(m)、秒(s)、季度(q) 可以用 1-2 个占位符，
 *年(y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
 *例子：
 *(new Date()).Format("yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
 *(new Date()).Format("yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
 */
Date.prototype.format = function (fmt) {
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "h+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (var k in o) if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
  return fmt;
};
