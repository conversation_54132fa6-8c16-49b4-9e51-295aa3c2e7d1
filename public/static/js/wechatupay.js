var wsCache = new WebStorageCache();
$(function () {
    //console.log('begin');
    //history.pushState(null, null, 'http://m.yikayi.net/pay/#!/order/orderlist')
    $(".amount-box").on("touchstart", function (event) {
        $("#keyboard").show();
    });
    var kb = $("#keyboard").keyboard({
        onShow: function () {
            console.log("show kw,value");
        },
        onChange: function (val) {
            let bid = getQueryString('bid');
            let pay_config = wsCache.get(bid + '_pay_config');
            let service_charge_rate = pay_config.parameter.service_charge_rate;
            $("#order-coupon-amt").html((val * service_charge_rate).toFixed(2));
            $("#total-amt").html((val * (1 + service_charge_rate)).toFixed(2));
            $("#paid_value").html('-' + val);
            $("#keyboard-text").html(val);
        },
        onConfirm: function (value) {
            parseFloatvalue = parseFloat(value);
            if (!parseFloatvalue || !value) {
                return false;
            }
            $(".loading").show();
            $("#pay").html("正在<br>支付");
            //开始下单
            url = "/member_api/v1/upay/create_order";
            var data = {};
            data = parseQueryString(); // url参数直接透传到后端,这样无需反复修改
            // data.bid = getQueryString('bid');
            // data.store_guid = getQueryString('store_guid');
            // data.user_account = getQueryString('user_account');
            // data.memo = getQueryString('memo');
            data.total_fee = $("#keyboard-text").html();
            data.service_charge = $("#service_charge").html();
            data.value_balance = $("#value_balance").html();
            ajax({
                    url: url,
                    method: 'POST',
                    data: data,
                    complete: function () {
                        /* 移除loading */
                        $(".loading").hide();
                        //  alert('complete');
                    },
                    success: function (result) {
                        console.log(result);
                        if (result.code !== 0) {
                            alert(result.msg);
                            return false;
                        }
                        if (result.data.success !== true) {
                            alert(result.data.message);
                            return false;
                        }
                        let redirectUrl = result.data.redirectUrl;
                        let payType = result.data.payType;
                        switch (payType) {
                            case 3:
                                //储值支付
                                window.location.href = redirectUrl;
                                return true;
                            case 2:
                                //微信支付
                                jsApiCall(result.data.data, redirectUrl);
                                return true;
                            default:
                                alert('未知的支付方式:payType=' + payType);
                                return false;
                        }
                    }
                }
            );
            console.log("onConfirm kw value:" + value);
            return true;
        }
    });
    kb.showKb();
    $("#display").on("touchstart", function (event) {
        event.preventDefault();
        kb.showKb();
    });
});


//调用微信JS api 支付
function jsApiCall(options, redirectUrl) {
    console.log(options);
    console.log(options.appId);
    WeixinJSBridgeInit(function () {
        WeixinJSBridge.invoke(
            'getBrandWCPayRequest',
            {
                "appId": options.appId,
                "nonceStr": options.nonceStr,
                "package": options.package,
                "paySign": options.paySign,
                "signType": options.signType,
                "timeStamp": options.timeStamp
            },
            function (res) {
                var last = JSON.stringify(res); //将JSON对象转化为JSON字符
                // 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回    ok，但并不保证它绝对可靠。
                //WeixinJSBridge.log(res.err_msg);
                if (res.err_msg == "get_brand_wcpay_request:ok") {
                    // var bill_no = options.package.replace('prepay_id=', '');
                    // var bid = getQueryString('bid');
                    // var location_url = "/member/pay/pay_success.html?bill_no=" + bill_no + '&appid=' + options.appId + '&bid=' + bid;
                    // alert(redirectUrl);
                    window.location.href = redirectUrl;
                    return false;
                } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
                    $("#pay").html("确定<br>支付");
                    // alert('您取消了支付');
                } else if (res.err_msg == "get_brand_wcpay_request:fail") {
                    $("#pay").html("重新<br>支付");
                    alert('支付失败:~' + res.err_desc);
                } else {
                    $("#pay").html("确定<br>支付");
                    alert('状态未知~' + res.err_msg);
                }
            })
    });
}

//init
function WeixinJSBridgeInit(f) {
    (typeof WeixinJSBridge == 'undefined' || (typeof WeixinJSBridge.invoke == 'undefined')) ? setTimeout('WeixinJSBridgeInit(' + f + ')', 200) : f()
}

//默认隐藏分享按钮
WeixinJSBridgeInit(function () {
    WeixinJSBridge.call("hideOptionMenu");
    WeixinJSBridge.call("hideToolbar");
});

function reload() {
    return window.location.reload();
}
