body
{
	background:#FCFCFC;
}
.say-btn
{
	text-align:right;
	margin-top:12px;
}

#dialog
{
	/*min-height:600px;*/
	background:#EEEEEE;
	max-height: 80vh;
	overflow: scroll;
}

.textarea
{
    /*height:2em;*/
	width:100%;
}

#userlist
{
	/*min-height:600px;*/
	background:#EEEEEE;
}

#userlist > li
{
	color:#1372A2;
	list-style:none;
	margin-left:12px;
}

#userlist > h4
{
    text-align:center;
	font-size:14px;
	font-weight:nold;
}

.words
{
	margin:8px;
}


.triangle-isosceles {
    position:relative;
    padding:10px;
    margin:10px 0 15px;
    color:#000;
    background:#D3FF93; /* default background for browsers without gradient support */
    background:-webkit-gradient(linear, 0 0, 0 100%, from(#EFFFD7), to(#D3FF93));
    background:-moz-linear-gradient(#EFFFD7, #D3FF93);
    background:-o-linear-gradient(#EFFFD7, #D3FF93);
    background:linear-gradient(#EFFFD7, #D3FF93);
    -webkit-border-radius:10px;
    -moz-border-radius:10px;
    border-radius:10px;
    -moz-box-shadow:1px 1px 2px hsla(0, 0%, 0%, 0.3);  
    -webkit-box-shadow:1px 1px 2px hsla(0, 0%, 0%, 0.3);  
     box-shadow:1px 1px 2px hsla(0, 0%, 0%, 0.3);  
}

    .triangle-isosceles:hover{  
        top:-2px;  
        left:-2px;  
        -moz-box-shadow:3px 3px 2px hsla(0, 0%, 0%, 0.3);  
        -webkit-box-shadow:3px 3px 2px hsla(0, 0%, 0%, 0.3);  
        box-shadow:3px 3px 2x hsla(0, 0%, 0%, 0.3);  
    }  

.triangle-isosceles.top {
    background:-webkit-gradient(linear, 0 0, 0 100%, from(#D3FF93), to(#EFFFD7));
    background:-moz-linear-gradient(#D3FF93, #EFFFD7);
    background:-o-linear-gradient(#D3FF93, #EFFFD7);
    background:linear-gradient(#D3FF93, #EFFFD7);
}

.triangle-isosceles:after {
    content:"";
    position:absolute;
    bottom:-9px;
    left:15px;
    border-width:9px 21px 0; 
    border-style:solid;
    border-color:#D3FF93 transparent;
    display:block; 
    width:0;
}
.triangle-isosceles.top:after {
    top:-9px;
    left:15px;
    bottom:auto;
    border-width:0 9px 9px;
    border-color:#D3FF93 transparent;
}

.speech_item img{
    max-height: 300px;
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
}

.user_icon
{
	float:left;border:1px solid #DDDDDD;padding:2px;margin:0 5px 0 5px;
}

.cp
{
	color:#888888;
	text-align:center;
	font-size:11px;
}

.thumbnail
{
	border:1px solid #CCCCCC;
}

#sinaEmotion .item{
    margin: 2px;
    height: 25px;
}

#sinaEmotion .face{
    padding: 1px 2px;
    height: 25px;
}

img.sina-emotion{
    display: inline;
}
