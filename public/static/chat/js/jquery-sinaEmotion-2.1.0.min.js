/*!
 * j<PERSON><PERSON>y <PERSON> Emotion v2.1.0
 * http://www.clanfei.com/
 *
 * Copyright 2012-2014 Lanfei
 * Released under the MIT license
 *
 * Date: 2014-05-19T20:10:23+0800
 */
(function(h){var b;var n;var i;var j;var d;var a=[];var e="默认";var k=function(){h("body").bind({click:function(){h("#sinaEmotion").hide()}});h("#sinaEmotion").bind({click:function(o){o.stopPropagation()}}).delegate(".prev",{click:function(o){var p=h("#sinaEmotion .categories").data("page");f(p-1);o.preventDefault()}}).delegate(".next",{click:function(o){var p=h("#sinaEmotion .categories").data("page");f(p+1);o.preventDefault()}}).delegate(".category",{click:function(o){h("#sinaEmotion .categories .current").removeClass("current");g(h.trim(h(this).addClass("current").text()));o.preventDefault()}}).delegate(".page",{click:function(o){h("#sinaEmotion .pages .current").removeClass("current");var p=parseInt(h(this).addClass("current").text()-1);c(p);o.preventDefault()}}).delegate(".face",{click:function(o){h("#sinaEmotion").hide();b.insertText(h(this).children("img").prop("alt"));o.preventDefault()}})};var m=function(o){if(i){o&&o();return}if(!n){n=h.fn.sinaEmotion.options}i={};j=[];d={};h("body").append('<div id="sinaEmotion">正在加载，请稍后...</div>');k();h.getJSON("https://api.weibo.com/2/emotions.json?callback=?",{source:n.appKey,language:n.language},function(r){var t,s;var u=r.data;h("#sinaEmotion").html('<div class="right"><a href="#" class="prev">&laquo;</a><a href="#" class="next">&raquo;</a></div><ul class="categories"></ul><ul class="faces"></ul><ul class="pages"></ul>');for(var q=0,p=u.length;q<p;++q){t=u[q];s=t.category||e;if(!i[s]){i[s]=[];j.push(s)}i[s].push({icon:t.icon,phrase:t.phrase});d[t.phrase]=t.icon}h(a).parseEmotion();a=null;o&&o()})};var f=function(u){var r="";var t=j.length;var p=Math.ceil(t/5);var o=h("#sinaEmotion .categories");var s=o.data("category")||e;u=(u+p)%p;for(var q=u*5;q<t&&q<(u+1)*5;++q){r+='<li class="item"><a href="#" class="category'+(s==j[q]?" current":"")+'">'+j[q]+"</a></li>"}o.data("page",u).html(r)};var g=function(o){h("#sinaEmotion .categories").data("category",o);c(0);l()};var c=function(u){var v;var t="";var p="";var w=n.rows;var o=h("#sinaEmotion .categories").data("category");var q=i[o];u=u||0;for(var s=u*w,r=q.length;s<r&&s<(u+1)*w;++s){v=q[s];t+='<li class="item"><a href="#" class="face"><img class="sina-emotion" src="'+v.icon+'" alt="'+v.phrase+'" /></a></li>'}h("#sinaEmotion .faces").html(t)};var l=function(){var r="";var u=n.rows;var t=h("#sinaEmotion .categories").data("category");var o=i[t];var s=o.length;if(s>u){for(var q=0,p=Math.ceil(s/u);q<p;++q){r+='<li class="item"><a href="#" class="page'+(q==0?" current":"")+'">'+(q+1)+"</a></li>"}h("#sinaEmotion .pages").html(r).show()}else{h("#sinaEmotion .pages").hide()}};h.fn.sinaEmotion=function(p){p=p||function(){return h(this).parents("form").find("textarea,input[type=text]").eq(0)};var o=h(this).last();var q=o.offset();if(o.is(":visible")){if(typeof p=="function"){b=p.call(o)}else{b=h(p)}m(function(){g(e);f(0)});h("#sinaEmotion").css({top:q.top+o.outerHeight()+5,left:q.left}).show()}return this};h.fn.parseEmotion=function(){if(!j){a=h(this);m()}else{if(j.length==0){a=a.add(h(this))}else{h(this).each(function(){var p=h(this);var o=p.html();o=o.replace(/<.*?>/g,function(q){q=q.replace("[","&#91;");q=q.replace("]","&#93;");return q}).replace(/\[[^\[\]]*?\]/g,function(q){var r=d[q];if(r){return'<img class="sina-emotion" src="'+r+'" alt="'+q+'" />'}return q});p.html(o)})}}return this};h.fn.insertText=function(o){this.each(function(){if(this.tagName!=="INPUT"&&this.tagName!=="TEXTAREA"){return}if(document.selection){this.focus();var q=document.selection.createRange();q.text=o;q.collapse();q.select()}else{if(this.selectionStart!==undefined){var r=this.selectionStart;var p=this.selectionEnd;this.value=this.value.substring(0,r)+o+this.value.substring(p,this.value.length);this.selectionStart=this.selectionEnd=r+o.length}else{this.value+=o}}});return this};h.fn.sinaEmotion.options={rows:72,language:"cnname",appKey:"1362404091"}})(jQuery);