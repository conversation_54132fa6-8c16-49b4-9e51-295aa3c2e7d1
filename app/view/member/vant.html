<!DOCTYPE html>
<html>
<head>
  <!-- ===== 方案1: 性能优化版本 ===== -->

  <!-- 1. 基础meta标签 - 最高优先级 -->
  <meta charset="utf-8"/>
  <title>首页</title>
  <meta name="renderer" content="webkit"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
  <meta name="format-detection" content="telephone=no"/>
  <meta content="always" name="referrer"/>
  <link rel="shortcut icon" href='/favicon.ico?v=1' type=image/x-icon>

  <!-- 2. 关键CSS - 同步加载，阻塞渲染但必需 -->
  <link rel="stylesheet" href="/static/vant/index.css?v=4.9.21" media="all"/>
 </head>
<body>
  <!-- ===== 方案1: 关键脚本优先加载 ===== -->

  <!-- 1. 核心依赖库 - 同步加载，确保后续脚本可用 -->
  <script src="//file.yikayi.net/static/js/jquery.min.js?v=2.2.4"></script>
  <script src="//file.yikayi.net/static/js/vue.global.prod.js?v=3.5.18"></script>
  <script src="/static/vant/vant.js?v=4.9.21"></script>

  <!-- 2. 业务工具库 - 同步加载，确保API函数可用 -->
<!--  <script src="/static/js/template-web.js?v=4.13.2"></script>-->
<!--  <script src="/static/js/web-storage-cache.min.js?v=1.1.0"></script>-->
  <script src="/static/js/function.js?v=20250808"></script>
<!--  <script src="/static/js/member/common.js?v=2025080401"></script>-->

  <!-- 3. 页面内容 - 在所有依赖加载完成后渲染 -->
  {__CONTENT__}

  <!-- ===== 非关键脚本异步加载 ===== -->
  <script>
    // 1. 统计代码异步加载 - 不阻塞页面渲染
    (function() {
      var _paq = window._paq = window._paq || [];
      _paq.push(['trackPageView']);
      _paq.push(['enableLinkTracking']);

      // 延迟加载统计脚本
      setTimeout(function() {
        var d = document, g = d.createElement('script'), s = d.getElementsByTagName('script')[0];
        g.async = true;
        g.src = '//trace.ecarde.cn/matomo.js';
        g.onload = function() {
          _paq.push(['setTrackerUrl', '//trace.ecarde.cn/matomo.php']);
          _paq.push(['setSiteId', '2']);
        };
        s.parentNode.insertBefore(g, s);
      }, 100);
    })();

    // 2. 页面初始化 - DOMContentLoaded优先于window.onload
    document.addEventListener('DOMContentLoaded', function() {
      // 调试工具条件加载
      if (typeof getQueryStringOrCookie !== 'undefined' && getQueryStringOrCookie("debug") == 1) {
        var script = document.createElement('script');
        script.src = '/static/js/eruda.min.js?v=2.4.1';
        script.onload = function() { eruda.init(); };
        document.head.appendChild(script);
      }
    });

    // 3. 微信环境优化
    function initWeixinFeatures() {
      function handleFontSize() {
        if (typeof WeixinJSBridge !== 'undefined') {
          WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 });
          WeixinJSBridge.on('menu:setfont', function() {
            WeixinJSBridge.invoke('setFontSizeCallback', { fontSize: 0 });
          });
        }
      }

      if (typeof WeixinJSBridge === 'object') {
        handleFontSize();
      } else {
        document.addEventListener('WeixinJSBridgeReady', handleFontSize, false);
      }
    }

    // 延迟初始化微信功能
    setTimeout(initWeixinFeatures, 50);
  </script>
  {if !empty($options)}
  <!-- 微信JS-SDK动态加载 -->
  <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
  <script>
    /*
     * 注意：
     * 1. 所有的JS接口只能在公众号绑定的域名下调用，公众号开发者需要先登录微信公众平台进入“公众号设置”的“功能设置”里填写“JS接口安全域名”。
     * 2. 如果发现在 Android 不能分享自定义内容，请到官网下载最新的包覆盖安装，Android 自定义分享接口需升级至 ******** 版本及以上。
     * 3. 常见问题及完整 JS-SDK 文档地址：https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
     *
     * 开发中遇到问题详见文档“附录5-常见错误及解决办法”解决，如仍未能解决可通过以下渠道反馈：
     * 邮箱地址：<EMAIL>
     * 邮件主题：【微信JS-SDK反馈】具体问题
     * 邮件内容说明：用简明的语言描述问题所在，并交代清楚遇到该问题的场景，可附上截屏图片，微信团队会尽快处理你的反馈。
     */
    wx.config({
      debug: false,
      appId: "{$options.appId}",
      timestamp: "{$options.timestamp}",
      nonceStr: "{$options.nonceStr}",
      signature: "{$options.signature}",
      jsApiList: [
        // 所有要调用的 API 都要加到这个列表中
        "checkJsApi",
        "updateAppMessageShareData",
        "updateTimelineShareData",
        "onMenuShareTimeline",
        "onMenuShareAppMessage",
        "onMenuShareQQ",
        "onMenuShareWeibo",
        "onMenuShareQZone",
        "hideMenuItems",
        "showMenuItems",
        "hideAllNonBaseMenuItem",
        "showAllNonBaseMenuItem",
        "translateVoice",
        "startRecord",
        "stopRecord",
        "onVoiceRecordEnd",
        "playVoice",
        "onVoicePlayEnd",
        "pauseVoice",
        "stopVoice",
        "uploadVoice",
        "downloadVoice",
        "chooseImage",
        "previewImage",
        "uploadImage",
        "downloadImage",
        "getNetworkType",
        "openLocation",
        "getLocation",
        "hideOptionMenu",
        "showOptionMenu",
        "closeWindow",
        "scanQRCode",
        "chooseWXPay",
        "openProductSpecificView",
        "addCard",
        "chooseCard",
        "openCard",
        "openAddress",
      ],
      openTagList: ["wx-open-launch-weapp", "wx-open-subscribe"], // 跳转小程序时必填
    });
  </script>
  {/if}
</html>
