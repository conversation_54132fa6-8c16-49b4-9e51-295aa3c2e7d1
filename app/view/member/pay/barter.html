{layout name="layui"/} {block name="title"}自助买单{/block} {block name="head"}
<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
<meta name="apple-mobile-web-app-capable" content="no"/>
<meta name="format-detection" content="telephone=no"/>
<link rel="stylesheet" href="/static/css/member/pay/barter.css"/>
{/block} {block name="main"}
<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-state">
    <div class="loading-content">
      <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      <h4>加载中...</h4>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="page-container">
    <!-- 顶部广告栏 -->
    <div v-if="showTopBar" class="top-bar" :style="{backgroundColor: topBarColor}">
      <div class="top-content">
        <span class="top-text">
          <a :href="topBarLink">{{ topBarText }}</a>
        </span>
        <a :href="topBarLink" class="top-button">{{ topBarButtonText }}</a>
      </div>
    </div>

    <!-- 店铺信息 -->
    <div class="shop-info">
      <div class="shop-logo"></div>
      <h4 class="store-name">{{ storeInfo.storeName || '门店名称' }}</h4>
      <h4 v-if="memo" class="memo">{{ memo }}</h4>
    </div>

    <!-- 金额输入区域 -->
    <div class="amount-section">
      <div class="amount-box">
        <div class="input-box">
          <div class="label">消费金额</div>
          <div class="amount-display">
            ￥
            <span class="amount-text">{{ displayAmount }}</span>
            <div class="cursor-blink"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单详情 -->
    <div class="order-section">
      <ul class="order-list">
        <!-- 手续费 -->
        <li class="order-item">
          <div class="item-row">
            <div class="item-left">
              <div class="fee-icon"></div>
              <span>手续费({{ serviceChargeRate }}%)</span>
            </div>
            <div class="item-right text-orange">+{{ formatAmount(serviceCharge) }}</div>
          </div>
        </li>
      </ul>

      <!-- 余额支付 -->
      <ul class="balance-section">
        <li class="balance-item">
          <div class="item-row">
            <div class="item-left">
              <div class="check-icon"></div>
              <span class="text-orange">储值余额</span>
              <span class="text-orange">({{ formatAmount(memberInfo.balance) }}元)</span>
            </div>
            <div class="item-right">-{{ formatAmount(payAmount) }}</div>
          </div>
        </li>
      </ul>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message-box" :class="messageType">{{ message }}</div>

    <!-- 数字键盘 -->
    <div class="keyboard-container">
      <div class="brand-info">
        <div class="brand-logo"></div>
        <div class="brand-text">| 提供技术支持</div>
      </div>

      <div class="keyboard-grid">
        <div class="key-row">
          <div class="key" @click="inputNumber('1')">1</div>
          <div class="key" @click="inputNumber('2')">2</div>
          <div class="key" @click="inputNumber('3')">3</div>
          <div class="key delete-key" @click="deleteNumber">
            <i class="layui-icon layui-icon-delete"></i>
          </div>
          <div class="key pay-key" @click="confirmPayment" :disabled="!canPay" rowspan="2">
            确定
            <br/>
            支付
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('4')">4</div>
          <div class="key" @click="inputNumber('5')">5</div>
          <div class="key" @click="inputNumber('6')">6</div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('7')">7</div>
          <div class="key" @click="inputNumber('8')">8</div>
          <div class="key" @click="inputNumber('9')">9</div>
          <div class="key hide-key" @click="hideKeyboard">
            <i class="layui-icon layui-icon-down"></i>
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('0')">0</div>
          <div class="key" @click="inputNumber('.')">.</div>
        </div>
      </div>
    </div>
  </div>
</div>
{/block} {block name="script"}
<script src="/static/js/member/pay/barter.js"></script>
{/block}
