{layout name="layui"/} {block name="title"}自助付款{/block} {block name="head"}
<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
<meta name="apple-mobile-web-app-capable" content="no"/>
<meta name="format-detection" content="telephone=no"/>
<link rel="stylesheet" href="/static/css/member/pay/index.css"/>
{/block} {block name="main"}
<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-state">
    <div class="loading-content">
      <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      <h4>加载中...</h4>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="page-container">
    <!-- 店铺信息 -->
    <div class="shop-info">
      <div class="shop-logo"></div>
      <h4 class="shop-name">{{ '自助付款' }}</h4>
    </div>

    <!-- 金额输入区域 -->
    <div class="amount-section">
      <div class="amount-box">
        <div class="input-box">
          <div class="label">输入金额</div>
          <div class="amount-display">
            ￥
            <span class="amount-text">{{ displayAmount }}</span>
            <div class="cursor-blink"></div>
          </div>
        </div>
        <div v-if="showBalance" class="balance-box">
          <p>
            余额
            <span class="text-orange">￥{{ formatAmount(memberInfo.balance) }}</span>
          </p>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message-box" :class="messageType">{{ message }}</div>

    <!-- 数字键盘 -->
    <div class="keyboard-container">
      <div class="brand-info">
        <div class="brand-logo"></div>
        <div class="brand-text">| 提供技术支持服务</div>
      </div>

      <div class="keyboard-grid">
        <div class="key-row">
          <div class="key" @click="inputNumber('1')">1</div>
          <div class="key" @click="inputNumber('2')">2</div>
          <div class="key" @click="inputNumber('3')">3</div>
          <div class="key delete-key" @click="deleteNumber">
            <i class="layui-icon layui-icon-delete"></i>
          </div>
          <div class="key pay-key" @click="confirmPayment" :disabled="!canPay" rowspan="2">
            确定
            <br/>
            支付
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('4')">4</div>
          <div class="key" @click="inputNumber('5')">5</div>
          <div class="key" @click="inputNumber('6')">6</div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('7')">7</div>
          <div class="key" @click="inputNumber('8')">8</div>
          <div class="key" @click="inputNumber('9')">9</div>
          <div class="key hide-key" @click="hideKeyboard">
            <i class="layui-icon layui-icon-down"></i>
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('0')">0</div>
          <div class="key" @click="inputNumber('.')">.</div>
        </div>
      </div>
    </div>

    <!-- 支付订单弹窗 -->
    <div v-if="showOrder" class="order-overlay" @click="hidePaymentOrder">
      <div class="order-container" @click.stop>
        <div class="order-header">
          <h3>¥ {{ formatAmount(payAmount) }}</h3>
        </div>

        <ul class="order-list">
          <!-- 优惠信息 -->
          <li v-if="couponInfo.show" class="order-item">
            <div class="item-row">
              <div class="item-left">优惠</div>
              <div class="item-right text-orange">
                <span>{{ couponInfo.title }}</span>
                <span>{{ formatAmount(couponInfo.amount) }}</span>
              </div>
            </div>
          </li>

          <!-- 收款方 -->
          <li class="order-item">
            <div class="item-row">
              <div class="item-left">收款方</div>
              <div class="item-right">{{ paymentInfo.receiver || '微信充值' }}</div>
            </div>
          </li>
        </ul>

        <!-- 余额支付 -->
        <ul v-if="showBalancePayment" class="balance-section">
          <li class="balance-item">
            <div class="item-row">
              <div class="item-left">
                <div class="check-icon"></div>
                <span class="text-orange">可用储值余额</span>
              </div>
              <div class="item-right">￥{{ formatAmount(memberInfo.balance) }}</div>
            </div>
          </li>
        </ul>

        <!-- 品牌信息 -->
        <div class="order-brand">
          <div class="brand-logo"></div>
          <div class="brand-text">| 提供技术支持服务</div>
        </div>

        <!-- 支付按钮 -->
        <button class="pay-button" @click="confirmPayment" :disabled="paying">{{ paying ? '支付中...' : '立即支付' }}
        </button>
      </div>
    </div>
  </div>
</div>
{/block} {block name="script"}
<!-- 支付SDK集成 -->
<script>
  // 检测微信环境并动态加载对应的支付SDK
  function is_weixin() {
    var ua = navigator.userAgent.toLowerCase();
    return ua.indexOf('micromessenger') !== -1;
  }

  if (is_weixin()) {
    document.write("<script src='/static/js/wechatpay.js?v=20191203'><\/script>");
  } else {
    document.write("<script src='https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js'><\/script>");
    document.write("<script src='/static/js/alipay.js?v=20191201'><\/script>");
  }
</script>

<!-- 调试工具集成 -->
<script>
  window.onload = function () {
    // 开发环境下加载调试工具
    if (window.location.hostname === 'localhost' || window.location.hostname.indexOf('192.168') === 0 || window.location.search.indexOf('debug=1') !== -1) {
      loadScript('/static/js/eruda.min.js?v=2.4.1', function () {
        eruda.init();
      });
    }
  }

  function loadScript(src, callback) {
    var script = document.createElement('script');
    script.src = src;
    script.onload = callback;
    document.head.appendChild(script);
  }
</script>

<!-- 响应式适配脚本 -->
<script>
  var bid = "{$bid}";
  var openid = "{$openid}";
  !function (win) {
    function resize() {
      var domWidth = domEle.getBoundingClientRect().width;
      if (domWidth / v > 540) {
        domWidth = 540 * v;
      }
      win.rem = domWidth / 18.75;
      domEle.style.fontSize = win.rem + "px";
    }

    var v, initial_scale, timeCode, dom = win.document, domEle = dom.documentElement,
      viewport = dom.querySelector('meta[name="viewport"]'),
      flexible = dom.querySelector('meta[name="flexible"]');
    if (viewport) {
      var o = viewport.getAttribute("content").match(/initial\-scale=(["']?)([\d\.]+)\1?/);
      if (o) {
        initial_scale = parseFloat(o[2]);
        v = parseInt(1 / initial_scale);
      }
    } else {
      if (flexible) {
        var o = flexible.getAttribute("content").match(/initial\-dpr=(["']?)([\d\.]+)\1?/);
        if (o) {
          v = parseFloat(o[2]);
          initial_scale = parseFloat((1 / v).toFixed(2))
        }
      }
    }
    if (!v && !initial_scale) {
      var n = (win.navigator.appVersion.match(/android/gi), win.navigator.appVersion.match(/iphone/gi));
      v = win.devicePixelRatio;
      v = n ? v >= 3 ? 3 : v >= 2 ? 2 : 1 : 1, initial_scale = 1 / v
    }
    if (domEle.setAttribute("data-dpr", v), !viewport) {
      if (viewport = dom.createElement("meta"), viewport.setAttribute("name", "viewport"), viewport.setAttribute("content", "initial-scale=" + initial_scale + ", maximum-scale=" + initial_scale + ", minimum-scale=" + initial_scale + ", user-scalable=no"), domEle.firstElementChild) {
        domEle.firstElementChild.appendChild(viewport)
      } else {
        var m = dom.createElement("div");
        m.appendChild(viewport), dom.write(m.innerHTML)
      }
    }
    win.dpr = v;
    win.addEventListener("resize", function () {
      clearTimeout(timeCode), timeCode = setTimeout(resize, 300)
    }, false);
    win.addEventListener("pageshow", function (b) {
      b.persisted && (clearTimeout(timeCode), timeCode = setTimeout(resize, 300))
    }, false);
    resize();
  }(window);
</script>

<script src="/static/js/member/pay/index.js?v=2"></script>
{/block}
