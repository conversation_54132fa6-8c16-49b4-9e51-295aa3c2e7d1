
<!DOCTYPE HTML>
<html lang="zh-CN" data-rem="375">
<head>
  <meta charset="UTF-8">
  <title>自助买单</title>
  <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="keywords" content="">
  <meta name="description" content="">
  <meta name="apple-mobile-web-app-capable" content="no">
  <meta name="format-detection" content="telephone=no">
  <script>
    var bid = "{$bid}";
    var openid = "{$openid}";
    !function (win) {
      function resize() {
        var domWidth = domEle.getBoundingClientRect().width;
        if (domWidth / v > 540) {
          domWidth = 540 * v;
        }
        win.rem = domWidth / 18.75;
        domEle.style.fontSize = win.rem + "px";
      }

      var v, initial_scale, timeCode, dom = win.document, domEle = dom.documentElement,
        viewport = dom.querySelector('meta[name="viewport"]'),
        flexible = dom.querySelector('meta[name="flexible"]');
      if (viewport) {
        //viewport：<meta name="viewport"content="initial-scale=0.5, minimum-scale=0.5, maximum-scale=0.5,user-scalable=no,minimal-ui"/>
        var o = viewport.getAttribute("content").match(/initial\-scale=(["']?)([\d\.]+)\1?/);
        if (o) {
          initial_scale = parseFloat(o[2]);
          v = parseInt(1 / initial_scale);
        }
      } else {
        if (flexible) {
          var o = flexible.getAttribute("content").match(/initial\-dpr=(["']?)([\d\.]+)\1?/);
          if (o) {
            v = parseFloat(o[2]);
            initial_scale = parseFloat((1 / v).toFixed(2))
          }
        }
      }
      if (!v && !initial_scale) {
        var n = (win.navigator.appVersion.match(/android/gi), win.navigator.appVersion.match(/iphone/gi));
        v = win.devicePixelRatio;
        v = n ? v >= 3 ? 3 : v >= 2 ? 2 : 1 : 1, initial_scale = 1 / v
      }
      //没有viewport标签的情况下
      if (domEle.setAttribute("data-dpr", v), !viewport) {
        if (viewport = dom.createElement("meta"), viewport.setAttribute("name", "viewport"), viewport.setAttribute("content", "initial-scale=" + initial_scale + ", maximum-scale=" + initial_scale + ", minimum-scale=" + initial_scale + ", user-scalable=no"), domEle.firstElementChild) {
          domEle.firstElementChild.appendChild(viewport)
        } else {
          var m = dom.createElement("div");
          m.appendChild(viewport), dom.write(m.innerHTML)
        }
      }
      win.dpr = v;
      win.addEventListener("resize", function () {
        clearTimeout(timeCode), timeCode = setTimeout(resize, 300)
      }, false);
      win.addEventListener("pageshow", function (b) {
        b.persisted && (clearTimeout(timeCode), timeCode = setTimeout(resize, 300))
      }, false);
      /* 个人觉得没必要完成后就把body的字体设置为12
       "complete" === dom.readyState ? dom.body.style.fontSize = 12 * v + "px" : dom.addEventListener("DOMContentLoaded", function() {
       //dom.body.style.fontSize = 12 * v + "px"
       }, false);
       */
      resize();
    }(window);
  </script>
  <link rel="stylesheet" href="/static/css/style-pay.css?v=1"/>
</head>
<body>
<div id="wrap">
  <div class="shopinfo">
    <div class="shoplogo"></div>
    <h4 id="nickname">自助付款</h4>
  </div>
  <div class="container">
    <div class="amount-box">
      <div class="input-box">
        <div class="left">
          输入金额
        </div>
        <div class="amount-text right">
          ￥<span id="keyboard-text"></span>
          <div class="shining"></div>
        </div>
      </div>
      <div class="balance-box" style="display:none;">
        <p>余额<span id="balance-amt" class="text-orange">￥0.00</span></p>
      </div>
    </div>
    <div style="display:none;">
      <li id="member" style="font-size: 1.6rem;text-align: center;display: none"><span
              style="font-size: 1rem">余额:</span><span
              id="value_blance"></span>
      </li>
    </div>
  </div>
  <div id="message"></div>
  <div class="keyboard" id="keyboard">
    <div class="haojin-bear" style="display: none"></div>
    <div class="haojin">
      <div class="haojin-logo left"></div>
      <div class="haojin-str left">
        &nbsp;|&nbsp;提供技术支持服务
      </div>
    </div>
    <div data-str="1" class="key">1</div>
    <div data-str="2" class="key">2</div>
    <div data-str="3" class="key">3</div>
    <div data-str="del" class="key icon icon-backspace">&nbsp;</div>
    <div data-str="submit" class="key pay" id="pay">确定<br>支付</div>
    <div data-str="4" class="key">4</div>
    <div data-str="5" class="key">5</div>
    <div data-str="6" class="key">6</div>
    <div data-str="7" class="key">7</div>
    <div data-str="8" class="key">8</div>
    <div data-str="9" class="key">9</div>
    <div data-str="hide" class="key icon icon-hide">&nbsp;</div>
    <div data-str="0" class="key">0</div>
    <div data-str="." class="key">.</div>
  </div>
</div>
<div id="order" class="order">
  <h3>¥ <span id="order-pay-amt">0.00</span><span id="order-total-amt"></span></h3>
  <ul>
    <li id="coupon-row" style="display: none">
      <div class="row">
        <div class="left">优惠</div>
        <div class="right text-orange">
          <span id="order-coupon-title">优惠信息</span>
          <span id="order-coupon-amt">0.00</span>
        </div>
      </div>
    </li>
    <li>
      <div class="row">
        <div class="left">收款方</div>
        <div class="right" id="order-nickname">微信充值</div>
      </div>
    </li>
  </ul>

  <ul id="balance-row" style="display: none">
    <li>
      <div class="row">
        <div class="left">
          <div class="img-gou"></div>
          <span class="text-orange">可用储值余额</span></div>
        <div class="right" id="order-balance-amt">￥0.00</div>
      </div>
    </li>
  </ul>
  <div class="haojin">
    <div class="haojin-logo left"></div>
    <div class="haojin-str left">
      &nbsp;|&nbsp;提供技术支持服务
    </div>
  </div>
  <button id="pay-now">立即支付</button>
</div>
<div class="loading" style="display: none"></div>
</body>
<script src="/static/js/zepto.js"></script>
<script src="/static/js/function.js?v=20250521"></script>
<script src="/static/js/web-storage-cache.min.js?v=1.1.0"></script>
<script src="/static/js/pay.js?v=20191201"></script>
<script>
  if (is_weixin()) {
    document.write("<script src='/static/js/wechatpay.js?v=20191203'><\/script>");
  } else {
    document.write("<script src='https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js'><\/script>");
    document.write("<script src='/static/js/alipay.js?v=20191201'><\/script>");
  }
</script>
<script>
  window.onload = function () {
    // loadScript('/static/js/eruda.min.js?v=2.4.1', function () {
    //   eruda.init();
    // });
  }
</script>
<!--<script src="//res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>-->
<script>
  function init() {
    url = "/member_api/v1/business/info/";
    var data = {};
    data.bid = getQueryString('bid');
    data = Zepto.param(data) || jQuery.param(data);
    ajax({
      url: url,
      data: data,
      complete: function () {
        /* 移除loading */
        $(".loading").hide();
      },
      success: function (result) {
        console.log(result);
        if (result.code === 0) {
          $("#business_name").html(result.data.business_name);
          return true;
        } else {
          alert(result.msg);
          return false;
        }
      }
    })
  }

  init();
</script>
</html>
