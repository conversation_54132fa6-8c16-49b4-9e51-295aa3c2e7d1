{layout name="layui"/} {block name="title"}自助付款{/block} {block name="head"}
<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
<meta name="apple-mobile-web-app-capable" content="no"/>
<meta name="format-detection" content="telephone=no"/>
<link rel="stylesheet" href="/static/css/member/pay/index.css"/>
{/block} {block name="main"}
<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-state">
    <div class="loading-content">
      <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop"></i>
      <h4>加载中...</h4>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="page-container">
    <!-- 店铺信息 -->
    <div class="shop-info">
      <div class="shop-logo"></div>
      <h4 class="shop-name">自助付款</h4>
    </div>

    <!-- 主容器 -->
    <div class="container">
      <!-- 金额输入区域 -->
      <div class="amount-section">
        <div class="amount-box">
          <div class="input-box">
            <div class="label left">输入金额</div>
            <div class="amount-display right">
              ￥<span class="amount-text">{{ displayAmount }}</span>
              <div class="cursor-blink"></div>
            </div>
          </div>
          <div v-if="showBalance" class="balance-box" style="display:none;">
            <p>余额<span class="text-orange">￥{{ formatAmount(memberInfo.balance) }}</span></p>
          </div>
        </div>
      </div>

      <!-- 隐藏的会员信息 -->
      <div style="display:none;">
        <li v-if="memberInfo" style="font-size: 1.6rem;text-align: center;display: none">
          <span style="font-size: 1rem">余额:</span>
          <span>￥{{ formatAmount(memberInfo.balance) }}</span>
        </li>
      </div>
    </div>

    <!-- 消息提示 -->
    <div id="message" v-if="showMessage" class="message-box" :class="message.type">{{ message.text }}</div>

    <!-- 数字键盘 -->
    <div class="keyboard-container" id="keyboard">
      <div class="brand-info">
        <div class="brand-logo left"></div>
        <div class="brand-text left">&nbsp;|&nbsp;提供技术支持服务</div>
      </div>

      <div class="keyboard-grid">
        <div class="key-row">
          <div class="key" @click="inputNumber('1')">1</div>
          <div class="key" @click="inputNumber('2')">2</div>
          <div class="key" @click="inputNumber('3')">3</div>
          <div class="key delete-key" @click="deleteNumber">
            <i class="layui-icon layui-icon-delete"></i>
          </div>
          <div class="key pay-key" @click="confirmPayment" :disabled="!canPay" id="pay">
            确定<br>支付
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('4')">4</div>
          <div class="key" @click="inputNumber('5')">5</div>
          <div class="key" @click="inputNumber('6')">6</div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('7')">7</div>
          <div class="key" @click="inputNumber('8')">8</div>
          <div class="key" @click="inputNumber('9')">9</div>
          <div class="key hide-key" @click="hideKeyboard">
            <i class="layui-icon layui-icon-down"></i>
          </div>
        </div>

        <div class="key-row">
          <div class="key" @click="inputNumber('0')">0</div>
          <div class="key" @click="inputNumber('.')">.</div>
        </div>
      </div>
    </div>

    <!-- 支付订单弹窗 -->
    <div v-if="showOrder" class="order-overlay" @click="hidePaymentOrder">
      <div class="order-container" @click.stop>
        <div class="order-header">
          <h3>¥ {{ formatAmount(payAmount) }}</h3>
        </div>

        <ul class="order-list">
          <!-- 优惠信息 -->
          <li v-if="couponInfo.show" class="order-item">
            <div class="item-row">
              <div class="item-left">优惠</div>
              <div class="item-right text-orange">
                <span>{{ couponInfo.title }}</span>
                <span>{{ formatAmount(couponInfo.amount) }}</span>
              </div>
            </div>
          </li>

          <!-- 收款方 -->
          <li class="order-item">
            <div class="item-row">
              <div class="item-left">收款方</div>
              <div class="item-right">{{ paymentInfo.receiver || '微信充值' }}</div>
            </div>
          </li>
        </ul>

        <!-- 余额支付 -->
        <ul v-if="showBalancePayment" class="balance-section">
          <li class="balance-item">
            <div class="item-row">
              <div class="item-left">
                <div class="check-icon"></div>
                <span class="text-orange">可用储值余额</span>
              </div>
              <div class="item-right">￥{{ formatAmount(memberInfo.balance) }}</div>
            </div>
          </li>
        </ul>

        <!-- 品牌信息 -->
        <div class="order-brand">
          <div class="brand-logo"></div>
          <div class="brand-text">| 提供技术支持服务</div>
        </div>

        <!-- 支付按钮 -->
        <button class="pay-button" @click="confirmPayment" :disabled="paying">{{ paying ? '支付中...' : '立即支付' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 加载状态 -->
<div class="loading" style="display: none"></div>
{/block} {block name="script"}
<script>
  // 设置全局变量供JS文件使用
  window.PAY_CONFIG = {
    bid: "{$bid}",
    openid: "{$openid}"
  };
</script>
<script src="/static/js/member/pay/index.js?v=3"></script>
{/block}
