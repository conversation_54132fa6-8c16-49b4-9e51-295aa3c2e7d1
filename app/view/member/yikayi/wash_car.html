{layout name="vant" /}
<link href="/static/css/member/yikayi/wash_car.css?v=1" rel="stylesheet" type="text/css" />

<div id="app" v-cloak>
  <!-- 加载状态 -->
  <div v-if="loading" style="text-align: center; padding: 40px">
    <van-loading type="spinner" color="#1989fa" vertical>获取券列表中...</van-loading>
  </div>

  <!-- 券列表（有券和无券都显示） -->
  <div v-else>
    <!-- 标题（只在有券时显示） -->
    <div v-if="couponList.length > 0" class="section-title">洗车券列表 ({{ couponList.length }}张)</div>

    <!-- 券列表 -->
    <div style="margin-bottom: 70px">
      <van-coupon-list
        :coupons="formattedCoupons"
        :chosen-coupon="chosenCoupon"
        :disabled-coupons="[]"
        enabled-title="洗车券"
        disabled-title=""
        :show-close-button="false"
        :show-exchange-bar="false"
        :show-count="false"
        empty-image="/static/img/custom-empty-image.png"
        @change="onCouponChange"
      >
        <!-- 自定义空状态内容 -->
        <template #list-footer v-if="couponList.length === 0">
          <div style="text-align: center; padding: 20px">
            <van-button type="primary" size="small" @click="getCouponList">刷新券列表</van-button>
          </div>
        </template>
      </van-coupon-list>
    </div>

    <!-- 确认核销按钮（只在有券时显示） -->
    <div v-if="couponList.length > 0" class="confirm-button-fixed" style="padding: 12px 16px; background: white; border-top: 1px solid #eee">
      <van-button type="primary" size="large" block :disabled="chosenCoupon === -1 || isSubmitting" :loading="isSubmitting" @click="useCoupon">{{ chosenCoupon === -1 ? '请选择优惠券' : (isSubmitting ? '核销中...' : '确认核销') }}</van-button>
    </div>
  </div>

  
</div>

<script src="/static/js/member/yikayi/wash_car.js?v=2"></script>
