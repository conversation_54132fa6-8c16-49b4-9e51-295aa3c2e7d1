{layout name="layui_plus"  /}
<div class="layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <form class="layui-form" id="edit_form" loadUrl="">
        <input type="hidden" name="guid"/>
        <div class="layui-form-item">
          <div class="layui-inline">
            <label class="layui-form-label">账号</label>
            <div class="layui-input-block">
              <input type="text" name="account" required="" lay-verType="tips" lay-verify="required"
                     placeholder="请输入账号" autocomplete="off" class="layui-input fsEditReadonly"
                     maxlength="20"/>
            </div>
          </div>
          <div class="layui-inline" auth="admin">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-block">
              <select name="type" lay-verify="required" lay-search="">
                <option value="">请选择类型</option>
                <option value="1" selected>商家</option>
                <option value="2">代理</option>
                <option value="3">管理员</option>
              </select>
            </div>
          </div>
        </div>
        <div class="layui-form-item" style="display: none">
          <div class="layui-inline">
            <label class="layui-form-label">admin_id</label>
            <div class="layui-input-block">
              <input type="tel" name="wxapp_admin_id" required="" lay-verType="tips" lay-verify=""
                     placeholder="请输入代理商ID"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">store_id</label>
            <div class="layui-input-block">
              <input type="tel" name="mall_store_id" required="" lay-verType="tips" lay-verify=""
                     placeholder="请输入门店ID"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">商家名称</label>
          <div class="layui-input-block">
            <input type="text" name="business_name" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入商家名称"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">公司名称</label>
          <div class="layui-input-block">
            <input type="text" name="principal_name"
                   placeholder="请输入公司名称"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>


        <div class="layui-form-item">

          <div class="layui-inline">

            <label class="layui-form-label">手机号</label>
            <div class="layui-input-block">
              <input type="tel" maxlength="11" name="mobile" required="" lay-verType="tips"
                     lay-verify="required" placeholder="手机号"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
          <div class="layui-inline">

            <label class="layui-form-label">联系人</label>
            <div class="layui-input-block">
              <input type="text" maxlength="11" name="true_name" required="" lay-verType="tips"
                     lay-verify="required" placeholder="联系人姓名"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>

        </div>


        <div class="layui-form-item" style="display: none">
          <label class="layui-form-label">省份</label>
          <div class="layui-input-inline" style="width: 100px;">
            <select name="province_id" lay-filter="province_id" class="fsSelect" dict="province" addNull="1"
                    childrenSelectId="city_id">
            </select>
          </div>
          <label class="layui-form-label">城市</label>
          <div class="layui-input-inline" style="width: 100px;">
            <select id="city_id" name="city_id" lay-filter="city_id" class="fsSelect" isLoad="0" dict="city"
                    addNull="1" childrenSelectId="area_id">
            </select>
          </div>
          <label class="layui-form-label">区</label>
          <div class="layui-input-inline" style="width: 100px;">
            <select id="area_id" name="area_id" class="fsSelect" isLoad="0" dict="area" addNull="1">
            </select>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">版本</label>
          <div class="layui-input-block">
            <select name="version_guid" lay-search lay-verify="required" lay-verType="tips"
                    class="fsSelect fsEditReadonly"
                    dict="version" inputs="type:1,status:1,business_guid:$guid" addNull="1">
            </select>
          </div>
        </div>
        <!--                <div class="layui-form-item">-->
        <!--                    <div class="layui-inline">-->
        <!--                        <label class="layui-form-label">到期时间</label>-->
        <!--                        <div class="layui-input-block">-->
        <!--                            <input type="text" name="expired_time" autocomplete="off"-->
        <!--                                   class="layui-input fsDate fsAddReadonly fsEditReadonly" dateType="datetime"/>-->
        <!--                        </div>-->
        <!--                    </div>-->
        <!--                    &lt;!&ndash;                    统一通过 延期操作来更改商家有效期 便于追溯&ndash;&gt;-->
        <!--                    &lt;!&ndash;                    <div class="layui-inline">&ndash;&gt;-->
        <!--                    &lt;!&ndash;                        <div class="tips" style="cursor:pointer" onclick="set_expire_time()">一年有效</div>&ndash;&gt;-->
        <!--                    &lt;!&ndash;                    </div>&ndash;&gt;-->

        <!--                </div>-->

        <div class="layui-form-item fsEdit">
          <div class="layui-inline">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
              <input type="password" name="login_password" required="" lay-verType="tips"
                     placeholder="密码"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">重复密码</label>
            <div class="layui-input-block">
              <input type="password" name="repassword" required="" lay-verType="tips"
                     placeholder="重复密码"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
        </div>


        <div class="layui-form-item fsEdit">
          <div class="layui-inline">
            <label class="layui-form-label">状态</label>
            <div class="layui-input-block">
              <input type="radio" name="status" value="1" title="启用" checked="">
              <div class="layui-unselect layui-form-radio layui-form-radioed"><i
                  class="layui-anim layui-icon"></i>
              </div>
              <input type="radio" name="status" value="0" title="禁用">
              <div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i>
              </div>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">签约</label>
            <div class="layui-input-block">
              <input class="fsEditReadonly" type="radio" name="license_status" value="1" title="试用版"
                     checked="">
              <div class="layui-unselect layui-form-radio layui-form-radioed"><i
                  class="layui-anim layui-icon"></i>
              </div>
              <input class="fsEditReadonly" type="radio" name="license_status" value="2" title="正式版">
              <div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-form-item" auth="admin|staff">
          <div class="layui-inline">
            <label class="layui-form-label">首年费用</label>
            <div class="layui-input-block">
              <input type="tel" name="first_year_fee" required="" lay-verType="tips"
                     placeholder="请输入首年费用"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">续费年费</label>
            <div class="layui-input-block">
              <input type="tel" name="annual_service_fee" required="" lay-verType="tips"
                     placeholder="年费"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
        </div>


        <div class="layui-form-item" auth="admin">
          <div class="layui-inline">
            <label class="layui-form-label">授信金额</label>
            <div class="layui-input-block">
              <input type="tel" name="credit_money" required="" lay-verType="tips"
                     placeholder="请输入授信金额(元)"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>
          <div class="layui-inline">
            <label class="layui-form-label">授信短信</label>
            <div class="layui-input-block">
              <input type="tel" name="credit_sms" required="" lay-verType="tips"
                     placeholder="请输入授信短信(条)"
                     autocomplete="off" class="layui-input"/>
            </div>
          </div>

        </div>

        <div class="layui-form-item" auth="admin">
          <label class="layui-form-label">所属代理</label>
          <div class="layui-input-block">
            <!--                        <input id="agent" type="text" name="agent" required="" lay-verType="tips"-->
            <!--                               lay-verify="required"-->
            <!--                               placeholder="请选择所属代理商"-->
            <!--                               autocomplete="off" class="layui-input"/>-->

            <select placeholder='代理' lay-search inputs="" name="parent_guid"
                    lay-verType="tips"
                    class="fsSelect" dict="agent" addNull="1">
            </select>

          </div>


        </div>
        <div class="layui-form-item">
          <label class="layui-form-label">备注</label>
          <div class="layui-input-block">
            <input type="text" name="memo" required="" lay-verType="tips" placeholder="请输入备注"
                   autocomplete="off" class="layui-input"/>
          </div>
        </div>
        <!--<div class="layui-form-item layui-form-text">-->
        <!--<label class="layui-form-label">附件</label>-->
        <!--<div class="layui-input-inline">-->
        <!--<input type="text" id="filePath" name="filePath" autocomplete="off" disabled="disabled"-->
        <!--class="layui-input"/>-->
        <!--</div>-->
        <!--<div class="layui-input-inline">-->
        <!--<button type="button" class="layui-btn layui-btn-normal" function="upload" fileElem="#filePath" fileAccept="file"-->
        <!--fileExts="" fileSize="3072" inputs="type:test"><i class="layui-icon layui-icon-upload-drag"></i>上传图片-->
        <!--</button>-->
        <!--</div>-->
        <!--</div>-->
        <!--<div class="layui-form-item layui-form-text">-->
        <!--<label class="layui-form-label">描述</label>-->
        <!--<div class="layui-input-block">-->
        <!--<textarea id="description" name="description" placeholder="请输入描述" class="fsEditor"-->
        <!--height="80"></textarea>-->
        <!--</div>-->
        <!--</div>-->
        <hr/>
        <div class="layui-form-item" style="text-align: center;">
          <button class="layui-btn fsAdd" lay-submit="" lay-filter="save" url="/business/add">保存</button>
          <button class="layui-btn fsEdit" lay-submit="" lay-filter="edit" url="/business/edit">保存
          </button>
          <button type="button" class="layui-btn layui-btn-primary" function="close">关闭</button>
        </div>
      </form>
    </div>
  </div>
</div>
<script>
  //个位数补0
  function getZero(num) {
    // 单数前面加0
    if (parseInt(num) < 10) {
      num = '0' + num;
    }
    return num;
  }

  //获取现在时间
  function getDateTime() {

    var myDate = new Date();
    var year = myDate.getFullYear() + 1; //获取当前年
    var mon = myDate.getMonth() + 1; //获取当前月
    var date = myDate.getDate() + 1; //获取当前日
    var hours = myDate.getHours(); //获取当前小时
    var minutes = myDate.getMinutes(); //获取当前分钟
    var seconds = myDate.getSeconds(); //获取当前秒
    // 以自己需要的方式拼接
    var now = year + "-" + this.getZero(mon) + "-" + this.getZero(date);
    return now;
  }

  function set_expire_time_old() {
    $(":input[name='expired_time']").val(getDateTime() + ' 00:00:00');
  }

  function set_expire_time() {
    let now = new Date();
    now.setTime(now.getTime() + 1 * 24 * 60 * 60 * 1000);
    let tomorrow = (now.getFullYear() + 1) + "-" + (now.getMonth() + 1) + "-" + now.getDate();
    $(":input[name='expired_time']").val(tomorrow + ' 00:00:00');
  }


</script>
