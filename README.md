# 项目说明

本项目基于 ThinkPHP 框架开发，采用模块化结构，便于维护和扩展。以下为主要目录及文件说明：

## 目录结构

- `app/`  
  应用主目录，包含业务逻辑、控制器、模型、中间件等。

  - `BaseController.php`  
    控制器基础类，所有控制器继承自此类，封装了通用的请求、参数、验证等功能。
  - `ExceptionHandle.php`  
    全局异常处理类，负责捕获和处理应用运行时的异常，并进行日志记录、通知等操作。
  - `common.php`  
    公共函数库，封装了常用的工具函数，如时间处理、Curl 请求、GUID 生成、iOS 推送等。
  - `middleware.php`  
    全局中间件定义文件，注册了项目所需的中间件，如内容类型追加、多语言加载、全局处理等。

- `public/`  
  WEB 入口目录，包含入口文件（如 index.php）及静态资源。

- `config/`  
  配置文件目录，存放数据库、缓存、日志等相关配置。

- `route/`  
  路由定义目录，管理项目的 URL 路由规则。

## 主要功能

- 全局异常捕获与处理，支持企业微信通知、日志记录等。
- 丰富的工具函数，便于开发过程中调用。
- 支持多语言、内容类型自动追加等中间件功能。
- 结构清晰，易于扩展和维护。

## 快速开始

1. 克隆项目到本地
2. 配置好环境及相关依赖
3. 运行 `php think run` 或配置好 Web 服务器访问 `public` 目录

如需详细开发文档，请参考各模块源码注释。
