# 商品选择页面 JavaScript 文档

## 📋 **业务概述**

这是一个**券卡兑换商品选择系统**的前端 JavaScript 文件，主要用于用户使用券卡兑换商品的场景。支持单规格和多规格（SKU）商品的选择，具备完整的购物车管理和数据验证功能。

## 🎯 **核心业务流程**

### **1. 页面初始化流程**

```
页面加载 → 获取URL参数(bid, token) → 初始化Vue应用 → 加载商品数据 → 加载配置信息 → 渲染商品列表
```

### **2. 商品选择流程**

```
用户点击商品 → 检查是否多规格商品 →
├─ 单规格：直接验证数量限制 → 添加到购物车
└─ 多规格：打开SKU选择弹窗 → 用户选择属性 → 确认SKU → 添加到购物车
```

### **3. SKU 选择流程**

```
打开SKU弹窗 → 选择商品属性 → SKU匹配算法 → 更新价格库存 → 选择数量 → 验证限制 → 确认添加
```

### **4. 购物车管理流程**

```
商品变更 → 重新计算总价 → 验证券卡限制 → 更新UI显示 → 同步购物车状态
```

### **5. 订单提交流程**

```
点击提交 → 验证购物车 → 构建商品信息 → 缓存数据 → 跳转订单页面
```

## 🏗️ **代码架构**

### **工具模块（Utils）**

- **MessageUtils**: 消息提示工具
- **SkuUtils**: SKU 相关操作工具
- **CartUtils**: 购物车操作工具
- **ValidationUtils**: 数据验证工具
- **UIUtils**: UI 界面工具

### **Vue 应用结构**

- **data**: 响应式数据管理
- **methods**: 业务逻辑方法
- **computed**: 计算属性
- **watch**: 数据监听

## 📚 **详细方法说明**

### **🔧 工具函数模块**

#### **MessageUtils - 消息提示工具**

| 方法名                   | 参数                          | 功能说明          |
|-----------------------|-----------------------------|---------------|
| `show(message, type)` | message: 消息内容<br>type: 消息类型 | 显示 layui 消息提示 |
| `alert(message)`      | message: 消息内容               | 显示确认对话框       |

#### **SkuUtils - SKU 操作工具**

| 方法名                                          | 参数                                                  | 功能说明                  |
|----------------------------------------------|-----------------------------------------------------|-----------------------|
| `getPrice(item)`                             | item: 商品对象                                          | 获取当前 SKU 价格           |
| `getStock(item)`                             | item: 商品对象                                          | 获取当前 SKU 库存           |
| `getSpecs(item)`                             | item: 商品对象                                          | 获取当前 SKU 规格描述         |
| `getImage(item)`                             | item: 商品对象                                          | 获取当前 SKU 图片           |
| `findMatching(item)`                         | item: 商品对象                                          | **核心算法**：通过属性交集匹配 SKU |
| `isAttrAvailable(item, groupGuid, attrGuid)` | item: 商品对象<br>groupGuid: 属性分组 ID<br>attrGuid: 属性 ID | 检查属性是否可选（基于库存）        |

#### **CartUtils - 购物车工具**

| 方法名                                     | 参数                                            | 功能说明         |
|-----------------------------------------|-----------------------------------------------|--------------|
| `getQuantity(item, cartList)`           | item: 商品对象<br>cartList: 购物车列表                 | 获取商品在购物车中的数量 |
| `setQuantity(item, quantity, cartList)` | item: 商品对象<br>quantity: 数量<br>cartList: 购物车列表 | 设置商品在购物车中的数量 |
| `calculateTotal(goodsList, cartList)`   | goodsList: 商品列表<br>cartList: 购物车列表            | 计算购物车总数量和总价格 |

#### **ValidationUtils - 验证工具**

| 方法名                                                              | 参数                                                                          | 功能说明            |
|------------------------------------------------------------------|-----------------------------------------------------------------------------|-----------------|
| `checkQuantity(goods, targetQuantity, context)`                  | goods: 商品对象<br>targetQuantity: 目标数量<br>context: Vue 实例                      | **核心验证**：检查数量限制 |
| `checkAmountLimit(goods, newQuantity, currentQuantity, context)` | goods: 商品对象<br>newQuantity: 新数量<br>currentQuantity: 当前数量<br>context: Vue 实例 | 检查金额限制（券卡限制）    |

### **🎨 Vue 应用方法**

#### **基础 UI 方法**

| 方法名            | 参数 | 功能说明           |
|----------------|----|----------------|
| `toggleCart()` | 无  | 切换购物车显示/隐藏     |
| `clearCart()`  | 无  | 清空购物车所有商品      |
| `search()`     | 无  | 重新搜索商品（重新加载数据） |

#### **消息方法**

| 方法名                    | 参数            | 功能说明    |
|------------------------|---------------|---------|
| `showMessage(message)` | message: 消息内容 | 显示提示消息  |
| `showAlert(message)`   | message: 消息内容 | 显示确认对话框 |

#### **验证方法**

| 方法名                                       | 参数                                  | 功能说明         |
|-------------------------------------------|-------------------------------------|--------------|
| `validateQuantity(goods, targetQuantity)` | goods: 商品对象<br>targetQuantity: 目标数量 | 验证商品数量是否符合限制 |
| `getMaxSelectableQuantity(goods)`         | goods: 商品对象                         | 获取商品最大可选择数量  |

#### **SKU 属性选择方法**

| 方法名                                          | 参数                                                  | 功能说明                      |
|----------------------------------------------|-----------------------------------------------------|---------------------------|
| `selectAttr(item, groupGuid, attrGuid)`      | item: 商品对象<br>groupGuid: 属性分组 ID<br>attrGuid: 属性 ID | **核心方法**：选择商品属性，触发 SKU 匹配 |
| `findMatchingSku(item)`                      | item: 商品对象                                          | 查找匹配的 SKU（调用工具函数）         |
| `isAttrSelected(item, groupGuid, attrGuid)`  | item: 商品对象<br>groupGuid: 属性分组 ID<br>attrGuid: 属性 ID | 判断属性是否被选中                 |
| `isAttrAvailable(item, groupGuid, attrGuid)` | item: 商品对象<br>groupGuid: 属性分组 ID<br>attrGuid: 属性 ID | 判断属性是否可选                  |

#### **SKU 属性获取方法**

| 方法名                        | 参数         | 功能说明              |
|----------------------------|------------|-------------------|
| `getCurrentSkuPrice(item)` | item: 商品对象 | 获取当前 SKU 价格（模板调用） |
| `getCurrentSkuStock(item)` | item: 商品对象 | 获取当前 SKU 库存（模板调用） |
| `getCurrentSkuImage(item)` | item: 商品对象 | 获取当前 SKU 图片（模板调用） |
| `getCurrentSkuSpecs(item)` | item: 商品对象 | 获取当前 SKU 规格（模板调用） |

#### **购物车操作方法**

| 方法名                                 | 参数                         | 功能说明                  |
|-------------------------------------|----------------------------|-----------------------|
| `getCartKey(item)`                  | item: 商品对象                 | 生成购物车键值（商品 GUID）      |
| `getChooseQuantity(item)`           | item: 商品对象                 | 获取商品在购物车中的数量          |
| `setChooseQuantity(item, quantity)` | item: 商品对象<br>quantity: 数量 | 设置商品在购物车中的数量          |
| `recalculateTotal()`                | 无                          | **重要方法**：重新计算购物车总数和总价 |
| `hasSelectedSku(item)`              | item: 商品对象                 | 检查商品是否已选择 SKU         |

#### **商品操作方法**

| 方法名                      | 参数                         | 功能说明              |
|--------------------------|----------------------------|-------------------|
| `addGoods(event, goods)` | event: 点击事件<br>goods: 商品对象 | **核心方法**：添加商品到购物车 |
| `delGoods(event, goods)` | event: 点击事件<br>goods: 商品对象 | 从购物车减少商品数量        |
| `gotoDetail(goods)`      | goods: 商品对象                | 跳转到商品详情页面         |

#### **SKU 弹窗方法**

| 方法名                     | 参数          | 功能说明                      |
|-------------------------|-------------|---------------------------|
| `openSkuModal(goods)`   | goods: 商品对象 | 打开 SKU 选择弹窗               |
| `closeSkuModal()`       | 无           | 关闭 SKU 选择弹窗               |
| `increaseSkuQuantity()` | 无           | 增加 SKU 弹窗中的数量             |
| `decreaseSkuQuantity()` | 无           | 减少 SKU 弹窗中的数量             |
| `canConfirmSku()`       | 无           | 检查是否可以确认 SKU 选择           |
| `confirmSkuSelection()` | 无           | **重要方法**：确认 SKU 选择并添加到购物车 |

#### **数据提交方法**

| 方法名        | 参数 | 功能说明                 |
|------------|----|----------------------|
| `submit()` | 无  | **核心方法**：提交购物车数据到下一步 |

#### **数据加载方法**

| 方法名            | 参数 | 功能说明             |
|----------------|----|------------------|
| `loadConfig()` | 无  | 加载商品选择页面配置       |
| `loadData()`   | 无  | **初始化方法**：加载商品数据 |
| `initBanner()` | 无  | 初始化轮播图           |

## 🔄 **核心算法详解**

### **1. SKU 匹配算法（SkuUtils.findMatching）**

```javascript
// 算法流程：
1. 检查是否为多规格商品
2. 验证是否选择了所有属性分组
3. 遍历每个属性分组，获取选中属性的SKU列表
4. 通过交集运算找到匹配的SKU
5. 返回第一个匹配的SKU对象
```

### **2. 数量验证算法（ValidationUtils.checkQuantity）**

```javascript
// 验证层级：
1. 最小数量限制（min_choose_num）
2. 最大数量限制（max_choose_num）
3. 库存限制（stock_mode == 1时）
4. 总体数量限制（券卡类型限制）
```

### **3. 购物车计算算法（CartUtils.calculateTotal）**

```javascript
// 计算流程：
1. 遍历所有商品列表
2. 获取每个商品在购物车中的数量
3. 获取每个商品的当前SKU价格
4. 累计总数量和总价格
5. 返回计算结果
```

## 📊 **数据结构说明**

### **商品对象（goods/item）**

```javascript
{
  guid: "商品唯一标识",
  name: "商品名称",
  price: "基础价格",
  pic: "商品图片",
  is_attribute: 1, // 是否多规格商品
  stock_mode: 1, // 库存管理模式
  stock: 100, // 基础库存
  min_choose_num: 1, // 最小选择数量
  max_choose_num: 999, // 最大选择数量
  attr_group_list: [], // 属性分组列表
  selected_attrs: {}, // 用户选择的属性
  current_sku: {} // 当前匹配的SKU
}
```

### **SKU 对象**

```javascript
{
  sku_guid: "SKU唯一标识",
  price: "SKU价格",
  stock: 50, // SKU库存
  picture: "SKU图片"
}
```

### **购物车数据（choose_goods_list）**

```javascript
{
  "商品GUID1": 数量1,
  "商品GUID2": 数量2,
  // ...
}
```

## 🎯 **业务规则说明**

### **券卡类型限制**

- **次数型券卡（EXCHANGE_TYPE.COUNT）**: 限制可选商品件数
- **金额型券卡（EXCHANGE_TYPE.AMOUNT）**: 限制可选商品总金额

### **库存管理**

- **不限库存（STOCK_MODE.UNLIMITED）**: 不检查库存限制
- **限制库存（STOCK_MODE.LIMITED）**: 严格检查库存数量

### **SKU 选择规则**

- 多规格商品必须选择所有属性分组
- 单规格商品直接使用基础价格和库存
- SKU 匹配失败时使用商品基础信息

## 🚀 **使用示例**

### **HTML 模板调用示例**

```html
<!-- 添加商品到购物车 -->
<button @click="addGoods($event, item)">+</button>

<!-- 减少商品数量 -->
<button @click="delGoods($event, item)">-</button>

<!-- 选择商品属性 -->
<span @click="selectAttr(item, group.attr_group_id, attr.attr_id)">{{attr.attr_name}}</span>

<!-- 显示商品价格 -->
<span>{{getCurrentSkuPrice(item)}}</span>

<!-- 显示商品库存 -->
<span>{{getCurrentSkuStock(item)}}</span>
```

### **初始化调用**

```javascript
// 页面加载完成后自动初始化
document.addEventListener("DOMContentLoaded", initChooseGoodsApp);
```

## 📝 **常量定义**

### **CONSTANTS 对象**

```javascript
const CONSTANTS = {
  EXCHANGE_TYPE: { COUNT: 1, AMOUNT: 2 }, // 兑换类型
  STOCK_MODE: { UNLIMITED: 0, LIMITED: 1 }, // 库存模式
  UI: { GRID_COLUMNS: 2, AUTOPLAY_DELAY: 3000, SWIPER_SPEED: 1000 }, // UI配置
  DEFAULT_MAX_QUANTITY: 999, // 默认最大数量
};
```

## 🔧 **技术特点**

### **1. 模块化设计**

- 工具函数独立封装，职责单一
- Vue 组件方法按功能分组
- 代码复用性高，维护成本低

### **2. 性能优化**

- SKU 匹配算法高效
- 购物车计算批量处理
- 事件处理防抖设计

### **3. 用户体验**

- 实时价格和库存更新
- 智能 SKU 选择提示
- 流畅的交互动画

### **4. 数据一致性**

- 多层验证机制
- 状态同步管理
- 错误处理完善

## 📋 **维护指南**

### **1. 添加新功能**

- 在对应的工具模块中添加方法
- 在 Vue 应用中添加调用方法
- 更新 HTML 模板调用

### **2. 修改业务规则**

- 修改 ValidationUtils 中的验证逻辑
- 更新 CONSTANTS 中的常量值
- 测试相关功能

### **3. 性能优化**

- 监控 SKU 匹配算法性能
- 优化购物车计算频率
- 减少不必要的 DOM 操作

### **4. 错误处理**

- 完善 MessageUtils 的错误提示
- 添加边界情况处理
- 增强用户友好性

---

**文件位置**: `public/static/js/member/code/js/detail.js`
**依赖**: Vue.js 3.x, Swiper, layui
**最后更新**: 2025 年 1 月
**维护者**: 开发团队
