# 商品详情页 SKU 功能改造文档

## 概述

本文档详细说明了商品详情页 SKU 选择功能的完整改造过程，包括 UI 组件、JavaScript 逻辑、数据流转和用户交互流程。

## 改造目标

1. 在商品详情页添加 SKU 规格选择功能
2. 支持多规格商品的属性选择和 SKU 匹配
3. 实现 SKU 选择弹窗交互
4. 确保选择的 SKU 信息正确传递到订单系统
5. 保持与现有选择页面 SKU 逻辑的一致性

## 技术架构

### 数据结构

#### 商品数据结构

```javascript
{
  "is_attribute": 1,  // 是否有属性规格（1=有规格，0=无规格）
  "attr_group_list": [
    {
      "attr_group_name": "颜色",
      "attr_group_id": "group-id-1",
      "attr_list": [
        {
          "attr_name": "红色",
          "attr_id": "attr-id-1",
          "sku_list": [
            {
              "sku_guid": "sku-guid-1",
              "price": "99.00",
              "stock": 10,
              "picture": "image-url"
            }
          ]
        }
      ]
    }
  ]
}
```

#### SKU 状态管理

```javascript
// 全局变量
var goodsData = null; // 商品详情数据
var selectedAttrs = {}; // 用户选择的属性映射 {attr_group_id: attr_id}
var currentSku = null; // 当前匹配的SKU对象
var skuQuantity = 1; // SKU弹窗中的数量选择
```

#### 缓存数据格式

```javascript
let choose_goods_info = [
  {
    guid: "goods-guid",
    attr: [
      {
        attr_group_id: "group-id-1",
        attr_group_name: "颜色",
        attr_id: "attr-id-1",
        attr_name: "红色",
      },
    ],
    amount: 1,
  },
];
```

## 核心组件

### 1. SkuManager 对象

集中管理 SKU 相关的核心业务逻辑：

#### 方法列表

- `isFullySelected()` - 检查是否完全选择了 SKU
- `findMatching()` - 匹配 SKU 算法
- `isAttrAvailable()` - 检查属性是否可用
- `getSelectedSpecs()` - 获取已选规格文本
- `buildAttrArray()` - 构建属性数组（用于缓存）

### 2. UI 控制函数

#### 弹窗管理

- `openSkuModal()` - 打开 SKU 选择弹窗
- `closeSkuModal()` - 关闭 SKU 选择弹窗

#### 属性选择

- `generateSkuAttrOptions()` - 生成属性选择区域
- `selectAttr(groupId, attrId)` - 选择属性

#### 信息更新

- `updateSkuModalInfo()` - 更新弹窗中的 SKU 信息
- `updatePageSkuInfo()` - 更新页面中的 SKU 信息显示
- `updateConfirmButtonState()` - 更新确认按钮状态

#### 数量控制

- `decreaseSkuQuantity()` - 减少数量（显示提示）
- `increaseSkuQuantity()` - 增加数量（显示提示）

#### 兑换逻辑

- `exchange()` - 主兑换入口
- `executeExchange()` - 执行兑换逻辑
- `confirmSkuSelection()` - 确认 SKU 选择并执行兑换

## 页面加载流程

### 1. 初始化阶段

```
页面加载 → 调用商品详情API → 保存商品数据到goodsData → 渲染页面模板
```

### 2. 数据处理

```javascript
post_layui_member_api_v1("/goods/detail", { guid: guid }, function (result) {
  // 保存商品数据用于SKU处理
  goodsData = result.data;

  // 渲染页面
  render_template_beta(result.data, "body");
});
```

## 用户交互流程

### 1. 点击兑换按钮流程

```
用户点击"立即兑换"
↓
检查是否为多规格商品(is_attribute == 1)
↓
是：打开SKU选择弹窗
否：直接执行兑换逻辑
```

### 2. SKU 选择弹窗流程

```
打开弹窗
↓
显示商品信息（图片、名称、价格）
↓
生成属性选择区域
↓
用户选择属性值
↓
实时匹配SKU并更新显示
↓
选择完整规格后按钮变为"确认兑换"
↓
点击确认执行兑换逻辑
```

### 3. 属性选择流程

```
用户点击属性值
↓
更新selectedAttrs对象
↓
调用SkuManager.findMatching()匹配SKU
↓
更新currentSku对象
↓
刷新弹窗显示（价格、库存、按钮状态）
↓
更新页面显示（已选规格信息）
```

## 关键算法

### SKU 匹配算法

```javascript
findMatching() {
  // 1. 检查基本条件
  if (!goodsData || goodsData.is_attribute != 1) return null;

  // 2. 检查是否选择了所有属性分组
  if (Object.keys(selectedAttrs).length !== goodsData.attr_group_list.length) return null;

  // 3. 遍历所有属性分组，找到交集SKU
  let matchedSkus = null;
  for (const group of goodsData.attr_group_list) {
    const selectedAttrId = selectedAttrs[group.attr_group_id];
    const selectedAttr = group.attr_list.find(attr => attr.attr_id === selectedAttrId);

    // 计算SKU交集
    matchedSkus = matchedSkus ?
      matchedSkus.filter(sku1 =>
        selectedAttr.sku_list.some(sku2 => sku1.sku_guid === sku2.sku_guid)
      ) : selectedAttr.sku_list;
  }

  return matchedSkus?.[0] || null;
}
```

### 属性可用性检查算法

```javascript
isAttrAvailable(groupId, attrId) {
  // 1. 找到对应的属性分组和属性
  const group = goodsData.attr_group_list.find(g => g.attr_group_id === groupId);
  const attr = group.attr_list.find(a => a.attr_id === attrId);

  // 2. 检查该属性下是否有可用库存的SKU
  return attr.sku_list.some(sku => sku.stock === -1 || sku.stock > 0);
}
```

## CSS 样式组件

### 1. 弹窗容器样式

```css
.sku-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
}

.sku-modal {
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: 70vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}
```

### 2. 属性选择样式

```css
.sku-attr-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.sku-attr-option.selected {
  border-color: #2c68ff;
  background-color: #2c68ff;
  color: white;
}

.sku-attr-option.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}
```

### 3. 按钮状态样式

```css
.sku-confirm-btn:disabled {
  background-color: #ccc;
  color: #999;
  cursor: not-allowed;
}
```

## HTML 结构

### 1. 已选规格显示区域

```html
<div id="selected-sku-info" class="selected-sku-info" style="display: none;">
  <span class="sku-info-label">已选规格：</span>
  <span id="selected-sku-specs" class="goods-specs" onclick="openSkuModal()"></span>
</div>
```

### 2. SKU 选择弹窗结构

```html
<div id="sku-modal-overlay" class="sku-modal-overlay" style="display: none;">
  <div class="sku-modal">
    <!-- 商品信息头部 -->
    <div class="sku-modal-header">
      <div class="sku-modal-product">
        <img id="sku-modal-image" class="sku-modal-image" />
        <div class="sku-modal-info">
          <div id="sku-modal-name" class="sku-modal-name"></div>
          <div id="sku-modal-price" class="sku-modal-price"></div>
          <div id="sku-modal-stock" class="sku-modal-stock"></div>
        </div>
      </div>
    </div>

    <!-- 属性选择区域 -->
    <div id="sku-modal-content" class="sku-modal-content"></div>

    <!-- 底部操作区域 -->
    <div class="sku-modal-footer">
      <div class="sku-quantity-selector">
        <span class="sku-quantity-label">数量</span>
        <div class="sku-quantity-controls">
          <button class="sku-quantity-btn disabled">-</button>
          <span id="sku-quantity-display" class="sku-quantity-display">1</span>
          <button class="sku-quantity-btn disabled">+</button>
        </div>
      </div>
      <button id="sku-confirm-btn" class="sku-confirm-btn">确认兑换</button>
    </div>
  </div>
</div>
```

## 数据流转

### 1. 页面加载时

```
API返回商品数据 → 保存到goodsData → 初始化selectedAttrs = {} → 渲染页面
```

### 2. 用户选择属性时

```
点击属性值 → 更新selectedAttrs[groupId] = attrId → 匹配SKU → 更新currentSku → 刷新UI显示
```

### 3. 确认兑换时

```
构建属性数组 → 缓存到WebStorage → 执行兑换逻辑 → 跳转到订单页面
```

## 兼容性处理

### 1. 商品类型兼容

- **无规格商品** (`is_attribute != 1`): 直接执行兑换逻辑
- **单规格商品**: 自动选择唯一规格
- **多规格商品**: 弹出选择弹窗

### 2. 数据容错

- 检查`attr_group_list`是否存在
- 检查`sku_list`是否为空
- 处理库存为-1（无限库存）的情况

### 3. UI 状态管理

- 弹窗显示时隐藏返回顶部按钮
- 数量按钮始终显示为禁用状态
- 确认按钮根据规格选择状态动态启用/禁用

## 性能优化

### 1. 代码结构优化

- 使用 SkuManager 对象集中管理核心逻辑
- 避免重复的 DOM 查询操作
- 减少不必要的函数调用

### 2. 内存管理

- 及时清理事件监听器
- 避免内存泄漏

### 3. 用户体验优化

- 添加 CSS 动画效果
- 实时反馈用户操作
- 防止重复点击

## 测试要点

### 1. 功能测试

- [ ] 无规格商品正常兑换
- [ ] 多规格商品弹窗显示
- [ ] 属性选择和 SKU 匹配
- [ ] 价格库存实时更新
- [ ] 确认按钮状态控制

### 2. 兼容性测试

- [ ] 不同浏览器兼容性
- [ ] 移动端响应式显示
- [ ] 数据异常情况处理

### 3. 性能测试

- [ ] 大量 SKU 数据加载性能
- [ ] 频繁操作响应速度
- [ ] 内存使用情况

## 维护说明

### 1. 代码修改

- SKU 核心逻辑修改请在 SkuManager 对象中进行
- UI 相关修改请在对应的控制函数中进行
- 样式修改请在 CSS 部分进行

### 2. 数据格式变更

- 如需修改数据格式，请同步更新 SkuManager 中的相关方法
- 确保与后端 API 接口数据格式保持一致

### 3. 功能扩展

- 新增 SKU 相关功能请遵循现有的架构模式
- 保持与选择页面 SKU 逻辑的一致性

## 总结

本次改造成功实现了商品详情页的 SKU 选择功能，主要特点：

1. **完整的 SKU 选择体验**: 支持多规格商品的完整选择流程
2. **一致的数据格式**: 与现有系统保持数据格式一致性
3. **优化的代码结构**: 使用 SkuManager 集中管理核心逻辑
4. **良好的用户体验**: 实时反馈、动画效果、状态提示
5. **完善的兼容性**: 支持各种商品类型和异常情况处理

改造后的系统能够满足用户在商品详情页进行 SKU 选择和兑换的完整需求，同时保持了良好的代码可维护性和扩展性。
