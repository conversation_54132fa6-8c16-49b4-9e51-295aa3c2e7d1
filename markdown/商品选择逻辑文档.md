# 商品选择逻辑文档

## 概述

本文档详细说明了 `choose_goods.html` 页面中商品选择、SKU 管理、购物车操作的完整业务逻辑。

## 目录

1. [数据结构说明](#数据结构说明)
2. [SKU 选择逻辑](#sku选择逻辑)
3. [购物车操作流程](#购物车操作流程)
4. [数量验证规则](#数量验证规则)
5. [缓存机制](#缓存机制)
6. [关键方法说明](#关键方法说明)

## 数据结构说明

### 商品基础数据结构

```javascript
{
  "guid": "商品唯一标识",
  "name": "商品名称",
  "price": "商品基础价格",
  "is_attribute": 1,  // 0=无属性商品, 1=多属性商品
  "min_choose_num": 0, // 最小选择数量
  "max_choose_num": 1, // 最大选择数量
  "stock": -1,         // 库存(-1表示不限库存)
  "stock_mode": 0,     // 库存管理模式

  // 多属性商品特有字段
  "attr_group_list": [
    {
      "attr_group_id": "属性分组ID",
      "attr_group_name": "属性分组名称",
      "attr_list": [
        {
          "attr_id": "属性ID",
          "attr_name": "属性名称",
          "sku_list": [
            {
              "sku_guid": "SKU唯一标识",
              "price": "SKU价格",
              "stock": "SKU库存",
              "picture": "SKU图片"
            }
          ]
        }
      ]
    }
  ],

  // 运行时动态字段
  "selected_attrs": {},    // 用户选择的属性 {分组ID: 属性ID}
  "current_sku": null      // 当前匹配的SKU对象
}
```

### 购物车数据结构

```javascript
{
  // 购物车商品映射（当前简化版本）
  "choose_goods_list": {
    "商品GUID": 数量  // 注意：每个商品只能有一种SKU组合
  },

  // 统计信息
  "total_choose_num": 0,     // 总数量
  "total_choose_price": 0    // 总价格
}
```

**重要限制**：

- 当前版本中，**单个商品只能选择一种 SKU 组合**
- 例如：用户选择了"红色+大号"后，只能增加这个组合的数量
- 无法同时添加"红色+小号"和"蓝色+大号"到购物车
- 如果用户改变 SKU 选择，会覆盖原有的 SKU 信息，但数量保持不变

## SKU 选择逻辑

### 1. 商品类型判断

```javascript
if (item.is_attribute == 1) {
  // 多属性商品：需要用户选择具体的SKU属性
} else {
  // 单属性商品：直接使用商品基础价格
}
```

### 2. SKU 匹配算法

**核心原理**：通过属性的 SKU 列表取交集来找到匹配的 SKU

```javascript
// 算法步骤：
// 1. 检查是否选择了所有属性分组
// 2. 遍历每个属性分组，获取选中属性的SKU列表
// 3. 第一个属性分组：直接使用其SKU列表作为候选
// 4. 后续属性分组：与之前的候选SKU取交集
// 5. 返回第一个匹配的SKU
```

**示例**：

- 商品有两个属性分组：颜色、尺寸
- 用户选择：红色 + 大号
- 红色的 SKU 列表：[SKU1, SKU2, SKU3]
- 大号的 SKU 列表：[SKU2, SKU4, SKU5]
- 交集结果：[SKU2] ← 最终匹配的 SKU

### 3. 属性可用性检查

```javascript
// 检查某个属性是否可选（是否有对应的SKU且有库存）
isAttrAvailable(item, groupGuid, attrGuid) {
  // 1. 找到对应的属性对象
  // 2. 检查该属性下是否有可用的SKU
  // 3. 库存检查：-1表示不限库存，>0表示有库存
}
```

## 购物车操作流程

### 1. 添加商品流程 (add_goods)

```
用户点击"+"按钮
    ↓
检查是否为多属性商品
    ↓
[是] → 检查SKU是否选择完整
    ↓
[否] → 自动弹出SKU选择框
    ↓
[是] → 数量验证（最小/最大/库存限制）
    ↓
金额限制检查（针对兑换券）
    ↓
更新购物车数量
    ↓
重新计算总数和总价
```

### 2. 减少商品流程 (del_goods)

```
用户点击"-"按钮
    ↓
获取当前数量
    ↓
[数量>0] → 减少1个
    ↓
[新数量=0] → 清除SKU选择信息
    ↓
重新计算总数和总价
```

### 3. SKU 选择流程 (selectAttr)

```
用户点击属性选项
    ↓
更新selected_attrs对象
    ↓
调用SKU匹配算法
    ↓
[找到匹配SKU] → 更新current_sku和价格
    ↓
[商品在购物车中] → 重新计算总价
```

## 数量验证规则

### 验证优先级

1. **最小数量限制**：`targetQuantity >= min_choose_num`
2. **最大数量限制**：`targetQuantity <= max_choose_num`
3. **库存限制**：`targetQuantity <= current_stock` (如果启用库存管理)
4. **总体数量限制**：针对次数型券卡的总数量限制
5. **金额限制**：针对兑换券的单次最大金额限制

### 特殊规则

- **库存管理**：`stock_mode == 1` 时启用库存检查
- **无限库存**：`stock == -1` 表示不限库存
- **兑换券限制**：`exchange_goods_type == 2` 时检查金额限制

## 缓存机制

### 双缓存策略

为了保持系统兼容性，生成两种格式的缓存：

#### 1. choose_goods_list (兼容旧系统)

```javascript
{
  "goods_info": "[{\"guid\":\"商品ID\",\"attr\":[...],\"amount\":1}]", // JSON字符串
  "choose_goods_list": {"商品ID": 数量}
}
```

#### 2. choose_goods_info (新系统格式)

```javascript
"[{\"guid\":\"商品ID\",\"attr\":[{\"attr_group_id\":\"分组ID\",\"attr_group_name\":\"分组名\",\"attr_id\":\"属性ID\",\"attr_name\":\"属性名\"}],\"amount\":1}]";
```

### 缓存生成逻辑

```javascript
// 遍历购物车中的每个商品
Object.keys(choose_goods_list).forEach((key) => {
  const goodsItem = goods_list.find((goods) => goods.guid === key);

  // 构建商品信息对象
  const goods_info_item = {
    guid: goodsItem.guid,
    amount: choose_goods_list[key],
    attr: [],
  };

  // 如果是多规格商品，添加属性信息
  if (goodsItem.is_attribute == 1 && goodsItem.selected_attrs) {
    // 遍历属性分组，构建attr数组
  }
});
```

## 关键方法说明

### 核心方法

| 方法名             | 功能               | 调用时机           |
| ------------------ | ------------------ | ------------------ |
| `selectAttr`       | 选择商品属性       | 用户点击属性选项   |
| `findMatchingSku`  | 查找匹配的 SKU     | 属性选择后自动调用 |
| `add_goods`        | 添加商品到购物车   | 用户点击"+"按钮    |
| `del_goods`        | 从购物车减少商品   | 用户点击"-"按钮    |
| `validateQuantity` | 验证数量限制       | 添加商品前调用     |
| `recalculateTotal` | 重新计算总数和总价 | 购物车变化时调用   |
| `submit`           | 提交购物车数据     | 用户点击"下一步"   |

### 工具方法

| 方法名               | 功能               | 返回值    |
| -------------------- | ------------------ | --------- |
| `getCartKey`         | 生成购物车键值     | 商品 GUID |
| `getChooseQuantity`  | 获取商品数量       | 数字      |
| `getCurrentSkuPrice` | 获取当前 SKU 价格  | 数字      |
| `getCurrentSkuSpecs` | 获取规格描述       | 字符串    |
| `hasSelectedSku`     | 检查是否已选择 SKU | 布尔值    |
| `isAttrAvailable`    | 检查属性是否可用   | 布尔值    |

## 业务场景示例

### 场景 1：用户选择多规格商品

1. 用户看到商品列表，点击某个多规格商品的"+"按钮
2. 系统检测到 `is_attribute == 1` 且未选择完整 SKU
3. 自动弹出 SKU 选择弹窗
4. 用户依次选择各个属性（颜色、尺寸等）
5. 每次选择后调用 `selectAttr` → `findMatchingSku`
6. 找到匹配 SKU 后显示对应价格
7. 用户确认数量并点击确定
8. 商品添加到购物车，重新计算总价

### 场景 2：用户修改已选商品的规格

1. 用户点击商品列表中已显示的规格信息
2. 弹出 SKU 选择弹窗，显示当前选择
3. 用户修改属性选择
4. 系统重新匹配 SKU 和价格
5. 确认后更新购物车中的 SKU 信息
6. 重新计算总价（因为价格可能变化）

### 场景 3：库存不足处理

1. 用户尝试添加商品数量
2. 系统检查库存限制
3. 如果超出库存，显示错误提示
4. 用户只能选择库存范围内的数量

## 注意事项

### 开发注意点

1. **数据一致性**：确保 `selected_attrs` 和 `current_sku` 的同步
2. **性能优化**：避免频繁的总价重计算
3. **错误处理**：妥善处理 SKU 匹配失败的情况
4. **兼容性**：维护双缓存格式的一致性

### 常见问题

1. **SKU 匹配失败**：通常是属性选择不完整导致
2. **价格显示错误**：检查 `current_sku` 是否正确更新
3. **购物车数据丢失**：确认缓存生成逻辑是否正确
4. **数量验证失败**：检查各种限制条件的优先级

## 扩展建议

### 功能扩展

1. **多规格同时存在**（当前限制：单商品只能选一种SKU）：
   ```javascript
   // 修改getCartKey方法包含SKU信息
   getCartKey: function (item) {
     if (item.is_attribute == 1 && item.selected_attrs) {
       return item.guid + '_' + JSON.stringify(item.selected_attrs);
     }
     return item.guid;
   }

   // 购物车数据结构变为：
   choose_goods_list: {
     "商品GUID_{'颜色':'红色','尺寸':'大号'}": 数量1,
     "商品GUID_{'颜色':'蓝色','尺寸':'小号'}": 数量2
   }

   // 需要同步修改的方法：
   // - recalculateTotal: 遍历逻辑需要解析键值
   // - 缓存生成逻辑: 需要从键值中提取SKU信息
   // - 商品显示逻辑: 需要支持同一商品的多个SKU显示
   ```

2. **批量操作**：支持批量添加/删除商品
3. **收藏功能**：保存用户的属性选择偏好
4. **库存预警**：低库存时的提示机制

### 性能优化

1. **懒加载**：大量商品时的分页加载
2. **缓存优化**：减少不必要的计算
3. **防抖处理**：避免用户快速点击导致的重复操作

---

**文档版本**：v1.0  
**最后更新**：2025-01-23  
**维护人员**：开发团队
