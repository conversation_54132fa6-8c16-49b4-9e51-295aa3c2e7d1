{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "topthink/framework": "8.x-dev", "topthink/think-queue": "^3.0", "topthink/think-migration": "^3.1", "topthink/think-captcha": "^3.0", "workerman/phpsocket.io": "^1.1", "qiniu/php-sdk": "^7.13", "zoujingli/ip2region": "^2.0", "qcloudsms/qcloudsms_php": "^0.1.4", "zoujingli/weopen-developer": "dev-master", "aliyuncs/oss-sdk-php": "^2.7", "tp5er/tp5-databackup": "^1.0", "ext-sockets": "*", "ext-posix": "*", "yzh52521/think-mail": "^3.2", "ext-json": "*", "ext-zip": "*", "topthink/think-api": "^1.0", "phppkg/http-client": "^3.0", "workerman/gateway-worker": "^3.0", "workerman/gatewayclient": "^3.1", "vlucas/pikirasa": "^1.0", "ennnnny/tbk": "^0.4.2", "kosinix/grafika": "^2.0", "baidu/aip-sdk": "^2.2", "wechatpay/wechatpay-guzzle-middleware": "^0.2.2", "phpoffice/phpword": "^1.3", "phpmailer/phpmailer": "^6.9", "geerlingguy/ping": "^1.2", "bingher/ding-bot": "^1.4", "ext-pcntl": "*", "yunwuxin/think-mail": "^4.0", "spatie/guzzle-rate-limiter-middleware": "^2.0", "yakeing/php_fsockopen": "^2.1", "yidas/phpspreadsheet-helper": "^1.3", "phpoffice/phpspreadsheet": "~1.28.0", "phpoption/phpoption": "^1.8", "xieyongfa/work_weixin": "dev-master", "hsldymq/bugs-bunny": "^0.3.2", "code-distortion/fluent-dotenv": "^0.3.3", "ext-redis": "*", "predis/predis": "^1.1", "jamespi/php-redis": "dev-master", "nabao/lock": "^1.2", "workerman/crontab": "^1.0", "khanamiryan/qrcode-detector-decoder": "*******", "alapi/address_parse": "^0.0.3", "jayin/address-parser": "^1.0", "php-amqplib/php-amqplib": "^3.3", "topthink/think-swoole": "^4.0", "topthink/think-filesystem": "*", "ddeboer/imap": "1.14.2", "zhaohangyang/lop-opensdk-php": "^1.0", "alibabacloud/dyvmsapi-20170525": "^2.1", "alibabacloud/darabonba-openapi": "^0.2.9", "orhanerday/open-ai": "^4.7", "xingwenge/canal_php": "^1.0", "ext-curl": "*", "whichbrowser/parser": "^2.1", "ext-openssl": "*", "ext-xlswriter": "*", "funkjedi/composer-include-files": "^1.1", "endroid/qr-code": "~4.0", "topthink/think-trace": "^1.6", "topthink/think-throttle": "^2.0", "liliuwei/thinkphp-jump": "^2.0", "thans/tp-jwt-auth": "^2.2", "opis/closure": "^3.6", "topthink/think-orm": "4.0.x-dev", "zoujingli/wechat-developer": "dev-master", "symfony/var-exporter": "^6.0", "symfony/dom-crawler": "^6.0", "php-mqtt/client": "^2.2", "intervention/image": "^2.7", "ext-gd": "*", "ext-imagick": "*", "cyokup/easy-aichat": "dev-master", "textalk/websocket": "^1.6"}, "autoload": {"psr-4": {"app\\": "app"}, "psr-0": {"": "extend/"}, "files": ["app/helpers.php"], "exclude-from-classmap": ["app/controller/test", "app/validate/test"]}, "config": {"preferred-install": "dist", "optimize-autoloader": true, "allow-plugins": {"funkjedi/composer-include-files": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "extra": {"include_files": ["app/helpers.php"]}}