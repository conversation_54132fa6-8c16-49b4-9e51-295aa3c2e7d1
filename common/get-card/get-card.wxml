<block wx:if='{{show_card}}'>
    <view class='get-card flex-x-center flex-y-center'>
        <view class='card-block'>
            <image src='/images/icon-card-top.png' style='width:630rpx;height:330rpx;'></image>
            <view class='card-del flex-x-center flex-y-center' bindtap='cardDel'>
                <image src='/images/icon-card-del.png'></image>
            </view>
            <view class='flex-x-center'>
                <view class='card-info'>
                    <view class='flex-x-center card-get'>获得{{goods_card_list.length}}张卡券</view>
                    <block wx:for='{{goods_card_list}}'>
                        <block wx:if='{{index<1}}'>
                            <view class='flex-x-center card-one'>
                                <view class='card-bg'>
                                    <view class='card-bg-1 flex-row'>
                                        <view class='flex-grow-0 flex-y-center'>
                                            <image src='{{item.pic_url}}' style='width:70rpx;height:70rpx;margin-right:32rpx;'></image>
                                        </view>
                                        <view class='flex-grow-1 flex-y-center'>
                                            <view class='text-more-2'>{{item.content}}</view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </block>
                    </block>
                    <view class='card-text flex-x-center'>卡券将5分钟内放入您的卡包</view>
                    <view class='flex-x-center card-btn' bindtap='cardTo'>
                        <image src='/images/icon-card-btn.png'></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
</block>