/**
 * 文件上传
 */
var config = require("../config.js");
var api = require("../api.js");
var http = require("../utils/request.js");

async function get_upload_file_url() {
  let access_token = wx.getStorageSync('access_token');
  let access_token_expires_time = wx.getStorageSync('access_token_expires_time');
  let expire = (!access_token || parseInt(access_token_expires_time) < parseInt(Date.parse(new Date()).toString().substr(0, 10))); //access_token是否过期
  if (expire) {
    await http.refreshToken(); //过期则同步刷新access_token
  }
  let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
  let protocol = wx.getAccountInfoSync().miniProgram.envVersion == 'develop' ? 'http' : 'https';
  return protocol + '://' + extConfig.domain + config.path + api.file.upload + '?access_token=' + wx.getStorageSync("access_token");
}

async function uploader(file, callBack) {
  let upload_file_url = await get_upload_file_url();
  wx.uploadFile({
    url: upload_file_url,
    filePath: file, //需要上传的文件
    name: file, //文件名称
    header: {
      "Content-Type": "multipart/form-data"
    },
    success: function (res) {
      //成功,文件返回值存入成功列表
      if (res && res.data) {
        var data = JSON.parse(res.data);
        if (data.code === 0) {
          if (callBack) {
            callBack(data.data.filePath);
          }
        } else {
          wx.hideToast();
          wx.showModal({
            title: '错误提示',
            content: '上传图片失败',
            showCancel: false
          });
        }
      }
    }
  });
};
/**
 * 采用递归的方式多文件上传
 * imgPaths:需要上传的文件列表
 * index：imgPaths开始上传的序号
 * successFiles:已上传成功的文件
 * callBack：文件上传后的回调函数
 */
async function uploadFiles(imgPaths, index, successFiles, callBack) {
  var that = this;
  let upload_file_url = await get_upload_file_url();
  wx.showLoading({
    title: '正在上传第' + (index + 1) + '张',
  })
  wx.uploadFile({
    url: upload_file_url,
    filePath: imgPaths[index],
    name: imgPaths[index],
    header: {
      "Content-Type": "multipart/form-data"
    },
    success: function (res) {
      //成功,文件返回值存入成功列表
      if (res && res.data) {
        var data = JSON.parse(res.data);
        if (data.code === 0) {
          successFiles.push(data.data.filePath);
        } else {
          wx.hideToast();
          wx.showModal({
            title: '错误提示',
            content: '上传图片失败',
            showCancel: false
          });
        }
      }
    },
    complete: function (e) {
      index++; //下一张
      if (index == imgPaths.length) {
        wx.hideLoading();
        //上传完毕，作一下提示
        wx.showToast({
          title: '上传成功(' + successFiles.length + '张)',
          icon: 'success',
          duration: 2000
        });
        if (callBack) {
          callBack(successFiles);
        }
      } else {
        //递归调用，上传下一张
        that.uploadFiles(imgPaths, index, successFiles, callBack);
      }
    }
  })
}

module.exports = {
  uploader: uploader,
  uploadFiles: uploadFiles
};