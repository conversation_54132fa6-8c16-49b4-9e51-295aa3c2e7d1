<!--pages/tabbar/tabbar.wxml-->
<template name="tabbar">
  <view class="navbar" style="background-color:{{tabbar.backgroundColor}}; border-top-color:{{tabbar.borderStyle}}; {{tabbar.position == 'top' ? 'top:0' : 'bottom:0'}}">
    <block wx:for="{{tabbar.list}}" wx:for-item="item" wx:key="index">
      <navigator class="tabbar_nav" url="{{item.pagePath}}" style="color:{{item.selected ? tabbar.selectedColor : tabbar.color}}" open-type="redirect">
        <image class="tabbar_icon" src="{{item.selected ? item.selectedIconPath : item.iconPath}}"></image>
        <text class="navbar-text">{{item.text}}</text>
      </navigator>
    </block>
  </view>
</template>