/* pages/tabbar/tabbar.wxss */

.tabbar_box {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  width: 100%;
  height: 120rpx;
  border-top: 1rpx solid gray;
}

.tabbar_nav {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  height: 100%;
  min-width: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  display: -webkit-box;
  display: -webkit-flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  -ms-grid-row-align: center;
}

.tabbar_icon {
  width: 61rpx;
  height: 61rpx;
}

.navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 115rpx;
  background: #fff;
  color: #555;
  z-index: 2000;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row;
  flex-direction: row;
}

.navbar navigator {
  height: 100%;
  width: 1%;
}

.navbar navigator > view {
  width: 100%;
  padding-top: 4px;
}

.navbar .navbar-icon {
  width: 64rpx;
  height: 64rpx;
  display: block;
  margin: 0 auto;
}

.navbar .navbar-text {
  font-size: 8pt;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.navbar + .after-navber {
  padding-bottom: 115rpx;
}

.navbar ~ .float-icon, .navbar + .after-navber .float-icon {
  bottom: 170rpx !important;
}
