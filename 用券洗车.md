# 扫码洗车系统开发需求文档

## 1. 项目概述

### 1.1 项目背景

基于一卡易会员系统开发的扫码洗车功能，用户通过扫描二维码进入洗车页面，选择可用的洗车券进行核销，系统自动调用 Bolink 设备开闸，实现无人值守的洗车服务。

### 1.2 技术栈

- **前端**: Vue 3 + Vant 4 UI 组件库
- **后端**: ThinkPHP 6 + 一卡易 API + Bolink 设备 API
- **数据库**: MySQL（使用一卡易现有数据结构）

### 1.3 核心功能

1. 扫码进入洗车页面
2. 获取用户可用洗车券列表
3. 选择券并核销
4. 调用 Bolink 设备开闸
5. 显示操作结果

## 2. 文件结构

```
项目根目录/
├── app/
│   ├── controller/
│   │   └── member_api/
│   │       └── v1/
│   │           └── Yikayi.php                 # 后端API控制器
│   └── view/
│       └── member/
│           └── yikayi/
│               └── wash_car.html              # 前端页面模板
├── public/
│   └── static/
│       ├── css/
│       │   └── member/
│       │       └── yikayi/
│       │           └── wash_car.css           # 页面样式文件
│       └── js/
│           ├── function.js                    # 全局工具函数（已修改）
│           └── member/
│               └── yikayi/
│                   └── wash_car.js            # 页面逻辑脚本
```

## 3. 前端开发详情

### 3.1 页面模板 (`app/view/member/yikayi/wash_car.html`)

#### 3.1.1 模板结构

```html
{layout name="vant" /}
<!-- 使用vant布局模板 -->
<link href="/static/css/member/yikayi/wash_car.css?v=1" rel="stylesheet" type="text/css" />

<div id="app" v-cloak>
  <!-- 页面标题 -->
  <van-nav-bar title="扫码洗车" left-arrow @click-left="goBack" />

  <!-- 调试信息（开发模式显示） -->
  <div v-if="showDebugInfo" class="debug-info">...</div>

  <!-- 加载状态 -->
  <van-loading v-if="showLoading" type="spinner" color="#1989fa" vertical>获取券列表中...</van-loading>

  <!-- 券列表 -->
  <div v-if="showCouponList" class="coupon-container">
    <van-coupon-list :coupons="formattedCoupons" :chosen-coupon="chosenCoupon" @change="onCouponChange" @exchange="onExchange" />
    <div class="action-container">
      <van-button type="primary" size="large" :disabled="chosenCoupon === -1" :loading="using" @click="useCoupon" block>{{ using ? '核销中...' : '立即使用洗车券' }}</van-button>
    </div>
  </div>

  <!-- 空状态 -->
  <van-empty v-if="showEmptyState" description="暂无可用洗车券" image="coupon">
    <van-button type="primary" @click="refreshCoupons">刷新</van-button>
  </van-empty>

  <!-- 成功弹窗 -->
  <van-dialog v-model:show="showSuccess" title="开闸成功" :show-cancel-button="false" :show-confirm-button="false" :close-on-click-overlay="false">
    <div class="success-content">
      <van-icon name="success" size="48" color="#07c160" />
      <p>洗车闸门已开启</p>
      <p>请进入洗车区域</p>
      <div class="countdown">{{ countdown }}秒后跳转...</div>
    </div>
  </van-dialog>
</div>

<!-- 脚本引入 -->
<script src="/static/js/web-storage-cache.min.js?v=1.1.0"></script>
<script src="/static/js/function.js?v=20250521"></script>
<script src="/static/js/member/common.js?v=2025080401"></script>
<script src="/static/js/member/yikayi/wash_car.js?v=1"></script>
```

#### 3.1.2 关键组件说明

- **van-nav-bar**: 页面导航栏
- **van-loading**: 加载状态指示器
- **van-coupon-list**: Vant 优惠券列表组件
- **van-empty**: 空状态组件
- **van-dialog**: 成功提示弹窗

### 3.2 页面脚本 (`public/static/js/member/yikayi/wash_car.js`)

#### 3.2.1 Vue 应用结构

```javascript
const { createApp } = Vue;
const { showToast, showConfirm, showDialog, showLoadingToast } = vant;

createApp({
  data() {
    return {
      // 页面参数
      bid: "",                    // 商家ID
      guid: "",                   // 设备GUID

      // 页面状态
      loading: true,              // 加载状态
      using: false,               // 核销中状态
      showSuccess: false,         // 成功弹窗显示状态
      countdown: 3,               // 倒计时秒数

      // 券数据
      couponList: [],             // 券列表原始数据
      chosenCoupon: -1,           // 选中的券索引

      // 调试相关
      showDebugInfo: false,       // 是否显示调试信息
      forceEmptyState: false,     // 强制显示空状态（调试用）

      // 定时器
      countdownTimer: null        // 倒计时定时器
    };
  },

  computed: {
    // 格式化券数据为Vant组件需要的格式
    formattedCoupons() { ... },

    // 计算属性：控制各状态显示
    showLoading() { ... },
    showCouponList() { ... },
    showEmptyState() { ... }
  },

  mounted() {
    this.initPage();
  },

  methods: { ... },

  beforeUnmount() {
    // 清理定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  }
})
.use(vant)
.mount("#app");
```

#### 3.2.2 核心方法详解

##### `initPage()` - 页面初始化

```javascript
initPage() {
  // 获取URL参数
  this.bid = getQueryString("bid") || "";
  this.guid = getQueryString("guid") || "";

  // 检查是否开启调试模式
  this.showDebugInfo = getQueryString("debug") === "1";

  // 参数验证
  if (!this.bid) {
    showToast("缺少必要参数bid");
    return;
  }
  if (!this.guid) {
    showToast("缺少设备参数guid");
    return;
  }

  // 获取券列表
  this.getCouponList();
}
```

##### `getCouponList()` - 获取券列表

```javascript
getCouponList() {
  this.loading = true;

  // 调用静默API（不显示loading toast）
  ajax_vant_member_api_v1(
    "/yikayi/get_coupon_list",
    {
      bid: this.bid,
      guid: this.guid,
    },
    (result) => {
      // 解析嵌套数据结构 {data: {status: 0, total: 0, data: []}}
      const newCouponList = result.data && result.data.data ? result.data.data : [];
      this.couponList = [...newCouponList];

      // 使用nextTick确保DOM更新
      this.$nextTick(() => {
        this.loading = false;
        this.$nextTick(() => {
          // 双重nextTick确保DOM完全更新
          if (!this.loading && this.couponList.length === 0) {
            this.$forceUpdate(); // 强制更新DOM
          }
        });
      });
    },
    (error) => {
      console.error("获取券列表失败:", error);
      showToast(error.msg || "获取券列表失败");
      this.loading = false;
    }
  );
}
```

##### `useCoupon()` - 使用券核销

```javascript
useCoupon() {
  if (this.chosenCoupon === -1) {
    showToast("请先选择优惠券");
    return;
  }

  this.using = true;
  const originalCoupon = this.couponList[this.chosenCoupon];

  // 调用核销接口
  post_vant_member_api_v1(
    "/yikayi/use_wash_car_coupon",
    {
      bid: this.bid,
      guid: this.guid,
      coupon_send_guid: originalCoupon.Guid,
    },
    (result) => {
      this.using = false;
      this.showSuccessDialog(); // 显示成功弹窗
    },
    (error) => {
      this.using = false;
      showDialog({
        title: "核销失败",
        message: error.msg || "核销失败，请重试",
      });
    }
  );
}
```

##### `showSuccessDialog()` - 显示成功弹窗

```javascript
showSuccessDialog() {
  this.showSuccess = true;
  this.countdown = 3;

  // 倒计时跳转
  this.countdownTimer = setInterval(() => {
    this.countdown--;
    if (this.countdown <= 0) {
      this.jumpToSuccessPage();
    }
  }, 1000);
}
```

#### 3.2.3 数据格式转换

**API 返回的券数据格式**:

```javascript
{
  "Guid": "券发送记录ID",
  "Title": "券标题",
  "Content": "券描述",
  "StartDate": "开始时间",
  "EndDate": "结束时间",
  "EnableCount": "可用次数"
}
```

**转换为 Vant 组件格式**:

```javascript
{
  id: index,
  name: "券标题",
  condition: "有效期至 2024-12-31",
  startAt: "开始时间",
  endAt: "结束时间",
  description: "券描述",
  reason: "已用完", // 不可用原因
  value: 1, // 可用次数
  valueDesc: "剩余1张",
  unitDesc: "张",
  _original: originalData // 保存原始数据
}
```

### 3.3 样式文件 (`public/static/css/member/yikayi/wash_car.css`)

#### 3.3.1 关键样式类

```css
/* 基础样式 */
[v-cloak] {
  display: none !important;
}
body {
  background-color: #f5f5f5;
  margin: 0;
  padding: 0;
}
#app {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 券容器 */
.coupon-container {
  padding: 16px;
}
.page-desc {
  text-align: center;
  margin-bottom: 16px;
  color: #666;
}

/* 操作按钮 */
.action-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* 成功弹窗 */
.success-content {
  text-align: center;
  padding: 20px;
}
.countdown {
  color: #999;
  font-size: 14px;
  margin-top: 10px;
}

/* 空状态 */
.van-empty {
  margin-top: 100px;
  background-color: transparent;
  padding: 20px;
}

/* 隐藏ThinkPHP调试信息 */
#think_page_trace_open,
#think_page_trace_tab,
.think_page_trace,
.trace-info {
  display: none !important;
}
```

### 3.4 全局函数修改 (`public/static/js/function.js`)

#### 3.4.1 新增 Vant 请求封装函数

**带 Loading 的请求函数**:

```javascript
function post_vant_member_api_v1(url, data, success, failed) {
  // 支持Promise和回调两种风格
  if (!success && !failed) {
    return new Promise((resolve, reject) => {
      post_vant_member_api_v1(url, data, resolve, reject);
    });
  }

  // 显示加载提示
  const loadingToast = window.vant.showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0,
  });

  // 使用现有ajax方法
  ajax({
    url: "/member_api/v1" + url,
    data: data,
    method: "POST",
    success: function (result) {
      loadingToast.close();

      if (result.code === 0) {
        if (success && typeof success === "function") {
          success(result);
        }
      } else {
        const errorMsg = result.msg || "请求失败";
        window.vant.showToast(errorMsg);
        if (failed && typeof failed === "function") {
          failed(result);
        }
      }
    },
    error: function (xhr, status, error) {
      loadingToast.close();
      window.vant.showToast("网络错误，请重试");
      if (failed && typeof failed === "function") {
        failed({ code: -1, msg: "网络错误" });
      }
    },
  });
}
```

**静默请求函数**:

```javascript
function ajax_vant_member_api_v1(url, data, success, failed) {
  // 同上，但不显示loading提示
}
```

## 4. 后端开发详情

### 4.1 API 控制器 (`app/controller/member_api/v1/Yikayi.php`)

#### 4.1.1 获取券列表接口

**方法**: `get_coupon_list()`
**路径**: `/member_api/v1/yikayi/get_coupon_list`
**请求方式**: POST

**请求参数**:

```php
$params = [
    'bid' => '商家ID',
    'guid' => '设备GUID' // 可选，用于后续扩展
];
```

**核心逻辑**:

```php
public function get_coupon_list()
{
    $params = $this->params;
    $bid = $this->get_bid();
    $yky_member_guid = $this->get_yky_member_guid_by_cookie();
    $config = get_config_by_bid($bid);

    // 洗车券GUID（固定）
    $coupon_guid = '("b8e55ad3-57e6-11f0-9a1b-34735a9c7120")';

    $yky_coupon = Yky::Coupon($config);
    $post_data = [
        'where' => "1=1 AND Flag=1 AND MemberGuid='" . $yky_member_guid . "' AND CouponGuid IN " . $coupon_guid . " AND EnableCount>0 ",
        'orderBy' => 'EndDate ASC'
    ];

    $coupon_send_list = $yky_coupon->Get_CoupnSendPagedV2($post_data);
    if ($coupon_send_list === false) {
        error($yky_coupon->message);
    }

    result($coupon_send_list);
}
```

**返回数据格式**:

```json
{
  "code": 0,
  "msg": "success",
  "time": 1754374732,
  "data": {
    "status": 0,
    "total": 2,
    "data": [
      {
        "Guid": "券发送记录ID",
        "Title": "洗车券",
        "Content": "券描述",
        "StartDate": "2024-01-01 00:00:00",
        "EndDate": "2024-12-31 23:59:59",
        "EnableCount": 1
      }
    ]
  }
}
```

#### 4.1.2 使用洗车券接口

**方法**: `use_wash_car_coupon()`
**路径**: `/member_api/v1/yikayi/use_wash_car_coupon`
**请求方式**: POST

**请求参数**:

```php
$params = [
    'coupon_send_guid' => '优惠券发送记录ID',
    'guid' => '设备GUID'
];
```

**核心逻辑**:

```php
public function use_wash_car_coupon()
{
    $params = $this->params;
    $bid = $this->get_bid();

    // 获取参数
    $coupon_send_guid = $params['coupon_send_guid'] ?? '';
    $device_guid = $params['guid'] ?? '';

    // 参数验证
    if (empty($coupon_send_guid)) {
        error('缺少优惠券参数');
    }
    if (empty($device_guid)) {
        error('缺少设备参数');
    }

    // 获取会员信息（验证会员身份）
    $yky_member_guid = $this->get_yky_member_guid_by_cookie();
    if (empty($yky_member_guid)) {
        error('请先登录');
    }

    // 获取配置
    $config = get_config_by_bid($bid);

    try {
        // 1. 核销优惠券
        $yky_coupon = Yky::Coupon($config);
        $sub_data = [
            'couponSendGuid' => $coupon_send_guid,
            'subCount' => 1,
        ];

        $coupon_result = $yky_coupon->SubCoupon($sub_data);
        if ($coupon_result === false) {
            error('优惠券核销失败：' . $yky_coupon->message);
        }

        if (!isset($coupon_result['couponUsedNoteGuid'])) {
            error('优惠券核销失败，请重试');
        }

        // 2. 根据设备GUID获取通道ID（当前写死用于测试）
        $channel_id = '100189008'; // 测试用的固定通道ID

        // 3. 调用Bolink开闸
        $bolink = new \OpenApi\Bolink();
        $liftrod_id = time(); // 使用时间戳作为操作ID
        $operate = 0; // 0-抬杆，1-落杆

        $gate_result = $bolink->operateLiftrod($channel_id, $liftrod_id, $operate);
        if ($gate_result === false) {
            // 开闸失败，但券已核销，记录错误但不回滚
            wr_log('开闸失败，券已核销：' . $bolink->errmsg . '，券核销ID：' . $coupon_result['couponUsedNoteGuid'], 1, $bid);
            error('开闸失败：' . $bolink->errmsg);
        }

        // 4. 记录操作日志
        wr_log('洗车券核销成功并开闸：设备' . $device_guid . '，通道' . $channel_id . '，券核销ID：' . $coupon_result['couponUsedNoteGuid'], 0, $bid);

        // 5. 返回成功结果
        result([
            'coupon_used_guid' => $coupon_result['couponUsedNoteGuid'],
            'channel_id' => $channel_id,
            'device_guid' => $device_guid,
            'operate_time' => date('Y-m-d H:i:s'),
            'gate_result' => $gate_result
        ], '开闸成功');

    } catch (Exception $e) {
        wr_log('洗车券核销异常：' . $e->getMessage(), 1, $bid);
        error('操作失败：' . $e->getMessage());
    }
}
```

**返回数据格式**:

```json
{
  "code": 0,
  "msg": "开闸成功",
  "data": {
    "coupon_used_guid": "券核销记录ID",
    "channel_id": "100189008",
    "device_guid": "设备GUID",
    "operate_time": "2024-01-01 12:00:00",
    "gate_result": "开闸API返回结果"
  }
}
```

## 5. 业务流程

### 5.1 完整业务流程图

```mermaid
graph TD
    A[用户扫码] --> B[跳转洗车页面]
    B --> C[获取URL参数 bid, guid]
    C --> D{参数验证}
    D -->|失败| E[显示错误提示]
    D -->|成功| F[调用获取券列表API]
    F --> G{是否有可用券}
    G -->|无券| H[显示空状态页面]
    G -->|有券| I[显示券列表]
    I --> J[用户选择券]
    J --> K[点击使用按钮]
    K --> L[调用核销API]
    L --> M{核销是否成功}
    M -->|失败| N[显示错误弹窗]
    M -->|成功| O[调用Bolink开闸]
    O --> P{开闸是否成功}
    P -->|失败| Q[显示开闸失败]
    P -->|成功| R[显示成功弹窗]
    R --> S[3秒倒计时]
    S --> T[跳转到成功页面]
    H --> U[点击刷新按钮]
    U --> F
```

### 5.2 错误处理机制

#### 5.2.1 前端错误处理

- **参数缺失**: 显示 toast 提示，阻止后续操作
- **网络错误**: 显示重试提示，提供刷新按钮
- **核销失败**: 显示具体错误信息，允许重新选择券

#### 5.2.2 后端错误处理

- **券核销成功但开闸失败**: 记录日志，不回滚券状态，返回开闸错误
- **券核销失败**: 直接返回错误，不调用开闸接口
- **参数验证失败**: 返回具体的参数错误信息

### 5.3 日志记录

**成功日志**:

```
洗车券核销成功并开闸：设备{device_guid}，通道{channel_id}，券核销ID：{coupon_used_guid}
```

**失败日志**:

```
开闸失败，券已核销：{error_message}，券核销ID：{coupon_used_guid}
洗车券核销异常：{exception_message}
```

## 6. 部署和配置

### 6.1 环境要求

- PHP 7.4+
- ThinkPHP 6.x
- MySQL 5.7+
- 一卡易 API 接入权限
- Bolink 设备 API 接入权限

### 6.2 配置项

#### 6.2.1 洗车券 GUID 配置

```php
// 在 app/controller/member_api/v1/Yikayi.php 中
$coupon_guid = '("b8e55ad3-57e6-11f0-9a1b-34735a9c7120")';
```

#### 6.2.2 Bolink 通道 ID 配置

```php
// 当前写死，后续需要根据设备GUID动态获取
$channel_id = '100189008';
```

### 6.3 访问地址

**页面访问**:

```
https://domain.com/member/yikayi/wash_car.html?bid={商家ID}&guid={设备GUID}
```

**调试模式**:

```
https://domain.com/member/yikayi/wash_car.html?bid={商家ID}&guid={设备GUID}&debug=1
```

**API 接口**:

```
POST /member_api/v1/yikayi/get_coupon_list
POST /member_api/v1/yikayi/use_wash_car_coupon
```

## 7. 测试用例

### 7.1 前端测试

#### 7.1.1 页面加载测试

- [ ] 正常参数访问页面
- [ ] 缺少 bid 参数
- [ ] 缺少 guid 参数
- [ ] 开启 debug 模式

#### 7.1.2 券列表测试

- [ ] 有可用券的情况
- [ ] 无可用券的情况
- [ ] 网络错误的情况
- [ ] API 返回错误的情况

#### 7.1.3 核销流程测试

- [ ] 正常核销成功
- [ ] 券核销失败
- [ ] 开闸失败
- [ ] 网络异常

### 7.2 后端测试

#### 7.2.1 获取券列表接口测试

```bash
curl -X POST "https://domain.com/member_api/v1/yikayi/get_coupon_list" \
  -H "Content-Type: application/json" \
  -d '{"bid":"test-bid","guid":"test-guid"}'
```

#### 7.2.2 核销券接口测试

```bash
curl -X POST "https://domain.com/member_api/v1/yikayi/use_wash_car_coupon" \
  -H "Content-Type: application/json" \
  -d '{"coupon_send_guid":"test-guid","guid":"device-guid"}'
```

## 8. 待优化项目

### 8.1 功能优化

1. **设备 GUID 与通道 ID 映射**: 建立设备 GUID 到 Bolink 通道 ID 的映射关系
2. **券类型扩展**: 支持多种类型的洗车券
3. **用户身份验证**: 增强用户身份验证机制
4. **操作记录**: 完善用户操作记录和统计

### 8.2 性能优化

1. **缓存机制**: 对券列表进行缓存
2. **异步处理**: 开闸操作异步化处理
3. **错误重试**: 增加自动重试机制
4. **日志优化**: 结构化日志记录

### 8.3 用户体验优化

1. **离线提示**: 网络断开时的友好提示
2. **操作反馈**: 更丰富的操作反馈动画
3. **多语言支持**: 支持多语言界面
4. **无障碍访问**: 提升无障碍访问体验

## 9. 常见问题

### 9.1 前端问题

**Q: 空状态不显示？**
A: 检查 Vue 的 DOM 更新机制，确保使用了`$nextTick`和计算属性

**Q: Vant 组件样式不生效？**
A: 确保正确引入了 Vant CSS 文件，并使用`.use(vant)`注册组件

**Q: 调试信息不显示？**
A: 确保 URL 中包含`debug=1`参数

### 9.2 后端问题

**Q: 券核销失败？**
A: 检查一卡易 API 配置和券 GUID 是否正确

**Q: 开闸失败？**
A: 检查 Bolink API 配置和通道 ID 是否正确

**Q: 会员身份验证失败？**
A: 检查 cookie 中的会员 GUID 是否有效

## 10. 开发注意事项

### 10.1 Vue 3 + Vant 4 注意事项

1. **组件注册**: 必须使用 `.use(vant)` 注册组件
2. **响应式数据**: 注意 Vue 3 的响应式系统变化
3. **DOM 更新**: 使用 `$nextTick` 确保 DOM 更新时序
4. **计算属性**: 优先使用计算属性而不是直接条件判断

### 10.2 ThinkPHP 6 注意事项

1. **命名空间**: 注意控制器的命名空间声明
2. **中间件**: 正确配置会员身份验证中间件
3. **异常处理**: 使用 try-catch 包装关键业务逻辑
4. **日志记录**: 使用 wr_log 记录关键操作日志

### 10.3 API 集成注意事项

1. **一卡易 API**: 注意券 GUID 的正确配置
2. **Bolink API**: 注意通道 ID 与设备 GUID 的映射关系
3. **错误处理**: 区分券核销失败和开闸失败的处理逻辑
4. **日志记录**: 详细记录 API 调用的成功和失败情况

---

**文档版本**: v1.0
**最后更新**: 2024-01-01
**维护人员**: 开发团队
