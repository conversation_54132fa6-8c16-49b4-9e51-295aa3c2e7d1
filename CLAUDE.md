# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development & Build Commands
```bash
# Install PHP dependencies
composer install

# Update PHP dependencies
composer update

# Run ThinkPHP console commands
php think

# Start crontab worker (定时任务)
php think crontab --sleep=60 --memory=8

# Start queue worker
php think queue:work

# Start WebSocket server (客服系统)
php think kefu:socket

# Start customer service worker
php think kefu:worker

# Database migration
php think migrate:run
```

### Available Batch Scripts (Windows)
```bash
# Start crontab worker
crontab.bat

# Start queue workers
queue.bat
queue_amqp.bat  
queue_host.bat

# Start customer service system
kefu.bat

# Start Swoole queue worker
swoole_queue.bat

# Update composer dependencies
composer_update.bat

# Git pull and composer update
git_pull_composer_update.bat

# Clear field cache
remove_field_cache.bat

# Canal data sync
canal.bat
```

## Architecture Overview

### Core Framework
- **ThinkPHP 8.x**: Main application framework
- **MySQL**: Primary database with MySQL2 connector
- **Redis**: Caching and session storage
- **WebSocket**: Real-time communication via Workerman/GatewayWorker

### Application Structure
```
app/
├── BaseController.php          # Base controller with common functionality
├── common.php                  # Global helper functions
├── helpers.php                 # Additional helper functions
├── controller/                 # Application controllers
│   ├── admin/                 # Admin backend controllers
│   ├── admin_api/             # Admin API controllers  
│   ├── api/                   # Public API controllers
│   ├── member/                # Member frontend controllers
│   ├── gateway/               # Gateway/webhook controllers
│   └── index/                 # Public frontend controllers
├── model/                     # Database models (100+ models)
├── service/                   # Business logic services
├── command/                   # Console commands
├── middleware/                # HTTP middleware
├── validate/                  # Validation classes
└── common/                    # Shared utilities
    ├── service/               # Common services
    ├── tools/                 # Utility classes
    └── validate/              # Common validation
```

### Key Services & Components

#### Customer Service System (客服系统)
- **WebSocket Server**: Real-time chat via `app/service/Events.php`
- **Session Management**: `app/model/CustomerSession.php` 
- **Message Handling**: `app/model/CustomerMessage.php`
- **Status Management**: Automated online/offline tracking
- **Multi-media Support**: Images, videos, emoji, file uploads

#### Queue System
- **ThinkPHP Queue**: Background job processing
- **AMQP Support**: RabbitMQ integration via php-amqplib
- **Swoole Integration**: High-performance async processing
- **Crontab**: Scheduled task management

#### Payment Integration
- **WeChat Pay**: Comprehensive WeChat payment integration
- **Alipay**: Alipay payment processing
- **Multiple Channels**: Support for various payment providers

#### Third-party Integrations
- **WeChat Ecosystem**: Official accounts, mini-programs, work WeChat
- **SMS Services**: Multiple SMS providers (Tencent, Aliyun)
- **Email**: SMTP email services
- **AI Services**: Baidu AI, OpenAI integration
- **Express/Logistics**: Multiple courier service APIs

### Database Architecture
- **GUID-based**: Uses UUIDs for distributed-friendly primary keys
- **Multi-tenant**: Business ID (bid) separation
- **Soft Deletes**: Most models support soft deletion
- **Timestamp Precision**: Millisecond-level timestamps (datetime(3))
- **Rich Metadata**: Extensive use of JSON extra fields for flexibility

### Frontend Architecture
- **Vue.js 3**: Modern reactive frontend
- **LayUI**: UI component library
- **WebSocket Client**: Real-time communication
- **Modern Features**: ES6+, modular JavaScript

## Development Guidelines

### Database Conventions
- Use `guid` fields for primary keys (UUID format)
- Include `bid` field for multi-tenant separation  
- Add `create_time` and `update_time` with datetime(3) precision
- Use `extra` JSON fields for extensible metadata
- Follow soft delete pattern with appropriate fields

### API Development
- Extend `BaseController` for common functionality
- Use helper functions from `app/common.php`
- Implement proper validation using validate classes
- Return standardized responses via `result()`, `success()`, `error()`
- Log important operations using `wr_log()` function

### WebSocket Development
- Events handled in `app/service/Events.php`
- Use standardized message formats
- Implement proper error handling and reconnection
- Maintain client state and heartbeat

### Queue Usage
```php
// Push job to queue
job()->set_job_name('ClassName@method')->push_job($data, $delay);

// Send SMS via queue
send_sms($content, $mobile, $bid);

// Send email via queue  
send_email($data, $delay);
```

### Common Patterns

#### Model Usage
```php
// Get paginated results with conditions
protected function _list() {
    return $this->_paginate($this->model);
}

// Build query conditions from request params
$map = $this->build_condition_map($model);
```

#### Service Integration
```php
// WeChat service
$wechat = weixin($appid, $component_appid);

// Payment service
$payment = pay($bid);

// Redis cache
$redis = get_redis_instance();

// Tools service
$tools = tools();
```

## Testing

### Manual Testing
- Test files available in `test/` directory
- WebSocket status testing: `test_websocket_status.php`
- Customer service testing: Various HTML test files
- API testing: Use test files for specific features

### Debugging
- Use `wr_log()` for application logging
- Enable debug mode via `is_debug()` check
- WebSocket events logged in detail
- Error notifications via enterprise WeChat

## Deployment Notes

### Environment Setup
- PHP 8.0+ required with extensions: redis, pcntl, sockets, posix
- MySQL 5.7+ with proper timezone configuration
- Redis for caching and sessions
- Proper file permissions for runtime directories

### Production Considerations
- Configure crontab for background tasks
- Set up queue workers for async processing
- Monitor WebSocket server health
- Configure proper logging and error reporting
- Set up automated backups for critical data

### Security
- Input validation on all user data
- SQL injection protection via ORM
- XSS prevention in output
- CSRF protection where needed
- Secure WebSocket connections in production