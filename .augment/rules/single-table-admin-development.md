---
type: "Manual"
---

# 单表后台管理开发规范

## 📋 开发流程

### 🔄 标准开发步骤（严格按顺序执行）

1. **验证器** → `app/validate/admin_api/v1/{TableName}.php`
2. **模型类** → `app/model/{TableName}.php`
3. **控制器** → `app/controller/admin_api/v1/{TableName}.php`
4. **列表页** → `app/view/admin/{table_name}/index.html`
5. **详情页** → `app/view/admin/{table_name}/detail.html`

### ⚡ 分步确认原则

- **每完成一步必须与用户确认**
- **获得确认后才能进行下一步**
- **严格遵循最小开发原则**
- **不添加用户未明确要求的功能**

## 🔧 文件开发规范

### 1. 验证器规范

```php
<?php
declare(strict_types=1);

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class {TableName} extends ValidateBase
{
    protected $rule = [
        'guid|唯一标识'    => ['require'],           // 编辑/删除必需
        'title|标题'      => ['require', 'max:100'], // 根据实际字段调整
        'status|状态'     => ['integer', 'in:0,1'],  // 状态字段
    ];

    protected $scene = [
        'add'  => ['title'],                    // 新增场景：只验证必需字段
        'edit' => ['guid', 'status'],          // 编辑场景：只验证guid和status（用于状态开关）
        'del'  => ['guid'],                    // 删除场景：只验证guid
    ];
}
```

**关键要点：**

- `edit` 场景只验证 `guid` 和 `status`，支持状态开关功能
- 如需完整编辑，可添加 `full_edit` 场景
- 验证规则根据实际表字段调整

### 2. 模型类规范

```php
<?php

namespace app\model;

use think\model\concern\SoftDelete;

class {TableName} extends ModelBase
{
    use SoftDelete;
    
    protected $pk = 'guid';
    protected $deleteTime = 'delete_time';
}
```

**关键要点：**

- 必须继承 `ModelBase`
- 必须使用 `SoftDelete` trait
- 主键设置为 `guid`
- 软删除字段为 `delete_time`
- **不添加关联方法**（除非用户明确要求）

### 3. 控制器规范

```php
<?php

namespace app\controller\admin_api\v1;

use app\model\{TableName} as {TableName}Model;
use Exception;

class {TableName} extends BasicAdminApi
{
    public function index()
    {
        $db = new {TableName}Model();
        $this->model = $db;
        $bid = $this->get_bid();
        
        $map = [
            ['bid', '=', $bid],
        ];
        
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    public function add()
    {
        $params = $this->params;
        $db = new {TableName}Model();
        $db->add($params);
        success('添加成功');
    }

    public function edit()
    {
        $params = $this->params;
        $db = new {TableName}Model();
        $db->edit($params);
        success('编辑成功');
    }

    public function del()
    {
        $params = $this->params;
        $db = new {TableName}Model();
        $db->del($params);
        success('删除成功');
    }
}
```

**关键要点：**

- 只包含4个标准方法：`index/add/edit/del`
- **不包含 `detail()` 方法**
- 使用 `$this->get_bid()` 进行商户隔离
- 依赖自动验证机制，无手动验证代码
- 使用软删除机制

### 4. 列表页规范

```html
{layout name="layui_plus" /}
<div class='layui-fluid'>
  <div class='layui-card'>
    <div class='layui-card-body'>
      <!-- 查询表单 -->
      <form class="layui-form" id="query_form" style="margin-left: 10px">
        <div class="layui-form-item">
          <div class="layui-inline">
            <input type="text" name="title" placeholder="标题" autocomplete="off" class="layui-input"/>
          </div>
          <div class="layui-inline">
            <select name="status">
              <option value="">状态</option>
              <option value="1">启用</option>
              <option value="0">禁用</option>
            </select>
          </div>
          <div class="layui-inline">
            <div class="layui-btn-container">
              <button id="query" class="layui-btn" type="button" function="query">
                <i class="layui-icon layui-icon-search"></i>查询
              </button>
              <button class="layui-btn layui-btn-normal" type="reset">
                <i class="layui-icon layui-icon-delete"></i>重置
              </button>
            </div>
          </div>
        </div>
      </form>

      <!-- 数据表格 -->
      <table toolbar="#toolbarDemo" id="fsDatagrid" lay-filter="fsDatagrid" class="fsDatagrid"
             isLoad="1" url="/{table_name}/index" isPage="1" defaultForm="query_form"
             height="auto" defaultToolbar="filter">
      </table>
      <div class="fsDatagridCols">
        <p checkbox="true" width="60"/>
        <p field="title" title="标题" align="center" width="200">
        <p field="status" title="状态" templet="#statusTpl" align="center" width="100">
        <p field="create_time" title="创建时间" align="center" width="160">
        <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="200"/>
      </div>
    </div>
  </div>
</div>

<!-- 工具栏 -->
<script type="text/html" id="toolbarDemo">
  <div class="layui-inline">
    <button auth="{table_name}/add" class="layui-btn layui-btn-sm" function="top"
            topUrl="/admin/{table_name}/detail" topMode="add"
            topWidth="800px" topHeight="600px" topTitle="新增">
      <i class="layui-icon">&#xe609;</i>新增
    </button>
  </div>
</script>

<!-- 状态开关 -->
<script type="text/html" id="statusTpl">
  <input type="checkbox" name="status" url="/{table_name}/edit" inputs="guid:{{d.guid}}"
         lay-skin="switch" lay-text="启用|禁用" {{ d.status== 1 ? 'checked' : '' }}>
</script>

<!-- 操作按钮 -->
<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="top"
     topUrl="/admin/{table_name}/detail" topMode="readonly"
     topWidth="800px" topHeight="600px" topTitle="查看详情" inputs="guid:">查看</a>
  <a auth='{table_name}/edit' class="layui-btn layui-btn-xs" lay-event="top"
     topUrl="/admin/{table_name}/detail" topMode="edit"
     topWidth="800px" topHeight="600px" topTitle="编辑" inputs="guid:">编辑</a>
  <a auth='{table_name}/del' class="layui-btn layui-btn-danger layui-btn-xs" lay-event="submit"
     url="/{table_name}/del" isConfirm="1"
     confirmMsg="是否确定删除当前记录？" inputs="guid:">删除</a>
</script>
```

### 5. 详情页规范

```html
{layout name="layui_plus" /}
<div class="layui-fluid">
  <div class="layui-card">
    <div class="layui-card-body">
      <form class="layui-form" id="edit_form" loadUrl="">
        <input type="hidden" name="guid"/>

        <div class="layui-form-item">
          <label class="layui-form-label">标题</label>
          <div class="layui-input-block">
            <input type="text" name="title" required="" lay-verType="tips" lay-verify="required"
                   placeholder="请输入标题" autocomplete="off" class="layui-input"/>
          </div>
        </div>

        <div class="layui-form-item">
          <label class="layui-form-label">状态</label>
          <div class="layui-input-block">
            <input type="radio" name="status" value="1" title="启用" checked>
            <input type="radio" name="status" value="0" title="禁用">
          </div>
        </div>

        <hr/>
        <div class="layui-form-item" style="text-align: center;">
          <button class="layui-btn fsAdd" lay-submit="" lay-filter="save" url="/{table_name}/add">保存</button>
          <button class="layui-btn fsEdit" lay-submit="" lay-filter="edit" url="/{table_name}/edit">保存</button>
          <button type="button" class="layui-btn layui-btn-primary" function="close">关闭</button>
        </div>
      </form>
    </div>
  </div>
</div>
```

## 🎯 关键规范要点

### 状态开关功能

- 使用 `lay-skin="switch"` 实现开关样式
- `edit` 验证场景只验证 `guid` 和 `status`
- 自动调用 `edit` 接口更新状态

### 优惠券字段显示

- 列表页使用：`<p field="coupon_guid" title="优惠券" dict="coupon" align="center" width="150">`
- 详情页使用：`<select name="coupon_guid" class="fsSelect" dict="coupon" addNull="1">`

### 软删除机制

- 删除操作更新 `delete_time` 字段，不物理删除
- 查询时自动过滤已删除记录

### 权限控制

- 使用 `auth="{table_name}/{action}"` 控制按钮显示
- 商户数据隔离通过 `$this->get_bid()` 实现

## ⚠️ 注意事项

1. **严格按步骤执行**：每步完成后必须确认
2. **最小开发原则**：不添加未要求的功能
3. **验证场景设计**：`edit` 场景支持状态开关
4. **命名规范**：文件名、类名、路径严格按规范
5. **不添加关联**：除非明确要求，否则不添加模型关联方法

## 📝 开发检查清单

- [ ] 验证器：场景配置正确，支持状态开关
- [ ] 模型类：继承ModelBase，使用SoftDelete
- [ ] 控制器：4个标准方法，无额外业务逻辑
- [ ] 列表页：状态开关，权限控制，优惠券显示
- [ ] 详情页：fsAdd/fsEdit按钮，表单验证
- [ ] 测试：状态开关功能正常工作
