---
type: "Always"
---

# 开发协作规范

## 📖 文档使用指南

- **develop.md**: 开发行为和流程规范（何时做什么）
- **augment-rule.md**: 技术实现规范（怎么做）
- **读取顺序**: 先读 develop.md 确定行为，再读 augment-rule.md 获取技术规范

## 1. 需求理解和分解

- 先理解需求背景和目标
- 分解具体的功能点
- 确认技术实现方案
- 数据库结构在 disk/sql 目录下文件名就是表名

## 2. 代码修改流程

### 2.1 修改前准备

- 修改前先说明思路和计划
- 获得确认后再动手写代码
- 重要修改要说明影响范围
- 涉及数据库结构修改请先确认
- 大需求需要先生成 TodoList 分步执行，每完成一个任务必须停止并等待用户确认后才能进行下一个任务

### 2.2 生成 TodoList 的情况

遇到以下情况时，必须先生成详细的 TodoList，获得确认后再开始执行：

1. **新模块开发**：需要创建完整的 CRUD 功能模块（包括模型、控制器、验证器、视图等多个文件）
    - **检查要求**：对照规范确认所有必需文件都已列入 TodoList
2. **多文件修改**：需要同时修改或创建 3 个以上的文件
3. **数据库相关**：涉及数据库表结构修改、新增表、或复杂的数据迁移
4. **架构调整**：涉及项目架构、目录结构、或核心功能的修改
5. **功能重构**：需要重写或大幅修改现有功能
6. **集成开发**：需要集成第三方服务、API 或组件
7. **权限系统**：涉及权限、角色、菜单等安全相关功能的修改

### 2.3 TodoList 格式要求

TodoList 必须包含以下信息：

- 任务编号和优先级
- 具体的文件路径和操作类型（新增/修改/删除）
- 每个步骤的详细说明
- 预估的影响范围
- 依赖关系说明
- 验证和测试要点

### 2.4 最小化执行原则

1. **单任务执行**：每次只能执行 TodoList 中的一个任务
2. **最小完成单位**：一个任务 = 一个文件的完整操作（新增/修改/删除）
3. **强制停止**：完成一个任务后必须立即停止
4. **明确报告**：明确告知用户"任务 X 已完成，请确认后继续下一个任务"
5. **等待确认**：必须等待用户明确确认才能执行下一个任务

### 2.5 用户确认机制

- **明确确认**：用户说"继续""下一个""OK""好的"等
- **修改要求**：用户提出修改意见，AI 需先修改当前任务再等待确认
- **跳过任务**：用户说"跳过"，AI 可跳过当前任务执行下一个
- **无效确认**：用户沉默、提出新需求或询问其他问题
- **严禁行为**：未经确认自动执行下一个任务

### 2.6 新建文件的参考原则

- **规则文档优先**：规则足够时直接开发，不参考现有文件
- **规则不足时询问**：缺少信息时询问用户是否需要参考特定文件

### 2.7 任务状态报告格式

- 明确说明任务完成情况
- 报告文件路径和操作类型
- 询问用户是否继续下一个任务
- 等待用户确认后再执行

### 2.8 任务依赖关系处理

- 执行任务前检查依赖关系
- 依赖任务未完成时先提醒用户
- 任务执行失败时立即停止并报告错误

## 3. 代码质量要求

- 遵循项目编码规范
- 保持代码可读性
- 添加必要的注释和日志
- 请直接给出最佳平衡版本，不要逐步优化
- 尽量代码行数要简约 可维护

### 3.1 最小化实现原则

1. **只实现明确要求的功能**：

    - 用户明确提到的功能才能实现
    - 用户说"需要 CRUD" → 只实现 CRUD
    - 用户说"需要验证" → 只验证明确提到的字段
    - 用户说"需要特殊方法" → 询问具体需求后实现

2. **标准模板的最小化使用**：

    - 模型：只继承 ModelBase
    - 控制器：只实现标准 CRUD
    - 验证器：只验证明确提到的字段

3. **极简控制器模式**：
    - 接收参数 → 调用模型 → 返回响应

## 4. 文档和总结

- 及时更新相关文档
- 记录重要的技术决策
- 总结开发过程中的问题和解决方案
- 如果觉得有必要写文档的时候先和我确认是否需要再写

## 5. 关于测试

- 请勿自行创建测试文件去测试
- 请勿自行编写命令去做整体测试

## ⚡ 执行前检查清单

□ 需求是否明确？
□ 是否需要生成 TodoList？
□ 是否参考了同级目录文件？
□ 是否只实现明确要求的功能？
