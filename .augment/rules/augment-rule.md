---
type: "Auto"
description: "Example description"
---

# 一卡易项目技术规范 (AI 优化版)

## 📖 文档使用指南

- **develop.md**: 开发行为和流程规范（何时做什么）
- **augment-rule.md**: 技术实现规范（怎么做）
- **读取顺序**: 先读 develop.md 确定行为，再读 augment-rule.md 获取技术规范

## 🤖 AI 核心规则速览（必读）

### 🚨 架构规则（第一优先级）

- **多商户隔离**：所有查询必带 `['bid', '=', $this->get_bid()]`
- **API 规范**：统一 POST，路径 `/{模块}/v{版本}/{控制器}/{方法}`
- **CRUD 命名**：`index/add/edit/del`（核心方法），`detail`（可选，仅在用户明确需要时实现）

### 🔄 自动机制（第二优先级）

- **验证**：创建验证器，无需手动验证代码
- **查询**：使用 `$this->_list()` 自动处理分页搜索排序
- **异常**：默认自动处理，事务时手动捕获

### 📋 必需字段（第三优先级）

- **bid**：必须字段
- **delete_time**：软删除（推荐）
- **create_time/update_time**：自动维护，无需手动设置

## 🔄 AI 决策树

### 创建新模块时

1. **控制器** → 继承 BasicAdminApi → 实现标准 CRUD（index/add/edit/del）
2. **验证器** → 路径与控制器对应 → 定义验证场景
3. **模型** → 继承 ModelBase → 使用标准方法（add/edit/del）
4. **视图** → admin 目录下 → index.html（必需），detail.html（必需）

### 数据库操作时

- **查询** → 排除 delete_time
- **新增** → 自动添加 guid、bid、时间字段
- **删除** → 设置 delete_time（软删除）
- **更新** → update_time 自动维护

### API 接口开发时

- **请求方式** → POST
- **路径格式** → `/{模块}/v{版本}/{控制器}/{方法}`
- **验证方式** → 自动验证（创建对应验证器）
- **响应格式** → `result()/error()/success()`

## 🎯 关键词触发规则

看到这些关键词时，自动应用对应规则：

- **"新模块开发"** → 完整 CRUD + 验证器 + 视图
- **"API 接口"** → POST + 自动验证
- **"数据库查询"** → 软删除检查
- **"列表页面"** → `_list()`自动机制
- **"参数验证"** → 创建验证器文件
- **"异常处理"** → 优先自动机制

## ⚖️ 规则优先级

1. **安全规则** > 性能规则 > 代码风格
2. **自动机制** > 手动实现
3. **项目约定** > ThinkPHP 默认约定

### 🚫 例外情况

- **测试代码**：可不遵循 bid 隔离
- **队列任务**：可使用物理删除
- **系统初始化**：可跳过自动验证

## 📝 标准代码模板

**📋 使用说明**：以下模板用于手动创建文件，代码生成器模板见第 11.6.4 节

### 🏗️ 架构分层原则

**控制器职责（极简模式）**：

- 接收参数：`$params = $this->params;`
- 调用模型：`$db->add($params);`
- 返回响应：`success('操作成功');`

**模型职责（核心逻辑）**：

- 业务逻辑处理
- 数据验证和保存
- bid、guid 等字段自动设置
- 错误处理

**数据库操作规范**：使用 ModelBase 提供的标准方法

### 控制器模板

```php
<?php
declare(strict_types=1);

namespace app\controller\admin_api\v1;

use app\model\{Name} as {Name}Model;
use Exception;

class {Name} extends BasicAdminApi
{
    /**
     * 列表查询
     */
    public function index()
    {
        $db = new {Name}Model();
        $map = [['bid', '=', $this->get_bid()]];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 新增数据
     */
    public function add()
    {
        $params = $this->params;
        $db = new {Name}Model();
        $db->add($params);
        success('新增成功');
    }

    /**
     * 编辑数据
     */
    public function edit()
    {
        $params = $this->params;
        $db = new {Name}Model();
        $db->edit($params);
        success('编辑成功');
    }

    /**
     * 删除数据
     */
    public function del()
    {
        $params = $this->params;
        $db = new {Name}Model();
        $db->del($params);
        success('删除成功');
    }
}
```

### 验证器模板

```php
<?php
declare(strict_types=1);

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class {Name} extends ValidateBase
{
    protected $rule = [
        'id|ID'         => ['require', 'integer'],      // 主键字段必须require
        'name|名称'     => ['require', 'max:255'],      // 业务必需字段
        'status|状态'   => ['integer', 'in:0,1'],       // 可选字段
        'sort_order|排序' => ['integer', 'egt:0'],       // 可选字段
    ];

    protected $message = [
        'id.require'        => '请选择要操作的记录',
        'name.require'      => '请输入名称',
        'name.max'          => '名称长度不能超过255个字符',
        'status.in'         => '状态值不正确，只能是0或1',
        'sort_order.egt'    => '排序不能小于0',
    ];

    protected $scene = [
        'add'   => ['name', 'status', 'sort_order'],
        'edit'  => ['id', 'name', 'status', 'sort_order'],
        'del'   => ['id'],
    ];
}
```

### 模型模板

```php
<?php
declare(strict_types=1);

namespace app\model;

class {Name} extends ModelBase
{
    protected $pk = 'id';

    protected $type = [
        // 根据需要定义字段类型转换
        // 'json' => 'json',
    ];

    protected $jsonAssoc = true;

    // ModelBase已提供标准的add/edit/del方法
    // 大部分情况下无需重写，直接使用即可
    // 只有特殊业务需求时才重写相应方法
}
```

## 🎨 前端组件规范

### 图片上传组件

```html

<div class="layui-form-item layui-form-text">
  <label class="layui-form-label">图片</label>
  <div class="layui-input-inline">
    <input type="text" id="filePath" name="pic" autocomplete="off" disabled="disabled"
           class="layui-input upload-input"/>
    <button type="button" class="layui-btn layui-btn-normal" function="upload" fileElem="#filePath" fileAccept="file"
            fileExts="" fileSize="3072" inputs="type:test">
      <i class="layui-icon layui-icon-upload-drag"></i>
      上传
    </button>
    <span class="info">建议尺寸 800px*800px</span>
  </div>
</div>
```

### 前端组件使用规范

- **图片上传**：使用`function="upload"`属性，不使用原生 Layui 上传
- **文件路径显示**：使用`disabled="disabled"`的文本输入框
- **文件限制**：使用`fileSize`限制大小，`fileExts`限制类型
- **目标元素**：使用`fileElem`指定目标输入框

### 视图页面设计原则

- **字段提示**：只提供必要的简洁提示，不添加详细说明
- **操作说明**：不添加冗余的操作说明卡片
- **界面简洁**：保持界面简洁，避免过多的解释性文字

### 代码编写原则

- **优先参考现有代码**：用户打开或提及的现有文件是最佳参考
- **学习项目规范**：从现有代码中学习项目的实际规范和风格
- **保持一致性**：新代码必须与现有代码风格保持一致
- **基于实际规范**：根据项目实际规范编写代码，参考现有文件

## ⚡ 技术实现快速检查清单

□ 是否使用了项目标准组件？
□ 模型中是否使用 `$this->get_bid_from_request()`？
□ 控制器中是否使用 `$this->get_bid()`？
□ 数据库更新是否使用 `::update($update_data, $map)`？
□ 验证器主键字段是否添加了 require 验证？

## 🧪 AI 理解验证

### 自测问题：

1. 创建新 API 接口时，应该使用什么 HTTP 方法和路径格式？
2. 数据库查询时，哪个字段是必须包含的？
3. 什么情况下需要手动捕获异常？
4. CRUD 方法的标准命名是什么？
5. 如何实现数据库更新操作？
6. 如何实现数据库删除操作？
7. 模型实例应该如何命名？
8. 主键字段在验证器中需要添加什么验证？
9. 验证场景应该与什么对应？
10. 图片上传组件应该使用什么属性？
11. 文件路径输入框应该如何设置？
12. 编写新代码时应该优先参考什么？
13. 如何确保代码风格一致性？

### 标准答案：

1. POST 方法，路径格式：`/{模块}/v{版本}/{控制器}/{方法}`
2. 数据库事务、批量操作需要继续执行时
3. `$db_{name}::update($update_data, $map)`
4. `$db_{name}::destroy($map)`
5. `$db_{name} = new {Name}Model()`
6. 必须添加`require`验证（如`['require', 'integer']`）
7. 验证场景必须与控制器方法对应
8. 使用`function="upload"`属性，不使用原生 Layui 上传
9. 使用`disabled="disabled"`的文本输入框显示文件路径
10. 用户打开或提及的现有文件，学习其代码规范和风格
11. 新代码必须与现有代码风格保持一致，避免凭想象编写

---

## 📚 详细规范参考

## 1. 快速参考指南

### 1.1 核心开发流程

1. 理解需求 → 生成 TodoList（如需要）
2. 参考同级目录文件 → 学习现有规范
3. 最小化实现 → 只做用户要求的功能
4. 单任务执行 → 完成一个确认一个

### 1.2 必须遵循的规则

- **极简控制器**：只接收参数、调用模型、返回响应

## 2. 项目架构概述

### 2.1 技术栈

- **框架**: ThinkPHP 8.x
- **数据库**: MySQL 5.7+
- **缓存**: Redis
- **队列**: Think Queue
- **日志**: 自定义日志驱动

### 2.2 目录结构规范

```
app/
├── AppService.php              # 应用服务类
├── BaseController.php          # 控制器基类
├── ExceptionHandle.php         # 全局异常处理
├── Request.php                 # 请求类
├── common.php                  # 公共函数库
├── helpers.php                 # 助手函数
├── event.php                   # 事件定义
├── middleware.php              # 中间件定义
├── provider.php                # 服务提供者
├── service.php                 # 服务定义
├── controller/                 # 控制器目录
│   ├── Error.php              # 错误控制器
│   ├── admin_api/             # 后台管理API
│   │   └── v1/               # API版本1
│   ├── member_api/            # 会员端API
│   │   └── v1/               # API版本1
│   ├── api/                   # 通用API
│   │   └── v1/               # API版本1
│   ├── gateway/               # 网关接口
│   ├── index/                 # 前台页面控制器
│   ├── mall/                  # 商城控制器
│   ├── member/                # 会员控制器
│   ├── openapi/               # 开放API
│   ├── paimai/                # 拍卖控制器
│   ├── push/                  # 推送控制器
│   └── test/                  # 测试控制器
├── model/                     # 模型目录
│   ├── ModelBase.php          # 模型基类
│   ├── Member.php             # 会员模型
│   ├── User.php               # 用户模型
│   ├── Business.php           # 商家模型
│   ├── Goods.php              # 商品模型
│   ├── GoodsOrder.php         # 订单模型
│   ├── Coupon.php             # 优惠券模型
│   ├── PayOrder.php           # 支付订单模型
│   └── ...                    # 其他业务模型
├── middleware/                # 中间件目录
│   ├── BasicMiddleware.php    # 中间件基类
│   ├── AdminApi.php           # 后台API中间件
│   ├── MemberApi.php          # 会员API中间件
│   ├── AllowCrossDomain.php   # 跨域中间件
│   ├── Validate.php           # 验证中间件
│   ├── BusinessStatusVerify.php # 商家状态验证
│   └── ...                    # 其他中间件
├── common/                    # 公共类库
│   ├── controller/            # 公共控制器
│   ├── service/               # 服务类
│   │   ├── TokenService.php   # Token服务
│   │   ├── SmsService.php     # 短信服务
│   │   ├── WechatService.php  # 微信服务
│   │   ├── PayService.php     # 支付服务
│   │   └── ...                # 其他服务
│   ├── tools/                 # 工具类
│   │   ├── Excel.php          # Excel工具
│   │   ├── Image.php          # 图片工具
│   │   ├── Visitor.php        # 访问者工具
│   │   └── ...                # 其他工具
│   ├── validate/              # 验证器基类
│   │   └── ValidateBase.php   # 验证器基类
│   ├── exceptions/            # 自定义异常
│   │   └── NotNotifyException.php # 不通知异常
│   └── view/                  # 公共视图
├── queue/                     # 队列任务
│   └── controller/            # 队列控制器
│       ├── BasicQueue.php     # 队列基类
│       ├── Test.php           # 测试队列
│       ├── GoodsOrder.php     # 订单队列
│       ├── Pay.php            # 支付队列
│       ├── Notify.php         # 通知队列
│       └── ...                # 其他队列任务
├── command/                   # 命令行工具
│   ├── Crontab.php            # 定时任务
│   ├── DatabaseBackup.php    # 数据库备份
│   ├── QueueMonitor.php       # 队列监控
│   ├── SwoolePool.php         # Swoole连接池
│   └── ...                    # 其他命令
├── validate/                  # 验证器
│   ├── admin_api/             # 后台API验证器
│   │   └── v1/               # 版本1验证器
│   ├── member_api/            # 会员API验证器
│   │   └── v1/               # 版本1验证器
│   ├── api/                   # 通用API验证器
│   ├── gateway/               # 网关验证器
│   ├── index/                 # 前台验证器
│   ├── mall/                  # 商城验证器
│   ├── member/                # 会员页面验证器
│   ├── openapi/               # 开放API验证器
│   └── test/                  # 测试验证器
├── view/                      # 视图模板
│   ├── admin/                 # 后台管理视图
│   ├── index/                 # 前台页面视图
│   ├── member/                # 会员页面视图
│   ├── paimai/                # 拍卖页面视图
│   ├── push/                  # 推送页面视图
│   └── test/                  # 测试页面视图
├── event/                     # 事件类
│   ├── HttpEnd.php            # HTTP结束事件
│   └── UserLogin.php          # 用户登录事件
├── listener/                  # 监听器
│   ├── AppInit.php            # 应用初始化监听器
│   └── UserLogin.php          # 用户登录监听器
├── service/                   # 服务类
│   ├── Events.php             # 事件服务
│   └── WorkerServer.php       # Worker服务
└── subscribe/                 # 订阅类
    └── User.php               # 用户订阅
```

## 3. 模块定义

### 2.1 控制器模块

项目控制器按照功能模块划分为多个目录：

- **admin_api**: 后台管理 API 接口

    - 用于后台管理系统的 API 接口
    - 包含用户管理、商家管理、订单管理等功能
    - 需要管理员权限验证

- **member_api**: 会员端 API 接口

    - 用于会员端应用的 API 接口
    - 包含会员信息、积分管理、订单查询等功能
    - 需要会员身份验证

- **api**: 通用 API 接口

    - 提供通用的 API 服务
    - 包含公共数据查询、工具接口等

- **gateway**: 网关接口

    - 处理第三方回调和推送
    - 如微信事件回调、支付回调等

- **index**: 前台页面控制器

    - 处理前台页面展示
    - 包含首页、商品展示等页面

- **mall**: 商城相关控制器

    - 处理商城业务逻辑
    - 包含商品管理、购物车等功能

- **member**: 会员相关控制器

    - 处理会员页面展示
    - 包含会员中心、个人信息等页面

- **openapi**: 开放 API 接口

    - 提供给第三方调用的开放接口
    - 需要 API 密钥验证

- **paimai**: 拍卖相关控制器

    - 处理拍卖业务逻辑
    - 包含拍卖商品、竞价等功能

- **push**: 推送相关控制器

    - 处理消息推送业务
    - 包含短信、邮件、APP 推送等

- **test**: 测试控制器
    - 用于开发测试的控制器
    - 仅在开发环境使用

### 2.2 控制器继承关系

```php
// 基础控制器
BaseController

// API控制器基类
├── BasicAdminApi (admin_api模块基类)
├── BasicMemberApi (member_api模块基类)
├── BasicApi (api模块基类)
└── BasicOpenApi (openapi模块基类)

// 具体业务控制器
├── admin_api/v1/Member extends BasicAdminApi
├── member_api/v1/Member extends BasicMemberApi
└── api/v1/Common extends BasicApi
```

### 2.3 模型模块

模型类统一放在 `app/model` 目录下，所有模型继承自 `ModelBase`：

- **核心业务模型**: Member、User、Business、Goods 等
- **订单相关模型**: GoodsOrder、PayOrder、CouponOrder 等
- **营销相关模型**: Coupon、Banner、Activity 等
- **系统相关模型**: SystemConfig、Log、Rule 等
- **第三方相关模型**: WechatConfig、AlipayConfig 等

### 2.4 中间件模块

中间件类统一放在 `app/middleware` 目录下：

- **权限验证中间件**: AdminApi、MemberApi、BusinessStatusVerify 等
- **参数处理中间件**: Validate、ParamsFilter 等
- **跨域处理中间件**: AllowCrossDomain 等
- **日志记录中间件**: AdminApiLog、MemberApiLog 等
- **缓存处理中间件**: RequestCache 等

### 2.5 服务模块

服务类放在 `app/common/service` 目录下，封装业务逻辑：

- **认证服务**: TokenService、AuthService 等
- **通信服务**: SmsService、EmailService、WechatService 等
- **支付服务**: PayService、AlipayService、WechatPayService 等
- **文件服务**: UploadService、StorageService 等
- **第三方服务**: ErpService、YlhService 等

### 2.6 工具模块

工具类放在 `app/common/tools` 目录下，提供通用工具方法：

- **数据处理工具**: Excel、Image、Visitor 等
- **网络工具**: Curl、Http 等
- **加密工具**: Encrypt、Hash 等
- **时间工具**: DateTime、Calendar 等

### 2.7 队列模块

队列任务放在 `app/queue/controller` 目录下：

- **订单处理队列**: GoodsOrder 队列
- **支付处理队列**: Pay 队列
- **通知队列**: Notify 队列
- **数据同步队列**: Sync 队列
- **定时任务队列**: Crontab 队列

## 4. 命名规范

### 3.1 类命名规范

- **控制器**: 大驼峰命名法，继承自 `BaseController`
  ```php
  class MemberController extends BaseController
  ```
- **模型**: 大驼峰命名法，继承自 `ModelBase`
  ```php
  class Member extends ModelBase
  ```
- **中间件**: 大驼峰命名法，继承自 `BasicMiddleware`
  ```php
  class AdminApi extends BasicMiddleware
  ```
- **服务类**: 大驼峰命名法，以 `Service` 结尾
  ```php
  class TokenService
  ```

### 3.2 方法命名规范

- **控制器方法**: 蛇形命名法
  ```php
  public function get_member_info()
  public function update_member_status()
  ```
- **模型方法**: 蛇形命名法
  ```php
  public function get_member_info()
  public function verify_member_status()
  ```
- **服务类方法**: 蛇形命名法
  ```php
  public static function encode_token($data)
  public static function verify_token($token)
  ```

### 3.3 变量命名规范

- **普通变量**: 下划线命名法
  ```php
  $member_info = [];
  $user_guid = '';
  ```
- **布尔变量**: 使用 `is_`、`has_`、`can_` 前缀
  ```php
  $is_valid = true;
  $has_permission = false;
  ```
- **数组变量**: 使用复数形式
  ```php
  $members = [];
  $users = [];
  ```

### 3.4 常量命名规范

- **全局常量**: 全大写，下划线分隔
  ```php
  const DEFAULT_PAGE_SIZE = 20;
  const MAX_RETRY_TIMES = 3;
  ```

## 5. 控制器规范

### 4.1 基础控制器结构

```php
<?php
declare(strict_types=1);

namespace app\controller\admin_api\v1;

use Exception;

class Member extends BasicAdminApi
{
    /**
     * 获取会员列表
     * @return void
     * @throws Exception
     */
    public function index()
    {
        $db = new MemberModel();
        $this->model = $db;
        $bid = $this->get_bid();
        $map = [['bid', '=', $bid]];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}
```

### 4.2 API 响应规范

- **成功响应**: 使用 `result()` 函数
  ```php
  result($data, 'success');
  ```
- **错误响应**: 使用 `error()` 函数
  ```php
  error('错误信息', -1);
  ```
- **操作成功**: 使用 `success()` 函数
  ```php
  success('操作成功');
  ```

### 4.3 标准 CRUD 方法规范

#### 4.3.1 CRUD 方法命名规范

**重要规范：项目中的 CRUD 方法必须使用以下标准命名，确保代码的一致性。**

```php
// 📋 标准CRUD方法命名（必须严格遵循）
public function index()     // 列表查询
public function detail()    // 详情页面（新增和编辑共用）
public function add()       // 新增数据
public function edit()      // 编辑数据
public function del()       // 删除数据
```

#### 4.3.2 CRUD 方法实现规范

```php
// ✅ 标准的控制器CRUD实现
class Test extends BasicAdminApi
{
    /**
     * 列表查询
     */
    public function index()
    {
        $db = new TestModel();
        $map = [['bid', '=', $this->get_bid()]];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 详情页面（新增和编辑共用）
     */
    public function detail()
    {
        $guid = $this->params['guid'] ?? '';
        if (!empty($guid)) {
            // 编辑模式：返回数据
            $db = new TestModel();
            $map = [
                ['bid', '=', $this->get_bid()],
                ['guid', '=', $guid]
            ];
            $info = $db->where($map)->findOrEmpty();
            if ($info->isEmpty()) {
                error('数据不存在');
            }
            result($info->toArray());
        } else {
            // 新增模式：返回空数据或默认值
            result([]);
        }
    }

    /**
     * 新增数据
     */
    public function add()
    {
        $params = $this->params;
        $params['guid'] = create_guid();
        $params['bid'] = $this->get_bid();

        $db = new TestModel();
        $result = $db->save($params);

        if ($result) {
            wr_log('数据新增成功：' . ($params['name'] ?? $params['guid']), false, $this->get_bid());
            result($result, '新增成功');
        } else {
            error('新增失败');
        }
    }

    /**
     * 编辑数据
     */
    public function edit()
    {
        $params = $this->params;
        $guid = $params['guid'] ?? '';

        if (empty($guid)) {
            error('缺少必要参数');
        }

        $map = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid]
        ];

        $db = new TestModel();
        $result = $db->where($map)->save($params);

        if ($result !== false) {
            wr_log('数据编辑成功：GUID=' . $guid, false, $this->get_bid());
            result($result, '编辑成功');
        } else {
            error('编辑失败');
        }
    }

    /**
     * 删除数据
     */
    public function del()
    {
        $guid = $this->params['guid'] ?? '';

        if (empty($guid)) {
            error('缺少必要参数');
        }

        $map = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid]
        ];

        $db = new TestModel();

        // 软删除（推荐）
        $result = $db->where($map)->save(['delete_time' => format_timestamp()]);

        // 或者物理删除（根据业务需求）
        // $result = $db->where($map)->delete();

        if ($result) {
            wr_log('数据删除成功：GUID=' . $guid, false, $this->get_bid());
            success('删除成功');
        } else {
            error('删除失败');
        }
    }
}
```

### 4.4 参数获取规范

```php
// 获取所有参数
$params = $this->params;

// 获取特定参数
$member_guid = $params['guid'] ?? null;

// 获取业务ID
$bid = $this->get_bid();

// 获取会员GUID
$member_guid = $this->get_member_guid();
```

#### 4.4.1 CRUD 方法最佳实践

```php
// ✅ 推荐的CRUD方法实现模式

class Member extends BasicAdminApi
{
    /**
     * 列表查询 - 标准实现
     */
    public function index()
    {
        $db = new MemberModel();
        $map = [
            ['bid', '=', $this->get_bid()],
            ['delete_time', 'NULL', NULL]  // 排除已删除数据
        ];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }

    /**
     * 详情页面 - 支持新增和编辑
     */
    public function detail()
    {
        $guid = $this->params['guid'] ?? '';

        if (!empty($guid)) {
            // 编辑模式
            $db = new MemberModel();
            $map = [
                ['bid', '=', $this->get_bid()],
                ['guid', '=', $guid],
                ['delete_time', 'NULL', NULL]
            ];
            $info = $db->where($map)->findOrEmpty();

            if ($info->isEmpty()) {
                error('数据不存在或已被删除');
            }

            result($info->toArray());
        } else {
            // 新增模式 - 返回默认值
            $default_data = [
                'status' => 1,
                'sort_order' => 0,
                // 其他默认值...
            ];
            result($default_data);
        }
    }

    /**
     * 新增数据 - 完整实现
     */
    public function add()
    {
        $params = $this->params;

        // 添加必要字段
        $params['guid'] = create_guid();
        $params['bid'] = $this->get_bid();
        $params['create_user_id'] = $this->get_user_id_from_request();

        $db = new MemberModel();
        $result = $db->save($params);

        if ($result) {
            // 记录操作日志
            wr_log('会员新增成功：' . ($params['name'] ?? $params['guid']), false, $this->get_bid());

            // 后续处理（如果需要）
            $this->after_add($params, $result);

            result($result, '新增成功');
        } else {
            error($db->getError() ?: '新增失败');
        }
    }

    /**
     * 编辑数据 - 完整实现
     */
    public function edit()
    {
        $params = $this->params;
        $guid = $params['guid'] ?? '';

        if (empty($guid)) {
            error('缺少必要参数guid');
        }

        // 添加更新信息
        $params['update_user_id'] = $this->get_user_id_from_request();

        $map = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
            ['delete_time', 'NULL', NULL]
        ];

        $db = new MemberModel();

        // 检查数据是否存在
        $exists = $db->where($map)->findOrEmpty();
        if ($exists->isEmpty()) {
            error('数据不存在或已被删除');
        }

        $result = $db->where($map)->save($params);

        if ($result !== false) {
            // 记录操作日志
            wr_log('会员编辑成功：GUID=' . $guid, false, $this->get_bid());

            // 后续处理（如果需要）
            $this->after_edit($params, $result);

            result($result, '编辑成功');
        } else {
            error($db->getError() ?: '编辑失败');
        }
    }

    /**
     * 删除数据 - 软删除实现
     */
    public function del()
    {
        $guid = $this->params['guid'] ?? '';

        if (empty($guid)) {
            error('缺少必要参数guid');
        }

        $map = [
            ['bid', '=', $this->get_bid()],
            ['guid', '=', $guid],
            ['delete_time', 'NULL', NULL]
        ];

        $db = new MemberModel();

        // 检查数据是否存在
        $exists = $db->where($map)->findOrEmpty();
        if ($exists->isEmpty()) {
            error('数据不存在或已被删除');
        }

        // 软删除
        $delete_data = [
            'delete_time' => format_timestamp(),
            'delete_user_id' => $this->get_user_id_from_request()
        ];

        $result = $db->where($map)->save($delete_data);

        if ($result) {
            // 记录操作日志
            wr_log('会员删除成功：GUID=' . $guid, false, $this->get_bid());

            // 后续处理（如果需要）
            $this->after_delete($guid, $exists->toArray());

            success('删除成功');
        } else {
            error($db->getError() ?: '删除失败');
        }
    }

    /**
     * 新增后的处理
     */
    private function after_add($params, $result)
    {
        // 清除相关缓存
        // 发送通知
        // 记录统计信息等
    }

    /**
     * 编辑后的处理
     */
    private function after_edit($params, $result)
    {
        // 清除相关缓存
        // 发送通知
        // 记录统计信息等
    }

    /**
     * 删除后的处理
     */
    private function after_delete($guid, $data)
    {
        // 清除相关缓存
        // 删除关联数据
        // 记录统计信息等
    }
}
```

#### 4.4.2 CRUD 方法注意事项

```php
// ✅ 必须遵循的规范

// 1. 所有查询都必须带bid条件
$map = [['bid', '=', $this->get_bid()]];

// 2. 软删除查询要排除已删除数据
$map[] = ['delete_time', 'NULL', NULL];

// 3. 新增时必须添加guid和bid
$params['guid'] = create_guid();
$params['bid'] = $this->get_bid();

// 4. 记录操作日志
wr_log('操作描述', false, $this->get_bid());

// 5. 错误处理要完善
if (!$result) {
    error($db->getError() ?: '操作失败');
}

// 6. 参数验证通过验证器自动处理
// 无需在控制器中手动验证

// 7. 返回数据使用统一方法
result($data, '成功信息');
error('错误信息');
success('成功信息');
```

## 6. 模型规范

### 5.1 模型基础结构

```php
<?php
declare(strict_types=1);

namespace app\model;

class Member extends ModelBase
{
    protected $table = 'member';
    protected $pk = 'guid';

    /**
     * 获取会员信息
     * @param array $map 查询条件
     * @return array
     */
    public function get_member_info(array $map): array
    {
        return $this->where($map)->find()->toArray();
    }
}
```

### 5.2 数据库操作规范

- **查询单条记录**:
  ```php
  $member = $this->where($map)->find();
  ```
- **查询多条记录**:
  ```php
  $members = $this->where($map)->select();
  ```
- **分页查询**:
  ```php
  $result = $this->where($map)->paginate();
  ```
- **保存数据**:
  ```php
  $this->save($data);
  ```
- **更新数据**:
  ```php
  $this->where($map)->update($data);
  ```

## 7. 中间件规范

### 6.1 中间件结构

```php
<?php
declare(strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;

class AdminApi extends BasicMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // 前置处理逻辑

        return $next($request);
    }
}
```

### 6.2 中间件配置

在 `config/middleware.php` 中配置中间件组：

```php
'admin_api/v1' => [
    app\middleware\AllowCrossDomain::class,
    app\middleware\AdminApi::class,
    app\middleware\Validate::class,
],
```

## 8. 验证器规范

### 8.1 自动验证机制

**重要说明：一卡易项目具有自动验证机制，业务逻辑中无需手动编写验证代码，通过验证中间件自动实现。**

#### 7.1.1 自动验证流程

```
请求 → Validate中间件 → 自动匹配验证器 → 自动验证场景 → 验证失败抛出异常 → 验证成功继续执行
```

#### 7.1.2 验证器自动映射规则

验证器文件路径与控制器路径自动映射：

```
控制器路径: app/controller/admin_api/v1/Member.php
验证器路径: app/validate/admin_api/v1/Member.php

控制器路径: app/controller/member_api/v1/Register.php
验证器路径: app/validate/member_api/v1/Register.php

控制器路径: app/controller/gateway/PayNotify.php
验证器路径: app/validate/gateway/PayNotify.php
```

#### 7.1.3 验证场景自动匹配

验证场景与控制器方法名自动匹配：

```php
// 控制器方法: Member::add()
// 自动匹配验证场景: 'add'

// 控制器方法: Member::edit()
// 自动匹配验证场景: 'edit'

// 控制器方法: Register::submit()
// 自动匹配验证场景: 'submit'
```

### 7.2 验证器目录结构

```
app/validate/
├── admin_api/                 # 后台管理API验证器
│   └── v1/                   # 版本1验证器
│       ├── Member.php        # 会员验证器
│       ├── Goods.php         # 商品验证器
│       ├── User.php          # 用户验证器
│       └── ...               # 其他验证器
├── member_api/               # 会员端API验证器
│   └── v1/                   # 版本1验证器
│       ├── Register.php      # 注册验证器
│       ├── Passport.php      # 登录验证器
│       ├── GoodsOrder.php    # 订单验证器
│       └── ...               # 其他验证器
├── api/                      # 通用API验证器
│   └── v1/                   # 版本1验证器
├── gateway/                  # 网关验证器
│   ├── PayNotify.php         # 支付回调验证器
│   ├── WechatEvent.php       # 微信事件验证器
│   └── ...                   # 其他验证器
├── openapi/                  # 开放API验证器
│   └── v1/                   # 版本1验证器
└── test/                     # 测试验证器
    └── User.php              # 测试用户验证器
```

### 7.3 验证器结构规范

```php
<?php
declare(strict_types=1);

namespace app\validate\admin_api\v1;

use app\common\validate\ValidateBase;

class Member extends ValidateBase
{
    // 验证规则定义
    protected $rule = [
        'guid|唯一标识'                  => ['require'],
        'mobile|手机号'                  => ['mobile'],
        'money|金额'                     => ['require', 'number'],
        'brokerage_ratio|分佣比例'       => ['between:0,1'],
        'member_guid|会员标识'           => ['require'],
    ];

    // 错误信息定义
    protected $message = [
        'mobile.mobile' => '请输入正确的手机号',
        'money.require' => '请输入金额',
        'money.number'  => '金额必须为数字',
    ];

    // 验证场景定义
    protected $scene = [
        'add'      => ['mobile', 'name', 'brokerage_ratio'],
        'edit'     => ['guid', 'mobile', 'name'],
        'recharge' => ['member_guid', 'money'],
        'send_sms' => ['content'],
    ];
}
```

### 7.4 验证器命名规范

#### 7.4.1 文件命名规范

```php
// 文件名与控制器名保持一致（大驼峰命名）
Member.php              // 对应 MemberController
GoodsOrder.php          // 对应 GoodsOrderController
CouponPlan.php          // 对应 CouponPlanController
PayNotify.php           // 对应 PayNotifyController
```

#### 7.4.2 命名空间规范

```php
// 命名空间与控制器路径保持一致
namespace app\validate\admin_api\v1;     // 对应 app\controller\admin_api\v1
namespace app\validate\member_api\v1;    // 对应 app\controller\member_api\v1
namespace app\validate\gateway;          // 对应 app\controller\gateway
```

#### 7.4.3 场景命名规范

```php
protected $scene = [
    'add'           => [...],    // 新增场景
    'edit'          => [...],    // 编辑场景
    'delete'        => [...],    // 删除场景
    'submit'        => [...],    // 提交场景
    'login'         => [...],    // 登录场景
    'register'      => [...],    // 注册场景
    'reset_password'=> [...],    // 重置密码场景
    'send_sms'      => [...],    // 发送短信场景
    'recharge'      => [...],    // 充值场景
];
```

### 7.5 自定义验证规则

#### 7.5.1 ValidateBase 提供的自定义规则

```php
// 项目内置的自定义验证规则
protected $rule = [
    'guid'           => ['guid'],              // GUID格式验证
    'password'       => ['safe_password'],     // 安全密码验证
    'member_id'      => ['exist:member,id'],   // 数据存在性验证
    'unique_mobile'  => ['not_exist:member,mobile'], // 数据唯一性验证
    'parent_id'      => ['exist_pid:category'], // 父级ID验证
];
```

#### 7.5.2 常用验证规则示例

```php
protected $rule = [
    // 基础验证
    'name|姓名'          => ['require', 'max:50'],
    'mobile|手机号'      => ['require', 'mobile'],
    'email|邮箱'         => ['email'],
    'age|年龄'           => ['integer', 'between:1,120'],

    // 数字验证
    'money|金额'         => ['require', 'number', 'gt:0'],
    'ratio|比例'         => ['float', 'between:0,1'],
    'count|数量'         => ['integer', 'egt:1'],

    // 字符串验证
    'content|内容'       => ['require', 'min:10', 'max:500'],
    'title|标题'         => ['require', 'chsAlphaNum', 'max:100'],
    'code|编码'          => ['require', 'alphaNum', 'length:6,20'],

    // 日期时间验证
    'start_time|开始时间' => ['require', 'date'],
    'end_time|结束时间'   => ['require', 'date', 'after:start_time'],

    // 选择验证
    'status|状态'        => ['require', 'in:0,1'],
    'type|类型'          => ['require', 'in:1,2,3,4'],
    'gender|性别'        => ['in:male,female'],

    // 文件验证
    'avatar|头像'        => ['file', 'fileExt:jpg,png,gif', 'fileSize:2048'],
    'document|文档'      => ['file', 'fileMime:application/pdf'],

    // 数组验证
    'tags|标签'          => ['array'],
    'ids|ID列表'         => ['array', 'each:integer'],

    // 自定义验证
    'guid|唯一标识'      => ['require', 'guid'],
    'password|密码'      => ['require', 'safe_password'],
    'member_id|会员ID'   => ['require', 'exist:member,id'],
];
```

### 7.6 验证器使用最佳实践

#### 7.6.1 业务代码中的使用

```php
// 📋 标准验证处理方式（使用自动验证机制）
public function add()
{
    $params = $this->params;

    // 验证中间件已自动完成参数验证
    // 如果验证失败，会自动抛出ValidateException
    // 全局异常处理器会自动返回错误信息

    // 直接使用已验证的参数进行业务处理
    $member = new MemberModel();
    $result = $member->save($params);

    result($result, '添加成功');
}
```

````

#### 7.6.3 验证失败处理

```php
// 验证失败时的自动处理流程：
// 1. Validate中间件检测到验证失败
// 2. 抛出ValidateException异常
// 3. ExceptionHandle自动捕获异常
// 4. 自动调用error()返回错误信息给前端
// 5. 前端接收到统一格式的错误响应

// 前端接收到的错误响应格式：
{
    "code": -1,
    "msg": "请输入正确的手机号格式",
    "data": {},
    "time": 1234567890
}
````

## 9. 服务类规范

### 7.1 服务类结构

```php
<?php
declare(strict_types=1);

namespace app\common\service;

class TokenService
{
    /**
     * 生成JWT Token
     * @param array $data
     * @return string
     */
    public static function encode(array $data): string
    {
        // 实现逻辑
    }

    /**
     * 验证JWT Token
     * @param string $token
     * @return array
     */
    public static function verify(string $token): array
    {
        // 实现逻辑
    }
}
```

## 10. 队列任务规范

### 8.1 队列任务结构

```php
<?php
declare(strict_types=1);

namespace app\queue\controller;

use think\queue\Job;

class Test extends BasicQueue
{
    public function handle(Job $job, $data)
    {
        $attempts = $job->attempts();

        try {
            // 业务逻辑处理

            $job->delete(); // 删除任务
            return true;
        } catch (\Exception $e) {
            $job->release(10); // 延迟重试
            throw $e;
        }
    }
}
```

### 8.2 队列任务调用

```php
// 推送队列任务
job()->set_job_name('Test@handle')->push_job($data);
```

## 11. 异常处理规范

### 9.1 全局异常自动处理机制

**重要说明：一卡易项目具有全局异常自动处理机制，业务代码中默认不需要手动捕获异常。**

#### 9.1.1 自动异常接管

项目中的 `app/ExceptionHandle.php` 会自动接管所有异常：

```php
// ✅ 推荐：业务代码中直接抛出异常，无需手动捕获
public function saveMember()
{
    $params = $this->params;

    // 参数验证失败会自动抛出ValidateException
    $this->validate($params, 'Member.add');

    // 数据库操作失败会自动抛出PDOException
    $member = new MemberModel();
    $result = $member->save($params); // 失败时自动抛出异常

    // 第三方服务调用失败会自动抛出Exception
    $smsService = new SmsService();
    $smsService->sendWelcomeSms($params['mobile']); // 失败时自动抛出异常

    // 只有成功时才会执行到这里
    result($result, '保存成功');
}
```

#### 9.1.2 自动异常处理流程

```
业务代码抛出异常 → ExceptionHandle::report() → 自动记录日志 → 自动发送通知 → 自动调用error()返回错误信息
```

#### 9.1.3 异常自动处理特性

- **自动日志记录**: 异常信息自动记录到日志文件
- **自动通知发送**: 重要异常自动发送企业微信通知
- **自动错误返回**: 自动调用 `error()` 方法返回统一格式的错误信息
- **自动去重**: 相同异常 60 秒内只记录一次，避免日志刷屏
- **自动脱敏**: 自动过滤敏感参数（access_token 等）

### 9.2 异常分类

- **系统异常**: 数据库连接失败、文件读取错误等
- **业务异常**: 参数验证失败、权限不足等
- **第三方异常**: 微信支付异常、短信发送失败等

### 9.3 何时需要手动捕获异常

**默认情况下不需要手动捕获异常，但以下场景需要手动处理：**

#### 9.3.1 需要手动捕获的场景

```php
// ✅ 场景1：数据库事务处理
public function transferPoints()
{
    Db::startTrans();
    try {
        // 扣减积分
        $this->deductPoints($fromMemberId, $amount);
        // 增加积分
        $this->addPoints($toMemberId, $amount);

        Db::commit();
    } catch (\Exception $e) {
        Db::rollback();
        throw $e; // 重新抛出异常，让全局异常处理器处理
    }

    success('积分转移成功');
}

// ✅ 场景2：需要忽略某些异常继续执行
public function sendNotifications()
{
    $members = Member::select();

    foreach ($members as $member) {
        try {
            // 发送短信，失败不影响其他用户
            $smsService = new SmsService();
            $smsService->send($member['mobile'], '通知内容');
        } catch (\Exception $e) {
            // 记录失败日志，但不中断流程
            wr_log('短信发送失败：' . $member['mobile'] . ' - ' . $e->getMessage());
            continue;
        }
    }

    success('通知发送完成');
}

// ✅ 场景3：需要特殊错误处理逻辑
public function processPayment()
{
    try {
        $payService = new PayService();
        $result = $payService->createOrder($orderData);
    } catch (PaymentException $e) {
        // 支付异常需要特殊处理
        if ($e->getCode() == 'INSUFFICIENT_BALANCE') {
            error('余额不足，请充值后重试');
        } else {
            error('支付失败：' . $e->getMessage());
        }
    }

    result($result, '支付成功');
}
```

#### 9.3.2 不需要手动捕获的场景

```php
// ✅ 推荐：让全局异常处理器自动处理
public function createMember()
{
    $params = $this->params;

    // 验证失败会自动抛出ValidateException
    $this->validate($params, 'Member.add');

    // 数据库操作失败会自动抛出异常
    $member = new MemberModel();
    $result = $member->save($params);

    // 第三方服务失败会自动抛出异常
    $smsService = new SmsService();
    $smsService->sendWelcomeSms($params['mobile']);

    // 只有全部成功才会执行到这里
    result($result, '会员创建成功');
}
```

### 9.4 try-catch 中的返回数据限制

**重要规范：try-catch 中的数据返回必须在 catch 外部进行。**

#### 9.4.1 try-catch 数据返回规范

```php
// 📋 标准异常处理模式
try {
    // 只在try块中处理可能抛出异常的业务逻辑
    $result = $this->processData();

    // 记录操作日志
    wr_log('操作成功');

} catch (\Exception $e) {
    wr_log($e->getMessage());
    error('系统异常');
    return; // 异常时直接返回
}

// 成功时在try-catch外部返回数据
result($result, '操作成功');
```

#### 9.4.2 正确的异常处理方式

```php
// ✅ 正确：将返回数据的方法放在try-catch外部
try {
    // 只在try块中处理可能抛出异常的业务逻辑
    $member = new MemberModel();
    $result = $member->save($data);

    // 记录操作日志
    $log = new LogModel();
    $log->save([
        'action' => 'member_save',
        'data' => json_encode($data),
        'create_time' => time()
    ]);

} catch (\Exception $e) {
    // 异常处理
    wr_log('会员保存失败：' . $e->getMessage());
    error('保存失败，请稍后重试');
    return; // 异常时直接返回
}

// 成功时在try-catch外部返回数据
result($result, '保存成功');
```

#### 9.3.3 数据库事务的正确处理

```php
// ✅ 正确：事务处理的标准写法
Db::startTrans();
try {
    // 数据库操作
    $member = new MemberModel();
    $member->save($memberData);

    $memberInfo = new MemberInfoModel();
    $memberInfo->save($infoData);

    // 提交事务
    Db::commit();

} catch (\Exception $e) {
    // 回滚事务
    Db::rollback();
    wr_log('事务执行失败：' . $e->getMessage());
    error('操作失败，请稍后重试');
    return;
}

// 事务成功后返回结果
success('操作成功');
```

#### 9.3.4 API 接口的异常处理模式

```php
// ✅ 推荐：API接口的标准异常处理模式
public function saveMember()
{
    $params = $this->params;

    // 参数验证
    $validate = new MemberValidate();
    if (!$validate->check($params)) {
        error($validate->getError());
    }

    try {
        // 业务逻辑处理
        $memberService = new MemberService();
        $result = $memberService->createMember($params);

        // 发送通知（可能失败的操作）
        $smsService = new SmsService();
        $smsService->sendWelcomeSms($params['mobile']);

    } catch (\Exception $e) {
        wr_log('会员创建失败：' . $e->getMessage());
        error('创建失败：' . $e->getMessage());
        return;
    }

    // 成功返回
    result($result, '会员创建成功');
}
```

#### 9.3.5 异常处理最佳实践

```php
// ✅ 最佳实践：细化异常处理
try {
    // 数据库操作
    $result = $this->saveToDatabase($data);

} catch (PDOException $e) {
    // 数据库异常
    wr_log('数据库异常：' . $e->getMessage());
    error('数据保存失败');
    return;

} catch (ValidationException $e) {
    // 验证异常
    error($e->getMessage());
    return;

} catch (\Exception $e) {
    // 其他异常
    wr_log('未知异常：' . $e->getMessage());
    error('系统异常，请稍后重试');
    return;
}

// 所有异常处理完毕后，正常返回数据
result($result);
```

## 12. 日志记录规范

### 10.1 日志记录方法

```php
// 普通日志
wr_log('日志信息');

// 错误日志
logToFile('错误信息');

// API日志
Log::channel('api_log')->info('API调用日志');
```

### 10.2 日志级别

- `debug`: 调试信息
- `info`: 一般信息
- `warning`: 警告信息
- `error`: 错误信息
- `critical`: 严重错误

### 10.3 项目日志记录方法

#### 10.3.1 数据库日志记录 (`wr_log`)

**重要说明：`wr_log()` 是项目的核心日志记录方法，将日志写入数据库，便于查询和统计。**

```php
/**
 * 写日志到数据库
 * @param string|array $content 日志内容
 * @param int|bool $notify 是否需要用微信企业号通知
 * @param null|string $bid 商家唯一标识
 * @return bool
 */
function wr_log($content, $notify = false, $bid = null)

// ✅ 基础用法
wr_log('用户登录成功');
wr_log('订单创建失败：参数验证错误');

// ✅ 带通知的重要日志
wr_log('系统异常：数据库连接失败', true);
wr_log('支付回调异常', 1);  // 1 等同于 true

// ✅ 带商户标识的日志
wr_log('会员注册成功', false, $bid);
wr_log('商品库存不足', false, $this->get_bid());

// ✅ 记录数组数据（自动转JSON）
wr_log([
    'action' => 'member_login',
    'user_id' => $user_id,
    'ip' => request()->ip(),
    'time' => time()
]);

// ✅ 记录复杂业务数据
$order_data = [
    'order_guid' => $order_guid,
    'member_guid' => $member_guid,
    'total_money' => $total_money,
    'goods_list' => $goods_list
];
wr_log('订单创建成功：' . json_encode($order_data, JSON_UNESCAPED_UNICODE));
```

#### 10.3.2 文件日志记录 (`logToFile`)

**重要说明：`logToFile()` 用于调试日志，将日志写入文件，便于开发调试和问题排查。**

```php
/**
 * 打印输出数据到文件
 * @param mixed $data 要记录的数据
 * @param string|null $path_name 文件路径（可选）
 */
function logToFile($data, $path_name = null)

// ✅ 基础调试日志
logToFile('调试信息：进入方法');
logToFile('参数验证通过');

// ✅ 记录请求参数
logToFile('接收到的参数：' . json_encode($this->params));
logToFile($this->params);  // 直接记录数组

// ✅ 记录API响应
$response = curl()->post($url, $data)->get_body();
logToFile('API响应：' . json_encode($response));

// ✅ 记录复杂对象
logToFile($model_instance);  // 记录模型对象
logToFile($exception);       // 记录异常对象

// ✅ 自定义路径（一般不需要）
logToFile($debug_data, '/custom/debug/path/');
```

#### 10.3.3 调试日志记录 (`debug_log`)

```php
/**
 * 写调试日志，增加前缀便于删除
 * @param string|array $content 日志内容
 * @param int|bool $notify 是否需要用微信企业号通知
 * @return bool
 */
function debug_log($content, $notify = false)

// ✅ 调试日志（带debug_log前缀）
debug_log('进入支付处理方法');
debug_log(['step' => 1, 'data' => $payment_data]);
debug_log('支付参数验证完成', false);
```

#### 10.3.4 日志记录最佳实践

```php
// ✅ 业务流程日志
public function createOrder()
{
    $params = $this->params;

    // 记录开始
    wr_log('开始创建订单', false, $this->get_bid());

    try {
        // 参数验证
        $this->validate($params, 'Order.create');
        wr_log('订单参数验证通过');

        // 业务处理
        $order = new OrderModel();
        $result = $order->createOrder($params);

        // 记录成功
        wr_log('订单创建成功：订单号' . $result['order_no'], false, $this->get_bid());

        result($result, '订单创建成功');

    } catch (Exception $e) {
        // 记录错误（带通知）
        wr_log('订单创建失败：' . $e->getMessage(), true, $this->get_bid());
        throw $e;
    }
}

// ✅ 调试日志使用
public function debugPayment()
{
    $params = $this->params;

    // 调试：记录原始参数
    debug_log('支付参数：' . json_encode($params));

    // 调试：记录处理步骤
    debug_log('开始验证支付参数');

    // 调试：记录中间结果
    $processed_data = $this->processPaymentData($params);
    debug_log('处理后的数据：' . json_encode($processed_data));

    // 调试：记录API调用
    logToFile('调用支付API：' . $api_url);
    logToFile('请求参数：' . json_encode($api_params));

    $response = curl()->post($api_url, $api_params)->get_body();
    logToFile('API响应：' . json_encode($response));
}

// ✅ 异常处理中的日志
public function handleException($e)
{
    // 详细错误信息写入文件
    logToFile([
        'error_message' => $e->getMessage(),
        'error_file' => $e->getFile(),
        'error_line' => $e->getLine(),
        'error_trace' => $e->getTraceAsString(),
        'request_params' => request()->param(),
        'request_url' => request()->url(),
        'user_agent' => request()->header('user-agent'),
        'ip' => request()->ip()
    ]);

    // 简要错误信息写入数据库（带通知）
    wr_log('系统异常：' . $e->getMessage(), true);
}
```

### 10.4 日志文件管理

```
日志文件存储位置：
├── runtime/log/                    # 应用日志目录
│   ├── 202401/                    # 按月份分目录
│   │   ├── 01.log                 # 按日期分文件
│   │   ├── 02.log
│   │   └── ...
│   ├── error/                     # 错误日志
│   ├── sql/                       # SQL日志
│   └── api/                       # API日志

日志轮转规则：
- 按天分割日志文件
- 保留最近30天的日志
- 错误日志单独存储
- 大文件自动压缩
```

## 13. 数据库规范

### 11.1 表结构规范

#### 11.1.1 数据库文件管理

- **数据库结构文件**: `disk/sql` 目录下存放当前数据库结构
- **版本控制**: 数据库结构变更需要更新对应的 SQL 文件
- **文档同步**: 表结构变更后需要同时更新相关文档

#### 11.1.2 表命名规范

```sql
-- ✅ 正确：表名使用小写下划线命名
CREATE TABLE `goods_order`
(
    `guid` char(36) NOT NULL COMMENT '订单唯一标识',
    `bid`  char(36) NOT NULL COMMENT '商户ID',
    -- 其他字段...
    PRIMARY KEY (`guid`),
    KEY    `idx_bid` (`bid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品订单表';

CREATE TABLE `member_address`
(
    `guid` char(36) NOT NULL COMMENT '地址唯一标识',
    `bid`  char(36) NOT NULL COMMENT '商户ID',
    -- 其他字段...
    PRIMARY KEY (`guid`),
    KEY    `idx_bid_member` (`bid`, `member_guid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员地址表';

-- ❌ 错误：不规范的表名
CREATE TABLE `GoodsOrder`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    -- 其他字段...
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; -- 大驼峰命名

CREATE TABLE `goodsorder`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    -- 其他字段...
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; -- 全小写无分隔

CREATE TABLE `goods-order`
(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    -- 其他字段...
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; -- 使用连字符
```

#### 11.1.3 必需字段规范

**多商户系统必需字段：**

```sql
-- 每个表都必须包含的字段
`bid`
char(36) NOT NULL COMMENT '商户ID',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON
UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

-- 软删除字段（推荐）
    `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
```

**重要说明：**

- `bid` 字段用于区分不同商户，所有数据库操作都必须带上此条件
- `create_time/update_time` 字段由框架自动维护，插入和更新时无需手动设置
- 软删除使用 `delete_time` 字段，而不是 `status` 字段

### 11.2 字段命名规范

#### 11.2.1 主键规范

```sql
-- ✅ 推荐：使用guid作为主键
`guid`
char(36) NOT NULL COMMENT '唯一标识',
PRIMARY KEY (`guid`)

-- ✅ 可选：使用自增id作为主键
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
PRIMARY KEY (`id`)

-- 同时使用guid和id的情况
`id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
`guid` char(36) NOT NULL COMMENT '唯一标识',
PRIMARY KEY (`id`),
UNIQUE KEY `uk_guid` (`guid`)
```

#### 11.2.2 字段命名规则

```sql
-- ✅ 正确：字段名使用小写下划线命名
`member_guid`
char(36) NOT NULL COMMENT '会员唯一标识',
`goods_name` varchar(255) NOT NULL COMMENT '商品名称',
`total_money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认 0否 1是',
`province_id` int(11) NOT NULL DEFAULT '0' COMMENT '省份ID',

-- ❌ 错误：不规范的字段命名
`memberGuid` char(36) NOT NULL,     -- 驼峰命名
`goodsname` varchar(255) NOT NULL,  -- 全小写无分隔
`total-money` decimal(10,2) NOT NULL, -- 使用连字符
`CreateTime` datetime DEFAULT NULL,   -- 大驼峰命名
```

#### 11.2.3 字段注释规范

```sql
-- ✅ 必须有详细的字段注释
CREATE TABLE `goods_order`
(
    `guid`         char(36)       NOT NULL COMMENT '订单唯一标识',
    `bid`          char(36)       NOT NULL COMMENT '商户ID',
    `bill_number`  varchar(50)    NOT NULL COMMENT '订单号',
    `member_guid`  char(36)       NOT NULL COMMENT '会员唯一标识',
    `status`       tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态 -3已退款 -2已取消 -1待付款 0待发货 1待收货 2已完成',
    `total_money`  decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
    `paid_money`   decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '储值支付金额',
    `paid_wechat`  decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '微信支付金额',
    `goods_info`   text COMMENT '商品信息JSON格式',
    `express_code` varchar(20)             DEFAULT NULL COMMENT '快递公司编码',
    `express_no`   varchar(50)             DEFAULT NULL COMMENT '快递单号',
    `mobile`       varchar(20)    NOT NULL COMMENT '收货人手机号',
    `true_name`    varchar(50)    NOT NULL COMMENT '收货人姓名',
    `address`      varchar(500)   NOT NULL COMMENT '收货详细地址',
    `remark`       text COMMENT '订单备注',
    `create_time`  datetime                DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  datetime                DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time`  datetime                DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`guid`),
    KEY            `idx_bid` (`bid`),
    KEY            `idx_member` (`bid`, `member_guid`),
    KEY            `idx_status` (`bid`, `status`),
    KEY            `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品订单表';

-- ❌ 错误：缺少注释或注释不清晰
CREATE TABLE `goods_order`
(
    `guid`   char(36)       NOT NULL, -- 缺少注释
    `status` tinyint(4) NOT NULL,     -- 注释不清晰，没有说明状态值含义
    `money`  decimal(10, 2) NOT NULL, -- 字段名不明确
);
```

#### 11.2.4 索引命名规范

```sql
-- 主键索引
PRIMARY KEY (`guid`)

-- 唯一索引：uk_ + 字段名
UNIQUE KEY `uk_bill_number` (`bid`, `bill_number`)
UNIQUE KEY `uk_mobile` (`bid`, `mobile`)

-- 普通索引：idx_ + 字段名
KEY `idx_bid` (`bid`)
KEY `idx_member` (`bid`, `member_guid`)
KEY `idx_status` (`bid`, `status`)
KEY `idx_create_time` (`create_time`)

-- 复合索引：idx_ + 主要字段名
KEY `idx_bid_status_time` (`bid`, `status`, `create_time`)
```

### 11.3 数据库操作规范

#### 11.3.1 多商户数据隔离

```php
// 📋 数据库操作标准格式

// 查询操作标准格式
$map = [
    ['bid', '=', $this->get_bid()],      // 商户隔离（必需）
    ['status', '=', 1],                  // 业务条件
    ['delete_time', 'NULL', NULL],       // 排除已删除（推荐）
];

// 新增数据标准格式
$data = [
    'guid' => create_guid(),             // 唯一标识（推荐）
    'bid' => $this->get_bid(),          // 商户ID（必需）
    'member_guid' => $member_guid,       // 业务字段
    'name' => $name,                     // 业务字段
    // create_time 和 update_time 由框架自动维护
];
```

#### 11.3.2 时间字段处理

```php
// 📋 时间字段处理标准（框架自动维护）

// 新增数据时
$data = [
    'guid' => create_guid(),
    'bid' => $this->get_bid(),
    'name' => $name,
    'status' => 1,
    // create_time 和 update_time 由框架自动维护
];

// 更新数据时
$update_data = [
    'name' => $new_name,
    'status' => 2,
    // update_time 框架会自动更新
];
```

#### 11.3.3 软删除处理

```php
// 📋 软删除标准操作

// 删除数据（软删除）
$delete_data = [
    'delete_time' => date('Y-m-d H:i:s'),
];
$map = [
    ['bid', '=', $this->get_bid()],
    ['id', '=', $id],
];
$db->where($map)->save($delete_data);

// 查询时排除已删除数据
$map = [
    ['bid', '=', $this->get_bid()],
    ['delete_time', 'NULL', NULL],  // 排除已删除数据
];
```

### 11.4 自动查询处理机制

**重要说明：一卡易项目具有自动查询处理机制，无需手动编写参数封装和查询条件构建代码。**

#### 11.4.1 自动查询流程

```
前端参数 → BaseController自动解析 → 自动构建查询条件 → 自动分页排序 → 返回结果
```

#### 11.4.2 标准查询方法使用

```php
// ✅ 推荐：使用框架自动查询处理
public function index()
{
    $db = new MemberModel();
    $map = [['bid', '=', $this->get_bid()]]; // 只需定义基础条件
    $this->model = $db->where($map)->order(['create_time' => 'DESC']);
    result($this->_list()); // 自动处理分页、搜索、排序
}

// ✅ 复杂查询示例
public function my_agent()
{
    $db = new BusinessModel();
    $join = [
        ['business b1', 'b.parent_guid = b1.guid'],
    ];
    $field = [
        'b.*',
        'b1.account' => 'agent_account',
        "CONCAT(b1.business_name,'(',b1.account,')')" => 'agent',
    ];
    $map = [
        ['b.type', '=', 2],
        ['b.parent_guid', '=', $this->get_bid()],
        ['b.wxapp_admin_id', '>', 0]
    ];
    $this->model = $db->alias('b')->where($map)->join($join)->field($field)->order(['b.create_time' => 'DESC']);
    result($this->_list()); // 自动处理前端传递的查询参数
}
```

#### 11.4.3 自动参数处理机制

框架会自动处理以下前端参数：

```javascript
// 前端发送的查询参数会被自动处理
{
  "page"
:
  1,                                    // 自动分页
    "limit"
:
  20,                                  // 每页数量
    "field"
:
  "create_time",                       // 排序字段
    "order"
:
  "desc",                              // 排序方向
    "key"
:
  "name",                                // 搜索字段
    "value"
:
  "张三",                              // 搜索值
    "status"
:
  1,                                  // 精确匹配
    "create_time"
:
  "2024-01-01 - 2024-12-31",    // 时间范围
    "money#>="
:
  100,                              // 大于等于条件
    "age#<"
:
  30,                                  // 小于条件
    "name#LIKE"
:
  "张"                             // 模糊搜索
}
```

#### 11.4.4 自动查询条件构建规则

```php
// 1. 精确匹配：参数名对应字段名
// 前端: {"status": 1}
// 自动生成: ['status', '=', 1]

// 2. 模糊搜索：key + value 组合
// 前端: {"key": "name", "value": "张三"}
// 自动生成: ['name', 'LIKE', '%张三%']

// 3. 操作符查询：字段名#操作符
// 前端: {"money#>=": 100}
// 自动生成: ['money', '>=', 100]

// 4. 时间范围查询：自动识别时间格式
// 前端: {"create_time": "2024-01-01 - 2024-12-31"}
// 自动生成: ['create_time', 'BETWEEN', ['2024-01-01 00:00:00', '2024-12-31 23:59:59']]

// 5. 动态条件查询：支持复杂条件组合
// 前端: {"conditions": {"jsonStr": [...]}}
// 自动解析JSON条件并构建查询
```

#### 11.4.5 支持的操作符

```php
// 比较操作符
'field#='     => ['field', '=', 'value']      // 等于
'field#<>'    => ['field', '<>', 'value']     // 不等于
'field#>'     => ['field', '>', 'value']      // 大于
'field#>='    => ['field', '>=', 'value']     // 大于等于
'field#<'     => ['field', '<', 'value']      // 小于
'field#<='    => ['field', '<=', 'value']     // 小于等于

// 模糊查询
'field#LIKE'     => ['field', 'LIKE', '%value%']     // 包含
'field#NOT LIKE' => ['field', 'NOT LIKE', '%value%'] // 不包含

// 范围查询
'field#BETWEEN'     => ['field', 'BETWEEN', [start, end]]     // 在范围内
'field#NOT BETWEEN' => ['field', 'NOT BETWEEN', [start, end]] // 不在范围内

// 集合查询
'field#IN'     => ['field', 'IN', [1,2,3]]     // 在集合中
'field#NOT IN' => ['field', 'NOT IN', [1,2,3]] // 不在集合中
```

### 11.5 查询构建规范

```php
// 📋 标准列表查询模式（使用自动查询机制）
public function index()
{
    $db = new MemberModel();
    // 只需定义业务相关的固定条件
    $map = [
        ['bid', '=', $this->get_bid()],        // 商户隔离
        ['delete_time', 'NULL', NULL],         // 排除已删除
    ];
    $this->model = $db->where($map)->order(['create_time' => 'DESC']);
    result($this->_list()); // 自动处理分页、搜索、排序
}
```

#### 11.5.1 自动查询最佳实践

```php
// ✅ 标准列表查询模式
public function index()
{
    $db = new MemberModel();
    $map = [['bid', '=', $this->get_bid()]]; // 业务固定条件
    $this->model = $db->where($map)->order(['create_time' => 'DESC']);
    result($this->_list());
}

// ✅ 带关联查询的列表
public function index()
{
    $db = new GoodsOrderModel();
    $join = [
        ['member m', 'o.member_guid = m.guid'],
        ['goods g', 'o.goods_guid = g.guid'],
    ];
    $field = [
        'o.*',
        'm.name as member_name',
        'g.title as goods_title',
    ];
    $map = [['o.bid', '=', $this->get_bid()]];
    $this->model = $db->alias('o')->join($join)->field($field)->where($map)->order(['o.create_time' => 'DESC']);
    result($this->_list());
}

// ✅ 带统计的查询
public function index()
{
    $db = new MemberModel();
    $map = [['bid', '=', $this->get_bid()]];
    $this->model = $db->where($map)
        ->field('*, (SELECT COUNT(*) FROM goods_order WHERE member_guid = member.guid) as order_count')
        ->order(['create_time' => 'DESC']);
    result($this->_list());
}
```

#### 11.5.2 前端查询参数示例

```javascript
// 基础分页查询
post_layui_admin_api_v1(
  "/member/index",
  {
    page: 1,
    limit: 20,
  },
  function (result) {
    // 处理结果
  }
);

// 带搜索的查询
post_layui_admin_api_v1(
  "/member/index",
  {
    page: 1,
    limit: 20,
    key: "name", // 搜索字段
    value: "张三", // 搜索值
  },
  function (result) {
    // 处理结果
  }
);

// 多条件查询
post_layui_admin_api_v1(
  "/member/index",
  {
    page: 1,
    limit: 20,
    status: 1, // 精确匹配
    "money#>=": 100, // 大于等于
    create_time: "2024-01-01 - 2024-12-31", // 时间范围
    field: "create_time", // 排序字段
    order: "desc", // 排序方向
  },
  function (result) {
    // 处理结果
  }
);

// 动态条件查询
post_layui_admin_api_v1(
  "/member/index",
  {
    conditions: {
      jsonStr: [
        {
          conditionFieldVal: "name",
          conditionOptionVal: "like",
          conditionValueVal: { value: "张" },
        },
        {
          conditionFieldVal: "age",
          conditionOptionVal: "between",
          conditionValueLeftVal: { value: 18 },
          conditionValueRightVal: { value: 60 },
        },
      ],
    },
  },
  function (result) {
    // 处理结果
  }
);
```

#### 11.5.3 自动查询忽略字段

框架会自动忽略以下参数，不会作为查询条件：

```php
// 系统保留参数（不会作为查询条件）
$ignored_fields = [
    'field',                // 排序字段
    'order',                // 排序方向
    'page',                 // 页码
    'limit',                // 每页数量
    ':group',               // 分组字段
    'key',                  // 搜索字段名
    'value',                // 搜索值
    'access_token',         // 访问令牌
    'business_user_token',  // 商家用户令牌
    'conditions',           // 动态条件
];
```

#### 11.5.4 特殊查询场景处理

```php
// 场景1：需要手动处理特殊搜索逻辑
public function agent_list()
{
    $map = [];
    $params = $this->params;

    // 手动处理关键词搜索（账号或商家名称）
    if (!empty($params['keyword'])) {
        if (ctype_alnum($params['keyword'])) {
            $map[] = ['account', 'like', '%' . $params['keyword'] . '%'];
        } else {
            $map[] = ['business_name', 'like', '%' . $params['keyword'] . '%'];
        }
        unset($this->params['keyword']); // 移除参数，避免重复处理
    }

    $this->model = $this->model->where($map)->order(['id' => 'DESC']);
    result($this->_list());
}

// 场景2：需要返回特殊格式的数据
public function select_list()
{
    $db = new MemberModel();
    $map = [['bid', '=', $this->get_bid()]];
    $this->model = $db->where($map)->field('guid as value, name as label')->order(['create_time' => 'DESC']);

    $list = $this->_list();
    // 返回适合下拉选择的格式
    $data = [
        'code' => 0,
        'msg' => 'success',
        'data' => $list['data'],
        'count' => $list['total'],
    ];
    return json($data);
}
```

### 11.3 数据库操作变量命名规范

**重要规范：在进行数据库操作前，必须先定义好相关变量，遵循统一的命名规范。**

#### 11.3.1 模型实例命名规范

```php
// ✅ 正确：模型实例使用 $db_ 前缀 + 下划线命名
$db_goods_order = new GoodsOrderModel();
$db_member_address = new MemberAddressModel();
$db_coupon_send_note = new CouponSendNoteModel();
$db_user_money_note = new UserMoneyNoteModel();
$db_extend_field = new ExtendFieldModel();

// ❌ 错误：不规范的命名
$goodsOrder = new GoodsOrderModel();
$memberAddr = new MemberAddressModel();
$model = new CouponSendNoteModel();
```

#### 11.3.2 查询条件命名规范

```php
// ✅ 正确：查询条件使用 $map_ 前缀 + 表名或业务含义
$map_goods_order = [
    ['bid', '=', $bid],
    ['status', '=', 1],
    ['delete_time', 'NULL', NULL],
];

$map_member_address = [
    ['bid', '=', $bid],
    ['member_guid', '=', $member_guid],
    ['is_default', '=', 1],
];

$map_area_rule = [
    ['bid', '=', $bid],
    ['status', '=', 1],
];

// 简单查询可以直接使用 $map
$map = [
    ['bid', '=', $bid],
    ['guid', '=', $guid],
];
```

#### 11.3.3 更新数据命名规范

```php
// ✅ 正确：更新数据使用 $update_data 或具体含义
$update_data = [
    'status' => 1,
    'send_or_pick_up_time' => format_timestamp(),
    'send_or_pick_up_user_id' => $user_id,
    'express_no' => $express_no,
];

// 特定业务的更新数据可以使用具体命名
$member_update_data = [
    'name' => $name,
    'mobile' => $mobile,
    'update_time' => time(),
];

$order_update_data = [
    'status' => 2,
    'complete_time' => format_timestamp(),
];
```

#### 11.3.4 插入数据命名规范

```php
// ✅ 正确：插入数据使用 $data 或 $insert_data
$data = [
    'guid' => create_guid(),
    'bid' => $bid,
    'member_guid' => $member_guid,
    'goods_guid' => $goods_guid,
    'amount' => $amount,
    'create_time' => format_timestamp(),
];

$insert_data = [
    'bid' => $bid,
    'user_guid' => $user_guid,
    'way' => 4,
    'type' => -1,
    'money' => $money,
    'memo' => '[发货扣除]',
    'relation_guid' => $order_guid,
];
```

#### 11.3.5 标准数据库操作流程

```php
// ✅ 推荐：标准的数据库操作流程
public function updateOrderStatus($bid, $order_guid, $status)
{
    // 1. 定义模型实例
    $db_goods_order = new GoodsOrderModel();

    // 2. 定义查询条件
    $map = [
        ['bid', '=', $bid],
        ['guid', '=', $order_guid],
        ['status', '<>', $status], // 避免重复更新
    ];

    // 3. 定义更新数据
    $update_data = [
        'status' => $status,
        'update_time' => format_timestamp(),
        'update_user_id' => $this->get_user_id_from_request(),
    ];

    // 4. 执行更新操作
    $result = $db_goods_order::update($update_data, $map);

    return $result;
}

// ✅ 复杂业务操作示例
public function createMemberAddress($bid, $member_guid, $address_data)
{
    // 1. 定义相关模型实例
    $db_member_address = new MemberAddressModel();
    $db_area = new AreaModel();

    // 2. 验证地区信息
    $map_area = [
        ['id', 'IN', [$address_data['province_id'], $address_data['city_id'], $address_data['area_id']]],
    ];
    $area_count = $db_area->where($map_area)->count();
    if ($area_count < 3) {
        throw new Exception('地区信息不完整');
    }

    // 3. 如果是默认地址，先取消其他默认地址
    if ($address_data['is_default'] == 1) {
        $map_default = [
            ['bid', '=', $bid],
            ['member_guid', '=', $member_guid],
            ['is_default', '=', 1],
        ];
        $update_default_data = ['is_default' => 0];
        $db_member_address::update($update_default_data, $map_default);
    }

    // 4. 准备插入数据
    $insert_data = [
        'guid' => create_guid(),
        'bid' => $bid,
        'member_guid' => $member_guid,
        'province_id' => $address_data['province_id'],
        'city_id' => $address_data['city_id'],
        'area_id' => $address_data['area_id'],
        'address' => $address_data['address'],
        'true_name' => $address_data['true_name'],
        'mobile' => $address_data['mobile'],
        'is_default' => $address_data['is_default'],
        'create_time' => format_timestamp(),
    ];

    // 5. 执行插入操作
    $result = $db_member_address->save($insert_data);

    return $result;
}
```

#### 11.3.6 批量操作规范

```php
// ✅ 批量插入规范
public function batchCreateOrderItems($bid, $order_guid, $goods_list)
{
    $db_goods_order_item = new GoodsOrderItemModel();
    $batch_data = [];

    foreach ($goods_list as $goods) {
        $item_data = [
            'guid' => create_guid(),
            'bid' => $bid,
            'order_guid' => $order_guid,
            'goods_guid' => $goods['guid'],
            'goods_name' => $goods['name'],
            'amount' => $goods['amount'],
            'goods_price' => $goods['price'],
            'create_time' => format_timestamp(),
        ];
        $batch_data[] = $item_data;
    }

    // 批量插入
    $result = $db_goods_order_item->saveAll($batch_data);
    return $result;
}

// ✅ 批量更新规范
public function batchUpdateOrderStatus($bid, $order_guids, $status)
{
    $db_goods_order = new GoodsOrderModel();

    $map_batch = [
        ['bid', '=', $bid],
        ['guid', 'IN', $order_guids],
        ['status', '<>', $status],
    ];

    $update_batch_data = [
        'status' => $status,
        'update_time' => format_timestamp(),
        'update_user_id' => $this->get_user_id_from_request(),
    ];

    $result = $db_goods_order::update($update_batch_data, $map_batch);
    return $result;
}
```

#### 11.3.7 变量命名最佳实践

```php
// ✅ 推荐的变量命名模式

// 模型实例：$db_ + 表名（下划线命名）
$db_goods_order = new GoodsOrderModel();
$db_member_address = new MemberAddressModel();
$db_coupon_send_note = new CouponSendNoteModel();

// 查询条件：$map 或 $map_ + 业务含义
$map = [['bid', '=', $bid]];
$map_goods_order = [['bid', '=', $bid], ['status', '=', 1]];
$map_member_address = [['member_guid', '=', $member_guid]];

// 更新数据：$update_data 或具体业务含义
$update_data = ['status' => 1];
$member_update_data = ['name' => $name];
$order_update_data = ['status' => 2];

// 插入数据：$data 或 $insert_data
$data = ['guid' => create_guid(), 'bid' => $bid];
$insert_data = ['bid' => $bid, 'name' => $name];

// 批量数据：$batch_data
$batch_data = [];
$batch_insert_data = [];
$batch_update_data = [];
```

### 11.4 模型开发规范

#### 11.4.1 模型基础结构

```php
<?php
declare(strict_types=1);

namespace app\model;

class GoodsOrder extends ModelBase
{
    // ✅ 正确：只有当表有guid字段时才定义主键
    protected $pk = 'guid';

    // ❌ 错误：不要定义字段类型转换
    // protected $type = [
    //     'status'            => 'integer',
    //     'goods_info'        => 'json',
    //     'remark_image_list' => 'json',
    //     'goods_item_guid'   => 'json',
    // ];

    // ❌ 错误：不要定义只读字段
    // protected $readonly = ['create_time', 'update_time'];

    // ❌ 错误：不要使用SoftDelete trait
    // use SoftDelete;

    // ✅ 正确：JSON字段关联数组可以定义
    protected $jsonAssoc = true;

    // ✅ 正确：自动时间戳由框架处理
    protected $autoWriteTimestamp = false;
}
```

#### 11.4.2 模型属性定义规范

```php
// ✅ 正确：有guid字段的表
class Member extends ModelBase
{
    protected $pk = 'guid';  // 定义主键
}

// ✅ 正确：只有id字段的表
class Config extends ModelBase
{
    // 不定义$pk，使用默认的id主键
}

// ❌ 错误：不要定义这些属性
class BadExample extends ModelBase
{
    protected $type = ['status' => 'integer'];     // 不要定义
    protected $readonly = ['create_time'];          // 不要定义
    protected $deleteTime = 'delete_time';         // 不要使用软删除
}
```

#### 11.4.3 访问器（Accessor）规范

```php
// ✅ 正确：访问器命名和实现
public function getStatusTextAttr($value, $data)
{
    $status = [
        -3 => '已退款',
        -2 => '已取消',
        -1 => '待付款',
        0  => '待发货',
        1  => '待收货',
        2  => '已完成',
    ];
    return $status[$data['status']] ?? '未知状态';
}

public function getGoodsInfoTextAttr($value, $data)
{
    $goods_info = json_decode($data['goods_info'], true);
    return $this->get_goods_list_text($goods_info);
}

// 访问器命名规则：get + 字段名(驼峰) + Attr
// 自动追加：在模型查询时使用 ->append(['status_text', 'goods_info_text'])
```

#### 11.4.4 复杂查询构建方法

```php
// ✅ 推荐：将复杂查询封装为模型方法
public function build_query($bid = null, $user_guid = null, $params = [])
{
    $bid = $bid ?: $this->get_bid_from_request();
    $user_guid = $user_guid ?: $this->get_user_guid_from_request();

    // 定义关联表
    $join = [
        ['member m', 'go.member_guid = m.guid and go.bid = m.bid', 'LEFT'],
        ['coupon c', 'go.bid = c.bid and go.coupon_guid = c.guid', 'LEFT'],
        ['express e', 'go.express_code = e.code', 'LEFT'],
        ['area a1', 'go.province_id = a1.id', 'LEFT'],
        ['area a2', 'go.city_id = a2.id', 'LEFT'],
        ['area a3', 'go.area_id = a3.id', 'LEFT'],
    ];

    // 定义查询字段
    $field = [
        'go.*',
        'c.name' => 'coupon_name',
        'e.name' => 'express_name',
        'm.name' => 'member_name',
        'a1.name' => 'province_name',
        'a2.name' => 'city_name',
        'a3.name' => 'area_name',
        "CONCAT(a1.name,a2.name,a3.name,go.address)" => 'address_info',
    ];

    // 定义基础查询条件
    $map = [
        ['go.bid', '=', $bid],
        ['go.delete_time', 'NULL', NULL],
        ['go.status', 'NOT IN', [-1, -2]], // 不展示已取消订单
    ];

    // 权限控制
    $db_user = new User();
    $map[] = ['go.owner_user_id', 'IN', $db_user->getChildUserIdArray($bid, $user_guid)];

    // 构建查询模型
    $db_goods_order = new GoodsOrderModel();
    $model = $db_goods_order->alias('go')
        ->where($map)
        ->join($join)
        ->field($field)
        ->order(['go.id' => 'DESC'])
        ->append(['status_text', 'goods_info_text']);

    return ['params' => $params, 'model' => $model];
}
```

#### 11.4.5 业务逻辑封装规范

```php
// ✅ 推荐：将业务逻辑封装在模型方法中
public function send_out_goods(string $bid, string $order_guid, array $express_data, int $send_type)
{
    // 1. 参数处理和验证
    $express_code = $express_data['express_code'] ?? null;
    $express_no = $this->filter_express_no($express_data['express_no'] ?? '');
    $user_id = $this->get_user_id_from_request();
    $user_guid = $this->get_user_guid_from_request();

    // 2. 查询订单信息
    $db_goods_order = new GoodsOrderModel();
    $map = [
        ['bid', '=', $bid],
        ['guid', '=', $order_guid],
    ];
    $order_info = $db_goods_order->where($map)->findOrEmpty();

    if ($order_info->isEmpty()) {
        $this->error = '订单不存在';
        return false;
    }

    // 3. 业务逻辑验证
    if ($order_info['status'] != 0) {
        $this->error = '订单不是待发货状态';
        return false;
    }

    // 4. 准备更新数据
    $update_data = [
        'status' => 1,
        'express_code' => $express_code,
        'express_no' => $express_no,
        'send_or_pick_up_user_id' => $user_id,
        'send_or_pick_up_user_guid' => $user_guid,
        'send_or_pick_up_time' => format_timestamp(),
        'send_type' => $send_type,
    ];

    // 5. 执行更新
    $map[] = ['status', '=', 0]; // 乐观锁
    $result = $db_goods_order::update($update_data, $map);

    // 6. 后续处理（异步）
    if ($result) {
        $order_data = ['bid' => $bid, 'guid' => $order_guid];
        job()->set_job_name('GoodsOrder@after_send_out_goods')->push_job(['order_data' => $order_data]);
    }

    return $result;
}
```

#### 11.4.6 权限检查方法规范

```php
// ✅ 推荐：权限检查方法的标准实现
protected function has_auth_cost_price($bid = null, $user_guid = null)
{
    $bid = $bid ?: $this->get_bid_from_request();
    $user_guid = $user_guid ?: $this->get_user_guid_from_request();

    $db_rule = new Rule();
    return $db_rule->check_rule($bid, $user_guid, Rule::COST_PRICE);
}

protected function has_auth_multiple_express_send_out_goods($bid = null, $user_guid = null)
{
    $bid = $bid ?: $this->get_bid_from_request();
    $user_guid = $user_guid ?: $this->get_user_guid_from_request();

    $db_rule = new Rule();
    return $db_rule->check_rule($bid, $user_guid, Rule::MULTIPLE_EXPRESS_SEND_OUT_GOODS);
}

// 权限检查方法命名规则：has_auth_ + 权限功能名称
```

#### 11.4.7 数据导出方法规范

```php
// ✅ 推荐：数据导出方法的标准实现
public function export_data($data, $bid = null, $user_guid = null)
{
    $bid = $bid ?: $this->get_bid_from_request();
    $user_guid = $user_guid ?: $this->get_user_guid_from_request();

    // 基础表头定义
    $header = [
        'bill_number' => '订单号',
        'status_text' => '状态',
        'goods_info_text' => '商品信息',
        'create_time' => '下单时间',
        'express_name' => '快递公司',
        'express_no' => '快递单号',
    ];

    // 根据权限动态添加字段
    if ($this->has_auth_multiple_express_send_out_goods($bid, $user_guid)) {
        $header['express_name_2'] = '快递公司2';
        $header['express_no_2'] = '快递单号2';
    }

    // 处理扩展字段
    $header = $this->append_extend_field($bid, $header);

    // 动态添加有数据的字段
    $optional_fields = [
        'mobile' => '手机号',
        'true_name' => '姓名',
        'address_info' => '地址',
        'total_money' => '订单金额',
        'paid_money' => '储值支付',
    ];

    foreach ($optional_fields as $key => $value) {
        foreach ($data as $item) {
            if (!tools()::is_empty_or_zero($item[$key])) {
                $header[$key] = $value;
                break;
            }
        }
    }

    // 生成Excel文件
    $file = new Excel();
    return $file->arrayToExcel($header, $data, '商品订单_' . date('Y-m-d-H-i-s'));
}
```

### 11.5 队列任务规范

#### 11.5.1 队列目录结构

```
app/queue/controller/
├── BasicQueue.php          # 队列基类
├── Test.php               # 测试队列
├── Monitor.php            # 监控队列
├── GoodsOrder.php         # 订单处理队列
├── Pay.php                # 支付处理队列
├── Notify.php             # 通知队列
├── Sms.php                # 短信队列
├── Email.php              # 邮件队列
├── Weixin.php             # 微信相关队列
├── Crontab.php            # 定时任务队列
└── ...                    # 其他业务队列
```

#### 11.5.2 队列类基础结构

```php
<?php
declare(strict_types=1);

namespace app\queue\controller;

use think\queue\Job;
use Exception;

class Monitor extends BasicQueue
{
    /**
     * 商户到期提醒
     * @param Job $job 任务对象
     * @param string $data 任务数据
     * @return bool
     * @throws Exception
     */
    public function expire_notify(Job $job, $data)
    {
        // 1. 获取任务执行次数
        $attempts = $job->attempts();

        // 2. 解析任务数据
        $job_data = json_decode($data, true);

        try {
            // 3. 执行业务逻辑
            $db_business = new Business();
            $map = [['license_status', '=', 2]];
            $business_list = $db_business->where($map)
                ->whereRaw("TO_DAYS(expired_time)-TO_DAYS(NOW()) IN (1,2,3,7,15,30)")
                ->select();

            foreach ($business_list as $business) {
                $data = [
                    'title' => '【重要】账号即将到期提醒',
                    'name' => '【重要】账号' . $business['expired_day'] . '天后即将到期提醒',
                    'user' => $business['business_name'] . '(' . $business['account'] . ')',
                    'detail' => '点击咨询续费',
                ];

                // 发送通知
                notify()->set_key_name(NotifyService::Notice)
                    ->set_data($data)
                    ->set_bid($business['guid'])
                    ->send();
            }

            // 4. 任务执行成功，删除任务
            $job->delete();
            return true;

        } catch (Exception $e) {
            // 5. 任务执行失败处理
            wr_log('商户到期提醒失败：' . $e->getMessage());

            if ($attempts < 3) {
                // 重试机制：延迟重新执行
                $job->release(60); // 60秒后重试
            } else {
                // 超过最大重试次数，删除任务
                $job->delete();
            }

            throw $e;
        }
    }
}
```

#### 11.5.3 队列任务调用规范

```php
// ✅ 推荐：标准队列任务调用
public function send_expire_notify()
{
    // 1. 准备任务数据
    $job_data = [
        'type' => 'expire_notify',
        'create_time' => format_timestamp(),
        'operator' => $this->get_user_guid(),
    ];

    // 2. 推送队列任务
    $result = job()
        ->set_job_name('Monitor@expire_notify')
        ->push_job($job_data);

    if ($result) {
        success('到期提醒任务已加入队列');
    } else {
        error('任务推送失败');
    }
}

// ✅ 延迟执行队列任务
public function delayed_task()
{
    $job_data = ['message' => '这是一个延迟任务'];

    // 延迟300秒执行
    $result = job()
        ->set_job_name('Test@test1')
        ->set_delay(300)
        ->push_job($job_data);
}

// ✅ 同步连接队列任务（立即执行）
public function sync_task()
{
    $job_data = ['urgent' => true];

    $result = job()
        ->set_sync_connections()  // 使用同步连接
        ->set_job_name('Notify@urgent_notify')
        ->push_job($job_data);
}
```

#### 11.5.4 队列任务重试机制

```php
public function process_with_retry(Job $job, $data)
{
    $attempts = $job->attempts();
    $max_attempts = 5;

    try {
        // 业务逻辑处理
        $result = $this->process_business_logic($data);

        // 成功时删除任务
        $job->delete();
        return true;

    } catch (Exception $e) {
        wr_log("任务执行失败，第{$attempts}次尝试：" . $e->getMessage());

        if ($attempts < $max_attempts) {
            // 计算延迟时间：指数退避算法
            $delay = min(300, pow(2, $attempts) * 10); // 最大延迟5分钟
            $job->release($delay);
        } else {
            // 超过最大重试次数，记录错误并删除任务
            wr_log("任务最终失败，已达到最大重试次数：" . $e->getMessage(), 1);
            $job->delete();
        }

        throw $e;
    }
}
```

#### 11.5.5 队列任务数据处理

```php
public function process_data(Job $job, $data)
{
    // 1. 数据解析和验证
    $job_data = json_decode($data, true);
    if (!$job_data || !isset($job_data['required_field'])) {
        wr_log('队列任务数据格式错误：' . $data);
        $job->delete();
        return false;
    }

    // 2. 提取必要参数
    $bid = $job_data['bid'] ?? null;
    $user_guid = $job_data['user_guid'] ?? null;
    $action_type = $job_data['action_type'] ?? 'default';

    // 3. 参数验证
    if (!$bid) {
        wr_log('队列任务缺少必要参数bid');
        $job->delete();
        return false;
    }

    // 4. 执行业务逻辑
    switch ($action_type) {
        case 'send_notification':
            $this->send_notification($bid, $job_data);
            break;
        case 'update_status':
            $this->update_status($bid, $job_data);
            break;
        default:
            wr_log('未知的任务类型：' . $action_type);
            break;
    }

    $job->delete();
    return true;
}
```

#### 11.5.6 队列任务监控和日志

```php
public function monitored_task(Job $job, $data)
{
    $start_time = microtime(true);
    $task_id = uniqid('task_');

    // 记录任务开始
    wr_log("队列任务开始执行 - 任务ID: {$task_id}, 数据: {$data}");

    try {
        // 执行业务逻辑
        $result = $this->execute_business_logic($data);

        // 计算执行时间
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);

        // 记录成功日志
        wr_log("队列任务执行成功 - 任务ID: {$task_id}, 耗时: {$execution_time}ms");

        $job->delete();
        return true;

    } catch (Exception $e) {
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);

        // 记录失败日志
        wr_log("队列任务执行失败 - 任务ID: {$task_id}, 耗时: {$execution_time}ms, 错误: " . $e->getMessage());

        throw $e;
    }
}
```

#### 11.5.7 队列任务最佳实践

```php
// ✅ 推荐的队列任务结构
class GoodsOrder extends BasicQueue
{
    /**
     * 订单发货后处理
     */
    public function after_send_out_goods(Job $job, $data)
    {
        $attempts = $job->attempts();
        $job_data = json_decode($data, true);

        try {
            $order_data = $job_data['order_data'];
            $bid = $order_data['bid'];
            $order_guid = $order_data['guid'];

            // 1. 发送发货通知
            $this->send_delivery_notification($bid, $order_guid);

            // 2. 更新库存
            $this->update_goods_stock($bid, $order_guid);

            // 3. 记录物流信息
            $this->record_logistics_info($bid, $order_guid);

            // 4. 推送相关队列任务
            $this->push_related_tasks($bid, $order_guid);

            $job->delete();
            return true;

        } catch (Exception $e) {
            wr_log('订单发货后处理失败：' . $e->getMessage());

            if ($attempts < 3) {
                $job->release(120); // 2分钟后重试
            } else {
                $job->delete();
            }

            throw $e;
        }
    }

    /**
     * 推送相关任务
     */
    private function push_related_tasks($bid, $order_guid)
    {
        // 推送积分奖励任务
        $point_data = ['bid' => $bid, 'order_guid' => $order_guid];
        job()->set_job_name('MemberPoint@add_order_points')->push_job($point_data);

        // 推送统计任务
        $stat_data = ['bid' => $bid, 'type' => 'order_delivery'];
        job()->set_job_name('StatisticTask@update_delivery_stats')->push_job($stat_data);
    }
}
```

### 11.6 代码生成规范

#### 11.6.1 后台功能模块代码生成

**重要说明：开发后台功能模块时，使用内置命令自动生成代码，确保代码结构的一致性。**

```bash
# 标准代码生成命令
php think curd -t 表名 -l 模块名

# 示例：生成会员管理模块
php think curd -t member -l admin_api

# 示例：生成商品管理模块
php think curd -t goods -l admin_api

# 示例：生成订单管理模块
php think curd -t goods_order -l admin_api
```

#### 11.6.2 生成文件规范

代码生成器会自动创建以下文件：

```
生成的文件结构：
├── app/controller/admin_api/v1/Member.php     # 控制器文件
├── app/model/Member.php                       # 模型文件
├── app/validate/admin_api/v1/Member.php       # 验证器文件
└── app/view/admin/member/                     # 视图文件目录
    ├── index.html                             # 列表页面
    ├── detail.html                            # 详情/编辑页面
```

#### 11.6.3 模板文件路径

代码生成器使用以下模板文件：

```
模板文件位置：
├── app/command/Curd/tpl/controller.tpl        # 控制器模板
├── app/command/Curd/tpl/model.tpl             # 模型模板
├── app/command/Curd/tpl/validate.tpl          # 验证器模板
├── app/command/Curd/tpl/index.tpl             # 列表页面模板
├── app/command/Curd/tpl/detail.tpl            # 详情页面模板
```

#### 11.6.4 自定义模板规范

```php
// ✅ 模板文件必须遵循的规范

// 1. 控制器模板规范
class {%ClassName%} extends BasicAdminApi
{
    /**
     * 列表
     */
    public function index()
    {
        $db = new {%ClassName%}Model();
        $map = [['bid', '=', $this->get_bid()]];
        $this->model = $db->where($map)->order(['create_time' => 'DESC']);
        result($this->_list());
    }
}

// 2. 模型模板规范
class {%ClassName%} extends ModelBase
{
    protected $pk = 'guid';  // 只有当表有guid字段时才包含
}

// 3. 验证器模板规范
class {%ClassName%} extends ValidateBase
{
    protected $rule = [
        'name|名称' => ['require', 'max:50'],
    ];

    protected $scene = [
        'add'  => ['name'],
        'edit' => ['name'],
    ];
}
```

#### 11.6.5 生成后的代码调整

```php
// ✅ 生成代码后需要手动调整的部分

// 1. 根据实际业务调整验证规则
protected $rule = [
    'name|名称'     => ['require', 'max:50'],
    'mobile|手机号' => ['mobile'],
    'status|状态'   => ['in:0,1'],
];

// 2. 根据实际需求调整查询条件
$map = [
    ['bid', '=', $this->get_bid()],
    ['delete_time', 'NULL', NULL],  // 软删除条件
    ['status', '=', 1],             // 业务条件
];

// 3. 根据实际字段调整视图模板
// 在生成的HTML文件中调整表格列定义和表单字段
```

### 11.7 数据库表结构规范

#### 11.7.1 新增表的标准字段要求

**重要规范：所有新建的数据表都必须包含以下标准字段。**

```sql
-- ✅ 标准表结构模板
CREATE TABLE `table_name`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`        char(36)     NOT NULL COMMENT '全局唯一标识',
    `bid`         char(36)     NOT NULL COMMENT '商户ID',

    -- 业务字段
    `name`        varchar(255) NOT NULL COMMENT '名称',
    `status`      tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',

    -- 标准时间字段
    `create_time` datetime(3) NOT NULL COMMENT '创建时间',
    `update_time` datetime(3) NOT NULL COMMENT '更新时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`),
    KEY           `idx_bid` (`bid`),
    KEY           `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表注释';
```

#### 11.7.2 必需字段说明

```sql
-- 必需字段详细说明

-- 1. 自增主键（必需）
`id`
bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',

-- 2. 全局唯一标识（必需）
`guid` char(36) NOT NULL COMMENT '全局唯一标识',

-- 3. 商户ID（必需，多商户系统）
`bid` char(36) NOT NULL COMMENT '商户ID',

-- 4. 创建时间（必需，精确到毫秒）
`create_time` datetime(3) NOT NULL COMMENT '创建时间',

-- 5. 更新时间（必需，精确到毫秒）
`update_time` datetime(3) NOT NULL COMMENT '更新时间',
```

#### 11.7.3 索引规范

```sql
-- ✅ 标准索引配置

-- 主键索引（必需）
PRIMARY KEY (`id`),

-- GUID唯一索引（必需）
UNIQUE KEY `uk_guid` (`guid`),

-- 商户ID索引（必需）
KEY `idx_bid` (`bid`),

-- 创建时间索引（推荐）
KEY `idx_create_time` (`create_time`),

-- 业务字段索引（根据查询需求）
KEY `idx_status` (`bid`, `status`),
KEY `idx_name` (`bid`, `name`),
```

#### 11.7.4 表创建示例

```sql
-- ✅ 完整的表创建示例
CREATE TABLE `member_level`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `guid`          char(36)      NOT NULL COMMENT '全局唯一标识',
    `bid`           char(36)      NOT NULL COMMENT '商户ID',
    `name`          varchar(100)  NOT NULL COMMENT '等级名称',
    `min_points`    int(11) NOT NULL DEFAULT '0' COMMENT '最低积分',
    `max_points`    int(11) NOT NULL DEFAULT '0' COMMENT '最高积分',
    `discount_rate` decimal(3, 2) NOT NULL DEFAULT '1.00' COMMENT '折扣率',
    `status`        tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
    `sort_order`    int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `create_time`   datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`   datetime(3) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_guid` (`guid`),
    KEY             `idx_bid` (`bid`),
    KEY             `idx_bid_status` (`bid`, `status`),
    KEY             `idx_points` (`bid`, `min_points`, `max_points`),
    KEY             `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员等级表';
```

### 11.8 静态资源管理规范

#### 11.8.1 静态资源目录结构

**重要说明：项目静态资源统一存放在 `public/static` 目录下，按资源类型和功能模块进行分类管理。**

```
public/static/
├── css/                    # 样式文件
│   ├── admin/             # 后台管理样式
│   ├── member/            # 会员端样式
│   │   ├── code/          # 卡券模块样式
│   │   ├── coupon_book/   # 优惠券模块样式
│   │   ├── goods/         # 商品模块样式
│   │   └── user/          # 用户模块样式
│   ├── themes/            # 主题样式
│   │   ├── default.css    # 默认主题
│   │   ├── blue.css       # 蓝色主题
│   │   └── ...            # 其他主题
│   ├── plugins/           # 插件样式
│   ├── fonts/             # 字体文件
│   ├── common.css         # 公共样式
│   ├── admin.css          # 后台通用样式
│   └── weui.css           # WeUI框架样式
├── js/                     # JavaScript文件
│   ├── admin/             # 后台管理脚本
│   ├── member/            # 会员端脚本
│   │   ├── code/          # 卡券模块脚本
│   │   ├── goods/         # 商品模块脚本
│   │   └── common.js      # 会员端公共脚本
│   ├── plugins/           # 第三方插件
│   │   ├── layui-2.5.7/   # LayUI框架
│   │   ├── bootstrap/     # Bootstrap框架
│   │   ├── kindeditor/    # 富文本编辑器
│   │   └── ...            # 其他插件
│   ├── function.js        # 核心功能脚本
│   ├── jquery.min.js      # jQuery库
│   └── vue.min.js         # Vue.js库
├── img/                    # 图片资源
│   ├── admin/             # 后台管理图片
│   ├── member/            # 会员端图片
│   ├── ai/                # AI功能图片
│   ├── app_icon/          # 应用图标
│   ├── loading.gif        # 加载动画
│   ├── default_head.png   # 默认头像
│   └── ...                # 其他图片
├── audio/                  # 音频文件
│   ├── message.mp3        # 消息提示音
│   ├── order.wav          # 订单提示音
│   └── default.wav        # 默认提示音
└── 专用模块目录/            # 特定功能模块
    ├── chat/              # 聊天功能资源
    ├── haoping/           # 好评功能资源
    ├── wemall/            # 微商城资源
    ├── weui_mall/         # WeUI商城资源
    └── layim/             # 即时通讯资源
```

#### 11.8.2 文件命名规范

```
✅ 正确的文件命名：
- web-storage-cache.js         # 使用连字符分隔
- jquery.min.js               # 库名.版本.扩展名
- font-awesome.css            # 功能描述-具体名称
- login-background.jpg        # 功能-用途描述
- member_center.png           # 使用下划线分隔
- wechat_login_icon.png       # 平台_功能_类型

❌ 错误的文件命名：
- webStorageCache.js          # 避免驼峰命名
- jquery_min.js              # 不要混用分隔符
- fontAwesome.css             # 避免驼峰命名
- loginBg.jpg                 # 缩写不清晰
```

#### 11.8.3 资源引用规范

```html
<!-- ✅ 正确的资源引用方式 -->

<!-- 1. 基础框架资源（按加载顺序） -->
<link href="/static/css/common.css" rel="stylesheet"/>
<script src="/static/js/jquery.min.js"></script>
<script src="/static/js/function.js"></script>

<!-- 2. UI框架资源 -->
<link href="/static/js/plugins/layui-2.5.7/css/layui.css" rel="stylesheet"/>
<script src="/static/js/plugins/layui-2.5.7/layui.js"></script>

<!-- 3. 模块化资源 -->
<link href="/static/css/admin/common.css" rel="stylesheet"/>
<script src="/static/js/admin/common.js"></script>

<!-- 4. 页面专用资源 -->
<link href="/static/css/member/goods/list.css" rel="stylesheet"/>
<script src="/static/js/member/goods/list.js"></script>

<!-- 5. 第三方插件资源 -->
<script src="/static/js/plugins/kindeditor/kindeditor-all.js"></script>
<script src="/static/js/plugins/echarts/echarts.min.js"></script>
```

#### 11.8.4 版本管理规范

```
文件版本管理：
├── jquery.js              # 开发版本（调试用）
├── jquery.min.js          # 压缩版本（生产用）
├── jquery.min.map         # 源码映射文件
├── layui-2.5.7/           # 版本号目录
├── layui-pro-2.4.3/       # 专业版本目录
└── vue.global.prod.js     # 生产环境版本

版本选择规则：
- 开发环境：使用未压缩版本便于调试
- 生产环境：使用压缩版本提高性能
- 版本升级：保留旧版本目录，新建版本目录
```

#### 11.8.5 业务功能资源分类

```javascript
// ✅ 按业务功能分类的资源

// 1. 支付相关资源
/static/;
js /
pay.js / // 通用支付处理
static /
js /
alipay.js / // 支付宝支付
static /
js /
wechatpay.js / // 微信支付
static /
js /
alipayjsapi.min.js / // 支付宝JSAPI
// 2. 聊天客服资源
static /
js /
chatCore.js / // 聊天核心功能
static /
js /
chatMember.js / // 会员端聊天
static /
js /
chatUser.js / // 用户端聊天
static /
js /
kefu -
sdk.js / // 客服SDK
static /
js /
s;
ervice -
sdk.js / // 服务SDK
// 3. 图片处理资源
static /
js /
lazyload.js / // 图片懒加载
static /
js /
watermask.js / // 图片水印
static /
js /
fly_zomm_img.js / // 图片缩放
// 4. 数据处理资源
static /
js /
web -
storage -
cache.js / // 本地存储缓存
static /
js /
template -
web.js / // 模板引擎
static /
js /
jquery.md5.js / // MD5加密
// 5. 打印相关资源
static /
js /
LodopFuncs.js / // Lodop打印控件
static /
js /
jQuery.print.js / // jQuery打印插件
static /
js /
SCPPrint.js; // 小票打印
```

#### 11.8.6 多端适配资源

```html
<!-- ✅ 不同端的资源适配 -->

<!-- PC端后台管理 -->
<link href="/static/js/plugins/layui-2.5.7/css/layui.css"/>
<script src="/static/js/plugins/layui-2.5.7/layui.js"></script>

<!-- 移动端WeUI -->
<link href="/static/css/weui.min.css"/>
<script src="/static/js/weui.min.js"></script>

<!-- 移动端YDUI -->
<link href="/static/ydui/css/ydui.css"/>
<script src="/static/ydui/js/ydui.js"></script>

<!-- 微信小程序相关 -->
<script src="/static/js/wechatupay.js"></script>
<script src="/static/jssdk/js/jweixin.js"></script>

<!-- 响应式适配 -->
<script src="/static/js/responsively-lazy.min.js"></script>
```

#### 11.8.7 资源优化规范

```html
<!-- ✅ 资源加载优化 -->

<!-- 1. 关键资源预加载 -->
<link rel="preload" href="/static/css/common.css" as="style"/>
<link rel="preload" href="/static/js/jquery.min.js" as="script"/>

<!-- 2. 非关键资源延迟加载 -->
<script>
  // 延迟加载非关键插件
  setTimeout(function () {
    loadScript("/static/js/plugins/echarts/echarts.min.js");
  }, 1000);
</script>

<!-- 3. 图片懒加载 -->
<img data-src="/static/img/banner.jpg" class="lazyload"/>
<script src="/static/js/lazyload.min.js"></script>

<!-- 4. 资源合并（生产环境） -->
<!-- 将多个CSS文件合并为一个 -->
<link href="/static/css/combined.min.css"/>
<!-- 将多个JS文件合并为一个 -->
<script src="/static/js/combined.min.js"></script>
```

#### 11.8.8 CDN 和缓存策略

```html
<!-- ✅ CDN资源引用 -->

暂时用不到
```

#### 11.8.9 专用模块资源管理

```
专用功能模块资源组织：

1. 聊天功能模块 (/static/chat/)
├── css/                    # 聊天样式
├── js/                     # 聊天脚本
├── img/                    # 聊天图标
└── swf/                    # Flash文件

2. 好评功能模块 (/static/haoping/)
├── base.css               # 基础样式
├── bonus_evaluate.css     # 评价样式
├── bonus_evaluate.js      # 评价脚本
├── img/                   # 相关图片
└── need/                  # 依赖文件

3. 微商城模块 (/static/wemall/)
├── images/                # 商城图片
└── notice.mp3             # 通知音频

4. WeUI商城模块 (/static/weui_mall/)
├── css/                   # WeUI商城样式
├── js/                    # WeUI商城脚本
├── lib/                   # 依赖库
├── images/                # 商城图片
├── upload/                # 上传目录
└── *.html                 # 静态页面模板
```

#### 11.8.10 图片资源管理规范

```
图片资源分类和命名：

1. 功能图标类
├── add.png                # 添加图标
├── minus.png              # 减少图标
├── search-btn.png         # 搜索按钮
├── close.png              # 关闭图标
└── arrow.png              # 箭头图标

2. 状态图片类
├── loading.gif            # 加载动画
├── success.png            # 成功图标
├── empty.png              # 空状态图
├── no_img.jpg             # 默认图片
└── default_head.png       # 默认头像

3. 背景图片类
├── bg.jpg                 # 通用背景
├── login-background.jpg   # 登录背景
├── mybg.png               # 个人中心背景
└── bg_load.jpg            # 加载背景

4. 业务图片类
├── wechat.png             # 微信图标
├── alipay.png             # 支付宝图标
├── qrcode.png             # 二维码图标
├── cart.png               # 购物车图标
└── order.png              # 订单图标

5. 品牌图片类
├── yikayi.png             # 项目Logo
├── taoci_logo.png         # 品牌Logo
└── logo_paimai.png        # 拍卖Logo
```

#### 11.8.11 音频资源管理规范

```
音频文件使用规范：

1. 消息提示音 (/static/audio/)
├── message.mp3            # 消息提示音
├── order.wav              # 订单提示音
├── default.wav            # 默认提示音
└── notice.mp3             # 通知提示音

2. 音频格式选择
- .mp3: 通用格式，兼容性好
- .wav: 高质量格式，文件较大
- .ogg: 开源格式，部分浏览器支持

3. 音频调用示例
// JavaScript调用音频
function playNotificationSound() {
    var audio = new Audio('/static/audio/message.mp3');
    audio.play().catch(function(error) {
        console.log('音频播放失败:', error);
    });
}

// HTML5音频标签
<audio id="orderSound" preload="auto">
    <source src="/static/audio/order.wav" type="audio/wav">
    <source src="/static/audio/order.mp3" type="audio/mpeg">
</audio>
```

#### 11.8.12 字体资源管理规范

```
字体文件管理 (/static/css/fonts/)：

1. 图标字体
├── fontawesome-webfont.woff2    # FontAwesome字体
├── fontawesome-webfont.woff     # FontAwesome字体
├── fontawesome-webfont.ttf      # FontAwesome字体
└── fontawesome-webfont.eot      # FontAwesome字体

2. 中文字体
├── MSYH.TTC                     # 微软雅黑
├── SONGTI.TTF                   # 宋体
├── KAISHU.ttf                   # 楷书
├── LISHU.ttf                    # 隶书
└── XINGSHU.TTF                  # 行书

3. 英文字体
├── OpenSans-light.woff          # Open Sans字体
└── summernote.woff              # Summernote编辑器字体

4. 字体引用示例
@font-face {
    font-family: 'FontAwesome';
    src: url('fonts/fontawesome-webfont.eot');
    src: url('fonts/fontawesome-webfont.eot?#iefix') format('embedded-opentype'),
         url('fonts/fontawesome-webfont.woff2') format('woff2'),
         url('fonts/fontawesome-webfont.woff') format('woff'),
         url('fonts/fontawesome-webfont.ttf') format('truetype');
}
```

#### 11.8.13 资源加载最佳实践

```javascript
// ✅ 动态加载资源的最佳实践

// 1. 动态加载CSS
function loadCSS(href) {
  var link = document.createElement("link");
  link.rel = "stylesheet";
  link.href = href;
  document.head.appendChild(link);
}

// 2. 动态加载JavaScript
function loadScript(src, callback) {
  var script = document.createElement("script");
  script.src = src;
  script.onload = callback;
  document.head.appendChild(script);
}

// 3. 条件加载资源
if (isMobile()) {
  loadCSS("/static/css/mobile.css");
  loadScript("/static/js/mobile.js");
} else {
  loadCSS("/static/css/desktop.css");
  loadScript("/static/js/desktop.js");
}

// 4. 模块化加载
function loadModule(moduleName) {
  var basePath = "/static/js/member/" + moduleName + "/";
  loadCSS("/static/css/member/" + moduleName + "/style.css");
  loadScript(basePath + "main.js", function () {
    console.log(moduleName + " 模块加载完成");
  });
}

// 5. 资源预加载
function preloadResources() {
  var resources = ["/static/img/loading.gif", "/static/audio/message.mp3", "/static/css/themes/blue.css"];

  resources.forEach(function (url) {
    var link = document.createElement("link");
    link.rel = "prefetch";
    link.href = url;
    document.head.appendChild(link);
  });
}
```

### 11.9 事务处理规范

```php
Db::startTrans();
try {
    // 数据库操作
    $member->save($data);
    $log->save($log_data);

    Db::commit();
    success('操作成功');
} catch (\Exception $e) {
    Db::rollback();
    error('操作失败：' . $e->getMessage());
}
```

### 11.3 分页处理规范

```php
// 控制器中的分页
protected function _list()
{
    return $this->_paginate($this->model);
}

// 自定义分页
$page_size = $this->params['page_size'] ?? 20;
$result = $this->model->paginate($page_size);
```

## 14. 缓存使用规范

### 12.1 缓存键命名规范

```php
// 格式：模块:功能:标识
$cache_key = "member:info:{$member_guid}";
$cache_key = "business:config:{$bid}";
$cache_key = "system:config:global";
```

### 12.2 缓存操作规范

```php
// 获取缓存
$data = cache($cache_key);
if (!$data) {
    $data = $this->get_data_from_db();
    cache($cache_key, $data, 3600); // 缓存1小时
}

// 删除缓存
cache($cache_key, null);

// 标签缓存
cache($cache_key, $data, 3600, 'member');
cache()->tag('member')->clear(); // 清除标签缓存
```

## 15. 文件上传规范

### 13.1 文件上传处理

```php
// 控制器中处理文件上传
public function upload()
{
    $upload = Storage::driver($this->get_storage_type());
    $file = $upload->upload();

    if ($file) {
        // 记录上传日志
        $upload_note = new FileUploadNote();
        $upload_note->save([
            'bid' => $this->get_bid(),
            'file_url' => $file['url'],
            'file_size' => $file['size'],
            'create_time' => time(),
        ]);

        result($file);
    } else {
        error('上传失败');
    }
}
```

### 13.2 文件存储配置

```php
// 获取存储类型
private function get_storage_type()
{
    $config = get_config_by_bid($this->get_bid());
    return $config['storage_type'] ?? 'local';
}
```

## 16. API 接口规范

### 14.1 API 接口设计规范

**重要说明：项目中所有 API 接口统一使用 POST 请求方式**

#### 14.1.1 接口 URL 规范

```
POST   /admin_api/v1/member/index      # 获取会员列表
POST   /admin_api/v1/member/detail     # 获取会员详情
POST   /admin_api/v1/member/add        # 新增会员
POST   /admin_api/v1/member/edit       # 编辑会员
POST   /admin_api/v1/member/del        # 删除会员

POST   /member_api/v1/goods/index      # 会员端商品列表
POST   /member_api/v1/order/add        # 会员端创建订单
POST   /api/v1/common/upload           # 通用文件上传
POST   /gateway/pay_notify             # 支付回调
```

#### 14.1.2 接口命名规范

```
模块结构：/{模块名}/v{版本号}/{控制器名}/{方法名}

后台管理接口：
/admin_api/v1/{controller}/{action}
- /admin_api/v1/member/index
- /admin_api/v1/goods/add
- /admin_api/v1/order/edit

会员端接口：
/member_api/v1/{controller}/{action}
- /member_api/v1/login/submit
- /member_api/v1/goods/list
- /member_api/v1/order/create

通用接口：
/api/v1/{controller}/{action}
- /api/v1/upload/file
- /api/v1/common/config

网关接口：
/gateway/{controller}/{action}
- /gateway/pay_notify
- /gateway/wechat_event
```

#### 14.1.3 POST 请求参数规范

**所有接口都使用 POST 请求，参数通过请求体传递：**

```javascript
// ✅ 标准的POST请求格式

// 1. 列表查询接口
POST / admin_api / v1 / member / index
Content - Type
:
application / json

{
  "page"
:
  1,
    "limit"
:
  20,
    "key"
:
  "name",
    "value"
:
  "张三",
    "status"
:
  1,
    "create_time"
:
  "2024-01-01 - 2024-12-31"
}

// 2. 详情查询接口
POST / admin_api / v1 / member / detail
Content - Type
:
application / json

{
  "guid"
:
  "550e8400-e29b-41d4-a716-************"
}

// 3. 新增数据接口
POST / admin_api / v1 / member / add
Content - Type
:
application / json

{
  "name"
:
  "张三",
    "mobile"
:
  "13800138000",
    "email"
:
  "<EMAIL>",
    "status"
:
  1
}

// 4. 编辑数据接口
POST / admin_api / v1 / member / edit
Content - Type
:
application / json

{
  "guid"
:
  "550e8400-e29b-41d4-a716-************",
    "name"
:
  "李四",
    "mobile"
:
  "13900139000",
    "status"
:
  1
}

// 5. 删除数据接口
POST / admin_api / v1 / member / del
Content - Type
:
application / json

{
  "guid"
:
  "550e8400-e29b-41d4-a716-************"
}
```

#### 14.1.4 前端调用示例

```javascript
// ✅ 使用项目封装的请求方法

// 1. 后台管理API调用
post_layui_admin_api_v1(
  "/member/index",
  {
    page: 1,
    limit: 20,
    key: "name",
    value: "张三",
  },
  function (result) {
    console.log("成功:", result);
  },
  function (error) {
    console.log("失败:", error);
  }
);

// 2. 会员端API调用
post_layui_member_api_v1(
  "/goods/index",
  {
    category_id: 1,
    page: 1,
    limit: 10,
  },
  function (result) {
    console.log("商品列表:", result.data);
  }
);

// 3. 通用API调用
post_layui_api_v1(
  "/upload/file",
  {
    file: fileData,
    type: "image",
  },
  function (result) {
    console.log("上传成功:", result.data.url);
  }
);

// 4. 原生fetch调用
fetch("/admin_api/v1/member/add", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    Authorization: "Bearer " + token,
  },
  body: JSON.stringify({
    name: "张三",
    mobile: "13800138000",
  }),
})
  .then((response) => response.json())
  .then((data) => console.log(data));
```

#### 14.1.5 接口响应格式规范

```json
// ✅ 成功响应格式
{
  "code": 0,
  "msg": "success",
  "data": {
    "guid": "550e8400-e29b-41d4-a716-************",
    "name": "张三",
    "mobile": "13800138000"
  },
  "time": 1640995200
}

// ✅ 列表响应格式
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "guid": "550e8400-e29b-41d4-a716-************",
      "name": "张三"
    }
  ],
  "count": 100,
  "total": 100,
  "time": 1640995200
}

// ✅ 错误响应格式
{
  "code": -1,
  "msg": "参数验证失败：手机号格式不正确",
  "data": {},
  "time": 1640995200
}

// ✅ 操作成功响应格式
{
  "code": 0,
  "msg": "操作成功",
  "data": {},
  "time": 1640995200
}
```

### 14.2 API 响应格式

```json
// 成功响应
{
  "code": 0,
  "msg": "success",
  "data": {},
  "time": 1234567890
}

// 错误响应
{
  "code": -1,
  "msg": "error message",
  "data": {},
  "time": 1234567890
}

// 分页响应
{
  "code": 0,
  "msg": "success",
  "data": {
    "data": [],
    "total": 100,
    "per_page": 20,
    "current_page": 1,
    "last_page": 5
  },
  "time": 1234567890
}
```

### 14.3 API 版本控制

- 使用 URL 路径版本控制：`/api/v1/`、`/api/v2/`
- 控制器按版本分目录：`controller/api/v1/`、`controller/api/v2/`

## 17. 安全规范

### 15.1 权限验证

```php
// 中间件中进行权限验证
public function check_auth($bid, $user_guid, $controller, $action)
{
    $path = $controller . '/' . $action;
    $db_rule = new Rule();
    if ($db_rule->check_rule($bid, $user_guid, $path) === false) {
        error('权限不足', -1, null, 'NO_AUTH');
    }
}
```

### 15.2 参数过滤

```php
// 获取参数时自动过滤
$params = $this->request->param('', null, 'trim,htmlspecialchars');

// 验证器中进行参数验证
$this->validate($params, 'Member.add');
```

### 15.3 SQL 注入防护

```php
// 使用参数绑定
$result = Db::query('SELECT * FROM member WHERE id = ?', [$id]);

// 使用查询构造器
$result = Db::name('member')->where('id', $id)->find();
```

## 18. 性能优化规范

### 16.1 数据库优化

- 合理使用索引
- 避免 N+1 查询问题
- 使用批量操作
- 适当使用缓存

### 16.2 缓存策略

- 热点数据缓存
- 查询结果缓存
- 页面片段缓存
- 分布式缓存

### 16.3 代码优化

- 避免在循环中进行数据库查询
- 合理使用单例模式
- 减少不必要的对象创建
- 使用适当的数据结构

## 19. 第三方服务集成规范

### 17.1 服务类封装

```php
<?php
namespace app\common\service;

class WechatService
{
    protected static $instance = null;

    public static function getInstance($config)
    {
        if (is_null(self::$instance)) {
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    public function sendMessage($data)
    {
        try {
            // 调用微信API
            $result = $this->callWechatApi($data);
            return $result;
        } catch (\Exception $e) {
            wr_log('微信消息发送失败：' . $e->getMessage());
            return false;
        }
    }
}
```

### 17.2 配置管理

```php
// 获取第三方服务配置
$config = get_config_by_bid($bid);
$wechat_config = [
    'app_id' => $config['wechat_app_id'],
    'app_secret' => $config['wechat_app_secret'],
];
```

### 17.3 错误处理

```php
// 统一的第三方服务错误处理
try {
    $result = $service->call($params);
    if ($result === false) {
        error($service->getError());
    }
    result($result);
} catch (\Exception $e) {
    wr_log('第三方服务调用失败：' . $e->getMessage());
    error('服务暂时不可用，请稍后重试');
}
```

## 20. 测试规范

### 18.1 单元测试

```php
<?php
namespace tests\unit;

use PHPUnit\Framework\TestCase;
use app\model\Member;

class MemberTest extends TestCase
{
    public function testGetMemberInfo()
    {
        $member = new Member();
        $result = $member->get_member_info(['guid' => 'test-guid']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('guid', $result);
    }
}
```

### 18.2 API 测试

```php
// 使用Postman或其他工具进行API测试
// 测试用例应包括：
// - 正常请求测试
// - 异常参数测试
// - 权限验证测试
// - 边界值测试
```

## 21. 部署规范

### 19.1 环境配置

```bash
# 开发环境
APP_DEBUG = true
APP_ENV = dev

# 生产环境
APP_DEBUG = false
APP_ENV = prod
```

### 19.2 代码发布流程

1. 代码提交到版本控制系统
2. 通过 CI/CD 进行自动化测试
3. 部署到测试环境进行验证
4. 部署到生产环境
5. 监控系统运行状态

## 22. 代码注释规范

### 20.1 类注释

```php
/**
 * 会员管理类
 *
 * @package app\model
 * <AUTHOR>
 * @since 1.0.0
 */
class Member extends ModelBase
{
}
```

### 20.2 方法注释

```php
/**
 * 获取会员信息
 *
 * @param array $map 查询条件
 * @param bool $with_group 是否包含分组信息
 * @return array 会员信息
 * @throws Exception 当会员不存在时抛出异常
 */
public function get_member_info(array $map, bool $with_group = false): array
{
    // 方法实现
}
```

### 20.3 复杂逻辑注释

```php
// 计算会员等级升级所需积分
// 规则：当前等级积分 * 1.5 = 下一等级所需积分
$next_level_points = $current_points * 1.5;

/*
 * 处理会员积分变更逻辑
 * 1. 验证积分变更合法性
 * 2. 更新会员积分
 * 3. 记录积分变更日志
 * 4. 检查是否触发等级升级
 */
```

## 23. Git 提交规范

### 21.1 提交信息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 21.2 提交类型

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 21.3 提交示例

```
feat(member): 添加会员积分管理功能

- 新增积分充值接口
- 新增积分消费记录查询
- 优化积分计算逻辑

Closes #123
```

## 24. 常用工具函数

### 22.1 时间处理

```php
// 格式化时间戳
format_timestamp($timestamp);

// 获取当前微秒时间
microsecond();

// 计算时间差
get_diff_time($start_time);
```

### 22.2 数据处理

```php
// 生成GUID
create_guid();

// 检查是否为GUID
tools()::is_guid($value);

// 对象转数组
tools()::object2array($object);

// 检查是否为JSON
tools()::is_json($string);
```

### 22.3 网络请求

```php
// HTTP请求
$result = tools()::curl_request($url, $data, $method);

// 获取客户端IP
$ip = tools()::get_client_ip();
```

## 25. 错误码规范

### 23.1 系统错误码

- `0`: 成功
- `-1`: 通用错误
- `-2`: 业务状态异常
- `-3`: 权限不足
- `-4`: 参数错误
- `-5`: 数据不存在

### 23.2 业务错误码

- `1001-1999`: 用户相关错误
- `2001-2999`: 会员相关错误
- `3001-3999`: 订单相关错误
- `4001-4999`: 支付相关错误
- `5001-5999`: 第三方服务错误

## 26. 监控和告警

### 24.1 异常监控

- 系统异常自动发送企业微信通知
- 关键业务异常实时告警
- 性能指标监控

### 24.2 日志监控

- 错误日志实时监控
- API 调用量监控
- 数据库性能监控

## 27. 前后端数据交互规范

### 25.1 视图模板规范

#### 25.1.1 视图目录结构

```
app/view/
├── admin/                     # 后台管理视图
│   ├── layout/               # 布局模板
│   │   ├── base.html         # 基础布局
│   │   └── sidebar.html      # 侧边栏布局
│   ├── member/               # 会员管理页面
│   ├── goods/                # 商品管理页面
│   ├── order/                # 订单管理页面
│   └── system/               # 系统设置页面
├── index/                    # 前台页面视图
│   ├── layout/               # 前台布局
│   │   ├── base.html         # 前台基础布局
│   │   └── header.html       # 头部模板
│   ├── home/                 # 首页模板
│   ├── goods/                # 商品页面
│   └── member/               # 会员页面
├── member/                   # 会员中心视图
│   ├── layout/               # 会员中心布局
│   ├── profile/              # 个人资料
│   ├── order/                # 订单管理
│   └── coupon/               # 优惠券管理
└── common/                   # 公共模板
    ├── error.html            # 错误页面
    ├── success.html          # 成功页面
    └── components/           # 公共组件
```

#### 25.1.2 模板命名规范

```html
<!-- 页面模板命名：模块_功能.html -->
member_list.html
<!-- 会员列表页 -->
goods_detail.html
<!-- 商品详情页 -->
order_create.html
<!-- 订单创建页 -->

<!-- 布局模板命名：layout_类型.html -->
layout_admin.html
<!-- 后台布局 -->
layout_member.html
<!-- 会员中心布局 -->

<!-- 组件模板命名：component_名称.html -->
component_pagination.html
<!-- 分页组件 -->
component_breadcrumb.html
<!-- 面包屑组件 -->
```

#### 25.1.3 后台视图开发规范

**重要说明：在一卡易项目中，开发视图时只需要在 `app/view` 目录下创建视图文件，无需在控制器目录中增加对应的控制器。**

#### 25.1.3.1 后台视图布局系统

```html
<!-- ✅ 标准后台页面结构 -->
{layout name="layui_plus" /}
<div class="layui-layout layui-layout-admin">
  <div class="layui-fluid">
    <div class="layui-card">
      <div class="layui-card-body layui-form-query">
        <!-- 查询表单 -->
        <form class="layui-form" id="query_form">
          <!-- 查询条件 -->
        </form>

        <!-- 数据表格 -->
        <table
                toolbar="#toolbarDemo"
                id="fsDatagrid"
                lay-filter="fsDatagrid"
                class="fsDatagrid"
                isLoad="1"
                url="/code/index"
                isPage="1"
                defaultForm="query_form"
                defaultToolbar="filter"
                height="auto"
        ></table>

        <!-- 表格列定义 -->
        <div class="fsDatagridCols">
          <p checkbox="true"/>
          <p field="name" title="名称" align="center" width="240"></p>
          <p field="status" title="状态" align="center" templet="#statusTpl"></p>
          <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="auto"></p>
        </div>
      </div>
    </div>
  </div>
</div>
```

#### 25.1.3.2 数据表格自动化配置

```html
<!-- ✅ 表格配置属性说明 -->
<table toolbar="#toolbarDemo" <!-- 工具栏模板ID -->
id="fsDatagrid"
<!-- 表格ID -->
lay-filter="fsDatagrid"
<!-- Layui过滤器 -->
class="fsDatagrid"
<!-- 自动化表格类 -->
isLoad="1"
<!-- 自动加载数据 -->
url="/code/index"
<!-- 数据接口URL -->
isPage="1"
<!-- 启用分页 -->
defaultForm="query_form"
<!-- 默认查询表单ID -->
defaultToolbar="filter"
<!-- 默认工具栏 -->
height="auto">
<!-- 表格高度 -->
</table>

<!-- ✅ 列定义规范 -->
<div class="fsDatagridCols">
  <!-- 复选框列 -->
  <p checkbox="true"/>

  <!-- 普通列 -->
  <p field="name" title="名称" align="center" width="240">
    <!-- 字典列 -->
  </p>

  <p field="category_guid" title="类别" dict="coupon_category" align="center">
    <!-- 权限控制列 -->
  </p>

  <p auth="code/batch_renew" field="expire_time" title="到期时间" align="center">
    <!-- 模板列 -->
  </p>

  <p field="status" title="状态" align="center" templet="#statusTpl">
    <!-- 排序列 -->
  </p>

  <p field="create_time" title="创建时间" align="center" sort="true" width="180">
    <!-- 操作列 -->
  </p>

  <p fixed="right" align="center" toolbar="#barDemo" title="操作" width="auto"></p>
</div>
```

#### 25.1.3.3 查询表单规范

```html
<!-- ✅ 标准查询表单 -->
<form class="layui-form" id="query_form">
  <div class="layui-form-item">
    <!-- 关键词搜索 -->
    <div class="layui-inline">
      <select name="key" lay-verify="">
        <option value="name">名称</option>
        <option value="mobile">手机号</option>
      </select>
    </div>
    <div class="layui-inline">
      <input type="text" name="value" placeholder="请输入" autocomplete="off" class="layui-input"/>
    </div>

    <!-- 字典选择 -->
    <div class="layui-inline" auth_admin="coupon_category/index">
      <select placeholder="类别" name="category_guid" class="fsSelect" dict="coupon_category" addNull="1"></select>
    </div>

    <!-- 日期范围 -->
    <div class="layui-inline layui-input-wrap">
      <div class="layui-input-prefix">
        <i class="layui-icon layui-icon-date"></i>
      </div>
      <input type="text" name="create_time" autocomplete="off" class="layui-input fsDate" readonly dateRange="1"
             placeholder="创建时间"/>
    </div>

    <!-- 操作按钮 -->
    <div class="layui-inline">
      <button class="layui-btn" type="button" function="query">
        <i class="layui-icon layui-icon-search"></i>
        查询
      </button>
      <button class="layui-btn layui-btn-normal" type="reset">
        <i class="layui-icon layui-icon-delete"></i>
        重置
      </button>
      <button type="button" class="layui-btn" function="refresh">
        <i class="layui-icon layui-icon-refresh"></i>
        刷新
      </button>
    </div>
  </div>
</form>
```

#### ******** 工具栏和操作按钮规范

```html
<!-- ✅ 工具栏模板 -->
<script type="text/html" id="toolbarDemo">
  <div class="layui-inline">
    <!-- 新增按钮 -->
    <button class="layui-btn layui-btn-sm" function="top" topUrl="/admin/code/detail" topMode="add" topWidth="800px"
            topHeight="95%" topTitle="新增">
      <i class="layui-icon layui-icon-addition"></i>
      新增
    </button>

    <!-- 批量操作按钮 -->
    <button auth="code/batch_renew" class="layui-btn layui-btn-warm layui-btn-sm" function="batch_renew" isMutiDml="1"
            inputs="coupon_guid:$guid,edit_from:1">
      <i class="layui-icon layui-icon-edit"></i>
      批量延期
    </button>
  </div>
</script>

<!-- ✅ 行操作模板 -->
<script type="text/html" id="barDemo">
  <!-- 编辑按钮 -->
  <a auth="code/edit" class="layui-btn layui-btn-xs" lay-event="top" topUrl="/admin/code/detail" topMode="edit"
     topWidth="800px" topHeight="95%" topTitle="编辑" inputs="guid:">编辑</a>

  <!-- 自定义操作按钮 -->
  <a
          auth="coupon_generate_note/add"
          class="layui-btn layui-btn-normal layui-btn-xs"
          lay-event="top"
          topUrl="/admin/coupon_generate_note/detail"
          topMode="add"
          topHeight="80%"
          topWidth="800px"
          topTitle="发卡【{{d.name}}】"
          inputs="coupon_guid:$guid"
  >
    发卡
  </a>

  <!-- 删除按钮 -->
  <a auth="code/del" class="layui-btn layui-btn-danger layui-btn-xs" lay-event="submit" url="/code/del" isConfirm="1"
     confirmMsg="是否确定删除当前记录？" inputs="guid:">删除</a>
</script>
```

#### ******** 状态模板规范

```html
<!-- ✅ 状态显示模板 -->
<script type="text/html" id="typeTpl">
  {{# if(d.type == '1'){ }}
  <button class="layui-btn layui-btn-primary layui-btn-xs">数量卡</button>
  {{# } else if(d.type == '2'){ }}
  <button class="layui-btn layui-btn-normal layui-btn-xs">金额卡</button>
  {{# } else if(d.type == '3'){ }}
  <button class="layui-btn layui-btn-warm layui-btn-xs">充值卡</button>
  {{# } else{ }}
  <button class="layui-btn layui-btn-danger layui-btn-xs">未知类型</button>
  {{# } }}
</script>

<!-- ✅ 开关模板 -->
<script type="text/html" id="statusTpl">
  <input type="checkbox" name="status" url="/code/edit" inputs="guid:{{d.guid}}" lay-skin="switch" lay-text="启用|停用"
         {{ d.status== 1 ? 'checked' : '' }}>
</script>
```

#### ******** 权限控制规范

```html
<!-- ✅ HTML级别权限控制 -->
<!-- 单个权限 -->
<div auth="code/edit">编辑功能区域</div>
<button auth="code/delete">删除按钮</button>

<!-- 管理员权限 -->
<div auth_admin="system_admin">系统管理员功能</div>

<!-- 多个权限（满足其中一个） -->
<div auth="code/edit|code/view">编辑或查看权限</div>

<!-- 表格列权限控制 -->
<p auth="code/batch_renew" field="expire_time" title="到期时间">
  <!-- 查询条件权限控制 -->
</p>

<div class="layui-inline" auth_admin="coupon_category/index">
  <select name="category_guid" dict="coupon_category"></select>
</div>
```

#### ******** 表单字段类型规范

**重要说明：项目中提供了丰富的表单字段类型，每种类型都有标准的实现方式。**

##### ********.1 基础输入字段

```html
<!-- ✅ 文本输入框 -->
<div class="layui-form-item">
  <label class="layui-form-label">名称</label>
  <div class="layui-input-block">
    <input type="text" name="name" required="" lay-verType="tips" lay-verify="required" placeholder="请输入名称"
           autocomplete="off" class="layui-input"/>
  </div>
</div>

<!-- ✅ 数字输入框 -->
<div class="layui-form-item">
  <label class="layui-form-label">序号</label>
  <div class="layui-input-block">
    <input type="tel" name="sort" value="1" required="" lay-verType="tips" lay-verify="required"
           placeholder="请输入序号,越小越靠前" autocomplete="off" class="layui-input"/>
  </div>
</div>

<!-- ✅ 金额输入框（带单位） -->
<div class="layui-form-item">
  <label class="layui-form-label">售价</label>
  <div class="layui-input-block">
    <div class="layui-input-inline">
      <input type="text" name="selling_price" placeholder="请输入售价" autocomplete="off" class="layui-input"/>
    </div>
    <div class="layui-form-mid layui-word-aux">元</div>
  </div>
</div>

<!-- ✅ 隐藏字段 -->
<input type="hidden" name="guid"/>
```

##### ********.2 下拉选择字段

```html
<!-- ✅ 字典选择器 -->
<div class="layui-form-item" auth="coupon_category/index">
  <label class="layui-form-label">卡券类别</label>
  <div class="layui-input-block">
    <select lay-search name="category_guid" class="fsSelect" placeholder="归属卡券类别" dict="coupon_category"
            addNull="1"></select>
  </div>
</div>

<!-- ✅ 普通下拉选择 -->
<div class="layui-form-item">
  <label class="layui-form-label">发放途径</label>
  <div class="layui-input-inline">
    <select name="send_type" class="fsEditReadonly2">
      <option value="">发放途径</option>
      <option value="1">在线销售</option>
      <option value="2">免费领取</option>
    </select>
  </div>
</div>

<!-- ✅ 带联动的下拉选择 -->
<div class="layui-form-item">
  <label class="layui-form-label">兑换限制</label>
  <div class="layui-input-inline">
    <select lay-filter="used_rate_limit_type" name="used_rate_limit_type">
      <option value="">兑换限制</option>
      <option value="0" selected="">不限制</option>
      <option value="1">每人每天限兑N次</option>
      <option value="2">每人每周限兑N次</option>
    </select>
  </div>
</div>
```

##### ********.3 单选和多选字段

```html
<!-- ✅ 单选按钮组 -->
<div class="layui-form-item">
  <label class="layui-form-label">类型</label>
  <div class="layui-input-block">
    <input type="radio" checked="checked" name="type" value="1" title="限制数量" class="fsEditReadonly"/>
    <input type="radio" name="type" value="2" title="限制金额" class="fsEditReadonly"/>
    <input type="radio" name="type" value="3" title="充值卡" class="fsEditReadonly"/>
  </div>
</div>

<!-- ✅ 开关式单选 -->
<div class="layui-form-item">
  <label class="layui-form-label">允许转赠</label>
  <div class="layui-input-block">
    <input type="radio" name="share_status" value="1" title="开启"/>
    <input type="radio" name="share_status" value="0" title="关闭" checked=""/>
  </div>
</div>

<!-- ✅ 多选组件（xm-select） -->
<div class="layui-form-item">
  <label class="layui-form-label">关联产品</label>
  <div class="layui-input-block">
    <div id="goods_item_guid" class="xm-select-demo"></div>
  </div>
</div>
```

##### ********.4 文件上传字段

```html
<!-- ✅ 图片上传 -->
<div class="layui-form-item layui-form-text">
  <label class="layui-form-label">主图</label>
  <div class="layui-input-inline">
    <input type="text" id="filePath" name="pic" autocomplete="off" disabled="disabled"
           class="layui-input upload-input"/>
    <button type="button" class="layui-btn layui-btn-normal" function="upload" fileElem="#filePath" fileAccept="file"
            fileExts="" fileSize="3072" inputs="type:test">
      <i class="layui-icon layui-icon-upload-drag"></i>
      上传
    </button>
    <span class="info">建议尺寸 800px*800px</span>
  </div>
</div>

<!-- ✅ 视频上传 -->
<div class="layui-form-item layui-form-text">
  <label class="layui-form-label">视频</label>
  <div class="layui-input-inline">
    <input type="text" id="filePathvideo_url" name="video_url" autocomplete="off" disabled="disabled"
           class="layui-input upload-input"/>
    <button type="button" class="layui-btn layui-btn-normal" function="upload" fileElem="#filePathvideo_url"
            fileAccept="video" fileExts="" fileSize="10240" inputs="type:test" image="0">
      <i class="layui-icon layui-icon-upload-drag"></i>
      上传视频
    </button>
  </div>
</div>
```

##### ********.5 日期时间字段

```html
<!-- ✅ 日期选择器 -->
<div class="layui-form-item">
  <label class="layui-form-label">有效期</label>
  <div class="layui-input-inline">
    <input type="text" name="expire_time" autocomplete="off" class="layui-input fsDate" readonly dateType="date"/>
  </div>
</div>

<!-- ✅ 日期范围选择器 -->
<div class="layui-form-item">
  <label class="layui-form-label">活动时间</label>
  <div class="layui-input-inline">
    <input type="text" name="activity_time" autocomplete="off" class="layui-input fsDate" readonly dateRange="1"
           placeholder="开始时间 - 结束时间"/>
  </div>
</div>
```

##### ********.6 富文本编辑器

```html
<!-- ✅ 富文本编辑器 -->
<div class="layui-form-item layui-form-text">
  <label class="layui-form-label">卡券详情</label>
  <div class="layui-input-block">
    <div class="form-group">
      <textarea name="description" id="description" class="Kindeditor"></textarea>
    </div>
  </div>
</div>

<!-- ✅ 简单文本域 -->
<div class="layui-form-item layui-form-text">
  <label class="layui-form-label">备注</label>
  <div class="layui-input-block">
    <textarea name="remark" placeholder="请输入备注" class="layui-textarea"></textarea>
  </div>
</div>
```

##### ********.7 条件显示字段

```html
<!-- ✅ 根据其他字段值显示/隐藏 -->
<div class="layui-form-item" show_name="type" show_value="1">
  <label class="layui-form-label">每次提货产品数量</label>
  <div class="layui-input-inline">
    <input type="tel" value="1" placeholder="单次兑换N件产品" name="exchange_goods_num" autocomplete="off"
           class="layui-input"/>
  </div>
  <div class="layui-form-mid layui-word-aux">件</div>
</div>

<!-- ✅ 多值条件显示 -->
<div class="layui-form-item" show_name="type" show_value="2,3">
  <label class="layui-form-label">提货产品金额</label>
  <div class="layui-input-inline">
    <input type="text" placeholder="兑换X元以内的产品" name="value" autocomplete="off" class="layui-input"/>
  </div>
  <div class="layui-form-mid layui-word-aux">元</div>
</div>
```

##### ********.8 JavaScript 字段初始化

```javascript
// ✅ 多选组件初始化
function load_goods_item_guid_select() {
  let goods_item_guid_select = xmSelect.render({
    el: "#goods_item_guid",
    name: "goods_item_guid",
    filterable: true,
    toolbar: { show: true },
    searchTips: "请输入产品名称",
    tips: "请选择产品",
    prop: {
      name: "name",
      value: "goods_item_guid",
    },
    data: [],
  });

  // 加载数据
  post_layui_admin_api_v1("/code/goods_item_list", { guid: guid }, function (result) {
    goods_item_guid_select.update({
      data: result.data,
    });
  });
}

// ✅ 表单联动事件
layui.use("form", function () {
  var form = layui.form;

  // 下拉选择联动
  form.on("select(used_rate_limit_type)", function (data) {
    let used_rate_limit_type = data.value;
    if (used_rate_limit_type > 0) {
      $("#used_rate_limit_num").show();
    } else {
      $("#used_rate_limit_num").hide();
    }
  });

  // 单选按钮联动
  form.on("radio(cycle_delivery)", function (data) {
    if (data.value == "1") {
      $('[show_name="cycle_delivery"][show_value="1"]').show();
    } else {
      $('[show_name="cycle_delivery"][show_value="1"]').hide();
    }
  });
});
```

##### 视图文件创建流程

```bash
# 1. 直接在对应的视图目录下创建HTML文件
# 例如：创建会员管理页面
app/view/admin/member/list.html        # 会员列表页
app/view/admin/member/edit.html        # 会员编辑页
app/view/admin/member/detail.html      # 会员详情页

# 2. 创建前台页面
app/view/index/goods/list.html         # 商品列表页
app/view/index/goods/detail.html       # 商品详情页

# 3. 创建会员中心页面
app/view/member/profile/index.html     # 个人资料页
app/view/member/order/list.html        # 订单列表页
```

##### 视图文件访问规则

```
访问规则：域名/模块/控制器/方法
视图文件路径：app/view/模块/控制器/方法.html

示例：
URL: http://domain.com/admin/member/list
对应视图文件: app/view/admin/member/list.html

URL: http://domain.com/index/goods/detail
对应视图文件: app/view/index/goods/detail.html

URL: http://domain.com/member/profile/index
对应视图文件: app/view/member/profile/index.html
```

##### 视图开发注意事项

**重要限制：视图文件中严禁出现 PHP 代码和 ThinkPHP 模板语法，只允许使用纯 HTML 代码。**

```html
<!-- ✅ 正确：使用纯HTML代码 -->
<!DOCTYPE html>
<html>
<head>
  <title>会员列表</title>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="stylesheet" href="/static/css/admin.css"/>
</head>
<body>
<!-- 1. 纯HTML结构 -->
<div class="header">
  <h1>管理后台</h1>
  <nav class="nav">
    <a href="/admin/member/list">会员管理</a>
    <a href="/admin/goods/list">商品管理</a>
  </nav>
</div>

<!-- 2. 页面内容使用静态HTML -->
<div class="content">
  <div class="page-title">
    <h2>会员列表</h2>
  </div>

  <!-- 3. 表格结构 -->
  <table class="data-table" id="memberTable">
    <thead>
    <tr>
      <th>会员ID</th>
      <th>会员姓名</th>
      <th>手机号</th>
      <th>状态</th>
      <th>操作</th>
    </tr>
    </thead>
    <tbody>
    <!-- 数据通过JavaScript动态加载 -->
    </tbody>
  </table>
</div>

<!-- 4. JavaScript处理数据和交互 -->
<script src="/static/js/jquery.min.js"></script>
<script src="/static/js/function.js"></script>
<script>
  // 页面加载完成后获取数据
  $(document).ready(function () {
    loadMemberList();
  });

  // 加载会员列表数据
  function loadMemberList() {
    post_layui_admin_api_v1("/member/index", {}, function (result) {
      renderMemberTable(result.data);
    });
  }

  // 渲染表格数据
  function renderMemberTable(data) {
    var html = "";
    data.forEach(function (item) {
      html += "<tr>";
      html += "<td>" + item.id + "</td>";
      html += "<td>" + item.name + "</td>";
      html += "<td>" + item.mobile + "</td>";
      html += "<td>" + (item.status == 1 ? "正常" : "禁用") + "</td>";
      html += '<td><button onclick="editMember(' + item.id + ')">编辑</button></td>';
      html += "</tr>";
    });
    $("#memberTable tbody").html(html);
  }
</script>
</body>
</html>
```

```html
<!-- ❌ 错误：视图文件中不允许出现PHP代码和ThinkPHP模板语法 -->
<!DOCTYPE html>
<html>
<head>
  <title>会员列表</title>
</head>
<body>
<!-- ❌ 禁止：PHP代码 -->
<?php
    $members = Member::select();
    foreach($members as $member) {
        echo $member['name'];
    }
    ?>

<!-- ❌ 禁止：ThinkPHP模板语法 -->
{volist name="list" id="item"}
<div>{$item.name}</div>
{/volist}

<!-- ❌ 禁止：模板变量输出 -->
<h1>{$title}</h1>
<p>{$user.name}</p>

<!-- ❌ 禁止：模板条件判断 -->
{if condition="$status eq 1"}
<div>正常状态</div>
{/if}

<!-- ❌ 禁止：模板包含 -->
{include file="header" /}
</body>
</html>
```

##### 视图文件编码规范

```html
<!-- ✅ 只允许纯HTML标记 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>页面标题</title>
  <link rel="stylesheet" href="/static/css/style.css"/>
</head>
<body>
<!-- 1. 标准HTML结构 -->
<div class="container">
  <header class="header">
    <h1>页面标题</h1>
  </header>

  <!-- 2. 表单结构 -->
  <form id="dataForm" class="form">
    <div class="form-group">
      <label for="name">姓名：</label>
      <input type="text" id="name" name="name" required/>
    </div>
    <div class="form-group">
      <label for="mobile">手机号：</label>
      <input type="tel" id="mobile" name="mobile" required/>
    </div>
    <button type="submit">提交</button>
  </form>

  <!-- 3. 数据展示区域 -->
  <div id="dataList" class="data-list">
    <!-- 数据通过JavaScript动态渲染 -->
  </div>
</div>

<!-- 4. JavaScript处理逻辑 -->
<script src="/static/js/jquery.min.js"></script>
<script src="/static/js/function.js"></script>
<script>
  // 所有数据处理和交互逻辑都在JavaScript中完成
</script>
</body>
</html>
```

##### 数据处理原则

```
视图层职责：
✅ 静态HTML结构展示
✅ CSS样式定义
✅ JavaScript前端交互逻辑
✅ 通过AJAX获取和展示数据

API层职责：
✅ 数据查询和处理
✅ 业务逻辑处理
✅ 数据验证和过滤
✅ 返回JSON格式数据

严格分离原则：
❌ 视图中不允许PHP代码
❌ 视图中不允许ThinkPHP模板语法
❌ 视图中不允许数据库操作
❌ 视图中不允许服务器端逻辑处理

数据交互模式：
前端HTML → JavaScript → AJAX请求 → API接口 → 返回JSON → JavaScript渲染
```

##### 数据传递规范

```php
// 控制器中向视图传递数据（如果需要控制器处理）
class MemberController extends BaseController
{
    public function list()
    {
        $list = Member::select();
        return $this->fetch('admin/member/list', [
            'list' => $list,
            'title' => '会员列表'
        ]);
    }
}
```

##### 静态页面开发

```html
<!-- 对于纯静态页面，可以直接创建HTML文件，无需控制器 -->
<!-- 文件：app/view/admin/dashboard/index.html -->
<!DOCTYPE html>
<html>
<head>
  <title>管理后台首页</title>
  <link rel="stylesheet" href="/static/css/admin.css"/>
</head>
<body>
<div class="dashboard">
  <h1>欢迎使用管理后台</h1>
  <!-- 静态内容 -->
</div>
<script src="/static/js/admin.js"></script>
</body>
</html>
```

### 25.2 前后端数据交互格式

#### 25.2.1 项目专用请求方法规范

项目中定义了多个专用的请求方法，按照不同的 UI 框架和 API 模块进行分类：

##### 后台管理 API 请求方法

**1. Layui 框架 + 后台管理 API**

```javascript
// 使用 post_layui_admin_api_v1 方法
post_layui_admin_api_v1(
  "/member/index",
  {
    page: 1,
    page_size: 20,
    keyword: "搜索关键词",
  },
  function (result) {
    // 成功回调
    console.log(result.data);
  },
  function (result) {
    // 失败回调
    console.log(result.msg);
  }
);
```

**2. WeUI 框架 + 后台管理 API**

```javascript
// 使用 post_weui_admin_api_v1 方法
post_weui_admin_api_v1(
  "/member/save",
  {
    name: "会员姓名",
    mobile: "13800138000",
  },
  function (result) {
    // 成功回调
    weui.alert("保存成功");
  },
  function (result) {
    // 失败回调
    weui.alert(result.msg);
  }
);
```

##### 会员端 API 请求方法

**1. Layui 框架 + 会员 API**

```javascript
// 使用 post_layui_member_api_v1 方法
post_layui_member_api_v1("/member/info", {}, function (result) {
  // 成功回调
  console.log("会员信息:", result.data);
});
```

**2. WeUI 框架 + 会员 API**

```javascript
// 使用 post_weui_member_api_v1 方法
post_weui_member_api_v1(
  "/coupon/list",
  {
    page: 1,
  },
  function (result) {
    // 渲染优惠券列表
    renderCouponList(result.data);
  }
);
```

**3. jQuery WeUI + 会员 API**

```javascript
// 使用 post_jqweui_member_api_v1 方法（带loading效果）
post_jqweui_member_api_v1(
  "/order/create",
  {
    goods_id: 123,
    quantity: 1,
  },
  function (result) {
    $.alert("订单创建成功", "提示");
  }
);
```

**4. HUI 框架 + 会员 API**

```javascript
// 使用 post_hui 方法
post_hui(
  "/member/update",
  {
    nickname: "新昵称",
  },
  function (result) {
    hui.alert("更新成功");
  }
);
```

##### 通用 API 请求方法

**1. 通用 API 请求**

```javascript
// 使用 post_layui_api_api_v1 方法
post_layui_api_api_v1("/common/area", {}, function (result) {
  // 处理地区数据
  console.log(result.data);
});
```

**2. 原生 AJAX API 请求**

```javascript
// 使用 ajax_admin_api_v1 方法（无UI框架依赖）
ajax_admin_api_v1("/system/config", {}, function (result) {
  // 处理系统配置
  console.log(result.data);
});

// 使用 ajax_member_api_v1 方法
ajax_member_api_v1("/member/profile", {}, function (result) {
  // 处理会员资料
  console.log(result.data);
});
```

#### 25.2.2 自动 Token 处理机制

项目中的 API 请求方法会自动处理 Token 认证：编码无需关心

#### 25.2.3 表单数据获取规范

项目提供了多种表单数据获取方法：

```javascript
// 方法1：获取表单JSON对象
var formData = getFormJson("#myForm");
console.log(formData); // {name: 'value', mobile: '13800138000'}

// 方法2：获取表单数据（简化版）
var formData = get_form_data("myForm"); // 传入表单ID（不含#）

// 方法3：Layui表单数据获取（支持数组）
var formData = getFormDataArray($("#myForm"));

// 方法4：Layui专用表单数据获取
var formData = getFormData("formFilter"); // 传入lay-filter值

// 方法5：jQuery序列化为对象
var formData = $("#myForm").serializeObject();
```

#### 25.2.4 Loading 效果处理

不同 UI 框架的 Loading 处理方式：

```javascript
// Layui框架Loading
post_layui("/api/url", data, success, failed); // 自动显示loading

// 禁用loading效果
post_layui(
  "/api/url",
  {
    _loading: false, // 特殊字段，禁用loading
    name: "value",
  },
  success,
  failed
);

// WeUI框架Loading
post_weui("/api/url", data, success, failed); // 自动显示"加载中"

// HUI框架Loading
post_hui("/api/url", data, success, failed); // 自动显示loading

// jQuery WeUI Loading
post_jqweui_member_api_v1("/api/url", data, success, failed); // 自动显示loading
```

#### 25.2.5 表单提交规范

```javascript
// 使用项目专用方法提交表单
function submitForm(formId) {
  var formData = get_form_data(formId); // 使用项目方法获取表单数据

  // 使用对应的API方法提交
  post_layui_admin_api_v1(
    "/member/save",
    formData,
    function (result) {
      layer.msg("保存成功");
      // 刷新页面或跳转
      location.reload();
    },
    function (result) {
      layer.alert(result.msg);
    }
  );
}

// 文件上传表单提交
function submitFormWithFile(formId) {
  var formData = new FormData(document.getElementById(formId));

  // 使用原生ajax方法处理文件上传
  ajax({
    url: "/admin_api/v1/file/upload",
    data: formData,
    processData: false,
    contentType: false,
    success: function (result) {
      if (result.code === 0) {
        layer.msg("上传成功");
      } else {
        layer.alert(result.msg);
      }
    },
  });
}
```

#### 25.2.6 常用工具函数

项目提供了丰富的工具函数，以下是常用的：

```javascript
// 1. URL参数和Cookie处理
var bid = getQueryString("bid"); // 获取URL参数
var token = getCookie("access_token"); // 获取Cookie
setCookie("name", "value", 7); // 设置Cookie（7天）
var value = getQueryStringOrCookie("bid"); // 优先URL参数，其次Cookie

// 2. 设备类型检测
var deviceType = get_device_type(); // 返回: wechat/alipay/miniapp/wap/web
var isWeixin = is_weixin(); // 是否微信环境
var isMobile = is_mobile(); // 是否移动设备
var isIOS = is_ios(); // 是否iOS设备

// 3. 数据验证
var isValidPhone = is_phone("13800138000"); // 手机号验证
var isValidIdCard = is_id_card_number("身份证号"); // 身份证验证

// 4. 数字计算（解决精度问题）
var sum = accAdd(0.1, 0.2); // 精确加法
var diff = Subtr(1.0, 0.9); // 精确减法
var product = accMul(0.1, 3); // 精确乘法
var quotient = accDiv(1, 3); // 精确除法

// 5. 字符串处理
var encoded = url_encode("中文字符"); // URL编码
var decoded = url_decode(encoded); // URL解码
var htmlEncoded = HTMLEncode("<script>"); // HTML转义
var htmlDecoded = HTMLDecode(htmlEncoded); // HTML反转义

// 6. 页面跳转和刷新
redirect("/admin/member", 3); // 3秒后跳转
reload(2); // 2秒后刷新页面

// 7. 权限检查
var hasAuth = has_auth("member/edit"); // 检查是否有权限
init_permission(); // 初始化权限控制

// 8. 时间格式化
var timeStr = formatDateTime("yyyy-MM-dd hh:mm:ss"); // 当前时间格式化
var dateStr = new Date().format("yyyy-MM-dd"); // Date对象格式化

// 9. 数据判空
var isEmpty = isEmpty(data); // 判断数据是否为空

// 10. UUID生成
var id = uuid(); // 生成UUID
```

#### 25.2.7 文件上传规范

```javascript
// 文件上传处理
function uploadFile(fileInput) {
  var file = fileInput.files[0];

  // 文件类型验证
  var allowedTypes = ["image/jpeg", "image/png", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    showError("只支持JPG、PNG、GIF格式的图片");
    return;
  }

  // 文件大小验证（5MB）
  if (file.size > 5 * 1024 * 1024) {
    showError("文件大小不能超过5MB");
    return;
  }

  var formData = new FormData();
  formData.append("file", file);
  formData.append("type", "image");

  $.ajax({
    url: "/admin_api/v1/file/upload",
    type: "POST",
    data: formData,
    processData: false,
    contentType: false,
    success: function (response) {
      if (response.code === 0) {
        // 显示上传成功的图片
        $("#preview").attr("src", response.data.url);
        $("#file_url").val(response.data.url);
      } else {
        showError(response.msg);
      }
    },
  });
}
```

#### 25.2.4 分页数据处理

```javascript
// 分页数据处理
post_layui_member_api_v1();

function loadPageData(page = 1) {
  $.ajax({
    url: "/admin_api/v1/member/index",
    type: "GET",
    data: {
      page: page,
      page_size: 20,
      keyword: $("#keyword").val(),
      status: $("#status").val(),
    },
    success: function (response) {
      if (response.code === 0) {
        // 渲染数据列表
        renderDataList(response.data.data);
        // 渲染分页组件
        renderPagination(response.data);
      }
    },
  });
}
```

### 25.3 数据导出规范

#### 25.3.1 导出功能使用

```javascript
// 方法1：使用模块和控制器导出
function exportData() {
  export_note("admin_api", "member", "export");
  // 会自动获取查询表单参数并导出
}

// 方法2：使用完整URL导出
function exportDataByUrl() {
  export_note_url("/admin_api/v1/member/export");
  // 会自动获取查询表单参数并导出
}

// 导出时会自动序列化查询表单的参数
// 要求页面中存在id为"query_form"的表单
```

#### 25.3.2 批量操作规范

```javascript
// 获取选中的复选框值
function batchDelete() {
  var ids = get_checkbox_values("member_ids"); // 获取name为member_ids的复选框值
  if (!ids) {
    layer.alert("请选择要删除的记录");
    return;
  }

  layer.confirm("确定要删除选中的记录吗？", function (index) {
    post_layui_admin_api_v1(
      "/member/batch_delete",
      {
        ids: ids,
      },
      function (result) {
        layer.msg("删除成功");
        location.reload();
      }
    );
    layer.close(index);
  });
}
```

### 25.4 权限控制规范

#### 25.4.1 HTML 权限控制

```html
<!-- 单个权限控制 -->
<button auth="member/edit" class="layui-btn">编辑</button>
<a auth="member/delete" href="javascript:;">删除</a>

<!-- 多个权限控制（满足其中一个即可） -->
<button auth="member/edit|member/view" class="layui-btn">查看/编辑</button>

<!-- 管理员权限控制 -->
<div auth_admin="system_admin">系统管理员专用功能</div>
```

#### 25.4.2 JavaScript 权限检查

```javascript
// 检查单个权限
if (has_auth("member/edit")) {
  // 有权限时的操作
  showEditButton();
}

// 初始化权限控制（页面加载时调用）
init_permission(); // 会自动隐藏无权限的元素
```

#### 25.4.3 权限数据存储

```javascript
// 权限数据存储在WebStorage中
var wsCache = new WebStorageCache();

// 用户权限列表
var userPermission = wsCache.get("user_permission");
// 示例：['member/index', 'member/edit', 'goods/index']

// 商家权限列表
var businessPermission = wsCache.get("business_permission");
// 示例：['system_admin', 'data_export']
```

### 25.5 数据验证规范

#### 25.5.1 前端验证

```javascript
// 表单验证规则
var validationRules = {
  mobile: {
    required: true,
    pattern: /^1[3-9]\d{9}$/,
    message: "请输入正确的手机号码",
  },
  email: {
    required: false,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: "请输入正确的邮箱地址",
  },
  password: {
    required: true,
    minLength: 6,
    maxLength: 20,
    message: "密码长度为6-20位",
  },
};

// 验证函数
function validateForm(formId) {
  var form = document.getElementById(formId);
  var isValid = true;

  for (var field in validationRules) {
    var input = form.querySelector('[name="' + field + '"]');
    if (input) {
      if (!validateField(input, validationRules[field])) {
        isValid = false;
      }
    }
  }

  return isValid;
}
```

## 28. 变更日志维护规范

### 26.1 变更日志格式规范

#### 26.1.1 CHANGELOG.md 文件结构

```markdown
# 变更日志

本项目的所有重要变更都将记录在此文件中。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本号遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增

- 待发布的新功能

### 变更

- 待发布的功能变更

### 修复

- 待发布的问题修复

## [1.2.0] - 2025-01-15

### 新增

- 会员积分管理功能
- 优惠券批量发放功能
- 订单导出功能
- 微信小程序支付接口

### 变更

- 优化会员列表查询性能
- 更新支付回调处理逻辑
- 调整 API 响应格式统一标准

### 修复

- 修复会员登录状态异常问题
- 修复订单状态更新延迟问题
- 修复文件上传大小限制问题

### 安全

- 加强 API 接口权限验证
- 修复 SQL 注入安全漏洞
```

### 26.2 版本号管理规范

#### 26.2.1 语义化版本规则

```
版本格式：主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)

- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

示例：
- 1.0.0 -> 1.0.1 (修复bug)
- 1.0.1 -> 1.1.0 (新增功能)
- 1.1.0 -> 2.0.0 (重大变更)
```

#### 26.2.2 版本发布流程

```markdown
### 版本发布检查清单

#### 发布前准备

- [ ] 代码审查完成
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 安全扫描通过
- [ ] 文档更新完成

#### 版本标记

- [ ] 更新版本号
- [ ] 更新 CHANGELOG.md
- [ ] 创建 Git 标签
- [ ] 生成发布说明

#### 部署验证

- [ ] 测试环境部署验证
- [ ] 预生产环境验证
- [ ] 生产环境部署
- [ ] 功能验证测试
- [ ] 性能监控检查
```

### 26.3 API 变更管理

#### 26.3.1 API 版本控制

```php
// API版本在URL中体现
/api/v1/members          // 版本1
/api/v2/members          // 版本2

// 向下兼容处理
class MemberV1Controller extends BasicApi
{
    public function index()
    {
        // v1版本的实现
        $data = $this->getV1Format();
        result($data);
    }
}

class MemberV2Controller extends BasicApi
{
    public function index()
    {
        // v2版本的实现
        $data = $this->getV2Format();
        result($data);
    }
}
```

## 29. 总结

本编码规范涵盖了一卡易项目开发的各个方面，包括：

- 项目架构和目录结构
- 命名规范和编码风格
- 控制器、模型、中间件开发规范
- 数据库操作和缓存使用
- API 设计和安全规范
- 前后端数据交互规范
- 变更日志维护规范
- 性能优化和测试规范
- 部署和监控规范

遵循这些规范可以确保代码的一致性、可维护性和可扩展性，提高团队开发效率和代码质量。

---

**文档版本**: v1.0
**最后更新**: 2025-07-26
**维护者**: 开发团队

```

```
