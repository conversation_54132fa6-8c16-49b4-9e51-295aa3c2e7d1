<view class="bottom-bar" style="display: none">
  <navigator url="/pages/address-edit/address-edit">添加新地址</navigator>
</view>
<view class="address-list">
  <view style="margin-bottom: 32rpx">
    <view wx:if="{{address_list.length==0}}" style="color: #888;text-align: center;padding: 32rpx 0;">暂无收货地址</view>
    <bloc wx:else>
      <view bindtap="pickAddress" data-index="{{index}}" class="address-item" wx:for="{{address_list}}">
        <view class="userinfo flex-row">
          <view class="flex-grow-1"> 收货人：{{item.true_name}}</view>
          <view class="flex-grow-0">{{item.mobile}}</view>
        </view>
        <view class="address-detail">收货地址：{{item.province_name}}{{item.city_name}}{{item.area_name}}{{item.address}}
          <text wx:if="{{item.is_default==1}}">(默认)</text>
        </view>
      </view>
    </bloc>
  </view>
  <view class="flex-row">
    <view class="flex-grow-1 px-24">
      <navigator class="btn btn-red" url="/pages/address-edit/address-edit">添加新地址</navigator>
    </view>
    <view class="flex-grow-1 px-24">
      <button class="btn btn-green" bindtap="getWechatAddress">一键获取地址</button>
    </view>
  </view>
</view>