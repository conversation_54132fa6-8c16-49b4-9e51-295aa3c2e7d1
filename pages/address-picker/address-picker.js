var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    address_list: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this,options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var page = this;
    wx.showNavigationBarLoading();
    app.request({
      url: api.address.list,
      success: function (res) {
        wx.hideNavigationBarLoading();
        page.setData({
          address_list: res.data,
        });
      }
    });
  },

  pickAddress: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    var address = page.data.address_list[index];
    wx.setStorageSync("picker_address", address);
    wx.navigateBack();
  },

  getWechatAddress: function (e) {
    var page = this;
    wx.chooseAddress({
      success: function (e) {
        if (e.errMsg != 'chooseAddress:ok')
          return;
        app.request({
          url: api.user.add_wechat_address,
          data: {
            national_code: e.nationalCode,
            true_name: e.userName,
            mobile: e.telNumber,
            address: e.detailInfo,
            province_name: e.provinceName,
            city_name: e.cityName,
            area_name: e.countyName,
          },
          success: function (res) {
              wx.setStorageSync("picker_address", res.data);
              wx.navigateBack();
          }
        });
      }
    });
  },
});