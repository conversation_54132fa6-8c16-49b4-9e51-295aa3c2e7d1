<view class="info">
  <block wx:if="{{share_setting.level == 3}}">
    <view class="info-title flex-row">
      <navigator class="flex-x-center width" url="/pages/share-team/share-team?level=1" open-type="redirect">
        <view class="info-text {{level == 1?'active':''}}">{{share_setting.first_name || '一级'}}({{first_count}})</view>
      </navigator>
      <navigator class="flex-x-center width" url="/pages/share-team/share-team?level=2" open-type="redirect">
        <view class="info-text {{level == 2?'active':''}}">{{share_setting.second_name || '二级'}}({{second_count}})</view>
      </navigator>
      <navigator class="flex-x-center width" url="/pages/share-team/share-team?level=3" open-type="redirect">
        <view class="info-text {{level == 3?'active':''}}">{{share_setting.third_name || '三级'}}({{third_count}})</view>
      </navigator>
    </view>
  </block>
  <block wx:elif="{{share_setting.level == 2}}">
    <view class="info-title flex-row">
      <navigator class="flex-x-center width-50" url="/pages/share-team/share-team?level=1" open-type="redirect">
        <view class="info-text {{level == 1?'active':''}}">{{share_setting.first_name || '一级'}}({{first_count}})</view>
      </navigator>
      <navigator class="flex-x-center width-50" url="/pages/share-team/share-team?level=2" open-type="redirect">
        <view class="info-text {{level == 2?'active':''}}">{{share_setting.second_name || '二级'}}({{second_count}})</view>
      </navigator>
    </view>
  </block>
  <block wx:else>
  </block>
  <view class="info-content">
    <block wx:for="{{list}}">
      <view class="info-label">
        <view class="info-up flex-y-center flex-row">
          <view class="info-img flex-grow-0">
            <image class="img" src="{{item.head_img}}"></image>
          </view>
          <view class="info-text flex-grow-1">
            <view class="info-name flex-row" style='justify-content:space-between;'>
              <view class='flex-grow-0 flex-y-center text-more' style='width:300rpx;display:block'>{{item.name ? item.name : 'ID:'+item.id}}</view>
              <view wx:if="{{item.child_count}}" class='flex-grow-0 flex-y-center fs-sm'>推广{{item.child_count}}人</view>
            </view>
            <view class="info-time">注册时间：{{item.create_time}}</view>
          </view>
        </view>
        <view class="info-down flex-y-center" wx:if="{{item.total_order_money}}">
          <view class="info-left">消费{{item.total_order_money}}元</view>
          <view class="info-right" wx:if="{{item.count}}">
            <view class="info-order">{{item.total_order_count}}个订单</view>
          </view>
        </view>
      </view>
    </block>
  </view>
  <view class="info-footer flex-row">
    <view class="info-before">
      <view class="info-border"></view>
    </view>
    <view class="info-t">没有更多了</view>
    <view class="info-after">
      <view class="info-border"></view>
    </view>
  </view>
</view>