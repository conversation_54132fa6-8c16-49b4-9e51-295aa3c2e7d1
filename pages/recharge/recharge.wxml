<import src="/common/tabbar/tabbar.wxml" />
<template wx:if="{{show_tab_bar==1}}" is="tabbar" data="{{tabbar}}" />
<view class="info page after-navber">
  <view class="info-t" style="margin-top:40rpx;">
    我的账户
    <navigator url="/pages/balance/balance" style="float: right;">
      <span>余额明细</span>
    </navigator>

  </view>

  <view class="info-account flex-y-center">
    <image class="info-bg" src="/images/icon-recharge-bg.png"></image>
    <view class="flex-row w-100">
      <view class="flex-grow-1 flex-row">
        <view class="flex-grow-0">
          <image src="/images/icon-recharge-balance.png" style="width:72rpx;height:72rpx;margin-right:20rpx;"></image>
        </view>
        <view class="flex-grow-1" style="font-size:19pt;">余额</view>
      </view>
      <view class="flex-grow-0 flex-y-center" style="font-size:21pt;">{{balance.money}} {{balance.money_unit}}</view>
    </view>
  </view>


  <view class="info-t" style="margin-top:56rpx;">
    <block wx:if="{{list.length>0}}">
      方式一:
    </block>
    绑定礼品卡
  </view>

  <form bindsubmit="exchange">
    <view class="weui-cells weui-cells_after-title">

      <view class="weui-cell ">
        <view class="weui-cell__hd">
          <view class="weui-label">卡号</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" type="text" name='code' value="{{code}}" placeholder="请输入卡号" />
        </view>
        <view bindtap="scan">
          <image class="image" style="width: 40rpx;height: 40rpx;" src="/images/scan.png" alt></image>
        </view>
      </view>

      <view class="weui-cell ">
        <view class="weui-cell__hd">
          <view class="weui-label">密码</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" type="password" name='password' value="{{password}}" focus="{{password_focus}}" placeholder="请输入密码" />
        </view>
      </view>

    </view>
    <view class="weui-btn-area" style="margin: 20px 0 0 0;">
      <button class='recharge-btn flex-x-center flex-y-center' style="width: 90%;" type="primary" formType="submit">确认绑定</button>
    </view>
  </form>



  <block wx:if="{{list.length>0}}">

    <view class="info-t" style="margin-top:56rpx;">方式二: 在线充值</view>
    <view class="list flex-row">
      <view bindtap="click" class="one flex-x-center {{selected==index?'active':''}}" data-index="{{index}}" wx:for="{{list}}">
        <view wx:if="{{item.send_price>0}}">
          <view class="flex-x-center one-1">{{item.pay_price}}</view>
          <view class="flex-x-center one-2">送{{item.send_price}}</view>
        </view>
        <view class="flex-y-center" wx:else>
          <view class="flex-x-center one-1">{{item.pay_price}}</view>
        </view>
      </view>
    </view>


    <view bindtap="click" class="one-type" data-index="-1" wx:if="{{balance.type==1}}">
      <view class="flex-y-center">
        <input bindinput="input" class="r-input" placeholder="手动输入充值金额" placeholderClass="r-input-p" type="number" value="{{money}}"></input>
      </view>
    </view>
    <view bindtap="pay" class="recharge-btn flex-x-center flex-y-center">立即充值</view>
    <view class="info-t" style="margin-top:72rpx;" wx:if="{{balance.help}}">充值说明</view>
    <view class="info-t" style="border:0;line-height:1.5" wx:if="{{balance.help}}">
      <text>{{balance.help}}</text>
    </view>
  </block>
  <view style="padding-top: 10rpx;"></view>
  <include src="/common/padding/top_for_show_tab_bar.wxml" />
</view>