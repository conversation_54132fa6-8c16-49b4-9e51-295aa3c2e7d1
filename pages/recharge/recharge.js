var api = require("../../api.js"),
  app = getApp();

Page({
  data: {
    code: '',
    password: '',
    password_focus: false,
    selected: -1
  },
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    this.loadData();
    if (options.q !== undefined) {
      app.tools.parse_qrcode(this, decodeURIComponent(options.q));
    }
    if (options) {
      console.log('options - options')
    }
    if (options.code !== undefined) {
      this.setData({
        code: options.code
      });
    }
    if (options.password !== undefined) {
      this.setData({
        password: options.password
      });
    }
  },
  loadData: function () {
    var page = this;
    app.request({
      url: api.recharge.list,
      success: function (result) {
        page.setData(result.data);
      }
    });
  },
  scan: function (e) {
    console.log('index - scan')
    var that = this;
    wx.scanCode({
      success(res) {
        console.log(res.result);
        app.tools.parse_qrcode(that, res.result);
        that.setData({
          // code: res.result,
          password_focus: true,
        });
      },
      fail(res) {
        console.log(res);
      }
    })
  },
  exchange: function (e) {
    var that = this;
    let data = e.detail.value;
    console.log(data);
    if (!data.code) {
      wx.showModal({
        title: '系统提示',
        content: '请输入卡号!',
        showCancel: false,
      });
      return false;
    }
    if (!data.password) {
      wx.showModal({
        title: '系统提示',
        content: '请输入密码!',
        showCancel: false,
      });
      return false;
    }

    app.request({
      url: app.api.code.code_to_member_money,
      data: data,
      success: function (result) {
        console.log(result);
        app.get_user_info();
        that.loadData();
        wx.showModal({
          title: "提示",
          content: result.msg,
          showCancel: false,
          confirmText: "确认",
          success: function (e) {
            wx.navigateTo({
              url: '/pages/user/user',
            })
            return;
            wx.navigateBack({
              delta: 1
            });
          }
        })
      },
    });
  },
  onReady: function () {

  },
  onShow: function () {
    // app.pageOnShow(this);
  },
  onHide: function () {
    // app.pageOnHide(this);
  },
  onUnload: function () {
    // app.pageOnUnload(this);
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
  click: function (e) {
    this.setData({
      selected: e.currentTarget.dataset.index
    });
  },
  pay: function (e) {
    var that = this,
      data = {},
      index = that.data.selected;
    if (-1 == index) {
      var i = that.data.money;
      if (i < .01) return void wx.showModal({
        title: "提示",
        content: "充值金额不能小于0.01",
        showCancel: !1
      });
      data.pay_price = i, data.send_price = 0;
    } else {
      var o = that.data.list;
      data.pay_price = o[index].pay_price, data.send_price = o[index].send_price;
    };

    if (!data.pay_price) {
      wx.showModal({
        title: "提示",
        content: "请选择充值金额",
        showCancel: !1
      });
      return;
    }
    data.share_member_guid = wx.getStorageSync('share_member_guid');
    data.share_user_guid = wx.getStorageSync('share_user_guid');

    app.request({
      url: api.recharge.submit,
      data: data,
      success: function (e) {
        wx.requestPayment({
          timeStamp: e.data.timeStamp,
          nonceStr: e.data.nonceStr,
          package: e.data.package,
          signType: e.data.signType,
          paySign: e.data.paySign,
          complete: function (e) {
            if ("requestPayment:ok" == e.errMsg) {
              app.get_user_info();
              that.loadData();

              wx.showModal({
                title: "提示",
                content: "充值成功",
                showCancel: false,
                confirmText: "确认",
                success: function (e) {
                  wx.navigateTo({
                    url: '/pages/user/user',
                  })
                  return;
                  wx.navigateBack({
                    delta: 1
                  });
                }
              })
            } else {
              //"requestPayment:fail" != e.errMsg && "requestPayment:fail cancel" != e.errMsg ? "requestPayment:ok" == e.errMsg && wx.showModal({
              wx.showModal({
                title: "提示",
                content: "订单尚未支付" + (e.errMsg),
                showCancel: false,
                confirmText: "确认"
              });
            }
          }
        });

      }
    })

  },
  input: function (e) {
    this.setData({
      money: e.detail.value
    });
  }
});