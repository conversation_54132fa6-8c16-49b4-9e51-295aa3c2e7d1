// pages/order-detail/order-detail.js
var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    order: null,
    getGoodsTotalPrice: function() {
      return this.data.order.total_price;
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    app.pageOnLoad(this,options);
    console.log(options);
    var page = this;
    page.setData({
      store: wx.getStorageSync("store")
    });
    app.request({
      url: api.order.detail,
      data: {
        guid: options.guid,
      },
      success: function(res) {
          page.setData({
            order: res.data,
          });
      }
    });
  },

  copyText: function(e) {
    var page = this;
    var text = e.currentTarget.dataset.text;
    wx.setClipboardData({
      data: text,
      success: function() {
        wx.showToast({
          title: "已复制"
        });
      }
    });
  },
  orderConfirm: function(e) {
    var page = this;
    wx.showModal({
      title: "提示",
      content: "是否确认已收到货？",
      cancelText: "否",
      confirmText: "是",
      success: function(res) {
        if (res.cancel)
          return true;
        if (res.confirm) {
          app.request({
            url: api.order.confirm,
            data: {
              order_guid: e.currentTarget.dataset.guid,
            },
            success: function(res) {
              wx.showToast({
                title: res.msg,
              });
                wx.navigateTo({
                  url: '/pages/order/order?status=3'
                })
            }
          });
        }
      }
    });
  },
  location: function() {
    var page = this;
    // var shop = page.data.order.shop;
    // wx.openLocation({
    //   latitude: parseFloat(shop.latitude),
    //   longitude: parseFloat(shop.longitude),
    //   address: shop.address,
    //   name: shop.name
    // })
  }

});