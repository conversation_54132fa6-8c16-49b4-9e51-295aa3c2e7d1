<!--pages/order-detail/order-detail.wxml-->
<view style="overflow-x: hidden">
  <view class="status-bar">
    <image src="/images/img-order-status-bar.png" mode="aspectFill"></image>
    <text>{{order.status_text}}</text>
  </view>

  <navigator url="/pages/express-detail/express-detail?guid={{order.guid}}" class="block flex-row" wx:if="{{order.express_no && order.is_send==1}}">
    <view class="flex-grow-1">
      <view class="mb-10">快递公司：{{order.express}}</view>
      <view class="">快递单号：{{order.express_no}}</view>
      <view class="flex-grow-0">
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no}}">复制</text>
      </view>
    </view>
    <view class="flex-grow-0 flex-y-center">
      <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx"></image>
    </view>

    <view class="block-row flex-row" wx:if="{{false}}">
      <view class="flex-grow-1">快递单号：{{order.express_no}}</view>
      <view class="flex-grow-0">
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no}}">复制</text>
      </view>
    </view>
  </navigator>

  <view class="block" wx:if="{{order.express}}">
    <view class="block-row flex-row">
      <view class="flex-grow-1">快递公司：{{order.express}}</view>
    </view>
    <view class="block-row flex-row">
      <view class="flex-grow-1">快递单号：{{order.express_no}}
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no}}">复制</text>
      </view>

    </view>
  </view>

  <view class="block">
    <view class="flex-row block-row">
      <view class="flex-grow-1">
        收货人：{{order.true_name}}
      </view>
      <view class="flex-grow-0">
        {{order.mobile}}
      </view>
    </view>
    <view wx:if='{{order.address}}'>地址：{{order.province_name}}{{order.city_name}}{{order.area_name}}{{order.address}}</view>
  </view>
  <block wx:if="{{order.is_offline == 1}}">
    <view class="block flex-row">
      <view class='flex-grow-1'>
        <view class="flex-grow-1">门店名称：{{order.shop.name}}</view>
        <view class="flex-grow-1">联系电话：{{order.shop.mobile}}</view>
        <view class="flex-grow-1">门店地址：{{order.shop.address}}</view>
      </view>
      <view class='flex-grow-0 flex-y-center' wx:if='{{order.shop.longitude}}'>
        <text class='copy-text-btn' bindtap='location'>导航</text>
      </view>
    </view>
  </block>

  <view class="block">
    <view class="block-row flex-row">
      <view class="flex-grow-1">订单编号：{{order.bill_number}}</view>
      <view class="flex-grow-0">
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.bill_number}}">复制</text>
      </view>
    </view>
    <view class="flex-row block-row">
      <view class="flex-grow-1">下单时间：{{order.create_time}}</view>
    </view>

    <block wx:if="{{order.pay_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">支付时间：{{order.pay_time}}</view>
      </view>
    </block>

    <block wx:if="{{order.send_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">发货时间：{{order.send_time}}</view>
      </view>
    </block>

    <block wx:if="{{order.confirm_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">确认时间：{{order.confirm_time}}</view>
      </view>
    </block>
    <block wx:if="{{order.pay_type==0}}">
      <view class="pay-type">支付方式：未支付</view>
    </block>
    <block wx:if="{{order.pay_type==1}}">
      <view class="pay-type">支付方式：微信支付</view>
    </block>
    <block wx:if="{{order.pay_type==2}}">
      <view class="pay-type">支付方式：余额支付</view>
    </block>
  </view>
  <view class="block">
    <view class="flex-row block-row">
      <view class="flex-grow-1">商品总额</view>
      <view class="flex-grow-0" wx:if="{{order}}">￥{{order.total_money}}</view>
      <view class="flex-grow-0" wx:else>￥0.00</view>
    </view>
    <view class="flex-row block-row">
      <view class="flex-grow-1">商品数量</view>
      <view class="flex-grow-0">{{order.total_amount}}件</view>
    </view>
    <view class="flex-row block-row" wx:if='{{user_coupon_id}}'>
      <view class="flex-grow-1">优惠券优惠</view>
      <view class="flex-grow-0">-￥{{order.coupon_sub_price}}</view>
    </view>
    <view class="flex-row block-row" wx:if='{{order.discount<10 && order.discount}}'>
      <view class="flex-grow-1">会员折扣</view>
      <view class="flex-grow-0">{{order.discount}}折</view>
    </view>
    <view class="flex-row block-row" wx:if="{{order.before_update}}">
      <view class="flex-grow-1">{{order.before_update}}</view>
      <view class="flex-grow-0">￥{{order.money}}</view>
    </view>
    <!-- <view class="flex-row block-row">
      <view class="flex-grow-1">运费</view>
      <view class="flex-grow-0">￥0.00{{order.express_price}}</view>
    </view> -->
    <view wx:if='{{order.content}}'>
      <view>买家留言</view>
      <view style='width:100%;overflow:auto;word-wrap:break-word;'>{{order.content}}</view>
    </view>
    <view wx:if='{{order.words}}'>
      <view>商家留言</view>
      <view class='fs-sm' style='width:100%;overflow:auto;word-wrap:break-word;'>{{order.words}}</view>
    </view>
    <view class="block-footer">合计：
      <text style="color: #ff4544">￥{{order.total_money}}</text>
    </view>
    <view class="flex-grow-0" wx:if="{{order.status==2}}">
      <button class="order-option-btn" bindtap="orderConfirm" data-guid="{{order.guid}}">确认收货
      </button>
    </view>
  </view>

  <view class="block">
    <view wx:for="{{order.goods_list}}" class="flex-row goods-item">
      <view class="flex-grow-0">
        <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}" style="font-size: 0">
          <image mode="aspectFill" style="width: 156rpx;height: 156rpx" src="{{item.pic}}"></image>
        </navigator>
      </view>
      <view class="flex-grow-1" style="padding-left: 20rpx">
        <view style="margin-bottom: 10rpx">
          <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}">{{item.name}}</navigator>
        </view>
        <view class="flex-row">
          <view class="flex-grow-1">
            <view style="font-size: 9pt;color: #888;margin-right: 20rpx;display: inline-block" wx:for="{{item.attr}}">
              {{item.attr_group_name}}：{{item.attr_name}}
            </view>
          </view>
          <view class="flex-grow-0" style="text-align: right">
            <view>×{{item.amount}}</view>
            <view style="color: #ff4544">￥：{{item.goods_price}}</view>
          </view>
        </view>
        <view wx:if="{{item.order_refund_enable==1}}">
          <navigator wx:if="{{item.is_order_refund==0}}" class="flex-y-center refund-btn" url="/pages/order-refund/order-refund?guid={{item.order_detail_id}}">申请售后
          </navigator>
          <text class="refund-text" wx:else="{{item.is_order_refund==1}}">已申请售后</text>
        </view>
      </view>
    </view>
  </view>
</view>