// goods.js
var app = getApp();
var p = 1;
var is_loading_comment = false;
var is_more_comment = true;
var share_count = 0;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    guid: null,
    goods: {},
    data: {},
    share_note_guid: '',
    coupon_send_note_guid: '',
    share_member_guid: '',
    share_note_info: {},
    user_info: {},
    is_self: false,
    share_num: 1,
    form: {
      number: 1,
    },
    autoplay: false,
    hide: "hide",
    show: false,
    x: wx.getSystemInfoSync().windowWidth,
    y: wx.getSystemInfoSync().windowHeight - 20,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let that = this;
    app.pageOnLoad(that, options);
    console.log(wx.getSystemInfoSync());
    share_count = 0;
    p = 1;
    is_loading_comment = false;
    is_more_comment = true;
    that.setData({
      coupon_send_note_guid: options.coupon_send_note_guid,
      share_note_guid: options.share_note_guid,
    });
    that.getGoods();
    app.async_get_user_info().then((user_info) => {
      that.setData({
        is_self: options.share_member_guid == user_info.guid || !options.share_member_guid,
        user_info: user_info
      });
      console.log("options=>" + JSON.stringify(options));
      // var scene = decodeURIComponent(options.scene);
      //page.getCommentList();
    });
  },
  get_share_note_info: function () {
    var page = this;
    if (!page.data.share_note_guid) {
      return;
    }
    app.request({
      url: app.api.code.get_share_note_info,
      data: {
        share_note_guid: page.data.share_note_guid
      },
      success: function (res) {
        page.setData({
          share_note_info: res.data,
        });
      }
    });
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    console.log('onShareAppMessage');
    // 这需要构造分享的 url
    const promise = new Promise(resolve => {
      var page = this;
      app.request({
        url: app.api.code.get_share_note_guid,
        data: {
          coupon_send_note_guid: page.data.coupon_send_note_guid,
          share_num: page.data.share_num
        },
        success: function (res) {
          page.setData({
            share_note_guid: res.data.share_note_guid
          });
          let path = "/pages/code_note_detail/index?coupon_send_note_guid=" + page.data.coupon_send_note_guid + '&share_note_guid=' + res.data.share_note_guid + '&share_member_guid=' + res.data.share_member_guid
          let title = (page.data.user_info.name ? page.data.user_info.name : '') + '送您' + page.data.share_num + '张 ' + page.data.goods.name + ' 快收下吧~';
          //  let share_obj = app.pageOnShareAppMessage(page, options, title, path, page.data.goods.pic);
          console.log('share_obj');
          console.log(share_obj);
          let share_obj = {
            title: title, // 默认是小程序的名称(可以写slogan等)
            path: path, // 默认是当前页面，必须是以‘/'开头的完整路径
            imageUrl: page.data.goods.pic, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
          };
          resolve(share_obj)
        }
      });
    })
    return {
      title: '',
      path: '/pages/index/index',
      promise
    }
  },
  receive_share_code: function () {
    var page = this;
    app.request({
      url: app.api.code.receive_share_code,
      data: {
        share_note_guid: page.data.share_note_guid,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          // image: '/images/tan.png', //自定义图标的本地路径，image 的优先级高于 icon
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {
            setTimeout(function () {
              wx.navigateTo({
                url: '/pages/code_list/index',
              })
            }, 1000) //延迟时间
          },
          fail: function () {},
          complete: function () {}
        })
      }
    });
  },
  getGoods: function () {
    var page = this;
    app.request({
      url: app.api.code.code_note_detail,
      data: {
        coupon_send_note_guid: page.data.coupon_send_note_guid
      },
      success: function (res) {
        page.setData({
          data: res.data,
          goods: res.data.coupon_info
        });
        wx.setNavigationBarTitle({
          title: res.data.coupon_info.name
        })
      }
    });
  },

  onGoodsImageClick: function (e) {
    var page = this;
    var urls = [];
    var index = e.currentTarget.dataset.index;
    console.log(index);
    console.log(page.data.goods.pic_list);
    for (var i in page.data.goods.pic_list) {
      urls.push(page.data.goods.pic_list[i]);
      // urls.push(page.data.goods.pic_list[i].pic_url); 这种可以用于播放视频
    }
    console.log(urls);
    console.log(urls[index]);

    wx.previewImage({
      urls: urls, // 需要预览的图片http链接列表
      current: urls[index],
    });
  },




  addCart: function () {
    this.submit('ADD_CART');
  },

  buyNow: function () {
    this.submit('BUY_NOW');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.get_share_note_info();
    this.shareModalClose();
    // wx.showModal({
    //   title: '提示',
    //   content: '卡券领取成功',
    //   showCancel: true, //是否显示取消按钮-----》false 去掉取消按钮
    //   cancelText: "卡券列表", //默认是“取消”
    //   // cancelColor: 'skyblue', //取消文字的颜色
    //   confirmText: "立即使用", //默认是“确定”
    //   // confirmColor: 'skyblue', //确定文字的颜色
    //   success: function (res) {
    //     if (res.cancel) {
    //       //点击取消
    //       console.log("您点击了取消")
    //     } else if (res.confirm) {
    //       //点击确定
    //       console.log("您点击了确定")
    //     }
    //   }
    // })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var page = this;
    // page.getCommentList(true);
  },
  play: function (e) {
    var url = e.target.dataset.url; //获取视频链接
    this.setData({
      url: url,
      hide: '',
      show: true,
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.play();
  },

  close: function (e) {
    if (e.target.id == 'video') {
      return true;
    }
    this.setData({
      hide: "hide",
      show: false
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.pause();
  },
  hide: function (e) {
    if (e.detail.current == 0) {
      this.setData({
        img_hide: ""
      });
    } else {
      this.setData({
        img_hide: "hide"
      });
    }
  },

  showShareModal: function () {
    var page = this;
    page.setData({
      share_modal_active: "active",
      no_scroll: true,
    });
  },

  shareModalClose: function () {
    var page = this;
    page.setData({
      share_modal_active: "",
      no_scroll: false,
    });
  },

  getGoodsQrcode: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "active",
      share_modal_active: "",
    });
    console.log('aaaa');
    if (page.data.goods_qrcode)
      return true;

    app.request({
      url: app.api.code.share_qrcode,
      data: {
        goods_guid: page.data.guid,
      },
      success: function (res) {
        page.setData({
          goods_qrcode: res.data.pic_url,
        });
      },
      fail: function (res) {
        page.goodsQrcodeClose();
        // wx.showModal({
        //   title: "提示",
        //   content: res.msg,
        //   showCancel: false,
        //   success: function (res) {
        //     if (res.confirm) {

        //     }
        //   }
        // });
      }
    });
  },

  goodsQrcodeClose: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "",
      no_scroll: false,
    });
  },

  saveGoodsQrcode: function () {
    app.utils.saveImageToPhotosAlbum(this.data.goods_qrcode);
  },

  goodsQrcodeClick: function (e) {
    var src = e.currentTarget.dataset.src;
    wx.previewImage({
      urls: [src],
    });
  },
  closeCouponBox: function (e) {
    this.setData({
      get_coupon_list: ""
    });
  },
  numberSub: function () {
    var page = this;
    var num = page.data.share_num;
    if (num <= 1)
      return true;
    num--;
    page.setData({
      share_num: num
    });
  },
  numberAdd: function () {
    var page = this;
    var num = page.data.share_num;
    num++;
    if (num > page.data.data.available_num) {
      //超过最大数量
      return;
    }
    page.setData({
      share_num: num
    });
  },

  numberBlur: function (e) {
    var page = this;
    var num = e.detail.value;
    num = parseInt(num);
    if (isNaN(num)) {
      num = 1;
    }
    if (num <= 0) {
      num = 1;
    }
    if (num > page.data.data.available_num) {
      //超过最大数量
      num = page.data.data.available_num;
    }
    page.setData({
      share_num: num
    });
    return num;
  },
});