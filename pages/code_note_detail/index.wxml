<!--goods.wxml-->
<view class="{{(show_attr_picker||show||no_scroll)?'no-scroll':''}}">

  <view style="padding-bottom: 120rpx">
    <swiper class="goods-image-swiper" autoplay="{{autoplay}}" interval="5000" duration="300" indicator-dots="true" circular="true" bindchange="hide">
      <block wx:for="{{goods.pic_list}}">
        <swiper-item>
          <block wx:if="{{goods.video_url}}">
            <image class="play {{img_hide}}" src="/images/video-play.png" data-url="{{goods.video_url}}" bindtap="play"></image>
          </block>
          <image bindtap="onGoodsImageClick" data-index="{{index}}" class="goods-image" src="{{item}}" mode="aspectFill" />
        </swiper-item>
      </block>
    </swiper>

    <view class="goods-info" style="background: #fff;padding:32rpx 24rpx;">
      <view style="font-weight: bold;margin-bottom: 24rpx;line-height: 1.7">{{goods.name}}</view>
      <view class="flex-row flex-y-center">
        <view class="flex-grow-1 flex-row flex-y-bottom">
          <block>

            <view class="flex-grow-0">
              <text style="font-size: 36rpx;color: #999;margin-right: 32rpx">券号</text>
              <text style="font-size: 48rpx;font-weight: bold;color: #ff4544;margin-right: 32rpx">{{data.code}}</text>
            </view>

            <view class=" flex-grow-0" style="margin-right: 32rpx" wx:if="{{goods.discount_selling_price<goods.selling_price}}">
              <text style="font-size: 9pt;color: #888;">市场价: </text>
              <text style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.selling_price}}</text>
            </view>
          </block>
          <view class="flex-grow-0" style="margin-right: 32rpx">
            <text style="font-size: 9pt;color: #888;">{{goods.sales_volume}} {{goods.unit}}</text>
          </view>
        </view>
        <view class="flex-grow-0" style="text-align: center;" wx:if="{{ is_self && goods.share_status==1 && data.available_num>0}}">
          <view bindtap="showShareModal" class="share-btn" plain="true">
            <image style="width: 40rpx;height: 40rpx" src="/images/icon-share.png" />
            <view style="color: #888">转赠给朋友</view>
          </view>
        </view>
      </view>
    </view>

    <view class="weui-cell" style="padding:20rpx 20rpx 10rpx 20rpx;" wx:if="{{is_self==true && (data.available_num>0||data.available_value>0) }}">
      <block wx:if="{{data.exchange_goods_type==1}}">
        <view class="weui-cell__bd">
          可用次数
        </view>
        <view class="weui-cell__ft">{{data.available_num}} 次</view>
      </block>

      <block wx:if="{{data.exchange_goods_type==2}}">
        <view class="weui-cell__bd">
          可用余额
        </view>
        <view class="weui-cell__ft">{{data.available_value}} 元</view>
      </block>

    </view>

    <view class="weui-cell" style="padding:20rpx 20rpx 10rpx 20rpx;" wx:if="{{is_self==false}}">
      <block wx:if="{{data.exchange_goods_type==1}}">
        <view class="weui-cell__bd">
          赠送次数
        </view>
        <view class="weui-cell__ft">{{share_note_info.share_num}} 次</view>
      </block>
    </view>
    <view class="weui-cell" style="padding:20rpx 20rpx 10rpx 20rpx;">
      <view class="weui-cell__bd">
        有效期
      </view>
      <view class="weui-cell__ft">{{data.availability_time}} - {{data.expire_time}}</view>
    </view>

    <view>
      <rich-text nodes="{{goods.description}}"></rich-text>
    </view>
  </view>


  <view class="flex-row bar-bottom">
    <view class="flex-grow-0 flex-row">

      <!--     
      <view class="flex-grow-0 flex-y-center bar-bottom-btn">
        <button open-type="contact" plain="true" style="padding: 0;background-color: #fff;">
          <image src="/images/icon-user-kf.png"></image>
          <text>客服</text>
        </button>
      </view> -->

      <!-- <navigator class="flex-grow-0 flex-y-center bar-bottom-btn" url="/pages/index/index" open-type="redirect">
        <view>
          <image src="/images/icon-store.png"></image>
          <text>首页</text>
        </view>
      </navigator> -->

    </view>

    <view class="flex-grow-1 flex-row">
      <!-- <view class="flex-grow-1 flex-y-center flex-x-center add-cart" bindtap="addCart">加入购物车</view> -->
      <!-- <view class="flex-grow-1 flex-y-center flex-x-center buy-now" bindtap="buyNow">立即领取</view> -->

      <block wx:if="{{is_self==true}}">
        <view wx:if="{{goods.share_status==1 && data.available_num>0 && !share_note_guid}}" class="flex-grow-1 flex-y-center flex-x-center buy-now" bindtap="showShareModal">转赠好友</view>
        <view wx:if="{{share_note_info.status==0 }}" class="flex-grow-1 flex-y-center flex-x-center buy-now">等待好友领取中</view>
        <view wx:if="{{share_note_info.status==1 }}" class="flex-grow-1 flex-y-center flex-x-center buy-now">已被好友领取</view>

      </block>

      <block wx:if="{{is_self==false}}">

        <view wx:if="{{share_note_info.status==0}}" class="flex-grow-1 flex-y-center flex-x-center add-cart" bindtap="receive_share_code">立即领取</view>

        <navigator wx:if="{{share_note_info.status==1 && share_note_info.receive_member_guid==user_info.guid}}" url="/pages/code_list/index" class="flex-grow-1 flex-y-center flex-x-center add-cart">已领取,查看卡券列表</navigator>

        <navigator wx:if="{{share_note_info.status==1 && share_note_info.receive_member_guid!=user_info.guid}}" url="/pages/code_list/index" class="flex-grow-1 flex-y-center flex-x-center add-cart">您来晚啦,查看卡券列表</navigator>

      </block>


    </view>
  </view>
  <block wx:if="{{store.show_customer_service && store.show_customer_service==1}}">
    <include src="/common/float-icon/float-icon.wxml" />
  </block>
</view>

<view class='modal flex-row {{hide}}' bindtap='close'>
  <view class='flex-y-center' style='width:100%;'>
    <video src='{{url}}' id="video" autoplay="true"></video>
  </view>
</view>

<view class="share-modal {{share_modal_active}}">
  <view class="share-modal-body" style="background-color: #fff;">
    <view class="flex-row">
      <view class="flex-grow-1 flex-x-center">
        <view style="padding-top: 10rpx;text-align: center;">
          <view class="flex-grow-1">转赠数量(可用{{data.available_num}}张)</view>
          <view class="flex-grow-0">
            <view class="flex-row number-input-box">
              <view class="flex-grow-0 flex-x-center flex-y-center number-btn number-sub {{share_num<=1?'disabled':''}}" bindtap="numberSub">-
              </view>
              <view class="flex-grow-0">
                <input class="flex-grow-1 number-input" value="{{share_num}}" type="number" step="1" min="1" bindblur="numberBlur" bindinput="numberBlur" />
              </view>
              <view class="flex-grow-0 flex-x-center flex-y-center number-btn number-add" bindtap="numberAdd">+
              </view>
            </view>
          </view>
        </view>

      </view>

      <view class="flex-grow-1 flex-x-center">

        <button open-type="share" class="share-bottom">
          <image src="/images/icon-share-friend.png"></image>
          <view>转赠给朋友</view>
        </button>

      </view>
      <!-- <view class="flex-grow-1 flex-x-center">
        <view bindtap="getGoodsQrcode" class="share-bottom">
          <image src="/images/icon-share-qrcode.png"></image>
          <view>生成商品海报</view>
        </view>
      </view> -->
    </view>
    <view bindtap="shareModalClose" class="share-modal-close flex-y-center flex-x-center">取消</view>
  </view>
</view>

<view class="goods-qrcode-modal {{goods_qrcode_active}}">
  <view class="goods-qrcode-body flex-col">
    <view class="flex-grow-1" style="position: relative">
      <view style="position: absolute;left: 0;top:0;width: 100%;height: 100%;padding: 100rpx 100rpx 60rpx">
        <view class="goods-qrcode-box">
          <view class="goods-qrcode-loading flex-x-center flex-y-center">
            <view class="flex-x-center flex-col">
              <image style="width: 150rpx;height: 150rpx" src="/images/loading2.svg"></image>
              <view style="color: #888">海报生成中</view>
            </view>
          </view>
          <image bindtap="goodsQrcodeClick" mode="aspectFill" class="goods-qrcode {{goods_qrcode?'active':''}}" data-src="{{goods_qrcode}}" src="{{goods_qrcode}}"></image>
        </view>
      </view>
    </view>
    <view class="flex-grow-0 flex-col flex-x-center" style="padding: 0 60rpx 80rpx">
      <view style="margin-bottom: 20rpx;padding: 0 40rpx">
        <button wx:if="{{goods_qrcode}}" bindtap="saveGoodsQrcode" style="background: #ff4544;color: #fff;">
          保存图片
        </button>
        <button wx:else style="opacity: .4">保存图片</button>
      </view>
      <view style="color: #888;font-size: 9pt;text-align: center">保存至相册</view>
    </view>
    <view class="goods-qrcode-close" bindtap="goodsQrcodeClose">
      <image src="/images/icon-close2.png" style="width: 50rpx;height: 50rpx;display: block"></image>
    </view>
  </view>
</view>