// pages/member/info.js
var app = getApp(); //获取应用实例
var that;
// var WxParse = require('../../wxParse/wxParse.js');
Page({
  /**
   * 页面的初始数据
   */
  data: {
    member_info: {},
    user_info: {},
    score_note: {},
    last_month_avg_score: 0,
    this_month_avg_score: 0,
    last_month_star: 0,
    last_month_star_desc: null,
    this_month_star: 0,
    this_month_star_desc: null,
    member_guid: null,
    recommend_member_name: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log(options);
    //app.editTabBar();
    app.pageOnLoad(this, options);

    that = this;
    this.setData({
      member_guid: options.member_guid,
    })
    this.updateUserInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    console.log('分享完成');
  },
  showMemberInfo: function (member_guid) {
    app.request({
      url: app.api.scores.member_detail,
      data: {
        guid: member_guid
      },
      success: function (res) {
        let data = res.data;
        that.setData({
          member_info: data.member_info,
          score_note: data.score_note,
          last_month_avg_score: data.last_month_avg_score,
          this_month_avg_score: data.this_month_avg_score,
          last_month_star: data.last_month_star,
          last_month_star_desc: data.last_month_star_desc,
          this_month_star: data.this_month_star,
          this_month_star_desc: data.this_month_star_desc,
          recommend_member_name: data.recommend_member_name,
        })
      }
    });
  },
  to_submit_page: function () {
    wx.navigateTo({
      url: '/pages/score/submit?member_guid=' + this.data.member_guid
    })
    return;
  },
  to_qrcode_page: function () {
    wx.navigateTo({
      url: '/pages/member/qrcode?member_guid=' + this.data.member_guid
    })
    return;
  },
  returnPre: function () {
    var cps = getCurrentPages();
    if (cps.length > 1) {
      wx.navigateBack({
        delta: 1,
      })
    } else {
      wx.navigateTo({
        url: '/pages/index/index'
      })
    }
    return;
  },
  updateUserInfo: function () {
    app.request({
      url: app.api.user.info,
      data: {},
      success: function (res) {
        app.cache.set("user_info", res.data, 3600 * 24);
        that.setData({
          user_info: res.data
        })
        that.showMemberInfo(that.data.member_guid);
        // wx.navigateBack({
        //   delta: 1,
        // })
      }
    })
  },
})