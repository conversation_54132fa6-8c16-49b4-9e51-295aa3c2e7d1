// pages/member/qrcode.js
var app = getApp(); //获取应用实例
var that;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    width: 0,
    height: 0,
    qrcode_src: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    app.pageOnLoad(this,options);

    that = this;
    this.setData({
      member_guid: options.member_guid
    })
    this.showQrcode(options.member_guid);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  },
  //点击图片进行预览，长按保存分享图片
  previewImg: function(e) {
    var img = this.data.qrcode_src;
    console.log(img);
    wx.previewImage({
      current: img, // 当前显示图片的http链接
      urls: [img] // 需要预览的图片http链接列表
    })
  },
  showQrcode: function(member_guid) {
    app.request({
      url: app.api.scores.member_qrcode,
      data: {
        guid: member_guid
      },
      success: function(res) {
        that.setData({
          qrcode_src: res.data.src,
        })
      }
    });
  },
  downloadCode: function() {
    var imageUrl = this.data.qrcode_src
    wx.downloadFile({
      url: imageUrl,
      success: function(res) {
        console.log(res);
        //图片保存到本地
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: function(data) {
            console.log("保存到相册", data);
            wx.showToast({
              title: '保存成功',
              icon: "success"
            })
          },
          fail: function(err) {
            console.log(err)
          }
        })
      }
    })
  },
  //图片宽高处理

  imgLoad: function(e) {
    return;
    var that = this;
    console.info(e.detail);
    var imgwidth = e.detail.width;
    var imgheight = e.detail.height;
    var width, height;
    var ratio;
    if (imgwidth > imgheight) {
      ratio = imgwidth / 160;
      width = 160;
      height = imgheight / ratio;
    } else {
      ratio = imgheight / 160;
      height = 160;
      width = imgwidth / ratio;
    }
    console.info("计算后的宽高", width, height);
    that.setData({
      width: width,
      height: height
    })
  },
  returnPre: function() {
    var cps = getCurrentPages();
    if (cps.length > 1) {
      wx.navigateBack({
        delta: 1,
      })
    } else {
      wx.navigateTo({
        url: '/pages/index/index'
      })
    }
    return;
  }
})