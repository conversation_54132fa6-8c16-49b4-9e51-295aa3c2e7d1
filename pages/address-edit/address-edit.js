// pages/address-edit/address-edit.js
var api = require('../../api.js');
var app = getApp();
Page({
  data: {
    guid: '',
    true_name: '',
    mobile: '',
    address: '',
    province_id: 0,
    city_id: 0,
    area_id: 0,
    show_area_picker: false,
  },
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    page.setData({
      guid: options.guid,
    });
    if (options.guid) {
      app.request({
        url: api.address.detail,
        data: {
          guid: options.guid,
        },
        success: function (result) {
          page.setData(result.data);
          page.setData({
            show_area_picker: true
          });
          //let area_picker = page.selectComponent('.area_picker');
          // area_picker.getProvince(1, result.data.province_id, result.data.city_id, result.data.area_id);
        }
      });
    } else {
      page.setData({
        show_area_picker: true
      });
    }
  },
  get_mobile(e) {
    let that = this;
    return app.auth.getPhoneNumber(e, function (result) {
      that.setData({
        mobile: result.data.user_info.mobile,
      });
      wx.setStorageSync('user_info', result.data.user_info);
    });
  },
  saveAddress: function () {
    var page = this;
    // var myreg = /^([0-9]{6,12})$/;
    // var myreg2 = /^(\d{3,4}-\d{6,9})$/;
    // console.log(myreg2.test(page.data.mobile));
    // if (!myreg.test(page.data.mobile) && !myreg2.test(page.data.mobile)) {
    //   wx.showToast({
    //     title: "联系电话格式不正确",
    //     image: "/images/icon-warning.png",
    //   });
    //   return false;
    // }
    let area_picker = this.selectComponent('.area_picker');
    let url = page.data.guid ? api.address.edit : api.address.add;
    app.request({
      url: url,
      data: {
        guid: page.data.guid || "",
        true_name: page.data.true_name,
        mobile: page.data.mobile,
        province_id: area_picker.data.selectProvinceId,
        city_id: area_picker.data.selectCityId,
        area_id: area_picker.data.selectAreaId,
        address: page.data.address,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          // image: '/images/tan.png', //自定义图标的本地路径，image 的优先级高于 icon
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {
            setTimeout(function () {
              wx.navigateBack({
                delta: 1
              })
            }, 1000) //延迟时间
          },
          fail: function () {},
          complete: function () {}
        })
      }
    });
  },

  inputBlur: function (e) {
    //console.log(JSON.stringify(e));
    var name = e.currentTarget.dataset.name;
    var value = e.detail.value;
    //var data = '{"form":{"' + name + '":"' + value + '"}}';
    var data = '{"' + name + '":"' + value + '"}';
    this.setData(JSON.parse(data));
  },

  getWechatAddress: function (e) {
    var page = this;
    wx.chooseAddress({
      success: function (e) {
        if (e.errMsg != 'chooseAddress:ok')
          return;
        console.log(e);
        page.setData({
          true_name: e.userName,
          mobile: e.telNumber,
          address: e.detailInfo,
        });
        app.request({
          url: api.user.get_area_id_from_wechat,
          data: {
            national_code: e.nationalCode,
            province_name: e.provinceName,
            city_name: e.cityName,
            area_name: e.countyName,
          },
          success: function (res) {
            let result = res.data;
            page.setData(result);
            let area_picker = page.selectComponent('.area_picker');
            area_picker.getProvince(1, result.province_id, result.city_id, result.area_id);
          }
        });
      }
    });
  },

  onReady: function () {

  },
  onShow: function () {

  },
});