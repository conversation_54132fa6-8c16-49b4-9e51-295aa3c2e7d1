<!--pages/address-edit/address-edit.wxml-->
<view class="bottom-bar">
  <navigator bindtap="saveAddress">保存</navigator>
</view>
<view>
  <view class="flex-row flex-y-center edit-row">
    <view class="flex-grow-0 row-label">姓名</view>
    <view class="flex-grow-1">
      <input placeholder="请输入姓名" value="{{true_name}}" data-name="true_name" bindinput="inputBlur" />
    </view>
  </view>

  <view class="flex-row flex-y-center edit-row">
    <view class="flex-grow-0 row-label">手机号</view>
    <view class="flex-grow-1">
      <input placeholder="请输入手机号" value="{{mobile}}" data-name="mobile" type="number" bindinput="inputBlur" />
    </view>
    <button aria-role="button" style="width: 200rpx;" open-type="getPhoneNumber" bindgetphonenumber="get_mobile" class="weui-btn weui-btn_default weui-vcode-btn">获取</button>

  </view>

  <view class="flex-row flex-y-center edit-row">
    <view class="flex-grow-1 flex-row">
      <area_picker wx:if="{{show_area_picker}}" province_id="{{province_id}}" city_id="{{city_id}}" area_id="{{area_id}}" class="area_picker"></area_picker>
    </view>
  </view>

  <view class="flex-row flex-y-center edit-row">
    <view class="flex-grow-0 row-label">详细地址</view>
    <view class="flex-grow-1">
      <input placeholder="请输入详细地址" value="{{address}}" data-name="address" bindinput="inputBlur" />
    </view>
  </view>

  <view bindtap="getWechatAddress" class="get-wechat-address">一键获取微信地址</view>

</view>