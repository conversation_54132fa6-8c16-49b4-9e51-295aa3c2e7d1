// pages/cash/cash.js
var api = require('../../api.js');
var app = getApp();

function min(var1, var2) {
  var1 = parseFloat(var1);
  var2 = parseFloat(var2);
  return var1 > var2 ? var2 : var1;
}

Page({

  /**
   * 页面的初始数据
   */
  data: {
    member_brokerage: 0, //账户可用余额
    total_auditing_cash_brokerage_money: 0, //审核中提现金额
    available_cash_brokerage_money: 0, //可提现金额
    cash_max_day: -1,
    pay_type: '',
    cash_money: '',
    share_setting: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  get_share_setting: function () {
    var page = this;
    app.request({
      url: api.share.get_share_setting,
      success: function (result) {
        page.setData({
          share_setting: result.data
        });
      }
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var page = this;
    page.get_share_setting();
    app.request({
      url: api.share.get_cash_info,
      success: function (res) {
        if (res.data.last_cash_note) {
          page.setData({
            pay_type: res.data.last_cash_note['pay_type'],
          });
        }
        page.setData(res.data);
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  cash_all_money: function (e) {
    this.setData({
      cash_money: this.data.available_cash_brokerage_money
    });
  },
  formSubmit: function (e) {
    var page = this;
    // var cash = parseFloat(parseFloat(e.detail.value.cash).toFixed(2));
    // var cash_max = page.data.price;
    // if (page.data.cash_max_day != -1) {
    //   cash_max = min(cash_max, page.data.cash_max_day)
    // }
    // if (cash > cash_max) {
    //   wx.showToast({
    //     title: "提现金额不能超过" + cash_max + "元",
    //     image: "/images/icon-warning.png",
    //   });
    //   return;
    // }
    // if (cash < parseFloat(page.data.share_setting.min_money)) {
    //   wx.showToast({
    //     title: "提现金额不能低于" + page.data.share_setting.min_money + "元",
    //     image: "/images/icon-warning.png",
    //   });
    //   return;
    // }
    var data = e.detail.value;
    console.log(data);
    data.pay_type = page.data.pay_type;
    app.request({
      url: api.share.apply,
      data: data,
      success: function (res) {
        wx.showModal({
          title: "提示",
          content: res.msg,
          showCancel: false,
          success: function (e) {
            if (e.confirm) {
              wx.redirectTo({
                url: '/pages/cash-detail/cash-detail',
              })
            }
          }
        });
      }
    });
  },

  showCashMaxDetail: function () {
    wx.showModal({
      title: "提示",
      content: "今日剩余提现金额=平台每日可提现金额-今日所有用户提现金额"
    });
  },
  select: function (e) {
    var pay_type = e.currentTarget.dataset.pay_type;
    this.setData({
      pay_type: pay_type
    });
  }

});