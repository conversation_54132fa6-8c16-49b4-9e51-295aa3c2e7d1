<form report-submit='true' bindsubmit="formSubmit">
  <view class="block">
    <view class="user-money">账户可用余额：{{member_brokerage}}元</view>
    <view wx:if="{{cash_max_day!=-1}}" class="flex-row cash-max-day">
      <view class="flex-y-center">今日剩余提现金额{{cash_max_day}} 元</view>
      <view class="cash-max-detail" bindtap="showCashMaxDetail">详情</view>
    </view>
  </view>
  <view class="block" wx:if="{{total_auditing_cash_brokerage_money>0}}">
    <view class="user-money">审核中金额：{{total_auditing_cash_brokerage_money}}元</view>
  </view>

  <view class="flex-row block">
    <view class="flex-grow-0 flex-y-center cash-cny">￥</view>
    <view class="flex-grow-1 flex-y-center">
      <input type="digit" name="cash_money" value="{{cash_money}}" class="cash-input" placeholder="输入提现金额,可提({{available_cash_brokerage_money}}元)" placeholder-style="color:#bbb;font-size:13pt" />
    </view>
    <view bindtap="cash_all_money">全部提现</view>
  </view>
  <view class="block" style="background: none;border: none" wx:if="{{share_setting.min_money>0}}">
    <view class="cash-min">提现金额不能小于{{share_setting.min_money}}元</view>
  </view>

  <view class='block'>
    <view class='pay-title'>提现方式</view>
    <view class='flex-row'>
      <view class='flex-grow-0' bindtap='select' data-pay_type='wechat' wx:if='{{pay_type_list.wechat==true}}'>
        <view class='pay flex-row {{pay_type=="wechat"?"active":""}}'>
          <view class='flex-grow-0'>
            <image src='/images/icon-share-wechat.png' class='pay-img'></image>
          </view>
          <view class='flex-grow-0'>微信</view>
          <image src='/images/icon-share-selected.png' class='selected' wx:if='{{pay_type=="wechat"}}'></image>
        </view>
      </view>
      <view class='flex-grow-0' style='margin-left:32rpx;' bindtap='select' data-pay_type='alipay' wx:if='{{pay_type_list.alipay==true}}'>
        <view class='pay flex-row  {{pay_type=="alipay"?"active":""}}'>
          <view class='flex-grow-0'>
            <image src='/images/icon-share-ant.png' class='pay-img'></image>
          </view>
          <view class='flex-grow-0'>支付宝</view>
          <image src='/images/icon-share-selected.png' class='selected' wx:if='{{pay_type=="alipay"}}'></image>
        </view>
      </view>

      <view class='flex-grow-0' style='margin-left:32rpx;' bindtap='select' data-pay_type='bank' wx:if='{{pay_type_list.bank==true}}'>
        <view class='pay flex-row  {{pay_type== "bank" ?"active":""}}'>
          <view class='flex-grow-0'>
            <image src='/images/icon-share-bank.png' class='pay-img'></image>
          </view>
          <view class='flex-grow-0'>银行卡</view>
          <image src='/images/icon-share-selected.png' class='selected' wx:if='{{pay_type== "bank" }}'></image>
        </view>
      </view>

    </view>
  </view>
  <block wx:if="{{pay_type == 'wechat'}}">
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>姓名</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入微信姓名' placeholder-style='color:#ccc;font-size:13pt' name="wechat_true_name" value='{{last_cash_note.wechat_true_name}}'></input>
      </view>
    </view>
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>微信号</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入微信号' placeholder-style='color:#ccc;font-size:13pt' name="wechat_account" value='{{last_cash_note.wechat_account}}'></input>
      </view>
    </view>
  </block>

  <block wx:elif="{{pay_type == 'alipay'}}">
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>姓名</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入支付宝姓名' placeholder-style='color:#ccc;font-size:13pt' name="alipay_true_name" value='{{last_cash_note.alipay_true_name}}'></input>
      </view>
    </view>
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>账号</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入支付宝账号' placeholder-style='color:#ccc;font-size:13pt' name="alipay_account" value='{{last_cash_note.alipay_account}}'></input>
      </view>
    </view>
  </block>
  <block wx:elif="{{pay_type == 'bank'}}">
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>开户人姓名</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入开户人姓名' placeholder-style='color:#ccc;font-size:13pt' name="bank_true_name" value='{{last_cash_note.bank_true_name}}'></input>
      </view>
    </view>
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>银行名称</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入银行名称(如建设银行)' placeholder-style='color:#ccc;font-size:13pt' name="bank_name" value='{{last_cash_note.bank_name}}'></input>
      </view>
    </view>
    <view class='block flex-row' style='margin-top:20rpx'>
      <view class='flex-grow-0 flex-y-center required'>银行卡号</view>
      <view class='flex-grow-1 flex-y-center'>
        <input class='cash-input' placeholder='请输入银行卡账号' placeholder-style='color:#ccc;font-size:13pt' name="bank_account" value='{{last_cash_note.bank_account}}'></input>
      </view>
    </view>
  </block>
  <view class="block" style="background: none;border: none;margin-top:68rpx;">
    <button class="cash-btn" formType="submit">提交申请</button>
  </view>
</form>