<!--order-submit.wxml-->
<view class="bottom-bar flex-row">
  <view class="flex-y-center flex-grow-1" style="padding: 0 24rpx">
    <view class="flex-grow-1" style="color:#ff4544 ">
      <!-- <block wx:if="{{picker_coupon}}">
                <block wx:if="{{total_price-picker_coupon.sub_price<0.01}}">总计:￥{{0.01+express_price}}</block>
                <block wx:else>总计:￥{{new_total_price+express_price}}</block>
            </block>
            <block wx:else>
                <block wx:if="{{total_price_1<0.01}}">总计:￥{{0.01+express_price}}</block>
                <block wx:else>总计:￥{{total_price_1+express_price}}</block>
            </block> -->
      <block wx:if="{{paid_wechat>0}}">
        还需支付 ￥ {{paid_wechat}}
      </block>
      <block wx:else>
        总计:￥{{total_price}}
      </block>
    </view>
  </view>
  <view class="flex-y-center flex-grow-0 flex-x-center submit-btn" bindtap="submit">提交订单</view>
</view>

<view style="padding-bottom: 129rpx" class="{{show_card?'no-scroll':''}} {{(show_store || show_coupon_picker || show_card) ?'hidden':''}}">
  <block wx:if="{{config.is_show_request_send_or_pick_up_store!=0}}">
    <view class='flex-row flex-y-center' style='background-color:#fff;padding:0 24rpx;border-bottom:1rpx #eee solid;border-top:1rpx #eee solid;height:90rpx;'>
      <view wx:if="{{config.is_show_address==1}}" class='flex-grow-1 flex-x-center' style='height:100%;'>
        <view class='flex-y-center {{type == 1 ?"active":""}}' style='height:100%;' bindtap='show_address'>快递配送
        </view>
      </view>
      <view wx:if="{{config.is_show_request_send_or_pick_up_store==1}}" class='flex-grow-1 flex-x-center' style='height:100%;'>
        <view class='flex-y-center {{type == 2 ?"active":""}}' style='height:100%;' bindtap='show_pick_up_store'>到店自提
        </view>
      </view>
    </view>
  </block>

  <navigator wx:if='{{type==1}}' url="/pages/address-picker/address-picker" class="flex-row address-picker">
    <block wx:if="{{address}}">
      <view class="flex-grow-1">
        <view class="flex-row" style="margin-bottom: 20rpx">
          <view class="flex-grow-1">收货人：{{address.true_name}}</view>
          <view class="flex-grow-0">{{address.mobile}}</view>
        </view>
        <view>收货地址：{{address.province_name}}{{address.city_name}}{{address.area_name}}{{address.address}}</view>
      </view>
    </block>
    <block wx:else>
      <view class="flex-grow-1">
        <view style="vertical-align: center;" class="flex-y-center">
          <image style="width: 48rpx; height: 48rpx;margin-left: 24rpx;margin-right: 30rpx;" src="/images/icon-user-dz.png" />
          请选择收货地址
        </view>
      </view>
    </block>
    <view class="flex-grow-0 flex-y-center">
      <image style="width: 12rpx; height: 22rpx;margin-left: 24rpx" src="/images/icon-jiantou-r.png" />
    </view>
  </navigator>

  <block wx:if="{{type==2}}">
    <view class="flex-row address-picker">
      <view class="flex-grow-1">
        <view class="flex-row" style="margin-bottom: 20rpx">
          <view class='flex-y-center'>提货人：</view>
          <view>
            <input placeholder='请输入提货人姓名' value="{{true_name}}" bindinput='KeyTrueName'></input>
          </view>
        </view>
        <view class="flex-row">
          <view class='flex-y-center'>联系电话：</view>
          <view>
            <input placeholder='请输入提货人电话' value="{{mobile}}" bindinput='KeyMobile'></input>
          </view>
        </view>
      </view>
    </view>
    <view class="flex-row address-picker" bindtap='showStorePicker'>
      <view class="flex-grow-1">
        <view class="flex-row" style="margin-bottom: 20rpx">
          <view class="flex-grow-1">门店名称：{{store.store_name || '请选择自提门店'}}</view>
        </view>
        <view class="flex-row" style="margin-bottom: 20rpx" wx:if="{{store}}">
          <view class="flex-grow-1">门店电话：{{store.mobile}}</view>
        </view>
        <view wx:if="{{store}}">自提门店：{{store.address_info}}</view>
      </view>
      <view class="flex-grow-0 flex-y-center">
        <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx;margin-left: 12rpx"></image>
      </view>
    </view>
  </block>
  <block wx:if='{{form.is_form == 1 && form.list.length>0}}'>
    <view class='address-picker' style='padding:0 24rpx;'>
      <view class='form-title'>{{form.name?form.name:"表单信息"}}</view>
      <view class='form-list'>
        <block wx:for='{{form.list}}'>
          <view class='form-one flex-row'>
            <view class='flex-grow-0 list-name {{item.type=="text"?"flex-y-center":""}} {{item.required == 1?"required":""}}'>{{item.name}}</view>
            <block wx:if='{{item.type == "text"}}'>
              <view class='flex-grow-1 flex-y-center'>
                <input type='text' placeholder='{{item.tip}}' bindInput='formInput' bindConfirm='formInput' bindblur='formInput' data-index='{{index}}' value='{{item.default}}'></input>
              </view>
            </block>
            <block wx:if='{{item.type == "textarea"}}'>
              <view class='flex-grow-1'>
                <textarea auto-height='true' placeholder='{{item.tip}}' bindInput='formInput' bindConfirm='formInput' bindblur='formInput' data-index='{{index}}' value='{{item.default}}' style='width:auto;max-height:400rpx;z-index:-1'></textarea>
              </view>
            </block>
            <block wx:if='{{item.type == "time"}}'>
              <view class='flex-grow-1' style='justify-content:flex-end;text-align:right;'>
                <picker mode='time' value='{{item.default?item.default:"00:00"}}' start='00:00' end='23:59' bindchange='formInput' data-index='{{index}}'>
                  <view>{{item.default?item.default:"00:00"}}</view>
                </picker>
              </view>
              <view class='flex-grow-0'>
                <image class='right-jiantou' src="/images/icon-jiantou-r.png"></image>
              </view>
            </block>
            <block wx:if='{{item.type == "date"}}'>
              <view class='flex-grow-1' style='justify-content:flex-end;text-align:right;'>
                <picker mode='date' value='{{item.default?item.default:time}}' bindchange='formInput' data-index='{{index}}'>
                  <view>{{item.default?item.default:time}}</view>
                </picker>
              </view>
              <view class='flex-grow-0'>
                <image class='right-jiantou' src="/images/icon-jiantou-r.png"></image>
              </view>
            </block>
            <block wx:if='{{item.type == "radio"}}'>
              <view class='flex-grow-1 flex-row' style='flex-wrap:wrap'>
                <block wx:for='{{item.default_list}}' wx:for-index='k' wx:for-item='v'>
                  <view class='default {{v.is_selected == 1?"d-active":""}}' bindtap='selectForm' data-k='{{k}}' data-index='{{index}}'>{{v.name}}</view>
                </block>
              </view>
            </block>
            <block wx:if='{{item.type == "checkbox"}}'>
              <view class='flex-grow-1 flex-row' style='flex-wrap:wrap'>
                <block wx:for='{{item.default_list}}' wx:for-index='k' wx:for-item='v'>
                  <view class='default {{v.is_selected == 1?"d-active":""}}' style='border-radius:0' bindtap='selectForm' data-k='{{k}}' data-index='{{index}}'>{{v.name}}</view>
                </block>
              </view>
            </block>
          </view>
        </block>
      </view>
    </view>
  </block>


  <!-- <view bindtap="showPayment" class="flex-row flex-y-center" style="background: #fff;padding: 0 24rpx;height: 90rpx;border-bottom: 1rpx solid #e3e3e3;margin-bottom: 20rpx;">
    <view class="flex-grow-1">支付方式</view>
    <block wx:if="{{payment==1}}">
      <view class="flex-grow-0">微信支付</view>
    </block>
    <block wx:if="{{payment==2}}">
      <view class="flex-grow-0">储值支付</view>
    </block>
    <block wx:if="{{payment==3}}">
      <view class="flex-grow-0">其他支付</view>
    </block>
    <view class="flex-grow-0">
      <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx;margin-left: 12rpx"></image>
    </view>
  </view> -->

  <view class="cart-list">
    <view class="flex-col " wx:for="{{goods_list}}">
      <view class='cart-item flex-row' style='{{item.give <= 0 ?"border-bottom:1rpx solid #e3e3e3":""}}'>
        <view class="flex-grow-0">
          <image class="goods-pic" mode="aspectFill" src="{{item.goods_pic}}" />
        </view>
        <view class="flex-grow-1 flex-col">
          <view class="flex-grow-1">
            <view class="goods-name" style="color: #000;">{{item.goods_name}}</view>
            <view class="attr-list">
              <view class="attr-item" wx:for="{{item.attr}}" wx:for-item="attr">
                {{attr.attr_group_name}}:{{attr.attr_name}}
              </view>
            </view>
          </view>
          <view class="flex-grow-0 flex-row">
            <view class="flex-grow-1 num">×{{item.num}}</view>
            <view class="flex-grow-0 price" style="color: rgb(87, 83, 83);">￥{{item.price}}</view>
          </view>

        </view>


      </view>
      <view class='goods-intrgral flex-row' wx:if="{{item.give > 0}}">
        <view class='flex-y-center'> 订单完成返回</view>
        <view class='flex-y-center' style='color:#ff4544'>{{item.give}}</view>
        <view class='flex-y-center'>积分</view>
      </view>



    </view>
    <view class="" style="background: #fff;padding: 24rpx 24rpx;">
      <view style="display: inline;">
        <text>买家留言</text>
        <input style="width:80%;float: right;" name="remark" bindinput="bindkeyinput" placeholder="选填(20字以内)" value="{{content}}" />
      </view>
    </view>

  </view>

  <!-- <view class="flex-row" style="background: #fff;padding: 32rpx 24rpx;margin-bottom:20rpx;">
    <view class="flex-grow-1">会员折扣</view>
    <view class="flex-grow-0">
      <view style="color:#ff4544">{{level.discount}}8.8折</view>
    </view>
  </view> -->





  <view class="flex-row" style="background: #fff;padding: 12rpx 24rpx;margin-top: 8px;">
    <view class="flex-grow-1">商品小计</view>
    <view class="flex-grow-0">
      <view style="color:#000;">￥{{goods_price}}</view>
    </view>
  </view>

  <view class="flex-row" style="background: #fff;padding: 12rpx 24rpx" wx:if="{{extra_charges_price>0}}">
    <view class="flex-grow-1">{{extra_charges_name}}</view>
    <view class="flex-grow-0">
      <view style="color:#000;">￥{{extra_charges_price}}</view>
    </view>
  </view>

  <block wx:if="{{express_price !==false && type==1}}">
    <view class="flex-row" style="background: #fff;padding: 12rpx 24rpx">
      <view class="flex-grow-1">运费</view>
      <view class="flex-grow-0">
        <block wx:if="{{type==1}}">
          <block wx:if='{{address || express_price || 1==1}}'>
            <view style="color:#000;">￥{{express_price}}</view>
          </block>
          <block wx:else>
            <view style="color:#888">请先选择收货地址</view>
          </block>
        </block>
        <block wx:elif="{{type==1}}">
          <view style="color:#ff4544">￥0元</view>
        </block>
      </view>

    </view>
  </block>



  <view class="flex-row" style="background: #fff;padding: 12rpx 24rpx">
    <view class="flex-grow-1"></view>
    <view class="flex-grow-0">
      <view>
        <text>合计:</text>
        <text style="color:#ff4544;font-size: 36rpx;">￥{{total_price}}</text>
      </view>
    </view>
  </view>

  <!-- <view bindtap="showCouponPicker" class="flex-row flex-y-center" style="background: #fff;padding: 0 24rpx;height: 90rpx;border-bottom: 1rpx solid #e3e3e3;{{integral.forehead_integral>0?'':'margin-bottom: 20rpx;'}}">
    <view class="flex-grow-1">优惠券</view>
    <view class="flex-grow-0">
      <view wx:if="{{!coupon_list||coupon_list.length==0}}" style="color: #888">目前无可用优惠券</view>
      <block wx:else>
        <view wx:if="{{picker_coupon}}" style="color: #ff4544">-{{picker_coupon.sub_price}}元</view>
        <view wx:else style="color: #ff4544">有{{coupon_list.length}}张优惠券可以使用</view>
      </block>
    </view>
    <view class="flex-grow-0">
      <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx;margin-left: 12rpx"></image>
    </view>
  </view> -->

  <view class="weui-cells weui-cells_checkbox">
    <checkbox-group bindchange="checkboxChange">
      <label class="weui-cell weui-cell_active weui-check__label" wx:if="{{can_use_point_pay}}">
        <view class="weui-cell__bd">
          <view class='flex-y-center'>
            <view class="flex-grow-0">
              <image src="/images/integral.png" style="width:48rpx;height:48rpx;margin-right:30rpx;"></image>
            </view>
            可用{{member_available_point}}积分,使用{{max_use_point_num}}分抵
            <text class='flex-y-center' style='color:#ff4544'>{{max_use_point_money}}</text>
            <text class='flex-y-center'>元</text>
          </view>
        </view>
        <view class="weui-cell__hd">
          <checkbox class="weui-check" value="use_point_paid" checked="{{use_point_paid}}" disabled="{{max_use_point_num<=0}}" />
          <i class="weui-icon-checked"></i>
        </view>
      </label>


      <label class="weui-cell weui-cell_active weui-check__label" wx:if="{{can_use_money_pay}}">
        <view class="weui-cell__bd">
          <view class='flex-y-center'>
            <view class="flex-grow-0">
              <image src="/images/img-share-price.png" style="width:48rpx;height:48rpx;margin-right:30rpx;"></image>
            </view>
            余额 {{member_available_money}} {{money_unit}},最多抵
            <text class='flex-y-center' style='color:#ff4544;padding-left: 10rpx;padding-right: 10rpx;'> {{max_use_money}} </text>
            <text class='flex-y-center'>{{money_unit}}</text>

          </view>
        </view>
        <view class="weui-cell__hd">
          <checkbox class="weui-check" value="use_money_paid" checked="{{use_money_paid}}" disabled="{{max_use_money<=0}}" />
          <i class="weui-icon-checked"></i>
        </view>
      </label>

    </checkbox-group>
  </view>

</view>
<view class="coupon-picker" wx:if="{{show_coupon_picker}}">
  <scroll-view class="coupon-list" scroll-y="true" style="height: 100%">
    <view style="color: #888;font-size: 9pt;margin-bottom: 20rpx;line-height: 1.35">
      注：优惠券只能抵消商品金额，不能抵消运费，商品金额最多优惠到0.01元
    </view>
    <view bindtap="pickCoupon" data-index="-1" style="height: 80rpx;margin-bottom: 24rpx;color: #888;border: 1rpx solid #e3e3e3;border-radius: 10rpx" class="flex-y-center flex-x-center">不使用优惠券
    </view>
    <view bindtap="pickCoupon" data-index="{{index}}" class="coupon-item coupon-status-{{item.status}} {{picker_coupon&&(item.user_coupon_id==picker_coupon.user_coupon_id)?'active':''}}" wx:for="{{coupon_list}}">
      <image class="coupon-bg" src="/images/img-coupon-bg-{{item.status==0?0:1}}.png"></image>
      <image wx:if="{{item.status!=0}}" class="coupon-status-icon" src="/images/img-coupon-status-icon-{{item.status}}.png"></image>
      <view class="flex-row" style="height: 100%;overflow: hidden">
        <view class="flex-grow-0 flex-col flex-y-center flex-x-center coupon-left">
          <view class="flex-row flex-y-bottom">
            <view style="font-size: 9pt">￥</view>
            <view style="font-size: {{item.sub_price.length>4?'13':'19'}}pt;line-height: .9">
              {{item.sub_price}}
            </view>
          </view>
          <view style="font-size: 8pt;margin-top: 10rpx">{{item.min_price_desc}}</view>
        </view>
        <view class="flex-grow-1 flex-y-center coupon-right">
          <view style="width: 100%">
            <view class="flex-row flex-y-center mb-10">
              <view class="flex-grow-1" style="font-size: 13pt;font-weight: bold">{{item.event_desc}}
              </view>
              <view class="flex-grow-0" style="font-size: 9pt"></view>
            </view>
            <view style="font-size: 8pt;color: #888">{{item.begin_time}} ~ {{item.end_time}}</view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</view>
<!--选择自提地址  开始-->
<view class="coupon-picker" wx:if="{{show_store}}" style='background-color:#f7f7f7;'>
  <scroll-view class="coupon-list" scroll-y="true" style="height: 100%;padding:0;">

    <!-- <view class='shop-block flex-y-center'>当前地址</view>
    <view class='shop-address flex-row flex-y-center'>
      <view class='flex-grow-1'>{{location}}</view>
      <view class='flex-grow-0 flex-y-center' bindtap='dingwei'>
        <image src='/images/icon-shop-dingwei.png' style='width:32rpx;height:32rpx;margin-right:2rpx;'></image>
        <text style='color:#2495ff'>手动定位</text>
      </view>
    </view> -->

    <view class='shop-block flex-row flex-y-center'>
      <view class='flex-grow-1'>门店列表</view>
      <view class='flex-grow-0 close-btn' bindtap='hideStorePicker'>
        <image src='/images/icon-close.png'></image>
      </view>
    </view>

    <view class="flex-row address-picker" style='margin:0;' bindtap='pickShop' wx:for='{{store_list}}' wx:key="guid" data-index="{{index}}">
      <view class="flex-grow-0 flex-y-center">
        <image src="{{item.guid==store.guid?'/images/icon-shop-checked.png':'/images/icon-shop-un.png'}}" style="width: 40rpx;height: 40rpx;margin-right: 20rpx"></image>
      </view>
      <view class="flex-grow-1">
        <view class="flex-row" style="margin-bottom: 20rpx">
          <view class="flex-grow-1" style='font-weight:bold;{{item.guid==store.guid?"color:#ff4544":""}}'>
            {{item.store_name}}
          </view>
          <view class='flex-grow-0' wx:if='{{item.distance!=-1}}'>{{item.distance_text}}</view>
        </view>
        <view class="flex-row" style="margin-bottom: 20rpx">
          <view class="flex-grow-1">电话：{{item.mobile}}</view>
        </view>
        <view>地址：{{item.address_info}}</view>
      </view>
    </view>
  </scroll-view>
</view>
<!--选择自提地址  结束-->
<block wx:if="{{show_payment}}">
  <view class="payment">
    <view class="pay-modal">
      <view class="pay-head flex-x-center flex-y-center">请选择支付方式</view>
      <view bindtap="payPicker" class="pay-block flex-row flex-y-center" data-index="1">
        <view class="flex-grow-0">
          <image src="/images/icon-online.png" style="width:72rpx;height:72rpx;margin-right:30rpx;"></image>
        </view>
        <view class="flex-grow-1 {{payment==1?'red':''}}">微信支付</view>
        <block wx:if="{{payment==1}}">
          <view class="flex-grow-0">
            <image src="/images/icon-pay-right.png" style="width:34rpx;height:34rpx;"></image>
          </view>

        </block>
      </view>
      <view bindtap="payPicker" class="pay-block flex-row flex-y-center" data-index="2">
        <view class="flex-grow-0">
          <image src="/images/img-share-price.png" style="width:72rpx;height:72rpx;margin-right:30rpx;"></image>
        </view>
        <view class="flex-grow-1 {{payment==2?'red':''}}">储值支付(余额:{{user_info.money}}{{money_unit}})</view>
        <block wx:if="{{payment==2}}">
          <view class="flex-grow-0">
            <image src="/images/icon-pay-right.png" style="width:34rpx;height:34rpx;"></image>
          </view>
        </block>
      </view>
      <view bindtap="payClose" class="pay-footer flex-x-center flex-y-center hidden">关闭</view>
    </view>
  </view>
</block>
<include src="/common/get-card/get-card.wxml" />