// order-submit.js
var app = getApp();
var longitude = "";
var latitude = "";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    options: {}, //当前页面参数
    goods_info: {}, // 商品信息
    cart_guid_list: {}, //购物车信息

    address_guid: '', //地址guid
    address: {}, //地址信息
    config: {},
    list: [], //用于展示商品列表
    store_list: [], //门店列表(初始化为数组)
    store_info: null, //选中的门店信息
    store: null, //选中的门店信息(兼容)
    show_store: false, // 是否展示门店选择组件
    type: 0, // 0 初始化 1 快递配送 2 到店自提
    goods_price: 0.00, //商品总价
    express_price: 0, //运费总价
    extra_charges_name: '附加费', //附加费名称
    money_unit: '', //金额单位 例如 元/ 点
    extra_charges_price: 0, //附加费金额
    total_price: 0, //总价

    paid_wechat: 0, //微信支付金额
    paid_money: 0, // 储值支付金额
    paid_point: 0, // 积分支付金额

    max_use_money: 0, // 最大可用储值支付金额
    max_use_point_num: 0, // 最大可用积分数量
    max_use_point_money: 0, // 最大可用积分数量抵用的金额

    use_money_paid: true, //是否使用储值支付
    use_point_paid: true, //是否使用积分支付

    member_available_money: 0, //会员可用储值
    member_available_point: 0, //会员可用积分

    can_use_money_pay: false, //是否可用余额付款
    can_use_point_pay: false, //是否可用积分付款


    //提交订单用
    remark: '', //备注信息
    offline: 0, // 快递发货还是上门自提
    true_name: "", //  收货地址的姓名
    mobile: "", //收货地址的手机号
    pay_complete: false,

    // integral_radio: 1,
    // new_total_price: 0,
    // show_card: false,
    // show_payment: !1,
    // payment: 1,
  },
  checkboxChange(e) {
    //console.log('checkbox发生change事件，携带value值为：', e.detail.value);
    let list = e.detail.value;
    this.setData({
      use_money_paid: false,
      use_point_paid: false
    })
    for (let key in list) { //prop指对象的属性名
      let type = list[key];
      console.log(type);
      switch (type) {
        case 'use_money_paid':
          this.setData({
            use_money_paid: true
          })
          break;
        case 'use_point_paid':
          this.setData({
            use_point_paid: true
          })
          break;
        default:
          break;
      };
    }
    this.submit_preview();
  },
  submit: function () {
    var that = this;
    var data = {};
    data.goods_info = that.data.options.goods_info;
    data.cart_guid_list = that.data.options.cart_guid_list;
    data.address_guid = that.data.address_guid;
    data.use_money_paid = that.data.use_money_paid;
    data.use_point_paid = that.data.use_point_paid;
    data.paid_money = that.data.paid_money;
    data.paid_point = that.data.paid_point;
    data.paid_wechat = that.data.paid_wechat;
    data.remark = that.data.remark;
    data.type = that.data.type;
    data.share_member_guid = wx.getStorageSync('share_member_guid');
    data.share_user_guid = wx.getStorageSync('share_user_guid');
    data.way = 2;
    if (data.type == 2 && that.data.store) {
      data.request_send_or_pick_up_store_guid = that.data.store.guid;
      data.true_name = that.data.true_name;
      data.mobile = that.data.mobile;
      data.address_guid = '';
    }
    if (data.type == 2 && !data.request_send_or_pick_up_store_guid) {
      //没有选择收货地址进行提示
      wx.showModal({
        title: "提示",
        content: "请选择自提门店",
        showCancel: false,
        cancelText: "取消",
        confirmText: "去选择",
        success: function (res) {
          if (res.confirm) {
            that.showStorePicker();
          }
        }
      });
      return;
    }
    if (data.type == 1 && !that.data.address_guid) {
      //没有选择收货地址进行提示
      wx.showModal({
        title: "提示",
        content: "请选择收货地址",
        showCancel: false,
        cancelText: "取消",
        confirmText: "去选择",
        success: function (res) {
          if (res.confirm) {
            wx.navigateTo({
              url: "/pages/address-picker/address-picker",
            });
          }
        }
      });
      return;
    }
    //提交订单
    app.request({
      url: app.api.order.submit,
      data: data,
      success: function (res) {
        var order_guid = res.data.order_guid;
        var payment = res.data.pay_type;
        if (payment == 1) {

          //获取支付数据
          app.request({
            url: app.api.order.pay_data,
            data: {
              order_guid: order_guid,
              pay_type: payment,
              scene: 'miniapp'
            },
            success: function (res) {
              //微信支付继续调用微信API
              var pay_options = res.data.pay_options;
              var third_pay_bill_number = res.data.third_pay_bill_number
              wx.requestPayment({
                timeStamp: pay_options.timeStamp,
                nonceStr: pay_options.nonceStr,
                package: pay_options.package,
                signType: pay_options.signType,
                paySign: pay_options.paySign,
                success: function (e) {
                  console.log('支付success');
                  console.log(e);
                  // that.redirectToOrderList();
                },
                fail: function (e) {
                  console.log('支付 fail');
                  console.log(e);
                },
                complete: function (e) {
                  console.log('支付 complete');
                  console.log(e);
                  that.setData({
                    pay_complete: true
                  });
                  if (e.errMsg == "requestPayment:fail" || e.errMsg == "requestPayment:fail cancel") {
                    //支付失败转到待支付订单列表
                    wx.showModal({
                      title: "提示",
                      content: "订单尚未支付",
                      showCancel: false,
                      confirmText: "确认",
                      success: function (res) {
                        if (res.confirm) {
                          wx.redirectTo({
                            url: "/pages/order/order?status=-1",
                          });
                        }
                      }
                    });
                  } else if (e.errMsg == "requestPayment:ok") {
                    that.queryOrder(third_pay_bill_number);
                  } else {
                    //支付失败转到待支付订单列表
                    wx.showModal({
                      title: "提示",
                      content: "支付失败:" + e.errMsg,
                      showCancel: false,
                      confirmText: "确认",
                      success: function (res) {
                        if (res.confirm) {
                          wx.redirectTo({
                            url: "/pages/order/order?status=-1",
                          });
                        }
                      }
                    });
                  }
                },
              });
            }
          });
        } else if (payment == 2) {
          that.redirectToOrderList();
        }
      }
    });
  },
  queryOrder: function (third_pay_bill_number) {
    var that = this;
    app.pay.query(third_pay_bill_number,
      function () {
        that.redirectToOrderList()
      },
      function () {
        wx.redirectTo({
          url: "/pages/order/order?status=-1",
        });
      })
  },
  orderSubmit: function () {
    var page = this;
    var offline = page.data.offline;
    var payment = page.data.payment;
    var data = {};
    if (offline == 0) {
      if (!page.data.address || !page.data.address.guid) {
        wx.showToast({
          title: "请选择收货地址",
          image: "/images/icon-warning.png",
        });
        return;
      }
      data.address_guid = page.data.address.guid;
    } else {
      data.address_name = page.data.true_name;
      data.address_mobile = page.data.mobile;
      if (page.data.shop.id) {
        data.shop_id = page.data.shop.id;
      } else {
        wx.showModal({
          title: '警告',
          content: '请选择门店',
          showCancel: false
        });
        return;
      }
      if (!data.address_name || data.address_name == undefined) {
        wx.showToast({
          title: "请填写收货人",
          image: "/images/icon-warning.png",
        });
        return;
      }
      if (!data.address_mobile || data.address_mobile == undefined) {
        wx.showToast({
          title: "请填写联系方式",
          image: "/images/icon-warning.png",
        });
        return;
      } else {
        var check_mobile = /^1\d{10}$/;
        if (!check_mobile.test(data.address_mobile)) {
          wx.showModal({
            title: '提示',
            content: '手机号格式不正确',
            showCancel: false
          });
          return;
        }
      }
    }

    if (!payment) {
      wx.showToast({
        title: "请选择支付方式",
        image: "/images/icon-warning.png",
      });
      return;
    }
    data.pay_type = payment;
    data.offline = offline;
    var form = page.data.form;
    if (form.is_form == 1) {
      var form_list = form.list;
      for (var i in form_list) {
        if (form_list[i].type == 'date') {
          form_list[i].default = form_list[i].default ? form_list[i].default : page.data.time;
        }
        if (form_list[i].type == 'time') {
          form_list[i].default = form_list[i].default ? form_list[i].default : '00:00';
        }
        if (form_list[i].required == 1) {
          if (form_list[i].type == 'radio' || form_list[i].type == 'checkboxc') {
            var is_true = false;
            for (var j in form_list[i].default_list) {
              if (form_list[i].default_list[j].is_selected == 1) {
                is_true = true;
              }
            }
            if (!is_true) {
              wx.showModal({
                title: '提示',
                content: '请填写' + form.name + '，加‘*’为必填项',
                showCancel: false
              })
              return false;
            }
          } else {
            if (!form_list[i].default || form_list[i].default == undefined) {
              wx.showModal({
                title: '提示',
                content: '请填写' + form.name + '，加‘*’为必填项',
                showCancel: false
              })
              return false;
            }
          }
        }
      }
    }
    data.form = JSON.stringify(form);

    if (page.data.cart_guid_list) {
      data.cart_guid_list = JSON.stringify(page.data.cart_guid_list);
    }
    if (page.data.goods_info) {
      data.goods_info = JSON.stringify(page.data.goods_info);
    }
    if (page.data.picker_coupon) {
      data.user_coupon_id = page.data.picker_coupon.user_coupon_id;
    }
    if (page.data.remark) {
      data.remark = page.data.remark
    }
    if (page.data.type) {
      data.type = page.data.type
    }
    page.data.integral_radio == 1 ? data.use_integral = 1 : data.use_integral = 2;

    data.share_member_guid = wx.getStorageSync('share_member_guid');
    data.share_user_guid = wx.getStorageSync('share_user_guid');

    //提交订单
    app.request({
      url: app.api.order.submit,
      data: data,
      success: function (res) {
        setTimeout(function () {
          page.setData({
            options: {},
          });
        }, 1);
        var order_guid = res.data.order_guid;

        //获取支付数据
        app.request({
          url: app.api.order.pay_data,
          data: {
            order_guid: order_guid,
            pay_type: payment,
          },
          success: function (res) {
            if (payment == 1) {
              //微信支付继续调用微信API
              //发起支付
              var pay_options = res.data.pay_options;
              wx.requestPayment({
                timeStamp: pay_options.timeStamp,
                nonceStr: pay_options.nonceStr,
                package: pay_options.package,
                signType: pay_options.signType,
                paySign: pay_options.paySign,
                success: function (e) {
                  page.redirectToOrderList();
                },
                fail: function (e) {},
                complete: function (e) {
                  if (e.errMsg == "requestPayment:fail" || e.errMsg == "requestPayment:fail cancel") { //支付失败转到待支付订单列表
                    wx.showModal({
                      title: "提示",
                      content: "订单尚未支付",
                      showCancel: false,
                      confirmText: "确认",
                      success: function (res) {
                        if (res.confirm) {
                          wx.redirectTo({
                            url: "/pages/order/order?status=-1",
                          });
                        }
                      }
                    });
                    return;
                  }
                  if (e.errMsg == "requestPayment:ok") {
                    page.redirectToOrderList();
                    return;
                  }
                  wx.redirectTo({
                    url: "/pages/order/order?status=-1",
                  });
                },
              });
              return;
            }
            if (payment == 2) {
              page.redirectToOrderList();
            }
          }
        });
      }
    });
  },

  submit_preview: function () {
    let that = this;
    if (that.data.pay_complete == true) {
      return;
    }
    app.request({
      url: app.api.order.submit_preview,
      data: {
        goods_info: that.data.options.goods_info,
        cart_guid_list: that.data.options.cart_guid_list,
        address_guid: that.data.address_guid,
        use_money_paid: that.data.use_money_paid,
        use_point_paid: that.data.use_point_paid,
        type: that.data.type
      },
      success: function (result) {
        that.setData(result.data)
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    //var time = app.utils.formatData(new Date());
    page.setData({
      options: options,
      // store: wx.getStorageSync("store"),
      // time: time
    })
    // page.setData({
    //   user_info: wx.getStorageSync('user_info'),
    // })
  },
  bindkeyinput: function (e) {
    this.setData({
      remark: e.detail.value
    });
  },
  KeyTrueName: function (e) {
    this.setData({
      true_name: e.detail.value
    });
  },
  KeyMobile: function (e) {
    this.setData({
      mobile: e.detail.value
    });
  },
  showPayment: function () {
    this.setData({
      show_payment: !0
    })
  },

  // 显示门店选择组件
  showStorePicker: function () {
    console.log('显示门店选择');
    var page = this;
    // 先获取门店列表
    this.get_store_list();
    // 延迟确保数据加载完成
    setTimeout(function () {
      page.setData({
        show_store: true
      });
      console.log('门店列表数据:', page.data.store_list);
    }, 300);
  },

  // 隐藏门店选择组件
  hideStorePicker: function () {
    this.setData({
      show_store: false
    });
  },
  payClose: function () {
    this.setData({
      show_payment: !1
    })
  },
  payPicker: function (t) {
    var a = t.currentTarget.dataset.index;
    this.setData({
      payment: a,
      show_payment: !1
    })
  },
  // 切换到快递配送
  show_address: function () {
    var page = this;
    page.setData({
      type: 1,
      offline: 0
    });
    page.getPrice();
    this.submit_preview();
  },

  // 切换到到店自提
  show_pick_up_store: function () {
    console.log('show_pick_up_store');
    let page = this;
    page.setData({
      type: 2
    });
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        console.log(res);
        const latitude = res.latitude;
        const longitude = res.longitude;
        const speed = res.speed;
        const accuracy = res.accuracy;
        page.setData({
          latitude: res.latitude,
          longitude: res.longitude,
        })
        page.get_store_list();
      },
      fail(res) {
        console.log(res);
        page.get_store_list();
      }
    })
    this.submit_preview();
  },

  // 获取门店列表
  get_store_list: function () {
    var page = this;
    // 直接获取门店列表，不需要定位
    app.request({
      url: app.api.store.list,
      data: {
        lat: page.data.latitude,
        lon: page.data.longitude,
        way: 2,
      },
      success: function (res) {
        console.log('门店列表返回：', res);
        page.setData({
          store_list: res.data
        });
        console.log('设置门店列表：', res.data);
      },
      fail: function (err) {
        console.log('请求门店列表失败：', err);
      }
    });
  },

  dingwei: function () {
    var page = this;
    // wx.chooseLocation({
    //   success: function (e) {
    //     longitude = e.longitude;
    //     latitude = e.latitude;
    //     page.setData({
    //       location: e.address,
    //     });
    //   },
    //   fail: function (res) {
    //     app.getauth({
    //       content: "需要获取您的地理位置授权，请到小程序设置中打开授权",
    //       success: function (e) {
    //         if (e) {
    //           if (e.authSetting["scope.userLocation"]) {
    //             page.dingwei();
    //           } else {
    //             wx.showToast({
    //               title: '您取消了授权',
    //               image: '/images/icon-warning.png'
    //             })
    //           }
    //         }
    //       }
    //     });
    //   }
    // })
  },
  redirectToOrderList: function () {
    let time = 1500;
    wx.showToast({
      title: '支付成功',
      duration: time,
      success: function () {
        setTimeout(function () {
          //要延时执行的代码
          wx.redirectTo({
            url: "/pages/order/order?status=0",
          })
        }, time)
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var that = this;
    var address = wx.getStorageSync("picker_address");
    if (address) {
      that.setData({
        address_guid: address.guid,
        address: address,
        true_name: address.true_name,
        mobile: address.mobile
      });
      wx.removeStorageSync("picker_address");
    }
    app.request({
      url: app.api.code.get_submit_order_config,
      data: {
        way: 2
      },
      success: function (result) {
        console.log(result);
        that.setData({
          config: result.data,
          type: result.data.default_type,
        });

        if (result.data.is_show_address == 1) {
          // 开启快递配送 再赋值省市区id,便于预览订单计算运费

        }
        if (result.data.default_type == 2) {
          // 只开启到店自提需要初始化门店列表,否则就需要点击tab触发
          // that.show_pick_up_store();
        }
        that.submit_preview();
      },
    });
    // page.getOrderData(page.data.options);
  },
  getOrderData: function (options) {
    var page = this;
    var address_guid = "";
    if (page.data.address && page.data.address.guid)
      address_guid = page.data.address.guid;
    if (options.cart_guid_list) {
      var cart_guid_list = JSON.parse(options.cart_guid_list);
      app.request({
        url: app.api.order.submit_preview,
        data: {
          cart_guid_list: options.cart_guid_list,
          address_guid: address_guid,
          longitude: longitude,
          latitude: latitude
        },
        success: function (res) {
          page.setData({
            total_price: parseFloat(res.data.total_price),
            goods_list: res.data.list,
            cart_guid_list: res.data.cart_guid_list,
            address: res.data.address,
            express_price: parseFloat(res.data.express_price),
            coupon_list: res.data.coupon_list,
            store_list: res.data.store_list,
            shop: {},
            true_name: res.data.address ? res.data.address.true_name : '',
            mobile: res.data.address ? res.data.address.mobile : '',
            send_type: res.data.send_type,
            level: res.data.level,
            new_total_price: parseFloat(res.data.total_price),
            integral: res.data.integral,
            goods_card_list: res.data.goods_card_list,
            form: res.data.form
          });
          if (res.data.send_type == 1) { //仅快递
            page.setData({
              offline: 0,
            });
          }
          if (res.data.send_type == 2) { //仅自提
            page.setData({
              offline: 1,
            });
          }
          page.getPrice();
        }
      });
    }
    if (options.goods_info) {
      app.request({
        url: app.api.order.submit_preview,
        data: {
          goods_info: options.goods_info,
          address_guid: address_guid,
          longitude: longitude,
          latitude: latitude
        },
        fail: function (res) {
          wx.showModal({
            title: "提示",
            content: res.msg,
            showCancel: false,
            confirmText: "返回",
            success: function (res) {
              if (res.confirm) {
                wx.navigateBack({
                  delta: 1,
                });
              }
            }
          });
        },
        success: function (res) {
          page.setData({
            total_price: res.data.total_price,
            goods_list: res.data.list,
            goods_info: res.data.goods_info,
            address: res.data.address,
            express_price: parseFloat(res.data.express_price),
            coupon_list: res.data.coupon_list,
            store_list: res.data.store_list,
            shop: {},
            name: res.data.address ? res.data.address.name : '',
            mobile: res.data.address ? res.data.address.mobile : '',
            send_type: res.data.send_type,
            level: res.data.level,
            new_total_price: res.data.total_price,
            integral: res.data.integral,
            goods_card_list: res.data.goods_card_list,
            form: res.data.form
          });
          if (res.data.send_type == 1) { //仅快递
            page.setData({
              offline: 0,
            });
          }
          if (res.data.send_type == 2) { //仅自提
            page.setData({
              offline: 1,
            });
          }
          page.getPrice();
        }
      });
    }
  },

  copyText: function (e) {
    var text = e.currentTarget.dataset.text;
    if (!text)
      return;
    wx.setClipboardData({
      data: text,
      success: function () {
        wx.showToast({
          title: "已复制内容",
        });
      },
      fail: function () {
        wx.showToast({
          title: "复制失败",
          image: "/images/icon-warning.png",
        });
      },
    });
  },

  showCouponPicker: function () {
    var page = this;
    if (page.data.coupon_list && page.data.coupon_list.length > 0) {
      page.setData({
        show_coupon_picker: true,
      });
    }
  },

  pickCoupon: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    if (index == '-1' || index == -1) {
      page.setData({
        picker_coupon: false,
        show_coupon_picker: false,
      });
    } else {
      // var new_total_price = page.data.total_price - page.data.coupon_list[index].sub_price - page.data.integral.forehead;
      // if (page.data.level) {
      //     new_total_price = new_total_price * page.data.level.discount / 10;
      // }
      page.setData({
        picker_coupon: page.data.coupon_list[index],
        show_coupon_picker: false,
        // new_total_price: parseFloat(new_total_price.toFixed(2)),
      });
    }
    page.getPrice();
  },

  numSub: function (num1, num2, length) {
    return 100;
  },
  showShop: function (e) {
    var page = this;
    page.dingwei();
    if (page.data.store_list && page.data.store_list.length > 1) {
      page.setData({
        show_store: true,
      });
    }
  },
  pickShop: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    if (index == '-1' || index == -1) {
      page.setData({
        store: false,
        show_store: false,
      });
    } else {
      page.setData({
        store: page.data.store_list[index],
        show_store: false,
      });
    }
    page.getPrice();
  },
  // integralRadio:function(e){
  //     var page = this;
  //     var index = e.currentTarget.dataset.index;
  //     if (index == null || index =='radio'){
  //         page.setData({
  //             integral_radio: 'radio-active',
  //         });
  //     } else {
  //         page.setData({
  //             integral_radio: 'radio',
  //         });
  //     }
  // },
  integralSwitchChange: function (e) {
    var page = this;
    if (e.detail.value != false) {
      page.setData({
        integral_radio: 1,
      });
    } else {
      page.setData({
        integral_radio: 2,
      });
    }
    page.getPrice();
  },
  integration: function (e) {
    var page = this;
    var integration = page.data.integral.integration;
    wx.showModal({
      title: '积分使用规则',
      content: integration,
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: '#ff4544',
      success: function (res) {
        if (res.confirm) {
          console.log('用户点击确定')
        }
      }
    });
  },
  /**
   * 计算总价
   */
  getPrice: function () {
    var page = this;
    var total_price = page.data.total_price;
    var new_total_price = total_price;
    var express_price = page.data.express_price;
    var picker_coupon = page.data.picker_coupon;
    var integral = page.data.integral;
    var integral_radio = page.data.integral_radio;
    var level = page.data.level;
    var offline = page.data.offline;

    if (picker_coupon) {
      new_total_price = new_total_price - picker_coupon.sub_price;
    }

    if (integral && integral_radio == 1) {
      new_total_price = new_total_price - parseFloat(integral.forehead);
    }

    if (level) {
      new_total_price = new_total_price * level.discount / 10;
    }

    if (new_total_price <= 0.01) {
      new_total_price = 0.01;
    }

    if (offline == 0) {
      new_total_price = new_total_price + express_price;
    }
    new_total_price = parseFloat(new_total_price)
    page.setData({
      new_total_price: new_total_price.toFixed(2)
    });

  },
  cardDel: function () {
    var page = this;
    page.setData({
      show_card: false
    });
    wx.redirectTo({
      url: '/pages/order/order?status=1',
    })
  },
  cardTo: function () {
    var page = this;
    page.setData({
      show_card: false
    });
    wx.redirectTo({
      url: '/pages/card/card'
    })
  },
  formInput: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    var form = page.data.form;
    var form_list = form.list;
    form_list[index].default = e.detail.value;
    form.list = form_list;
    page.setData({
      form: form
    });
  },
  selectForm: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    var k = e.currentTarget.dataset.k;
    var form = page.data.form;
    var form_list = form.list;
    if (form_list[index].type == 'radio') {
      var default_list = form_list[index].default_list;
      for (var i in default_list) {
        if (i == k) {
          default_list[k].is_selected = 1;
        } else {
          default_list[i].is_selected = 0;
        }
      }
      form_list[index].default_list = default_list;
    }
    if (form_list[index].type == 'checkbox') {
      var default_list = form_list[index].default_list;
      if (default_list[k].is_selected == 1) {
        default_list[k].is_selected = 0;
      } else {
        default_list[k].is_selected = 1;
      }
      form_list[index].default_list = default_list;
    }
    form.list = form_list;
    page.setData({
      form: form
    });
  }
});