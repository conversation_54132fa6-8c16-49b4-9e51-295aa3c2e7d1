// pages/topic/topic.js
var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    app.request({
      url: api.topic.detail,
      data: {
        guid: options.guid,
      },
      success: function (res) {
        page.setData(res.data);
      }
    });
  },

  wxParseTagATap: function (e) {
    console.log(e);
    if (e.currentTarget.dataset.goods) {
      var src = e.currentTarget.dataset.src || false;
      if (!src)
        return;
      wx.navigateTo({
        url: src,
      });
    }
  },

  favoriteClick: function (e) {
    var page = this;
    var action = e.currentTarget.dataset.action; //add 或者remove
    let is_favorite = action == 'add';
    let url = is_favorite ? app.api.user.favorite_add : app.api.user.favorite_remove;
    app.request({
      url: url,
      data: {
        guid: page.data.guid,
        type: 3,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })
        page.setData({
          is_favorite: is_favorite
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  }
});