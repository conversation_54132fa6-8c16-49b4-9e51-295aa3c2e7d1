var is_more = !1;
var app = getApp();

Page({
  data: {
    show: !1,
    note_list: [],
  },
  onLoad: function (options) {
    app.pageOnLoad(this, options);
  },
  // getData: function () {
  //   var e = this;
  //   app.core.showLoading({
  //     title: "加载中"
  //   }), app.request({
  //     url: app.api.recharge.record,
  //     data: {
  //       date: e.data.date_1 || ""
  //     },
  //     success: function (t) {
  //       e.setData({
  //         list: t.data.list
  //       }), is_more = !1;
  //     }
  //   });
  // },
  onReady: function () {
    // app.page.onReady(this);
  },
  onReachBottom: function () {
    console.log('onReachBottom');
    this.getData();

  },
  getData: function () {
    var page = this;
    app.request({
      url: app.api.member_point_note.index,
      pagination: 'note_list',
      page: page,
      // success: function (result) {
      //   that.setData({
      //     list: result.data.list,
      //     // date_1: t.data.date,
      //     // date: t.data.date.replace("-", "年") + "月"
      //   }); 
      // }
    });
  },
  onShow: function () {
    // app.page.onShow(this);
    this.getData();
  },
  dateChange: function (t) {
    if (!is_more) {
      is_more = !0;
      var e = t.detail.value,
        a = e.replace("-", "年") + "月";
      this.setData({
        date: a,
        date_1: e
      }), this.getData();
    }
  },
  dateUp: function () {
    var t = this;
    if (!is_more) {
      is_more = !0;
      var e = t.data.date_1,
        a = (t.data.date, new Date(e));
      a.setMonth(a.getMonth() + 1);
      var o = a.getMonth() + 1;
      o = (o = o.toString())[1] ? o : "0" + o, t.setData({
        date: a.getFullYear() + "年" + o + "月",
        date_1: a.getFullYear() + "-" + o
      }), t.getData();
    }
  },
  dateDown: function () {
    var t = this;
    if (!is_more) {
      is_more = !0;
      var e = t.data.date_1,
        a = (t.data.date, new Date(e));
      a.setMonth(a.getMonth() - 1);
      var o = a.getMonth() + 1;
      o = (o = o.toString())[1] ? o : "0" + o, t.setData({
        date: a.getFullYear() + "年" + o + "月",
        date_1: a.getFullYear() + "-" + o
      }), t.getData();
    }
  },
  click: function () {
    this.setData({
      show: !0
    });
  },
  close: function () {
    this.setData({
      show: !1
    });
  },
  GoToDetail: function (t) {
    var guid = t.currentTarget.dataset.guid;
    wx.navigateTo({
      url: "/pages/balance/point_balance_detail?guid=" + guid
    });
  }
});