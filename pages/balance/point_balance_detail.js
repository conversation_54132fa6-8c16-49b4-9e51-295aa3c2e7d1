var is_more = !1;
var app = getApp();

Page({
  data: {},
  onLoad: function (e) {
    app.pageOnLoad(this, e);
    var that = this;
    that.setData(e);
    app.request({
      url: app.api.member_point_note.detail,
      data: {
        guid: e.guid
      },
      success: function (result) {
        that.setData({
          data: result.data
        })
      }
    });
  },
  onReady: function () {
    // app.page.onReady(this);
  },
  onShow: function () {
    // app.page.onShow(this);
  },
  onHide: function () {
    // app.page.onHide(this);
  },
  onUnload: function () {
    // app.page.onUnload(this);
  }
});