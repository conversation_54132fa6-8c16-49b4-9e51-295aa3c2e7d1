<view class="page">
  <!-- <include src="/components/common/common"></include>
    <include src="/components/header/header"></include> -->
  <view class="body after-navber">
    <view class="detail-block-1 flex-y-center">
      <view wx:if="{{order_type=='r'}}">
        <view class="flex-row">
          <view class="flex-grow-0 t-color">交易金额</view>
          <view class="flex-grow-1 {{data.flag==0?'record-3':'record-4'}}">{{data.pay_price}}</view>
        </view>
        <view class="flex-row">
          <view class="flex-grow-0 t-color">赠送金额</view>
          <view class="flex-grow-1 {{data.flag==0?'record-3':'record-4'}}">{{data.send_price}}</view>
        </view>
      </view>
      <view class="flex-row" wx:else>
        <view class="flex-grow-0 t-color">交易金额</view>
        <view class="flex-grow-1 {{data.type!=1?'record-3':'record-4'}}">
          <block wx:if="{{data.type==1}}">+</block>
          {{data.point}}
        </view>
      </view>
    </view>
    <view class="detail-block-1">
      <view class="flex-row" style="margin-top:16rpx;">
        <view class="flex-grow-0 t-color">操作时间</view>
        <view class="flex-grow-1">{{data.create_time}}</view>
      </view>

      <view class="flex-row" style="margin-top:16rpx;">
        <view class="flex-grow-0 t-color">操作工号</view>
        <view class="flex-grow-1">{{data.user_account}}</view>
      </view>

      <view class="flex-row" style="margin-top:16rpx;" wx:if="{{data.store_name}}">
        <view class="flex-grow-0 t-color">操作门店</view>
        <view class="flex-grow-1">{{data.store_name}}</view>
      </view>

      <view class="flex-row" style="margin-top:32rpx;">
        <view class="flex-grow-0 t-color">备注</view>
        <view class="flex-grow-1">{{data.memo}}</view>
      </view>
      <view class="flex-row" style="margin-top:32rpx;margin-bottom:16rpx;" wx:if="{{data.bill_number}}">
        <view class="flex-grow-0 t-color">交易单号</view>
        <view class="flex-grow-1">{{data.bill_number}}</view>
      </view>
      <view class="flex-row" style="padding-top:16rpx;margin-bottom:16rpx;" wx:if="{{data.refund_bill_number}}">
        <view class="flex-grow-0 t-color">退款单号</view>
        <view class="flex-grow-1">{{data.refund_bill_number}}</view>
      </view>
    </view>
  </view>
  <!-- <include src="/components/footer/footer"></include> -->
</view>