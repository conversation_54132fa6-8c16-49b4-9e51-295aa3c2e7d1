<view class="page show_navbar">
  <!--cart.wxml-->
  <import src="/common/tabbar/tabbar.wxml" />
  <template is="tabbar" data="{{tabbar}}" />
  <view class="body after-navber">
    <view class="bottom-bar flex-row" wx:if="{{cart_list.length>0}}">
      <view class="flex-y-center flex-grow-1" style="padding: 0 24rpx">
        <view bindtap="cartCheckAll" class="flex-grow-0 flex-y-center" style="margin-right: 20rpx">
          <view class="cart-checkbox {{cart_check_all?'active':''}}" style="margin-right: 10rpx"></view>
          <text style="white-space: nowrap">全选</text>
        </view>
        <view class="flex-grow-1" style="color:#ff4544;white-space: nowrap">\n 总计:￥{{total_price}}\n</view>
      </view>
      <block wx:if="{{show_cart_edit}}">
        <view bindtap="cartDone" class="flex-y-center flex-grow-0 flex-x-center edit-btn">完成</view>
        <view bindtap="cartDelete" class="flex-y-center flex-grow-0 flex-x-center submit-btn">删除</view>
      </block>
      <block wx:else>
        <view bindtap="cartEdit" class="flex-y-center flex-grow-0 flex-x-center edit-btn">编辑</view>
        <view bindtap="cartSubmit" class="flex-y-center flex-grow-0 flex-x-center submit-btn">结算</view>
      </block>
    </view>


    <block wx:if="{{(!cart_list.length&&!mch_list.length)&&!loading}}">
      <view class="no-data-tip">
        <view class="no-data-icon flex-y-center flex-x-center">
          <image src="/images/icon_cart_empty.png" style="width: 81rpx;height: 81rpx"></image>
        </view>
        <view>购物车还是空的哦</view>
      </view>
    </block>
    <block wx:else>
      <block wx:if="{{cart_list&&cart_list.length}}">
        <view class="flex flex-row mch-header">
          <view bindtap="checkGroup" class="flex-grow-0 flex-y-center" data-index="0" data-type="self" style="padding: 24rpx">
            <view class="cart-checkbox {{check_all_self?'active':''}}"></view>
          </view>
          <view class="flex-grow-1 flex-y-center">平台自营</view>
        </view>
        <view class="cart-list">
          <block wx:for="{{cart_list}}" wx:key="{{item.id}}" wx:for-item="item" wx:for-index="index">
            <view class="cart-item flex-row">
              <block wx:if="{{item.disabled&&!show_cart_edit}}">
                <view class="flex-grow-0 flex-y-center" style="padding: 24rpx">
                  <view style="width: 40rpx"></view>
                </view>
              </block>
              <block wx:else>
                <view bindtap="cartCheck" class="flex-grow-0 flex-y-center" data-index="{{index}}" data-mch-index="0" data-type="self" style="padding: 24rpx">
                  <view class="cart-checkbox {{item.checked?'active':''}}"></view>
                </view>
              </block>
              <view class="flex-grow-1">
                <view class="flex-grow-1 flex-row">
                  <view class="flex-grow-0">
                    <image class="goods-pic" mode="aspectFill" src="{{item.goods_pic}}"></image>
                  </view>
                  <view class="flex-grow-1 flex-col">
                    <view class="flex-grow-1">
                      <view class="goods-name">
                        <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}">{{item.goods_name}}</navigator>
                      </view>
                      <view class="attr-list">
                        <block wx:for="{{item.attr_list}}" wx:key="{{item.id}}" wx:for-item="attr" wx:for-index="index">
                          <view class="attr-item">\n {{attr.attr_group_name}}:{{attr.attr_name}}\n</view>
                        </block>
                      </view>
                    </view>
                    <view class="flex-grow-0 flex-row">
                      <view class="flex-grow-1 price">￥{{item.unitPrice}}</view>
                      <view>
                        <view class="flex-row">
                          <block wx:if="{{item.num!=1}}">
                            <image bindtap="cartLess" class="image1" id="{{item.cart_guid}}" src="/images/cart_less.png"></image>
                          </block>
                          <block wx:if="{{item.num==1}}">
                            <image class="image1" src="/images/cart_no_less.png"></image>
                          </block>
                          <view class="row-data">{{item.num}}</view>
                          <block wx:if="{{item.num==item.max_num}}">
                            <image class="image1" src="/images/cart_no_add.png"></image>
                          </block>
                          <block wx:if="{{item.num!=item.max_num}}">
                            <image bindtap="cartAdd" class="image1" id="{{item.cart_guid}}" src="/images/cart_add.png"></image>
                          </block>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
                <view>
                  <block wx:if="{{item.num>item.max_num}}"> <text class="cart-tag">库存不足</text>
                  </block>
                </view>
              </view>
            </view>
          </block>
        </view>
      </block>
      <block wx:if="{{mch_list&&mch_list.length}}">
        <block wx:for="{{mch_list}}" wx:key="{{item.id}}" wx:for-item="item" wx:for-index="mch_index">
          <view class="mch-item">
            <view class="flex flex-row mch-header">
              <view bindtap="checkGroup" class="flex-grow-0 flex-y-center" data-index="{{mch_index}}" data-type="mch" style="padding: 24rpx">
                <view class="cart-checkbox {{item.checked_all?'active':''}}"></view>
              </view>
              <view class="flex-grow-1 flex-y-center">{{item.name}}</view>
            </view>
            <view class="cart-list">
              <block wx:for="{{item.list}}" wx:key="{{item.id}}" wx:for-item="item" wx:for-index="index">
                <view class="cart-item flex-row">
                  <block wx:if="{{item.disabled&&!show_cart_edit}}">
                    <view class="flex-grow-0 flex-y-center" style="padding: 24rpx">
                      <view style="width: 40rpx"></view>
                    </view>
                  </block>
                  <block wx:else>
                    <view bindtap="cartCheck" class="flex-grow-0 flex-y-center" data-index="{{index}}" data-mch-index="{{mch_index}}" data-type="mch" style="padding: 24rpx">
                      <view class="cart-checkbox {{item.checked?'active':''}}"></view>
                    </view>
                  </block>
                  <view class="flex-grow-1">
                    <view class="flex-grow-1 flex-row">
                      <view class="flex-grow-0">
                        <image class="goods-pic" mode="aspectFill" src="{{item.goods_pic}}"></image>
                      </view>
                      <view class="flex-grow-1 flex-col">
                        <view class="flex-grow-1">
                          <view class="goods-name">
                            <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}">{{item.goods_name}}</navigator>
                          </view>
                          <view class="attr-list">
                            <block wx:for="{{item.attr_list}}" wx:key="{{item.id}}" wx:for-item="attr" wx:for-index="index">
                              <view class="attr-item">\n {{attr.attr_group_name}}:{{attr.attr_name}}\n</view>
                            </block>
                          </view>
                        </view>
                        <view class="flex-grow-0 flex-row">
                          <view class="flex-grow-1 price">￥{{item.unitPrice}}</view>
                          <view>
                            <view class="flex-row">
                              <block wx:if="{{item.num!=1}}">
                                <image bindtap="cartLess" class="image1" data-index="{{index}}" data-mch-index="{{mch_index}}" data-type="mch" id="{{item.cart_guid}}" src="{{__wxapp_img.cart.less.url}}"></image>
                              </block>
                              <block wx:if="{{item.num==1}}">
                                <image class="image1" src="{{__wxapp_img.cart.no_less.url}}"></image>
                              </block>
                              <view class="row-data">{{item.num}}</view>
                              <block wx:if="{{item.num==item.max_num}}">
                                <image class="image1" src="{{__wxapp_img.cart.no_add.url}}"></image>
                              </block>
                              <block wx:if="{{item.num!=item.max_num}}">
                                <image bindtap="cartAdd" class="image1" data-index="{{index}}" data-mch-index="{{mch_index}}" data-type="mch" id="{{item.cart_guid}}" src="{{__wxapp_img.cart.add.url}}"></image>
                              </block>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view>
                      <block wx:if="{{item.num>item.max_num}}"> <text class="cart-tag">库存不足</text>
                      </block>
                    </view>
                  </view>
                </view>
              </block>
            </view>
          </view>
        </block>
      </block>
      <view style="height: 109rpx"></view>
    </block>
  </view>
</view>