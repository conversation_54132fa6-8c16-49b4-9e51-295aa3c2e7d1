<!--pages/order/detail.wxml-->
<!--pages/order-detail/order-detail.wxml-->
<view style="overflow-x: hidden">
  <view class="status-bar">
    <image src="/images/img-order-status-bar.png" mode="aspectFill"></image>
    <text wx:if="{{order.status==-3}}">已退单</text>
    <text wx:if="{{order.status==-2}}">已取消</text>
    <text wx:if="{{order.status==-1}}">待支付</text>
    <text wx:if="{{order.status==0}}">
      <block wx:if="{{order.type==1}}"> 待发货 </block>
      <block wx:if="{{order.type==2}}"> 待自提 </block>
    </text>
    <text wx:if="{{order.status==1}}">
      <block wx:if="{{order.type==1}}"> 已发货 </block>
      <block wx:if="{{order.type==2}}"> 已自提 </block>
    </text>
    <text wx:if="{{order.status==2}}">已完成</text>
  </view>
  <view class="block" wx:if="{{order.express_no}}">
    <view class="block-row flex-row" wx:if="{{order.send_out_goods_remark}}">
      <view class="flex-grow-1">发货备注：{{order.send_out_goods_remark}}</view>
    </view>
    <view class="block-row flex-row">
      <view class="flex-grow-1">快递公司：{{order.express_name}}</view>
    </view>

    <view class="block-row flex-row">
      <view class="flex-grow-1">快递单号：{{order.express_no}}
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no}}">复制</text>
        <text class="copy-text-btn" style="margin-left: 20rpx;background-color: #07C160 !important;color: #fff;border: none;" bindtap="query_route" data-express_no="{{order.express_no}}" data-query_route_url="{{order.query_route_url}}">查看物流</text>
      </view>
    </view>
  </view>

  <view class="block" wx:if="{{order.express_no_2}}">
    <view class="block-row flex-row" wx:if="{{order.send_out_goods_remark_2}}">
      <view class="flex-grow-1">发货备注2：{{order.send_out_goods_remark_2}}</view>
    </view>
    <view class="block-row flex-row">
      <view class="flex-grow-1">快递公司2：{{order.express_name_2}}</view>
    </view>

    <view class="block-row flex-row">
      <view class="flex-grow-1">快递单号2：{{order.express_no_2}}
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no_2}}">复制</text>
        <text class="copy-text-btn" style="margin-left: 20rpx;background-color: #07C160 !important;color: #fff;border: none;" bindtap="query_route" data-express_no="{{order.express_no_2}}" data-query_route_url="{{order.query_route_url_2}}">查看物流</text>
      </view>
    </view>
  </view>

  <view class="block" wx:if="{{order.express_no_3}}">
    <view class="block-row flex-row" wx:if="{{order.send_out_goods_remark_3}}">
      <view class="flex-grow-1">发货备注3：{{order.send_out_goods_remark_3}}</view>
    </view>
    <view class="block-row flex-row">
      <view class="flex-grow-1">快递公司3：{{order.express_name_3}}</view>
    </view>

    <view class="block-row flex-row">
      <view class="flex-grow-1">快递单号3：{{order.express_no_3}}
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.express_no_3}}">复制</text>
        <text class="copy-text-btn" style="margin-left: 20rpx;background-color: #07C160 !important;color: #fff;border: none;" bindtap="query_route" data-express_no="{{order.express_no_3}}" data-query_route_url="{{order.query_route_url_3}}">查看物流</text>
      </view>
    </view>
  </view>



  <view class="block" wx:if="{{order.type==1}}">
    <view class="flex-row block-row">
      <view class="flex-grow-1">
        收货人：{{order.true_name}}
      </view>
      <view class="flex-grow-0">
        {{order.mobile}}
      </view>
    </view>
    <view wx:if='{{order.address_info}}'>地址：{{order.address_info}}</view>
  </view>


  <block wx:if="{{order.type == 2}}">
    <view class="block flex-row">
      <view class='flex-grow-1'>
        <view class="flex-grow-1">门店名称：{{order.store_name}}</view>
        <view class="flex-grow-1" bindtap="makePhoneCall" data-phone="{{order.store_mobile}}">联系电话：{{order.store_mobile}}
          <text class="copy-text-btn" bindtap="copyText">点击拨打</text>

        </view>
        <view class="flex-grow-1">门店地址：{{order.store_address}}
          <!-- <view class='flex-grow-0 flex-y-center' wx:if='{{order.store_longitude}}'>
            <text class='copy-text-btn' bindtap='openLocation'>导航</text>
          </view> -->
        </view>
      </view>
      <view class='flex-grow-0 flex-y-center' wx:if='{{order.store_longitude}}' style="margin-top: 60rpx;padding-left: 20rpx;">
        <view class="placeholder" style="text-align: center;padding-right: 20rpx;" bindtap='openLocation'>
          <image src="/images/icon-daohang.png" style="height: 1.2rem;width:1.2rem;"></image>
        </view>
      </view>
    </view>
  </block>

  <view class="block">
    <view class="block-row flex-row">
      <view class="flex-grow-1">订单编号：{{order.bill_number}}</view>
      <view class="flex-grow-0">
        <text class="copy-text-btn" bindtap="copyText" data-text="{{order.bill_number}}">复制</text>
      </view>
    </view>
    <view class="flex-row block-row" wx:if="{{order.pick_up_code}}">
      <view class="flex-grow-1">核销码：{{order.pick_up_code}}

      </view>
      <view class="flex-grow-0">
        <view class="order-option-btn clerk flex-y-center" bindtap="orderQrcode" data-guid="{{order.guid}}" data-pick_up_code="{{order.pick_up_code}}" style="box-sizing:content-box; height: 48rpx;">
          <image src="/images/icon-clerk.png" style='width:36rpx;height:36rpx;margin-right:10rpx' /> 核销码
        </view>
      </view>

    </view>
    <view class="flex-row block-row">
      <view class="flex-grow-1">下单日期：{{order.create_time}}</view>
    </view>

    <block wx:if="{{order.request_send_or_pick_up_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">预约时间：{{order.request_send_or_pick_up_time}}

          <text wx:if="{{order.allow_modify}}" class="copy-text-btn" style="background-color: coral !important;color: #fff;border: none;" bindtap="modify_request_send_or_pick_up_time">修改</text>
        </view>
      </view>
    </block>

    <block>
      <calendar class="calendar" wx:if="{{show_calendar}}" config="{{calendarConfig}}" bind:takeoverTap="takeoverTap" bind:afterTapDate="afterTapDate" bind:afterCalendarRender="afterCalendarRender" bind:onSwipe="onSwipe" bind:whenChangeMonth="whenChangeMonth" />
    </block>

    <block wx:if="{{order.send_or_pick_up_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">发货时间：{{order.send_or_pick_up_time}}</view>
      </view>
    </block>

    <block wx:if="{{order.pay_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">支付时间：{{order.pay_time}}</view>
      </view>
    </block>

    <block wx:if="{{order.cancel_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">取消时间：{{order.cancel_time}}</view>
      </view>
    </block>

    <block wx:if="{{order.confirm_time}}">
      <view class="flex-row block-row">
        <view class="flex-grow-1">确认时间：{{order.confirm_time}}</view>
      </view>
    </block>

    <!-- <block wx:if="{{order.pay_type==0}}">
      <view class="pay-type">支付方式：未支付</view>
    </block> -->
    <block wx:if="{{order.status>=0}}">

      <block wx:if="{{order.pay_type==1}}">
        <view class="flex-row block-row">
          <view class="flex-grow-1">支付方式：微信支付</view>
        </view>
      </block>
      <block wx:if="{{order.pay_type==2}}">

        <view class="flex-row block-row">
          <view class="flex-grow-1">支付方式：余额支付</view>
        </view>
      </block>

      <block wx:if="{{order.paid_wechat>0}}">
        <view class="flex-row block-row">
          <view class="flex-grow-1">微信支付：{{order.paid_wechat}}</view>
        </view>
      </block>

      <block wx:if="{{order.paid_money>0}}">
        <view class="flex-row block-row">
          <view class="flex-grow-1">余额支付：{{order.paid_money}}</view>
        </view>
      </block>

      <block wx:if="{{order.paid_point>0}}">
        <view class="flex-row block-row">
          <view class="flex-grow-1">积分支付：{{order.paid_point}} 元</view>
        </view>
      </block>

      <block wx:if="{{order.paid_coupon>0}}">
        <view class="flex-row block-row">
          <view class="flex-grow-1">卡券支付：{{order.paid_coupon}} 元</view>
        </view>
      </block>

    </block>
    <block wx:if='{{order.id_card_number}}'>
      <view class="flex-row block-row">
        <view class="flex-grow-1">身份证号：{{order.id_card_number}}</view>
      </view>
    </block>

    <block wx:if='{{order.remark}}'>
      <view class="flex-row block-row">
        <view class="flex-grow-1">用户备注：{{order.remark}}</view>
      </view>
      <!-- <view style='width:100%;overflow:auto;word-wrap:break-word;'>{{order.remark}}</view> -->
    </block>


    <block wx:if='{{order.extend_field_1  !== "" && order.extend_field_1_name}}'>
      <view class="flex-row block-row">
        <view class="flex-grow-1">{{order.extend_field_1_name}}：{{order.extend_field_1}}</view>
      </view>
    </block>


    <block wx:if='{{order.extend_field_2  !== ""  && order.extend_field_2_name}}'>
      <view class="flex-row block-row">
        <view class="flex-grow-1">{{order.extend_field_2_name}}：{{order.extend_field_2}}</view>
      </view>
    </block>

    <block wx:if='{{order.extend_field_3  !== "" && order.extend_field_3_name}}'>
      <view class="flex-row block-row">
        <view class="flex-grow-1">{{order.extend_field_3_name}}{{order.extend_field_3}}</view>
      </view>
    </block>

  </view>

  <view class="block">
    <view wx:for="{{order.goods_list}}" class="flex-row goods-item">
      <view class="flex-grow-0">
        <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}" style="font-size: 0">
          <image mode="aspectFill" style="width: 156rpx;height: 156rpx" src="{{item.pic}}"></image>
        </navigator>
      </view>
      <view class="flex-grow-1" style="padding-left: 20rpx">
        <view style="margin-bottom: 10rpx">
          <navigator url="/pages/index/goods_detail?guid={{item.goods_guid}}">{{item.name}}</navigator>
        </view>
        <view class="flex-row">
          <view class="flex-grow-1">
            <view style="font-size: 9pt;color: #888;margin-right: 20rpx;display: inline-block" wx:if="{{item.specs}}">
              规格 ：{{item.specs}}
            </view>
            <view class="flex-grow-1">
              <view style="font-size: 9pt;color: #888;margin-right: 20rpx;display: inline-block" wx:for="{{item.attr_list}}">
                {{item.attr_group_name}}：{{item.attr_name}}
              </view>
            </view>
          </view>
          <view class="flex-grow-0" style="text-align: right">
            <view>×{{item.amount}}</view>
            <view style="color: #ff4544" wx:if="{{item.price>0}}">￥{{item.price}}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="block-footer">
      <view class="flex-grow-0" style="text-align: right" wx:if="{{order.freight_money>0}}">
        <text>运费 </text>
        <text style="color: #ff4544">￥ {{order.freight_money}}</text>
      </view>

      <view class="flex-grow-0" style="text-align: right" wx:if="{{order.extra_charges_money>0}}">
        <text>{{order.extra_charges_name ? order.extra_charges_name :'附加费'}} </text>
        <text style="color: #ff4544">￥ {{order.extra_charges_money}}</text>
      </view>

      合计：{{order.total_amount}}件
      <text style="color: #ff4544" wx:if="{{order.total_money>0}}">￥{{order.total_money}}</text>
    </view>

    <view class="block" wx:if="{{order.enable_refund==1}}">
      　 <navigator data-guid="{{order.guid}}" class="flex-y-center refund-btn" bindtap="apply_refund">
        申请退款
      </navigator>

    </view>
  </view>
</view>
<copy_right></copy_right>
<view style="height: 120rpx;"></view>
<view style="position: fixed;bottom: 0;width: 100%;padding: 10rpx;text-align: center;">
  <navigator style="width: 80%;" class="weui-btn weui-btn_primary" id="js_btn" aria-role="button" bindtap="go_back">返回</navigator>
</view>


<view class='flex-row flex-y-center modal {{hide==1?"hide":""}}' bindtap='hide'>
  <view class='flex-y-center' style='width:100%;height:800rpx;padding:100rpx;'>
    <view style='background-color:#fff;width:100%;height:100%;border-radius:10rpx;padding:0 50rpx;'>
      <view class='flex-x-center' style='width:100%;height:50rpx;margin-top:50rpx;font-size:13pt;margin-bottom:20rpx'>核销二维码</view>
      <image src='{{qrcode}}' style='width:450rpx;height:450rpx;'></image>
    </view>
  </view>
</view>
<!-- <navigator class="weui-btn weui-btn_default" aria-role="button" url="/pages/index/index">返回首页</navigator> -->