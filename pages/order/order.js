// order.js
var api = require('../../api.js');
var app = getApp();
// var is_no_more = false;
// var is_loading = false;
// var p = 2;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: '',
    order_list: [],
    // show_no_data_tip: false,
    hide: 1,
    all: true,
    is_load: false,
    qrcode: ""
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    // is_no_more = false;
    // is_loading = false;
    // p = 2;
    if (options.status) {
      page.setData({
        all: false
      })
    }
    page.loadOrderList(options.status || '');
    var pages = getCurrentPages();
    if (pages.length < 2) {
      page.setData({
        show_index: true,
      });
    }
  },

  loadOrderList: function (status) {
    if (status == undefined)
      status = '';
    var page = this;
    page.setData({
      status: status,
      append_order_detail: false,
    });
    app.request({
      url: api.order.list,
      pagination: 'order_list',
      page: page,
      data: {
        status: page.data.status,
        append_order_detail: false,
      },
      success: function (res) {
        page.setData({
          is_load: true,
        });
      }
    });
  },

  query_route: function (e) {
    var express_no = e.currentTarget.dataset.express_no;
    var order_guid = e.currentTarget.dataset.guid;
    var query_route_url = e.currentTarget.dataset.query_route_url;
    if (query_route_url) {
      app.to_web(query_route_url)
    } else {
      let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
      app.to_web('https://' + extConfig.domain + '/member/tools/kuaidi?bid=' + extConfig.bid + '&coname=www&nu=' + express_no);
    }
  },
  onReachBottom: function () {
    var page = this;
    this.loadOrderList(page.data.status);
    return;
    if (is_loading || is_no_more)
      return;
    is_loading = true;
    app.request({
      url: api.order.list,
      data: {
        status: page.data.status,
        page: p,
      },
      success: function (res) {
        var order_list = page.data.order_list.concat(res.data.data);
        page.setData({
          order_list: order_list,
        });
        if (res.data.current_page == res.data.last_page) {
          is_no_more = true;
        }
        p++;
      },
      complete: function () {
        is_loading = false;
      }
    });
  },
  queryOrder: function (third_pay_bill_number) {
    var that = this;
    app.pay.query(third_pay_bill_number,
      function () {
        that.redirectToOrderList()
      },
      function () {
        wx.redirectTo({
          url: "/pages/order/order?status=-1",
        });
      })
  },
  redirectToOrderList: function () {
    let time = 1500;
    wx.showToast({
      title: '支付成功',
      duration: time,
      success: function () {
        setTimeout(function () {
          //要延时执行的代码
          wx.redirectTo({
            url: "/pages/order/order?status=0",
          })
        }, time)
      }
    })
  },
  orderPay: function (e) {
    let that = this;
    app.request({
      url: api.order.pay_data,
      data: {
        order_guid: e.currentTarget.dataset.guid,
        pay_type: 1,
        scene: 'miniapp'
      },
      success: function (res) {
        console.log(res);
        var pay_options = res.data.pay_options;
        var third_pay_bill_number = res.data.third_pay_bill_number

        wx.requestPayment({
          timeStamp: pay_options.timeStamp,
          nonceStr: pay_options.nonceStr,
          package: pay_options.package,
          signType: pay_options.signType,
          paySign: pay_options.paySign,
          success: function (e) {
            console.log("success");
            console.log(e);
          },
          fail: function (e) {
            console.log("fail");
            console.log(e);
          },
          complete: function (e) {
            console.log("complete");
            console.log(e);
            if (e.errMsg == "requestPayment:fail" || e.errMsg == "requestPayment:fail cancel") {
              //支付失败转到待支付订单列表
              wx.showModal({
                title: "提示",
                content: "订单尚未支付",
                showCancel: false,
                confirmText: "确认",
                success: function (res) {
                  if (res.confirm) {
                    wx.redirectTo({
                      url: "/pages/order/order?status=-1",
                    });
                  }
                }
              });
            } else if (e.errMsg == "requestPayment:ok") {
              that.queryOrder(third_pay_bill_number);
            } else {
              //支付失败转到待支付订单列表
              wx.showModal({
                title: "提示",
                content: "支付失败:" + e.errMsg,
                showCancel: false,
                confirmText: "确认",
                success: function (res) {
                  if (res.confirm) {
                    wx.redirectTo({
                      url: "/pages/order/order?status=-1",
                    });
                  }
                }
              });
            }
          },
        });
      }
    });
  },

  orderRevoke: function (e) {
    var page = this;
    wx.showModal({
      title: "提示",
      content: "是否取消该订单？",
      cancelText: "否",
      confirmText: "是",
      success: function (res) {
        if (res.cancel)
          return true;
        if (res.confirm) {
          wx.showLoading({
            title: "操作中",
          });
          app.request({
            url: api.order.revoke,
            data: {
              order_guid: e.currentTarget.dataset.guid,
            },
            success: function (res) {
              wx.hideLoading();
              wx.showModal({
                title: "提示",
                content: res.msg,
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    page.setData({
                      order_list: [],
                      page: 0,
                    })
                    page.loadOrderList(page.data.status);
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  orderConfirm: function (e) {
    var page = this;
    wx.showModal({
      title: "提示",
      content: "是否确认已收到货？",
      cancelText: "否",
      confirmText: "是",
      success: function (res) {
        if (res.cancel)
          return true;
        if (res.confirm) {
          app.request({
            url: api.order.confirm,
            data: {
              order_guid: e.currentTarget.dataset.guid,
            },
            success: function (res) {
              wx.showToast({
                title: res.msg,
              });
              page.loadOrderList(2);
            }
          });
        }
      }
    });
  },
  orderQrcode: function (e) {
    var page = this;
    var order_list = page.data.order_list;
    var index = e.target.dataset.index;
    var guid = e.target.dataset.guid;
    var pick_up_code = e.target.dataset.pick_up_code;
    app.request({
      url: api.order.get_pickup_qrcode_url,
      data: {
        guid: guid,
        pick_up_code: pick_up_code,
      },
      success: function (res) {
        page.setData({
          hide: 0,
          qrcode: res.data.qrcode_url
        });
      }
    });
  },
  hide: function (e) {
    this.setData({
      hide: 1
    });
  }

});