<!--order.wxml-->
<block wx:if="{{show_tab_bar==1}}">
  <import src="/common/tabbar/tabbar.wxml" />
  <template is="tabbar" data="{{tabbar}}" />
</block>
<navigator class="return-index-btn" open-type="redirect" wx:if="{{show_index}}" url="/pages/index/index">
  <image src="/images/nav-icon-index.png" style="width: 50rpx;height: 50rpx"></image>
</navigator>
<view class="flex-row tab-bar">
  <view class="flex-grow-1">
    <navigator class="{{status=='' && all?'active':''}}" url="/pages/order/order?show_tab_bar={{show_tab_bar}}" open-type="redirect">
      <text>全部</text>
    </navigator>
  </view>
  <view class="flex-grow-1">
    <navigator class="{{status==-1 && !all?'active':''}}" url="/pages/order/order?status=-1&show_tab_bar={{show_tab_bar}}" open-type="redirect">
      <text>待付款</text>
    </navigator>
  </view>
  <view class="flex-grow-1">
    <navigator class="{{status==0 & !all ?'active':''}}" url="/pages/order/order?status=0&show_tab_bar={{show_tab_bar}}" open-type="redirect">
      <text>待发货</text>
    </navigator>
  </view>
  <view class="flex-grow-1">
    <navigator class="{{status==1 & !all ?'active':''}}" url="/pages/order/order?status=1&show_tab_bar={{show_tab_bar}}" open-type="redirect">
      <text>待收货</text>
    </navigator>
  </view>
  <view class="flex-grow-1">
    <navigator class="{{status==2 & !all ?'active':''}}" url="/pages/order/order?status=2&show_tab_bar={{show_tab_bar}}" open-type="redirect">
      <text>已完成</text>
    </navigator>
  </view>
  <!-- <view class="flex-grow-1">
        <navigator class="{{status==4?'active':''}}" url="/pages/order/order?status=4" open-type="redirect">
            <text>售后</text>
        </navigator>
    </view> -->
</view>
<view class="order-list" wx:if="{{order_list.length>0}}">
  <!-- <view wx:if="{{show_no_data_tip}}" class="no-data-tip">暂无相关订单</view> -->
  <view class="order-item" wx:for="{{order_list}}" wx:for-item="order">
    <navigator hover-class="none" url="{{status==4?'/pages/order-refund-detail/order-refund-detail?id='+order.order_refund_id:'/pages/order/detail?order_guid='+order.guid}}">
      <view class="flex-row order-info">
        <view class="flex-grow-1">订单号:{{order.bill_number}}</view>
        <view class="flex-grow-0">{{order.create_time}}</view>
      </view>

      <view class="goods-list">
        <view class="goods-item flex-row" wx:for="{{order.goods_info}}" wx:for-item="goods">
          <view class="flex-grow-0" style="overflow: visible">
            <image class="goods-pic" mode="aspectFill" src="{{goods.pic}}" />
          </view>
          <view class="flex-grow-1 flex-col">
            <view class="flex-grow-1">
              <view class="goods-name">{{goods.name}}</view>
              <view class="goods-name" wx:if="{{goods.specs}}" style="font-size: 24rpx; background-color: #EDF1FC;padding:4rpx 10rpx 4rpx 10rpx;display: inline-block;border-radius: 4rpx;">{{goods.specs}}</view>
              <view class="attr-list">
                <view class="attr-item" wx:for="{{goods.attr_list}}" wx:for-item="attr">
                  {{attr.attr_group_name}}:{{attr.attr_name}}
                </view>
              </view>
            </view>
            <view class="flex-grow-0 flex-row">
              <view class="flex-grow-1 num">×{{goods.amount}}</view>
              <view class="flex-grow-0 price">￥{{goods.price}}</view>
            </view>
          </view>
        </view>
      </view>
    </navigator>
    <view class="flex-row">
      <view class="flex-grow-1 flex-y-center">
        <block wx:if="{{status==4}}">
          <block wx:if="{{order.refund_type==1}}">
            退货退款：
            <text style="color: #ff4544">￥{{order.refund_price}}</text>
          </block>
          <block wx:if="{{order.refund_type==2}}">
            换货
          </block>
        </block>
        <block wx:else>合计：￥{{order.total_money}}</block>
      </view>
      <view class="flex-grow-0 flex-y-center flex-row">
        <block wx:if="{{status==4}}">
          <view wx:if="{{order.refund_status==0}}">等待处理</view>
          <view style="color: #36ba75" wx:if="{{order.refund_status==1}}">已同意并退款</view>
          <view style="color: #36ba75" wx:if="{{order.refund_status==2}}">已同意换货</view>

          <block wx:if="{{order.refund_status==3}}">
            <view style="color: #ff4544" wx:if="{{order.refund_type==1}}">已拒绝退货退款</view>
            <view style="color: #ff4544" wx:if="{{order.refund_type==2}}">已拒绝换货</view>
          </block>
        </block>
        <block wx:else>
          <view class="flex-grow-1" wx:if="{{order.status==-1}}">
            <text wx:if="{{order.apply_delete==1}}">取消申请中</text>
            <button wx:else class="order-option-btn" bindtap="orderRevoke" data-guid="{{order.guid}}">取消
            </button>
          </view>
          <view class="flex-grow-1" wx:if="{{order.status==-3}}">
            <button style="width: 200rpx !important;" class="order-option-btn" data-guid="{{order.guid}}">已退单</button>
          </view>
          <view class="flex-grow-1" wx:if="{{order.status==-1}}">
            <button class="order-option-btn red" bindtap="orderPay" data-guid="{{order.guid}}">付款</button>
          </view>
          <view class="flex-grow-1" wx:if="{{order.pick_up_code}}">
            <view class="order-option-btn clerk flex-y-center" bindtap="orderQrcode" data-index="{{index}}" data-guid="{{order.guid}}" data-pick_up_code="{{order.pick_up_code}}" style="box-sizing:content-box;">
              <image src="/images/icon-clerk.png" style='width:32rpx;height:32rpx;margin-right:10rpx' data-index="{{index}}" /> 核销码
            </view>
          </view>
          <view class="flex-grow-1 flex-row" wx:if="{{order.status==1}}">
            <view class="flex-grow-0" wx:if='{{order.express_code}}'>
              <navigator style="text-align: center;" class="order-option-btn" bindtap="query_route" data-express_no="{{order.express_no}}" data-order_guid="{{order.guid}}" data-query_route_url="{{order.query_route_url}}">物流
              </navigator>
            </view>
            <view class="flex-grow-0">
              <button style="width: 200rpx !important;" class="order-option-btn red" bindtap="orderConfirm" data-guid="{{order.guid}}">确认收货
              </button>
            </view>
          </view>
          <view class="flex-grow-1" wx:if="{{order.is_send==1&&order.is_confirm==1&&order.is_comment==0}}">
            <navigator class="order-option-btn" url="/pages/order-comment/order-comment?id={{order.guid}}">评价
            </navigator>
          </view>
        </block>
      </view>
    </view>
  </view>
  <include src="/common/loadmore/loadmore"></include>

</view>
<block wx:if="{{order_list.length==0 && is_load===true}}">
  <view class="weui-msg" style="background-color: #fff;padding-top: 200rpx;">
    <view class="weui-msg__icon-area"><text style="width: 5rem;height:5rem;" class="weui-icon-info weui-icon_msg"></text></view>
    <view class="weui-msg__text-area">
      <view class="weui-msg__title">暂无订单</view>
    </view>
  </view>
</block>
<view class='flex-row flex-y-center modal {{hide==1?"hide":""}}' bindtap='hide'>
  <view class='flex-y-center' style='width:100%;height:800rpx;padding:100rpx;'>
    <view style='background-color:#fff;width:100%;height:100%;border-radius:10rpx;padding:0 50rpx;'>
      <view class='flex-x-center' style='width:100%;height:50rpx;margin-top:50rpx;font-size:13pt;margin-bottom:20rpx'>核销二维码</view>
      <image src='{{qrcode}}' style='width:450rpx;height:450rpx;'></image>
    </view>
  </view>
</view>