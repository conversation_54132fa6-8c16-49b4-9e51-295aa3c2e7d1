/* pages/order/detail.wxss */
/* pages/order-detail/order-detail.wxss */
page {
  overflow-x: hidden;
}

.status-bar {
  box-sizing: border-box;
  padding: 0 88rpx;
  color: #fff;
  font-size: 13pt;
  height: 120rpx;
  line-height: 120rpx;
  position: relative;
}

.status-bar image {
  position: absolute;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.block {
  background: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;
}

.block .block-row {
  margin-bottom: 10rpx;
}

.block .block-footer {
  border-top: 1rpx solid #eee;
  margin-left: -24rpx;
  margin-right: -24rpx;
  padding: 24rpx;
  padding-bottom: 0;
  margin-top: 32rpx;
  text-align: right;
  font-weight: bold;
}

.goods-item {
  /* border-bottom: 1rpx solid #E3E3E3; */
  padding-bottom: 32rpx;
  margin-bottom: 32rpx;
}

.goods-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.refund-btn {
  float: right;
  height: 60rpx;
  border: 1rpx solid #bbb;
  padding: 0 30rpx;
  border-radius: 10rpx;
  margin-top: -10rpx;
}

.refund-text {
  display: inline-block;
  margin-top: 20rpx;
}

.pay-type {
  width: 100%;
  padding: 32rpx 0 8rpx 0;
  border-top: 1rpx solid #e2e2e2;
  margin-top: 32rpx;
}

.order-option-btn {
  border: 1rpx solid #bbb;
  background: #fff;
  border-radius: 10rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: inherit;
  margin-left: 30rpx;
  padding: 0 30rpx
}

.order-option-btn::after {
  display: none
}

.hide {
  display: none;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}