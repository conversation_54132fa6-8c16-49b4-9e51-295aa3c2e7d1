// pages/order/detail.js
import todo from '../../component/calendar/plugins/todo';
import selectable from '../../component/calendar/plugins/selectable';
import solarLunar from '../../component/calendar/plugins/solarLunar/index';
import timeRange from '../../component/calendar/plugins/time-range';
import week from '../../component/calendar/plugins/week';
import holidays from '../../component/calendar/plugins/holidays/index';
import plugin from '../../component/calendar/plugins/index';

plugin
  .use(todo)
  .use(solarLunar)
  .use(selectable)
  .use(week)
  .use(timeRange)
  .use(holidays)
var app = getApp(); //获取应用实例

Page({
  /**
   * 日历初次渲染完成后触发事件，如设置事件标记
   */
  afterCalendarRender(e) {
    console.log('afterCalendarRender', e);
    const calendar = this.selectComponent('.calendar').calendar
    // calendar.jump({
    //   year: 2018,
    //   month: 6,
    //   date: 6
    // });
    console.log(this.data.disable_date_list);
    calendar.disableDates(this.data.disable_date_list);

    // disable_date_after
    // calendar.setCalendarConfig({
    //   theme: 'elegant',
    //   disableMode: {
    //     // 禁用某一天之前/之后的所有日期
    //     // type: 'after', // ['before', 'after']
    //     // date: '2022-04-29' // 无该属性或该属性值为假，则默认为当天
    //   },
    // });

  },
  /**
   * 日期点击事件（此事件会完全接管点击事件），需自定义配置 takeoverTap 值为真才能生效
   * currentSelect 当前点击的日期
   */
  takeoverTap(e) {
    console.log('takeoverTap', e.detail) // => { year: 2019, month: 12, date: 3, ...}
  },
  /**
   * 选择日期后执行的事件
   */
  afterTapDate(e) {
    console.log('afterTapDate', e.detail) // => { year: 2019, month: 12, date: 3, ...}
    let date = e.detail.year + '-' + e.detail.month + '-' + e.detail.date;
    console.log(date) // => { year: 2019, month: 12, date: 3, ...}

    // var now = new Date();
    // let today = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
    // console.log(date);
    // console.log(today);
    // if (date <= today) {
    //   wx.showToast({
    //     title: '请不要选择今天或者更早的日期哦',
    //     icon: 'none',
    //     duration: 1500
    //   })
    //   return false;
    // }
    //todo http请求
    let that = this;
    app.request({
      url: app.api.code.modify_order,
      data: {
        request_send_or_pick_up_time: date,
        order_guid: that.data.order.guid
      },
      success: function (result) {
        wx.showToast({
          title: '修改成功',
          icon: 'success',
          duration: 2000
        })
        let order = that.data.order;
        order.request_send_or_pick_up_time = date;
        that.setData({
          show_calendar: false,
          order: order,
        });
      },
    });

    this.setData({
      request_send_or_pick_up_time: date,
      calendar: false
    });
  },
  /**
   * 当日历滑动时触发
   */
  onSwipe(e) {
    console.log('onSwipe', e.detail)
  },
  /**
   * 当日历滑动时触发(适用于周视图)
   * 可在滑动时按需在该方法内获取当前日历的一些数据
   */
  whenChangeWeek(e) {
    console.log('whenChangeWeek', e.detail)
  },
  /**
   * 当改变月份时触发
   * => current 当前年月 / next 切换后的年月
   */
  whenChangeMonth(e) {
    console.log('whenChangeMonth', e.detail)
    // => { current: { month: 3, ... }, next: { month: 4, ... }}
  },
  /**
   * 页面的初始数据
   */
  data: {
    options: {},
    order: {},
    hide: 1,
    show_calendar: false,
    disable_date_list: {},
    calendarConfig: {
      multi: false, // 是否开启多选,
      //weekMode: true, // 周视图模式
      theme: 'default', // 日历主题，目前共两款可选择，默认 default 及 elegant，自定义主题色在参考 /theme 文件夹
      //showLunar: true, // 是否显示农历，此配置会导致 setTodoLabels 中 showLabelAlways 配置失效
      inverse: true, // 单选模式下是否支持取消选中,
      //markToday: '今', // 当天日期展示不使用默认数字，用特殊文字标记
      //hideHeader: true, // 隐藏日历头部操作栏
      //takeoverTap: true, // 是否完全接管日期点击事件（日期不会选中)
      emphasisWeek: true, // 是否高亮显示周末日期
      chooseAreaMode: true, // 开启日期范围选择模式，该模式下只可选择时间段
      //showHolidays: true, // 显示法定节假日班/休情况，需引入holidays插件
      //showFestival: true, // 显示节日信息（如教师节等），需引入holidays插件
      highlightToday: true, // 是否高亮显示当天，区别于选中样式（初始化时当天高亮并不代表已选中当天）
      //defaultDate: '2018-3-6', // 默认选中指定某天，如需选中需配置 autoChoosedWhenJump: true
      preventSwipe: false, // 是否禁用日历滑动切换月份
      //firstDayOfWeek: 'Mon', // 每周第一天为周一还是周日，默认按周日开始
      onlyShowCurrentMonth: true, // 日历面板是否只显示本月日期
      autoChoosedWhenJump: true, // 设置默认日期及跳转到指定日期后是否需要自动选中
      disableMode: {
        // 禁用某一天之前/之后的所有日期
        type: 'before', // ['before', 'after']
        date: '' // 无该属性或该属性值为假，则默认为当天
      },
    }
  },
  hide: function (e) {
    this.setData({
      hide: 1
    });
  },
  orderQrcode: function (e) {
    var page = this;
    var guid = e.target.dataset.guid;
    var pick_up_code = e.target.dataset.pick_up_code;
    app.request({
      url: app.api.order.get_pickup_qrcode_url,
      data: {
        guid: guid,
        pick_up_code: pick_up_code,
      },
      success: function (res) {
        page.setData({
          hide: 0,
          qrcode: res.data.qrcode_url
        });
      }
    });
  },
  makePhoneCall(e) {
    let phoneNumber = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: (res) => {},
      fail: (res) => {},
      complete: (res) => {},
    })
  },
  openLocation() {
    if (this.data.order.store_latitude && this.data.order.store_longitude) {
      wx.openLocation({
        latitude: parseFloat(this.data.order.store_latitude),
        longitude: parseFloat(this.data.order.store_longitude),
        scale: 16,
        name: this.data.order.store_name,
        address: this.data.order.store_address,
      })
    }
  },
  copyText: function (e) {
    var page = this;
    var text = e.currentTarget.dataset.text;
    wx.setClipboardData({
      data: text,
      success: function () {
        wx.showToast({
          title: "已复制"
        });
      }
    });
  },
  go_back: function () {
    wx.navigateBack({
      delta: 1
    })
  },
  apply_refund: function (e) {
    var order_guid = e.currentTarget.dataset.guid;
    let that = this;
    wx.showModal({
      title: "提示",
      content: "确认申请退单吗？",
      success: function (result) {
        if (result.cancel) return !0;
        app.request({
          url: app.api.order.apply_refund,
          data: {
            order_guid: order_guid
          },
          success: function (result) {
            wx.showModal({
              title: "提示",
              content: result.msg,
              showCancel: false,
              success: function (e) {
                if (e.confirm) {
                  that.onLoad(that.data.options)
                }
              }
            });
          }
        })
      }
    })
  },
  query_route: function (e) {
    var express_no = e.currentTarget.dataset.express_no;
    var order_guid = e.currentTarget.dataset.guid;
    var query_route_url = e.currentTarget.dataset.query_route_url;
    if (query_route_url) {
      app.to_web(query_route_url)
    } else {
      let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
      app.to_web('https://' + extConfig.domain + '/member/tools/kuaidi?bid=' + extConfig.bid + '&coname=www&nu=' + express_no);
    }
  },
  modify_request_send_or_pick_up_time: function () {
    console.log('modify_request_send_or_pick_up_time');
    let that = this;
    app.request({
      url: app.api.code.get_disable_date_list,
      data: {
        coupon_guid: that.data.order.coupon_guid
      },
      success: function (result) {
        console.log(result);
        that.setData({
          disable_date_list: result.data.disable_date_list,
          show_calendar: true
        });
      },
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);

    console.log(options);
    var that = this;

    this.setData({
      options: options
    });
    app.request({
      url: app.api.order.detail,
      data: {
        guid: this.data.options.order_guid
      },
      success: function (result) {
        console.log(result);
        that.setData({
          order: result.data
        });
      },
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})