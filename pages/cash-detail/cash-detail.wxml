<view class="info">
  <view class="info-title flex-row">
    <navigator class="flex-x-center width" url="/pages/cash-detail/cash-detail" open-type="redirect">
      <view class="info-text {{status == ''  && all ?'active':''}}">全部</view>
    </navigator>
    <navigator class="flex-x-center  width" url="/pages/cash-detail/cash-detail?status=0" open-type="redirect">
      <view class="info-text {{status == 0 && !all ?'active':''}}">待审核</view>
    </navigator>
    <navigator class="flex-x-center  width" url="/pages/cash-detail/cash-detail?status=1" open-type="redirect">
      <view class="info-text {{status == 1 && !all ?'active':''}}">待打款</view>
    </navigator>
    <navigator class="flex-x-center  width" url="/pages/cash-detail/cash-detail?status=2" open-type="redirect">
      <view class="info-text {{status == 2  && !all ?'active':''}}">已打款</view>
    </navigator>
    <navigator class="flex-x-center  width" url="/pages/cash-detail/cash-detail?status=-1" open-type="redirect">
      <view class="info-text {{status == -1   && !all ?'active':''}}">已拒绝</view>
    </navigator>
  </view>
  <view style="margin-top:100rpx;">
    <block wx:if="{{show_no_data_tip}}">
      <view class="info-no">暂无信息</view>
    </block>
    <block wx:else>
      <block wx:for="{{cash_list}}">
        <view class="info-content">
          <view class="info-label flex-row">
            <view class="info-left" style="width: 80%;">
              <view class="info-up">
                <text wx:if="{{item.pay_type=='alipay'}}">支付宝</text>
                <text wx:if="{{item.pay_type=='wechat'}}">微信</text>
                <text wx:if="{{item.pay_type=='bank'}}">银行卡</text>
                <text>提现</text>
                <text style="padding: 0 5rpx 0 5rpx;font-weight: bolder;color: coral;">{{item.cash_money}}</text>
                <text>元</text>
              </view>


              <view class="info-up" wx:if="{{item.pay_type=='alipay'}}">
                <text>({{item.alipay_true_name}}-{{item.alipay_account}})</text>
              </view>

              <view class="info-up" wx:if="{{item.pay_type=='wechat'}}">
                <text>({{item.wechat_true_name}}-{{item.wechat_account}})</text>
              </view>

              <block wx:if="{{item.pay_type=='bank'}}">
                <view class="info-up">
                  <text>({{item.bank_true_name}}-{{item.bank_name}})</text>
                </view>

                <view class="info-up">
                  <text>{{item.bank_account}}</text>
                </view>

              </block>

              <view class="info-down">
                <text>{{item.create_time}}</text>
                <text wx:if="{{item.memo}}"  style="float: right;">{{item.memo}}</text>

              </view>
            </view>
            <view class="info-right" style="width: 20%;">{{item.status_text}}</view>
          </view>
        </view>
      </block>
    </block>
  </view>
</view>