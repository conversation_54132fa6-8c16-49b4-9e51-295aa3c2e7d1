/* pages/cash-detail/cash-detail.wxss */
.info{
  width: 100%;
  /* padding-top: 20rpx; */
}
.width{
  width: 20%;
  text-align: center;
}

.active{
  color:#ff4544;
  border-bottom: 4rpx #ff4544 solid;
}
.info .info-title{
  width: 100%;
  height: 100rpx;
  padding: 0 24rpx;
  border-bottom: 1rpx #ccc solid;
  /* font-weight: bold; */
  background-color: #fff;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
}
.info .info-title .info-text{
  height: 100rpx;
  line-height: 100rpx;
}
.info .info-content{
  width: 100%;
  padding: 0 24rpx;
  background-color: #fff;
}
.info .info-content .info-label{
  border-bottom: 1rpx #ccc solid;
  padding: 24rpx 0;
  width: 100%;
}
.info .info-content:last-child .info-label{
  border: none;
}
.info-content .info-label .info-left{
  width: 60%;
}
.info-content .info-label .info-left .info-up{
  /* font-weight: bold; */
  color: #353535;
  margin-bottom: 20rpx;
}
.info-content .info-label .info-left .info-down{
  font-size: 9pt;
  color: #666;
}
.info-content .info-label .info-right{
  width: 40%;
  text-align: right;
  line-height: 100rpx;
  color: #ff4544;
  /* font-weight: bold; */
}
.info-no{
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  padding: 10rpx;
  color: #666;
}