/* list.wxss */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}

.top-bar{
    position: fixed;
    top:0;
    left: 0;
    right: 0;
    width: 100%;
    overflow: hidden;
    background: #fff;
    z-index: 10;
    border-top: 1rpx solid #E3E3E3;
}

.cat-list {
    white-space: nowrap;
    border-bottom: 1rpx solid #e3e3e3;
    background: #fff;
}
.cat-list .list-content{
    white-space: nowrap;
    height: 100rpx;
}

.cat-list .cat-item {
    white-space: nowrap;
    display: inline-block;
    padding: 0 40rpx;
    height: 100%;
}
.cat-list .cat-item text{
    white-space: nowrap;
    border-bottom: 5rpx solid transparent;
    height: 100%;
    transform: translateY(1rpx);
}
.cat-list .cat-item.active text{
    color: #ff4544;
    border-bottom-color: #ff4544;
}

.sub-cat-list{
    white-space: nowrap;
    border-bottom: 1rpx solid #e3e3e3;
    background: #fff;
}
.sub-cat-list scroll-view{
    height: 100rpx;
}
.sub-cat-list .list-content{
    white-space: nowrap;
    height: 100rpx;
    padding: 0 20rpx;
}

.sub-cat-list .cat-item{
    display: inline-block;
    height: 60rpx;
    padding: 0 20rpx;
    border: 1rpx solid #ff4544;
    margin: 20rpx;
    color: #ff4544;
    border-radius: 5rpx;
}

.sub-cat-list .cat-item text{
    height: 100%;
}

.sub-cat-list .cat-item.active{
    background: #ff4544;
    color: #fff;
}


.top-bar ~ .goods-list{
    padding-top: 106rpx;
}
.top-bar.height-bar ~ .goods-list{
    padding-top: 206rpx;
}

.goods-item{
    width: 365rpx;
    display: inline-block;
    position: relative;
    margin: 0 5rpx;
    margin-bottom: 10rpx;
    font-size: 0;
    background: #fff;
}
.goods-item .goods-pic{
    width: 100%;
    height: 365rpx;
}
.goods-item .goods-info {
    padding: 5rpx 0;
}
.goods-item .goods-name {
    white-space: nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
    padding: 10rpx;
    font-size: 11pt;
    display: block;
    text-align: center;
}

.goods-item .goods-price{
    font-size: 11pt;
    color: #f40;
    display: block;
    text-align: center;
}

.loading-bar{
    visibility: hidden;
}
.loading-bar.active{
    visibility: visible;
}

.sort-bar{
    height: 100rpx;
}

.sort-item.active{
    color: #ff4544;
}

.sort-item .sort-icon-box{
    margin-left: 15rpx;
}

.sort-item .sort-icon-up,
.sort-item .sort-icon-down{
    width: 20rpx;
    height: 12rpx;
    display: block;
}
.sort-item .sort-icon-up{
    margin-bottom: 5rpx;
}