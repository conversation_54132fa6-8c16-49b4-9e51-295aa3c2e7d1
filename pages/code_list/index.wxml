<block wx:if="{{show_tab_bar==1}}">
  <import src="/common/tabbar/tabbar.wxml" />
  <template is="tabbar" data="{{tabbar}}" />
</block>
<view class="top-bar flex-row">
  <navigator hover-class="none" class="flex-grow-1 {{status==0?'active':''}}" openType="redirect" url="/pages/code_list/index?status=0&show_tab_bar={{show_tab_bar}}">
    <text>可使用({{available_use_num}})</text>
  </navigator>
  <navigator hover-class="none" class="flex-grow-1 {{status==1?'active':''}}" openType="redirect" url="/pages/code_list/index?status=1&show_tab_bar={{show_tab_bar}}">
    <text>已使用({{used_num}})</text>
  </navigator>
  <navigator hover-class="none" class="flex-grow-1 {{status==2?'active':''}}" openType="redirect" url="/pages/code_list/index?status=2&show_tab_bar={{show_tab_bar}}">
    <text>已过期({{expired_num}})</text>
  </navigator>
</view>
<view class="coupon-list" wx:if="{{list&&list.length>0}}">
  <block wx:for="{{list}}" wx:for-item="coupon">
    <block>
      <view class="coupon-item coupon-status-{{coupon.status}}" style="margin-top:20rpx;" catchtap="goto_detail" data-coupon_send_note_guid="{{coupon.guid}}">
        <image class="coupon-bg" src="/images/img-coupon-bg-{{coupon.status==0?0:1}}.png"></image>
        <image class="coupon-status-icon" src="/images/img-coupon-status-icon-{{coupon.status}}.png" wx:if="{{coupon.status!=0}}"></image>
        <view class="flex-row" style="height: 100%;overflow: hidden">
          <view class="flex-grow-0 flex-col flex-y-center flex-x-center coupon-left">
            <view class="flex-row flex-y-bottom">
              <view style="font-size: 9pt"></view>
              <view wx:if="{{coupon.type==1}}" style="font-size: {{coupon.send_num.length>2?'13':'19'}}pt;line-height: .9">
                {{coupon.send_num}}次
              </view>

              <view wx:if="{{coupon.type==2 || coupon.type==3}}" style="font-size: {{coupon.send_value.length>2?'13':'19'}}pt;line-height: .9">
                {{coupon.send_value}}{{config.money_unit}}
              </view>

            </view>

            <view wx:if="{{coupon.type==1 && status !=1}}" style="font-size: 8pt;margin-top: 10rpx">已用 {{coupon.used_num}} 次</view>
            <view wx:if="{{(coupon.type==2 || coupon.type==3 ) && status !=1}}" style="font-size: 8pt;margin-top: 10rpx">已用 {{coupon.used_value}} {{config.money_unit}}</view>
          </view>
          <view class="flex-grow-1 flex-y-center coupon-right">
            <view style="width:100%;">
              <view class="flex-row flex-y-center mb-10" style="margin-bottom:5rpx;">
                <view wx:if="{{coupon.name.length<=12}}" class="flex-grow-1" style="font-size: 12pt;">{{coupon.name}}</view>
                <view wx:elif="{{coupon.name.length<=16}}" class="flex-grow-1" style="font-size: 10pt;">{{coupon.name}}</view>
                <view wx:else class="flex-grow-1" style="font-size: 9pt;">{{coupon.name}}</view>
              </view>
              <view style="font-size: 8pt;color: #666666">{{coupon.availability_time}} ~ {{coupon.expire_time}}</view>
              <!-- <text class="user_coupon_font" wx:if="{{coupon.appoint_type==1&&coupon.cat.length==0}}">全场通用</text>
            <text class="user_coupon_font" wx:if="{{coupon.appoint_type==2&&coupon.goods.length==0}}">全场通用</text> -->
              <text class="user_coupon_font" wx:if="{{coupon.appoint_type==null}}"></text>

              <text wx:if="{{coupon.status==0}}" class="copy-text-btn" style="margin-left: 20rpx;background-color: #07C160 !important;color: #fff;border: none;" catchtap="goto_use" data-coupon_send_note_guid="{{coupon.guid}}" data-coupon_type="{{coupon.type}}">立即使用</text>
              <text wx:if="{{coupon.status==0 && coupon.share_status ==1}}" class="copy-text-btn" style="margin-left: 20rpx;background-color: #f36827 !important;color: #fff;border: none;" catchtap="goto_share" data-coupon_send_note_guid="{{coupon.guid}}">转赠</text>
              <view wx:if="{{coupon.status==0}}" style="float: right;">
                <image style="width: 40rpx;height: 40rpx;" src="/images/qrcode.png" catchtap="show_verify_qrcode" data-coupon_send_note_guid="{{coupon.guid}}"></image>
              </view>

              <block wx:if="{{coupon.appoint_type==1&&coupon.cat.length>0}}">
                <view class="user_coupon_font" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;width:87%;">
                  仅限<text wx:for="{{coupon.cat}}" wx:for-item="cat" wx:key="id">{{cat.name}}、</text>产品使用
                </view>
                <block wx:if="{{coupon.cat.length>2}}">
                  <image bindtap="xia" class="xia" data-index="{{index}}" src="/images/user-coupon-xia.png" wx:if="{{index!=check}}"></image>
                  <image bindtap="shou" class="xia" data-index="{{index}}" src="/images/shou.png" wx:if="{{index==check}}"></image>
                </block>
              </block>
              <view bindtap="goodsList" class="user_coupon_font" data-goods_id="{{coupon.goods}}" style="height:50rpx;" wx:if="{{coupon.appoint_type==2&&coupon.goods.length>0}}">指定商品使用 点进去查看指定商品</view>
            </view>
          </view>
        </view>
      </view>
      <block wx:if="{{coupon.cat.length>2}}">
        <view class="user_coupon" style="padding:10rpx 24rpx;background:#fff;" wx:if="{{check==index}}">
          仅限<text wx:for="{{coupon.cat}}" wx:for-item="cat" wx:key="id">{{cat.name}}、</text>产品使用
        </view>
      </block>
    </block>
  </block>
</view>
<view style="padding-top: 200rpx;color: #888;text-align: center" wx:else>暂无相关卡券</view>

<view style="height: 120rpx;"></view>
<view style="position: fixed;bottom: 0;width: 100%;padding-bottom: {{show_tab_bar==1 ? 150 : 10 }}rpx;text-align: center;">
  <button style="width: 80%;" class="weui-btn weui-btn_primary" id="js_btn" aria-role="button" bindtap="show_dialog">绑定卡券</button>
</view>

<view aria-role="dialog" aria-modal="true">
  <view class="weui-mask weui-transition {{dialog ? 'weui-transition_show' : ''}}" bindtap="close" aria-role="button" aria-label="关闭"></view>
  <view class="weui-half-screen-dialog weui-half-screen-dialog_large weui-transition {{dialog ? 'weui-transition_show' : ''}}">
    <view class="weui-half-screen-dialog__hd">
      <view class="weui-half-screen-dialog__hd__side" bindtap="close">
        <view aria-role="button" class="weui-icon-btn">关闭<i class="weui-icon-close-thin"></i></view>
      </view>
      <view class="weui-half-screen-dialog__hd__main">
        <strong class="weui-half-screen-dialog__title">绑定卡密</strong>
      </view>
    </view>
    <view class="weui-half-screen-dialog__bd" style="padding-top: 32px; height: 50px;">

      <text style="color: #999;padding-left: 40rpx;"> 您可以将实体卡绑定至您的账户 </text>

      <form bindsubmit="bind_code">

        <view class="top-bar flex-row">
          <view class="flex-grow-1 {{bind_type==1?'active':''}}" bindtap="show_code_password">
            <text>卡号密码</text>
          </view>
          <view class="flex-grow-1 {{bind_type==2?'active':''}}" bindtap="show_phone_verify_code">
            <text>手机验证码</text>
          </view>
        </view>


        <view class="weui-cells weui-cells_after-title" wx:if="{{bind_type==1}}">
          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">卡号</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="text" name='code' value="{{code}}" focus="{{false}}" placeholder="请输入卡号" />
            </view>
            <view bindtap="scan">
              <image class="image" style="width: 40rpx;height: 40rpx;" src="/images/scan.png" alt></image>
            </view>
          </view>

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">密码</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="password" name='password' value="{{password}}" focus="{{password_focus}}" placeholder="请输入密码" />
            </view>
          </view>
        </view>


        <view class="weui-cells weui-cells_after-title" wx:if="{{bind_type==2}}">

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">手机号</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="number" bindinput="phoneChange" name='phone' value="{{phone}}" placeholder="请输入手机号" />
            </view>

          </view>

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">验证码</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="number" name='verify_code' value="{{verify_code}}" focus="{{verify_code_focus}}" placeholder="输入验证码" />
            </view>
            <view aria-role="button" class="weui-cell__control weui-btn weui-btn_default   vcode-btn" bindtap="get_verify_code">{{get_verify_code_text}}</view>
          </view>

        </view>



        <view class="weui-btn-area" style="margin: 20px 0 0 0;">
          <button class='weui-btn' style="width: 90%;" type="primary" formType="submit">确认绑定</button>
        </view>
      </form>
    </view>
    <view class="weui-half-screen-dialog__ft">
      <view class="weui-half-screen-dialog__btn-area">
        <view aria-disabled="true" class="weui-btn weui-btn_default" aria-role="button" bindtap="close">暂不绑定</view>
      </view>
    </view>
  </view>
</view>

<view aria-role="dialog" aria-modal="true" class="fadeIn" wx:if="{{qrcode_dialog}}">
  <view class="weui-mask"></view>
  <view class="weui-dialog" style="text-align: center;width: 90%;">
    <view class="weui-dialog__hd"><strong class="weui-dialog__title">请出示给商家</strong></view>
    <view class="weui-dialog__bd" wx:if="{{verify_qrcode}}" style="text-align: center;">
      <image mode="aspectFit" src="{{verify_qrcode}}"></image>
    </view>
    <view class="weui-dialog__ft">
      <view aria-role="button" class="weui-dialog__btn weui-dialog__btn_primary" bindtap="close">知道了</view>
    </view>
  </view>
</view>