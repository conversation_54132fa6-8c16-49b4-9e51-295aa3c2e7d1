.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1rpx solid #e3e3e3;
  border-bottom: 1rpx solid #e3e3e3;
  z-index: 999;
}

.top-bar navigator {
  text-align: center;
}

.top-bar navigator text {
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 2rpx solid transparent;
  width: auto;
  display: inline-block;
}

.top-bar navigator.active text {
  color: #ff4544;
  border-bottom-color: #ff4544;
}

.coupon-list {
  padding: 20rpx 34rpx 20rpx 34rpx;
}

.coupon-list .coupon-item {
  height: 152rpx;
  width: 682rpx;
  position: relative;
}

.coupon-list .coupon-item.coupon-status-1 .coupon-right,
.coupon-list .coupon-item.coupon-status-2 .coupon-right {
  color: rgba(0, 0, 0, .35) !important;
}

.coupon-list .coupon-item .coupon-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.coupon-list .coupon-item .coupon-status-icon {
  width: 140rpx;
  height: 98rpx;
  position: absolute;
  top: 0;
  right: 8rpx;
  z-index: 1;
}

.coupon-list .coupon-item .coupon-left {
  color: #fff;
  width: 202rpx;
}

.coupon-list .coupon-item .coupon-right {
  padding: 20rpx 10rpx;
}

.user_coupon_font {
  font-size: 8pt;
  color: #666666;
  margin-top: 10rpx;
}

.user_coupon {
  font-size: 8pt;
  color: #666666;
}

.xia {
  position: absolute;
  width: 28rpx;
  height: 28rpx;
  right: 15px;
  bottom: 9px;
}


/*TAB页面*/
.top-bar {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  /* border-top: 1rpx solid #e3e3e3; */
  /* border-bottom: 1rpx solid #e3e3e3; */
  z-index: 999;
}

.top-bar view {
  text-align: center;
}

.top-bar view text {
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 1rpx solid transparent;
  width: auto;
  display: inline-block;
}

.top-bar view.active text {
  color: #ff4544;
  border-bottom-color: #ff4544;
}

.vcode-btn {
  font-size: 16px;
  padding: 0 12px;
  height: auto;
  width: auto;
  line-height: 2;
  border-radius: 6px;
  color: var(--weui-BTN-DEFAULT-COLOR);
  background-color: var(--weui-BTN-DEFAULT-BG);
}