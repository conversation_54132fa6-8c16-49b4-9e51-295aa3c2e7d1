// pages/code_list/index.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    show_tab_bar: 0,
    code: '',
    password: '',
    password_focus: false,
    dialog: false,
    verify_qrcode: '',
    qrcode_dialog: false,
    bind_type: 1,
    get_verify_code_text: '获取验证码', //倒计时 
    currentTime: 60, //限制60s
    is_allow_get_verify_code: true, //获取验证码按钮，默认允许点击
  },
  close: function (e) {
    this.setData({
      dialog: false,
      qrcode_dialog: false
    })
  },
  get_verify_code: function () {
    if (this.data.is_allow_get_verify_code == false) {
      console.log('当前不允许获取');
      return false;
    }
    var that = this;
    if (!that.data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    //调用接口
    app.request({
      url: app.api.user.send_sms_code,
      data: {
        mobile: that.data.phone,
      },
      success: function (result) {
        console.log(result);
        that.setData({
          is_allow_get_verify_code: false,
          verify_code_focus: true
        })
        wx.showToast({
          title: result.msg,
        })
        that.doLoop();
      },
      fail: function (result) {
        console.log('fail');
        console.log(result);
        that.setData({
          is_allow_get_verify_code: true
        })
      },
    });
  },
  doLoop: function () {
    let that = this;
    // 60s倒计时 setInterval功能用于循环，常常用于播放动画，或者时间显示
    var currentTime = that.data.currentTime;
    let interval = setInterval(function () {
      currentTime--; //减
      that.setData({
        get_verify_code_text: currentTime + '秒后获取'
      })
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          get_verify_code_text: '获取验证码',
          currentTime: 60,
          is_allow_get_verify_code: true
        })
      }
    }, 1000);
  },
  phoneChange: function (event) {
    console.log("phoneChange==", event.detail.value)
    this.setData({
      phone: event.detail.value
    })
  },
  show_dialog: function () {
    var that = this;
    that.setData({
      dialog: true
    })
  },
  scan: function (e) {
    console.log('index - scan')
    var that = this;
    wx.scanCode({
      success(res) {
        app.tools.parse_qrcode(that, res.result);
        // that.setData({
        //   code: res.result,
        // });
      },
      fail(res) {
        console.log(res);
      }
    })
  },

  show_verify_qrcode: function (event) {
    console.log('show_verify_qrcode');
    let page = this;
    app.request({
      url: app.api.code.get_verify_qrcode,
      data: {
        coupon_send_note_guid: event.currentTarget.dataset.coupon_send_note_guid
      },
      success: function (result) {
        console.log(result);
        page.setData({
          qrcode_dialog: true,
          verify_qrcode: result.data.image_url
        })
      },
    });
  },
  show_code_password: function (e) {
    this.setData({
      bind_type: 1
    })
  },
  show_phone_verify_code: function (e) {
    this.setData({
      bind_type: 2
    })
  },
  bind_code: function (e) {
    var that = this;
    that.setData({
      dialog: true
    })
    let data = e.detail.value;
    console.log(data);
    let bind_type = that.data.bind_type;
    let url = '';
    if (bind_type == 1) {
      if (!data.code) {
        wx.showModal({
          title: '系统提示',
          content: '请输入卡号!',
          showCancel: false,
        });
        return false;
      }
      if (!data.password) {
        wx.showModal({
          title: '系统提示',
          content: '请输入密码!',
          showCancel: false,
        });
        return false;
      }
      url = app.api.code.bind_code;
    }
    if (bind_type == 2) {
      if (!data.phone) {
        wx.showModal({
          title: '系统提示',
          content: '请输入手机号!',
          showCancel: false,
        });
        return false;
      }
      if (!data.verify_code) {
        wx.showModal({
          title: '系统提示',
          content: '请输入验证码!',
          showCancel: false,
        });
        return false;
      }
      url = app.api.code.bind_code_by_mobile;
    }
    app.request({
      url: url,
      data: data,
      success: function (result) {
        console.log(result);
        wx.showModal({
          title: "提示",
          content: result.msg,
          showCancel: false,
          confirmText: "确认",
          success: function (e) {
            // wx.navigateTo({
            //   url: '/pages/user/user',
            // })
            that.setData({
              dialog: false
            })
            that.loadData();
            return;
            wx.navigateBack({
              delta: 1
            });
          }
        })
        return;

        wx.showToast({
          title: result.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          // image: '/images/tan.png', //自定义图标的本地路径，image 的优先级高于 icon
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {
            setTimeout(function () {
              that.setData({
                dialog: false
              })
            }, 1000) //延迟时间
          },
          fail: function () {
          },
          complete: function () {
          }
        })
      },
    });
  },
  xia: function (t) {
    var a = t.target.dataset.index;
    this.setData({
      check: a
    });
  },
  shou: function () {
    this.setData({
      check: -1
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    app.getConfig(this);
    // console.log('onLoad');
    this.setData({
      status: options.status || 0
    });
    // this.loadData();
    // if (options.show_tab_bar == 1) {
    //   this.setData({
    //     show_tab_bar: 1
    //   })
    //   app.editTabBar();
    // }
  },
  loadData: function () {
    var that = this;
    app.request({
      url: app.api.code.member_code_list,
      data: {
        status: that.data.status
      },
      success: function (result) {
        that.setData(result.data);
        console.log(result.data.list);
      },
    });
  },

  goto_detail(event) {
    let coupon_send_note_guid = event.currentTarget.dataset.coupon_send_note_guid;
    console.log('goto_detail');
    wx.navigateTo({
      url: '/pages/code_note_detail/index?coupon_send_note_guid=' + coupon_send_note_guid,
    })

  },

  goto_share(event) {
    let coupon_send_note_guid = event.currentTarget.dataset.coupon_send_note_guid;
    console.log('goto_share');
    console.log(coupon_send_note_guid);
    wx.navigateTo({
      url: '/pages/code_note_detail/index?coupon_send_note_guid=' + coupon_send_note_guid,
    })

  },
  goto_use(event) {
    let coupon_send_note_guid = event.currentTarget.dataset.coupon_send_note_guid;
    let coupon_type = event.currentTarget.dataset.coupon_type;
    if (coupon_type == 1 || coupon_type == 2) {
      app.request({
        url: app.api.code.verify_coupon_send_note_guid,
        data: {
          coupon_send_note_guid: coupon_send_note_guid
        },
        success: function (result) {
          console.log(result);
          if (result.data.status == 1) {
            //选择商品
            wx.navigateTo({
              url: '/pages/code/detail?bid=' + app.ext_config.bid + '&token=' + result.data.data.token,
            })
          } else if (result.data.status == 2) {
            //订单详情
            wx.navigateTo({
              url: '/pages/order/detail?bid=' + app.ext_config.bid + '&order_guid=' + result.data.data.order_guid
            })
          }
        },
      });
    } else if (coupon_type == 3) {
      //礼品卡询问是否充值
      wx.showModal({
        title: "提示",
        content: "确认将该礼品卡充入会员余额?",
        success: function (res) {
          if (res.confirm) {
            app.request({
              url: app.api.code.code_to_member_money,
              data: {
                coupon_send_note_guid: coupon_send_note_guid
              },
              success: function (res) {
                wx.showToast({
                  title: res.msg,
                  icon: 'success',
                  duration: 500
                });
                setTimeout(function () {
                  wx.navigateTo({
                    url: '/pages/user/user'
                  })
                }, 500) //延迟时间
              }
            });
          }
        }
      });


    }

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // console.log('onShow');
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  }
})