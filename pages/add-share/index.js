// pages/add-share/index.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {
      name: '',
      mobile: '',
    },
    img: "/images/img-share-un.png",
    agree: 0,
    apply_status: -1,
    is_distributor: 0,
    share_setting: {},
  },
  get_share_setting: function () {
    var page = this;
    app.request({
      url: app.api.share.get_share_setting,
      success: function (result) {
        page.setData({
          share_setting: result.data
        });
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var page = this;
    page.get_share_setting();
    app.pageOnShow(page);
    app.async_get_user_info().then((user_info) => {
      page.setData({
        user_info: user_info,
      });
      app.request({
        url: app.api.share.check,
        success: function (res) {
          user_info.is_distributor = res.data.is_distributor;
          if (res.data.is_distributor == 1) {
            wx.redirectTo({
              url: '/pages/share/index',
            })
            return;
          }
          page.setData({
            user_info: user_info,
            apply_status: res.data.apply_status,
            is_distributor: res.data.is_distributor,
          });
        }
      });
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },
  formSubmit: function (e) {
    var page = this;
    app.async_get_user_info().then((user_info) => {
      page.data.form = e.detail.value;
      if (page.data.form.name == undefined || page.data.form.name == '') {
        wx.showToast({
          title: "请填写姓名！",
          image: "/images/icon-warning.png",
        });
        return;
      }
      if (page.data.form.mobile == undefined || page.data.form.mobile == '') {
        wx.showToast({
          title: "请填写联系方式！",
          image: "/images/icon-warning.png",
        });
        return;
      }
      console.log(page.data.agree);
      if (page.data.agree == 0) {
        wx.showToast({
          title: "请先阅读并确认分销申请协议！！",
          image: "/images/icon-warning.png",
        });
        return;
      }
      console.log(page.data.agree);
      app.request({
        url: app.api.share.join,
        data: e.detail.value,
        success: function (result) {
          wx.showModal({
            title: "提示",
            content: result.msg,
            showCancel: false,
            success: function (res) {
              if (res.confirm) {
                wx.redirectTo({
                  url: '/pages/add-share/index',
                })
              }
            }
          });
        }
      });
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  agreement: function () {
    var share_setting = this.data.share_setting;
    wx.showModal({
      title: '分销协议',
      content: share_setting.agree,
      showCancel: false,
      confirmText: "我已阅读",
      confirmColor: "#ff4544",
      success: function (res) {
        if (res.confirm) {
          console.log('用户点击确定')
        }
      }
    });
  },
  agree: function () {
    var page = this;
    var agree = page.data.agree;
    if (agree == 0) {
      agree = 1;
      page.setData({
        img: "/images/img-share-agree.png",
        agree: agree
      });
    } else if (agree == 1) {
      agree = 0;
      page.setData({
        img: "/images/img-share-un.png",
        agree: agree
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  }
})