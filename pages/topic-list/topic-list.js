// pages/topic-list/topic-list.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    show_tab_bar: 0,
    data: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    console.log(options);
    page.loadTopicList();
  },

  loadTopicList: function () {
    var page = this;
    app.request({
      url: app.api.topic.list,
      pagination: 'data',
      page: page,
      data: {},
      success: function (result) {},
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    app.pageOnShow(this);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    var page = this;
    page.setData({
      data: [],
    })
    page.loadTopicList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var page = this;
    page.loadTopicList();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
});