// pages/share-order/share-order.js
var api = require('../../api.js');
var app = getApp();
// var is_no_more = false;
// var is_loading = false;
// var p = 2;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: '',
    all: true,
    // is_no_more: false,
    data: [],
    hidden: -1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var page = this;
    // is_no_more = false;
    // is_loading = false;
    // p = 2;
    if (options.status) {
      page.setData({
        all: false,
        status: options.status
      })
    }
    page.GetList(options.status || '');
  },
  copyText: function (e) {
    var page = this;
    var text = e.currentTarget.dataset.text;
    wx.setClipboardData({
      data: text,
      success: function () {
        wx.showToast({
          title: "已复制"
        });
      }
    });
  },
  GetList: function (status) {
    var page = this;
    page.setData({
      status: status || '',
    });
    app.request({
      url: api.share.get_order,
      pagination: 'data',
      page: page,
      data: {
        status: page.data.status
      },
      success: function (res) {
        // page.setData({
        //   data: res.data.data
        // });
      }
    });

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    var page = this;
    page.setData({
      data: []
    });
    this.GetList(this.data.status);
    return;
  },
  click: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    page.setData({
      hidden: page.data.hidden == index ? -1 : index
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var page = this;
    this.GetList(this.data.status);
    return;
    var page = this;
    if (is_loading || is_no_more)
      return;
    is_loading = true;
    app.request({
      url: api.share.get_order,
      data: {
        status: page.data.status,
        page: p,
      },
      success: function (res) {
        var data = page.data.data.concat(res.data.data);
        page.setData({
          data: data,
        });
        if (res.data.current_page == res.data.last_page) {
          is_no_more = true;
          page.setData({
            is_no_more: true,
          });
        }
        p++;
      },
      complete: function () {
        is_loading = false;
      }
    });

  }
})