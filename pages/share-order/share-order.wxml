<view class="info">
  <view class="info-title flex-row">
    <navigator class="flex-x-center width" url="/pages/share-order/share-order" open-type="redirect">
      <view class="info-text {{status == '' && all ?'active':''}}">全部</view>
    </navigator>
    <navigator class="flex-x-center width" url="/pages/share-order/share-order?status=-1" open-type="redirect">
      <view class="info-text {{status == -1 && !all ?'active':''}}">待付款</view>
    </navigator>
    <navigator class="flex-x-center width" url="/pages/share-order/share-order?status=0" open-type="redirect">
      <view class="info-text {{status == 0 && !all ?'active':''}}">待发货</view>
    </navigator>
    <navigator class="flex-x-center width" url="/pages/share-order/share-order?status=1" open-type="redirect">
      <view class="info-text {{status == 1 && !all ?'active':''}}">待收货</view>
    </navigator>
    <navigator class="flex-x-center width" url="/pages/share-order/share-order?status=2" open-type="redirect">
      <view class="info-text {{status == 2 && !all ?'active':''}}">已完成</view>
    </navigator>

  </view>
  <view class="info-content">
    <block wx:for="{{data}}" wx:for-item="item">
      <view class="info-block">

        <view class="info-label flex-row">
          <view class="info-left" style="width:80%">
            订单号：{{item.bill_number}}
            <text class="copy-text-btn" bindtap="copyText" data-text="{{item.bill_number}}">复制</text>

          </view>
          <view class="info-right" style="width:20%">
            <view class="info-status">{{item.total_money}} 元</view>
          </view>
        </view>


        <view class="info-label flex-row" bindtap="click" data-index="{{index}}">
          <view class="info-left flex-row">
            <view class="info-img flex-y-center">
              <image class="img" src="{{item.member_head_img}}"></image>
            </view>
            <view class="info-name flex-col">{{item.member_name}}</view>
            <view class="info-level">{{item.share_status}}</view>
          </view>
          <view class="info-right">
            <view class="info-money flex-row">
              <block wx:if="{{item.share_money>0}}">
                <text>{{item.is_price!=1?"预计":"已得"}}佣金：<text class="red" style="padding: 0 5rpx 0 0;">{{item.share_money?item.share_money:0.00}}</text>元</text>
              </block>
              <block wx:if="{{!item.share_money}}">
                <text>订单详情</text>
              </block>
              <view class="down flex-y-center {{index == hidden ? 'active' : ''}}">
                <image src="/images/img-share-down.png" style="width:26rpx;height:16rpx;margin-left: 24rpx;" wx:if="{{index == hidden}}"></image>
                <image src="/images/img-share-right.png" style="width:16rpx;height:26rpx;margin-left: 24rpx;" wx:else></image>
              </view>
            </view>
          </view>
        </view>
        <view class="{{index == hidden ? 'show' : 'hide'}}">
          <block wx:for="{{item.order_detail.goods_list}}" wx:for-item="v">
            <view class="info-label height-164 flex-row">
              <view class="info-left flex-row width-90">
                <view class="info-img flex-y-center">
                  <image class="img img-size-120" src="{{v.pic}}"></image>
                </view>
                <view class="info-count">
                  <view class="info-detail">{{v.name}}</view>
                  <view class="info-detail">{{v.goods_price}}元 × {{v.num}}</view>

                </view>
              </view>
            </view>
          </block>
        </view>
      </view>
    </block>
  </view>
  <include src="/common/loadmore/loadmore"></include>

  <!-- <view class="info-footer flex-row"> -->
  <!-- <view class="info-before">
      <view class="info-border"></view>
    </view> -->
  <!-- <view class="info-t" wx:if="{{is_no_more==true}}"> 没有更多了 </view>
    <view class="info-t" wx:if="{{is_no_more==false}}"> 上滑加载 </view> -->

  <!-- <view class="info-after">
      <view class="info-border"></view>
    </view> -->

  <!-- </view> -->
</view>