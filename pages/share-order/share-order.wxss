/* pages/share-order/share-order.wxss */

.info {
  width: 100%;
  border-top: 1rpx #e3e3e3 solid;
  /* padding-top: 20rpx; */
}

.width {
  width: 25%;
  text-align: center;
}

.info-title .active {
  color: #ff4544;
  border-bottom: 4rpx #ff4544 solid;
}

.info-title .info-text {
  height: 100%;
  line-height: 100rpx;
}

.info .info-title {
  height: 100rpx;
  padding: 0 24rpx;
  border-bottom: 1rpx #e3e3e3 solid;
  /* font-weight: bold; *//* font-size: 13pt; */
  background-color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
}

.info .info-content {
  width: 100%;
  padding-top:  100rpx;
}

.info .info-content .info-block {
  width: 100%;
  background-color: #fff;
  margin-bottom: 26rpx;
  padding: 0 24rpx;
}

.info-block .info-label {
  width: 100%;
  height: 90rpx;
  border-bottom: 2rpx #e3e3e3 solid;
  font-size: 9pt;
  line-height: 90rpx;
}

.info-block .info-label .info-left {
  float: right;
  width: 78%;
}

.info-block .info-label .info-right {
  float: right;
  width: 60%;
  text-align: right;
}

.info-block .info-label .info-status {
  color: #ff4544;
  /* font-weight: bold; */
}

.info-left .info-img {
  /* margin-right: 40rpx; */
  width: 30%;
}

.info-left .info-img .img {
  width: 64rpx;
  height: 64rpx;
}

.info-left .info-name {
  margin-right: 36rpx;
  width: 27%;
  overflow: hidden;
  text-overflow: clip;
}

.info-block .height-164 {
  height: 164rpx;
  /* line-height: 164rpx; */
}

.info .info-block .info-label .width-90 {
  width: 90%;
}

.info-left .info-img .img-size-120 {
  width: 104rpx;
  height: 104rpx;
}

.info-label .info-left .info-count {
  /* margin-left: 32rpx; */
  /* font-size: 15pt; */
  width: 70%;
}

.info-count .info-detail {
  width: 100%;
  height: 80rpx;
  overflow: hidden;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.info-right .info-money {
  text-align: right;
  float: right;
}

/* .info-right .down::after{
  content: " ";
  background-image: url('/images/img-share-right.png');
  width: 16rpx;
  height: 26rpx;
  display: block;
  float: right;
  background-size: 100% 100%;
  margin-left: 24rpx;
}
.info-right .down.active::after{
  background-image: url('/images/img-share-down.png');
  width: 26rpx;
  height: 19rpx;
} */

.red {
  color: #ff4544;
  /* font-weight: bold; */
  font-size: 9pt;
}

.info-block .info-label:last-child {
  border: none;
}

.info .info-footer {
  color: #bbb;
  text-align: center;
  width: 100%;
  padding: 0 100rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.info .info-footer .info-t {
  height: 60rpx;
  margin: 0 20rpx;
}

.info .info-footer .info-before {
  width: 168rpx;
  height: 60rpx;
}

.info .info-footer .info-after {
  width: 168rpx;
  height: 60rpx;
}

.info .info-footer .info-border {
  border-bottom: 1rpx #e3e3e3 solid;
  padding-bottom: 30rpx;
}

.hide {
  display: none;
}

.show {
  display: block;
}
