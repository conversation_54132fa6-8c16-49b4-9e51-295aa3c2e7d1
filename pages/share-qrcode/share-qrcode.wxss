/* pages/share-qrcode/share-qrcode.wxss */
/* .info{
  width: 100%;
  height: 100%;
  background-image: url('/images/img-share-qrcodeBg.png');
  background-size:100% 100%; 
  padding-top: 290rpx;
}
.info .info-title{
  width: 100%;
  font-size: 18pt;
}
.info .info-title .info-left{
  margin: 0 34rpx 0 30rpx;
}
.info-title .info-left .info-img{
  width: 180rpx;
  height: 180rpx;
  background-color: #fff;
  border-radius: 10rpx;
  text-align: center;
}
.info-title .info-left .info-img .img{
  width: 168rpx;
  height: 168rpx;
  margin-top: 6rpx;
}
.info-title .info-right{
  margin-top: 38rpx;
  color: #fff;
  font-weight: bold;
}
.purple{
  color: #6400e0;
}
.info .info-content{
  margin-top: 56rpx;
  width: 100%;
}
.info-content .info-label{
  width: 100%;
  color:#fff;
  font-weight: bold;
  font-size: 15pt;
}
.info-content .info-qrcode{
  width: 420rpx;
  height: 420rpx;
  padding: 24rpx;
  background-color: #fff;
  border: 1rpx #ff4544 solid;
}
.info-content .info-qrcode .qrcode{
  width: 374rpx;
  height: 374rpx;
} */

.i-qrcode{
  width: 100%;
  height: 100%;
}
.i-qrcode .img{
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
}