<import src="/common/tabbar/tabbar.wxml" />
<template is="tabbar" data="{{tabbar}}" />
<view class="quick_purchase">

  <view class="search-block">
    <navigator hover-class="none" url="/pages/search/search">
      <image src="/images/icon-search.png" />
      <text>搜索</text>
    </navigator>
  </view>

  <view class="left_nav">
    <scroll-view scrollY="true" style="height: 100%;width:100%" wx:if="{{quick_list}}">
      <view bindtap="selectMenu" class="cat_name {{cat_checked_guid==item.guid?'cat_name_active':''}}" data-tag="hot_cakes" wx:if="{{quick_hot_goods_lists.length>0}}">
        <image class="quick-hot" src="/images/quick-hot.png"></image>
        <view style="float:left; height:30px;display:flex;align-items:center;justify-content:center;">热销</view>
      </view>
      <view bindtap="selectMenu" class="cat_name {{cat_checked_guid==item.guid?'cat_name_active':''}}" data-index="{{index}}" data-tag="{{'view-'+index}}" data-guid="{{item.guid}}" data-catguid="{{item.guid}}" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" wx:for="{{quick_list}}">{{item.name}}</view>
    </scroll-view>
  </view>
  <view class="r_goods" style="padding-bottom: 115rpx;">
    <scroll-view scrollIntoView="{{toView}}" scrollY="true" style="height: 100%;padding: 10rpx">
      <block wx:if="{{quick_hot_goods_lists.length>0}}">
        <view class="navname" id="hot_cakes">热销</view>
        <view class="goodsall" wx:for="{{quick_hot_goods_lists}}" wx:for-item="goods" wx:key="id">
          <view bindtap="get_goods_info" class="goods" data-guid="{{goods.guid}}">
            <view class="goods_pic">
              <image id="goods_pic" lazyLoad="true" src="{{goods.pic}}"></image>
            </view>
            <view class="goods_info">
              <view class="goods_name" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{goods.name}}</view>
              <view style="font-size:24rpx;color:#666666;margin-top:14rpx;">销量 {{goods.virtual_sales}}</view>
              <view wx:if='{{goods.price > 0}}' class='flex-grow-0' style='color:#ff334b;margin-right: 32rpx'>￥{{goods.price}}
                <text wx:if="{{goods.original_price>0}}" style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.original_price}}</text>
              </view>
            </view>
          </view>
          <view class="purchase" wx:if="{{goods.use_attr==0}}">
            <view bindtap="jia" class="standard" data-cid="{{goods_list.guid}}" data-guid="{{goods.guid}}" data-index="{{index}}" id="jia">
              <image class="standard" src="/images/jia.png" style="width:100%;"></image>
            </view>
            <block wx:if="{{goods.num>0}}">
              <view class="standard" id="num">{{goods.num}}</view>
              <view bindtap="jian" class="standard" data-cid="{{goods_list.guid}}" data-guid="{{goods.guid}}" data-index="{{index}}" id="jian">
                <image class="standard" src="/images/jian.png"></image>
              </view>
            </block>
          </view>
          <view bindtap="showDialogBtn" class="guigepurchase" data-guid="{{goods.guid}}" data-index="{{index}}" data-num="{{goods.num}}" data-price="{{goods.price}}" wx:if="{{goods.use_attr==1}}">选规格
            <view class="guigenum" wx:if="{{goods.num>0}}">{{goods.num}}</view>
          </view>
        </view>
      </block>
      <block wx:for="{{quick_list}}" wx:for-item="goods_list" wx:key="id">
        <block wx:if="{{selectedMenuId==index}}">
          <view class="navname" id="{{'view-'+index}}">
            <!-- {{goods_list.name}} -->
          </view>
          <view class="cat-list" wx:if="{{goods_list.sub_category.length>0}}">
            <scroll-view scroll-x="true">
              <view class="list-content">
                <view class="cat-item {{'' == sub_category_guid ?'active':''}}" bindtap="allSubCatClick">
                  <text class="flex-y-center">全部</text>
                </view>
                <view class="cat-item {{item.guid == sub_category_guid ?'active':''}}" wx:for="{{goods_list.sub_category}}" bindtap="subCatClick" data-index="{{index}}" data-sub_category_guid="{{item.guid}}">
                  <text class="flex-y-center">{{item.name}}</text>
                </view>
              </view>
            </scroll-view>
          </view>
          <view class="goodsall" wx:for="{{goods_list.goods}}" wx:for-item="goods" wx:key="id" wx:if="{{sub_category_guid=='' || sub_category_guid ==goods.category_guid}}">
            <view bindtap="get_goods_info" class="goods" data-guid="{{goods.guid}}">
              <view class="goods_pic">
                <image id="goods_pic" src="{{goods.pic}}"></image>
              </view>
              <view class="goods_info">
                <view class="goods_name" style="overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">{{goods.name}}</view>
                <view wx:if="{{goods.sales>0}}" style="font-size:24rpx;color:#666666;margin-top:14rpx;">已售{{goods.sales}}件</view>
                <view wx:if='{{goods.price > 0}}' class='flex-grow-0' style='color:#ff334b;margin-right: 32rpx'>￥{{goods.price}}
                  <text wx:if="{{goods.original_price>0}}" style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.original_price}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
    </scroll-view>
  </view>
</view>