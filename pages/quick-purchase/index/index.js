var app = getApp();

Page({
  data: {
    quick_list: [],
    goods_list: [],
    carGoods: [],
    showModal: !1,
    checked: !1,
    color: "",
    cat_checked_guid: '',
    selectedMenuId: 0,
    sub_category_guid: '',
  },
  onLoad: function (options) {
    app.editTabBar();
    app.pageOnLoad(this, options);
  },
  onShow: function () {
    app.pageOnShow(this), this.loadData();
  },
  loadData: function (t) {
    var that = this;
    app.request({
      url: app.api.quick.quick,
      success: function (result) {
        that.setData({
          quick_hot_goods_lists: {},
          quick_list: result.data.list,
        });
      }
    });
  },

  allSubCatClick: function (e) {
    this.setData({
      sub_category_guid: '',
    });
  },
  subCatClick: function (e) {
    var page = this;
    var sub_category_guid = e.currentTarget.dataset.sub_category_guid;
    console.log(sub_category_guid);
    page.setData({
      sub_category_guid: sub_category_guid,
    });
  },
  get_goods_info: function (e) {
    var guid = e.currentTarget.dataset.guid;
    wx.navigateTo({
      url: "/pages/index/goods_detail?guid=" + guid + "&quick=1"
    });
  },
  selectMenu: function (t) {
    var a = t.currentTarget.dataset;
    this.setData({
      toView: a.tag,
      selectedMenuId: a.index,
      cat_checked_guid: a.guid,
      sub_category_guid: ''
    });

  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
});