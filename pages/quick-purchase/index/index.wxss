::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.quick_purchase {
  height: 100%;
  width: 100%;
  background: #fafafa;
}

.left_nav {
  height: 100%;
  width: 20%;
  float: left;
  background: #f3f3f3;
  display: inline-block;
  padding-bottom: 115rpx;
}

.cat_name {
  height: 100rpx;
  width: 100%;
  font-size: 14px;
  color: #353535;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cat_name_active {
  background: #fafafa;
  color: #ff4544;
}

.quick-hot {
  width: 15px;
  height: 15px;
  background-repeat: no-repeat;
  background-size: 90%;
  float: left;
  margin-right: 6px;
}

.r_goods {
  height: 100%;
  width: 78%;
  float: right;
}

.navname {
  font-size: 11px;
  color: #666;
  width: 100%;
  /* padding-top: 14px;
  padding-bottom: 14px; */
}

.goodsall {
  height: 174rpx;
  width: 100%;
  border-bottom: 1rpx solid #e6e6e6;
  position: relative;
}

.goods {
  height: 152rpx;
  margin-top: 22rpx;
  width: 100%;
}

.goods_pic {
  width: 27%;
  height: 100%;
  float: left;
}

#goods_pic {
  width: 100%;
  height: 100%;
}

.goods_info {
  width: 70%;
  height: 100%;
  margin-right: 1%;
  float: right;
}

.goods_name {
  font-size: 28rpx;
  color: #353535;
  margin-top: 10rpx;
  font-weight: bold;
}

.guigenum {
  position: absolute;
  bottom: 60%;
  left: 77%;
  width: 35rpx;
  height: 25rpx;
  border-radius: 40%;
  border: 1rpx solid red;
  color: red;
  background: #fff;
  font-size: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.purchase {
  position: absolute;
  right: 1%;
  bottom: 10rpx;
  z-index: 100;
  height: 50rpx;
  width: 150rpx;
}

.standard {
  border-radius: 100%;
  width: 50rpx;
  height: 50rpx;
  float: right;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guigepurchase {
  position: absolute;
  right: 1%;
  bottom: 10rpx;
  z-index: 100;
  height: 50rpx;
  width: 22%;
  color: #ffe5e5;
  background: #ff4544;
  font-size: 26rpx;
  border-radius: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.guige {
  position: relative;
  width: 136rpx;
  height: 45rpx;
  bottom: 30%;
  left: 68%;
}

.show-btn {
  margin-top: 100rpx;
  color: #2c2;
}

.modal-mask {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: #000;
  opacity: 0.5;
  overflow: hidden;
  z-index: 2222;
  color: #fff;
}

.guigeshu {
  border-radius: 100%;
  width: 55rpx;
  height: 55rpx;
  background-repeat: no-repeat;
  background-size: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-confirm {
  width: 50%;
  color: #ec5300;
  text-align: center;
}

.navbar+.quick_purchase {
  padding-bottom: 115rpx;
}

.navbar+.quick_purchase #goods_car {
  bottom: 115rpx;
}

.navbar.device_iphone_x {
  padding-bottom: 65rpx;
}

.navbar.device_iphone_x~.quick_purchase {
  padding-bottom: 180rpx;
}

.navbar.device_iphone_x~.quick_purchase #goods_car {
  bottom: 180rpx;
}

.group-btn {
  height: 50rpx;
  width: 130rpx;
  color: #fff;
  font-size: 10pt;
  border-radius: 10rpx;
  background-color: #ffa360;
  background: -webkit-gradient(linear, left top, right bottom, color-stop(0%, #ffa360), color-stop(140%, #ff5c5c));
  background-image: linear-gradient(140deg, #ffa360, #ff5c5c);
  float: right;
  position: relative;
  top: -60rpx;
}


.search-block {
  padding: 17rpx 24rpx;
  display: block;
}

.search-block navigator {
  display: block;
  background: #fff;
  text-align: center;
  height: 68rpx;
  line-height: 62rpx;
  border: 1rpx solid #e3e3e3;
  border-radius: 10rpx;
  color: #b2b2b2;
}

.search-block navigator image {
  height: 24rpx;
  width: 24rpx;
}


/* list.wxss */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  overflow: hidden;
  background: #fff;
  z-index: 10;
  border-top: 1rpx solid #E3E3E3;
}

.cat-list {
  white-space: nowrap;
  border-bottom: 1rpx solid #e3e3e3;
  background: #fff;
}

.cat-list .list-content {
  white-space: nowrap;
  height: 60rpx;
}

.cat-list .cat-item {
  white-space: nowrap;
  display: inline-block !important;
  padding: 0 20rpx;
  height: 100%;
}

.cat-list .cat-item text {
  white-space: nowrap;
  border-bottom: 5rpx solid transparent;
  height: 100%;
  font-size: 14px;
  transform: translateY(1rpx);
}

.cat-list .cat-item.active text {
  color: #ff4544;
  border-bottom-color: #ff4544;
}

.sub-cat-list {
  white-space: nowrap;
  border-bottom: 1rpx solid #e3e3e3;
  background: #fff;
}

.sub-cat-list scroll-view {
  height: 100rpx;
}

.sub-cat-list .list-content {
  white-space: nowrap;
  height: 100rpx;
  padding: 0 20rpx;
}

.sub-cat-list .cat-item {
  display: inline-block;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ff4544;
  margin: 20rpx;
  color: #ff4544;
  border-radius: 5rpx;
}

.sub-cat-list .cat-item text {
  height: 100%;
}

.sub-cat-list .cat-item.active {
  background: #ff4544;
  color: #fff;
}


.top-bar~.goods-list {
  padding-top: 106rpx;
}

.top-bar.height-bar~.goods-list {
  padding-top: 206rpx;
}