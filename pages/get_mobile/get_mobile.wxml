<import src="/common/tabbar/tabbar.wxml" />
<template is="tabbar" data="{{tabbar}}" />
<swipers banner_list="{{banner}}"> </swipers>

<view class="login-container" wx:if="{{mobile_auth_type==1}}">
  <view class="login">
    <view class="app-info">
      <!-- <image class="app-logo" src="小程序LOGO" /> -->
      <view class="head_view">
        <open-data class="head_img" type="userAvatarUrl" default-avatar=""></open-data>
      </view>
      <!-- <text class="app-name">小程序名称</text> -->
      <open-data class="app-name" default-text="匿名" type="userNickName"></open-data>
    </view>
    <view class="alert">
      <view class="alert-title">网页由该小程序开发，请确认授权以下信息</view>
      <view class="alert-desc">
        <view class="alert-text">获得您的公开信息（手机号）</view>
      </view>
    </view>
    <button class="weui-btn" type="primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权登录</button>
    <button class="weui-btn" type="default" bindtap="login_out">暂不授权</button>
  </view>
  <view class="login" wx:if="{{ is_auth===true }}">
    <image class="login-icon" src="../../images/iconfont-weixin.png" />
    <view class="login-text">近期您已经授权登陆过</view>
    <view class="login-text">自动登录中</view>
  </view>
</view>


<block wx:if="{{mobile_auth_type==2}}">

  <!--pages/index/index.wxml-->
  <!--轮播图-->

  <view wx:if="{{config.default_login_page_background_image_url}}" style="width: 100%;">
    <image style="width: 100%;padding: 0;margin: 0;vertical-align:top;" src="{{config.default_login_page_background_image_url}}" mode="widthFix"></image>
  </view>

  <view class="page">
    <view class="page after-navber">



      <form bindsubmit="login">

        <view class="weui-cells__group weui-cells__group_form">

          <view class="weui-cells weui-cells_form">

            <view class="weui-cell weui-cell_active">
              <view class="weui-cell__hd"><label class="weui-label">手机号</label></view>
              <view class="weui-cell__bd">
                <input class="weui-input" name="phone" type="number" bindinput="phoneChange" placeholder="请输入手机号" value="" placeholder-class="weui-input__placeholder" />
              </view>
            </view>

            <view class="weui-cell weui-cell_active weui-cell_vcode weui-cell_wrap">
              <view class="weui-cell__hd"><label class="weui-label">验证码</label></view>
              <view class="weui-cell__bd">
                <input bindinput="bindVcodeInput" name="verify_code" focus="{{verify_code_focus}}" class="weui-cell__control weui-cell__control_flex weui-input" type="number" placeholder="输入验证码" placeholder-class="weui-input__placeholder" />
                <view aria-role="button" class="weui-cell__control weui-btn weui-btn_default weui-vcode-btn vcode-btn" bindtap="get_verify_code">{{get_verify_code_text}}</view>
              </view>
            </view>

          </view>

        </view>

        <view class="weui-btn-area" style="margin: 20px 0 0 0;">
          <button class='weui-btn' style="width: 90%;" type="primary" formType="submit">确认登录</button>
        </view>
      </form>
      <view class="notice" wx:if="{{config.login_page_notice}}">
        <rich-text nodes="{{config.login_page_notice}}"></rich-text>
      </view>
      <view style="padding-bottom: 80px;padding-top: 200rpx;">
        <copy_right></copy_right>
      </view>
    </view>

  </view>

</block>