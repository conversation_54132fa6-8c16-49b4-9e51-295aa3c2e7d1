// pages/login/login.js
var app = getApp(); //获取应用实例
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: null,
    mobile_auth_type: 0,
    config: {},
    banner: {},
    show_tab_bar: 0,
    phone: '',
    verify_code: '',
    get_verify_code_text: '发送', //倒计时 
    currentTime: 60, //限制60s
    is_allow_get_verify_code: true, //获取验证码按钮，默认允许点击
    verify_code_focus: false,
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);

    let that = this;
    console.log(options)
    var pages = getCurrentPages(); //页面指针数组 
    var prepage = pages[pages.length - 2]; //上一页面指针 
    let redirectToUrl;
    if (options.url) {
      redirectToUrl = options.url;
    } else if (prepage) {
      redirectToUrl = prepage.route;
    } else {
      redirectToUrl = 'pages/index/index';
    }
    that.setData({
      url: redirectToUrl
    });
    that.get_mobie_auth_type();
  },
  get_mobie_auth_type: function () {
    let that = this;
    app.request({
      url: app.api.passport.get_config,
      data: {},
      success: function (result) {
        console.log(result);
        that.setData({
          banner: result.data.banner_list,
          config: result.data.config,
          mobile_auth_type: result.data.config.mobile_auth_type,
        })
      },
    });

  },
  getPhoneNumber(e) {
    let that = this;
    return app.auth.getPhoneNumber(e, function (result) {
      wx.setStorageSync("user_info", result.data.user_info);
      wx.navigateBack({
        delta: 1
      })
      return;
      wx.redirectTo({
        url: '/' + decodeURIComponent(that.data.url)
      });
    });
  },
  get_verify_code: function () {
    if (this.data.is_allow_get_verify_code == false) {
      console.log('当前不允许获取');
      return false;
    }
    var that = this;
    if (!that.data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    //调用接口
    app.request({
      url: app.api.user.send_sms_code,
      data: {
        mobile: that.data.phone,
      },
      success: function (result) {
        console.log(result);
        that.setData({
          is_allow_get_verify_code: false,
          verify_code_focus: true
        })
        wx.showToast({
          title: result.msg,
        })
        that.doLoop();
      },
      fail: function (result) {
        console.log('fail');
        console.log(result);
        that.setData({
          is_allow_get_verify_code: true
        })
      },
    });
  },
  doLoop: function () {
    let that = this;
    // 60s倒计时 setInterval功能用于循环，常常用于播放动画，或者时间显示
    var currentTime = that.data.currentTime;
    let interval = setInterval(function () {
      currentTime--; //减
      that.setData({
        get_verify_code_text: currentTime + '秒'
      })
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          get_verify_code_text: '发送',
          currentTime: 60,
          is_allow_get_verify_code: true
        })
      }
    }, 1000);
  },
  phoneChange: function (event) {
    console.log("phoneChange==", event.detail.value)
    this.setData({
      phone: event.detail.value
    })
  },
  login: function (e) {
    var that = this;
    let data = e.detail.value;
    console.log(data);

    if (!data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    if (!data.verify_code) {
      wx.showModal({
        title: '系统提示',
        content: '请输入验证码!',
        showCancel: false,
      });
      return false;
    }
    data.mobile = data.phone;
    app.request({
      url: app.api.user.update_mobile,
      data: data,
      success: function (result) {
        console.log(result);

        wx.showToast({
          title: result.msg,
          icon: 'success',
          duration: 1000
        });
        setTimeout(function () {
          wx.navigateBack({
            delta: 1
          })
        }, 1000) //延迟时间
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  login_out() {
    //不授权则直接跳回登录首页
    wx.reLaunch({
      url: '/pages/index/index'
    })
  }
})