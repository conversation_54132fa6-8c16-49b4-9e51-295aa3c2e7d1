// pages/pay/submit.js
var app = getApp(); //获取应用实例

Page({

  /**
   * 页面的初始数据
   */
  data: {
    options: {},
    total_money: 0,
    third_pay_bill_number: '',
    pay_options: {},

    pay_success: false,
    order: {},
    redirect_url: '',
    redirect_path: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    app.pageOnLoad(this, options);

    console.log(options);
    var that = this;
    that.setData({
      options: options
    });

    let data = options;
    data.driver = 'wechat';
    data.scene = 'miniapp';
    app.request({
      url: app.api.pay.apply,
      data: data,
      success: function (result) {
        console.log(result);
        that.setData({
          pay_options: result.data.pay_options,
          third_pay_bill_number: result.data.third_pay_bill_number,
          total_money: result.data.total_money,
        });
        that.pay();
      },
    });

  },

  pay: function () {
    let that = this;
    var pay_options = that.data.pay_options;
    console.log(pay_options);
    wx.requestPayment({
      timeStamp: pay_options.timeStamp,
      nonceStr: pay_options.nonceStr,
      package: pay_options.package,
      signType: pay_options.signType,
      paySign: pay_options.paySign,
      success: function (e) {},
      fail: function (e) {},
      complete: function (e) {
        console.log(e.errMsg);
        if (e.errMsg == "requestPayment:fail" || e.errMsg == "requestPayment:fail cancel") {
          //支付失败转到待支付订单列表
          wx.showModal({
            title: "提示",
            content: "订单尚未支付",
            showCancel: false,
            confirmText: "确认",
            success: function (res) {
              if (res.confirm) {
                wx.navigateBack({
                  delta: 1
                });
              }
            }
          });
        } else if (e.errMsg == "requestPayment:ok") {
          that.setData({
            pay_success: true
          });

          //当前页做查单,减少跳转
          that.query(that.data.third_pay_bill_number);
          return;

          wx.showLoading({
            title: '支付成功,跳转中',
          })
          wx.navigateTo({
            url: "/pages/pay/pay_success?bill_no=" + that.data.third_pay_bill_number + '&appid=' + pay_options.appId + '&bid=' + app.ext_config.bid
          })
        } else {
          //支付失败转到待支付订单列表
          wx.showModal({
            title: "提示",
            content: "支付失败:" + e.errMsg,
            showCancel: false,
            confirmText: "确认",
            success: function (res) {
              if (res.confirm) {
                wx.navigateBack({
                  delta: 1
                });
              }
            }
          });
        }
      },
    });
  },

  query(bill_no) {
    let that = this;
    app.pay.query(bill_no, function (result) {
      that.setData({
        order: result.data.order_data,
        redirect_url: result.data.redirect_url,
        redirect_path: result.data.redirect_path,
        button_text: result.data.button_text
      });
    })
  },


  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})