<view class="page">
  <view class="weui-msg">
    <view class="weui-msg__icon-area">
      <icon type="warn" size="64" wx:if="{{order.status==-1}}"></icon>
      <icon type="success" size="64" wx:elif="{{order.status==1}}"></icon>
      <icon type="waiting" size="64" wx:elif="{{!order||order.status==0}}"></icon>
      <icon type="warn" size="64" color="#FFBE00" wx:else></icon>
    </view>
    <view class="weui-msg__text-area">
      <!-- <view class="weui-msg__title">1.00元</view> -->
      <view class="weui-msg__title success">
        <text wx:if="{{order.status==-1}}">支付失败</text>
        <text wx:elif="{{order.status==0}}">支付中</text>
        <text wx:elif="{{order.status==1}}">支付成功</text>
        <text wx:else>查询中</text>
      </view>
      <!-- <view class="weui-msg__desc">内容详情，可根据实际需要安排，如果换行则不超过规定长度，居中展现
        <navigator url="" class="weui-msg__link">文字链接</navigator>
      </view> -->
      <view wx:if="{{order.status==1}}">
        <view class="weui-form-preview">
          <view class="weui-form-preview__hd">
            <view class="weui-form-preview__label">金额</view>
            <view class="weui-form-preview__value_in-hd">¥{{order.total_fee/100}}</view>
          </view>
          <view class="weui-form-preview__bd">
            <view class="weui-form-preview__item">
              <view class="weui-form-preview__label">商品</view>
              <view class="weui-form-preview__value">{{order.body}}</view>
            </view>
            <view class="weui-form-preview__item">
              <view class="weui-form-preview__label">商户单号</view>
              <view class="weui-form-preview__value">{{order.bill_number}}</view>
            </view>
            <view class="weui-form-preview__item">
              <view class="weui-form-preview__label">交易单号</view>
              <view class="weui-form-preview__value">{{order.third_trade_no}}</view>
            </view>

            <view class="weui-form-preview__item">
              <view class="weui-form-preview__label">支付时间</view>
              <view class="weui-form-preview__value">{{order.trade_time}}</view>
            </view>
            <view class="weui-form-preview__item">
              <view class="weui-form-preview__label">备注</view>
              <view class="weui-form-preview__value">{{order.memo}}</view>
            </view>
          </view>
        </view>
        <!-- <view class="weui-form-preview__ft">
          <navigator class="weui-form-preview__btn weui-form-preview__btn_default" hover-class="weui-form-preview__btn_active">辅助操作</navigator>
          <navigator class="weui-form-preview__btn weui-form-preview__btn_primary" hover-class="weui-form-preview__btn_active">操作</navigator>
        </view> -->

      </view>
    </view>

    <view class="weui-msg__opr-area">
      <view class="weui-btn-area">
        <navigator wx:if="{{redirect_path}}" url="{{redirect_path}}" hover-class="weui-form-preview__btn_active" style="padding-bottom: 20rpx;">
          <button aria-role="button" class="weui-btn weui-btn_primary">{{button_text}}</button>
        </navigator>

        <navigator url="/pages/index/index" hover-class="weui-form-preview__btn_active">
          <button aria-role="button" class="weui-btn weui-btn_default">返回首页</button>
        </navigator>

      </view>
    </view>

    <!-- <view class="weui-msg__tips-area">
      <view class="weui-msg__tips">内容详情，可根据实际需要安排，如果换行则不超过规定长度，居中展现
        <navigator url="" class="weui-msg__link">文字链接</navigator>
      </view>
    </view> -->
  </view>
  <copy_right></copy_right>
</view>