// pages/login/login.js
var app = getApp(); //获取应用实例
Page({

  /**
   * 页面的初始数据
   */
  data: {
    code: null,
    is_auth: false,
    url: null
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this,options);

    let that = this;
    console.log(options)
    var pages = getCurrentPages(); //页面指针数组 
    var prepage = pages[pages.length - 2]; //上一页面指针 
    let redirectToUrl;
    if (options.url) {
      redirectToUrl = options.url;
    } else if (prepage) {
      redirectToUrl = prepage.route;
    } else {
      redirectToUrl = 'pages/index/index';
    }
    that.setData({
      url: redirectToUrl
    });
  },
  getUserProfile: function () {
    let that = this;
    app.auth.getUserProfile(function (result) {
      that.updateUserInfo(result);
    });
  },
  updateUserInfo: function (res) {
    let that = this;
    console.log(res)
    app.request({
      url: app.api.passport.get_user_info,
      data: {
        user_info: res.rawData,
        encrypted_data: encodeURIComponent(res.encryptedData),
        iv: res.iv,
        signature: res.signature,
        code: res.code,
        appid: wx.getAccountInfoSync().miniProgram.appId
      },
      success: function (res) {
        wx.setStorageSync("user_info", res.data);
        wx.redirectTo({
          url: '/' + decodeURIComponent(that.data.url)
        });
        // wx.navigateBack({
        //   delta: 1,
        // })
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  login_out() {
    //不授权则直接跳回登录首页
    wx.reLaunch({
      url: '/pages/index/index'
    })
  }
})