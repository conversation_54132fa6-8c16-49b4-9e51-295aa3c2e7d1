<view class="login-container">
  <view class="login" wx:if="{{ is_auth===false }}">
    <view class="app-info">
      <!-- <image class="app-logo" src="小程序LOGO" /> -->
      <view class="head_view">
        <open-data class="head_img" type="userAvatarUrl" default-avatar=""></open-data>
      </view>
      <!-- <text class="app-name">小程序名称</text> -->
      <open-data class="app-name" default-text="匿名" type="userNickName"></open-data>
    </view>
    <view class="alert">
      <view class="alert-title">网页由该小程序开发，请确认授权以下信息</view>
      <view class="alert-desc">
        <view class="alert-text">获得您的公开信息（昵称、头像等）</view>
      </view>
    </view>
    <button class="weui-btn" type="primary" open-type="getUserProfile" bindtap="getUserProfile">授权登录</button>
    <button class="weui-btn" type="default" bindtap="login_out">暂不授权</button>
  </view>
  <view class="login" wx:if="{{ is_auth===true }}">
    <image class="login-icon" src="../../images/iconfont-weixin.png" />
    <view class="login-text">近期您已经授权登陆过</view>
    <view class="login-text">自动登录中</view>
  </view>
</view>