/* pages/share/index.wxss */

.info {
  width: 100%;
  background-color: #ff4544;
  padding: 20rpx 24rpx 0 24rpx;
  color: #fff;
}

.info .info-title {
  border-bottom: 1rpx #ffa2a2 solid;
  padding-bottom: 20rpx;
}

.info .info-title .info-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 120rpx;
}

.info .info-block {
  padding: 18rpx 40rpx;
}

.info .info-block .info-up {
  font-size: 13pt;
  margin-bottom: 30rpx;
}

/* .info-blod {
  font-weight: bold;
} */

.info-big {
  font-size: 13pt;
}

.info .info-content {
  width: 100%;
  padding-top: 22rpx;
}

.info .info-content .info-left {
  width: 50%;
}

.info .info-content .info-right {
  width: 50%;
  margin-top: 24rpx;
}

.info .info-content .info-left .info-label {
  padding-bottom: 40rpx;
}

.info-label .info-first {
  margin-bottom: 24rpx;
}

.info-right .info-btn {
  width: 120rpx;
  height: 70rpx;
  border: 1rpx #e3e3e3 solid;
  border-radius: 10rpx;
  float: right;
  text-align: center;
  line-height: 70rpx;
  font-size: 13pt;
}
.list{
  background-color: #ffffff;
  width: 100%;
}
.list .border-bottom{
  border-bottom: 1rpx #e3e3e3 solid;
}
.list .border-between{
  border-left: 1rpx #e3e3e3 solid;
  border-right: 1rpx #e3e3e3 solid;
}
.list .item{
  width: 33.3333%;
  height: 220rpx;
}
.list .item .list-img{
  width: 100%;
  margin-top: 40rpx;
  text-align: center;
}
.list .item .list-img .img{
  width: 50rpx;
  height: 50rpx;
}
.list .item .list-content{
  color: #666666;
  margin-top: 13rpx;
}
.list-red{
  color:#ff4544;
}