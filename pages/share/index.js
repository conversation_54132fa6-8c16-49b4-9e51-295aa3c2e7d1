// pages/share/index.js
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    total_brokerage_money: 0, //累计分佣金额
    total_brokerage_order_money: 0, //累计分佣的关联订单金额
    total_share_member_count: 0, //累计推广会员数量
    total_success_cash_brokerage_money: 0, //累计成功提现佣金
    total_auditing_cash_brokerage_money: 0, //累计审核中佣金
    available_cash_brokerage_money: 0, //可提现金额
    cash_price: 0,
    total_cash: 0,
    team_count: 0,
    order_money: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var page = this;
    app.async_get_user_info().then((user_info) => {
      if (user_info.is_distributor != 1) {
        wx.showModal({
          title: "您还不是分销商！",
          content: '请先前往“我的->分销中心” 申请成为分销商',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              wx.redirectTo({
                url: '/pages/add-share/index',
              })
            }
          }
        });
      } else {
        page.setData({
          user_info: user_info,
        });
        app.request({
          url: app.api.share.get_info,
          success: function (result) {
            page.setData(result.data);
          },
        });
      }
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

})