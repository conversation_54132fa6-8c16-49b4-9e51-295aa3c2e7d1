// pages/live/index.js
var app = getApp(); //获取应用实例
var base64 = require("./js/base64.js");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: []
  },
  mixins: [require('./js/themeChanged.js')],

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);

    app.editTabBar();
    var that = this;
    app.request({
      url: app.api.live.list,
      success: function (res) {
        that.setData({
          list: res.data
        });
      }
    });
    that.setData({
      icon20: base64.icon20,
      icon60: base64.icon60
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  }
})