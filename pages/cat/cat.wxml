<!--pages/cat/cat.wxml-->
<view class="after-navber">
    <view class="flex-y-center flex-x-center"
          style="height: 100rpx;border-bottom: 1rpx solid #e3e3e3;border-top: 1rpx solid #e3e3e3;position: fixed;top: 0;width: 100%;z-index: 999;background: #EFEFF4">
        <navigator url="/pages/search/search" class="flex-y-center flex-x-center"
                   style="width: 700rpx;height: 60rpx;background: #fff;border: 1rpx solid #e3e3e3; border-radius: 10rpx;text-align: center;font-size: 9pt;color: #b2b2b2">
            <image src="/images/icon-search.png" style="width:24rpx;height: 24rpx;margin-right:10rpx "></image>
            <text>搜索</text>
        </navigator>
    </view>

    <view wx:if="{{store.cat_style==1||store.cat_style==3}}" style="height: 100%;padding-top: 100rpx;background: #fff">
        <!-- 大图模式（不显示侧栏） -->
        <scroll-view wx:if="{{store.cat_style==1}}" scroll-y="true" class="cat-block-list big"
                     style="padding: 20rpx 24rpx;height: 100%">
            <block wx:for="{{cat_list}}">
                <navigator class="cat-block flex-row" url="/pages/list/list?cat_guid={{item.guid}}">
                    <image style="width: 100%;height: 100%" src="{{item.big_pic_url}}"></image>
                </navigator>
                <block wx:if="{{item.list&&item.list.length>0}}">
                    <navigator wx:for="{{item.list}}" class="cat-block flex-row"
                               url="/pages/list/list?cat_guid={{item.guid}}">
                        <image style="width: 100%;height: 100%" src="{{item.big_pic_url}}"></image>
                    </navigator>
                </block>
            </block>
        </scroll-view>

        <!-- 小图标模式（不显示侧栏） -->
        <scroll-view wx:if="{{store.cat_style==3}}" scroll-y="true" class="cat-block-list"
                     style="padding: 20rpx 24rpx;height: 100%">
            <view class="cat-small-style cat-num-4 flex-row">
                <block wx:for="{{cat_list}}">
                    <view class="flex-grow-0">
                        <navigator class="cat-item" url="/pages/list/list?cat_guid={{item.guid}}">
                            <image class="cat-icon" src="{{item.pic_url}}"></image>
                            <view class="cat-name">{{item.name}}</view>
                        </navigator>
                    </view>

                    <block wx:if="{{item.list && item.list.length>0}}">
                        <view wx:for="{{item.list}}" class="flex-grow-0">
                            <navigator class="cat-item" url="/pages/list/list?cat_guid={{item.guid}}">
                                <image class="cat-icon" src="{{item.pic_url}}"></image>
                                <view class="cat-name">{{item.name}}</view>
                            </navigator>
                        </view>

                    </block>
                </block>
            </view>
        </scroll-view>

    </view>

    <view wx:if="{{store.cat_style==2||store.cat_style==4}}" style="height: 100%;padding-top: 100rpx" class="flex-row">
        <view class="flex-grow-0" style="height:100%;background: #fff">
            <scroll-view class="cat-list" scroll-y="true" style="height: 100%;width: 204rpx">
                <view bindtap="catItemClick"
                      data-index="{{index}}"
                      class="cat-item {{item.active?'active':''}} flex-y-center flex-x-center"
                      wx:for="{{cat_list}}">
                    <view class="cat-name">{{item.name}}</view>
                </view>
            </scroll-view>
        </view>
        <view class="flex-grow-1" style="background: #f6dae1">
            <view style="position: fixed;top: 100rpx;left: 200rpx;bottom: 0;right: 0;z-index:0;overflow: hidden">
                <scroll-view class="sub-cat-box cat-block-list" scroll-y="true" style="height: 100%;padding: 20rpx"
                             scroll-top="{{sub_cat_list_scroll_top}}">

                    <!-- 大图模式（开启侧栏） -->
                    <block wx:if="{{store.cat_style==2}}">
                        <block wx:if="{{current_cat}}">
                            <block wx:for="{{current_cat.list}}">
                                <navigator class="cat-block flex-row" url="/pages/list/list?cat_guid={{item.guid}}">
                                    <image style="width: 100%;height: 100%" src="{{item.big_pic_url}}"></image>
                                </navigator>
                            </block>
                        </block>
                        <block wx:else>
                            <block wx:for="{{cat_list}}">
                                <block wx:for="{{item.list}}">
                                    <navigator class="cat-block flex-row" url="/pages/list/list?cat_guid={{item.guid}}">
                                        <image style="width: 100%;height: 100%" src="{{item.big_pic}}"></image>
                                    </navigator>
                                </block>
                            </block>
                        </block>
                    </block>

                    <!-- 小图标模式（开启侧栏） -->
                    <view class="cat-small-style cat-num-3 flex-row" wx:if="{{store.cat_style==4}}">
                        <block wx:if="{{current_cat}}">
                            <block wx:if="{{current_cat.advert_pic}}">
                                <navigator class='advert' url="{{current_cat.advert_url}}">
                                    <image class="cat-icon" src="{{current_cat.advert_pic}}"></image>
                                </navigator>
                            </block>
                            <view wx:for="{{current_cat.list}}" class="flex-grow-0">
                                <navigator class="cat-item " url="/pages/list/list?cat_guid={{item.guid}}">
                                    <image class="cat-icon" src="{{item.pic_url}}"></image>
                                    <view class="cat-name">{{item.name}}</view>
                                </navigator>
                            </view>
                        </block>
                        <block wx:else>
                            <block wx:for="{{cat_list}}">
                                <block wx:for="{{item.list}}">
                                    <view class="flex-grow-0">
                                        <navigator class="cat-item " url="/pages/list/list?cat_guid={{item.guid}}">
                                            <image class="cat-icon" src="{{item.pic_url}}"></image>
                                            <view class="cat-name">{{item.name}}</view>
                                        </navigator>
                                    </view>
                                </block>
                            </block>
                        </block>
                    </view>

                </scroll-view>
            </view>
        </view>
    </view>
</view>