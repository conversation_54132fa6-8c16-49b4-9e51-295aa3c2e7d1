// pages/user/pay_qrcode.js
var app = getApp(); //获取应用实例

Page({

  /**
   * 页面的初始数据
   */
  data: {
    qrcode_url: '',
    time: '',
    member_info: {},
    money_unit: '',
    logo: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    app.pageOnLoad(this, options);
  },
  getData() {
    let that = this;
    app.request({
      url: app.api.member.pay_qrcode,
      data: {},
      success: function (result) {
        console.log(result)
        that.setData(result.data);
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.getData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})