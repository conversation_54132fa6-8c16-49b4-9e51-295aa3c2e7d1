<!--pages/user/edit.wxml-->
<view class="page">
  <!-- <view class="head_view" bindtap="change_head_img">
    <image class="head_img" src="{{user_info.head_img}}" background-size="cover"></image>
    <text class="head_text">更新头像</text>
  </view> -->
  <button class="head_view" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar" style="background-color: #fff;">
    <image class="head_img" src="{{user_info.head_img}}" background-size="cover"></image>
    <text class="head_text">更新头像</text>
  </button>
  <view class="weui-cells__title">个人资料</view>
  <view class="weui-cells weui-cells_after-title">
    <!-- <navigator url="" class="weui-cell weui-cell_access" hover-class="weui-cell_active">
      <view class="weui-cell__hd">
        <image src="{{icon}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
      </view>
      <view class="weui-cell__bd">名字</view>
      <view class="weui-cell__ft weui-cell__ft_in-access">谢永发</view>
    </navigator>
    <navigator url="" class="weui-cell weui-cell_example weui-cell_access" hover-class="weui-cell_active">
      <view class="weui-cell__hd">
        <image src="{{icon}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
      </view>
      <view class="weui-cell__bd">手机号</view>
      <view class="weui-cell__ft weui-cell__ft_in-access">18603047034</view>
    </navigator> -->
    <form bindsubmit="formBindsubmit" bindreset="formReset">
      <view class="weui-cell ">
        <view class="weui-cell__hd">
          <view class="weui-label">姓名</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" name="name" type="nickname" placeholder-class="weui-input__placeholder" placeholder="请输入姓名" value="{{user_info.name}}" />
        </view>
      </view>
      <!-- <view class="weui-cell ">
        <view class="weui-cell__hd">
          <view class="weui-label">门牌号</view>
        </view>
        <view class="weui-cell__bd">
          <input class="weui-input" name="room_number" placeholder-class="weui-input__placeholder" placeholder="请输入门牌号" value="{{user_info.room_number}}" />
        </view>
      </view>
      <view class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after">
        <view class="weui-cell__hd">
          <view class="weui-label">政治面貌</view>
        </view>
        <view class="weui-cell__bd">
          <picker bindchange="bindPoliticalOutlookChange" value="{{political_outlook_index}}" range="{{political_outlook_list}}">
            <view class="weui-select weui-select_in-select-after">{{political_outlook_list[political_outlook_index]}}
            </view>
          </picker>
        </view>
      </view>
      <view class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after">
        <view class="weui-cell__hd">
          <view class="weui-label">所属村庄</view>
        </view>
        <view class="weui-cell__bd">
          <picker bindchange="bindMemberGroupChange" range-key="name" value="{{member_group_index}}" range="{{member_group_list}}">
            <view class="weui-select weui-select_in-select-after">{{member_group_list[member_group_index].name}}</view>
          </picker>
        </view>
      </view> -->

      <view class="weui-btn-area">
        <button class="weui-btn leftBtn" type="primary" formType="submit">保存</button>
      </view>
    </form>
  </view>
</view>