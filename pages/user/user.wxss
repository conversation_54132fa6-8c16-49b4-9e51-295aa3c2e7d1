/* pages/user/user.wxss */

.weui-footer {
  margin-bottom: 50px;
}

.update_info {
  float: right;
  margin-top: -40rpx;
  width: 120rpx !important;
  min-width: 120rpx !important;
  max-width: 120rpx !important;
  padding: 0 16rpx !important;
}

.head_img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: block;
  overflow: hidden;
}

.grid_title {
  text-align: center;
  color: #999;
  font-size: 24rpx;
}

.grid_number {
  text-align: center;
  font-size: 48rpx
}

.weui-grid:before {
  border: none;
  height: 0;
}

.page__hd {
  padding: 10px;
}

.weui-grid {
  width: 33.33%;
}


.order-block {
  background: #fff;
  margin-bottom: 20rpx;
}

.order-block .my-order {
  display: block;
  border-bottom: 1rpx solid #e3e3e3;
  padding: 24rpx;
}

.order-block .nav-item {
  text-align: center;
  display: block;
  font-size: 9pt;
  padding: 24rpx 0;
  position: relative;
}

.order-block .nav-item .num-icon {
  position: absolute;
  top: 10rpx;
  left: 50%;
  margin-left: 30rpx;
  min-width: 30rpx;
  height: 30rpx;
  background: red;
  box-sizing: border-box;
  color: white;
  font-size: 10pt;
  text-align: center;
  line-height: 30rpx;
  padding: 0 5rpx;
  border-radius: 15rpx;
  display: inline-block;
}

.order-block .nav-item image {
  width: 60rpx;
  height: 57rpx;
  margin-bottom: 5rpx;
}

.option-list {
  margin-bottom: 20rpx;
}

.option-item {
  background: #fff;
  padding: 0 24rpx;
  display: block;
  position: relative;
}

.option-item .option-content {
  border-bottom: 1rpx solid #eee;
  height: 110rpx;
}

.option-item .option-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 24rpx;
}

.option-item .option-jisntou {
  width: 12rpx;
  height: 22rpx;
}

button.option-item {
  font-size: inherit;
  color: inherit;
  font-family: inherit;
  border: none;
  background: #fff;
}

.user-level {
  margin-top: 16rpx;
  position: relative;
}

.user-level image {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
  margin-left: -4rpx;
}

.level-name {
  height: 36rpx;
  border-radius: 18rpx;
  font-size: 8pt;
  color: #fff;
  background-color: #3c3642;
  padding: 0 16rpx 0 0;
}

.integral-bg,
.integral {
  height: 100rpx;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

.integral-bg {
  background-color: #888;
  opacity: 0.1;
}

.integral {
  padding: 0 27rpx;
  color: #353535;
  font-size: 12pt;
}

.integral text {
  margin-left: 20rpx;
}

.integral image {
  width: 52rpx;
  height: 54rpx;
  margin-right: 20rpx;
}

.userinfo-name,
.userinfo-addr {
  height: 248rpx;
}

.integral-num {
  margin-left: 20rpx;
  color: #fff;
}

.user-integral-arrow image {
  width: 16rpx;
  height: 26rpx;
}

.user-integral-go {
  font-size: 11pt;
  color: #666;
  margin-right: 20rpx;
}

.white {
  color: #fff;
}


/* 第三方csss*/
.user-info {
  height: 348rpx;
  color: #fff;
  position: relative;
}

.user-info .user-info-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.user-info .avatar {
  width: 130rpx;
  height: 130rpx;
  border-radius: 99999rpx;
  border: 5rpx solid #fff;
  margin: 0 24rpx;
}

.user-info .user-name {
  font-weight: bold;
}

.user-info .my-address {
  background: #ff4544;
  color: #fff;
  border-radius: 999rpx 0 0 999rpx;
  padding-right: 24rpx;
}

.user-info .my-address image {
  width: 44rpx;
  height: 44rpx;
  margin: 16rpx;
}

.order-block {
  background: #fff;
  margin-bottom: 20rpx;
}

.order-block .my-order {
  display: block;
  border-bottom: 1rpx solid #e3e3e3;
  padding: 24rpx;
}

.order-block .nav-item {
  text-align: center;
  display: block;
  font-size: 9pt;
  padding: 24rpx 0;
  position: relative;
}

.order-block .nav-item .num-icon {
  position: absolute;
  top: 8rpx;
  left: 50%;
  margin-left: 12rpx;
  display: inline-block;
  background: #f00;
  color: #fff;
  border-radius: 999rpx;
  padding: 0 8rpx;
  min-width: 40rpx;
  font-size: 7pt;
}

.order-block .nav-item image {
  width: 60rpx;
  height: 57rpx;
  margin-bottom: 5rpx;
}

.option-list {
  margin-bottom: 20rpx;
}

.option-item {
  background: #fff;
  padding: 0 24rpx;
  display: block;
  position: relative;
}

.option-item .option-content {
  border-bottom: 1rpx solid #eee;
  height: 110rpx;
}

.option-item .option-icon {
  width: 50rpx;
  height: 50rpx;
  margin-right: 24rpx;
}

.option-item .option-jisntou {
  width: 12rpx;
  height: 22rpx;
}

button.option-item {
  font-size: inherit;
  color: inherit;
  font-family: inherit;
  border: none;
  background: #fff;
}

.user-level {
  margin-top: 16rpx;
  position: relative;
}

.user-level image {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
  margin-left: -4rpx;
}

.level-name {
  height: 36rpx;
  border-radius: 18rpx;
  font-size: 8pt;
  color: #fff;
  background-color: #3c3642;
  padding: 0 16rpx 0 0;
}

.integral-bg,
.integral {
  height: 100rpx;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

.integral-bg {
  background-color: #888;
  opacity: 0.1;
}

.integral {
  padding: 0 27rpx;
  color: #353535;
  font-size: 12pt;
}

.integral text {
  margin-left: 20rpx;
}

.integral image {
  width: 52rpx;
  height: 54rpx;
  margin-right: 20rpx;
}

.userinfo-name,
.userinfo-addr {
  height: 248rpx;
}

.integral-num {
  margin-left: 20rpx;
}

.user-integral-arrow image {
  width: 16rpx;
  height: 26rpx;
}

.user-integral-go {
  font-size: 11pt;
  color: #666;
  margin-right: 20rpx;
}

.shadow-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-radius: 0;
}

button::after {
  transform: scale(0);
}

.num-1 {
  color: #ffbb43;
  font-size: 13pt;
  margin-bottom: 16rpx;
}

.num-2 {
  color: #666;
  align-items: center;
  line-height: 1;
}

.wallet {
  width: 100%;
  height: 140rpx;
  margin-bottom: 20rpx;
}

.wallet-1 {
  background-color: #fbfbfb;
  width: 310rpx;
  height: 100%;
}

.wallet-2 {
  background-color: #fff;
  width: 440rpx;
  height: 100%;
}

.wallet-3 {
  background-color: #fff;
  width: 300rpx;
  height: 100%;
}

.wallet .wallet-3:last-child .flex-grow-1 {
  border-left: 1rpx solid #e2e2e2;
}

.option-item.style {
  width: 25%;
  padding: 24rpx;
  font-size: 9pt;
}

.option-item.style .option-icon {
  margin: 0;
  margin-bottom: 12rpx;
}

.user-info-1 {
  margin-top: 50rpx;
  background-color: #fff;
  border-radius: 10rpx;
  height: 200rpx;
  width: 700rpx;
  color: #666;
  box-shadow: 0px 0px 12rpx 1rpx #ddd;
}