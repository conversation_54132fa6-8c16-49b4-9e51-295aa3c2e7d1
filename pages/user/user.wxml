<!--pages/user/user.wxml-->
<!-- <text>这个页面待完善</text>
<view>
  <view>GUID:{{member.guid}}</view>
  <view>卡号:{{member.card_id}}</view>
  <view>mobile:{{member.mobile}}</view>
  <view>注册时间:{{member.create_time}}</view>
  <view>更新时间:{{member.update_time}}</view>
  <view>余额:{{member.money}}</view>
  <view>积分:{{member.point}}</view>
  <view>openid:{{member.openid}}</view>
</view> -->
<import src="/common/tabbar/tabbar.wxml" />
<template is="tabbar" data="{{tabbar}}" />
<!--user.wxml-->
<view class="page after-navber">

  <view style="position:relative;">
    <view class="user-info flex-row flex-y-center" wx:if="{{1==2}}">
      <image class="user-info-bg" mode="aspectFill" src="/images/detail.png"></image>
      <view class="flex-grow-1 flex-y-center flex-row userinfo-name">
        <view class="flex-grow-0">
          <image class="avatar" src="{{member.head_img}}"></image>
        </view>
        <view class="flex-grow-1">
          <text class="user-name">{{member.name ? member.name : '用户'}} ID:{{member.id}}</text>
          <!-- <button wx:if="{{(member.mobile && !member.name) || 1==1}}" class="weui-btn  mini-btn update_info" size="mini" type="primary" open-type="getUserProfile" bindtap="getUserProfile">更新资料</button> -->


          <view bindtap="{{next_level||user_info&&user_info.level!=-1?'member':''}}" class="user-level flex-row">
            <view class="level-name flex-y-bottom">
              <image src="{{__wxapp_img.user.level.url}}"></image>
              <view class="flex-y-center" style="height:100%;">{{user_info.level_name||'普通用户'}}</view>

            </view>



            <!-- <navigator wx:if="{{member.mobile && member.name}}" url="/pages/user/edit">
              <button class="weui-btn mini-btn update_info" type="default" size="mini">更新</button>
            </navigator> -->


          </view>

          <view style="color: #fff;" class="weui-media-box__desc" wx:if="{{member.mobile}}">
            <text style="display: inline-block;"> 手机:{{member.mobile ? member.mobile : ''}}
            </text>
            <!-- <navigator url="/pages/login/login">
                <button class="weui-btn mini-btn" type="default" size="mini">更新</button>
              </navigator> -->
          </view>
          <!-- <button wx:if="{{!member.mobile || 1==1}}" class="weui-btn  mini-btn update_info" size="mini" type="primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">授权手机号</button> -->

        </view>
      </view>
      <!-- <view class="flex-grow-0 flex-y-center userinfo-addr">
        <navigator class="my-address flex-y-center" url="/pages/address/address">
          <image src="/images/icon-user-dz.png"></image>
          <text>收货地址</text>
        </navigator>
      </view> -->





    </view>
    <view class="user-info flex-row flex-x-center flex-y-center" wx:if="{{style.top==1}}">
      <image class="user-info-bg" mode="aspectFill" src="/images/detail.png"></image>
      <view>
        <view class="flex-x-center">
          <image class="avatar" src="{{member.head_img}}"></image>
        </view>
        <view bindtap="{{next_level||user_info&&user_info.level!=-1?'member':''}}" class="user-level flex-row flex-x-center" style="margin-top: -20rpx;">
          <view class="level-name flex-y-bottom">
            <image src="{{__wxapp_img.user.level.url}}"></image>
            <view class="flex-y-center" style="height:100%;">{{user_info.level_name||'普通用户'}}</view>
          </view>
        </view>
        <view class="flex-x-center" style="margin-top: 10rpx;">
          <text class="user-name">{{member.name ? member.name : '用户'}} ID:{{member.id}}</text>
        </view>

      </view>
    </view>
    <view class="user-info flex-row flex-y-center flex-x-center" wx:if="{{style.top==2}}">
      <image class="user-info-bg" mode="aspectFill" src="/images/detail.png"></image>
      <view class="flex-y-center user-info-1">
        <view class="flex-grow-1 flex-y-center flex-row">
          <view class="flex-grow-0">
            <image class="avatar" src="{{member.head_img}}"></image>
          </view>
          <view class="flex-grow-1">
            <text class="user-name text-more">{{member.name ? member.name : '用户'}} ID:{{member.id}}</text>
            <view bindtap="{{next_level||user_info&&user_info.level!=-1?'member':''}}" class="user-level flex-row">
              <view class="level-name flex-y-bottom">
                <image src="{{__wxapp_img.user.level.url}}"></image>
                <view class="flex-y-center" style="height:100%;">{{user_info.level_name||'普通用户'}}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="flex-grow-0 flex-y-center flex-x-center" style="padding: 10rpx;width: 200rpx;">
          <block wx:if="{{item.open_type=='navigator'}}" wx:for="{{menus}}" wx:key="{{item.id}}">
            <navigator hover-class="none" class="option-item flex-grow-0 style" style="width: auto;" url="{{item.url}}" wx:if="{{item.id=='address'}}">
              <view>
                <view class="flex-x-center">
                  <image class="option-icon" src="{{item.icon}}"></image>
                </view>
                <view class="text-more-2" style="text-align: center">{{item.name}}</view>
              </view>
            </navigator>
          </block>
        </view>
      </view>
    </view>
  </view>

  <!-- 头像 -->
  <view class="page__hd order-block" style="padding-top: 1rem;">
    <view class="weui-panel weui-panel_access">
      <view class="weui-panel__bd">
        <view class="weui-media-box weui-media-box_appmsg" hover-class="btn-hover">
          <view class="weui-media-box__hd weui-media-box__hd_in-appmsg">
            <image class="head_img" src="{{member.head_img}}"></image>
          </view>
          <view class="weui-media-box__bd weui-media-box__bd_in-appmsg">
            <view class="weui-media-box__title">{{member.name ? member.name : '用户'}} ID:{{member.id}}</view>


            <button wx:if="{{!member.mobile}}" class="weui-btn mini-btn" size="mini" type="primary" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" style="font-size: 30rpx;">授权手机号</button>
            <button wx:if="{{member.mobile && !member.name}}" class="weui-btn  mini-btn update_info" size="mini" type="primary" open-type="getUserProfile" bindtap="getUserProfile">更新</button>
            <image wx:if="{{data.show_pay_qrcode}}" style="width: 40rpx;height: 40rpx;float: right;display:  block;text-align: center;margin-top: -30rpx;margin-left: 30rpx;" src="/images/qrcode.png" catchtap="show_pay_qrocde"></image>

            <navigator wx:if="{{member.mobile && member.name}}" url="/pages/user/edit?way=1">

              <button class="weui-btn mini-btn update_info" type="default" style="font-size: 30rpx;" size="mini">更新</button>
            </navigator>

            <view class="weui-media-box__desc" wx:if="{{member.mobile}}">
              <text style="display: inline-block;"> 手机:{{member.mobile ? member.mobile : ''}}
              </text>
              <!-- <navigator url="/pages/login/login">
                <button class="weui-btn mini-btn" type="default" size="mini">更新</button>
              </navigator> -->
            </view>

          </view>
        </view>
      </view>
    </view>


  </view>


  <view class="page order-block">
    <view class="weui-grids">
      <block wx:for="{{data.card_list}}" wx:for-item="item" wx:for-index="index">
        <navigator hover-class="none" class="weui-grid" aria-role="button" url="{{item.url}}" style="border: none;width: {{data.card_list_width}}%;">
          <!-- <view class="weui-grid__icon">
				<image src="../images/icon_tabbar.png" alt></image>
			</view> -->
          <view class="grid_title">{{item.title}}</view>
          <view class="grid_number">{{item.number}}</view>
        </navigator>

      </block>

    </view>
  </view>

  <view class="order-block">
    <navigator class="my-order" url="/pages/order/order">我的订单</navigator>
    <view class="flex-row">
      <view class="flex-grow-1" wx:for="{{data.goods_order_status_list}}" wx:for-item="item" wx:for-index="index">
        <navigator class="nav-item" url="/pages/order/order?status={{item.status}}">
          <view>
            <image src="{{item.image}}" />
          </view>
          <view>{{item.name}}</view>
          <view class="num-icon" wx:if="{{item.count && item.count > 0}}">
            {{item.count}}
          </view>
        </navigator>
      </view>

    </view>
  </view>



  <view class="page__bd" style="padding-bottom: 10rpx;">
    <view class="weui-cells weui-cells_after-title">

      <block wx:for="{{data.url_list}}" wx:for-item="item" wx:for-index="index">
        <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" data-url="{{item.url}}" bindtap="goto_webview" wx:if="{{item.type==1}}">
          <view class="weui-cell__hd">
            <image src="{{item.image}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
          </view>
          <view class="weui-cell__bd">{{item.name}}</view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>

        <navigator url="{{item.url}}" class="weui-cell weui-cell_access" hover-class="weui-cell_active" wx:if="{{item.type==2}}">
          <view class="weui-cell__hd">
            <image src="{{item.image}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
          </view>
          <view class="weui-cell__bd">{{item.name}}</view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </navigator>

        <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" wx:if="{{item.type==3}}">
          <view class="weui-cell__hd">
            <image src="{{item.image}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
          </view>
          <button open-type="contact" plain="true" style="padding: 0;background-color: #fff;text-align: left;font-weight: normal;font-size: 32rpx;" class="weui-cell__bd">
            {{item.name}}
          </button>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>

        <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="clear_cache" wx:if="{{item.type==4}}">
          <view class="weui-cell__hd">
            <image src="{{item.image}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
          </view>
          <view class="weui-cell__bd">{{item.name}}</view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>


        <view class="weui-cell weui-cell_access" hover-class="weui-cell_active" bindtap="clear_cache" wx:if="{{item.type==5}}" data-phone="{{item.phone}}" bindtap="makePhoneCall">
          <view class="weui-cell__hd">
            <image src="{{item.image}}" style="margin-right: 16px;vertical-align: middle;width:20px; height: 20px;"></image>
          </view>
          <view class="weui-cell__bd">{{item.name}}</view>
          <view class="weui-cell__ft weui-cell__ft_in-access"></view>
        </view>


      </block>


    </view>
    <!-- <view style="padding: 20rpx">
      <button type="primary" open-type="feedback">意见反馈</button>
    </view> -->
  </view>
  <copy_right style="padding-bottom: 80px;"></copy_right>
</view>