// pages/user/user.js
var app = getApp(); //获取应用实例
Page({
  /**
   * 页面的初始数据
   */
  data: {
    member: [],
    data: {}
  },
  makePhoneCall(e) {
    let phoneNumber = e.currentTarget.dataset.phone;
    if (!phoneNumber) {
      wx.showModal({
        title: '提示',
        content: '客服电话未设置',
        showCancel: false,
        success: function (res) {}
      })
      return;
    }
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: (res) => {},
      fail: (res) => {},
      complete: (res) => {},
    })
  },
  loadData() {
    var that = this;
    app.request({
      url: app.api.user.index,
      loading: false,
      success: function (res) {
        that.setData({
          member: res.data.user_info,
          data: res.data,
        });
      }
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    app.editTabBar();
    // this.loadData();
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadData();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
  goto_webview(e) {
    var url = e.currentTarget.dataset.url;
    app.to_web(url);
  },
  bindGetUserInfo(e) {
    console.log(e.detail.userInfo)
  },
  clear_cache() {
    try {
      wx.clearStorageSync();
      wx.showToast({
        title: '清空成功',
        icon: 'success',
        duration: 2000
      })
    } catch (e) {
      // Do something when catch error
    }
  },
  getPhoneNumber(e) {
    let that = this;
    return app.auth.getPhoneNumber(e, function (result) {
      that.setData({
        member: result.data.user_info
      });
      wx.setStorageSync("user_info", result.data.user_info);
    });
  },
  getUserProfile: function () {
    let that = this;
    app.auth.getUserProfile(function (result) {
      that.updateUserInfo(result);
    });
  },
  show_pay_qrocde: function () {
    wx.navigateTo({
      url: '/pages/user/pay_qrcode',
    })
  },
  updateUserInfo: function (res) {
    let that = this;
    console.log(res)
    app.request({
      url: app.api.passport.get_user_info,
      data: {
        user_info: res.rawData,
        encrypted_data: encodeURIComponent(res.encryptedData),
        iv: res.iv,
        signature: res.signature,
        code: res.code,
        appid: wx.getAccountInfoSync().miniProgram.appId
      },
      success: function (result) {
        that.setData({
          member: result.data
        });
        wx.setStorageSync("user_info", result.data);
      }
    })
  },

})