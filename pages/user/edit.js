// pages/user/edit.js
var app = getApp(); //获取应用实例
var that;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    user_info: {},
    way: 0,
    // political_outlook_index: -1,
    // political_outlook_list: ['保密', '群众', '党员'],
    // member_group_index: -1,
    // member_group_list: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    that = this;
    if (options.way !== undefined) {
      that.setData({
        way: options.way
      })
    }
    that.showMemberInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {

  },
  showMemberInfo: function () {
    app.request({
      url: app.api.user.info,
      data: {},
      success: function (res) {
        wx.setStorageSync("user_info", res.data);
        that.setData({
          user_info: res.data,
        })
        //that.showMemberInfo(that.data.member_guid);
        //that.showMemberGroupInfo();
      }
    });
  },
  showMemberGroupInfo() {
    app.request({
      url: app.api.scores.member_group,
      data: {},
      success: function (res) {
        let member_group_list = res.data;
        let member_group_guid = that.data.user_info.member_group_guid;
        for (var i = 0; i < member_group_list.length; i++) {
          if (member_group_list[i].guid == member_group_guid) {
            console.log(member_group_guid);
            console.log(i);
            that.setData({
              member_group_index: i,
            })
          }
        }
        that.setData({
          member_group_list: member_group_list,
        })
        //that.showMemberInfo(that.data.member_guid);
      }
    });
  },
  onChooseAvatar(e) {
    console.log(e.detail.avatarUrl);
    var upload = require("../../common/upload.js");
    console.log('change_head_img2');
    var that = this;
    upload.uploader(e.detail.avatarUrl, function (image_url) {
      console.log(image_url);
      //todo 要更新会员资料
      that.updateMemberInfo({
        head_img: image_url
      }, false);
    }) // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
  },
  change_head_img: function () {
    var upload = require("../../common/upload.js");
    console.log('change_head_img');
    var that = this;
    wx.chooseImage({
      count: 1, // 默认9
      sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
      sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
      success: function (res) {
        upload.uploader(res.tempFilePaths[0], function (image_url) {
          console.log(image_url);
          //todo 要更新会员资料
          that.updateMemberInfo({
            head_img: image_url
          }, false);
        }) // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
      }
    })
  },
  updateMemberInfo: function (data, require_back) {
    console.log(data);
    var that = this;
    data.guid = that.data.user_info.guid; //更新会员guid赋值
    app.request({
      url: app.api.user.edit,
      data: data,
      success: function (res) {
        wx.setStorageSync("user_info", res.data);
        that.setData({
          user_info: res.data,
        })
        wx.showToast({
          title: '保存成功',
          icon: 'success',
          duration: 2000,
          success: function () {
            setTimeout(function () {
              if (require_back) {
                if (that.data.way == 1) {
                  wx.navigateBack({
                    delta: 1,
                  });
                } else {
                  wx.navigateTo({
                    url: '/pages/user/user',
                  })
                }
              }
            }, 1000);
          }
        });
        //that.showMemberInfo(that.data.member_guid);
      }
    })

  },
  bindPoliticalOutlookChange: function (e) {
    //console.log('bindPoliticalOutlookChange 发生选择改变，携带值为', e.detail.value);
    this.setData({
      political_outlook_index: e.detail.value
    })
  },
  bindMemberGroupChange: function (e) {
    //console.log('bindMemberGroupChange 发生选择改变，携带值为', e.detail.value);
    this.setData({
      member_group_index: e.detail.value,
    })
    //console.log("选中的id值:" + e.target.dataset.guid);
  },
  formBindsubmit: function (e) {
    console.log("提交表单:" + JSON.stringify(e));
    let data = e.detail.value;
    data.guid = that.data.user_info.guid;
    //data.political_outlook = that.data.political_outlook_index;
    //data.member_group_guid = that.data.member_group_list[that.data.member_group_index].guid;
    console.log(data);
    that.updateMemberInfo(data, true);
  },
})