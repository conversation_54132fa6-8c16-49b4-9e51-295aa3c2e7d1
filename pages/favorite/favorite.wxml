<!--pages/favorite/favorite.wxml-->
<view class="flex-col h-100">
  <view class="flex-grow-0 flex-row tab-bar">
    <view style="text-align: center" class="flex-grow-1" bindtap="tabSwitch" data-index="0">
      <view class="tab-name {{swiper_current==0?'active':''}}">商品({{goods.total?goods.total:0}})</view>
    </view>
    <view style="text-align: center" class="flex-grow-1" bindtap="tabSwitch" data-index="1">
      <view class="tab-name {{swiper_current==1?'active':''}}">卡券({{code.total?code.total:0}})</view>
    </view>
    <view style="text-align: center" class="flex-grow-1" bindtap="tabSwitch" data-index="2">
      <view class="tab-name {{swiper_current==2?'active':''}}">专题({{topic.total?topic.total:0}})</view>
    </view>
  </view>
  <view class="flex-grow-1 wh-100" style="position: absolute;top: 0;left: 0;z-index:0;padding-top: 100rpx">
    <swiper class="h-100" bindchange="swiperChange" current="{{swiper_current}}" duration="300">
      <swiper-item>
        <scroll-view class="h-100" scroll-y="true" bindscrolltolower="goodsScrollBottom" lower-threshold="1">
          <view class="goods-list">
            <navigator url="/pages/index/goods_detail?guid={{item.guid}}" open-type="navigate" wx:for="{{goods.list}}" class="goods-item">
              <image class="goods-pic" src="{{item.pic}}" mode="aspectFill" />
              <view class="goods-info">
                <text class="goods-name">{{item.name}}</text>
                <text class="goods-price">￥{{item.price}}</text>
              </view>
            </navigator>
          </view>
          <view class="no-content" wx:if="{{!goods.is_loading && (!goods.list || goods.list.length==0)}}">

            <text>暂无收藏的商品</text>

            <view style="padding-top:100rpx">
              <navigator style="background-color: #ff4544;" class="weui-btn weui-btn_primary" aria-role="button" url="/pages/index/index">去商城逛逛</navigator>
            </view>
          </view>

          <view class="no-content" wx:if="{{!goods.is_loading && goods.total>0 && goods.is_more==false}}">
            <text>已经到底啦</text>
          </view>


          <view class="loading-more {{goods.is_loading?'active':''}}">
            <image src="/images/loading.svg"></image>
          </view>

            <!-- <include src="/common/loadmore/loadmore"></include> -->

        </scroll-view>
      </swiper-item>


      <swiper-item>
        <scroll-view class="h-100" scroll-y="true" bindscrolltolower="codeScrollBottom" lower-threshold="1">

          <view class="goods-list">
            <navigator url="/pages/index/coupon_detail?guid={{item.guid}}" open-type="navigate" wx:for="{{code.list}}" class="goods-item">
              <image class="goods-pic" src="{{item.pic}}" mode="aspectFill" />
              <view class="goods-info">
                <text class="goods-name">{{item.name}}</text>
                <text class="goods-price">￥{{item.selling_price}}</text>
              </view>
            </navigator>
          </view>
          <view class="no-content" wx:if="{{!code.is_loading && (!code.list || code.list.length==0)}}">

            <text>暂无收藏的卡券</text>

            <view style="padding-top:100rpx">
              <navigator style="background-color: #ff4544;" class="weui-btn weui-btn_primary" aria-role="button" url="/pages/index/index">去商城逛逛</navigator>
            </view>
          </view>

          <view class="no-content" wx:if="{{!code.is_loading && code.total>0 && code.is_more==false}}">
            <text>已经到底啦</text>
          </view>

          <view class="loading-more {{code.is_loading?'active':''}}">
            <image src="/images/loading.svg"></image>
          </view>
        </scroll-view>
      </swiper-item>


      <swiper-item>
        <scroll-view class="h-100" scroll-y="true" bindscrolltolower="topicScrollBottom" lower-threshold="1">
          <view class="list">
            <block wx:for="{{topic.list}}">

              <navigator wx:if="{{item.layout==0}}" class="item layout-0" url="/pages/topic/topic?guid={{item.guid}}">
                <view class="flex-row">
                  <view class="flex-grow-1 flex-col">
                    <view class="flex-grow-1">
                      <view class="title">{{item.title}}</view>
                    </view>
                    <view class="flex-grow-0 read-count">{{item.read_count}}</view>
                  </view>
                  <view class="flex-grow-0" style="position: relative">
                    <image class="cover-pic" mode="aspectFill" src="{{item.cover_pic}}"></image>
                    <view class="goods-count" wx:if="{{item.goods_count}}">{{item.goods_count}}
                    </view>
                  </view>
                </view>
              </navigator>

              <navigator wx:if="{{item.layout==1}}" class="item layout-1" url="/pages/topic/topic?guid={{item.guid}}">
                <view class="title" style="font-weight: 600;font-size: 12pt">{{item.title}}</view>
                <image class="cover-pic" mode="aspectFill" src="{{item.cover_pic}}"></image>
                <view class="flex-row">
                  <view class="flex-grow-1 read-count">{{item.read_count}}</view>
                  <view class="flex-grow-0 goods-count" wx:if="{{item.goods_count}}">
                    {{item.goods_count}}
                  </view>
                </view>
              </navigator>

            </block>
          </view>
          <view class="no-content" wx:if="{{!topic.is_loading && (!topic.list || topic.list.length==0)}}">
            <text>暂无收藏的专题</text>
            <view style="padding-top:100rpx">
              <navigator style="background-color: #ff4544;" class="weui-btn weui-btn_primary" aria-role="button" url="/pages/topic-list/topic-list">查看专题列表</navigator>
            </view>
          </view>

          <view class="no-content" wx:if="{{!topic.is_loading && topic.total>0 && topic.is_more==false}}">
            <text>已经到底啦</text>
          </view>


          <view class="loading-more {{topic.is_loading?'active':''}}">
            <image src="/images/loading.svg"></image>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </view>
</view>