// pages/favorite/favorite.js
var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    swiper_current: 0,
    goods: {
      list: null,
      is_more: true,
      is_loading: false,
      total: 0,
      page: 1,
    },
    code: {
      list: null,
      is_more: true,
      is_loading: false,
      total: 0,
      page: 1,
    },
    topic: {
      list: null,
      is_more: true,
      is_loading: false,
      total: 0,
      page: 1,
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    this.loadGoodsList({
      reload: true,
      page: 1,
    });
    this.loadCodeList({
      reload: true,
      page: 1,
    });
    this.loadTopicList({
      reload: true,
      page: 1,
    });

  },


  tabSwitch: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    page.setData({
      swiper_current: index,
    });
  },
  swiperChange: function (e) {
    console.log(e);
    var page = this;
    page.setData({
      swiper_current: e.detail.current,
    });
  },

  loadGoodsList: function (args) {
    var page = this;
    if (page.data.goods.is_loading)
      return;
    if (args.loadmore && !page.data.goods.is_more)
      return;
    page.data.goods.is_loading = true;
    page.setData({
      goods: page.data.goods,
    });
    app.request({
      url: api.user.favorite_list,
      data: {
        page: args.page,
        type: 1,
      },
      success: function (res) {
        if (args.reload) {
          page.data.goods.list = res.data.data;
        }
        if (args.loadmore) {
          page.data.goods.list = page.data.goods.list.concat(res.data.data);
        }
        console.log(page.data.goods.list);
        page.data.goods.page = args.page;
        page.data.goods.total = res.data.total;
        page.data.goods.is_more = res.data.current_page < res.data.last_page;
        page.setData({
          goods: page.data.goods,
        });
      },
      complete: function () {
        page.data.goods.is_loading = false;
        page.setData({
          goods: page.data.goods,
        });
      }
    });
  },
  loadCodeList: function (args) {
    var page = this;
    if (page.data.code.is_loading)
      return;
    if (args.loadmore && !page.data.code.is_more)
      return;
    page.data.code.is_loading = true;
    page.setData({
      code: page.data.code,
    });
    app.request({
      url: api.user.favorite_list,
      data: {
        page: args.page,
        type: 2,
      },
      success: function (res) {
        if (args.reload) {
          page.data.code.list = res.data.data;
        }
        if (args.loadmore) {
          page.data.code.list = page.data.code.list.concat(res.data.data);
        }
        page.data.code.page = args.page;
        page.data.code.total = res.data.total;
        page.data.code.is_more = res.data.current_page < res.data.last_page;
        page.setData({
          code: page.data.code,
        });
      },
      complete: function () {
        page.data.code.is_loading = false;
        page.setData({
          code: page.data.code,
        });
      }
    });

  },

  loadTopicList: function (args) {
    var page = this;
    if (page.data.topic.is_loading)
      return;
    if (args.loadmore && !page.data.topic.is_more)
      return;
    page.data.topic.is_loading = true;
    page.setData({
      topic: page.data.topic,
    });
    app.request({
      url: api.user.favorite_list,
      data: {
        page: args.page,
        type: 3,
      },
      success: function (res) {
        if (args.reload) {
          page.data.topic.list = res.data.data;
        }
        if (args.loadmore) {
          page.data.topic.list = page.data.topic.list.concat(res.data.data);
        }
        page.data.topic.page = args.page;
        page.data.topic.total = res.data.total;
        page.data.topic.is_more = res.data.current_page < res.data.last_page;
        page.setData({
          topic: page.data.topic,
        });
      },
      complete: function () {
        page.data.topic.is_loading = false;
        page.setData({
          topic: page.data.topic,
        });
      }
    });
  },
  goodsScrollBottom: function () {
    console.log('goodsScrollBottom')
    var page = this;
    page.loadGoodsList({
      loadmore: true,
      page: page.data.goods.page + 1,
    });
  },
  codeScrollBottom: function () {
    console.log('codeScrollBottom')
    var page = this;
    page.loadCodeList({
      loadmore: true,
      page: page.data.code.page + 1,
    });
  },
  topicScrollBottom: function () {
    console.log('topicScrollBottom')
    var page = this;
    page.loadTopicList({
      loadmore: true,
      page: page.data.topic.page + 1,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadGoodsList({
      reload: true,
      page: 1,
    });
    this.loadCodeList({
      reload: true,
      page: 1,
    });
    this.loadTopicList({
      reload: true,
      page: 1,
    });

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },
  onReachBottom: function () {
    console.log('onReachBottom')
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  }
});