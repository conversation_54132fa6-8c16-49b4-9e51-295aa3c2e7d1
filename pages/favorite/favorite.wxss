/* pages/favorite/favorite.wxss */

.goods-list{
    padding-top: 5rpx;
}
.goods-item{
    width: 365rpx;
    display: inline-block;
    position: relative;
    margin: 0 5rpx;
    margin-bottom: 10rpx;
    font-size: 0;
    background: #fff;
}
.goods-item .goods-pic{
    width: 100%;
    height: 365rpx;
}
.goods-item .goods-info {
    padding: 5rpx 0;
}
.goods-item .goods-name {
    white-space: nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
    padding: 10rpx;
    font-size: 11pt;
    display: block;
    text-align: center;
}

.goods-item .goods-price{
    font-size: 11pt;
    color: #f40;
    display: block;
    text-align: center;
}
.loading-bar{
    visibility: hidden;
}
.loading-bar.active{
    visibility: visible;
}

.tab-bar{
    background: #fff;
    border-top: 1rpx solid #e3e3e3;
    border-bottom: 1rpx solid #e3e3e3;
    position: fixed;
    top:0;
    left: 0;
    width: 100%;
    z-index: 10;
    height: 100rpx;
}
.tab-bar .tab-name{
    display: inline-block;
    padding: 24rpx 32rpx;
    border-bottom: 2rpx solid transparent;
    height: 98rpx;
}
.tab-bar .tab-name.active{
    border-color: #ff4544;
    color: #ff4544;
}

.list .item{
    display: block;
    background: #fff;
    padding: 34rpx 24rpx;
    margin-bottom: 10rpx;
}

.list .item .title{
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.list .item .cover-pic{
    width: 268rpx;
    height: 202rpx;
    margin-left: 10rpx;
    display: block;
}
.list .item .goods-count,
.list .item .read-count{
    font-size: 9pt;
    color: #888;
}

.list .item.layout-1 .title{
    margin-bottom: 24rpx;
    -webkit-line-clamp: 2;
}

.list .item.layout-1 .cover-pic{
    width: 702rpx;
    height: 350rpx;
    margin-left: 0;
    margin-bottom: 24rpx;
}

.list .item.layout-0 .goods-count{
    position: absolute;
    bottom: 20rpx;
    right: 0rpx;
    display: inline-block;
    background: rgba(0,0,0,.5);
    color: rgba(255, 255, 255, 0.8);
    padding: 5rpx 10rpx;
}

.no-content{
    color: #888;
    padding: 100rpx 0 0 0;
    text-align: center;
}

.loading-more{
    text-align: center;
    opacity: 0;
}

.loading-more.active{
    opacity: 1;
}

.loading-more image{
    width: 160rpx;
    height: 80rpx;
}