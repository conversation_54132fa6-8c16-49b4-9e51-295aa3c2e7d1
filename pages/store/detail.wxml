<!-- <import src="../../wxParse/wxParse.wxml"></import> -->
<view class="page">
  <!-- <include src="/components/common/common"></include>
  <include src="/components/header/header"></include> -->
  <view class="w-100">
    <swipers banner_list="{{store.banner_list}}"> </swipers>
  </view>
  <view class="weui-flex">
    <view class="icon_container" style="padding-left: 20rpx;" wx:if="{{store.brand_info.icon}}">
      <image class="store_icon" src="{{store.brand_info.icon}}" alt></image>
    </view>
    <view class="weui-flex__item block" style="padding-left: 20rpx;">
      <view class="block block_1">
        <view style="display: block;">
          <view class="title">{{store.store_name}}</view>
          <!-- <view class="flex-row middle">
        <view class="flex-grow-0" style="margin-right:20rpx;color:#707070">评分：</view>
        <view class="flex-grow-1">
          <image src="{{__wxapp_img.store.detail_love.url}}" style="width:28rpx;height:24rpx;margin-right:6rpx;" wx:if="{{index<store.score}}" wx:for="{{score}}" wx:key="{{item.id}}"></image>
        </view>
      </view> -->
          <view style="color:#707070;" class="line" wx:if="{{store.business_time}}">
            <image src="/images/icon-time.png" class="icon"></image>
            <text style="padding-left: 20rpx;"> 营业时间：{{store.business_time}}</text>
          </view>
          <view style="color:#707070;" class="line" bindtap="makePhoneCall" data-phone="{{store.mobile}}" wx:if="{{store.mobile}}">
            <image src="/images/icon-phone.png" class="icon"></image>
            <text style="padding-left: 20rpx;"> 客服电话：{{store.mobile}}</text>
            <view style="padding-left: 20rpx;">
              <text class="copy-text-btn" bindtap="copyText">点击拨打</text>
            </view>
          </view>

        </view>

      </view>


    </view>
    <!-- <view>
        <view class="placeholder">weui</view>
      </view> -->
  </view>


  <view class="weui-flex block" style="padding:10rpx 0 0rpx 24rpx;color:#707070 ;border: none;" bindtap="openLocation">
    <view>
      <view class="placeholder">
        <image src="/images/icon-dingwei.png" class="icon" style="height: 0.8rem;width:0.8rem;"></image>
        地址：
      </view>
    </view>
    <view class="weui-flex__item">
      <view class="placeholder" style="padding-right: 20rpx;">
        {{store.address_info}}
      </view>
    </view>
    <view>
      <view class="placeholder" style="text-align: center;padding-right: 20rpx;">
        <image src="/images/icon-daohang.png" style="height: 1.2rem;width:1.2rem;"></image>
        <text style="color: #888;">
          {{store.distance_text}}
        </text>
      </view>
    </view>
  </view>
  <view class="block_3 flex-y-center" style="margin-top: 4rpx;border: none;" wx:if="{{store.brand_info.description}}">品牌介绍</view>

  <view class="block" style="border: none;" wx:if="{{store.brand_info.description}}">
    <view class="flex-grow-1" style="padding:0rpx 0 0rpx 24rpx;">
      <view style="color:#707070;">
        <rich-text nodes="{{store.brand_info.description}}"></rich-text>
      </view>
    </view>
    <!-- <view class="weui-flex">
        <view class="weui-flex__item">
          <view class="placeholder">weui</view>
        </view>
        <view class="weui-flex__item">
          <view class="placeholder">weui</view>
        </view>
      </view> -->


    <!-- <view class="flex-grow-1 block_2">
      <view bindtap="openLocation" style="width: 85%;">
        <image src="/images/icon-dingwei.png" class="icon" style="height: 0.8rem;width:0.8rem;"></image>
        门店地址：{{store.address}}
      </view>

      <view bindtap="openLocation" style="float: right;position: relative; top:-100rpx; right:10rpx;text-align: center;">
        <image src="/images/icon-daohang.png" style="height: 1.8rem;width:1.8rem;"></image>
        <text style="color: #888;">
          {{store.distance}} km
        </text>
      </view>
    </view> -->

  </view>
  <view class="block_3 flex-y-center" style="margin-top: 4rpx;border: none;">门店详情</view>
  <view class="detail" style="padding:0 20rpx 0 20rpx;color: #707070;">
    <rich-text nodes="{{store.description}}"></rich-text>
    <!-- <template is="wxParse" data="{{wxParseData:detail.nodes}}"></template> -->
  </view>
</view>
<view style="height: 120rpx;"></view>
<view style="position: fixed;bottom: 0;width: 100%;padding: 10rpx;text-align: center;">
  <navigator url="/pages/user/pay_qrcode" hover-class="none">
    <button style="width: 80%;" class="weui-btn weui-btn_primary" id="js_btn" aria-role="button" bindtap="bind_code">出示付款码</button>
  </navigator>
</view>
<!-- <include src="/components/footer/footer"></include> -->