/* pages/store/list.wxss */
.weui-cell__hd {
  font-size: 0
}

.weui-cell__hd image {
  /* margin-right: 16px; */
  vertical-align: middle;
  /* width: 20px; */
  /* height: 20px */
}

/* 
image {
  height: 50px
} */

.distance_text {
  position: absolute;
  right: 10rpx;
  top: 30rpx;
  text-align: justify;
  display: inline-block;
  color: #888;
}


.store_icon {
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
}

.brand_icon {
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
}

.weui-cells .demo_badge_tips {
  font-size: 1rem;
  vertical-align: middle
}

.weui-cells .demo_badge_tips+.weui-badge {
  margin-left: 5px;
  margin-right: 5px
}

.demo_badge_cells .weui-cell__hd {
  position: relative;
  margin-right: 10px
}

.demo_badge_cells .weui-cell__hd image {
  width: 50px;
  display: block
}

.demo_badge_cells .weui-cell__hd .weui-badge {
  position: absolute;
  top: -.4em;
  right: -.4em
}

.demo_badge_cells .weui-cell__bd .demo_badge_title {
  vertical-align: middle
}

.demo_badge_cells .weui-cell__bd .demo_badge_title+.weui-badge {
  margin-left: 5px
}

.demo_badge_cells .weui-cell__bd .demo_badge_desc {
  font-size: 0.7647058823529411rem;
  color: #888
}

.icon {
  width: 0.8rem;
  height: 0.8rem
}



/* list.wxss */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  overflow: hidden;
  background: #fff;
  z-index: 10;
  border-top: 1rpx solid #E3E3E3;
}

.cat-list {
  white-space: nowrap;
  border-bottom: 1rpx solid #e3e3e3;
  background: #fff;
}

.cat-list .list-content {
  white-space: nowrap;
  /* height: 200rpx; */
}

.cat-list .cat-item {
  white-space: nowrap;
  display: inline-block !important;
  padding: 0 20rpx;
  height: 100%;
  text-align: center;
}

.cat-list .cat-item text {
  white-space: nowrap;
  border-bottom: 5rpx solid transparent;
  height: 100%;
  font-size: 14px;
  transform: translateY(1rpx);
  text-align: center;
  align-items: center;
  /*垂直居中 */
  justify-content: center;
  /* 水平居中 */
  color: #fff;
}

.cat-list .cat-item.active text {
  color: #ff4544;
  border-bottom-color: #ff4544;
}

.sub-cat-list {
  white-space: nowrap;
  border-bottom: 1rpx solid #e3e3e3;
  background: #fff;
}

.sub-cat-list scroll-view {
  height: 100rpx;
}

.sub-cat-list .list-content {
  white-space: nowrap;
  height: 100rpx;
  padding: 0 20rpx;
}

.sub-cat-list .cat-item {
  display: inline-block;
  height: 60rpx;
  padding: 0 20rpx;
  border: 1rpx solid #ff4544;
  margin: 20rpx;
  color: #ff4544;
  border-radius: 5rpx;
}

.sub-cat-list .cat-item text {
  height: 100%;
}

.sub-cat-list .cat-item.active {
  background: #ff4544;
  color: #fff;
}


.top-bar~.goods-list {
  padding-top: 106rpx;
}

.top-bar.height-bar~.goods-list {
  padding-top: 206rpx;
}