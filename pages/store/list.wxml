<page-meta root-font-size="system" />
<import src="/common/tabbar/tabbar.wxml" />
<template wx:if="{{show_tab_bar==1}}" is="tabbar" data="{{tabbar}}" />
<view class="page" data-weui-theme="{{theme}}" data-weui-mode="{{mode}}">
  <!-- <view class="page__hd">
    <view class="page__title">List</view>
    <view class="page__desc">门店列表</view>
  </view> -->
  <view class="page__bd">
    <view class="cat-list" wx:if="{{brand_list.length>0}}" style="background-color: #d3c095;">
      <scroll-view scroll-x="true">
        <view class="list-content">
          <view class="cat-item {{'' == brand_guid ?'active':''}}" bindtap="allSubCatClick">
            <image src="/images/brand.png" class="brand_icon"></image>
            <text class="flex-y-center">全部品牌</text>
          </view>
          <view style="padding-top: 20rpx;" class="cat-item {{item.guid == brand_guid ?'active':''}}" wx:for="{{brand_list}}" bindtap="subCatClick" data-index="{{index}}" data-brand_guid="{{item.guid}}">
            <image src="{{item.icon}}" class="brand_icon"></image>
            <text class="flex-y-center">{{item.name}}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="weui-cells demo_badge_cells">
      <navigator hover-class="none" wx:for="{{list}}" wx:for-item="store" url="/pages/store/detail?guid={{store.guid}}" class="weui-cell weui-cell_access">

        <view class="weui-flex">
          <view>
            <image class="store_icon" src="{{store.icon}}" alt></image>
          </view>
          <view class="weui-flex__item">
            <view class="weui-cell__bd" style="padding-left: 20rpx;" aria-hidden="true">
              <view class="weui-flex">
                <text> {{store.store_name}} </text>
                <view class="distance_text" wx:if="{{store.distance>=0 && store.latitude>0 && store.longitude>0}}">
                  <text>{{store.distance_text}}</text>
                </view>
              </view>
              <view class="demo_badge_desc" wx:if="{{store.business_time}}">
                <image src="/images/icon-time.png" class="icon"></image>
                {{store.business_time}}
              </view>
              <view class="demo_badge_desc">
                <image src="/images/icon-dingwei.png" class="icon"></image>
                {{store.address_info}}
              </view>
            </view>

          </view>


        </view>


      </navigator>
    </view>
    <!-- <view class=" weui-cells__title">带图标、说明、跳转的列表项</view> -->
    <!-- <view class="weui-cells weui-cells_after-title">
      <navigator wx:for="{{list}}" wx:for-item="store" url="/pages/store/detail?guid={{store.guid}}" class="weui-cell weui-cell_access" hover-class="weui-cell_active">
        <view class="weui-cell__hd" aria-hidden="true" id="js_cell_itl1_hd">
          <image src="{{store.pic}}"></image>
        </view>
        <view class="weui-cell__bd" aria-hidden="true" id="js_cell_itl1_bd">{{store.store_name}}</view>
        <view aria-hidden="true" id="js_cell_itl1_ft" class="weui-cell__ft weui-cell__ft_in-access">{{store.address}}</view>
      </navigator>
    </view> -->
  </view>
  <include src="/common/padding/top_for_show_tab_bar.wxml" />
</view>