/* pages/store/detail.wxss */

.banner {
  width: 100%;
  height: 360rpx;
}

.banner image {
  height: 360rpx;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.line {
  align-items: center;
  /** 垂直居中*/
  display: inline-flex;
  justify-content: center;
  /* float: left; */
  /* margin-left: 20rpx; */
  /** flex 属性， 水平居中**/
}


.block {
  width: 100%;
  background-color: #fff;
  border-bottom: 1rpx solid #e2e2e2;
  color: #353535;
}

.block:last-child {
  border: 0;
}

.block_1 {
  padding: 24rpx 24rpx 30rpx 24rpx;
}

.title {
  color: #353535;
  font-size: 13pt;
}

.middle {
  margin: 24rpx 0 18rpx 0;
}

.block_2 {
  padding: 34rpx 0 34rpx 24rpx;
}

.shop-img {
  width: 120rpx;
  padding: 16rpx 0;
}

.shop-v {
  width: 100%;
  height: 40rpx;
  border-left: 1rpx solid #e2e2e2;
}

.shop-img image {
  width: 40rpx;
  height: 40rpx;
}

.block_3 {
  width: 100%;
  height: 72rpx;
  padding: 0 24rpx;
  color: #353535;
  background-color: #fff;
  margin-top: 20rpx;
  border-bottom: 1rpx solid #e2e2e2;
}

.detail {
  padding: 24rpx;
  background-color: #fff;
}

.icon_container {

  align-items: center;
  display: flex;
  justify-content: center;
  background-color: #fff;
}

.store_icon {
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
  vertical-align: center;
}