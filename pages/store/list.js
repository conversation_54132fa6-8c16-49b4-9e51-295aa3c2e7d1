// pages/store/list.js
var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    industry_guid: '',
    brand_guid: '',
    brand_list: [],
    latitude: 0,
    longitude: 0,
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let page = this;
    app.pageOnLoad(page, options);
    page.setData(options);
    page.getBrandList();
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        console.log(res);
        const latitude = res.latitude;
        const longitude = res.longitude;
        const speed = res.speed;
        const accuracy = res.accuracy;
        page.setData({
          latitude: res.latitude,
          longitude: res.longitude,
        })
        page.getData();
      },
      fail(res) {
        console.log(res);
        page.getData();
      }
    })
  },

  allSubCatClick: function (e) {
    var page = this;
    page.setData({
      brand_guid: '',
    });
    page.getData();

  },
  subCatClick: function (e) {
    var page = this;
    var brand_guid = e.currentTarget.dataset.brand_guid;
    page.setData({
      brand_guid: brand_guid,
    });
    page.getData();
    return;
    var parent_index = e.currentTarget.dataset.parentIndex;
    var cat_list = page.data.cat_list;
    for (var i in cat_list) {
      for (var j in cat_list[i].list) {
        if (i == parent_index && j == index) {
          cat_list[i].list[j].checked = true;
          cat_guid = cat_list[i].list[j].id;
        } else {
          cat_list[i].list[j].checked = false;
        }
      }
    }
    page.setData({
      cat_list: cat_list,
      cat_guid: cat_guid,
    });
    page.reloadGoodsList();
  },
  getBrandList() {
    let page = this;
    app.request({
      url: app.api.brand.list,
      data: {},
      success: function (result) {
        console.log(result);
        page.setData({
          brand_list: result.data
        })
      },
    });
  },
  getData() {
    let page = this;
    console.log('page.data.latitude');
    console.log(page.data.latitude);
    app.request({
      url: app.api.store.list,
      data: {
        industry_guid: page.data.industry_guid,
        brand_guid: page.data.brand_guid,
        lat: page.data.latitude,
        lon: page.data.longitude,
      },
      success: function (result) {
        console.log(result);
        page.setData({
          list: result.data
        })
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})