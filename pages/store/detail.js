// pages/store/detail.js
var app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    store: {},
    guid: '',
    latitude: 0,
    longitude: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let page = this;
    app.pageOnLoad(page, options);
    page.setData(options);
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        console.log(res);
        const latitude = res.latitude;
        const longitude = res.longitude;
        const speed = res.speed;
        const accuracy = res.accuracy;
        page.setData({
          latitude: res.latitude,
          longitude: res.longitude,
        })
        page.getData();
      },
      fail(res) {
        console.log(res);
        page.getData();
      }
    })
  },
  makePhoneCall(e) {
    let phoneNumber = e.currentTarget.dataset.phone;
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: (res) => {},
      fail: (res) => {},
      complete: (res) => {},
    })
  },
  openLocation() {
    if (this.data.store.latitude && this.data.store.longitude) {
      wx.openLocation({
        latitude: parseFloat(this.data.store.latitude),
        longitude: parseFloat(this.data.store.longitude),
        scale: 16,
        name: this.data.store.store_name,
        address: this.data.store.address_info,
      })
    }
  },
  getData() {
    let page = this;
    app.request({
      url: app.api.store.detail,
      data: {
        guid: page.data.guid,
        lat: page.data.latitude,
        lon: page.data.longitude,
      },
      success: function (result) {
        console.log(result);
        page.setData({
          store: result.data
        })
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})