<!--index.wxml-->
<import src="/common/tabbar/tabbar.wxml" />
<template is="tabbar" data="{{tabbar}}" />
<view class="body after-navber">
  <block wx:for="{{layout}}" wx:for-item="item" wx:for-index="index">
    <!--商城公告 开始-->
    <block wx:if="{{item.type=='notice' && item.data.title !=''}}">
      <view class='notice flex-row' bindtap='showNotice'>
        <view class='flex-grow-0 flex-y-center notice-a'>
          <image src='/images/icon-notice.png' style='width:36rpx;height:36rpx;margin-right:24rpx;'></image>
          <view>公告：</view>
        </view>
        <view class='flex-grow-1 notice-box'>
          <view class='notice-content'>
            <view style='display: inline-block;'>{{item.data.title}}</view>
          </view>
        </view>
        <view class='flex-grow-0 flex-y-center notice-b'>
          <image src='/images/icon-notice-jiantou.png' style='width:10rpx;height:18rpx;margin-left:32rpx;'></image>
        </view>
      </view>
      <view class='notice-modal flex-y-center flex-x-center {{show_notice?"":"hidden"}}'>
        <view style='margin-top:-200rpx;'>
          <image class='notice-img' src='/images/icon-notice-title.png'></image>
          <view class='w-100 notice-body'>
            <scroll-view scroll-y style='color:#666;max-height:400rpx;'>
              <rich-text nodes="{{item.data.content ?  item.data.content : item.data.title  }}"></rich-text>
            </scroll-view>
            <view class='flex-x-center'>
              <view class='notice-btn flex-x-center flex-y-center' bindtap='closeNotice'>我知道了</view>
            </view>
          </view>
        </view>
      </view>
    </block>
    <!--商城公告 结束-->



    <!-- Banner 开始 -->
    <view wx:if="{{item.type=='banner_list' && item.data && item.data.length>0}}">
      <swipers banner_list="{{item.data}}"> </swipers>
    </view>
    <!-- Banner 结束 -->

    <!-- 搜索栏 开始 -->
    <view wx:elif="{{item.type=='search'}}">
      <view class="search-block">
        <navigator hover-class="none" url="/pages/search/search">
          <image src="/images/icon-search.png" />
          <text>搜索</text>
        </navigator>
      </view>
    </view>
    <!-- 搜索栏 结束 -->


    <!-- 导航图标 开始 -->
    <view wx:elif="{{item.type=='nav' && item.data && item.data.length>0}}">
      <swiper style="height: {{swiperHeight}}" class="nav" autoplay="true" interval="5000" duration="300" indicator-dots="true" circular="true">
        <block wx:for="{{item.data}}">
          <swiper-item>
            <view class="cat-item-swiper nav-icon-list flex-row" wx:if="{{item && item.length>0}}">
              <view class="flex-grow-0 nav-icon{{nav_count==1?'-1':''}}" wx:for="{{item}}">
                <navigator hover-class="none" bindtap="navigatorClick" data-url="{{item.url}}" data-open_type="{{item.open_type}}" url="{{item.url}}" open-type="{{item.open_type}}">
                  <image src="{{item.mini_pic}}"></image>
                  <view>{{item.name}}</view>
                </navigator>
              </view>
            </view>
          </swiper-item>
        </block>
      </swiper>
    </view>
    <!-- 导航图标 结束 -->

    <!-- 专题 开始 -->
    <view wx:elif="{{item.type=='topic_list' && item.data && item.data.length>0}}">
      <navigator class="topic-bar" url="/pages/topic-list/topic-list">
        <view class="flex-row">
          <view class="flex-grow-0 flex-y-center">
            <image class="bar-icon" src="/images/icon-topic.png"></image>
          </view>
          <view class="flex-grow-1">
            <view wx:for="{{item.data}}" wx:if="{{index < 2}}" class="flex-row">
              <view class="flex-grow-0 flex-y-center">
                <view class="topic-tag">{{item.tag ? item.tag : '热门'}}</view>
              </view>
              <view class="flex-grow-1 topic-title">{{item.title}}</view>
            </view>
          </view>
        </view>
      </navigator>
    </view>
    <!-- 专题 结束 -->

    <!-- 全部商品分类 开始 -->
    <view wx:elif="{{item.type=='cat_list' && item.data && item.data.length>0}}">
      <block wx:for="{{item.data}}" wx:for-index="cat_index" wx:for-item="cat">
        <view wx:if="{{cat.goods_list.length>0}}" style="background: #fff;margin-bottom: 10rpx;width:100%;overflow-x: hidden;">

          <navigator hover-class="none" url="/pages/list/list?{{item.attr.key}}={{cat.guid}}" wx:if="{{cat.pic}}">
            <image style="width: 100%;padding: 0;margin: 0;vertical-align:top;" src="{{cat.pic}}" mode="widthFix"></image>
          </navigator>

          <view class="title-bar flex-y-center flex-x-center" wx:if="{{!cat.pic}}">
            <view class="title-line"></view>
            <view class="title flex-y-center">
              <image wx:if="{{cat.mini_pic}}" src="{{cat.mini_pic}}" mode="aspectFill"></image>
              <text>{{cat.name}}</text>
            </view>
            <view class="title-line"></view>
            <navigator hover-class="none" class="flex-y-center" bindtap="hideGetCoupon" url="/pages/list/list?{{item.attr.key}}={{cat.guid}}">
              <text style="font-size: 22rpx;">更多</text>
              <image src="/images/icon-jiantou-r.png" />
            </navigator>
          </view>

          <view class="goods-list flex-row goods-list-cols-{{cat_goods_cols}}">
            <view class="flex-grow-0" wx:for="{{cat.goods_list}}" wx:for-index="goods_index" wx:for-item="goods">
              <navigator hover-class="none" class="goods-item" url="/pages/index/goods_detail?guid={{goods.guid}}">
                <image src="{{goods.pic}}" mode="aspectFill" />
                <text class="text-more-2 {{cat_goods_cols == 3?'fs-sm':''}}" style='padding:8rpx 20rpx;line-height:1.4;font-size: 28rpx;'>{{goods.name}}</text>
                <text class="goods-price" wx:if='{{cat_goods_cols == 3}}'>￥{{goods.price}}</text>
                <view class='flex-row' style='padding:10rpx 20rpx' wx:if='{{cat_goods_cols == 2}}'>
                  <view class='flex-grow-1' style='color:#ff334b' wx:if='{{goods.price > 0}}'>
                    <text style="font-size: 24rpx;">￥</text>
                    <text style="font-size: 36rpx;">{{goods.price}} </text>
                    <text wx:if='{{goods.price<goods.original_price}}' style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.original_price}}</text>
                  </view>
                  <view wx:if="{{goods.sales>0}}" class='fs-sm' style='color:#999;'>已售{{goods.sales}}件</view>
                </view>
              </navigator>
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 全部商品分类 结束 -->


    <!-- 全部卡券分类 开始 -->
    <view wx:elif="{{item.type=='coupon_cat_list' && item.data && item.data.length>0}}">
      <block wx:for="{{item.data}}" wx:for-index="cat_index" wx:for-item="cat">
        <view wx:if="{{cat.coupon_list.length>0}}" style="background: #fff;margin-bottom: 10rpx;width:100%;overflow-x: hidden;">
          <navigator hover-class="none" url="{{cat.url}}" wx:if="{{cat.pic}}">
            <image style="width: 100%;padding: 0;margin: 0;vertical-align:top;" src="{{cat.pic}}" mode="widthFix"></image>
          </navigator>
          <view class="title-bar flex-y-center flex-x-center" wx:if="{{!cat.pic}}">
            <view class="title-line"></view>
            <view class="title flex-y-center">
              <image wx:if="{{cat.mini_pic}}" src="{{cat.mini_pic}}" mode="aspectFill"></image>
              <text>{{cat.name}}</text>
            </view>
            <view class="title-line"></view>
            <!-- <navigator hover-class="none" class="flex-y-center" bindtap="hideGetCoupon" url="/pages/list/list?cat_guid={{cat.guid}}">
              <text>查看更多</text>
              <image src="/images/icon-jiantou-r.png" />
            </navigator> -->

          </view>
          <view class="goods-list flex-row goods-list-cols-{{cat_goods_cols}}">
            <view class="flex-grow-0" wx:for="{{cat.coupon_list}}" wx:for-index="goods_index" wx:for-item="goods">
              <navigator hover-class="none" class="goods-item" url="/pages/index/coupon_detail?guid={{goods.guid}}">
                <image src="{{goods.pic}}" mode="aspectFill" />
                <text class="text-more-2 {{cat_goods_cols == 3?'fs-sm':''}}" style='padding:8rpx 20rpx;line-height:1.4;'>{{goods.name}}</text>
                <text class="goods-price" wx:if='{{cat_goods_cols == 3}}'>￥{{goods.discount_selling_price}}</text>
                <view class='flex-row' style='padding:10rpx 20rpx' wx:if='{{cat_goods_cols == 2}}'>
                  <view class='flex-grow-1' style='color:#ff334b'>

                    <text style="font-size: 24rpx;">￥</text>
                    <text style="font-size: 36rpx;">{{goods.discount_selling_price}} </text>
                    <text wx:if='{{goods.discount_selling_price<goods.selling_original_price}}' style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.selling_original_price}}</text>

                    <!-- <text wx:if="{{goods.discount_selling_price<goods.selling_price}}" style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.selling_price}}</text> -->
                  </view>
                  <!-- <view class='fs-sm' style='color:#999;'>{{goods.sales}}</view> -->
                </view>
              </navigator>
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 全部卡券分类 结束 -->

    <!-- 拆分的分类 开始 -->
    <view wx:elif="{{item.name=='single_cat'}}">
      <block wx:for="{{cat_list}}" wx:for-index="cat_index" wx:for-item="cat">
        <block wx:if="{{item.cat_guid==cat.guid}}">
          <view wx:if="{{cat.goods_list.length>0}}" style="background: #fff;margin-bottom: 10rpx;width:100%;overflow-x: hidden;">
            <view class="title-bar flex-y-center flex-x-center">
              <view class="title-line"></view>
              <view class="title flex-y-center">
                <image wx:if="{{cat.mini_pic}}" src="{{cat.mini_pic}}" mode="aspectFill"></image>
                <text>{{cat.name}}</text>
              </view>
              <view class="title-line"></view>
              <navigator hover-class="none" class="flex-y-center" bindtap="hideGetCoupon" url="/pages/list/list?cat_guid={{cat.guid}}">
                <text>更多</text>
                <image src="/images/icon-jiantou-r.png" />
              </navigator>
            </view>
            <view class="goods-list flex-row goods-list-cols-{{cat_goods_cols}}">
              <view class="flex-grow-0" wx:for="{{cat.goods_list}}" wx:for-index="goods_index" wx:for-item="goods">
                <navigator hover-class="none" class="goods-item" url="/pages/index/goods_detail?guid={{goods.guid}}">
                  <image src="{{goods.pic_url}}" mode="aspectFill" />
                  <text class="text-more-2 {{cat_goods_cols == 3?'fs-sm':''}}" style='padding:0 20rpx;height:2.8em;line-height:1.4;'>{{goods.name}}
                  </text>
                  <text class="goods-price" wx:if='{{cat_goods_cols == 3}}'>￥{{goods.price}}</text>
                  <view class='flex-row' style='padding:10rpx 20rpx' wx:if='{{cat_goods_cols == 2}}'>
                    <view class='flex-grow-1' style='color:#ff334b'>
                      <text style="font-size: 24rpx;">￥</text>
                      <text style="font-size: 36rpx;">{{goods.price}} </text>
                    </view>
                    <view class='fs-sm' style='color:#999;'>已售{{goods.sales}}</view>
                  </view>
                </navigator>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>
    <!-- 拆分的分类 结束 -->

    <!-- 富文本板块开始 -->
    <block wx:elif="{{item.type=='rich_text' && item.data}}">
      <view style="background-color: #fff;max-width: 100%;">
        <rich-text bindtap="previewImage" nodes="{{item.data}}"></rich-text>
      </view>
    </block>

    <!-- 富文本板块结束 -->

    <!-- 图片链接板块开始 -->
    <block wx:elif="{{item.type=='image_url_list'}}">
      <block wx:for="{{item.data}}">
        <navigator hover-class="none" url="{{item.path}}" style="display: inline;">
          <image style="width: {{item.width ? item.width : '100%'}};padding: 0;margin: 0;vertical-align:top;" src="{{item.img_url}}" mode="widthFix"></image>
        </navigator>
      </block>
    </block>
    <!-- 图片链接板块结束 -->

    <!-- 图片链接板块开始 -->
    <block wx:elif="{{item.type=='video_list'}}">
      <block wx:for="{{item.data}}">
        <block wx:if="{{item.video_url}}">
          <view class="video-wrap">
            <video class="video" id="video_player" src="{{item.video_url}}" show-center-play-btn="{{false}}" show-play-btn="{{false}}" enable-play-gesture bindplay="triggerPlay" bindpause="triggerPause" bindcontrolstoggle="controlsToggle" bindtouchmove="touchMove" bindtouchend="touchEnd" controls="{{false}}" show-fullscreen-btn="{{false}}">
              <view class="control-icon" hidden="{{!controlShow}}">
                <image hidden="{{isPlay}}" src="/images/icon-play.png" bindtap="handlePlay"></image>
                <image hidden="{{!isPlay}}" src="/images/icon-pause.png" bindtap="handlePause"></image>
              </view>
            </video>
          </view>


          <!-- <view style='width:750rpx;height:700rpx;position:absolute;top:0;left:0;'> -->
          <!-- <image class="play {{img_hide}}" src="/images/video-play.png" data-url="{{item.video_url}}" bindtap="play"></image> -->
          <!-- </view> -->
        </block>
        <!-- <image bindtap="onGoodsImageClick" data-index="{{index}}" src="{{item.img_url}}" class='image-view' mode="heightFix" /> -->
      </block>
    </block>
    <!-- 图片链接板块结束 -->




    <!-- 秒杀板块 开始 未启用 -->
    <block wx:elif="{{item.name=='seckill'}}">
      <view class="user-block">
        <view class="flex-row seckill-header" style="padding: 0 20rpx;height: 80rpx;background: #fff;margin-bottom: 4rpx">
          <view class="flex-grow-1 flex-row flex-y-center">
            <image src="/images/icon-seckill.png" style="width: 34rpx;height: 34rpx;margin-right: 12rpx"></image>
            <view style="font-size: 12pt;color: #ff8b57;margin-right: 16rpx">整点秒杀</view>
            <view style="margin-right: 24rpx">{{seckill.name}}</view>

            <view class="flex-y-center seckill-time-item" style="width: 32rpx;height: 34rpx">
              <image src="/images/icon-time-bg.png" style="width: 100%;height: 100%"></image>
              <view class="flex-y-center flex-x-center">{{seckill.times.h?seckill.times.h:'--'}}</view>
            </view>

            <image src="/images/icon-time-split.png" class="seckill-item-split" style="width: 20rpx;height: 34rpx"></image>

            <view class="flex-y-center seckill-time-item" style="width: 32rpx;height: 34rpx">
              <image src="/images/icon-time-bg.png" style="width: 100%;height: 100%"></image>
              <view class="flex-y-center flex-x-center">{{seckill.times.m?seckill.times.m:'--'}}</view>
            </view>

            <image src="/images/icon-time-split.png" class="seckill-item-split" style="width: 20rpx;height: 34rpx"></image>

            <view class="flex-y-center seckill-time-item" style="width: 32rpx;height: 34rpx">
              <image src="/images/icon-time-bg.png" style="width: 100%;height: 100%"></image>
              <view class="flex-y-center flex-x-center">{{seckill.times.s?seckill.times.s:'--'}}</view>
            </view>

          </view>
          <navigator url="/pages/seckill/seckill" class="flex-grow-0 flex-row flex-y-center">
            <view style="margin-right: 14rpx">更多</view>
            <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx"></image>
          </navigator>
        </view>
        <scroll-view scroll-x="true" style="background: #fff">
          <view class="flex-row seckill-goods-list">
            <navigator hover-class="none" class="flex-grow-0 " style="background: #fff;padding-bottom: 14rpx;" wx:for="{{seckill.goods_list}}" url="/pages/index/goods_detail?id={{item.id}}">
              <image src="{{item.pic}}" style="width: 168rpx;height: 168rpx;display: block;margin-bottom: 8rpx"></image>
              <view style="color: #ff4544;line-height: 1;text-overflow: ellipsis;overflow: hidden;white-space: nowrap" class="flex-row flex-y-center flex-x-center">
                <view style="font-size: 9pt">￥</view>
                <view>{{item.seckill_price}}</view>
              </view>
              <view style="color: #aaa;line-height: 1;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;text-decoration: line-through;font-size: 9pt" class="flex-y-center flex-x-center">
                <view>￥</view>
                <view>{{item.price}}</view>
              </view>
            </navigator>
          </view>
        </scroll-view>
      </view>
    </block>
    <!-- 秒杀板块 结束 -->

    <!-- 拼团板块 开始 未启用 -->
    <view class="user-block pintuan-block" wx:elif="{{item.name=='pintuan'}}" style="background: #fff7f5;">
      <view class="flex-row flex-y-center pintuan-header" style="height: 80rpx;padding: 0 20rpx;">
        <view class="flex-grow-1 flex-y-bottom">
          <image src="/images/icon-pintuan-text.png" style="width: 88rpx;height: 40rpx;margin-right: 20rpx"></image>
          <view style="color: #aaa;font-size: 9pt;line-height: 1.2">限量拼团，每日必逛</view>
        </view>
        <view class="flex-grow-0">
          <navigator hover-class="none" url="/pages/pt/index/index" class="flex-y-center">
            <view style="margin-right: 14rpx">更多</view>
            <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx"></image>
          </navigator>
        </view>
      </view>
      <scroll-view scroll-x="true">
        <view class="flex-row pintuan-goods-list" style="padding: 0 20rpx 20rpx">
          <navigator hover-class="none" wx:for="{{pintuan.goods_list}}" url="/pages/pt/details/details?gid={{item.id}}" class="flex-grow-0" style="background: #fff;width: 260rpx;">
            <view style="padding:24rpx 20rpx;">
              <image src="{{item.pic}}" style="width: 220rpx;height: 220rpx;margin-bottom: 12rpx;"></image>
              <view style="display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;font-size: 9pt;margin-bottom: 16rpx;height: 75rpx">
                {{item.name}}
              </view>
              <view style="color:#ff4544;line-height: 1">
                <text style="font-size: 9pt">￥</text>
                <text style="font-weight: bold">{{item.price}}</text>
              </view>
            </view>
            <view style="font-size: 9pt;color: #aaa;padding:10rpx 20rpx;border-top: 1rpx solid #eee">
              已拼{{item.sale_num}}件
            </view>
          </navigator>
        </view>
      </scroll-view>
    </view>
    <!-- 拼团板块 结束 -->

    <!-- 自定义板块 开始 未启用 -->
    <view wx:else>
      <block wx:for="{{block_list}}" wx:for-item="block" wx:if="{{item.block_id==block.id}}">

        <block wx:if="{{block.data.pic_list.length==1}}">
          <view class="user-block" style="height: auto">
            <navigator style="display:block;font-size: 0;width: 100%;height: auto;" bindtap="navigatorClick" data-url="{{block.data.pic_list[0].url}}" data-open_type="{{block.data.pic_list[0].open_type}}" url="{{block.data.pic_list[0].url}}">
              <image src="{{block.data.pic_list[0].pic_url}}" mode="widthFix" style="width: 100%;"></image>
            </navigator>
          </view>
        </block>

        <block wx:if="{{block.data.pic_list.length==2}}">
          <view class="flex-row user-block" style="height: 360rpx;">
            <view class="flex-grow-0" style="width: 300rpx;height: 100%; border-right: 5rpx solid transparent">
              <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[0].url}}" data-open_type="{{block.data.pic_list[0].open_type}}" url="{{block.data.pic_list[0].url}}">
                <image src="{{block.data.pic_list[0].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
              </navigator>
            </view>
            <view class="flex-grow-1" style="height: 100%;">
              <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[1].url}}" data-open_type="{{block.data.pic_list[1].open_type}}" url="{{block.data.pic_list[1].url}}">
                <image src="{{block.data.pic_list[1].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
              </navigator>
            </view>
          </view>
        </block>
        <block wx:if="{{block.data.pic_list.length==3}}">
          <view class="flex-row user-block" style="height: 360rpx;">
            <view class="flex-grow-0" style="width: 300rpx;height: 100%;border-right: 5rpx solid transparent">
              <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[0].url}}" data-open_type="{{block.data.pic_list[0].open_type}}" url="{{block.data.pic_list[0].url}}">
                <image src="{{block.data.pic_list[0].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
              </navigator>
            </view>
            <view class="flex-grow-1 flex-col" style="height: 100%;">
              <view class="flex-grow-1" style="height: 50%;border-bottom: 5rpx solid transparent">
                <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[1].url}}" data-open_type="{{block.data.pic_list[1].open_type}}" url="{{block.data.pic_list[1].url}}">
                  <image src="{{block.data.pic_list[1].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
                </navigator>
              </view>
              <view class="flex-grow-1" style="height: 50%">
                <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[2].url}}" data-open_type="{{block.data.pic_list[2].open_type}}" url="{{block.data.pic_list[2].url}}">
                  <image src="{{block.data.pic_list[2].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
                </navigator>
              </view>
            </view>
          </view>
        </block>
        <block wx:if="{{block.data.pic_list.length==4}}">
          <view class="flex-row user-block" style="height: 360rpx;">
            <view class="flex-grow-0" style="width: 300rpx;height: 100%;border-right: 5rpx solid transparent">
              <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[0].url}}" data-open_type="{{block.data.pic_list[0].open_type}}" url="{{block.data.pic_list[0].url}}">
                <image src="{{block.data.pic_list[0].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
              </navigator>
            </view>
            <view class="flex-grow-1 flex-col" style="height: 100%">
              <view class="flex-grow-1" style="height: 50%;border-bottom: 5rpx solid transparent">
                <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[1].url}}" data-open_type="{{block.data.pic_list[1].open_type}}" url="{{block.data.pic_list[1].url}}">
                  <image src="{{block.data.pic_list[1].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
                </navigator>
              </view>
              <view class="flex-grow-1 flex-row" style="height: 50%">
                <view class="flex-grow-1" style="height: 100%;border-right: 5rpx solid transparent">
                  <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[2].url}}" data-open_type="{{block.data.pic_list[2].open_type}}" url="{{block.data.pic_list[2].url}}">
                    <image src="{{block.data.pic_list[2].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
                  </navigator>
                </view>
                <view class="flex-grow-1" style="height: 100%;">
                  <navigator style="display:block;font-size: 0;width: 100%;height: 100%;" bindtap="navigatorClick" data-url="{{block.data.pic_list[3].url}}" data-open_type="{{block.data.pic_list[3].open_type}}" url="{{block.data.pic_list[3].url}}">
                    <image src="{{block.data.pic_list[3].pic_url}}" style="width: 100%;height: 100%;" mode="aspectFill"></image>
                  </navigator>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>
    <!-- 自定义板块 结束 -->
  </block>
  <!-- <view class='modal flex-row {{hide}}' bindtap='close'>
    <view class='flex-y-center' style="width: 100%;height: 100%;">
      <video bindended='endAction' controls="{{false}}" show-fullscreen-btn="{{false}}" show-play-btn="{{false}}" object-fit="cover" style="height: 100%;width: 100%;" src='{{url}}' id="video" autoplay="true"></video>
    </view>
  </view> -->
  <include src="/common/get-coupon/get-coupon.wxml" />
  <block wx:if="{{store.show_customer_service && store.show_customer_service==1}}">
    <include src="/common/float-icon/float-icon.wxml" />
  </block>
</view>