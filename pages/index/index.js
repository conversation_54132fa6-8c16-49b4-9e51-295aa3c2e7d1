//index.js
//获取应用实例
const app = getApp()
Page({
  data: {
    text: "This is page data.",
    layout: [],
    previewImage: [],
    swiperHeight: '',
    isPlay: false,
    controlShow: false
  },
  getHeight(class_name) {
    //根据不同的类名，去获取不同的内容高度
    // 获取元素高度
    try {
      let query = wx.createSelectorQuery();
      //选择id
      let that = this;
      query.select('.' + class_name).boundingClientRect(function (rect) {
        if (rect) {
          that.setData({
            swiperHeight: rect.height + 20 + 'px'
          })
        }
      }).exec();
    } catch (e) {
      console.log(e);
      console.log('自动调整高度失败');
    }
  },
  get_pic_list() {
    // 将富文本赋值线imageNode。   
    let that = this;
    var previewImage = [];
    for (let key in that.data.layout) {
      let layout = that.data.layout[key];
      if (layout.type == 'rich_text') {
        var imageNode = layout.data;
        // 是否存在图片文件资源判断。   
        if (imageNode.indexOf("src") >= 0) {
          // 定义一个空数组。       
          // 将imageNode进t地替换，并使用push()添加一个或多个元素，并返回新的长度。  
          imageNode = imageNode.replace(/]*src=['"]([^'"]+)[^>]*>/gi, function (match, capture) {
            previewImage.push(capture);
          });
        }
      }
    }
    if (previewImage.length > 0) {
      that.setData({
        previewImage: previewImage
      });
    }
  },
  //预览图片，放大预览
  previewImage(e) {
    // 定义src为一个空数组   
    var src = [];
    // 将previewImage中的图片资源数据进行遍历。 
    for (var i = 0; i < this.data.previewImage.length; i++) {
      src[i] = this.data.previewImage[i];
    }
    // 直接调e和用wx.previewImage  
    wx.previewImage({
      current: src[0],
      // 第一张图片。
      urls: src
    })
  },
  initData: function (data) {
    this.setData(data);
    this.getHeight('cat-item-swiper');
    this.get_pic_list();
    wx.setNavigationBarTitle({
      title: data.title
    })
  },
  /**
   * 加载页面数据
   */
  loadData: function (options) {
    var page = this;
    var pages_index_index = app.cache.get('pages_index_index');
    if (pages_index_index) {
      page.initData(pages_index_index);
      return;
    }
    let extConfig = wx.getExtConfigSync ? wx.getExtConfigSync() : {};
    app.request({
      url: app.api.home.index,
      data: {
        bid: extConfig.bid,
        appid: wx.getAccountInfoSync().miniProgram.appId,
        mini_program_ext_version: extConfig.version,
        mini_program_env_version: wx.getAccountInfoSync().miniProgram.envVersion,
        mini_program_version: wx.getAccountInfoSync().miniProgram.version,
        from: 'weapp',
        type: 2, // 新版获取二维数组
      },
      success: function (result) {
        if (result.data.cache_time > 0) {
          app.cache.set('pages_index_index', result.data, 60);
        }
        page.initData(result.data);
      },
      complete: function () {
        wx.stopPullDownRefresh();
      }
    });
  },
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    app.editTabBar();
    this.loadData(options);
    // var id = decodeURIComponent(options.id);
    // Do some initialize when page load.
    // 查看是否授权
    wx.getSetting({
      success: function (res) {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称
          wx.getUserInfo({
            success: function (res) {
              console.log('已经授权，可以直接调用 getUserInfo 获取头像昵称');
              //app.bindGetUserInfo(res, that);
            }
          })
        }
      }
    })
  },
  onReady: function () {
    // Do something when page ready.
  },
  onShow: function () {
    // Do something when page show.
  },
  onHide: function () {
    // Do something when page hide.
  },
  onUnload: function () {
    // Do something when page close.
  },
  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },
  onReachBottom: function () {
    // Do something when page reach bottom.
  },
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
    // return custom share data when user share.
  },
  onPageScroll: function () {
    // Do something when page scroll
  },
  onTabItemTap(item) {
    console.log(item.index)
    console.log(item.pagePath)
    console.log(item.text)
  },
  // Event handler.
  viewTap: function () {
    this.setData({
      text: 'Set some data for updating view.'
    }, function () {
      // this is setData callback
    })
  },
  customData: {
    hi: 'MINA'
  },
  bindGetUserInfo: function (e) {
    console.log(e.detail);
    app.bindGetUserInfo(e.detail, this);
  },
  getPhoneNumber: function (e) {
    console.log(e.detail.errMsg)
    console.log(e.detail.iv)
    console.log(e.detail.encryptedData)
    if (e.detail.errMsg == 'getPhoneNumber:fail user deny') {
      wx.showModal({
        title: '提示',
        showCancel: false,
        content: '未授权',
        success: function (res) {}
      })
    } else {
      console.log(e.detail.iv)
      app.request({
        url: app.api.passport.get_phone_number,
        data: {
          encrypted_data: e.detail.encryptedData,
          iv: e.detail.iv,
        },
        success: function (res) {
          wx.hideLoading();
          console.log(res)
        }
      })
    }
  },
  onShareTimeline: function () {
    var page = this;
    return page.data.share_obj;
  },
  notice: function () {
    var page = this;
    var notice = page.data.notice;
    if (notice == undefined) {
      return;
    }
    var length = notice.length * 14;
    return;
    var left = 0;
    var right = 260;
    var new_width = width * (page.data.x) / 375;
    console.log(length)
    if (length < new_width) {
      return;
    }
    int = setInterval(function () {
      // if (left + new_width >= length) {
      //     left = 0;
      // } else {
      //     left += 10;
      // }
      // page.setData({
      //     left: -left
      // });

      left += 2;
      if (left + new_width >= length) {
        var l = left + new_width;
        right -= 2;
        page.setData({
          show_second: true,
        });
        if (right <= 0) {
          left = 0;
          right = 260;
          page.setData({
            show_second: false,
          });
        }
      }
      page.setData({
        left: -left,
        right: right
      });
    }, 250);
  },
  showNotice: function () {
    this.setData({
      show_notice: true
    });
  },
  closeNotice: function () {
    this.setData({
      show_notice: false
    });
  },
  // 播放
  handlePlay() {
    this.videoContext = wx.createVideoContext('video_player')
    this.videoContext.play()
  },
  // 暂停
  handlePause() {
    this.videoContext.pause()
  },
  // 触发播放、暂停
  triggerPlay() {
    this.setData({
      isPlay: true
    })
  },
  triggerPause() {
    this.setData({
      isPlay: false
    })
  },
  // 跟 controls 显隐同步
  controlsToggle(e) {
    const {
      show
    } = e.detail
    this.setData({
      controlShow: show
    })
  },
  // 滑动进度时，隐藏播放按钮，避免遮挡
  touchMove() {
    this.setData({
      controlShow: false
    })
  },
  touchEnd() {
    this.setData({
      controlShow: true
    })
  }
})