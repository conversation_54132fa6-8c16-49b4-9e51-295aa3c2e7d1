// goods.js
var app = getApp();
var p = 1;
var is_loading_comment = false;
var is_more_comment = true;
var share_count = 0;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    coupon_guid: '', // 卡券GUID
    way: 2, // 1 卡券提货 2商城购物
    hide_price: 0,
    token: '', // 用于提货校验
    guid: null,
    goods: {},
    show_attr_picker: false,
    form: {
      number: 1,
    },
    share_obj: {
      title: '', // 默认是小程序的名称(可以写slogan等)
      query: '', // 默认是当前页面，必须是以‘/'开头的完整路径
      imageUrl: '', //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
    },
    tab_detail: "active",
    tab_comment: "",
    comment_list: [],
    comment_count: {
      score_all: 0,
      score_3: 0,
      score_2: 0,
      score_1: 0,
    },
    autoplay: false,
    hide: "hide",
    show: false,
    dialog: false,
    password_focus: false,
    x: wx.getSystemInfoSync().windowWidth,
    y: wx.getSystemInfoSync().windowHeight - 20,
    seckill_end_time_over: {
      h: "--",
      m: "--",
      s: "--",
    },
  },
  exchange_code: function (e) {
    var that = this;
    let data = e.detail.value;
    data.goods_guid = that.data.goods.guid;
    data.way = 2;
    console.log(data);
    if (!data.code) {
      wx.showModal({
        title: '系统提示',
        content: '请输入卡号!',
        showCancel: false,
      });
      return false;
    }
    if (!data.password) {
      wx.showModal({
        title: '系统提示',
        content: '请输入密码!',
        showCancel: false,
      });
      return false;
    }

    app.request({
      url: app.api.code.verify_code,
      data: data,
      success: function (result) {
        let choose_goods_list = {};
        choose_goods_list[that.data.goods.guid] = 1;
        wx.setStorageSync('choose_goods_list', choose_goods_list);
        wx.redirectTo({
          url: '/pages/code/submit_order?bid=' + app.ext_config.bid + '&token=' + result.data.data.token,
        })
        return;
      },
    });
  },
  exchange: function (e) {
    this.setData({
      dialog: true,
    })
  },
  scan: function (e) {
    console.log('index - scan')
    var that = this;
    wx.scanCode({
      success(res) {
        app.tools.parse_qrcode(that, res.result);
        // that.setData({
        //   code: res.result,
        // });
      },
      fail(res) {
        console.log(res);
      }
    })
  },
  close_dialog: function (e) {
    this.setData({
      dialog: false,
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);

    console.log(wx.getSystemInfoSync());
    share_count = 0;
    p = 1;
    is_loading_comment = false;
    is_more_comment = true;
    this.setData({
      store: wx.getStorageSync('store'),
    });

    // var parent_id = 0;
    // var user_guid = options.user_guid;
    // console.log("options=>" + JSON.stringify(options));
    // var scene = decodeURIComponent(options.scene);
    // if (user_guid != undefined) {
    //   parent_id = user_guid;
    // } else if (scene != undefined) {
    //   console.log("scene string=>" + scene);
    //   var scene_obj = app.utils.scene_decode(scene);
    //   console.log("scene obj=>" + JSON.stringify(scene_obj));
    //   if (scene_obj.uid && scene_obj.gid) {
    //     parent_id = scene_obj.uid;
    //     options.id = scene_obj.gid;
    //   } else {
    //     parent_id = scene;
    //   }
    // }
    // app.loginBindParent({
    //   parent_id: parent_id
    // });
    var page = this;
    console.log(options);
    page.setData({
      guid: options.guid,
    });
    if (options.way !== undefined) {
      page.setData({
        way: options.way,
      });
    }
    if (options.token !== undefined) {
      page.setData({
        token: options.token,
      });
    }
    if (options.coupon_guid !== undefined) {
      page.setData({
        coupon_guid: options.coupon_guid,
      });
    }
    if (options.hide_price !== undefined) {
      page.setData({
        hide_price: options.hide_price,
      });
    }

    page.getGoods();
    //page.getCommentList();
  },
  getShareObj: function (page, share_title, share_path, share_img) {
    let that = this;
    app.request({
      url: app.api.user.info,
      success: function (result) {
        let user_info = result.data;
        var query = ''
        var pages = getCurrentPages() //获取加载的页面
        var currentPage = pages[pages.length - 1] //获取当前页面的对象
        var options = currentPage.options //如果要获取url中所带的参数可以查看options
        for (var key in options) {
          var value = options[key]
          query += key + '=' + value + '&'
        }
        query = query + 'share_member_guid=' + user_info.guid;
        // 设置菜单中的转发按钮触发转发事件时的转发内容
        let share_obj = {
          title: share_title || "", // 默认是小程序的名称(可以写slogan等)
          query: query, // 默认是当前页面，必须是以‘/'开头的完整路径
          imageUrl: share_img || '', //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
        };
        console.log(share_obj)
        // 来自页面内的按钮的转发
        if (options.from == 'button') {
          let eData = options.target.dataset;
          console.log(eData); // shareBtn
        }
        that.setData({
          share_obj: share_obj
        })
      }
    });
  },
  getGoods: function () {
    var page = this;
    app.request({
      url: app.api.home.goods,
      data: {
        guid: page.data.guid
      },
      success: function (res) {
        page.setData({
          goods: res.data,
          attr_group_list: res.data.attr_group_list,
          checked_attr_list: res.data.checked_attr_list,
        });
        page.getShareObj(page, page.data.goods.name, '', page.data.goods.pic_list[0].pic_url);
        wx.setNavigationBarTitle({
          title: res.data.name
        })
        if (page.data.goods.seckill)
          page.setseckillTimeOver();
        page.selectDefaultAttr();
      }
    });
  },
  selectDefaultAttr: function () {
    var page = this;
    if (!page.data.goods || page.data.goods.use_attr !== 0)
      return;
    for (var i in page.data.attr_group_list) {
      for (var j in page.data.attr_group_list[i].attr_list) {
        if (i == 0 && j == 0)
          page.data.attr_group_list[i].attr_list[j]['checked'] = true;
      }
    }
    page.setData({
      attr_group_list: page.data.attr_group_list,
    });
  },
  getCommentList: function (more) {
    var page = this;
    if (more && page.data.tab_comment != "active")
      return;
    if (is_loading_comment)
      return;
    if (!is_more_comment)
      return;
    is_loading_comment = true;
    app.request({
      url: app.api.home.comment_list,
      data: {
        guid: page.data.guid,
        page: p,
      },
      success: function (res) {
        if (res.code != 0)
          return;
        is_loading_comment = false;
        p++;
        page.setData({
          comment_count: res.data.comment_count,
          comment_list: more ? page.data.comment_list.concat(res.data.data) : res.data.data,
        });
        if (res.data.list.length == 0)
          is_more_comment = false;
      }
    });
  },

  onGoodsImageClick: function (e) {
    var page = this;
    var urls = [];
    var index = e.currentTarget.dataset.index;
    //console.log(page.data.goods.pic_list);
    for (var i in page.data.goods.pic_list) {
      urls.push(page.data.goods.pic_list[i].pic_url);
    }
    wx.previewImage({
      urls: urls, // 需要预览的图片http链接列表
      current: urls[index],
    });
  },

  numberSub: function () {
    var page = this;
    var num = page.data.form.number;
    if (num <= 1)
      return true;
    num--;
    page.setData({
      form: {
        number: num,
      }
    });
  },

  numberAdd: function () {
    var page = this;
    var num = page.data.form.number;
    num++;
    page.setData({
      form: {
        number: num,
      }
    });
  },

  numberBlur: function (e) {
    var page = this;
    var num = e.detail.value;
    num = parseInt(num);
    if (isNaN(num))
      num = 1;
    if (num <= 0)
      num = 1;
    page.setData({
      form: {
        number: num,
      }
    });
  },

  addCart: function () {
    this.submit('ADD_CART');
  },

  buyNow: function () {
    this.submit('BUY_NOW');
  },
  exchangeGoods: function () {
    this.submit('EXCHANGE');
  },
  submit: function (type) {
    var page = this;
    if (!page.data.show_attr_picker) {
      page.setData({
        show_attr_picker: true,
      });
      return true;
    }
    if (page.data.seckill_data && page.data.seckill_data.rest_num > 0 && page.data.form.number > page.data.seckill_data.rest_num) {
      wx.showToast({
        title: "商品库存不足，请选择其它规格或数量",
        image: "/images/icon-warning.png",
      });
      return true;
    }

    if (page.data.form.number > page.data.goods.num) {
      wx.showToast({
        title: "商品库存不足，请选择其它规格或数量",
        image: "/images/icon-warning.png",
      });
      return true;
    }
    var attr_group_list = page.data.attr_group_list;
    var checked_attr_list = [];
    for (var i in attr_group_list) {
      var attr = false;
      for (var j in attr_group_list[i].attr_list) {
        if (attr_group_list[i].attr_list[j].checked) {
          attr = {
            attr_id: attr_group_list[i].attr_list[j].attr_id,
            attr_name: attr_group_list[i].attr_list[j].attr_name,
          };
          break;
        }
      }
      if (!attr) {
        wx.showToast({
          title: "请选择" + attr_group_list[i].attr_group_name,
          image: "/images/icon-warning.png",
        });
        return true;
      } else {
        checked_attr_list.push({
          attr_group_id: attr_group_list[i].attr_group_id,
          attr_group_name: attr_group_list[i].attr_group_name,
          attr_id: attr.attr_id,
          attr_name: attr.attr_name,
        });
      }
    }
    if (type == 'ADD_CART') { //加入购物车
      app.request({
        url: app.api.cart.add_cart,
        data: {
          goods_guid: page.data.guid,
          attr: JSON.stringify(checked_attr_list),
          amount: page.data.form.number,
        },
        success: function (res) {
          wx.showToast({
            title: res.msg,
            duration: 1500
          });
          page.setData({
            show_attr_picker: false,
          });

        }
      });
    }
    if (type == 'BUY_NOW') { //立即购买
      page.setData({
        show_attr_picker: false,
      });
      wx.redirectTo({
        url: "/pages/order-submit/order-submit?goods_info=" + JSON.stringify([{
          guid: page.data.guid,
          attr: checked_attr_list,
          amount: page.data.form.number,
        }]),
      });
    }

    if (type == 'EXCHANGE') { // 提货 暂时不支持SKU规格
      page.setData({
        show_attr_picker: false,
      });
      console.log(page.data.token);
      console.log(page.data.token);
      console.log(page.data.token);
      if (page.data.token !== undefined && page.data.token !== 'undefined') {
        let choose_goods_list = {};
        choose_goods_list[page.data.guid] = 1;
        wx.setStorageSync('choose_goods_list', choose_goods_list);
        wx.redirectTo({
          url: '/pages/code/submit_order?bid=' + app.ext_config.bid + '&token=' + page.data.token,
        })
      } else {
        let url = '/pages/code/index?goods_guid=' + page.data.guid;
        if (this.data.coupon_guid) {
          url += '&coupon_guid=' + this.data.coupon_guid
        }
        wx.redirectTo({
          url: url
        })
      }

      return;
      wx.redirectTo({
        url: "/pages/order-submit/order-submit?goods_info=" + JSON.stringify([{
          guid: page.data.guid,
          attr: checked_attr_list,
          amount: page.data.form.number,
        }]),
      });
    }

  },

  hideAttrPicker: function () {
    var page = this;
    page.setData({
      show_attr_picker: false,
    });
  },
  showAttrPicker: function () {
    var page = this;
    page.setData({
      show_attr_picker: true,
    });
  },

  attrClick: function (e) {
    var page = this;
    var attr_group_id = e.target.dataset.groupId;
    var attr_id = e.target.dataset.id;
    var attr_group_list = page.data.attr_group_list;
    for (var i in attr_group_list) {
      if (attr_group_list[i].attr_group_id != attr_group_id)
        continue;
      for (var j in attr_group_list[i].attr_list) {
        if (attr_group_list[i].attr_list[j].attr_id == attr_id) {
          attr_group_list[i].attr_list[j].checked = true;
        } else {
          attr_group_list[i].attr_list[j].checked = false;
        }
      }
    }
    page.setData({
      attr_group_list: attr_group_list,
    });

    var check_attr_list = [];
    var check_all = true;
    for (var i in attr_group_list) {
      var group_checked = false;
      for (var j in attr_group_list[i].attr_list) {
        if (attr_group_list[i].attr_list[j].checked) {
          check_attr_list.push(attr_group_list[i].attr_list[j].attr_id);
          group_checked = true;
          break;
        }
      }
      if (!group_checked) {
        check_all = false;
        break;
      }
    }
    if (!check_all)
      return;
    app.request({
      url: app.api.goods.goods_attr_info,
      data: {
        goods_guid: page.data.goods.guid,
        attr_list: JSON.stringify(check_attr_list),
      },
      success: function (res) {
        var goods = page.data.goods;
        goods.price = res.data.price;
        goods.stock = res.data.stock;
        goods.attr_pic = res.data.picture;
        page.setData({
          goods: goods,
          seckill_data: res.data.seckill,
        });
      }
    });

  },


  favoriteAdd: function () {
    var page = this;
    app.request({
      url: app.api.user.favorite_add,
      data: {
        guid: page.data.goods.guid,
        type: 1,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })
        var goods = page.data.goods;
        goods.is_favorite = 1;
        page.setData({
          goods: goods,
        });
      }
    });
  },

  favoriteRemove: function () {
    var page = this;
    app.request({
      url: app.api.user.favorite_remove,
      data: {
        guid: page.data.goods.guid,
        type: 1,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })
        var goods = page.data.goods;
        goods.is_favorite = 0;
        page.setData({
          goods: goods,
        });
      }
    });
  },

  tabSwitch: function (e) {
    var page = this;
    var tab = e.currentTarget.dataset.tab;
    if (tab == "detail") {
      page.setData({
        tab_detail: "active",
        tab_comment: "",
      });
    } else {
      page.setData({
        tab_detail: "",
        tab_comment: "active",
      });
    }
  },
  commentPicView: function (e) {
    console.log(e);
    var page = this;
    var index = e.currentTarget.dataset.index;
    var pic_index = e.currentTarget.dataset.picIndex;
    wx.previewImage({
      current: page.data.comment_list[index].pic_list[pic_index],
      urls: page.data.comment_list[index].pic_list,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.shareModalClose();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },


  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var page = this;
    page.getCommentList(true);
  },
  onShareTimeline: function () {
    var page = this;
    console.log(page.data.share_obj);
    return page.data.share_obj;
    return app.pageOnShareTimeline(page, page.data.goods.name, '', page.data.goods.pic_list[0].pic_url);
    return {
      title: page.data.goods.name, // 可不填
      query: '', // 可不填 传递的参数，只能是这种格式
      imageUrl: page.data.goods.pic_list[0].pic_url // 可不填,可以是网络路径也可以是本地路径，分享到朋友圈显示的图标
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    var page = this;

    return app.pageOnShareAppMessage(page, options, page.data.goods.name, '', page.data.goods.pic_list[0].pic_url);

    var page = this;
    var user_info = wx.getStorageSync("user_info");
    var res = {
      path: "/pages/index/goods_detail?guid=" + this.data.guid + "&user_guid=" + user_info.guid,
      success: function (e) {
        console.log(e);
        share_count++;
        if (share_count == 1)
          app.shareSendCoupon(page);
      },
      title: page.data.goods.name,
      imageUrl: page.data.goods.pic_list[0].pic_url,
    };
    return res;
  },
  play: function (e) {
    var url = e.target.dataset.url; //获取视频链接
    this.setData({
      url: url,
      hide: '',
      show: true,
    });
    var videoContext = wx.createVideoContext('video');
    // videoContext.requestFullScreen({
    //   direction: 0
    // });
    videoContext.play();
  },
  endAction: function (e) {
    this.setData({
      hide: "hide",
      show: false
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.pause();
  },
  close: function (e) {
    // if (e.target.id == 'video') {
    //   return true;
    // }
    this.setData({
      hide: "hide",
      show: false
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.pause();
  },
  hide: function (e) {
    if (e.detail.current == 0) {
      this.setData({
        img_hide: ""
      });
    } else {
      this.setData({
        img_hide: "hide"
      });
    }
  },

  showShareModal: function () {
    var page = this;
    page.setData({
      share_modal_active: "active",
      no_scroll: true,
    });
  },

  shareModalClose: function () {
    var page = this;
    page.setData({
      share_modal_active: "",
      no_scroll: false,
    });
  },

  getGoodsQrcode: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "active",
      share_modal_active: "",
    });
    if (page.data.goods_qrcode)
      return true;
    app.request({
      url: app.api.home.goods_qrcode,
      data: {
        goods_guid: page.data.guid,
      },
      success: function (res) {
        page.setData({
          goods_qrcode: res.data.pic_url,
        });
      },
    });
  },

  goodsQrcodeClose: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "",
      no_scroll: false,
    });
  },

  saveGoodsQrcode: function () {
    app.utils.saveImageToPhotosAlbum(this.data.goods_qrcode);
  },

  goodsQrcodeClick: function (e) {
    var src = e.currentTarget.dataset.src;
    wx.previewImage({
      urls: [src],
    });
  },
  closeCouponBox: function (e) {
    this.setData({
      get_coupon_list: ""
    });
  },

  setseckillTimeOver: function () {
    var page = this;

    function _init() {
      var time_over = page.data.goods.seckill.end_time - page.data.goods.seckill.now_time;
      time_over = time_over < 0 ? 0 : time_over;
      page.data.goods.seckill.now_time++;
      page.setData({
        goods: page.data.goods,
        seckill_end_time_over: secondToTime(time_over),
      });
    }

    _init();
    setInterval(function () {
      _init();
    }, 1000);

    function secondToTime(second) {
      var _h = parseInt(second / 3600);
      var _m = parseInt((second % 3600) / 60);
      var _s = second % 60;

      return {
        h: _h < 10 ? ("0" + _h) : ("" + _h),
        m: _m < 10 ? ("0" + _m) : ("" + _m),
        s: _s < 10 ? ("0" + _s) : ("" + _s),
      };
    }
  },

});