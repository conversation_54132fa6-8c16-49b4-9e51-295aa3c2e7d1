// goods.js
var app = getApp();
var p = 1;
var is_loading_comment = false;
var is_more_comment = true;
var share_count = 0;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    guid: null,
    goods: {},
    show_attr_picker: false,
    form: {
      number: 1,
    },
    tab_detail: "active",
    tab_comment: "",
    autoplay: false,
    hide: "hide",
    show: false,
    x: wx.getSystemInfoSync().windowWidth,
    y: wx.getSystemInfoSync().windowHeight - 20,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    console.log(wx.getSystemInfoSync());
    share_count = 0;
    p = 1;
    is_loading_comment = false;
    is_more_comment = true;

    console.log("options=>" + JSON.stringify(options));
    var scene = decodeURIComponent(options.scene);
    var page = this;
    page.setData({
      guid: options.guid,
    });
    page.getGoods();
    //page.getCommentList();
  },
  getGoods: function () {
    var page = this;
    app.request({
      url: app.api.code.code_detail,
      data: {
        guid: page.data.guid
      },
      success: function (res) {
        page.setData({
          goods: res.data
        });
        wx.setNavigationBarTitle({
          title: res.data.name
        })
      }
    });
  },
  onGoodsImageClick: function (e) {
    var page = this;
    var urls = [];
    var index = e.currentTarget.dataset.index;
    console.log(index);
    console.log(page.data.goods.pic_list);
    for (var i in page.data.goods.pic_list) {
      urls.push(page.data.goods.pic_list[i]);
      // urls.push(page.data.goods.pic_list[i].pic_url); 这种可以用于播放视频
    }
    console.log(urls);
    console.log(urls[index]);

    wx.previewImage({
      urls: urls, // 需要预览的图片http链接列表
      current: urls[index],
    });
  },
  hideAttrPicker: function () {
    var page = this;
    page.setData({
      show_attr_picker: false,
    });
  },
  showAttrPicker: function () {
    var page = this;
    page.setData({
      show_attr_picker: true,
    });
  },
  numberSub: function () {
    var page = this;
    var num = page.data.form.number;
    if (num <= 1)
      return true;
    num--;
    page.setData({
      form: {
        number: num,
      }
    });
  },

  numberAdd: function () {
    var page = this;
    var num = page.data.form.number;
    num++;
    page.setData({
      form: {
        number: num,
      }
    });
  },

  numberBlur: function (e) {
    var page = this;
    var num = e.detail.value;
    num = parseInt(num);
    if (isNaN(num))
      num = 1;
    if (num <= 0)
      num = 1;
    page.setData({
      form: {
        number: num,
      }
    });
  },
  favoriteAdd: function () {
    var page = this;
    app.request({
      url: app.api.user.favorite_add,
      method: "post",
      data: {
        guid: page.data.goods.guid,
        type: 2,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })
        var goods = page.data.goods;
        goods.is_favorite = 1;
        page.setData({
          goods: goods,
        });
      }
    });
  },

  favoriteRemove: function () {
    var page = this;
    app.request({
      url: app.api.user.favorite_remove,
      data: {
        guid: page.data.goods.guid,
        type: 2,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })

        var goods = page.data.goods;
        goods.is_favorite = 0;
        page.setData({
          goods: goods,
        });
      }
    });
  },
  gotoExchange() {
    wx.navigateTo({
      url: '/pages/code/index?coupon_guid=' + this.data.guid,
    })
  },

  addCart: function () {
    this.submit('ADD_CART');
  },

  buyNow: function () {
    this.submit('BUY_NOW');
  },
  online_free_receive_code: function () {
    let page = this;
    //发起http请求下单
    app.request({
      url: app.api.code.online_free_receive_code,
      data: {
        coupon_guid: page.data.guid,
        share_member_guid: wx.getStorageSync('share_member_guid'),
        share_user_guid: wx.getStorageSync('share_user_guid')
      },
      success: function (result) {
        wx.showToast({
          title: result.msg,
          icon: 'success',
          duration: 500
        });
        setTimeout(function () {
          wx.navigateTo({
            url: '/pages/code_list/index',
          })
        }, 500) //延迟时间
      },
    });
  },
  submit: function (type) {
    var page = this;
    if (!page.data.show_attr_picker) {
      page.setData({
        show_attr_picker: true,
      });
      return true;
    }
    if (page.data.seckill_data && page.data.seckill_data.rest_num > 0 && page.data.form.number > page.data.seckill_data.rest_num) {
      wx.showToast({
        title: "卡券库存不足，请选择其它规格或数量",
        image: "/images/icon-warning.png",
      });
      return true;
    }

    if (page.data.form.number > page.data.goods.num) {
      wx.showToast({
        title: "卡券库存不足，请选择其它规格或数量",
        image: "/images/icon-warning.png",
      });
      return true;
    }
    if (type == 'BUY_NOW') { //立即购买
      page.setData({
        show_attr_picker: false,
      });
      //发起http请求下单
      app.request({
        url: app.api.code.submit_buy_code_order,
        data: {
          coupon_guid: page.data.guid,
          amount: page.data.form.number,
          share_member_guid: wx.getStorageSync('share_member_guid'),
          share_user_guid: wx.getStorageSync('share_user_guid')
        },
        success: function (result) {
          if (result.data.status == -1) {
            wx.navigateTo({
              url: '/pages/pay/submit?bid=' + app.ext_config.bid + '&order_guid=' + result.data.order_guid + '&bill_number=' + result.data.bill_number + '&type=code_buy',
            })
          }
        },
      });
      return;
      wx.redirectTo({
        url: "/pages/order-submit/order-submit?goods_info=" + JSON.stringify([{
          guid: page.data.guid,
          amount: page.data.form.number,
        }]),
      });
    }

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.shareModalClose();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    var page = this;
    // page.getCommentList(true);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options, this.data.goods.name, '', this.data.goods.pic_list[0].pic_url);
    // return app.pageOnShareAppMessage(this, options)
  },
  play: function (e) {
    var url = e.target.dataset.url; //获取视频链接
    this.setData({
      url: url,
      hide: '',
      show: true,
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.play();
  },

  endAction: function (e) {
    this.setData({
      hide: "hide",
      show: false
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.pause();
  },
  close: function (e) {
    // if (e.target.id == 'video') {
    //   return true;
    // }
    this.setData({
      hide: "hide",
      show: false
    });
    var videoContext = wx.createVideoContext('video');
    videoContext.pause();
  },
  hide: function (e) {
    if (e.detail.current == 0) {
      this.setData({
        img_hide: ""
      });
    } else {
      this.setData({
        img_hide: "hide"
      });
    }
  },

  showShareModal: function () {
    var page = this;
    page.setData({
      share_modal_active: "active",
      no_scroll: true,
    });
  },

  shareModalClose: function () {
    var page = this;
    page.setData({
      share_modal_active: "",
      no_scroll: false,
    });
  },

  getGoodsQrcode: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "active",
      share_modal_active: "",
    });
    if (page.data.goods_qrcode)
      return true;

    app.request({
      url: app.api.code.share_qrcode,
      data: {
        goods_guid: page.data.guid,
      },
      success: function (res) {
        page.setData({
          goods_qrcode: res.data.pic_url,
        });
      },
      fail: function (res) {
        page.goodsQrcodeClose();
        // wx.showModal({
        //   title: "提示",
        //   content: res.msg,
        //   showCancel: false,
        //   success: function (res) {
        //     if (res.confirm) {

        //     }
        //   }
        // });
      }
    });
  },

  goodsQrcodeClose: function () {
    var page = this;
    page.setData({
      goods_qrcode_active: "",
      no_scroll: false,
    });
  },

  saveGoodsQrcode: function () {
    app.utils.saveImageToPhotosAlbum(this.data.goods_qrcode);
  },

  goodsQrcodeClick: function (e) {
    var src = e.currentTarget.dataset.src;
    wx.previewImage({
      urls: [src],
    });
  },
  closeCouponBox: function (e) {
    this.setData({
      get_coupon_list: ""
    });
  },

  setseckillTimeOver: function () {
    var page = this;

    function _init() {
      var time_over = page.data.goods.seckill.end_time - page.data.goods.seckill.now_time;
      time_over = time_over < 0 ? 0 : time_over;
      page.data.goods.seckill.now_time++;
      page.setData({
        goods: page.data.goods,
        seckill_end_time_over: secondToTime(time_over),
      });
    }

    _init();
    setInterval(function () {
      _init();
    }, 1000);

    function secondToTime(second) {
      var _h = parseInt(second / 3600);
      var _m = parseInt((second % 3600) / 60);
      var _s = second % 60;

      return {
        h: _h < 10 ? ("0" + _h) : ("" + _h),
        m: _m < 10 ? ("0" + _m) : ("" + _m),
        s: _s < 10 ? ("0" + _s) : ("" + _s),
      };
    }
  },

});