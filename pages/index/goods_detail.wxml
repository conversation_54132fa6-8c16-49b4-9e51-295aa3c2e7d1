<!--goods.wxml-->
<view class="{{(show_attr_picker||show||no_scroll)?'no-scroll':''}}">
  <view class="attr-picker" wx:if="{{show_attr_picker}}">
    <view class="content-box">
      <view class="flex-row" style="border-bottom: 1rpx solid #e3e3e3;padding: 24rpx 28rpx">
        <view class="flex-grow-0">
          <view class="goods-pic-box">
            <block wx:if="{{goods.attr_pic}}">
              <image mode="aspectFill" src="{{goods.attr_pic}}"></image>
            </block>
            <block wx:else>
              <image mode="aspectFill" src="{{goods.pic_list[0].pic_url}}"></image>
            </block>
          </view>
        </view>
        <view class="flex-grow-1" style="padding: 0 24rpx">

          <block wx:if="{{seckill_data && seckill_data.rest_num>0}}">
            <view style="margin-bottom: 12rpx;">
              <view>
                <text style="color:#ff4544;font-weight: bold">￥{{seckill_data.seckill_price}}</text>
                <text style="color:#888;text-decoration: line-through;font-size: 9pt;margin-left: 8rpx">￥{{goods.price}}</text>
              </view>
            </view>
          </block>
          <block wx:else>
            <view wx:if='{{hide_price != 1}}' style="color:#ff4544;margin-bottom: 12rpx;font-weight: bold">￥{{goods.price}}</view>
          </block>
          <view style="font-size:9pt">
            <block wx:if="{{goods.stock>=0 && goods.stock_mode == 1 && goods.show_stock==1 }}">库存: {{goods.stock}}
            </block>
          </view>
        </view>
        <view class="flex-grow-0">
          <view bindtap="hideAttrPicker" class="flex-x-center flex-y-center" style="width: 100rpx;height: 100rpx">
            <image style="width: 30rpx;height: 30rpx" src="/images/icon-close.png" />
          </view>
        </view>
      </view>
      <scroll-view scroll-y="true" style="max-height: 650rpx;">
        <view style="padding: 24rpx 28rpx">
          <view>
            <view class="attr-group" wx:for="{{attr_group_list}}" wx:for-item="attr_group" wx:if="{{attr_group.attr_list.length>0}}">
              <view class="attr-group-name">{{attr_group.attr_group_name}}</view>
              <view class="attr-list">
                <text class="attr-item {{item.checked?'active':''}}" wx:for="{{attr_group.attr_list}}" data-group-id="{{attr_group.attr_group_id}}" data-id="{{item.attr_id}}" bindtap="attrClick">{{item.attr_name}}
                </text>
              </view>
            </view>
          </view>
          <view style="height: 0;border-bottom: 1rpx solid #e3e3e3;margin-bottom: 40rpx"></view>
          <view style="padding-bottom: 40rpx">
            <view class="flex-row flex-y-center mb-20">
              <view class="flex-grow-1">数量</view>
              <view class="flex-grow-0">

                <view class="flex-row number-input-box">
                  <view class="flex-grow-0 flex-x-center flex-y-center number-btn number-sub {{form.number<=1?'disabled':''}}" bindtap="numberSub" wx:if="{{way==2}}">-
                  </view>
                  <view class="flex-grow-0">
                    <input class="flex-grow-1 number-input" disabled="{{way==1 ? 'disable' : '' }}" value="{{form.number}}" type="number" step="1" min="1" bindblur="numberBlur" />
                  </view>
                  <view class="flex-grow-0 flex-x-center flex-y-center number-btn number-add" bindtap="numberAdd" wx:if="{{way==2}}">+
                  </view>
                </view>

              </view>
            </view>
          </view>
        </view>
      </scroll-view>

    </view>
  </view>
  <view style="padding-bottom: 120rpx">
    <swiper class="goods-image-swiper" autoplay="{{autoplay}}" interval="5000" duration="300" indicator-dots="true" circular="true" bindchange="hide">
      <block wx:for="{{goods.pic_list}}">
        <swiper-item>
          <block wx:if="{{goods.video_url}}">
            <!-- <view style='width:750rpx;height:700rpx;position:absolute;top:0;left:0;'> -->
            <image class="play {{img_hide}}" src="/images/video-play.png" data-url="{{goods.video_url}}" bindtap="play"></image>
            <!-- </view> -->
          </block>
          <image bindtap="onGoodsImageClick" data-index="{{index}}" class="goods-image" src="{{item.pic_url}}" mode="aspectFill" />
        </swiper-item>
      </block>
    </swiper>

    <view wx:if="{{goods&&goods.seckill}}" class="seckill-bar flex-row">
      <view class="flex-grow-1 flex-row seckill-bar-left">
        <view class="flex-grow-0 flex-y-center">
          <view class="flex-y-bottom seckill-price">
            <text class="cny">￥</text>
            <text>{{goods.seckill.seckill_price}}</text>
          </view>
        </view>
        <view class="flex-grow-1 flex-y-center">
          <view>
            <view class="goods-price">￥{{goods.price}}</view>
            <view class="sell-num">已抢{{goods.seckill.sell_num}}件</view>
          </view>
        </view>
      </view>
      <view class="flex-grow-0 flex-y-center seckill-bar-right">
        <view>
          <view class="time-over-text">距离结束仅剩</view>
          <view class="timer">
            <text class="timer-num">{{seckill_end_time_over.h}}</text>
            <text class="timer-split">:</text>
            <text class="timer-num">{{seckill_end_time_over.m}}</text>
            <text class="timer-split">:</text>
            <text class="timer-num">{{seckill_end_time_over.s}}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="goods-info" style="background: #fff;padding:32rpx 24rpx;border-bottom: 1rpx solid #eee ">
      <view style="font-weight: bold;margin-bottom: 24rpx;line-height: 1.7">{{goods.name}}</view>
      <view class="flex-row flex-y-center">
        <view class="flex-grow-1 flex-row flex-y-bottom">
          <block wx:if="{{goods && goods.seckill}}">
            <view class="flex-grow-0" style="font-weight: bold;color: #ff4544;margin-right: 32rpx">
              <text style="font-size: 9pt;">￥</text>
              <text style="font-size: 13pt">{{goods.seckill.seckill_price}}</text>
            </view>
          </block>
          <block wx:else>
            <view wx:if='{{hide_price != 1 && way ==2}}' class="flex-grow-0" style="font-weight: bold;color: #ff4544;margin-right: 32rpx">
              <text style="font-size: 9pt;">￥</text>
              <text style="font-size: 13pt">{{goods.price}}</text>
              <block wx:if="{{goods.unit}}">/{{goods.unit}}</block>
            </view>
            <view class="flex-grow-0" style="margin-right: 32rpx" wx:if="{{goods.original_price > goods.price }}">
              <text style="font-size: 9pt;color: #888;">市场价: </text>
              <text style="font-size: 9pt;color: #888;text-decoration: line-through">{{goods.original_price}}</text>
            </view>

          </block>
          <view wx:if="{{goods.sales>0  && hide_price != 1  }}" class="flex-grow-0" style="margin-right: 32rpx;">
            <text style="font-size: 9pt;color: #888;">已售{{goods.sales}}件</text>
          </view>
        </view>
        <view class="flex-grow-0" wx:if="{{hide_price != 1 }}">
          <view bindtap="showShareModal" class="share-btn" plain="true">
            <image style="width: 40rpx;height: 40rpx" src="/images/icon-share.png" />
            <view style="color: #888">分享</view>
          </view>
        </view>
      </view>

      <view class="flex-grow-0" style="font-weight: bold;color: coral;font-size: 28rpx;">

        <!-- <view wx:if="{{goods.specs.length>3}}">{{goods.specs}}</view> -->
        <view wx:if="{{goods.specs}}">规格: {{goods.specs}}</view>

      </view>


    </view>
    <view wx:if="{{goods.service_list&&goods.service_list.length>0}}" class="bg-white mb-20 flex-row check-list">
      <view class="check-item flex-y-center" wx:for="{{goods.service_list}}">
        <image src="/images/icon-check.png"></image>
        <text>{{item}}</text>
      </view>
    </view>
    <view wx:else class="mb-20"></view>
    <!-- <view bindtap="showAttrPicker" class="flex-row flex-y-center" style="background: #fff;padding:0 24rpx;margin-bottom: 20rpx;height: 100rpx;">
      <view class="flex-grow-1 flex-row flex-y-center">
        <text>选择</text>
        <text style="margin-left: 40rpx;font-size: 9pt;color: #888888">规格</text>
      </view>
      <view class="flex-grow-0">
        <image style="width: 16rpx;height: 26rpx" src="/images/icon-jiantou-r.png" />
      </view>
    </view> -->
    <view class="tab-group">
      <view class="flex-row tab-group-header">
        <view bindtap="tabSwitch" data-tab="detail" class="flex-grow-1 flex-x-center tab-group-item {{tab_detail}}">
          <text class="flex-y-center">商品详情</text>
        </view>
        <!-- <view bindtap="tabSwitch" data-tab="comment" class="flex-grow-1 flex-x-center tab-group-item {{tab_comment}}">
          <text class="flex-y-center">评价</text>
        </view> -->
      </view>
      <view class="tab-group-body">
        <view class="tab-group-item {{tab_detail}}">
          <view class="goods-detail">
            <rich-text nodes="{{goods.description}}"></rich-text>
          </view>
        </view>
        <view class="tab-group-item {{tab_comment}}">
          <view class="comment-count flex-row">
            <view class="comment-count-item flex-grow-1 flex-x-center flex-col">
              <view>全部</view>
              <view>({{comment_count.score_all>999?'999+':comment_count.score_all}})</view>
            </view>
            <view class="comment-count-item flex-grow-1 flex-x-center flex-col">
              <view>好评</view>
              <view>({{comment_count.score_3>999?'999+':comment_count.score_3}})</view>
            </view>
            <view class="comment-count-item flex-grow-1 flex-x-center flex-col">
              <view>中评</view>
              <view>({{comment_count.score_2>999?'999+':comment_count.score_2}})</view>
            </view>
            <view class="comment-count-item flex-grow-1 flex-x-center flex-col">
              <view>差评</view>
              <view>({{comment_count.score_1>999?'999+':comment_count.score_1}})</view>
            </view>
          </view>
          <view class="comment-list">
            <view class="comment-item flex-row" wx:for="{{comment_list}}">
              <view class="flex-grow-0" style="overflow: visible">
                <image src="{{item.avatar_url}}" style="width: 70rpx;height: 70rpx;border-radius: 1000rpx" mode="aspectFill"></image>
              </view>
              <view class="flex-grow-1">
                <view class="flex-row mb-20">
                  <view class="flex-grow-1 flex-y-center nickname">{{item.nickname}}</view>
                  <view class="flex-grow-0 flex-y-center addtime">{{item.addtime}}</view>
                </view>
                <!--
                                <view class="mb-20" wx:if="{{item.score==3}}">
                                    <text class="score-tag">好评</text>
                                </view>
                                <view class="mb-20" wx:if="{{item.score==2}}">
                                    <text class="score-tag">中评</text>
                                </view>
                                <view class="mb-20" wx:if="{{item.score==1}}">
                                    <text class="score-tag">差评</text>
                                </view>
                                -->
                <view class="mb-20">{{item.content}}</view>
                <view class="pic-list" wx:if="{{item.pic_list&&item.pic_list.length>0}}">
                  <image bindtap="commentPicView" wx:for="{{item.pic_list}}" wx:for-index="{{pic_index}}" data-index="{{index}}" data-pic-index="{{pic_index}}" src="{{item}}" mode="aspectFill" class="pic-item"></image>
                </view>
              </view>
            </view>
          </view>


        </view>
      </view>
    </view>

  </view>
  <navigator url="/pages/cart/cart" class="cart-nav" open-type="redirect">
    <image src="/images/nav-icon-cart.png"></image>
  </navigator>
  <view class="flex-row bar-bottom">

    <view class="flex-grow-0 flex-row">
      <block wx:if="{{way==2}}">
        <navigator hover-class="none" class="flex-grow-0 flex-y-center bar-bottom-btn" url="/pages/index/index" open-type="redirect" style="padding: 0;background-color: #fff;">
          <view>
            <image src="/images/icon-store.png"></image>
            <text>首页</text>
          </view>
        </navigator>
        <view class="flex-grow-0 flex-y-center bar-bottom-btn">
          <button open-type="contact" plain="true" style="padding: 0;background-color: #fff;">
            <image src="/images/icon-user-kf.png"></image>
            <text>客服</text>
          </button>
        </view>


        <view wx:if="{{goods.is_favorite && goods.is_favorite==1}}" bindtap="favoriteRemove" class="flex-grow-0 flex-y-center bar-bottom-btn">
          <view>
            <image src="/images/icon-favorite-active.png"></image>
            <text>已收藏</text>
          </view>
        </view>
        <view wx:else bindtap="favoriteAdd" class="flex-grow-0 flex-y-center bar-bottom-btn">
          <view>
            <image src="/images/icon-favorite.png"></image>
            <text>收藏</text>
          </view>
        </view>
      </block>

    </view>
    <view class="flex-grow-1 flex-row">
      <block wx:if="{{way==1}}">
        <view wx:if="{{goods.stock_mode == 0 || goods.stock > 0 }}" class="flex-grow-1 flex-y-center flex-x-center buy-now" bindtap="exchangeGoods">立即兑换</view>
        <view wx:if="{{goods.stock_mode == 1 && goods.stock <= 0}}" class="flex-grow-1 flex-y-center flex-x-center empty">补货中</view>
      </block>
      <block wx:if="{{way==2}}">
        <block wx:if="{{goods.status==1}}">
          <block wx:if="{{goods.stock_mode ==  0 || (goods.stock_mode ==  1 && goods.stock > 0) }}">
            <view class="flex-grow-1 flex-y-center flex-x-center add-cart" wx:if="{{goods.show_exchange_button}}" bindtap="exchange">立即兑换</view>
            <view class="flex-grow-1 flex-y-center flex-x-center add-cart" wx:if="{{!goods.show_exchange_button}}" bindtap="addCart">加入购物车</view>
            <view class="flex-grow-1 flex-y-center flex-x-center buy-now" bindtap="buyNow">立即购买</view>
          </block>
          <block wx:if="{{goods.stock_mode == 1 &&  goods.stock <= 0}}">
            <view class="flex-grow-1 flex-y-center flex-x-center empty">已售罄</view>
          </block>
        </block>

        <block wx:if="{{goods.status==0}}">
          <view class="flex-grow-1 flex-y-center flex-x-center empty">已下架</view>
        </block>

      </block>
    </view>
  </view>
  <include src="/common/get-coupon/get-coupon.wxml" />
  <block wx:if="{{store.show_customer_service && store.show_customer_service==1}}">
    <include src="/common/float-icon/float-icon.wxml" />
  </block>
</view>

<view class='modal flex-row {{hide}}' bindtap='close'>
  <view class='flex-y-center' style="width: 100%;height: 100%;">
    <video bindended='endAction' controls="{{false}}" show-fullscreen-btn="{{false}}" show-play-btn="{{false}}" object-fit="cover" style="height: 100%;width: 100%;" src='{{url}}' id="video" autoplay="true"></video>
  </view>
</view>

<view class="share-modal {{share_modal_active}}">
  <view class="share-modal-body">
    <view class="flex-row">
      <view class="flex-grow-1 flex-x-center">
        <button open-type="share" class="share-bottom">
          <image src="/images/icon-share-friend.png"></image>
          <view>分享给朋友</view>
        </button>
      </view>
      <view class="flex-grow-1 flex-x-center">
        <button bindtap="getGoodsQrcode" class="share-bottom">
          <image src="/images/icon-share-qrcode.png"></image>
          <view>生成商品海报</view>
        </button>
      </view>
    </view>
    <view bindtap="shareModalClose" class="share-modal-close flex-y-center flex-x-center">关闭</view>
  </view>
</view>

<view class="goods-qrcode-modal {{goods_qrcode_active}}">
  <view class="goods-qrcode-body flex-col">
    <view class="flex-grow-1" style="position: relative">
      <view style="position: absolute;left: 0;top:0;width: 100%;height: 100%;padding: 100rpx 100rpx 60rpx">
        <view class="goods-qrcode-box">
          <view class="goods-qrcode-loading flex-x-center flex-y-center">
            <view class="flex-x-center flex-col">
              <image style="width: 150rpx;height: 150rpx" src="/images/loading2.svg"></image>
              <view style="color: #888">海报生成中</view>
            </view>
          </view>
          <image bindtap="goodsQrcodeClick" mode="widthFix" class="goods-qrcode {{goods_qrcode?'active':''}}" data-src="{{goods_qrcode}}" src="{{goods_qrcode}}"></image>
        </view>
      </view>
    </view>
    <view class="flex-grow-0 flex-col flex-x-center" style="padding: 0 60rpx 80rpx">
      <view style="margin-bottom: 20rpx;padding: 0 40rpx">
        <button wx:if="{{goods_qrcode}}" bindtap="saveGoodsQrcode" style="background: #ff4544;color: #fff;">
          保存图片
        </button>
        <button wx:else style="opacity: .4">保存图片</button>
      </view>
      <view style="color: #888;font-size: 9pt;text-align: center">保存至相册</view>
    </view>
    <view class="goods-qrcode-close" bindtap="goodsQrcodeClose">
      <image src="/images/icon-close2.png" style="width: 50rpx;height: 50rpx;display: block"></image>
    </view>
  </view>
</view>



<view aria-role="dialog" aria-modal="true" wx:if="{{dialog}}">
  <view class="weui-mask weui-transition {{dialog ? 'weui-transition_show' : ''}}" bindtap="close_dialog" aria-role="button" aria-label="关闭"></view>
  <view class="weui-half-screen-dialog weui-half-screen-dialog_large weui-transition {{dialog ? 'weui-transition_show' : ''}}">
    <view class="weui-half-screen-dialog__hd">
      <view class="weui-half-screen-dialog__hd__side" bindtap="close_dialog">
        <!-- <view bindtap="shareModalClose" class="share-modal-close flex-y-center flex-x-center">关闭</view> -->
        <!-- <view aria-role="button" class="weui-icon-btn"><i class="weui-icon-close-thin"></i></view> -->
        <image src="/images/icon-close2.png" style="width: 50rpx;height: 50rpx;display: block"></image>

      </view>
      <view class="weui-half-screen-dialog__hd__main">
        <strong class="weui-half-screen-dialog__title">卡密兑换</strong>
      </view>
    </view>
    <view class="weui-half-screen-dialog__bd" style="padding-top: 32px; height: 50px;">

      <!-- <text style="color: #999;padding-left: 40rpx;"> 您可以将实体卡绑定至您的账户 </text> -->

      <form bindsubmit="exchange_code">
        <view class="weui-cells weui-cells_after-title">

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">卡号</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="text" name='code' value="{{code}}" focus="{{false}}" placeholder="请输入卡号" />
            </view>
            <view bindtap="scan">
              <image class="image" style="width: 40rpx;height: 40rpx;" src="/images/scan.png" alt></image>
            </view>
          </view>

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">密码</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="password" name='password' value="{{password}}" focus="{{password_focus}}" placeholder="请输入密码" />
            </view>
          </view>
        </view>
        <view class="weui-btn-area" style="margin: 20px 0 0 0;">
          <button class='weui-btn' style="width: 90%;" type="primary" formType="submit">确认兑换</button>
        </view>
      </form>
    </view>
    <view class="weui-half-screen-dialog__ft">
      <view class="weui-half-screen-dialog__btn-area">
        <view aria-disabled="true" class="weui-btn weui-btn_default" aria-role="button" bindtap="close_dialog">暂不兑换</view>
      </view>
    </view>
  </view>
</view>