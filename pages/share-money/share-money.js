// pages/share-money/share-money.js
var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    block: false,
    active: '',
    share_setting: {},
    total_brokerage_money: 0, //分销佣金
    total_success_cash_brokerage_money: 0, //累计成功提现佣金
    total_auditing_cash_brokerage_money: 0, //待审核订单
    total_waiting_for_payment_brokerage_money: 0, //累计审核通过待打款金额
    available_cash_brokerage_money: 0, // 可提现
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    this.get_share_setting();
  },
  get_share_setting: function () {
    var page = this;
    app.request({
      url: api.share.get_share_setting,
      success: function (result) {
        page.setData({
          share_setting: result.data
        });
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

    var page = this;
    // var share_setting = wx.getStorageSync("share_setting");
    // page.setData({
    //   share_setting: share_setting,
    // });
    app.request({
      url: api.share.get_info,
      success: function (res) {
        page.setData(res.data);
      },
    });
  },
  tapName: function (e) {
    var page = this;
    var active = '';
    if (!page.data.block) {
      active = 'active';
    }
    page.setData({
      block: !page.data.block,
      active: active
    });

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
})