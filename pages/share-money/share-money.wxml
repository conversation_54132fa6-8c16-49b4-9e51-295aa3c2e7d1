<view class="info">
  <view class="info-title flex-row">
    <view class="info-block">
      <view class="info-up white">累计佣金</view>
      <view class="info-bottom white">
        <view class="big bold">{{total_brokerage_money}}</view>
        <view class="bottom">元</view>
      </view>
    </view>
    <view class="info-block">
      <navigator url="/pages/cash-detail/cash-detail" open-type="navigate">
        <view class="info-btn white big-13">提现明细</view>
      </navigator>
    </view>
  </view>
  <view class="info-content black">
    <view class="info-label flex-y-center big-13">
      <view class="info-left">可提现佣金</view>
      <view class="info-right">{{available_cash_brokerage_money}}元</view>
    </view>
    <view class=" info-margin">
      <view class="info-label big-13">
        <view class=" border-bottom flex-y-center">
          <view class="info-left">已提现佣金</view>
          <view class="info-right">{{total_success_cash_brokerage_money}}元</view>
        </view>
      </view>
      <view class="info-label flex-y-center big-13">
        <view class="info-left">待审核佣金</view>
        <view class="info-right">{{total_auditing_cash_brokerage_money}}元</view>
      </view>
      <view class="info-label flex-y-center big-13">
        <view class="info-left">待打款佣金</view>
        <view class="info-right">{{total_waiting_for_payment_brokerage_money}}元</view>
      </view>
    </view>



    <view hover="true" hover-class="button-hover" class="info-label flex-y-center  big-13" bindtap="tapName" wx:if="{{share_setting.content}}">
      <view class="info-left">用户须知</view>
      <view class="info-user info-right">
        <image src="/images/img-share-down.png" style="width:26rpx;height:16rpx;" wx:if="{{block}}"></image>
        <image src="/images/img-share-right.png" style="width:16rpx;height:26rpx;" wx:else></image>
      </view>
    </view>
    <view class="info-label flex-y-center big-13" style="height:auto;padding:24rpx 24rpx;" wx:if="{{block}}">
      <text class="" style="font-size:10pt;line-height:1.5">{{share_setting.content}}</text>
    </view>
  </view>
  <view class="info-footer">
    <navigator open-type="navigate" url="/pages/cash/cash">
      <view class="info-btn white">我要提现</view>
    </navigator>
  </view>
</view>