<!--pages/address/address.wxml-->
<view class="bottom-bar">
    <navigator url="/pages/address-edit/address-edit">添加新地址</navigator>
</view>
<view wx:if="{{show_no_data_tip}}" class="no-data-tip">暂无收货地址</view>
<view class="address-list">
    <view class="address-item" wx:for="{{address_list}}">
        <view class="userinfo flex-row">
            <view class="flex-grow-1">收货人：{{item.true_name}}</view>
            <view class="flex-grow-0">{{item.mobile}}</view>
        </view>
        <view class="address-detail">收货地址：{{item.province_name}}{{item.city_name}}{{item.area_name}}{{item.address}}</view>
        <view class="flex-row">
            <view class="flex-grow-1">
                <view style="display: inline-block" wx:if="{{item.is_default==1}}">
                    <navigator class="default-address active flex-row flex-y-center">
                        <image style="width: 40rpx;height: 40rpx;margin-right: 12rpx" src="/images/icon-checked.png"/>
                        <text>默认地址</text>
                    </navigator>
                </view>
                <view style="display: inline-block" wx:else>
                    <navigator bindtap="setDefaultAddress" data-index="{{index}}" class="default-address flex-row flex-y-center">
                        <image style="width: 40rpx;height: 40rpx;margin-right: 12rpx" src="/images/icon-uncheck.png"/>
                        <navigator>设为默认</navigator>
                    </navigator>
                </view>
            </view>
            <view class="flex-grow-0 flex-row">
                <navigator class="address-option flex-y-center" url="/pages/address-edit/address-edit?guid={{item.guid}}">
                    <image src="/images/icon-edit.png"/>
                    <text>编辑</text>
                </navigator>
                <navigator bindtap="deleteAddress" class="address-option flex-y-center" data-index="{{index}}" data-guid="{{item.guid}}">
                    <image src="/images/icon-delete.png"/>
                    <text>删除</text>
                </navigator>
            </view>
        </view>
    </view>
</view>