// pages/address/address.js
var api = require('../../api.js');
var app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    address_list: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    var page = this;
    wx.showNavigationBarLoading();
    page.loadList();
  },

  loadList: function () {
    var page = this;
    app.request({
      url: api.address.list,
      success: function (res) {
        wx.hideNavigationBarLoading();
        page.setData({
          address_list: res.data,
        });
        page.setData({
          show_no_data_tip: (page.data.address_list.length == 0),
        });
      }
    });
  },

  setDefaultAddress: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    var address = page.data.address_list[index];
    app.request({
      url: api.address.set_default,
      data: {
        guid: address.guid,
        is_default: 1,
      },
      success: function (res) {
        wx.showToast({
          title: res.msg,
          icon: 'success', //图标，支持"success"、"loading" 
          // image: '/images/tan.png', //自定义图标的本地路径，image 的优先级高于 icon
          duration: 1000, //提示的延迟时间，单位毫秒，默认：1500 
          mask: false, //是否显示透明蒙层，防止触摸穿透，默认：false 
          success: function () {},
          fail: function () {},
          complete: function () {}
        })
        var address_list = page.data.address_list;
        for (var i in address_list) {
          if (i == index) {
            address_list[i].is_default = 1;
          } else {
            address_list[i].is_default = 0;
          }
          page.setData({
            address_list: address_list,
          });
        }
      }
    });
  },

  deleteAddress: function (e) {
    var page = this;
    var guid = e.currentTarget.dataset.guid;
    var index = e.currentTarget.dataset.index;
    wx.showModal({
      title: "提示",
      content: "确认删除该收货地址？",
      success: function (res) {
        if (res.confirm) {
          app.request({
            url: api.user.address_delete,
            data: {
              guid: guid
            },
            success: function (res) {
              wx.showToast({
                title: res.msg,
                icon: 'success',
                duration: 500
              });
              setTimeout(function () {
                page.loadList();
              }, 500) //延迟时间
            }
          });
        }
      }
    });
  },
});