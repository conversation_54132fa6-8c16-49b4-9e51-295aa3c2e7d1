/* pages/address/address.wxss */
.bottom-bar{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}
.bottom-bar navigator{
    background: #ff4544;
    text-align: center;
    height: 100rpx;
    line-height: 100rpx;
    color: #fff;
}
.address-list{
    padding-bottom: 100rpx;
}

.address-item{
    background: #fff;
    margin-bottom: 20rpx;
    padding: 32rpx 24rpx;
}
.userinfo{
    margin-bottom: 24rpx;
}
.address-detail{
    border-bottom: 1rpx solid #eee;
    margin-bottom: 24rpx;
    padding-bottom: 24rpx;
}
.address-option{
    margin-left: 48rpx;
}
.address-option image{
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
}

.default-address{
    width: auto;
}

.default-address.active{
    color: #fe6e00;
}