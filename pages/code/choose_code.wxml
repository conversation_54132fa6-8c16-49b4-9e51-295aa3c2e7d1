<block wx:if="{{show_tab_bar==1}}">
  <import src="/common/tabbar/tabbar.wxml" />
  <template is="tabbar" data="{{tabbar}}" />
</block>
<block wx:if="{{available_use_num==0}}">
  <view class="weui-msg" style="background-color: #eee;">
    <view class="weui-msg__icon-area"><text style="width: 5rem;height:5rem;" class="weui-icon-info weui-icon_msg"></text></view>
    <view class="weui-msg__text-area">
      <view class="weui-msg__title">您没有可用的提货卡</view>
    </view>
  </view>
</block>

<view class="goods-list flex-row goods-list-cols-2">
  <view class="flex-grow-0" wx:for="{{list}}" wx:for-index="goods_index" wx:for-item="goods">
    <view hover-class="none" class="goods-item" bindtap="goto_detail" data-guid="{{goods.guid}}">
      <image src="{{goods.pic}}" mode="aspectFill" />
      <text class="text-more-2" style='padding:8rpx 20rpx;line-height:1.4;font-size: 28rpx;'>{{goods.name}}</text>
    </view>
  </view>
</view>
<view style="height: {{show_tab_bar==1 ? 250 : 160 }}rpx;"></view>
<view style="position: fixed;bottom: 0;width: 100%;padding-bottom: {{show_tab_bar==1 ? 150 : 10 }}rpx;text-align: center;">
  <button style="width: 80%;" class="weui-btn weui-btn_primary" id="js_btn" aria-role="button" bindtap="show_dialog">绑定卡券</button>
</view>
<view aria-role="dialog" aria-modal="true">
  <view class="weui-mask weui-transition {{dialog ? 'weui-transition_show' : ''}}" bindtap="close" aria-role="button" aria-label="关闭"></view>
  <view class="weui-half-screen-dialog weui-half-screen-dialog_large weui-transition {{dialog ? 'weui-transition_show' : ''}}">
    <view class="weui-half-screen-dialog__hd">
      <view class="weui-half-screen-dialog__hd__side" bindtap="close">
        <view aria-role="button" class="weui-icon-btn">关闭<i class="weui-icon-close-thin"></i></view>
      </view>
      <view class="weui-half-screen-dialog__hd__main">
        <strong class="weui-half-screen-dialog__title">绑定卡密</strong>
      </view>
    </view>
    <view class="weui-half-screen-dialog__bd" style="padding-top: 32px; height: 50px;">

      <text style="color: #999;padding-left: 40rpx;"> 您可以将实体卡绑定至您的账户 </text>

      <form bindsubmit="bind_code">

        <view class="weui-cells weui-cells_after-title">

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">手机号</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="number" bindinput="phoneChange" name='phone' value="{{phone}}" placeholder="请输入手机号" />
            </view>

          </view>

          <view class="weui-cell">
            <view class="weui-cell__hd">
              <view class="weui-label">验证码</view>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" cursor-spacing="0" type="number" name='verify_code' value="{{verify_code}}" focus="{{verify_code_focus}}" placeholder="输入验证码" />
            </view>
            <view aria-role="button" class="weui-cell__control weui-btn weui-btn_default   vcode-btn" bindtap="get_verify_code">{{get_verify_code_text}}</view>
          </view>

        </view>



        <view class="weui-btn-area" style="margin: 20px 0 0 0;">
          <button class='weui-btn' style="width: 90%;" type="primary" formType="submit">确认绑定</button>
        </view>
      </form>
    </view>
    <view class="weui-half-screen-dialog__ft">
      <view class="weui-half-screen-dialog__btn-area">
        <view aria-disabled="true" class="weui-btn weui-btn_default" aria-role="button" bindtap="close">暂不绑定</view>
      </view>
    </view>
  </view>
</view>