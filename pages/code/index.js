// pages/index/index.js
var app = getApp(); //获取应用实例
Page({

  /**
   * 页面的初始数据
   */
  data: {
    coupon_guid: '',
    goods_guid: '',
    member: {},
    config: {},
    code: '',
    password: '',
    phone: '',
    verify_code: '',
    get_verify_code_text: '获取验证码', //倒计时 
    currentTime: 60, //限制60s
    is_allow_get_verify_code: true, //获取验证码按钮，默认允许点击
    verify_code_focus: false,
    password_focus: false,
    dialog: false,
    show_pick_up_type: 0, //当前模式 1 卡号密码 2 手机号加验证码
    member_group: {},
    banner_config: {
      indicatorDots: true, //小点
      indicatorColor: "white", //指示点颜色
      activeColor: "coral", //当前选中的指示点颜色
      autoplay: true, //是否自动轮播
      interval: 3000, //间隔时间
      duration: 500, //滑动时间
    },
    banner: {},
  },

  close: function (e) {
    this.setData({
      dialog: false
    })
  },
  show_code_password: function (e) {
    this.setData({
      show_pick_up_type: 1
    })
  },
  show_phone_verify_code: function (e) {
    this.setData({
      show_pick_up_type: 2
    })
  },

  bindchange: function (e) {
    this.setData({
      current: e.detail.current
    })
  },
  codeInput: function (e) {
    const value = e.detail.value;
    // 允许中文、字母、数字的正则表达式
    const filteredValue = value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
    this.setData({
      code: filteredValue
    });
  },
  scan: function (e) {
    console.log('index - scan')
    var that = this;
    wx.scanCode({
      success(res) {
        app.tools.parse_qrcode(that, res.result);
        that.setData({
          // code: res.result,
          password_focus: true,
        });
      },
      fail(res) {
        console.log(res);
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('index - onLoad')
    console.log(options)
    app.pageOnLoad(this, options);
    if (options.q !== undefined) {
      app.tools.parse_qrcode(this, decodeURIComponent(options.q));
    }
    if (options) {
      console.log('options - options')
    }
    if (options.code !== undefined) {
      this.setData({
        code: options.code
      });
    }

    if (options.goods_guid !== undefined) {
      this.setData({
        goods_guid: options.goods_guid
      });
    }

    if (options.coupon_guid !== undefined) {
      this.setData({
        coupon_guid: options.coupon_guid
      });
    }

    var that = this;

    app.editTabBar();
    app.request({
      url: app.api.code.config,
      data: options,
      success: function (res) {
        console.log(res);
        that.setData({
          banner: res.data.banner_list,
          config: res.data
        });

        if (res.data.pick_up_type < 3) {
          that.setData({
            show_pick_up_type: res.data.pick_up_type
          })
        } else {
          that.setData({
            show_pick_up_type: 1
          })
        }
        wx.setNavigationBarTitle({
          title: res.data.title
        })
        if (res.data.pick_up_notice_pop) {
          that.setData({
            dialog: true
          });
        }
      },
    });
  },
  get_verify_code: function () {
    if (this.data.is_allow_get_verify_code == false) {
      console.log('当前不允许获取');
      return false;
    }
    var that = this;
    if (!that.data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    //调用接口
    app.request({
      url: app.api.user.send_sms_code,
      data: {
        mobile: that.data.phone,
      },
      success: function (result) {
        console.log(result);
        that.setData({
          is_allow_get_verify_code: false,
          verify_code_focus: true
        })
        wx.showToast({
          title: result.msg,
        })
        that.doLoop();
      },
      fail: function (result) {
        console.log('fail');
        console.log(result);
        that.setData({
          is_allow_get_verify_code: true
        })
      },
    });
  },
  doLoop: function () {
    let that = this;
    // 60s倒计时 setInterval功能用于循环，常常用于播放动画，或者时间显示
    var currentTime = that.data.currentTime;
    let interval = setInterval(function () {
      currentTime--; //减
      that.setData({
        get_verify_code_text: currentTime + '秒后获取'
      })
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          get_verify_code_text: '获取验证码',
          currentTime: 60,
          is_allow_get_verify_code: true
        })
      }
    }, 1000);
  },
  phoneChange: function (event) {
    console.log("password==", event.detail.value)
    this.setData({
      phone: event.detail.value
    })
  },
  exchange: function (e) {
    var that = this;
    let data = e.detail.value;
    console.log(data);
    let show_pick_up_type = that.data.show_pick_up_type;
    data.pick_up_type = show_pick_up_type;
    if (show_pick_up_type == 1) {
      if (!data.code) {
        wx.showModal({
          title: '系统提示',
          content: '请输入卡号!',
          showCancel: false,
        });
        return false;
      }
      if (!data.password) {
        wx.showModal({
          title: '系统提示',
          content: '请输入密码!',
          showCancel: false,
        });
        return false;
      }
    }

    if (show_pick_up_type == 2) {
      if (!data.phone) {
        wx.showModal({
          title: '系统提示',
          content: '请输入手机号!',
          showCancel: false,
        });
        return false;
      }
      if (!data.verify_code) {
        wx.showModal({
          title: '系统提示',
          content: '请输入验证码!',
          showCancel: false,
        });
        return false;
      }
    }
    if (that.data.goods_guid) {
      data.goods_guid = that.data.goods_guid;
    }
    app.request({
      url: app.api.code.verify_code,
      data: data,
      success: function (result) {
        console.log(result);
        if (result.data.path) {
          wx.navigateTo({
            url: result.data.path
          })
          return;
        }
        if (result.data.status == 1) {
          //选择商品
          let coupon_type = result.data.data.type;
          let path = coupon_type == 4 ? '/pages/code/gift' : '/pages/code/detail';
          wx.navigateTo({
            url: path + '?bid=' + app.ext_config.bid + '&token=' + result.data.data.token,
          })
        } else if (result.data.status == 2) {
          //订单详情
          wx.navigateTo({
            url: '/pages/order/detail?bid=' + app.ext_config.bid + '&order_guid=' + result.data.data.order_guid
          })
        } else if (result.data.status == 3) {
          //提交订单
          let choose_goods_list = {};
          choose_goods_list[result.data.data.goods_guid] = 1;
          wx.setStorageSync('choose_goods_list', choose_goods_list);
          wx.redirectTo({
            url: '/pages/code/submit_order?bid=' + app.ext_config.bid + '&token=' + result.data.data.token,
          })
        }
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.log('index - onReady')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('index - onShow')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.log('index - onHide')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.log('index - onUnload')
  },


  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('index - onReachBottom')
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
  /**
   * 页面滚动触发事件的处理函数
   */
  // onPageScroll: function(res) {
  //   //console.log(res)
  //   console.log('index - onPageScroll')
  // },
  /**
   * 页面尺寸改变时触发
   */
  onResize: function (res) {
    console.log(res)
    console.log('index - onResize')
  },
  /**
   * 当前是 tab 页时，点击 tab 时触发
   */
  onTabItemTap: function (res) {
    console.log(res)
    console.log('index - onTabItemTap')
  },
})