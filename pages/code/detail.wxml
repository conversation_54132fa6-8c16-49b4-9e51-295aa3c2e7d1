<!--pages/code/choose_goods.wxml-->
<view class="detail-container">
  <!-- ===== 访问模式提示区域 ===== -->
  <view class="section section-hint">
    <view class="access-mode-hint {{accessModeHint.type}}" wx:if="{{accessModeHint.show}}" bindtap="hideAccessModeHint">
      <view class="hint-content">
        <text class="hint-icon" wx:if="{{accessModeHint.type === 'success'}}">✓</text>
        <text class="hint-icon" wx:if="{{accessModeHint.type === 'info'}}">ℹ</text>
        <text class="hint-icon" wx:if="{{accessModeHint.type === 'warning'}}">⚠</text>
        <text class="hint-message">{{accessModeHint.message}}</text>
      </view>
    </view>
  </view>

  <!--商城公告 开始（置顶显示）-->
  <block wx:if="{{config.choose_goods_notice}}">
    <view class='section section-notice'>
      <view class='notice-bar flex-row' bindtap='showNotice'>
        <view class='flex-grow-0 flex-y-center notice-a'>
          <image src='/images/icon-notice.png' class="notice-icon"></image>
          <view>公告：</view>
        </view>
        <view class='flex-grow-1 notice-box'>
          <view class='notice-content'>
            <view class="notice-text">{{config.choose_goods_notice}}</view>
          </view>
        </view>
        <view class='flex-grow-0 flex-y-center notice-b'>
          <image src='/images/icon-notice-jiantou.png' class="notice-arrow"></image>
        </view>
      </view>
    </view>
    <view class='notice-modal {{show_notice?"":"hidden"}}'>
      <view class='notice-container'>
        <!-- 绿色主题头部 -->
        <view class='notice-header'>
          <view class='notice-header-icon'>📢</view>
          <view class='notice-header-title'>公告</view>
          <view class='notice-close-btn' bindtap='closeNotice'>✕</view>
        </view>

        <view class='notice-body'>
          <scroll-view scroll-y class='notice-content-scroll'>
            <rich-text nodes="{{config.choose_goods_notice}}"></rich-text>
          </scroll-view>
          <view class='flex-x-center'>
            <view class='notice-btn flex-x-center flex-y-center' bindtap='closeNotice'>我知道了</view>
          </view>
        </view>
      </view>
    </view>
  </block>
  <!--商城公告 结束-->

  <!-- 公共轮播图组件 -->
  <view class="section section-banner">
    <swipers banner_list="{{config.banner_list}}"></swipers>
  </view>

  <view class="section section-card" wx:if="{{hasList}}">
    <view class="card-info-block">
      <view class="card-info-cell">
        <!-- 卡片装饰条纹 -->
        <view class="card-stripe"></view>

        <!-- 卡片主要内容 -->
        <view class="card-content">
          <!-- 卡片标题区域 -->
          <view class="card-title-section">
            <text class="card-title">{{data.coupon_info.name || (exchange_goods_type == 1 ? '提货卡' : '储值卡')}}</text>
            <view class="card-chip"></view>
          </view>

          <!-- 卡片数值区域 -->
          <view class="card-value-section">
            <view wx:if="{{exchange_goods_type==1}}" class="card-value-line">
              <text class="card-label">可用</text>
              <text class="highlight-value">{{data.available_num}}</text>
              <text class="card-unit">次</text>
            </view>
            <view wx:if="{{exchange_goods_type==2}}" class="card-value-line">
              <text class="card-label">可用</text>
              <text class="highlight-value">{{data.available_value}}</text>
              <text class="card-unit">{{data.money_unit}}</text>
            </view>
          </view>

          <!-- 卡片有效期区域 -->
          <view class="card-expire-section">
            <text class="card-expire">有效期至 {{data.expire_time}}</text>
          </view>
        </view>

        <!-- 卡片装饰圆点 -->
        <view class="card-dots">
          <view class="card-dot"></view>
          <view class="card-dot"></view>
          <view class="card-dot"></view>
        </view>
      </view>
    </view>

    <!-- 卡券简介区块（使用后端富文本） -->
    <view class="card-intro-block" wx:if="{{data.coupon_info.simple_description}}">
      <view class="weui-cell card-intro-cell">
        <view class="weui-cell__bd">
          <rich-text class="intro-richtext" nodes="{{data.coupon_info.simple_description}}"></rich-text>
        </view>
      </view>
    </view>

    <!-- 商品选择提示 -->
    <view class="selection-hint-block" wx:if="{{!data.coupon_info.simple_description}}">
      <view class="weui-cells weui-cells_after-title">
        <block wx:if="{{exchange_goods_type== 1 &&  (template_type == 1 || exchange_goods_num > 1 ) }}">
          <view class="selection-header">
            <text class="selection-text">
              请在以下商品中 选 {{exchange_goods_num}} 种
            </text>
          </view>
        </block>
        <block wx:if="{{exchange_goods_type==2}}">
          <view class="selection-header">
            <text class="selection-text">
              请在以下商品中 选 {{exchange_goods_value}} {{data.money_unit}}
            </text>
          </view>
        </block>
      </view>
    </view>

    <!-- 公共分类切换Tab（有多个分类时显示） -->
    <view wx:if="{{showCategoryTabs}}" class="category-tabs-block">
      <view class="custom-category-tabs">
        <view class="tab-container">
          <view class="tab-item {{activeCategory === 'all' ? 'active' : ''}}"
                bindtap="changeCategory" data-category="all">全部
          </view>
          <view wx:for="{{data.goods_category_list}}" wx:for-item="category" wx:key="guid"
                class="tab-item {{activeCategory === category.guid ? 'active' : ''}}"
                bindtap="changeCategory" data-category="{{category.guid}}">{{category.name}}</view>
        </view>
      </view>
    </view>

    <!-- 模板2：网格商品列表 -->
    <view class="goods-grid-section" wx:if="{{template_type==2}}">
      <view class="goods-list goods-list-cols-2">
        <view class="goods-grid-item" wx:for="{{goods_list}}" wx:for-item="goods" wx:key="index"
              data-index="{{index}}" data-guid="{{goods.guid}}"
              style="{{goods.categoryVisible === false ? 'display: none;' : ''}}">
          <view class="goods-item">
            <!-- 商品图片区域 -->
            <view class="goods-image-container {{goods.stock_mode==1 && goods.stock <= 0 ? 'out-of-stock' : ''}}"
                  bindtap="goto_goods_detail" data-index="{{index}}" data-guid="{{goods.guid}}">
              <view class="replenish-badge" wx:if="{{goods.stock_mode==1 && goods.stock <= 0}}">
                <image src="{{data.config.replenish_image_url}}" mode="widthFix" class="replenish-image"/>
              </view>
              <image src="{{goods.pic}}" mode="aspectFill" class="goods-image"/>
            </view>

            <!-- 商品名称 -->
            <view class="goods-name-container" bindtap="goto_goods_detail" data-index="{{index}}" data-guid="{{goods.guid}}">
              <text class="goods-name text-more-2">{{goods.name}}</text>
            </view>

            <!-- 规格信息 -->
            <view class="goods-specs-container">
              <!-- 已选择的SKU信息显示 -->
              <view class="goods-specs-tag" wx:if="{{goods.is_attribute == 1 && goods.selected_specs}}"
                    catchtap="openSkuModal" data-index="{{index}}">
                已选: {{goods.selected_specs}}
              </view>

              <!-- 原有规格信息显示（兼容无SKU商品） -->
              <view class="goods-specs-tag" wx:if="{{goods.specs && goods.is_attribute != 1}}">
                {{goods.specs}}
              </view>

              <!-- 库存信息 -->
              <view class="goods-stock-info"
                    wx:if="{{goods.stock_mode == 1 && data.config.choose_goods_show_stock==1}}">
                库存 {{goods.stock}}
              </view>
            </view>

            <!-- 价格信息 -->
            <view class="goods-price-container" wx:if="{{data.config.choose_goods_show_price==1 && goods.price>0}}">
              <view class="goods-price">
                <text class="price-symbol">￥</text>
                <text class="price-value">{{goods.price}}</text>
                <text class="price-original"
                      wx:if="{{goods.price<goods.original_price}}">{{goods.original_price}}</text>
              </view>
            </view>

            <!-- 购物车控制按钮 -->
            <view class="goods-cart-controls" catchtap="stopPropagation">
              <!-- 当数量为0时，只显示加号按钮 -->
              <view wx:if="{{!goods.num || goods.num == 0}}" class="add-only-container">
                <view catchtap="{{(goods.stock_mode == 0 || (goods.is_attribute == 1 ? (goods.current_sku_stock > 0) : (goods.stock > 0))) ? 'addCount' : ''}}"
                      class="cart-btn-add-only {{(goods.stock_mode == 0 || (goods.is_attribute == 1 ? (goods.current_sku_stock > 0) : (goods.stock > 0))) ? 'active' : 'disabled'}}"
                      data-index="{{index}}" data-guid="{{goods.guid}}">
                  <image class="btn-icon" src="/images/add.svg" mode="aspectFit"></image>
                </view>
              </view>

              <!-- 当数量大于0时，显示完整的数量控制 -->
              <view wx:else class="full-cart-controls">
                <!-- 减号按钮 -->
                <view catchtap="minusCount" class="cart-btn minus"
                      data-index="{{index}}" data-guid="{{goods.guid}}">
                  <image class="btn-icon" src="/images/less.svg" mode="aspectFit"></image>
                </view>

                <!-- 数量显示 -->
                <view class="cart-num" wx:if="{{goods.enable_input==0}}">
                  {{goods.num}}
                </view>
                <view class="cart-num cart-num-clickable" wx:if="{{goods.enable_input==1}}"
                      catchtap="showQuantityInput" data-index="{{index}}" data-guid="{{goods.guid}}">
                  {{goods.num}}
                </view>

                <!-- 加号按钮 -->
                <view catchtap="{{(goods.num<goods.max_choose_num ) && (goods.stock_mode == 0 || (goods.is_attribute == 1 ? (goods.current_sku_stock > goods.num) : (goods.stock > goods.num))) ? 'addCount' : ''}}"
                      class="cart-btn plus {{(goods.num<goods.max_choose_num ) && (goods.stock_mode == 0 || (goods.is_attribute == 1 ? (goods.current_sku_stock > goods.num) : (goods.stock > goods.num))) ? 'active' : 'disabled'}}"
                      data-index="{{index}}" data-guid="{{goods.guid}}">
                  <image class="btn-icon" src="/images/add.svg" mode="aspectFit"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 模板1：列表商品布局 -->
    <view class="goods-list-section" wx:if="{{template_type==1}}">
      <view class="goods-list-items">
        <view class="goods-list-item" wx:for="{{goods_list}}" wx:key="index"
              data-index="{{index}}" data-guid="{{item.guid}}"
              style="{{item.categoryVisible === false ? 'display: none;' : ''}}">

          <!-- 商品图片 -->
          <view class="goods-list-image" data-index="{{index}}" data-guid="{{item.guid}}"
                bindtap="goto_goods_detail">
            <image class="goods-thumb" src="{{item.pic}}" mode="aspectFill"></image>
          </view>

          <!-- 商品信息 -->
          <view class="goods-list-info">
            <!-- 商品名称 -->
            <view class="goods-list-name" bindtap="goto_goods_detail" data-index="{{index}}" data-guid="{{item.guid}}">
              {{item.name}}
            </view>

            <!-- 价格信息 -->
            <view class="goods-list-price" wx:if="{{data.config.choose_goods_show_price==1 && item.price > 0}}">
              <text class="price-value">￥{{item.price}}</text>
              <text class="unit-text" wx:if="{{item.unit}}">/{{item.unit}}</text>
              <text class="original-price" wx:if="{{item.price<item.original_price}}">{{item.original_price}}</text>
            </view>

            <!-- 规格信息 -->
            <view class="goods-list-specs">
              <!-- 已选择的SKU信息显示 -->
              <view class="specs-tag" wx:if="{{item.is_attribute == 1 && item.selected_specs}}"
                    catchtap="openSkuModal" data-index="{{index}}">
                已选: {{item.selected_specs}}
              </view>

              <!-- 原有规格信息显示（兼容无SKU商品） -->
              <view class="specs-tag" wx:if="{{item.specs && item.is_attribute != 1}}"
                    catchtap="goto_goods_detail" data-index="{{index}}" data-guid="{{item.guid}}">
                {{item.specs}}
              </view>

              <!-- 库存信息 -->
              <view class="stock-info" wx:if="{{item.stock_mode == 1 && data.config.choose_goods_show_stock}}">
                库存 {{item.stock}}
              </view>
            </view>

            <!-- 查看详情链接 -->
            <!-- <view class="view-detail-link" bindtap="goto_goods_detail" data-index="{{index}}" data-guid="{{item.guid}}">
              查看详情
            </view> -->

            <!-- 数量控制 -->
            <view class="quantity-control" catchtap="stopPropagation">
              <!-- 当数量为0时，只显示加号按钮 -->
              <view wx:if="{{!item.num || item.num == 0}}" class="add-only-container">
                <view catchtap="{{(item.stock_mode == 0 || (item.is_attribute == 1 ? (item.current_sku_stock > 0) : (item.stock > 0))) ? 'addCount' : ''}}"
                      class="quantity-btn-add-only {{(item.stock_mode == 0 || (item.is_attribute == 1 ? (item.current_sku_stock > 0) : (item.stock > 0))) ? 'active' : 'disabled'}}"
                      data-index="{{index}}">
                  <image class="btn-icon" src="/images/add.svg" mode="aspectFit"></image>
                </view>
              </view>

              <!-- 当数量大于0时，显示完整的数量控制 -->
              <view wx:else class="full-quantity-controls">
                <!-- 减号按钮 -->
                <view catchtap="minusCount" class="quantity-btn minus" data-index="{{index}}">
                  <image class="btn-icon" src="/images/less.svg" mode="aspectFit"></image>
                </view>

                <!-- 数量显示 -->
                <view class="quantity-num" wx:if="{{item.enable_input==0}}">
                  {{item.num}}
                </view>
                <view class="quantity-num quantity-num-clickable" wx:if="{{item.enable_input==1}}"
                      catchtap="showQuantityInput" data-index="{{index}}">
                  {{item.num}}
                </view>

                <!-- 加号按钮 -->
                <view catchtap="{{(item.num<item.max_choose_num ) && (item.stock_mode == 0 || (item.is_attribute == 1 ? (item.current_sku_stock > item.num) : (item.stock > item.num))) ? 'addCount' : ''}}"
                      class="quantity-btn plus {{(item.num<item.max_choose_num ) && (item.stock_mode == 0 || (item.is_attribute == 1 ? (item.current_sku_stock > item.num) : (item.stock > item.num))) ? 'active' : 'disabled'}}"
                      data-index="{{index}}">
                  <image class="btn-icon" src="/images/add.svg" mode="aspectFit"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 使用购物车组件替代原有的底部栏 -->
  <view class="cart-bar-container">
    <cart-bar
            cart-goods="{{cartGoodsArray}}"
            total-count="{{totalNum}}"
            total-price="{{totalPrice}}"
            confirm-button-text="{{data.coupon_info && data.coupon_info.type == 4 ? '确认选择' : (access_mode === 'token' ? '下一步' : '验证后购买')}}"
            confirm-button-color="{{access_mode === 'token' ? 'coral' : '#1890ff'}}"
            show-price="{{data.config.choose_goods_show_price==1 || exchange_goods_type==2}}"
            bind:confirm="submitOrder"
            bind:countChange="onCartCountChange"
            bind:clear="clearCart"
    />
  </view>

</view>





<view aria-role="dialog" aria-modal="true" class="fadeIn" wx:if="{{notice_dialog}}">
  <view class="weui-mask"></view>
  <view class="weui-dialog" style="width: auto;">
    <view class="weui-dialog__hd" style="padding: 20rpx 0 20rpx 0"><strong class="weui-dialog__title">温馨提示</strong>
    </view>
    <view class="weui-dialog__bd" style="padding:0 10rpx 0 10rpx;margin: 0;">
      <rich-text style="text-align: left;" nodes="{{config.choose_goods_notice_pop}}"></rich-text>
    </view>
    <view class="weui-dialog__ft">
      <view aria-role="button" class="weui-dialog__btn weui-dialog__btn_primary" bindtap="close">知道了</view>
    </view>
  </view>

</view>

<!-- SKU选择器组件 - 移到外层，确保能正常显示 -->
<sku-selector
        visible="{{showSkuSelector}}"
        product="{{currentSkuProduct}}"
        validateFunctionName="validateSkuSelection"
        bind:confirm="onSkuConfirm"
        bind:cancel="onSkuCancel"
        bind:close="closeSkuModal"
/>

<!-- ===== 验证弹窗 ===== -->
<view class="verify-modal-overlay" wx:if="{{showVerifyModal}}" bindtap="hideVerifyModal">
  <view class="verify-modal" catchtap="">
    <!-- 弹窗头部 -->
    <view class="verify-modal-header">
      <view class="verify-modal-title">卡密验证</view>
      <view class="verify-modal-close" bindtap="hideVerifyModal">×</view>
    </view>

    <!-- 弹窗内容 -->
    <view class="verify-modal-content">
      <view class="verify-form">
        <!-- 卡号输入 -->
        <view class="verify-form-item">
          <view class="verify-label">卡号</view>
          <input
                  class="verify-input"
                  placeholder="请输入卡号"
                  value="{{verifyForm.code}}"
                  bindinput="onVerifyCodeInput"
                  maxlength="50"
          />
        </view>

        <!-- 密码输入 -->
        <view class="verify-form-item">
          <view class="verify-label">密码</view>
          <input
                  class="verify-input"
                  placeholder="请输入密码"
                  password="{{true}}"
                  value="{{verifyForm.password}}"
                  bindinput="onVerifyPasswordInput"
                  maxlength="50"
          />
        </view>

        <!-- 提交按钮 -->
        <view class="verify-form-item">
          <button class="verify-submit-btn" bindtap="submitVerify">
            确认验证
          </button>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 数量输入弹框 -->
<view class="quantity-modal" wx:if="{{showQuantityModal}}" catchtap="hideQuantityInput">
  <view class="quantity-modal-content" catchtap="stopPropagation">
    <view class="quantity-modal-header">
      <text class="quantity-modal-title">输入数量</text>
      <image class="quantity-modal-close" src="/images/icon-close.png" catchtap="hideQuantityInput"></image>
    </view>

    <view class="quantity-modal-body">
      <view class="quantity-input-wrapper">
        <input class="quantity-modal-input"
               type="number"
               placeholder="请输入数量"
               value="{{inputQuantity}}"
               bindinput="onQuantityModalInput"
               focus="{{quantityInputFocus}}"/>
      </view>

      <view class="quantity-tips">
        <text wx:if="{{currentInputProduct.min_choose_num}}">最少：{{currentInputProduct.min_choose_num}}件</text>
        <text wx:if="{{currentInputProduct.max_choose_num}}">最多：{{currentInputProduct.max_choose_num}}件</text>
      </view>
    </view>

    <view class="quantity-modal-footer">
      <button class="quantity-modal-cancel" catchtap="hideQuantityInput">取消</button>
      <button class="quantity-modal-confirm" catchtap="confirmQuantityInput">确定</button>
    </view>
  </view>
</view>
