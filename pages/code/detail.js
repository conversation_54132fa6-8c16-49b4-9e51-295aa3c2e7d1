// pages/code/detail.js
const app = getApp();

// 缓存key常量
const CACHE_KEY = 'choose_goods_info'; //获取应用实例

// 常量定义
const STOCK_MODE = {
  UNLIMITED: 0, // 不限制库存
  LIMITED: 1    // 限制库存
};

Page({
  /**
   * 页面的初始数据
   */
  data: {
    show_notice: false, //滚动公告
    choose_goods_list: {},
    choose_goods_info: [], // 包含SKU信息的详细格式（与H5端保持一致）
    choose_goods_value: 0,

    template_type: 1,
    exchange_goods_type: 0,
    exchange_goods_num: 0,
    exchange_goods_value: 0,
    max_exchange_num_once: 1, //单次可提货倍数
    max_exchange_value_once: 0, //单次最大可提货产品金额
    goods_list: [], //可选商品列表
    goods_list_num: 0,
    goods_detail: "",

    notice_dialog: false,
    data: {},
    config: {},
    token: "",
    hasList: false, // 列表是否有数据
    totalPrice: 0, // 总价，初始为0
    totalNum: 0, // 总数量，初始为0
    button_background_color: "#999",
    selectAllStatus: true, // 全选状态，默认全选
    obj: {
      name: "hello",
    },
    banner_config: {
      indicatorDots: true, //小点
      indicatorColor: "white", //指示点颜色
      activeColor: "coral", //当前选中的指示点颜色
      autoplay: true, //是否自动轮播
      interval: 3000, //间隔时间
      duration: 500, //滑动时间
    },

    // SKU相关状态管理
    showSkuSelector: false, // SKU选择器显示状态
    currentSkuProduct: null, // 当前选择SKU的商品对象
    currentSkuProductIndex: -1, // 当前选择SKU的商品在列表中的索引

    // 购物车相关数据
    cartGoodsArray: [], // 购物车商品数组，用于购物车组件展示

    // ===== 新增：多场景支持相关数据 =====
    access_mode: '', // 访问模式：token/code/coupon_guid
    bid: '', // 商户ID
    code: '', // 卡号（code模式使用）
    guid: '', // 券卡GUID（guid模式使用）
    coupon_send_note_guid: '', // 券卡发送记录GUID


    // 验证弹窗相关
    showVerifyModal: false, // 是否显示验证弹窗
    verifyForm: {
      code: '',
      password: ''
    },

    // 访问模式提示
    accessModeHint: {
      show: false,
      message: '',
      type: 'info' // info/success/warning
    },

    // 数量输入弹框相关
    showQuantityModal: false, // 是否显示数量输入弹框
    inputQuantity: '', // 输入的数量
    currentInputProduct: null, // 当前输入数量的商品
    currentInputIndex: -1, // 当前输入数量的商品索引
    quantityInputFocus: false, // 输入框是否聚焦

    // ===== 新增：分类切换功能 =====
    activeCategory: 'all', // 当前激活的分类，默认为"全部"
    showCategoryTabs: false, // 是否显示分类切换标签

  },

  /**
   * 显示公告弹窗
   */
  showNotice: function () {
    this.setData({
      show_notice: true,
    });
  },

  /**
   * 关闭公告弹窗
   */
  closeNotice: function () {
    this.setData({
      show_notice: false,
    });
  },
  /**
   * 当前商品选中事件
   */
  selectList(e) {
    const index = e.currentTarget.dataset.index;
    let goods_list = this.data.goods_list;
    const selected = goods_list[index].selected;
    goods_list[index].selected = !selected;
    this.setData({
      goods_list: goods_list,
    });
    this.getTotalPrice();
  },

  /**
   * 删除购物车当前商品
   */
  deleteList(e) {
    const index = e.currentTarget.dataset.index;
    let goods_list = this.data.goods_list;
    goods_list.splice(index, 1);
    this.setData({
      goods_list: goods_list,
    });
    if (!goods_list.length) {
      this.setData({
        hasList: false,
      });
    } else {
      this.getTotalPrice();
    }
  },

  /**
   * 购物车全选事件
   */
  selectAll(e) {
    let selectAllStatus = this.data.selectAllStatus;
    selectAllStatus = !selectAllStatus;
    let goods_list = this.data.goods_list;

    for (let i = 0; i < goods_list.length; i++) {
      goods_list[i].selected = selectAllStatus;
    }
    this.setData({
      selectAllStatus: selectAllStatus,
      goods_list: goods_list,
    });
    this.getTotalPrice();
  },
  /**
   * 通用校验函数 - 可用于SKU选择器和普通商品加减数量
   * @param {Object} params 校验参数
   * @param {Object} params.product 商品对象
   * @param {Number} params.num 要设置的数量
   * @param {Number} params.index 商品在列表中的索引（可选）
   * @param {Number} params.currentPrice 当前价格（可选，SKU价格）
   * @returns {Boolean} 校验结果，true表示通过，false表示不通过
   */
  /**
   * 通用校验函数 - 可用于SKU选择器和普通商品加减数量
   * @param {Object} params 校验参数
   * @param {Object} params.product 商品对象
   * @param {Number} params.num 要设置的数量
   * @param {Number} params.index 商品在列表中的索引（可选）
   * @param {Number} params.currentPrice 当前价格（可选，SKU价格）
   * @returns {Boolean} 校验结果，true表示通过，false表示不通过
   */
  validateGoodsQuantity(params) {
    const { product, num, index = -1, currentPrice } = params;
    const goods_list = this.data.goods_list;

    // 获取商品的最小和最大购买数量限制
    const min_choose_num = product.min_choose_num;
    const max_choose_num = product.max_choose_num;

    // 对于多规格商品，检查是否已选择完整SKU
    if (product.is_attribute == 1 && num > 0) {
      if (!product.current_sku && !params.sku) {
        this._showErrorModal("请先选择商品规格");
        return false;
      }
    }

    // 检查最小数量限制
    if (num < min_choose_num) {
      this._showErrorModal(`该产品至少需要选择${min_choose_num}件哦`);
      return false;
    }

    // 动态计算最大可选择数量（考虑SKU库存限制）
    let effectiveMaxNum = max_choose_num;
    if (product.stock_mode == STOCK_MODE.LIMITED) {
      // 获取可用库存：优先使用SKU库存，其次使用商品库存
      const availableStock = this._getAvailableStock(product, params.sku);
      effectiveMaxNum = Math.min(max_choose_num, availableStock);
    }

    // 检查最大数量限制
    // 礼品卡（type == 4）只检查库存限制，不检查商品配置的数量限制
    if (this.data.data.coupon_info && this.data.data.coupon_info.type == 4) {
      // 礼品卡只检查库存限制
      if (product.stock_mode == STOCK_MODE.LIMITED) {
        const availableStock = this._getAvailableStock(product, params.sku);
        if (num > availableStock) {
          this._showErrorModal(`库存不足，该规格最多选择${availableStock}件`);
          return false;
        }
      }
    } else {
      // 普通卡券检查完整的数量限制
      if (num > effectiveMaxNum) {
        let message = "";
        if (product.stock_mode == STOCK_MODE.LIMITED && effectiveMaxNum < max_choose_num) {
          message = `库存不足，该规格最多选择${effectiveMaxNum}件`;
        } else {
          message = `该产品最多选择${effectiveMaxNum}件哦`;
        }
        this._showErrorModal(message);
        return false;
      }
    }

    // 如果提供了索引，进行总数量和总价格的校验
    if (index >= 0) {
      // 礼品卡（type == 4）跳过总量和总价限制验证
      if (!(this.data.data.coupon_info && this.data.data.coupon_info.type == 4)) {
        const befor_num = goods_list[index].num || 0;
        const tempGoodsList = JSON.parse(JSON.stringify(goods_list));
        tempGoodsList[index].num = num;

        // 检查总数量限制
        if (!this._validateTotalQuantity(tempGoodsList)) {
          return false;
        }

        // 检查总价格限制
        if (!this._validateTotalPrice(tempGoodsList)) {
          return false;
        }
      }
    }

    // 通过所有校验
    return true;
  },

  /**
   * 获取商品可用库存
   * @private
   */
  _getAvailableStock(product, sku) {
    if (sku && sku.stock !== undefined) {
      return sku.stock;
    }
    return product.current_sku_stock || product.stock || 0;
  },

  /**
   * 显示错误提示框
   * @private
   */
  _showErrorModal(content) {
    wx.showModal({
      title: "系统提示",
      content,
      showCancel: false,
    });
  },

  /**
   * 校验总数量限制
   * @private
   */
  _validateTotalQuantity(goodsList) {
    // 礼品卡（type == 4）不限制数量
    if (this.data.data.coupon_info && this.data.data.coupon_info.type == 4) {
      return true;
    }

    const total_goods_num = this.getTotalGoodsNum(goodsList);
    const max_num = this.data.exchange_goods_num * this.data.max_exchange_num_once;

    if (this.data.exchange_goods_type == 1 && total_goods_num > max_num && this.data.exchange_goods_num > 0) {
      this._showErrorModal(`您单次最多能选择${max_num}件产品哦`);
      return false;
    }
    return true;
  },

  /**
   * 校验总价格限制
   * @private
   */
  _validateTotalPrice(goodsList) {
    // 礼品卡（type == 4）不限制价格
    if (this.data.data.coupon_info && this.data.data.coupon_info.type == 4) {
      return true;
    }

    const tempPrice = this.getTotalGoodsPrice(goodsList);

    if (this.data.data.config.value_code_pick_up_limit_type != 2 &&
      this.data.exchange_goods_type == 2 &&
      tempPrice > this.data.max_exchange_value_once) {
      this._showErrorModal(`您单次最多能选择${this.data.max_exchange_value_once}元产品哦`);
      return false;
    }
    return true;
  },

  /**
   * 修改商品数量
   */
  changeNunmber(index, number) {
    let num = parseInt(number);
    let goods_list = this.data.goods_list;
    let product = goods_list[index];

    // 使用通用校验函数
    const validationResult = this.validateGoodsQuantity({
      product: product,
      num: num,
      index: index
    });

    if (validationResult === false) {
      return false;
    }

    // 校验通过，更新数量
    goods_list[index].num = num;
    this.setData({
      goods_list: goods_list,
    });
    this.getTotalPrice();
    return true;
  },

  /**
   * 更新商品的SKU相关状态
   */
  updateProductSkuState(product) {
    if (product.is_attribute == 1) {
      // 更新当前SKU
      let matchedSku = this.findMatchingSku(product);
      if (matchedSku) {
        product.current_sku = matchedSku;
        product.current_sku_stock = matchedSku.stock;
        product.selected_specs = this.generateSelectedSpecsText(product);
        // 重要：更新商品价格为SKU价格
        if (matchedSku.price !== undefined) {
          product.price = matchedSku.price;
        }
      } else {
        product.current_sku = null;
        product.current_sku_stock = 0;
        product.selected_specs = "";
        // 恢复原始价格（如果有保存的话）
        if (product.original_price !== undefined) {
          product.price = product.original_price;
        }
      }
    } else {
      // 单规格商品
      product.current_sku_stock = product.stock || 0;
    }
  },
  numberInput: function (e) {
    let index = e.currentTarget.dataset.index;
    let num = e.detail.value;
    num = parseInt(num);
    let goods_list = this.data.goods_list;
    let befor_num = goods_list[index].num;
    let result = this.changeNunmber(index, num);
    if (result === false) {
      return befor_num;
    }
  },
  // numberBlur: function (e) {
  //   let index = e.currentTarget.dataset.index;
  //   let num = e.detail.value;
  //   num = parseInt(num);
  //   this.changeNunmber(index, num);
  // },
  /**
   * 绑定减数量事件
   */
  minusCount(e) {
    let index = e.currentTarget.dataset.index;
    let guid = e.currentTarget.dataset.guid;

    // 根据模板类型获取商品信息
    let product = this.getProductByIndex(index, guid);
    if (!product) {
      console.error('未找到商品信息');
      return false;
    }

    let goods_list = this.data.goods_list;
    let num = product.num ? product.num : 0;

    if (num <= 0) {
      return false;
    }

    let result = this.changeNunmber(index, num - 1);

    // 如果数量变为0且是多规格商品，清除SKU选择状态
    if (result !== false && product.num === 0 && product.is_attribute == 1) {
      product.selected_attrs = {};
      product.current_sku = null;
      product.selected_specs = "";
      product.current_sku_stock = product.stock || 0;
      // 恢复原始价格
      if (product.original_price !== undefined) {
        product.price = product.original_price;
      }

      this.setData({
        goods_list: goods_list,
      });
    }

    return result;
  },

  /**
   * 绑定加数量事件
   */
  addCount(e) {
    console.log('=== addCount 被调用 ===');
    let index = e.currentTarget.dataset.index;
    let guid = e.currentTarget.dataset.guid;

    // 根据模板类型获取商品信息
    let product = this.getProductByIndex(index, guid);
    if (!product) {
      console.error('未找到商品信息');
      return false;
    }

    console.log('商品索引:', index);
    console.log('商品GUID:', guid);
    console.log('商品信息:', product);
    console.log('是否多规格 (is_attribute):', product.is_attribute);
    console.log('库存模式 (stock_mode):', product.stock_mode);
    console.log('当前访问模式:', this.data.access_mode);

    // 检查访问模式，非token模式需要验证
    if (this.data.access_mode !== 'token') {
      this.handleNonTokenSubmit();
      return false;
    }

    // 业务逻辑判断
    if (product.is_attribute == 1) {
      // 多规格商品 - 需要打开SKU选择弹窗
      console.log('多规格商品，打开SKU选择弹窗');
      this.openSkuModal(e);
      return;
    } else {
      // 单规格商品 - 先校验，通过后直接添加到购物车
      console.log('单规格商品，直接增加数量');
      let num = product.num ? product.num : 0;

      // 使用通用校验函数进行校验
      const validationResult = this.validateGoodsQuantity({
        product: product,
        num: num + 1,
        index: index
      });

      if (validationResult) {
        return this.changeNunmber(index, num + 1);
      } else {
        return false;
      }
    }
  },

  /**
   * 根据索引和GUID获取商品信息（统一数据结构）
   */
  getProductByIndex: function (index, guid) {
    const goods_list = this.data.goods_list;

    // 优先使用GUID查找（更准确）
    if (guid) {
      for (let i = 0; i < goods_list.length; i++) {
        if (goods_list[i].guid === guid) {
          return goods_list[i];
        }
      }
    }

    // 如果GUID查找失败或没有GUID，使用索引
    if (index >= 0 && index < goods_list.length) {
      return goods_list[index];
    }

    return null;
  },
  /**
   * 计算商品总数量
   */
  getTotalGoodsNum(goods_list) {
    return goods_list.reduce((total, goods) => {
      return goods.num > 0 ? total + goods.num : total;
    }, 0);
  },

  /**
   * 计算商品总价格
   */
  getTotalGoodsPrice(goods_list) {
    const totalPrice = goods_list.reduce((total, goods) => {
      return goods.num > 0 ? total + (goods.num * goods.price) : total;
    }, 0);
    return totalPrice.toFixed(2);
  },
  /**
   * 计算总价并更新购物车数据
   */
  getTotalPrice() {
    const goods_list = this.data.goods_list; // 获取购物车列表
    const selectedGoods = goods_list.filter(goods => goods.num > 0);

    // 计算总价和总数量
    const totalNum = this.getTotalGoodsNum(goods_list);
    const total = selectedGoods.reduce((sum, goods) => sum + (goods.num * goods.price), 0);

    // 构建购物车数据
    const cartGoodsArray = this._buildCartGoodsArray(selectedGoods);

    // 生成包含SKU信息的缓存数据（与H5端保持一致）
    this.generateCacheData(goods_list, selectedGoods);

    // 更新数据
    this.setData({
      totalNum,
      totalPrice: total.toFixed(2),
      cartGoodsArray,
      button_background_color: totalNum > 0 ? "coral" : "#999"
    });
  },


  /**
   * 构建购物车组件所需的商品数组
   * @private
   */
  _buildCartGoodsArray(selectedGoods) {
    const goods_list = this.data.goods_list;
    return selectedGoods.map((goods) => ({
      cart_item_id: goods.guid,
      goods_id: goods.guid,
      goods_name: goods.name,
      goods_pic: goods.pic,
      goods_price: goods.price,
      count: goods.num,
      sku_text: goods.selected_specs || goods.specs || '',
      index: goods_list.findIndex(item => item.guid === goods.guid)
    }));
  },

  /**
   * 购物车数量变化处理
   */
  onCartCountChange: function (e) {
    const { type, index, goods } = e.detail;
    const goodsIndex = goods.index; // 获取商品在goods_list中的索引

    if (type === 'plus') {
      // 增加数量
      this.addCount({
        currentTarget: {
          dataset: {
            index: goodsIndex
          }
        }
      });
    } else if (type === 'minus') {
      // 减少数量
      this.minusCount({
        currentTarget: {
          dataset: {
            index: goodsIndex
          }
        }
      });
    }
  },

  /**
   * 清空购物车
   */
  clearCart: function () {
    let goods_list = this.data.goods_list;

    // 重置所有商品数量为0
    for (let i = 0; i < goods_list.length; i++) {
      goods_list[i].num = 0;

      // 如果是多规格商品，清除SKU选择状态
      if (goods_list[i].is_attribute == 1) {
        goods_list[i].selected_attrs = {};
        goods_list[i].current_sku = null;
        goods_list[i].selected_specs = "";
        goods_list[i].current_sku_stock = goods_list[i].stock || 0;
        // 恢复原始价格
        if (goods_list[i].original_price !== undefined) {
          goods_list[i].price = goods_list[i].original_price;
        }
      }
    }

    this.setData({
      goods_list: goods_list
    });

    // 清空缓存数据
    this.setData({
      choose_goods_info: []
    });
    wx.removeStorageSync(CACHE_KEY);

    // 重新计算总价
    this.getTotalPrice();

    console.log("=== 购物车已清空，缓存已删除 ===");

    wx.showToast({
      title: '购物车已清空',
      icon: 'success'
    });
  },

  /**
   * 清除缓存并重新开始选择
   */
  clearCacheAndRestart: function () {
    wx.showModal({
      title: '确认重新选择',
      content: '确定要清空当前选择并重新开始吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除缓存
          wx.removeStorageSync(CACHE_KEY);

          // 清空购物车
          this.clearCart();

          wx.showToast({
            title: '已重新开始',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 生成缓存数据（与H5端保持完全一致的格式）
   */
  generateCacheData: function (goods_list, selectedGoods) {
    // 构建与H5端完全兼容的goods_info格式
    let goods_info_array = [];

    selectedGoods.forEach((goodsItem) => {
      if (goodsItem.num > 0) {
        let goods_info_item = {
          guid: goodsItem.guid,
          attr: [],
          amount: goodsItem.num,
        };

        // 如果是多规格商品，构建当前选中规格的attr数组
        if (goodsItem.is_attribute == 1 && goodsItem.attr_group_list && goodsItem.selected_attrs) {
          goodsItem.attr_group_list.forEach((group) => {
            const selectedAttrGuid = goodsItem.selected_attrs[group.attr_group_id];
            if (selectedAttrGuid) {
              const selectedAttr = group.attr_list.find((attr) => attr.attr_id === selectedAttrGuid);
              if (selectedAttr) {
                goods_info_item.attr.push({
                  attr_group_id: group.attr_group_id,
                  attr_group_name: group.attr_group_name,
                  attr_id: selectedAttr.attr_id,
                  attr_name: selectedAttr.attr_name,
                });
              }
            }
          });
        }

        goods_info_array.push(goods_info_item);
      }
    });

    // 更新data中的缓存数据
    this.setData({
      choose_goods_info: goods_info_array,
    });

    // 只有当有选中商品时才保存到Storage，避免清空已有缓存
    if (goods_info_array.length > 0) {
      wx.setStorageSync(CACHE_KEY, goods_info_array);
      console.log("=== 小程序端缓存数据格式 ===");
      console.log("choose_goods_info:", goods_info_array);
      console.log("已保存到Storage");
    } else {
      console.log("=== 没有选中商品，保持原有缓存不变 ===");
      console.log("当前页面数据已清空，但Storage缓存保持不变");
    }
  },

  /**
   * 还原缓存的商品选择（用于编辑模式）
   */
  restoreCachedGoods: function () {
    const cachedGoodsInfo = wx.getStorageSync(CACHE_KEY) || [];

    if (cachedGoodsInfo.length === 0) {
      console.log("没有缓存的商品数据，跳过还原");
      return;
    }

    console.log("=== 开始还原缓存的商品选择 ===");
    console.log("缓存的商品数据:", cachedGoodsInfo);

    const goods_list = this.data.goods_list;
    let hasRestored = false;

    // 遍历缓存的商品信息
    cachedGoodsInfo.forEach(cachedItem => {
      // 在当前商品列表中查找对应的商品
      const goodsIndex = goods_list.findIndex(goods => goods.guid === cachedItem.guid);

      if (goodsIndex !== -1) {
        const goods = goods_list[goodsIndex];

        console.log(`=== 开始还原商品: ${goods.name} ===`);
        console.log(`原价格: ${goods.price}, 原数量: ${goods.num}`);
        console.log('商品完整数据:', goods);

        // 先还原SKU选择（如果是多规格商品），这样价格会先更新
        if (goods.is_attribute == 1 && cachedItem.attr && cachedItem.attr.length > 0) {
          this.restoreGoodsSKU(goods, cachedItem.attr);
        }

        // 再还原商品数量
        goods.num = cachedItem.amount;

        hasRestored = true;
        console.log(`✅ 商品还原完成: ${goods.name}`);
        console.log(`最终价格: ${goods.price}, 最终数量: ${goods.num}`);
      } else {
        console.warn(`❌ 缓存中的商品未找到: ${cachedItem.guid}`);
      }
    });

    if (hasRestored) {
      // 更新商品列表数据
      this.setData({
        goods_list: goods_list
      });

      // 重新计算购物车数据
      this.getTotalPrice();

      console.log("商品选择还原完成");
      console.log("还原后的商品列表:", goods_list.filter(g => g.num > 0));

      // 显示还原成功提示
      wx.showToast({
        title: '已还原商品选择',
        icon: 'success',
        duration: 2000
      });
    } else {
      console.log("没有找到可还原的商品");
    }
  },

  /**
   * 还原商品的SKU选择
   */
  restoreGoodsSKU: function (goods, cachedAttrs) {
    if (!goods.attr_group_list || !cachedAttrs) {
      console.log(`商品 ${goods.name} 没有属性组或缓存属性，跳过SKU还原`);
      return;
    }

    console.log(`=== 开始还原SKU: ${goods.name} ===`);
    console.log('缓存的属性:', cachedAttrs);
    console.log('商品属性组:', goods.attr_group_list);

    // 初始化选中属性对象
    goods.selected_attrs = {};

    // 用于构建SKU文本的数组
    const skuTextArray = [];

    // 根据缓存的属性信息还原选择
    cachedAttrs.forEach(cachedAttr => {
      // 查找对应的属性组
      const attrGroup = goods.attr_group_list.find(group =>
        group.attr_group_id === cachedAttr.attr_group_id
      );

      if (attrGroup) {
        // 查找对应的属性
        const attr = attrGroup.attr_list.find(a =>
          a.attr_id === cachedAttr.attr_id
        );

        if (attr) {
          goods.selected_attrs[cachedAttr.attr_group_id] = cachedAttr.attr_id;
          skuTextArray.push(cachedAttr.attr_name);
          console.log(`✅ 还原SKU选择: ${cachedAttr.attr_group_name} = ${cachedAttr.attr_name} (ID: ${cachedAttr.attr_id})`);
        } else {
          console.log(`❌ 属性不存在: ${cachedAttr.attr_group_name} = ${cachedAttr.attr_name} (ID: ${cachedAttr.attr_id})`);
        }
      } else {
        console.log(`❌ 属性组不存在: ${cachedAttr.attr_group_name} (ID: ${cachedAttr.attr_group_id})`);
      }
    });

    // 构建SKU文本用于购物车显示
    goods.selected_specs = skuTextArray.join(' ');
    console.log(`SKU文本: ${goods.selected_specs}`);
    console.log(`最终选中属性:`, goods.selected_attrs);

    // 更新当前SKU信息
    this.updateCurrentSKU(goods);
  },

  /**
   * 更新商品的当前SKU信息
   */
  updateCurrentSKU: function (goods) {
    if (!goods.attr_group_list || goods.attr_group_list.length === 0) {
      console.log(`商品 ${goods.name} 没有属性组，跳过SKU更新`);
      return;
    }

    console.log(`=== 开始匹配SKU: ${goods.name} ===`);
    console.log('选中的属性:', goods.selected_attrs);

    // 使用现有的SKU匹配算法
    const matchedSKU = this.findMatchingSku(goods);

    if (matchedSKU) {
      const oldPrice = goods.price;
      goods.current_sku = matchedSKU;
      goods.price = parseFloat(matchedSKU.price);
      goods.stock = matchedSKU.stock;
      goods.current_sku_stock = matchedSKU.stock;

      console.log(`✅ SKU匹配成功: ${goods.name}`);
      console.log(`价格更新: ${oldPrice} → ${goods.price}`);
      console.log(`库存: ${goods.stock}`);
      console.log('匹配的SKU:', matchedSKU);
    } else {
      console.log(`❌ 未找到匹配的SKU: ${goods.name}`);
      console.log('可能原因: 属性选择不完整或SKU数据不匹配');

      // 调试：显示属性组和SKU数据结构
      goods.attr_group_list.forEach(group => {
        console.log(`属性组 ${group.attr_group_name}:`, group.attr_list);
        group.attr_list.forEach(attr => {
          if (attr.sku_list) {
            console.log(`  属性 ${attr.attr_name} 的SKU:`, attr.sku_list);
          }
        });
      });
    }
  },

  /**
   * 跳转到商品详情页面
   */
  goto_goods_detail: function (event) {
    let view_goods_guid = event.currentTarget.dataset.guid;
    let url = "/pages/index/goods_detail?guid=" + view_goods_guid + "&token=" + this.data.token + "&way=1";
    if (this.data.data.config.choose_goods_show_price == 0) {
      url += "&hide_price=1";
    }
    wx.navigateTo({
      url: url,
    });
  },

  submitOrder: function () {
    console.log("submit");
    if (this.data.totalNum == 0) {
      wx.showModal({
        title: "系统提示",
        content: "请选择商品后再提交",
        showCancel: false,
      });
      return false;
    }

    // 检查访问模式，非token模式需要验证
    if (this.data.access_mode !== 'token') {
      this.handleNonTokenSubmit();
      return false;
    }


    // 礼品卡（type == 4）跳过所有限制验证
    if (this.data.data.coupon_info && this.data.data.coupon_info.type == 4) {
      // 礼品卡不做任何限制验证，直接跳过
    } else {
      // 普通卡券的验证逻辑
      if (this.data.exchange_goods_type == 1 && this.data.totalNum < this.data.exchange_goods_num && this.data.max_exchange_num_once == 1) {
        wx.showModal({
          title: "系统提示",
          content: "您还可以选择" + (this.data.exchange_goods_num - this.data.totalNum) + "种产品哦",
          showCancel: false,
        });
        return false;
      }

      if (this.data.data.config.value_code_pick_up_limit_type == 1 && this.data.exchange_goods_type == 2 && this.data.totalPrice < this.data.max_exchange_value_once) {
        wx.showModal({
          title: "系统提示",
          content: "您还可以选择" + (this.data.max_exchange_value_once - this.data.totalPrice) + "元产品哦",
          showCancel: false,
        });
        return false;
      }
    }

    // 保存缓存数据（与H5端保持一致）
    wx.setStorageSync(CACHE_KEY, this.data.choose_goods_info);

    // 检查是否为礼品卡
    if (this.data.data.coupon_info && this.data.data.coupon_info.type == 4) {
      // 礼品卡：显示提示并返回
      wx.showToast({
        title: "商品已选择",
        icon: "success",
        duration: 1000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1000);
      return;
    }

    // 普通卡券：跳转到订单提交页面
    wx.redirectTo({
      url: "/pages/code/submit_order?bid=" + this.data.bid + "&token=" + this.data.token,
    });
  },
  close: function (e) {
    this.setData({
      notice_dialog: false,
    });
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    console.log('页面参数:', options);

    // 解析所有可能的参数
    const token = options.token;
    const code = options.code || options.c;
    const guid = options.guid;
    const coupon_send_note_guid = options.coupon_send_note_guid;
    const bid = options.bid || app.ext_config.bid;
    console.log('解析后的参数:', {
      token: token,
      code: code,
      guid: guid,
      bid: bid
    });

    // 保存参数到data中
    this.setData({
      token: token,
      code: code,
      guid: guid,
      coupon_send_note_guid: coupon_send_note_guid,
      bid: bid
    });

    // 统一调用数据加载方法
    this.loadData();
  },

  /**
   * 统一的数据加载方法 - 支持多种访问模式
   */
  loadData: function () {
    const that = this;

    // 优先使用缓存中的token（验证成功后写入的）
    const cachedToken = wx.getStorageSync('verified_token');
    let token = cachedToken || that.data.token;

    console.log('数据加载参数检查:', {
      token: token,
      code: that.data.code,
      guid: that.data.guid,
      bid: that.data.bid,
      cachedToken: cachedToken
    });

    // 验证至少有一个参数
    if (!token && !that.data.code && !that.data.guid) {
      console.error('缺少必要参数：需要token、code或guid');
      wx.showModal({
        title: '参数错误',
        content: '页面参数错误，请重新访问',
        showCancel: false
      });
      return;
    }

    const params = {};

    // 将所有存在的参数都传给后端，后端会按优先级处理
    if (token) {
      params.token = token;
    }
    if (that.data.code) {
      params.code = that.data.code;
    }
    if (that.data.guid) {
      params.guid = that.data.guid;
    }
    if (that.data.coupon_send_note_guid) {
      params.coupon_send_note_guid = that.data.coupon_send_note_guid;
    }
    if (that.data.bid) {
      params.bid = that.data.bid;
    }

    console.log('请求参数:', params);

    // 显示加载提示
    wx.showLoading({
      title: '加载中...'
    });

    // 统一调用接口
    app.request({
      url: app.api.code.get_goods_list_by_token,
      data: params,
      success: function (result) {
        wx.hideLoading();
        console.log('接口返回数据:', result);

        // 检查返回数据的有效性
        if (!result.data) {
          console.error('接口返回数据为空:', result);
          wx.showModal({
            title: '数据错误',
            content: '服务器返回数据为空',
            showCancel: false
          });
          return;
        }

        if (!result.data.goods_list) {
          console.error('商品列表为空:', result.data);
          // 商品列表为空不一定是错误，可能是没有商品
          result.data.goods_list = [];
        }

        // 处理商品列表初始化
        that.initGoodsList(result.data.goods_list);

        // 设置基础数据
        that.setData({
          hasList: true,
          data: result.data,
          exchange_goods_num: result.data.exchange_goods_num || 0,
          max_exchange_num_once: result.data.max_exchange_num_once || 1,
          max_exchange_value_once: result.data.max_exchange_value_once || 0,
          exchange_goods_value: result.data.exchange_goods_value || 0,
          exchange_goods_type: result.data.exchange_goods_type || 0,
          goods_list: result.data.goods_list,
          template_type: result.data.template_type || 1,
          goods_list_num: result.data.goods_list.length,
          access_mode: result.data.access_mode || 'token' // 默认为token模式
        });

        // 设置页面标题
        if (result.data.coupon_info && result.data.coupon_info.name) {
          wx.setNavigationBarTitle({
            title: result.data.coupon_info.name,
          });
        }

        // 处理访问模式
        that.handleAccessMode(result.data.access_mode);

        // 处理配置信息（从返回数据中获取，与H5保持一致）
        const config = result.data.config || {};

        // 将轮播图数据合并到config中
        if (result.data.banner_list) {
          config.banner_list = result.data.banner_list;
        }

        that.setData({
          config: config
        });

        // 显示公告弹窗（如果有配置）
        if (config.choose_goods_notice_pop) {
          that.setData({
            notice_dialog: true,
          });
        }

        // 处理分类切换功能（仅模板2支持）
        that.handleCategoryTabs(result.data);

        // 计算总价
        that.getTotalPrice();

        // 尝试还原缓存的商品选择（用于编辑模式）
        that.restoreCachedGoods();
      },
      fail: function (error) {
        wx.hideLoading();
        console.error('数据加载失败:', error);
        wx.showModal({
          title: '加载失败',
          content: '网络请求失败，请检查网络连接后重试',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              // 用户点击确定后重新加载
              that.loadData();
            }
          }
        });
      }
    });
  },

  /**
   * 初始化商品列表数据
   */
  initGoodsList: function (goodsList) {
    if (!goodsList || goodsList.length === 0) return;

    for (let i = 0; i < goodsList.length; i++) {
      let item = goodsList[i];
      item.num = item.min_choose_num > 0 ? item.min_choose_num : 0;

      // 初始化SKU相关数据
      if (item.is_attribute == 1) {
        // 多规格商品初始化
        item.selected_attrs = {}; // 用户选择的属性
        item.current_sku = null; // 当前匹配的SKU
        item.selected_specs = ""; // 已选规格文本
        item.current_sku_stock = item.stock || 0; // 当前SKU库存，默认使用商品库存
        item.original_price = item.price; // 保存原始价格，用于恢复
      } else {
        // 单规格商品初始化
        item.current_sku_stock = item.stock || 0; // 使用商品库存
        item.original_price = item.price; // 保存原始价格
      }
    }
  },

  /**
   * 处理访问模式
   */
  handleAccessMode: function (accessMode) {
    console.log('当前访问模式:', accessMode);

    switch (accessMode) {
      case 'token':
        console.log('Token模式：完整功能');
        // this.showAccessModeHint('已验证，可正常购买', 'success');
        break;
      case 'code':
        console.log('Code模式：需要验证密码');
        // this.showAccessModeHint('预览模式，验证卡号密码后可购买', 'info');
        break;
      case 'coupon_guid':
        console.log('GUID模式：需要获取卡号');
        // this.showAccessModeHint('预览模式，获取卡号后可购买', 'info');
        break;
      default:
        console.log('未知模式:', accessMode);
        this.showAccessModeHint('未知访问模式', 'warning');
    }
  },

  /**
   * 显示访问模式提示
   */
  showAccessModeHint: function (message, type) {
    this.setData({
      accessModeHint: {
        show: true,
        message: message,
        type: type
      }
    });

    // 3秒后自动隐藏提示（成功状态）
    if (type === 'success') {
      setTimeout(() => {
        this.setData({
          'accessModeHint.show': false
        });
      }, 3000);
    }
  },

  /**
   * 隐藏访问模式提示
   */
  hideAccessModeHint: function () {
    this.setData({
      'accessModeHint.show': false
    });
  },

  /**
   * 处理分类切换功能（通用功能，不区分模板）
   */
  handleCategoryTabs: function (data) {
    const that = this;

    // 有多个分类时就显示分类切换（不区分模板类型）
    const showTabs = data.goods_category_list &&
      data.goods_category_list.length > 1;

    console.log('分类切换检查:', {
      template_type: data.template_type,
      category_count: data.goods_category_list ? data.goods_category_list.length : 0,
      showTabs: showTabs
    });

    that.setData({
      showCategoryTabs: showTabs,
      activeCategory: 'all' // 重置为全部
    });

    // 如果显示分类切换，初始化商品显示状态
    if (showTabs) {
      that.initCategoryDisplay();
    }
  },

  /**
   * 初始化分类显示状态
   */
  initCategoryDisplay: function () {
    // 默认显示所有商品，为每个商品添加显示状态
    const goods_list = this.data.goods_list;
    for (let i = 0; i < goods_list.length; i++) {
      goods_list[i].categoryVisible = true;
    }
    this.setData({
      goods_list: goods_list
    });
  },

  /**
   * 切换商品分类显示
   */
  changeCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    console.log('切换分类:', category);

    this.setData({
      activeCategory: category
    });

    const goods_list = this.data.goods_list;

    if (category === 'all') {
      // 显示所有商品
      for (let i = 0; i < goods_list.length; i++) {
        goods_list[i].categoryVisible = true;
      }
    } else {
      // 只显示选中分类的商品
      for (let i = 0; i < goods_list.length; i++) {
        // 根据商品的category_guid属性进行筛选
        goods_list[i].categoryVisible = (goods_list[i].category_guid === category);
      }
    }

    this.setData({
      goods_list: goods_list
    });

    console.log('分类切换完成，当前显示商品数:', goods_list.filter(item => item.categoryVisible).length);
  },


  getCurrentSkuStock: function (product) {
    // 获取当前SKU库存
    if (product.current_sku && product.current_sku.stock !== undefined) {
      return product.current_sku.stock;
    }
    return product.stock || 0;
  },

  getMaxSelectableQuantity: function (product) {
    // 获取最大可选择数量
    let maxByConfig = product.max_choose_num || 999;
    let maxByStock = product.stock_mode == 1 ? this.getCurrentSkuStock(product) : 999;
    return Math.min(maxByConfig, maxByStock);
  },

  checkCanConfirmSku: function (product) {
    // 检查是否可以确认SKU选择
    if (product.is_attribute != 1) {
      return true; // 单规格商品直接可确认
    }

    if (!product.attr_group_list || product.attr_group_list.length === 0) {
      return true;
    }

    // 检查是否所有属性组都已选择
    let selectedCount = Object.keys(product.selected_attrs || {}).length;
    return selectedCount === product.attr_group_list.length;
  },

  // SKU匹配核心算法
  findMatchingSku: function (product) {
    // SKU匹配算法，参考H5端逻辑
    if (product.is_attribute != 1) {
      return null; // 单规格商品无需匹配
    }

    if (!product.attr_group_list || !product.selected_attrs) {
      return null;
    }

    const selectedGroupCount = Object.keys(product.selected_attrs).length;
    if (selectedGroupCount !== product.attr_group_list.length) {
      return null; // 属性选择不完整
    }

    let matchedSkus = null;

    // 遍历每个属性组
    for (const group of product.attr_group_list) {
      const selectedAttrId = product.selected_attrs[group.attr_group_id];
      if (!selectedAttrId) {
        return null;
      }

      // 找到选中的属性
      const selectedAttr = group.attr_list.find((attr) => attr.attr_id === selectedAttrId);
      if (!selectedAttr || !selectedAttr.sku_list) {
        return null;
      }

      // 第一次匹配，直接使用该属性的SKU列表
      if (matchedSkus === null) {
        matchedSkus = selectedAttr.sku_list.slice(); // 复制数组
      } else {
        // 后续匹配，取交集
        matchedSkus = matchedSkus.filter((sku1) => selectedAttr.sku_list.some((sku2) => sku1.sku_guid === sku2.sku_guid));
      }

      if (matchedSkus.length === 0) {
        return null; // 没有匹配的SKU
      }
    }

    return matchedSkus && matchedSkus.length > 0 ? matchedSkus[0] : null;
  },

  // 检查属性是否可用（考虑其他已选属性的影响）
  isAttrAvailable: function (product, groupId, attrId) {
    if (product.is_attribute != 1) {
      return true;
    }

    if (!product.attr_group_list || product.attr_group_list.length === 0) {
      return true;
    }

    const group = product.attr_group_list.find((g) => g.attr_group_id === groupId);
    if (!group) {
      return false;
    }

    const attr = group.attr_list.find((a) => a.attr_id === attrId);
    if (!attr || !attr.sku_list) {
      return false;
    }

    // 获取当前已选择的属性（不包括当前要检查的属性组）
    let selectedAttrs = Object.assign({}, product.selected_attrs || {});

    // 如果没有选择任何其他属性，只需要检查该属性是否有可用的SKU
    let hasOtherSelections = false;
    for (let gId in selectedAttrs) {
      if (gId !== groupId && selectedAttrs[gId]) {
        hasOtherSelections = true;
        break;
      }
    }

    // 根据库存模式检查SKU可用性
    let availableSkus;
    if (product.stock_mode == 0) {
      // stock_mode = 0: 不限制库存，所有SKU都可用
      availableSkus = attr.sku_list;
    } else {
      // stock_mode = 1: 限制库存，只有库存大于0的SKU可用
      availableSkus = attr.sku_list.filter((sku) => sku.stock > 0);
    }

    if (!hasOtherSelections) {
      // 没有其他选择，只要该属性有可用SKU就行
      return availableSkus.length > 0;
    }

    // 有其他选择，需要检查能否与其他已选属性形成有效组合
    // 创建临时选择状态
    let tempSelectedAttrs = Object.assign({}, selectedAttrs);
    tempSelectedAttrs[groupId] = attrId;

    // 使用现有的findMatchingSku方法来检查是否能找到匹配的SKU
    let tempProduct = Object.assign({}, product, { selected_attrs: tempSelectedAttrs });
    let matchedSku = this.findMatchingSku(tempProduct);

    if (!matchedSku) {
      return false;
    }

    // 根据库存模式检查匹配的SKU是否可用
    if (product.stock_mode == 0) {
      return true; // 不限制库存时，有匹配的SKU就可用
    } else {
      return matchedSku.stock > 0; // 限制库存时，需要库存大于0
    }
  },

  // 生成已选规格文本
  generateSelectedSpecsText: function (product) {
    if (product.is_attribute != 1 || !product.attr_group_list || !product.selected_attrs) {
      return "";
    }

    let specsArray = [];

    for (const group of product.attr_group_list) {
      const selectedAttrId = product.selected_attrs[group.attr_group_id];
      if (selectedAttrId) {
        const selectedAttr = group.attr_list.find((attr) => attr.attr_id === selectedAttrId);
        if (selectedAttr) {
          specsArray.push(selectedAttr.attr_name);
        }
      }
    }

    return specsArray.join(" ");
  },

  // 更新商品的SKU状态
  updateProductSkuState: function (product) {
    if (product.is_attribute != 1) {
      return; // 单规格商品无需更新
    }

    // 匹配SKU
    const matchedSku = this.findMatchingSku(product);
    product.current_sku = matchedSku;

    // 更新库存
    if (matchedSku) {
      product.current_sku_stock = matchedSku.stock;
    } else {
      product.current_sku_stock = 0;
    }

    // 生成规格文本
    product.selected_specs = this.generateSelectedSpecsText(product);
  },

  // 更新属性的可用性状态
  updateAttrAvailability: function (product) {
    if (product.is_attribute != 1 || !product.attr_group_list) {
      return;
    }

    console.log("=== 更新属性可用性 ===");
    console.log("当前已选属性:", product.selected_attrs);

    for (const group of product.attr_group_list) {
      console.log(`属性组: ${group.attr_group_name}`);
      for (const attr of group.attr_list) {
        attr.available = this.isAttrAvailable(product, group.attr_group_id, attr.attr_id);
        console.log(`  ${attr.attr_name}: ${attr.available ? "可用" : "不可用"}`);
      }
    }
  },


  // SKU选择弹窗交互方法
  openSkuModal: function (e) {
    console.log('=== openSkuModal 被调用 ===');
    const index = e.currentTarget.dataset.index;
    const product = JSON.parse(JSON.stringify(this.data.goods_list[index])); // 深拷贝，避免引用问题

    console.log('SKU选择 - 商品索引:', index);
    console.log('SKU选择 - 商品信息:', product);

    if (!product) {
      console.log('商品不存在，返回');
      return;
    }

    // 确保product对象包含所有必要的属性
    if (!product.selected_attrs) {
      product.selected_attrs = {};
    }

    // 确保商品有guid属性
    if (!product.guid) {
      console.log('商品缺少guid属性，无法打开SKU选择器');
      wx.showToast({
        title: '商品数据异常',
        icon: 'none'
      });
      return;
    }

    console.log('显示SKU选择器');
    // 使用新的SKU选择组件
    this.setData({
      showSkuSelector: true,
      currentSkuProduct: product,
      currentSkuProductIndex: index
    }, () => {
      // setData回调，确保数据已更新
      console.log('setData回调 - 状态:', {
        showSkuSelector: this.data.showSkuSelector,
        currentSkuProduct: this.data.currentSkuProduct ? this.data.currentSkuProduct.name : 'null'
      });

      console.log('setData回调 - 完整的商品对象:', this.data.currentSkuProduct);
      console.log('setData回调 - 商品是否有guid:', this.data.currentSkuProduct ? this.data.currentSkuProduct.guid : 'no guid');
    });
  },

  // SKU选择校验函数 - 使用通用校验函数
  validateSkuSelection: function (skuData) {
    console.log('=== 校验SKU选择 ===', skuData);

    // 获取当前商品索引
    const index = this.data.currentSkuProductIndex;
    if (index < 0) {
      return false;
    }

    // 获取商品信息
    const product = this.data.goods_list[index];

    // 使用通用校验函数
    return this.validateGoodsQuantity({
      product: product,
      num: skuData.quantity,
      index: index,
      sku: skuData.sku,
      currentPrice: skuData.price
    });
  },

  closeSkuModal: function () {
    this.setData({
      showSkuSelector: false,
      currentSkuProduct: null,
      currentSkuProductIndex: -1,
    });
  },

  stopPropagation: function () {
    // 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
  },


  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },

  /**
   * SKU选择确认事件
   */
  onSkuConfirm: function (e) {
    const { product, selectedAttrs, quantity, sku, price } = e.detail;
    const index = this.data.currentSkuProductIndex;

    if (index < 0) {
      return;
    }

    // 获取商品信息
    let goods_list = this.data.goods_list;
    let targetProduct = goods_list[index];

    // 先进行数据校验
    const validationResult = this.validateGoodsQuantity({
      product: targetProduct,
      num: quantity,
      index: index,
      sku: sku,
      currentPrice: price
    });

    // 如果校验不通过，直接返回
    if (validationResult === false) {
      return;
    }

    // 校验通过，更新商品的SKU选择状态
    targetProduct.selected_attrs = {};
    selectedAttrs.forEach(attr => {
      targetProduct.selected_attrs[attr.attr_group_id] = attr.attr_id;
    });

    targetProduct.current_sku = sku;
    targetProduct.num = quantity;
    targetProduct.price = price; // 更新为SKU价格
    targetProduct.selected_specs = selectedAttrs.map(attr => attr.attr_name).join(" ");

    // 更新商品状态
    this.updateProductSkuState(targetProduct);

    this.setData({
      goods_list: goods_list,
      showSkuSelector: false
    });

    // 重新计算总价
    this.getTotalPrice();

    wx.showToast({
      title: "已添加到购物车",
      icon: "success",
      duration: 1500
    });
  },

  /**
   * SKU选择取消事件
   */
  onSkuCancel: function () {
    this.setData({ showSkuSelector: false });
  },

  // ===== 新增：验证相关方法 =====


  /**
   * 处理非token模式的提交
   */
  handleNonTokenSubmit: function () {
    const that = this;

    if (this.data.access_mode === 'code') {
      // Code模式：显示密码验证弹窗
      this.showVerifyModal();
    } else if (this.data.access_mode === 'coupon_guid') {
      // GUID模式：提示需要获取卡号
      wx.showModal({
        title: '需要验证',
        content: '请先获取卡号后再购买商品',
        confirmText: '去获取',
        cancelText: '取消',
        success: function (res) {
          if (res.confirm) {
            // 跳转到获取卡号页面
            wx.navigateTo({
              url: '/pages/code/index?bid=' + that.data.bid + '&guid=' + that.data.guid
            });
          }
        }
      });
    } else {
      // 其他情况：通用提示
      wx.showModal({
        title: '需要验证',
        content: '请先验证后再购买商品',
        showCancel: false
      });
    }
  },

  /**
   * 显示验证弹窗
   */
  showVerifyModal: function () {
    this.setData({
      showVerifyModal: true,
      'verifyForm.code': this.data.code || '', // 预填卡号
      'verifyForm.password': ''
    });
  },

  /**
   * 隐藏验证弹窗
   */
  hideVerifyModal: function () {
    this.setData({
      showVerifyModal: false,
      'verifyForm.code': '',
      'verifyForm.password': ''
    });
  },

  /**
   * 验证表单输入
   */
  onVerifyCodeInput: function (e) {
    this.setData({
      'verifyForm.code': e.detail.value
    });
  },

  onVerifyPasswordInput: function (e) {
    this.setData({
      'verifyForm.password': e.detail.value
    });
  },

  /**
   * 提交验证
   */
  submitVerify: function () {
    const that = this;
    const { code, password } = this.data.verifyForm;

    if (!code) {
      wx.showToast({
        title: '请输入卡号',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    const verifyData = {
      code: code,
      password: password,
      bid: this.data.bid
    };

    wx.showLoading({
      title: '验证中...'
    });

    app.request({
      url: app.api.code.verify_code,
      data: verifyData,
      success: function (result) {
        wx.hideLoading();
        console.log('验证结果:', result);

        if (result.data.status == 1) {
          // 验证成功
          that.hideVerifyModal();

          // 获取token
          const token = that.extractTokenFromResult(result);

          if (token) {
            // 判断是否已选择商品
            if (that.data.totalNum > 0) {
              // 已选择商品：保存商品信息并跳转订单页
              wx.setStorageSync(CACHE_KEY, that.data.choose_goods_info);
              wx.redirectTo({
                url: "/pages/code/submit_order?bid=" + that.data.bid + "&token=" + token,
              });
            } else {
              // 未选择商品：重新获取页面数据（变成token模式）
              that.reloadPageWithToken(token);
            }
          } else {
            wx.showToast({
              title: '验证成功但获取token失败',
              icon: 'none'
            });
          }
        } else {
          // 验证失败
          wx.showToast({
            title: result.msg || '验证失败，请检查卡号和密码',
            icon: 'none'
          });
        }
      },
      fail: function (error) {
        wx.hideLoading();
        console.error('验证请求失败:', error);
        wx.showToast({
          title: '验证失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 从验证结果中提取token
   */
  extractTokenFromResult: function (result) {
    if (result.data.data && result.data.data.token) {
      return result.data.data.token;
    }
    return null;
  },

  /**
   * 使用token重新加载页面数据
   */
  reloadPageWithToken: function (token) {
    console.log('验证成功，使用token重新获取数据');

    // 清空当前购物车
    this.setData({
      choose_goods_list: {},
      totalNum: 0,
      totalPrice: 0,
      token: token
    });

    // 将token写入缓存
    wx.setStorageSync('verified_token', token);

    // 重新加载数据
    this.loadData();

    // 显示成功提示
    wx.showToast({
      title: '验证成功！',
      icon: 'success'
    });
  },

  /**
   * 清除缓存的token
   */
  clearCachedToken: function () {
    wx.removeStorageSync('verified_token');
    console.log('已清除缓存的token');
  },

  // ===== 数量输入弹框相关方法 =====

  /**
   * 显示数量输入弹框
   */
  showQuantityInput: function (e) {
    const index = e.currentTarget.dataset.index;
    const guid = e.currentTarget.dataset.guid;
    const goods_list = this.data.goods_list;

    if (index < 0 || index >= goods_list.length) {
      console.error('商品索引无效:', index);
      return;
    }

    const product = goods_list[index];
    const currentNum = product.num || product.min_choose_num || 1;

    this.setData({
      showQuantityModal: true,
      inputQuantity: currentNum.toString(),
      currentInputProduct: product,
      currentInputIndex: index,
      quantityInputFocus: true
    });
  },

  /**
   * 隐藏数量输入弹框
   */
  hideQuantityInput: function () {
    this.setData({
      showQuantityModal: false,
      inputQuantity: '',
      currentInputProduct: null,
      currentInputIndex: -1,
      quantityInputFocus: false
    });
  },

  /**
   * 数量输入事件
   */
  onQuantityModalInput: function (e) {
    this.setData({
      inputQuantity: e.detail.value
    });
  },

  /**
   * 确认数量输入
   */
  confirmQuantityInput: function () {
    const inputQuantity = parseInt(this.data.inputQuantity);
    const index = this.data.currentInputIndex;
    const product = this.data.currentInputProduct;

    // 校验输入是否为有效数字
    if (isNaN(inputQuantity) || inputQuantity < 0) {
      wx.showModal({
        title: "系统提示",
        content: "请输入有效的数量",
        showCancel: false,
      });
      return;
    }

    // 使用通用校验函数进行校验
    const validationResult = this.validateGoodsQuantity({
      product: product,
      num: inputQuantity,
      index: index
    });

    if (validationResult === false) {
      return; // 校验失败，错误信息已在校验函数中显示
    }

    // 校验通过，更新数量
    const result = this.changeNunmber(index, inputQuantity);
    if (result !== false) {
      // 成功更新，关闭弹框
      this.hideQuantityInput();

      // 显示成功提示
      wx.showToast({
        title: '数量已更新',
        icon: 'success',
        duration: 1500
      });
    }
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function (e) {
    // 空函数，仅用于阻止事件冒泡
    return false;
  }
});
