// pages/gift/status.js
const app = getApp();

// 缓存key常量
const CACHE_KEY = "choose_goods_info";
const LAST_CODE_KEY = "last_gift_code"; // 缓存上一次的卡号

Page({
  /**
   * 页面的初始数据
   */
  data: {
    code: "", // 卡号
    password: "", // 密码
    token: "", // 验证成功后的token
    loading: true,
    error_message: "", // 错误信息
    show_error: false, // 显示错误状态
    coupon_info: {}, // 卡券基础信息
    coupon_send_note: {}, // 卡券发送记录
    gift_records: null, // 送礼记录（单个对象）
    order_detail: null, // 订单详情
    can_send_gift: false, // 是否可以送礼
    show_claim_modal: false, // 显示领取弹窗
    current_gift: {}, // 当前要领取的礼品
    delivery_address: "", // 收货地址
    selected_address: null, // 选中的收货地址

    // 选中的商品相关
    selected_goods: [], // 选中的商品列表
    show_selected_goods: false, // 是否显示选中商品
    total_selected_count: 0, // 选中商品总数量
    total_selected_price: 0, // 选中商品总价格

    // 送礼配置相关
    gift_message: "", // 感谢语
    gift_password: "", // 收礼口令
    gift_expire_time_type: "", // 收礼有效期类型：1-1个月，3-3个月，6-半年，0-永久
    expire_time_index: 0, // 有效期选择器索引
    expire_time_options: [
      // 有效期选项
      { label: "1个月", value: "1 month" },
      { label: "3个月", value: "3 months" },
      {
        label: "半年",
        value: "6 months",
      },
      { label: "永久", value: "" },
    ],

    // 收礼口令相关
    input_gift_password: "", // 用户输入的收礼口令
    canClaimGift: false, // 是否可以领取礼品
  },

  /**
   * 处理微信支付
   */
  processWechatPayment: function (order_guid, payment) {
    console.log("=== 开始微信支付 ===");
    console.log("订单GUID:", order_guid);
    console.log("支付类型:", payment);

    const that = this;

    // 获取支付数据
    app.request({
      url: app.api.order.pay_data,
      data: {
        order_guid: order_guid,
        pay_type: payment,
        scene: "miniapp",
      },
      success: function (res) {
        console.log("=== 获取支付数据成功 ===");
        console.log("支付数据:", res);

        const pay_options = res.data.pay_options;
        const third_pay_bill_number = res.data.third_pay_bill_number;

        // 调用微信支付API
        wx.requestPayment({
          timeStamp: pay_options.timeStamp,
          nonceStr: pay_options.nonceStr,
          package: pay_options.package,
          signType: pay_options.signType,
          paySign: pay_options.paySign,
          success: function (e) {
            console.log("=== 微信支付成功 ===");
            console.log("支付成功回调:", e);
          },
          fail: function (e) {
            console.log("=== 微信支付失败 ===");
            console.log("支付失败回调:", e);
          },
          complete: function (e) {
            console.log("=== 微信支付完成 ===");
            console.log("支付完成回调:", e);

            wx.hideLoading();

            if (e.errMsg == "requestPayment:fail" || e.errMsg == "requestPayment:fail cancel") {
              // 支付失败或取消
              wx.showModal({
                title: "支付提示",
                content: "订单尚未支付，您可以稍后在订单列表中继续支付",
                showCancel: false,
                confirmText: "确认",
                success: function (res) {
                  if (res.confirm) {
                    // 跳转到订单列表
                    wx.redirectTo({
                      url: "/pages/order/order?status=-1",
                    });
                  }
                },
              });
            } else if (e.errMsg == "requestPayment:ok") {
              // 支付成功，查询订单状态
              that.queryPaymentResult(third_pay_bill_number);
            } else {
              // 其他错误
              wx.showModal({
                title: "支付失败",
                content: "支付失败: " + e.errMsg,
                showCancel: false,
                confirmText: "确认",
                success: function (res) {
                  if (res.confirm) {
                    wx.redirectTo({
                      url: "/pages/order/order?status=-1",
                    });
                  }
                },
              });
            }
          },
        });
      },
      fail: function (err) {
        console.log("=== 获取支付数据失败 ===");
        console.log("错误信息:", err);
      },
    });
  },

  /**
   * 查询支付结果
   */
  queryPaymentResult: function (third_pay_bill_number) {
    console.log("=== 查询支付结果 ===");
    console.log("支付单号:", third_pay_bill_number);

    const that = this;

    app.pay.query(
      third_pay_bill_number,
      function () {
        // 支付成功回调
        console.log("=== 支付查询成功 ===");
        that.handlePaymentSuccess();
      },
      function () {
        // 支付失败回调
        console.log("=== 支付查询失败 ===");
        wx.redirectTo({
          url: "/pages/order/order?status=-1",
        });
      }
    );
  },

  /**
   * 处理支付成功
   */
  handlePaymentSuccess: function () {
    console.log("=== 处理支付成功 ===");

    const that = this;

    // 显示支付成功提示
    wx.showModal({
      title: "支付成功",
      content: "送礼订单已创建成功！正在刷新页面...",
      showCancel: false,
      confirmText: "确定",
      success: function (res) {
        if (res.confirm) {
          that.refreshPageAfterPayment();
        }
      },
    });

    // 延迟2秒后自动刷新页面（即使用户没点确定）
    setTimeout(() => {
      that.refreshPageAfterPayment();
    }, 2000);
  },

  /**
   * 支付成功后刷新页面
   */
  refreshPageAfterPayment: function () {
    const that = this;

    console.log("=== 清空选中商品数据 ===");
    // 清空选中的商品和配置（保留卡号密码）
    wx.removeStorageSync(CACHE_KEY);

    console.log("=== 刷新页面重新加载 ===");
    // 重新加载页面数据，这样会显示待领取状态
    that.setData({
      selected_goods: [],
      total_selected_count: 0,
      total_selected_price: 0,
      show_selected_goods: false,
      gift_message: "",
      gift_password: "",
      expire_time_index: 0,
      expire_time_type: "",
    });

    // 显示加载提示
    wx.showLoading({
      title: "刷新中...",
    });

    // 重新加载卡券信息（会显示待领取状态）
    setTimeout(() => {
      that.loadGiftInfo(that.data.code, that.data.password);
      wx.hideLoading();

      // 显示刷新完成提示
      wx.showToast({
        title: "页面已刷新",
        icon: "success",
        duration: 1500,
      });
    }, 1000);
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: async function (options) {
    console.log("gift/status - onLoad", options);

    app.pageOnLoad(this, options);

    // 获取参数
    var token = options.token || "";
    var code = options.code || "";
    var password = options.password || "";

    // 模式4：扫码进入，解析二维码获取卡号密码
    if (options.q !== undefined) {
      console.log("开始解析二维码:", options.q);
      const parsedData = await app.tools.parse_qrcode_promise(this, decodeURIComponent(options.q));
      console.log("二维码解析结果:", parsedData);

      // 页面数据已自动设置，直接使用解析后的参数
      code = parsedData.code || "";
      password = parsedData.password || "";

      console.log("code:" + code);
      console.log("password:" + password);
    }

    // 现在所有参数都已准备好，执行统一的加载逻辑
    // 模式1：直接带token参数
    if (token) {
      this.setData({
        token: token,
        loading: true,
        show_error: false,
      });
      this.loadGiftInfoByToken(token);
    }
    // 模式2：带code+password参数（包括扫码解析后的参数）
    else if (code && password) {
      this.setData({
        code: code,
        password: password,
        loading: true,
        show_error: false,
      });
      this.loadGiftInfo(code, password);
    }
    // 模式3：无参数或参数不全，跳转到扫码页面
    else {
      wx.redirectTo({
        url: "/pages/code/index",
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 处理从地址选择页面返回的地址
    var selected_address = wx.getStorageSync("picker_address");
    if (selected_address) {
      this.setData({
        selected_address: selected_address,
      });
      wx.removeStorageSync("picker_address");

      // 地址选择后检查是否可以领取礼品
      this.checkCanClaimGift();
    }

    // 检查是否有选中的商品（只有在已有token的情况下才预览）
    if (this.data.token) {
      this.refreshSelectedGoods();
    }
  },

  /**
   * 通过token加载送礼信息
   * @param {string} token - 验证token
   */
  loadGiftInfoByToken: function (token) {
    var that = this;

    // 设置加载状态
    that.setData({
      loading: true,
      show_error: false,
      error_message: "",
    });

    // 直接用token调用gift.info接口
    app.request({
      url: app.api.gift.info,
      data: {
        token: token,
      },
      success: function (res) {
        console.log("gift info:", res.data);

        // 处理新的数据结构
        var gift_records = res.data.gift_records;
        var order_detail = res.data.order_detail;

        // 判断是否可以送礼：没有送礼记录时可以送礼
        var can_send_gift = !gift_records || !gift_records.guid;

        that.setData({
          coupon_info: res.data.coupon_info,
          coupon_send_note: res.data.coupon_send_note,
          gift_records: gift_records,
          order_detail: order_detail,
          can_send_gift: can_send_gift,
          loading: false,
        });

        // 加载完成后检查是否可以领取礼品
        that.checkCanClaimGift();

        // 加载卡券信息成功后，刷新选中的商品预览
        that.refreshSelectedGoods();
      },
      fail: function (res) {
        // gift.info 接口调用失败
        that.showError(res.msg || "获取礼品信息失败，请重试");
      },
    });
  },

  /**
   * 加载送礼信息（统一方法）
   * @param {string} code - 卡号
   * @param {string} password - 密码
   */
  loadGiftInfo: function (code, password) {
    var that = this;

    // 检查是否是新的卡号（支持跨会话检测）
    const lastCode = wx.getStorageSync(LAST_CODE_KEY) || "";
    if (lastCode && lastCode !== code) {
      console.log("=== 检测到新卡号，清空商品缓存 ===");
      console.log("上次卡号:", lastCode, "新卡号:", code);
      wx.removeStorageSync(CACHE_KEY);

      // 清空页面商品显示数据
      that.setData({
        selected_goods: [],
        total_selected_count: 0,
        total_selected_price: 0,
        show_selected_goods: false,
        gift_message: "",
        gift_password: "",
        expire_time_index: 0,
        gift_expire_time_type: "",
      });
    }

    // 缓存当前卡号
    wx.setStorageSync(LAST_CODE_KEY, code);

    // 设置加载状态
    that.setData({
      loading: true,
      show_error: false,
      error_message: "",
    });

    // 第一步：先验证卡号密码获取token
    app.request({
      url: app.api.code.verify_code,
      data: {
        code: code,
        password: password,
        pick_up_type: 1,
      },
      success: function (verifyResult) {
        console.log("verify_code 结果:", verifyResult);

        if (verifyResult.data.status == 1) {
          // 验证成功，获取token
          const token = verifyResult.data.data.token;
          that.setData({
            token: token,
            code: code,
            password: password,
          });

          // 调用token加载方法
          that.loadGiftInfoByToken(token);
        } else {
          // 验证失败，显示后端返回的错误信息
          that.showError(verifyResult.msg || "卡号或密码错误，请重新输入");
        }
      },
      fail: function (res) {
        // verify_code 接口调用失败
        that.showError(res.msg || "网络错误，请稍后重试");
      },
    });
  },

  /**
   * 去送礼 - 跳转到商品选择页面
   */
  goSendGift: function () {
    // 直接使用已验证的token跳转到商品选择页面
    wx.navigateTo({
      url: `/pages/code/detail?bid=${app.ext_config.bid}&token=${this.data.token}`,
    });
  },

  /**
   * 刷新选中的商品（从商品选择页面返回时调用）
   */
  refreshSelectedGoods: function () {
    // 读取 detail.js 的数据格式
    const choose_goods_info = wx.getStorageSync(CACHE_KEY) || [];

    console.log("读取的商品数据:", { choose_goods_info });

    if (choose_goods_info.length > 0) {
      // 有选中商品，调用预览接口获取完整商品信息
      // 注意：此方法只在有token的情况下被调用
      this.loadSelectedGoodsPreview(choose_goods_info);
    } else {
      // 没有选中商品，清空显示
      this.setData({
        selected_goods: [],
        total_selected_count: 0,
        total_selected_price: 0,
        show_selected_goods: false,
      });
    }
  },

  /**
   * 加载选中商品的预览信息
   * @param {Array} choose_goods_info - 缓存的商品信息
   */
  loadSelectedGoodsPreview: function (choose_goods_info) {
    const that = this;

    console.log("=== 开始加载商品预览 ===");
    console.log("Token:", that.data.token);
    console.log("商品信息:", choose_goods_info);

    // 再次确认token存在
    if (!that.data.token) {
      console.error("Token不存在，无法调用预览接口");
      return;
    }

    // 调用预览接口获取完整商品信息
    app.request({
      url: app.api.code.submit_preview,
      data: {
        way: 3,
        type: 1, // 默认快递配送
        goods_info: choose_goods_info,
        token: that.data.token,
        province_id: 0,
        city_id: 0,
        area_id: 0,
      },
      success: function (result) {
        console.log("=== 礼品商品预览结果 ===");
        console.log(result);

        if (result.data && result.data.goods_list) {
          // 直接使用后端返回的完整商品数据（已包含SKU信息）
          const goods_list = result.data.goods_list;

          // 计算总数量和总价格
          let total_count = 0;
          let total_price = 0;

          goods_list.forEach((item) => {
            total_count += item.num;
            total_price += parseFloat(item.price || 0) * item.num;
          });

          that.setData({
            selected_goods: goods_list,
            total_selected_count: total_count,
            total_selected_price: total_price.toFixed(2),
            show_selected_goods: true,
          });

          console.log("商品预览加载成功，商品数量:", goods_list.length);
        } else {
          console.error("预览接口返回数据格式异常:", result);
          that.handlePreviewError(choose_goods_info);
        }
      },
      fail: function (res) {
        console.error("获取商品预览失败:", res);
        that.handlePreviewError(choose_goods_info);
      },
    });
  },

  /**
   * 处理预览失败的情况
   * @param {Array} choose_goods_info - 缓存的商品信息
   */
  handlePreviewError: function (choose_goods_info) {
    // 失败时使用基础信息显示
    const fallback_goods = choose_goods_info.map((item) => ({
      guid: item.guid,
      name: "商品信息加载失败",
      pic: "",
      price: 0,
      num: item.amount,
      attr: item.attr || [],
    }));

    this.setData({
      selected_goods: fallback_goods,
      total_selected_count: choose_goods_info.reduce((sum, item) => sum + item.amount, 0),
      total_selected_price: 0,
      show_selected_goods: true,
    });
  },

  /**
   * 显示/隐藏选中商品列表
   */
  toggleSelectedGoods: function () {
    this.setData({
      show_selected_goods: !this.data.show_selected_goods,
    });
  },

  /**
   * 感谢语输入事件
   */
  onGiftMessageInput: function (e) {
    this.setData({
      gift_message: e.detail.value,
    });
  },

  /**
   * 收礼口令输入事件
   */
  onGiftPasswordInput: function (e) {
    this.setData({
      gift_password: e.detail.value,
    });
  },

  /**
   * 收礼有效期选择事件
   */
  onExpireTimeSelect: function (e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      expire_time_index: index,
      gift_expire_time_type: this.data.expire_time_options[index].value,
    });
  },

  /**
   * 收礼口令输入事件
   */
  onInputGiftPasswordChange: function (e) {
    const input_gift_password = e.detail.value;
    this.setData({
      input_gift_password: input_gift_password,
    });

    // 检查是否可以领取礼品
    this.checkCanClaimGift();
  },

  /**
   * 检查是否可以领取礼品
   */
  checkCanClaimGift: function () {
    const { gift_records, selected_address, input_gift_password } = this.data;

    let canClaim = false;

    // 必须有收货地址
    if (selected_address) {
      // 如果需要收礼口令，则必须输入口令
      if (gift_records && gift_records.need_gift_password) {
        canClaim = input_gift_password && input_gift_password.trim().length > 0;
      } else {
        // 不需要口令，只要有地址就可以
        canClaim = true;
      }
    }

    this.setData({
      canClaimGift: canClaim,
    });
  },

  /**
   * 确认送礼（提交订单）
   */
  confirmSendGift: function () {
    // 检查是否有选中商品（使用页面数据，避免重复读取缓存）
    if (this.data.selected_goods.length === 0 || this.data.total_selected_count === 0) {
      wx.showToast({
        title: "请先选择商品",
        icon: "none",
      });
      return;
    }

    // 直接使用缓存中的商品信息（确保数据完整性）
    const goods_info = wx.getStorageSync(CACHE_KEY) || [];

    console.log("=== 提交订单时的商品信息 ===");
    console.log("从缓存读取的goods_info:", goods_info);

    // 验证缓存数据是否有效
    if (goods_info.length === 0) {
      wx.showToast({
        title: "商品数据异常，请重新选择",
        icon: "none",
      });
      return;
    }

    // 组装送礼数据
    const giftData = {
      // 卡券信息（使用token而不是明文卡号密码）
      token: this.data.token,
      goods_info: JSON.stringify(goods_info),
      total_count: this.data.total_selected_count,
      total_price: this.data.total_selected_price, // 送礼配置
      gift_message: this.data.gift_message,
      gift_password: this.data.gift_password,
      gift_expire_time_type: this.data.gift_expire_time_type,
    };

    console.log("=== 确认送礼 - 组装的数据 ===");
    console.log("完整送礼数据:", giftData);

    // 直接执行提交逻辑，不需要确认提示
    this.submitGiftOrder(giftData);
  },

  /**
   * 提交送礼订单
   */
  submitGiftOrder: function (giftData) {
    console.log("=== 开始提交送礼订单 ===");
    console.log("提交的数据:", JSON.stringify(giftData, null, 2));

    const that = this;

    // 组装订单提交数据
    const submitData = {
      // === 订单类型标识 ===
      way: 3, // 固定值3，标识送礼订单

      // === 卡券验证信息 ===
      gift_coupon_code: giftData.card_number, // 送礼券卡号
      gift_coupon_password: giftData.card_password, // 送礼券密码

      // === 商品信息 ===
      goods_info: giftData.goods_info, // 商品详情（已经是JSON字符串）

      // === 送礼配置 ===
      gift_message: giftData.gift_message || "", // 感谢语
      gift_password: giftData.gift_password || "", // 收礼口令
      gift_expire_time_type: giftData.gift_expire_time_type, // 收礼有效期类型

      // === 配送设置 ===
      type: 1, // 固定值1，快递配送
      address_guid: "", // 送礼订单不需要收货地址

      // === 支付设置 ===
      use_money_paid: false, // 送礼不使用储值支付
      use_point_paid: false, // 送礼不使用积分支付
      paid_money: 0, // 储值支付金额
      paid_point: 0, // 积分支付金额
      paid_wechat: giftData.total_price, // 微信支付金额（全额）
      coupon_send_note_guid: that.data.coupon_send_note.guid, // === 分享信息 ===
      share_member_guid: wx.getStorageSync("share_member_guid") || "",
      share_user_guid: wx.getStorageSync("share_user_guid") || "",
    };

    console.log("=== 组装的订单数据 ===");
    console.log("submitData:", JSON.stringify(submitData, null, 2));

    wx.showLoading({
      title: "提交中...",
    });

    // 提交订单
    app.request({
      url: app.api.order.submit,
      data: submitData,
      success: function (res) {
        console.log("=== 订单提交成功 ===");
        console.log("订单返回数据:", res);

        // 订单提交成功后立即清空商品缓存
        console.log("=== 订单提交成功，清空商品缓存 ===");
        wx.removeStorageSync(CACHE_KEY);

        // 同时清空页面显示的商品数据
        that.setData({
          selected_goods: [],
          total_selected_count: 0,
          total_selected_price: 0,
          show_selected_goods: false,
        });

        const order_guid = res.data.order_guid;
        const payment = res.data.pay_type;

        if (payment == 1) {
          // 微信支付
          that.processWechatPayment(order_guid, payment);
        } else {
          // 其他支付方式（理论上送礼只支持微信支付）
          wx.hideLoading();
          that.handlePaymentSuccess();
        }
      },
      fail: function (err) {
        console.log("=== 订单提交失败 ===");
        console.log("错误信息:", err);

        wx.hideLoading();
        wx.showModal({
          title: "提交失败",
          content: err.msg || "订单提交失败，请重试",
          showCancel: false,
          confirmText: "确认",
        });
      },
    });
  },

  /**
   * 确认收礼
   */
  confirmClaimGift: function () {
    var that = this;

    // 检查收货地址
    if (!that.data.selected_address) {
      wx.showModal({
        title: "提示",
        content: "请选择收货地址",
        showCancel: false,
      });
      return;
    }

    // 检查收礼口令（如果需要的话）
    if (that.data.gift_records && that.data.gift_records.need_gift_password) {
      if (!that.data.input_gift_password || that.data.input_gift_password.trim().length === 0) {
        wx.showModal({
          title: "提示",
          content: "请输入收礼口令",
          showCancel: false,
        });
        return;
      }
    }

    wx.showLoading({
      title: "处理中...",
    });

    // 组装提交数据
    const submitData = {
      gift_record_guid: that.data.gift_records.guid,
      address_guid: that.data.selected_address.guid,
    };

    // 如果需要收礼口令，则添加到提交数据中
    if (that.data.gift_records && that.data.gift_records.need_gift_password) {
      submitData.gift_password = that.data.input_gift_password.trim();
    }

    console.log("=== 提交收礼数据 ===");
    console.log("submitData:", submitData);

    // 提交收礼请求
    app.request({
      url: app.api.gift.claim_gift,
      data: submitData,
      success: function (res) {
        wx.hideLoading();
        wx.showModal({
          title: "收礼成功",
          content: "礼品将尽快为您配送，请耐心等待",
          showCancel: false,
          success: function () {
            // 重新加载页面数据
            that.loadGiftInfo(that.data.code, that.data.password);
            that.setData({
              selected_address: null,
              input_gift_password: "", // 清空输入的口令
            });
          },
        });
      },
      fail: function (res) {
        wx.hideLoading();
        wx.showModal({
          title: "收礼失败",
          content: res.msg || "操作失败，请稍后重试",
          showCancel: false,
        });
      },
    });
  },

  /**
   * 复制卡号
   */
  copyCode: function () {
    var that = this;
    wx.setClipboardData({
      data: that.data.coupon_send_note.code,
      success: function () {
        wx.showToast({
          title: "卡号已复制",
          icon: "success",
        });
      },
    });
  },

  /**
   * 显示错误状态
   */
  showError: function (message) {
    this.setData({
      loading: false,
      show_error: true,
      error_message: message,
    });
  },

  /**
   * 去首页逛逛
   */
  goHome: function () {
    wx.redirectTo({
      url: "/pages/index/index",
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.code && this.data.password) {
      // 重置错误状态
      this.setData({
        show_error: false,
        error_message: "",
      });
      this.loadGiftInfo(this.data.code, this.data.password);
    } else if (this.data.token) {
      // 如果有token，直接用token刷新
      this.setData({
        show_error: false,
        error_message: "",
      });
      this.loadGiftInfoByToken(this.data.token);
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return app.pageOnShareAppMessage(this, {}, this.data.coupon_info.name + " - 送礼卡券", "/pages/gift/status?code=" + this.data.code);
  },
});
