// pages/code/submit_order.js
import todo from "../../component/calendar/plugins/todo";
import selectable from "../../component/calendar/plugins/selectable";
import solarLunar from "../../component/calendar/plugins/solarLunar/index";
import timeRange from "../../component/calendar/plugins/time-range";
import week from "../../component/calendar/plugins/week";
import holidays from "../../component/calendar/plugins/holidays/index";
import plugin from "../../component/calendar/plugins/index";

plugin.use(todo).use(solarLunar).use(selectable).use(week).use(timeRange).use(holidays);

var app = getApp(); //获取应用实例

Page({
  /**
   * 日历初次渲染完成后触发事件，如设置事件标记
   */
  afterCalendarRender(e) {
    console.log("afterCalendarRender", e);
    let calendar = this.selectComponent(".calendar").calendar;
    // calendar.jump({
    //   year: 2018,
    //   month: 6,
    //   date: 6
    // });
    console.log(this.data.config.disable_date_list);
    calendar.disableDates(this.data.config.disable_date_list);

    // disable_date_after
    // calendar.setCalendarConfig({
    //   theme: 'elegant',
    //   disableMode: {
    //     // 禁用某一天之前/之后的所有日期
    //     // type: 'after', // ['before', 'after']
    //     // date: '2022-04-29' // 无该属性或该属性值为假，则默认为当天
    //   },
    // });
  },
  /**
   * 日期点击事件（此事件会完全接管点击事件），需自定义配置 takeoverTap 值为真才能生效
   * currentSelect 当前点击的日期
   */
  takeoverTap(e) {
    console.log("takeoverTap", e.detail); // => { year: 2019, month: 12, date: 3, ...}
  },
  /**
   * 选择日期后执行的事件
   */
  afterTapDate(e) {
    console.log("afterTapDate", e.detail); // => { year: 2019, month: 12, date: 3, ...}
    let date = e.detail.year + "-" + e.detail.month + "-" + e.detail.date;
    // var now = new Date();
    // let today = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
    // console.log(date);
    // console.log(today);
    // if (date <= today) {
    //   wx.showToast({
    //     title: '请不要选择今天或者更早的日期哦',
    //     icon: 'none',
    //     duration: 1500
    //   })
    //   return false;
    // }
    this.setData({
      request_send_or_pick_up_time: date,
      calendar: false,
    });
  },
  ComponentListener(e) {
    let info = e.detail;
    console.log(info);
    let before_province_id = this.data.province_id;
    let before_city_id = this.data.city_id;
    let before_area_id = this.data.area_id;
    this.setData({
      province_id: info.selectProvinceId,
      city_id: info.selectCityId,
      area_id: info.selectAreaId,
    });

    if (info.selectProvinceId != before_province_id || info.selectCityId != before_city_id || info.selectAreaId != before_area_id) {
      this.submit_preview();
    }
  },
  /**
   * 当日历滑动时触发
   */
  onSwipe(e) {
    console.log("onSwipe", e.detail);

    let that = this;
    let calendarConfig = that.data.calendarConfig;
    calendarConfig.disableMode.date = that.data.config.disable_date_before;
    that.setData({
      calendarConfig: calendarConfig,
    });
    let calendar = this.selectComponent(".calendar").calendar;
    // calendar.jump({
    //   year: 2018,
    //   month: 6,
    //   date: 6
    // });
    console.log(this.data.config.disable_date_list);
    calendar.disableDates(this.data.config.disable_date_list);
  },
  /**
   * 当日历滑动时触发(适用于周视图)
   * 可在滑动时按需在该方法内获取当前日历的一些数据
   */
  whenChangeWeek(e) {
    console.log("whenChangeWeek", e.detail);
    let calendar = this.selectComponent(".calendar").calendar;
    // calendar.jump({
    //   year: 2018,
    //   month: 6,
    //   date: 6
    // });
    console.log(this.data.config.disable_date_list);
    calendar.disableDates(this.data.config.disable_date_list);
  },
  /**
   * 当改变月份时触发
   * => current 当前年月 / next 切换后的年月
   */
  whenChangeMonth(e) {
    console.log("whenChangeMonth", e.detail);
    // => { current: { month: 3, ... }, next: { month: 4, ... }}
    const calendar = this.selectComponent(".calendar").calendar;
    // calendar.jump({
    //   year: 2018,
    //   month: 6,
    //   date: 6
    // });
    console.log(this.data.config.disable_date_list);
    calendar.disableDates(this.data.config.disable_date_list);
  },
  /**
   * 页面的初始数据
   */
  data: {
    token: null,
    calendar: false,
    user_info: {},
    submit_preview: {},
    exchange_times: 0,
    cycle_delivery_interval_days: 0,
    request_send_or_pick_up_time: "",
    config: {},
    extend_field_data: {},
    last_order_info_mobile: "",
    store_list: {}, //门店列表
    show_store: false, // 是否展示门店选择组件
    province_id: 0,
    city_id: 0,
    area_id: 0,
    type: 0, // 0 初始化 1 快递配送 2 到店自提
    choose_goods_info: [], // 包含SKU信息的商品数据
    calendarConfig: {
      multi: false, // 是否开启多选,
      //weekMode: true, // 周视图模式
      theme: "default", // 日历主题，目前共两款可选择，默认 default 及 elegant，自定义主题色在参考 /theme 文件夹
      //showLunar: true, // 是否显示农历，此配置会导致 setTodoLabels 中 showLabelAlways 配置失效
      inverse: true, // 单选模式下是否支持取消选中,
      //markToday: '今', // 当天日期展示不使用默认数字，用特殊文字标记
      //hideHeader: true, // 隐藏日历头部操作栏
      //takeoverTap: true, // 是否完全接管日期点击事件（日期不会选中)
      emphasisWeek: true, // 是否高亮显示周末日期
      chooseAreaMode: true, // 开启日期范围选择模式，该模式下只可选择时间段
      //showHolidays: true, // 显示法定节假日班/休情况，需引入holidays插件
      //showFestival: true, // 显示节日信息（如教师节等），需引入holidays插件
      highlightToday: true, // 是否高亮显示当天，区别于选中样式（初始化时当天高亮并不代表已选中当天）
      //defaultDate: '2018-3-6', // 默认选中指定某天，如需选中需配置 autoChoosedWhenJump: true
      preventSwipe: false, // 是否禁用日历滑动切换月份
      //firstDayOfWeek: 'Mon', // 每周第一天为周一还是周日，默认按周日开始
      onlyShowCurrentMonth: true, // 日历面板是否只显示本月日期
      autoChoosedWhenJump: true, // 设置默认日期及跳转到指定日期后是否需要自动选中
      disableMode: {
        // 禁用某一天之前/之后的所有日期
        type: "before", // ['before', 'after']
        date: "", // 无该属性或该属性值为假，则默认为当天
      },
    },
  },
  // 监听输入
  watch_exchange_times: function (event) {
    this.setData({
      exchange_times: event.detail.value,
    });
  },

  // 监听输入
  watch_cycle_delivery_interval_days: function (event) {
    this.setData({
      cycle_delivery_interval_days: event.detail.value,
    });
  },
  show_calendar: function () {
    this.setData({
      calendar: !this.data.calendar,
    });
  },
  show_address: function () {
    this.setData({
      type: 1,
    });
    this.submit_preview();
  },
  get_store_list: function () {
    let page = this;
    app.request({
      url: app.api.store.list,
      data: {
        token: page.data.token,
        lat: page.data.latitude,
        lon: page.data.longitude,
      },
      success: function (result) {
        console.log(result);
        page.setData({
          store_list: result.data,
        });
      },
    });
  },
  show_pick_up_store: function () {
    console.log("show_pick_up_store");
    let page = this;
    page.setData({
      type: 2,
    });
    wx.getLocation({
      type: "wgs84",
      success(res) {
        console.log(res);
        const latitude = res.latitude;
        const longitude = res.longitude;
        const speed = res.speed;
        const accuracy = res.accuracy;
        page.setData({
          latitude: res.latitude,
          longitude: res.longitude,
        });
        page.get_store_list();
      },
      fail(res) {
        console.log(res);
        page.get_store_list();
      },
    });
    this.submit_preview();
  },
  showShop: function (e) {
    console.log("showShop");
    console.log(this.data.store_list);
    var page = this;
    // page.dingwei();
    page.setData({
      show_store: true,
    });
  },
  pickShop: function (e) {
    var page = this;
    var index = e.currentTarget.dataset.index;
    if (index == "-1" || index == -1) {
      page.setData({
        store: false,
        show_store: false,
      });
    } else {
      page.setData({
        store: page.data.store_list[index],
        show_store: false,
      });
    }
    // page.getPrice();
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    var that = this;

    // 读取包含SKU信息的商品数据
    let choose_goods_info = wx.getStorageSync("choose_goods_info") || [];

    console.log("=== 订单提交页面缓存数据 ===");
    console.log("choose_goods_info:", choose_goods_info);

    this.setData({
      token: options.token,
      user_info: wx.getStorageSync("user_info"),
      choose_goods_info: choose_goods_info,
    });
    app.request({
      url: app.api.code.get_submit_order_config,
      data: {
        token: options.token,
      },
      success: function (result) {
        let calendarConfig = that.data.calendarConfig;
        calendarConfig.disableMode.date = result.data.disable_date_before;
        console.log(result);
        that.setData({
          config: result.data,
          cycle_delivery_interval_days: result.data.coupon_info.cycle_delivery_min_interval_days,
          exchange_times: result.data.available_num,
          type: result.data.default_type,
          calendarConfig: calendarConfig,
        });
        if (result.data.last_order_info.mobile !== undefined) {
          that.setData({
            last_order_info_mobile: result.data.last_order_info.mobile,
          });
        }
        if (result.data.is_show_address == 1) {
          // 开启快递配送 再赋值省市区id,便于预览订单计算运费
          that.setData({
            province_id: result.data.last_order_info.province_id,
            city_id: result.data.last_order_info.city_id,
            area_id: result.data.last_order_info.area_id,
          });
        }
        if (result.data.default_type == 2) {
          // 只开启到店自提需要初始化门店列表,否则就需要点击tab触发
          that.show_pick_up_store();
        }
        that.submit_preview();
      },
    });
  },
  submit_preview() {
    //预览商品和计算价格
    let that = this;
    app.request({
      url: app.api.code.submit_preview,
      data: {
        way: 1,
        type: that.data.type,
        goods_info: that.data.choose_goods_info, // 使用包含SKU信息的格式
        token: that.data.token,
        province_id: that.data.province_id,
        city_id: that.data.city_id,
        area_id: that.data.area_id,
      },
      success: function (result) {
        console.log("=== 订单预览结果 ===");
        console.log(result);

        // 处理商品数据，确保SKU规格信息正确显示
        if (result.data.goods_list) {
          result.data.goods_list = that.processGoodsListForDisplay(result.data.goods_list);
        }

        that.setData({
          submit_preview: result.data,
        });
      },
    });
  },

  /**
   * 处理商品列表数据，确保SKU规格信息正确显示
   */
  processGoodsListForDisplay: function (goods_list) {
    const choose_goods_info = this.data.choose_goods_info;

    // 为每个商品添加对应的SKU属性信息
    return goods_list.map((goods) => {
      // 在choose_goods_info中查找对应的商品信息
      const goodsInfo = choose_goods_info.find((info) => info.guid === goods.guid);

      if (goodsInfo && goodsInfo.attr && goodsInfo.attr.length > 0) {
        // 如果找到了SKU属性信息，添加到商品对象中
        goods.attr = goodsInfo.attr;
        console.log(`商品 ${goods.name} 的SKU属性:`, goods.attr);
      }

      return goods;
    });
  },

  get_mobile(e) {
    let that = this;
    return app.auth.getPhoneNumber(e, function (result) {
      let user_info = result.data.user_info;
      that.setData({
        user_info: user_info,
        last_order_info_mobile: result.data.user_info.mobile,
      });
      wx.setStorageSync("user_info", user_info);
    });
  },
  bindPickerChange(e) {
    console.log(e.detail.value);
    console.log(e.detail);
    console.log(e.target.dataset.field_info);
    console.log(e.target.dataset.index);
    console.log(e);
    let field_info = e.target.dataset.field_info;
    let selected_value = e.target.dataset.field_info.option[e.detail.value];
    this.data.config.show_extend_field_array[e.target.dataset.index].selected_value = selected_value;
    this.setData({
      ["extend_field_data." + field_info.key_name]: selected_value,
    });
    console.log(this.data.extend_field_data);
  },
  submit: function (e) {
    let upload = this.selectComponent("#upload");
    let data = e.detail.value;
    console.log(data);
    let extend_field_data = this.data.extend_field_data;
    for (var key in extend_field_data) {
      data[key] = extend_field_data[key];
    }
    if (upload) {
      data.remark_image_list = upload.getFiles();
    }
    data.token = this.data.token;
    data.type = this.data.type;
    data.request_send_or_pick_up_time = this.data.request_send_or_pick_up_time;
    if (this.data.type == 1) {
      let area_picker = this.selectComponent(".area_picker");
      data.province_id = area_picker.data.selectProvinceId;
      data.city_id = area_picker.data.selectCityId;
      data.area_id = area_picker.data.selectAreaId;
    }
    if (this.data.type == 2 && this.data.store) {
      data.request_send_or_pick_up_store_guid = this.data.store.guid;
    }
    // 使用包含SKU信息的格式（与H5端保持一致）
    data.goods_info = this.data.choose_goods_info;
    data.share_member_guid = wx.getStorageSync("share_member_guid");
    data.share_user_guid = wx.getStorageSync("share_user_guid");

    console.log("=== 订单提交数据 ===");
    console.log("goods_info:", data.goods_info);

    if (!data.goods_info || data.goods_info.length === 0) {
      wx.showModal({
        title: "系统提示",
        content: "请返回首页重新操作",
        showCancel: false,
      });
      return;
    }
    app.request({
      url: app.api.code.submit_order,
      data: data,
      success: function (result) {
        console.log(result);
        // 清除两种缓存格式
        wx.removeStorageSync("choose_goods_info");
        if (result.data.status == 0) {
          wx.redirectTo({
            url: "/pages/code/success?bid=" + app.ext_config.bid + "&order_guid=" + result.data.order_guid + "&bill_number=" + result.data.bill_number,
          });
        } else if (result.data.status == -1) {
          wx.redirectTo({
            url: "/pages/pay/submit?bid=" + app.ext_config.bid + "&order_guid=" + result.data.order_guid + "&bill_number=" + result.data.bill_number + "&type=goods",
          });
          return;
        }
      },
    });
  },
  // 隐藏门店选择组件
  hideStorePicker: function () {
    this.setData({
      show_store: false,
    });
  },
  openCustomerServiceChat: function () {
    var that = this;
    wx.openCustomerServiceChat({
      extInfo: {
        url: that.data.config.wechat_service_url,
      },
      corpId: that.data.config.wechat_service_corp_id,
      showMessageCard: true,
      success(res) {
      },
      fail(res) {
        wx.showModal({
          title: "系统提示",
          content: res.errMsg,
          showCancel: false,
        });
      },
    });
  },
  phoneClick: function (event) {
    wx.makePhoneCall({
      phoneNumber: event.target.dataset.phonenumber,
      success() {
      },
      fail() {
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },
});
