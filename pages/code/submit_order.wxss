/* pages/code/submit_order.wxss */
@import "/component/calendar/index.wxss";

.top-bar {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  background: #fff;
  /* border-top: 1rpx solid #e3e3e3; */
  /* border-bottom: 1rpx solid #e3e3e3; */
  z-index: 999;
}

.top-bar view {
  text-align: center;
}

.top-bar view text {
  height: 90rpx;
  line-height: 90rpx;
  border-bottom: 1rpx solid transparent;
  width: auto;
  display: inline-block;
}

.top-bar view.active text {
  color: #ff4544;
  border-bottom-color: #ff4544;
}

.address-picker {
  background: #fff;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;
  border-top: 1rpx solid #e3e3e3;
  /* border-bottom: 1rpx solid #e3e3e3; */
}
.coupon-picker {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  background: #fff;
  border-top: 1rpx solid #e3e3e3;
}
.coupon-list {
  padding: 34rpx;
}
.coupon-list .coupon-item {
  height: 152rpx;
  width: 682rpx;
  position: relative;
  margin-bottom: 24rpx;
}
.coupon-list .coupon-item.coupon-status-1 .coupon-right,
.coupon-list .coupon-item.coupon-status-2 .coupon-right {
  color: rgba(0, 0, 0, 0.35) !important;
}
.coupon-list .coupon-item .coupon-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.coupon-list .coupon-item .coupon-status-icon {
  width: 140rpx;
  height: 98rpx;
  position: absolute;
  top: 0;
  right: 8rpx;
  z-index: 1;
}
.coupon-list .coupon-item .coupon-left {
  color: #fff;
  width: 202rpx;
}
.coupon-list .coupon-item .coupon-right {
  padding: 20rpx 10rpx;
}
.active {
  color: #ff4544;
}
.shop-block {
  width: 100%;
  height: 70rpx;
  padding-left: 24rpx;
}
.shop-address {
  width: 100%;
  background-color: #fff;
  padding: 30rpx 24rpx;
}
.page {
  height: 100%;
}

.weui-label {
  width: 4.1em;
}

.layui-fixbar {
  position: fixed;
  right: 50rpx;
  bottom: 100rpx;
  z-index: 999999;
}

.wechat_service_img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.wechat_service_button {
  /* width: 80rpx;
  height: 80rpx; */
  background-color: transparent;
}

.wechat_service_button2::after {
  border: none;
}

.weui-media-box {
  padding-top: 12rpx;
  padding-bottom: 12rpx;
}

/* 门店列表关闭按钮样式 */
.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 6rpx;
  right: 20rpx;
}

.shop-block .close-btn image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.shop-block .close-btn:active image {
  opacity: 1;
}

/* 获取手机号按钮样式修复 */
.weui-vcode-btn {
  width: 120rpx !important;
  height: 64rpx !important;
  line-height: 64rpx !important;
  font-size: 28rpx !important;
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 8rpx !important;
  flex-shrink: 0 !important;
  min-width: 120rpx !important;
  max-width: 120rpx !important;
}

/* 确保手机号输入框容器的布局正确 */
.weui-cell__ft {
  flex-shrink: 0;
  margin-left: 20rpx;
}
