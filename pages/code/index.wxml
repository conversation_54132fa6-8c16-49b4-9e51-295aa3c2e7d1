<!--pages/index/index.wxml-->
<!--轮播图-->
<import src="/common/tabbar/tabbar.wxml" />
<template is="tabbar" data="{{tabbar}}" />
<view class="page">
  <view class="page after-navber">


    <swipers banner_list="{{banner}}"> </swipers>


    <!-- <swiper indicator-dots="{{banner_config.indicatorDots}}" autoplay="{{banner_config.autoplay}}" interval="{{banner_config.interval}}" duration="{{banner_config.duration}}" indicator-color="{{banner_config.indicatorColor}}" indicator-active-color="{{banner_config.activeColor}}" bindchange='bindchange' style="height:{{imgheights[current]}}rpx;">
      <block wx:for="{{banner}}" wx:key="imgUrls_key">
        <swiper-item>
          <navigator url="{{item.url}}" hover-class="navigator-hover">
            <image src="{{item.img_url}}" class='image-view' style="height:{{imgheights[current]}}rpx;width:{{imgwidth}}rpx;" bindload="imageLoad" data-src='{{item.url}}'></image>
          </navigator>
        </swiper-item>
      </block>
    </swiper> -->

    <form bindsubmit="exchange">

      <view class="top-bar flex-row" wx:if="{{config.pick_up_type>2}}">
        <view class="flex-grow-1 {{show_pick_up_type==1?'active':''}}" bindtap="show_code_password">
          <text>卡号密码</text>
        </view>
        <view class="flex-grow-1 {{show_pick_up_type==2?'active':''}}" bindtap="show_phone_verify_code">
          <text>手机验证码</text>
        </view>
      </view>

      <view class="weui-cells__group weui-cells__group_form" wx:if="{{show_pick_up_type==1}}">

        <view class="weui-cells weui-cells_form">
          <view class="weui-cell weui-cell_active">
            <view class="weui-cell__hd"><label class="weui-label">{{config.code_field_alias}}</label></view>
            <view class="weui-cell__bd">
              <input bindinput="{{!config.code_is_all_number ? 'codeInput' :''}}" class="weui-input" name='code' type="{{config.code_is_all_number?'number':'text'}}" value="{{code}}" placeholder="请输入{{config.code_field_alias}}" placeholder-class="weui-input__placeholder" />
            </view>
            <view bindtap="scan" wx:if="{{config.show_scan_button}}">
              <image class="image" style="width: 40rpx;height: 40rpx;" src="/images/scan.png" alt></image>
            </view>
          </view>

          <view class="weui-cell weui-cell_active weui-cell_vcode weui-cell_wrap">
            <view class="weui-cell__hd"><label class="weui-label">{{config.password_field_alias}}</label></view>
            <view class="weui-cell__bd">
              <input class="weui-cell__control weui-cell__control_flex weui-input" type="password" name='password' value="{{password}}" focus="{{password_focus}}" placeholder="请输入{{config.password_field_alias}}" placeholder-class="weui-input__placeholder" />
            </view>
          </view>
        </view>

      </view>

      <view class="weui-cells__group weui-cells__group_form" wx:if="{{show_pick_up_type==2}}">

        <view class="weui-cells weui-cells_form">

          <view class="weui-cell weui-cell_active">
            <view class="weui-cell__hd"><label class="weui-label">手机号</label></view>
            <view class="weui-cell__bd">
              <input class="weui-input" name="phone" type="number" bindinput="phoneChange" placeholder="请输入手机号" value="" placeholder-class="weui-input__placeholder" />
            </view>
          </view>

          <view class="weui-cell weui-cell_active weui-cell_vcode weui-cell_wrap">
            <view class="weui-cell__hd"><label class="weui-label">验证码</label></view>
            <view class="weui-cell__bd">
              <input bindinput="bindVcodeInput" name="verify_code" class="weui-cell__control weui-cell__control_flex weui-input" type="number" focus="{{verify_code_focus}}" placeholder="输入验证码" placeholder-class="weui-input__placeholder" />
              <view aria-role="button" class="weui-cell__control weui-btn weui-btn_default weui-vcode-btn" bindtap="get_verify_code">{{get_verify_code_text}}</view>
            </view>
          </view>

        </view>

      </view>

      <view class="weui-btn-area" style="margin: 20px 0 0 0;">
        <button class='weui-btn' style="width: 90%;" type="primary" formType="submit">确认提交</button>
      </view>
    </form>
    <view class="notice">
      <rich-text nodes="{{config.pick_up_notice}}"></rich-text>
    </view>
    <view style="padding-bottom: 80px;">
      <copy_right></copy_right>
    </view>
  </view>

</view>

<view aria-role="dialog" aria-modal="true" class="fadeIn" wx:if="{{dialog}}">
  <view class="weui-mask"></view>
  <view class="weui-dialog">
    <view class="weui-dialog__hd"><strong class="weui-dialog__title">温馨提示</strong></view>
    <view class="weui-dialog__bd" style="text-align: left;">
      <rich-text nodes="{{config.pick_up_notice_pop}}"></rich-text>
    </view>
    <view class="weui-dialog__ft">
      <view aria-role="button" class="weui-dialog__btn weui-dialog__btn_primary" bindtap="close">知道了</view>
    </view>
  </view>
</view>

<!-- 
<view aria-role="dialog" aria-modal="true">
  <view class="weui-mask weui-transition {{dialog ? 'weui-transition_show' : ''}}" bindtap="close" aria-role="button" aria-label="关闭"></view>
  <view class="weui-half-screen-dialog weui-half-screen-dialog_large weui-transition {{dialog ? 'weui-transition_show' : ''}}">
    <view class="weui-half-screen-dialog__hd">
      <view class="weui-half-screen-dialog__hd__side" bindtap="close">
        <view aria-role="button" class="weui-icon-btn">关闭<i class="weui-icon-close-thin"></i></view>
      </view>
      <view class="weui-half-screen-dialog__hd__main">
        <strong class="weui-half-screen-dialog__title">温馨提示</strong>
      </view>
    </view>
    <view class="weui-half-screen-dialog__bd" style="padding-top: 32px; height: 50px;">
      <rich-text nodes="{{config.pick_up_notice_pop}}"></rich-text>
    </view>
    <view class="weui-half-screen-dialog__ft">
      <view class="weui-half-screen-dialog__btn-area">
        <view aria-role="button" class="weui-btn weui-btn_primary" bindtap="close">我知道了</view>
      </view>
    </view>
  </view>
</view> -->