var app = getApp(); //获取应用实例

// pages/code/success.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    options: {},
    data: {},
  },
  goto_order_detail: function () {
    wx.redirectTo({
      url: '/pages/order/detail?bid=' + app.ext_config.bid + '&order_guid=' + this.data.options.order_guid + '&bill_number=' + this.data.options.bill_number,
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    app.pageOnLoad(this, options);
    this.setData({
      options: options
    })
    this.load_data();
  },
  load_data: function () {
    let page = this;
    app.request({
      url: app.api.code.get_after_submit_order_config,
      data: page.data.options,
      success: function (result) {
        page.setData({
          data: result.data
        })
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})