/* pages/code/active.wxss */
/* pages/index/index.wxss */

/*轮播图*/

.slide-image {
  width: 100%;
}

.weui-grid {
  text-align: center;
}

.weui-grid__icon {
  width: 120rpx;
  height: 120rpx;
}

.notice {
  padding: 20px;
}


.login-container {
  height: 100%;
  padding: 60px 30px;
  /* background: #fff; */
}

.app-info {
  position: relative;
  text-align: center;
}

.app-info:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  /* height: 1px;
  border-bottom: 1px solid #e5e5e5; */
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.app-info .app-logo {
  display: block;
  width: 64px;
  height: 64px;
  margin: 10px auto;
  border-radius: 4px;
  padding-bottom: 50px;
}

.app-info .app-name {
  font-weight: bold;
  font-size: 18px;
  color: #000;
}