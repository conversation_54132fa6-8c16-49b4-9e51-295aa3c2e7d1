<!--pages/code/submit_order.wxml-->
<view style="padding-top:0 ;padding-bottom: 0; background-color: #fff;">
  <view class="weui-cells__group weui-cells__group_form">
    <view class="weui-cells__title">商品信息</view>
    <view aria-labelledby="js_p1m1_bd" class="weui-media-box weui-media-box_appmsg" wx:for="{{submit_preview.goods_list}}" wx:key="index">
      <view aria-hidden="true" class="weui-media-box__hd">
        <image style="border-radius: 8rpx;" class="weui-media-box__thumb" src="{{item.pic}}" alt></image>
      </view>

      <view aria-hidden="true" id="js_p1m1_bd" class="weui-media-box__bd">
        <text class="weui-media-box__title">{{item.name}}</text>
        <view class="weui-media-box__desc" style="color: #999;"> 数量: {{item.num}} {{item.unit}}
        </view>
        <view wx:if="{{item.price>0}}" class="flex-row" style="float: right;color: #999;margin-top: -35rpx;">
          ￥ {{item.price}}
        </view>

        <!-- SKU规格信息显示（优先显示attr数组，兼容旧的specs字段） -->
        <view wx:if="{{item.attr && item.attr.length > 0}}" class="goods-specs-container" style="margin-top: 10rpx;">
          <view wx:for="{{item.attr}}" wx:key="attr_id" class="goods-specs-item" style="font-size: 24rpx; background-color: #EDF1FC; padding: 4rpx 10rpx; display: inline-block; border-radius: 4rpx; margin-right: 10rpx; margin-bottom: 5rpx;">
            {{item.attr_name}}
          </view>
        </view>
        <view wx:elif="{{item.specs}}" class="goods-name" style="font-size: 24rpx; background-color: #EDF1FC;padding:4rpx 10rpx 4rpx 10rpx;display: inline-block;border-radius: 4rpx;">{{item.specs}}</view>
      </view>

    </view>
  </view>
</view>
<form bindsubmit="submit">
  <view class="" style="padding-top:0 ;padding-bottom: 0;background-color: #fff;">
    <view class="weui-cells__group weui-cells__group_form">
      <view class="weui-cells__title">订单信息</view>
      <view class="weui-cells">

        <view class="top-bar flex-row" wx:if="{{config.is_show_request_send_or_pick_up_store!=0}}">
          <view class="flex-grow-1 {{type==1?'active':''}}" bindtap="show_address" wx:if="{{config.is_show_address==1}}">
            <text>快递配送</text>
          </view>
          <view class="flex-grow-1 {{type==2?'active':''}}" bindtap="show_pick_up_store" wx:if="{{config.is_show_request_send_or_pick_up_store==1}}">
            <text>到店自提</text>
          </view>

        </view>

        <view class="weui-cell weui-cell_active">
          <view class="weui-cell__hd"><label class="weui-label">姓名</label></view>
          <view class="weui-cell__bd">
            <input class="weui-input" name="true_name" placeholder="{{type ==1 ? '请填写收货人姓名' :'请填写预约人姓名'}}" value="{{config.last_order_info.true_name}}" placeholder-class="weui-input__placeholder" />
          </view>
        </view>

        <view class="weui-cell weui-cell_active">
          <view class="weui-cell__hd">
            <label class="weui-label">手机号</label>
            <!-- <view class="weui-cell__desc">副标题</view> -->
          </view>
          <view class="weui-cell__bd">
            <input class="weui-input" name="mobile" placeholder="{{type ==1 ? '请填写收货人手机号' :'请填写预约人手机号'}}" type="number" value="{{last_order_info_mobile}}" placeholder-class="weui-input__placeholder" />
          </view>
          <view class="weui-cell__ft">
            <button aria-role="button" open-type="getPhoneNumber" bindgetphonenumber="get_mobile" class="weui-btn weui-btn_default weui-vcode-btn">获取</button>
          </view>
        </view>

        <view class="weui-cell weui-cell_active" wx:if="{{config.is_show_id_card_number == 1}}">
          <view class="weui-cell__hd"><label class="weui-label">身份证号</label></view>
          <view class="weui-cell__bd">
            <input class="weui-input" name="id_card_number" placeholder="请填写身份证号" value="{{config.last_order_info.id_card_number}}" placeholder-class="weui-input__placeholder" />
          </view>
        </view>
        <block wx:if="{{type ==2}}">

          <view class="flex-row address-picker" bindtap='showShop'>
            <view class="flex-grow-1">
              <view class="flex-row" style="margin-bottom: 20rpx">
                <view class="flex-grow-1">自提门店：{{store.store_name}}</view>
              </view>
              <view class="flex-row" style="margin-bottom: 20rpx">
                <view class="flex-grow-1">门店电话：{{store.mobile}}</view>
              </view>
              <view>门店地址：{{store.address_info}}</view>
            </view>
            <view class="flex-grow-0 flex-y-center">
              <image src="/images/icon-jiantou-r.png" style="width: 12rpx;height: 22rpx;margin-left: 12rpx"></image>
            </view>
          </view>

        </block>

        <!--选择自提地址  开始-->
        <view class="coupon-picker" wx:if="{{show_store}}" style='background-color:#f7f7f7;'>
          <scroll-view class="coupon-list" scroll-y="true" style="height: 100%;padding:0;">

            <!-- <view class='shop-block flex-y-center'>当前地址</view>
            <view class='shop-address flex-row flex-y-center'>
              <view class='flex-grow-1'>{{location}}</view>
              <view class='flex-grow-0 flex-y-center' bindtap='dingwei'>
                <image src='/images/icon-shop-dingwei.png' style='width:32rpx;height:32rpx;margin-right:2rpx;'></image>
                <text style='color:#2495ff'>手动定位</text>
              </view>
            </view> -->

            <view class='shop-block flex-y-center'>门店列表</view>
            <view class='flex-grow-0 close-btn' bindtap='hideStorePicker'>
              <image src='/images/icon-close.png' style='width:32rpx;height:32rpx;'></image>
            </view>
            <view class="flex-row address-picker" style='margin:0;' bindtap='pickShop' wx:for='{{store_list}}' data-index="{{index}}">
              <view class="flex-grow-0 flex-y-center">
                <image src="{{item.guid==store.guid?'/images/icon-shop-checked.png':'/images/icon-shop-un.png'}}" style="width: 40rpx;height: 40rpx;margin-right: 20rpx"></image>
              </view>
              <view class="flex-grow-1">
                <view class="flex-row" style="margin-bottom: 20rpx">
                  <view class="flex-grow-1" style='font-weight:bold;{{item.guid==store.guid?"color:#ff4544":""}}'>
                    {{item.store_name}}
                  </view>
                  <view class='flex-grow-0' wx:if='{{item.distance!=-1}}'>{{item.distance_text}}</view>
                </view>
                <view class="flex-row" style="margin-bottom: 20rpx">
                  <view class="flex-grow-1">电话：{{item.mobile}}</view>
                </view>
                <view>地址：{{item.address_info}}</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <!--选择自提地址  结束-->
        <block wx:if="{{config.is_show_address == 1 && type ==1}}">
          <area_picker bind:listener="ComponentListener" province_id="{{config.last_order_info.province_id}}" city_id="{{config.last_order_info.city_id}}" area_id="{{config.last_order_info.area_id}}" class="area_picker"></area_picker>
          <view class="weui-cell weui-cell_active">
            <view class="weui-cell__hd">
              <label class="weui-label">详细地址</label>
              <!-- <view class="weui-cell__desc">副标题</view> -->
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" name="address" value="{{config.last_order_info.address}}" placeholder="填写详细地址" type="text" placeholder-class="weui-input__placeholder" />
            </view>
          </view>
        </block>

        <block wx:if="{{config.is_show_request_send_or_pick_up_time == 1}}">
          <view class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after">
            <view class="weui-cell__hd">
              <label class="weui-label">{{type ==1 ? '发货日期' :'预约日期'}}</label>
              <!-- <view class="weui-cell__desc">副标题</view> -->
            </view>
            <view class="weui-cell__bd">
              <view class="weui-select" bindtap="show_calendar">
                <text wx:if="{{request_send_or_pick_up_time}}">{{request_send_or_pick_up_time}} </text>
                <text wx:if="{{!request_send_or_pick_up_time}}" style="color: #999;">{{type ==1 ? '请选择预约发货日期' :'请选择预约日期'}} </text>
              </view>
            </view>
          </view>
          <calendar class="calendar" wx:if="{{calendar}}" config="{{calendarConfig}}" bind:takeoverTap="takeoverTap" bind:afterTapDate="afterTapDate" bind:afterCalendarRender="afterCalendarRender" bind:onSwipe="onSwipe" bind:whenChangeMonth="whenChangeMonth" />
        </block>





        <block wx:if="{{config.show_extend_field_array.length>0}}">
          <block wx:for="{{config.show_extend_field_array}}" wx:for-item="field" wx:key="index">

            <view class="weui-cell weui-cell_active" wx:if="{{field.type=='input'}}">
              <view class="weui-cell__hd">
                <label class="weui-label">{{field.name}}</label>
                <!-- <view class="weui-cell__desc">副标题</view> -->
              </view>
              <view class="weui-cell__bd">
                <input class="weui-input" name="{{field.key_name}}" value="{{field.default_value}}" placeholder="{{field.placeholder}}" type="text" placeholder-class="weui-input__placeholder" />
              </view>
            </view>

            <view class="weui-cell weui-cell_active weui-cell_select weui-cell_select-after" wx:if="{{field.type=='select'}}">
              <view class="weui-cell__hd">
                <label class="weui-label">{{field.name}}</label>
                <!-- <view class="weui-cell__desc">副标题</view> -->
              </view>
              <view class="weui-cell__bd">
                <picker aria-role="combobox" bindchange="bindPickerChange" data-field_info="{{field}}" data-index="{{index}}" range="{{field.option}}" header-text="{{field.placeholder}}">
                  <view class="weui-select" style="color: {{extend_field_data[field.key_name] ? '#000' : '#999'}};">{{extend_field_data[field.key_name] ? extend_field_data[field.key_name] : field.placeholder}}</view>
                </picker>
              </view>
            </view>
          </block>
        </block>

        <view class="weui-cell weui-cell_active">
          <view class="weui-cell__hd">
            <label class="weui-label">备注</label>
            <!-- <view class="weui-cell__desc">副标题</view> -->
          </view>
          <view class="weui-cell__bd">
            <input class="weui-input" name="remark" value="{{config.last_order_info.remark}}" placeholder="{{config.remark_placeholder}}" type="text" placeholder-class="weui-input__placeholder" />
          </view>
        </view>

        <upload wx:if="{{config.is_show_upload_image == 1}}" id='upload' upload_image_example_tips="{{config.upload_image_example_tips}}" upload_image_example_url="{{config.upload_image_example_url}}" files='{{files}}' maxFileCount='9' isCanAddFile='{{files.length>=maxFileCount?false:true}}'> </upload>

        <block wx:if="{{config.coupon_info.cycle_delivery == 1}}">
          <view class="weui-cell weui-cell_active">
            <view class="weui-cell__hd">
              <label class="weui-label">提货次数</label>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" bindinput='watch_exchange_times' name="exchange_times" value="{{config.available_num}}" placeholder="提货次数" type="number" placeholder-class="weui-input__placeholder" />
            </view>
          </view>

          <view class="weui-cell weui-cell_active">
            <view class="weui-cell__hd">
              <label class="weui-label">配送间隔</label>
            </view>
            <view class="weui-cell__bd">
              <input class="weui-input" bindinput='watch_cycle_delivery_interval_days' name="cycle_delivery_interval_days" value="{{config.coupon_info.cycle_delivery_min_interval_days}}" placeholder="请输入间隔天数" type="number" placeholder-class="weui-input__placeholder" />
            </view>
          </view>
          <text style="padding-left: 30rpx;"><text style="color: #ff7830;">{{request_send_or_pick_up_time? request_send_or_pick_up_time : '预约日期'}}</text> 起每隔 <text style="color: #ff7830;">{{cycle_delivery_interval_days}}</text>天配送一次,共配送 <text style="color: #ff7830">{{exchange_times}}</text>次
          </text>
        </block>
      </view>
    </view>
  </view>

  <view class="weui-form__tips-area">
    <view class="weui-form__tips">

    </view>
  </view>
  <view wx:if="{{submit_preview.total_price>0}}" style="font-size: 36rpx;text-align: center;margin-top: -20rpx;padding-bottom: 20rpx;">

    <text>{{submit_preview.cost_desc}}: </text>
    <text style="color: #ff7830;">{{submit_preview.total_price}}</text> 元

  </view>

  <view>
    <button aria-role="button" class="weui-btn weui-btn_primary" formType="submit">立即提交</button>
  </view>

</form>
<!-- <view class="weui-form__tips-area">
  <view class="weui-form__tips">
    表单页提示，居中对齐
  </view>
</view> -->


<view class="layui-fixbar" wx:if="{{config.weapp_service_type==1 && config.service_phone}}">
  <button class="wechat_service_button" size="mini">
    <image bindtap="phoneClick" data-phoneNumber="{{config.service_phone}}" class="wechat_service_img" src="/images/wechat_service.png"></image>
  </button>
</view>


<view class="layui-fixbar" wx:if="{{config.weapp_service_type==2}}">
  <button class="wechat_service_button" open-type="contact" bindcontact="handleContact" session-from="code_submit_order" size="mini">
    <image class="wechat_service_img" src="/images/wechat_service.png"></image>
  </button>
</view>

<view class="layui-fixbar" bindtap='openCustomerServiceChat' wx:if="{{config.weapp_service_type==3 && config.wechat_service_url && config.wechat_service_corp_id}}">
  <image class='wechat_service_img' src="/images/wechat_service.png"></image>
</view>

<view style="padding-top: 20rpx;">
  <copy_right></copy_right>
</view>