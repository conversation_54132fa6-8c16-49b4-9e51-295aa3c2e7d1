// pages/code/choose_code.js
var app = getApp(); //获取应用实例
Page({

  /**
   * 页面的初始数据
   */
  data: {
    available_use_num: 0,
    list: [],
    dialog: false,
    show_tab_bar: 0,
    phone: '',
    verify_code: '',
    get_verify_code_text: '获取验证码', //倒计时 
    currentTime: 60, //限制60s
    is_allow_get_verify_code: true, //获取验证码按钮，默认允许点击
    verify_code_focus: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  loadData: function () {
    let that = this;
    app.request({
      url: app.api.code.member_code_list,
      data: {
        status: 0
      },
      success: function (result) {
        console.log(result);
        if (result.data.available_use_num == 1) {
          that.goto_code_detail(result.data.list[0].coupon_guid, result.data.list[0].guid);
          return true;
        }
        that.setData({
          available_use_num: result.data.available_use_num,
          list: result.data.list
        })
      },
    });
  },
  onLoad(options) {
    let that = this;
    app.pageOnLoad(that, options);
    that.loadData();
  },
  close: function (e) {
    this.setData({
      dialog: false,
    })
  },
  get_verify_code: function () {
    if (this.data.is_allow_get_verify_code == false) {
      console.log('当前不允许获取');
      return false;
    }
    var that = this;
    if (!that.data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    //调用接口
    app.request({
      url: app.api.user.send_sms_code,
      data: {
        mobile: that.data.phone,
      },
      success: function (result) {
        console.log(result);
        that.setData({
          is_allow_get_verify_code: false,
          verify_code_focus: true
        })
        wx.showToast({
          title: result.msg,
        })
        that.doLoop();
      },
      fail: function (result) {
        console.log('fail');
        console.log(result);
        that.setData({
          is_allow_get_verify_code: true
        })
      },
    });
  },
  doLoop: function () {
    let that = this;
    // 60s倒计时 setInterval功能用于循环，常常用于播放动画，或者时间显示
    var currentTime = that.data.currentTime;
    let interval = setInterval(function () {
      currentTime--; //减
      that.setData({
        get_verify_code_text: currentTime + '秒后获取'
      })
      if (currentTime <= 0) {
        clearInterval(interval)
        that.setData({
          get_verify_code_text: '获取验证码',
          currentTime: 60,
          is_allow_get_verify_code: true
        })
      }
    }, 1000);
  },
  phoneChange: function (event) {
    console.log("phoneChange==", event.detail.value)
    this.setData({
      phone: event.detail.value
    })
  },
  show_dialog: function () {
    var that = this;
    that.setData({
      dialog: true
    })
  },
  bind_code: function (e) {
    var that = this;
    that.setData({
      dialog: true
    })
    let data = e.detail.value;
    console.log(data);

    if (!data.phone) {
      wx.showModal({
        title: '系统提示',
        content: '请输入手机号!',
        showCancel: false,
      });
      return false;
    }
    if (!data.verify_code) {
      wx.showModal({
        title: '系统提示',
        content: '请输入验证码!',
        showCancel: false,
      });
      return false;
    }
    app.request({
      url: app.api.code.bind_code_by_mobile,
      data: data,
      success: function (result) {
        console.log(result);
        wx.showModal({
          title: "提示",
          content: result.msg,
          showCancel: false,
          confirmText: "确认",
          success: function (e) {
            // wx.navigateTo({
            //   url: '/pages/user/user',
            // })
            that.setData({
              dialog: false,
              phone: '',
              verify_code: '',
            })
            that.loadData();
          }
        })
      },
    });
  },
  goto_detail: function (event) {
    let guid = event.currentTarget.dataset.guid;
    this.goto_code_detail(guid);
  },
  goto_code_detail: function (coupon_send_note_guid) {
    app.request({
      url: app.api.code.verify_coupon_send_note_guid,
      data: {
        coupon_send_note_guid: coupon_send_note_guid
      },
      success: function (result) {
        console.log(result);
        if (result.data.path) {
          wx.navigateTo({
            url: result.data.path
          })
          return;
        }
        if (result.data.status == 1) {
          //选择商品
          wx.navigateTo({
            url: '/pages/code/detail?bid=' + app.ext_config.bid + '&token=' + result.data.data.token,
          })
        } else if (result.data.status == 2) {
          //订单详情
          wx.navigateTo({
            url: '/pages/order/detail?bid=' + app.ext_config.bid + '&order_guid=' + result.data.data.order_guid
          })
        }
      },
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})