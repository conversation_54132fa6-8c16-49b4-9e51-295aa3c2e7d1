<!--pages/gift/status.wxml-->
<view class="container" style="padding: 0;">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{show_error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <view class="error-message">{{error_message}}</view>
    <button class="go-home-btn" bindtap="goHome">去首页逛逛</button>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content" style="padding: 10rpx 30rpx;">
    <!-- 卡券信息卡片 -->
    <view class="coupon-card">
      <!-- 状态标签 -->
      <view wx:if="{{gift_records && gift_records.status !== undefined}}"
            class="coupon-status-stamp status-{{gift_records.status}}">
        {{gift_records.status == -3 ? '超时自动退款' :
          gift_records.status == -2 ? '已取消' :
          gift_records.status == -1 ? '待支付' :
          gift_records.status == 0 ? '待领取' :
          gift_records.status == 1 ? '已领取' : '已过期'}}
      </view>

      <view class="coupon-header">
        <view class="coupon-name">{{coupon_info.name}}</view>
        <view class="coupon-type">送礼券</view>
      </view>

      <view class="coupon-body">
        <view class="coupon-code">
          <text class="label">卡号：</text>
          <text class="code-text">{{code}}</text>
          <!-- <text class="copy-btn" bindtap="copyCode">复制</text> -->
        </view>
      </view>
    </view>

    <!-- 收礼界面 -->
    <view wx:if="{{gift_records && gift_records.status == 0}}" class="recipient-section">
      <view class="thank-message">
        <view class="thank-title">🎁 您收到一份礼品</view>
        <view class="gift-message-display" wx:if="{{gift_records.gift_message}}">
          {{gift_records.gift_message}}
        </view>
      </view>

      <!-- 收礼口令输入 -->
      <view wx:if="{{gift_records.need_gift_password}}" class="gift-password-section">
        <view class="password-label">🔐 请输入收礼口令</view>
        <view class="password-input-wrapper">
          <input
            class="password-input"
            placeholder="请输入收礼口令"
            value="{{input_gift_password}}"
            bindinput="onInputGiftPasswordChange"
            maxlength="20"
            type="text"
          />
        </view>
        <view class="password-tip">请向送礼人索要收礼口令</view>
      </view>

      <!-- 礼品详情 -->
      <view wx:if="{{order_detail && order_detail.goods_list && order_detail.goods_list.length > 0}}"
            class="gift-details">
        <view class="gift-details-title">礼品详情</view>
        <view wx:for="{{order_detail.goods_list}}" wx:key="guid" class="flex-row gift-item">
          <view class="flex-grow-0">
            <image mode="aspectFill" class="gift-image" src="{{item.pic}}"></image>
          </view>
          <view class="flex-grow-1 gift-info">
            <view class="gift-name">{{item.name}}</view>
            <view class="gift-specs" wx:if="{{item.specs}}">
              规格：{{item.specs}}
            </view>
            <view class="gift-quantity">数量：×{{item.amount || item.num || 1}}</view>
          </view>
        </view>
      </view>
      <!-- 收货地址选择 -->
      <navigator url="/pages/address-picker/address-picker" class="address-picker-section">
        <view class="address-picker-content">
          <block wx:if="{{selected_address}}">
            <view class="address-info">
              <view class="address-header">
                <text class="receiver-name">{{selected_address.true_name}}</text>
                <text class="receiver-phone">{{selected_address.mobile}}</text>
              </view>
              <view class="address-detail">
                {{selected_address.province_name}}{{selected_address.city_name}}{{selected_address.area_name}}{{selected_address.address}}
              </view>
            </view>
          </block>
          <block wx:else>
            <view class="address-placeholder">
              <image class="address-icon" src="/images/icon-user-dz.png"/>
              <text>请选择收货地址</text>
            </view>
          </block>
          <view class="arrow-right">></view>
        </view>
      </navigator>

      <button class="claim-gift-btn" bindtap="confirmClaimGift" disabled="{{!canClaimGift}}">
        <text class="btn-text" wx:if="{{canClaimGift}}">确认收礼</text>
        <text class="btn-text" wx:elif="{{!selected_address}}">请先选择收货地址</text>
        <text class="btn-text" wx:elif="{{gift_records.need_gift_password && !input_gift_password}}">请输入收礼口令</text>
        <text class="btn-text" wx:else>请完善信息</text>
      </button>
      <view class="tip-text">
        {{gift_records.need_gift_password ? '请输入收礼口令并选择收货地址' : '选择收货地址后即可领取礼品'}}
      </view>
    </view>

    <!-- 选中商品预览 -->
    <view wx:if="{{(!gift_records || !gift_records.guid) && selected_goods.length > 0}}" class="selected-goods-section">
      <view class="selected-goods-header" bindtap="toggleSelectedGoods">
        <view class="selected-goods-title">
          <text class="title-text">已选商品</text>
          <text class="count-text">({{total_selected_count}}件)</text>
        </view>
        <view class="selected-goods-price">¥{{total_selected_price}}</view>
        <view class="toggle-icon {{show_selected_goods ? 'expanded' : ''}}">▼</view>
      </view>

      <view wx:if="{{show_selected_goods}}" class="selected-goods-list">
        <view wx:for="{{selected_goods}}" wx:key="guid" class="selected-goods-item">
          <image class="goods-image" src="{{item.pic}}" mode="aspectFill"/>
          <view class="goods-info">
            <view class="goods-name">{{item.name}}</view>
            <!-- SKU规格信息显示（优先显示attr数组，兼容旧的specs字段） -->
            <view wx:if="{{item.attr && item.attr.length > 0}}" class="goods-specs-container">
              <view wx:for="{{item.attr}}" wx:key="attr_id" wx:for-item="attr" class="goods-specs-item">
                {{attr.attr_name}}
              </view>
            </view>
            <view wx:elif="{{item.specs}}" class="goods-sku">{{item.specs}}</view>
            <view class="goods-price">¥{{item.price}} × {{item.num}}</view>
          </view>
        </view>

        <view class="selected-goods-actions">
          <!--          <button class="clear-btn" bindtap="clearSelectedGoods">清空</button>-->
          <button class="reselect-btn" bindtap="goSendGift">去修改</button>
        </view>
      </view>
    </view>

    <!-- 送礼配置区域 - 平级显示 -->
    <view wx:if="{{(!gift_records || !gift_records.guid) && selected_goods.length > 0}}" class="gift-config-section">
      <!-- 感谢语 -->
      <view class="config-item">
        <view class="config-label">感谢语</view>
        <view class="config-input-wrapper">
          <textarea
            class="config-textarea"
            placeholder="写下你的感谢话语（可选）"
            value="{{gift_message}}"
            bindinput="onGiftMessageInput"
            maxlength="200"
            show-confirm-bar="{{false}}"
          ></textarea>
          <view class="char-count">{{gift_message.length || 0}}/200</view>
        </view>
      </view>

      <!-- 收礼口令 -->
      <view class="config-item">
        <view class="config-label-with-tip">
          <view class="config-label">收礼口令</view>
          <view class="config-tip">设置后收礼人需要输入口令才能领取</view>
        </view>
        <view class="config-input-wrapper">
          <input
            class="config-input"
            placeholder="设置收礼口令（可选）"
            value="{{gift_password}}"
            bindinput="onGiftPasswordInput"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 收礼有效期 -->
      <view class="config-item">
        <view class="config-label-with-tip">
          <view class="config-label">收礼有效期</view>
          <view class="config-tip">超时未领取将自动退款</view>
        </view>
        <view class="expire-options">
          <view
            wx:for="{{expire_time_options}}"
            wx:key="value"
            class="expire-option {{expire_time_index === index ? 'active' : ''}}"
            data-index="{{index}}"
            bindtap="onExpireTimeSelect"
          >
            {{item.label}}
          </view>
        </view>
      </view>
    </view>

    <!-- 确认送礼按钮 - 平级显示 -->
    <view wx:if="{{(!gift_records || !gift_records.guid) && selected_goods.length > 0}}" class="send-action-section">
      <button class="confirm-send-btn" bindtap="confirmSendGift">
        <text class="btn-text">确认送礼</text>
      </button>
    </view>

    <!-- 送礼按钮 -->
    <view wx:if="{{(!gift_records || !gift_records.guid) && selected_goods.length === 0}}" class="action-section">
      <button class="send-gift-btn" bindtap="goSendGift">
        <text class="btn-text">去送礼</text>
      </button>
      <view class="tip-text">选择商品后即可送给好友</view>
    </view>

    <!-- 超时自动退款状态 -->
    <view wx:if="{{gift_records && gift_records.status == -3}}" class="status-section">
      <view class="status-tip refunded">
        <text class="tip-icon">↩</text>
        <text class="tip-text">超时自动退款</text>
      </view>
      <view class="tip-text">订单超时未支付，已自动退款</view>
    </view>

    <!-- 已取消状态 -->
    <view wx:if="{{gift_records && gift_records.status == -2}}" class="status-section">
      <view class="status-tip cancelled">
        <text class="tip-icon">✗</text>
        <text class="tip-text">订单已取消</text>
      </view>
      <view class="tip-text">该订单已被取消</view>
    </view>

    <!-- 待支付状态 -->
    <view wx:if="{{gift_records && gift_records.status == -1}}" class="status-section">
      <view class="status-tip pending">
        <text class="tip-icon">💰</text>
        <text class="tip-text">待支付</text>
      </view>
      <view class="tip-text">请完成支付以继续</view>
    </view>

    <!-- 已领取状态提示 -->
    <view wx:if="{{gift_records && gift_records.status == 1}}" class="status-section">
      <view class="status-tip success">
        <text class="tip-icon">✓</text>
        <text class="tip-text">礼品已领取</text>
      </view>
      <view class="tip-text">礼品将尽快为您配送</view>
    </view>

    <!-- 已过期状态提示 -->
    <view wx:if="{{gift_records && gift_records.status == 2}}" class="status-section">
      <view class="status-tip expired">
        <text class="tip-icon">✗</text>
        <text class="tip-text">礼品已过期</text>
      </view>
      <view class="tip-text">该礼品已过期，无法领取</view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{!gift_records && !order_detail}}" class="empty-state">
      <view class="empty-text">暂无相关信息</view>
    </view>
  </view>
</view>