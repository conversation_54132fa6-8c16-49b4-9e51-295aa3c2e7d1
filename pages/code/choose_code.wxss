/* @import '../../common/common.wxss'; */
page {
  background-color: #EFEFF4;
  font-family: "Helvetica Neue", "Hiragino Sans GB", "Microsoft YaHei", "\9ED1\4F53", Arial, sans-serif;
  font-size: 32rpx;
}



.images {
  width: 60rpx;
  height: 60rpx;
}

.row-data {
  height: 60rpx;
  line-height: 60rpx;
  margin: 0 4rpx;
  width: 88rpx;
  text-align: center;
  background: #f7f7f7;
  font-size: 11pt;
  color: #353535;
}

.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;

  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row;
  flex-direction: row;
}

.weui-cell {
  padding: 20rpx;
}



.goods-list {
  overflow-x: hidden;
}

.goods-list {
  margin-left: -5rpx;
  margin-right: -5rpx;
  flex-wrap: wrap;
}

.goods-list .flex-grow-0 {
  width: 33.333333%;
  padding-left: 5rpx;
  padding-right: 5rpx;
}

.goods-list {
  padding-left: 5rpx;
  padding-right: 5rpx;
  flex-wrap: wrap;
  padding-top: 10rpx;
}

.goods-list .goods-item {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  float: left;
}

.goods-list .goods-item image {
  width: 100%;
  height: 242rpx;
  /* height: calc(width); */
  display: block;
}

.goods-list .goods-item .goods-name {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  display: block;
  padding: 10rpx 5rpx 5rpx 5rpx;
}

.goods-list .goods-item .goods-price {
  color: #ff334b;
  text-align: center;
  display: block;
  padding: 5rpx 5rpx 10rpx 5rpx;
}

.goods-list.goods-list-cols-2 .flex-grow-0 {
  width: 50%;
}

.goods-list.goods-list-cols-2 .flex-grow-0 .goods-item image {
  height: 375rpx;
}

.goods-list.goods-list-cols-3 .flex-grow-0 {
  width: 33.333333%;
}

.coupon-list {
  padding: 122rpx 34rpx 20rpx 34rpx;
}

.vcode-btn {
  font-size: 16px;
  padding: 0 12px;
  height: auto;
  width: auto;
  line-height: 2;
  border-radius: 6px;
  color: var(--weui-BTN-DEFAULT-COLOR);
  background-color: var(--weui-BTN-DEFAULT-BG);
}