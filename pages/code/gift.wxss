/* pages/gift/status.wxss */

/* 页面背景色 */
page {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffc3a0 100%);
  background-attachment: fixed;
  background-size: 100% 100%;
  min-height: 100vh;
}


/* 登录页面样式 */
.login-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ffc3a0 100%);
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gift-float {
  position: absolute;
  font-size: 40rpx;
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
}

.gift-float-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.gift-float-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.gift-float-3 {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.gift-float-4 {
  top: 70%;
  right: 10%;
  animation-delay: 3s;
}

.gift-float-5 {
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}

.gift-float-6 {
  top: 85%;
  left: 50%;
  animation-delay: 5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20rpx) rotate(10deg);
  }
}

/* 主要内容 */
.login-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 40rpx;
}

.login-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.gift-icon-large {
  font-size: 100rpx;
  margin-bottom: 20rpx;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

.login-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.login-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.login-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

.login-form {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx 30rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 600;
}

.label-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.input-wrapper {
  position: relative;
}

.login-input {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  border: 3rpx solid #f0f0f0;
  border-radius: 16rpx;
  font-size: 32rpx;
  background: #fff;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.login-input:focus {
  border-color: #ff6b9d;
  outline: none;
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 157, 0.2);
  transform: translateY(-2rpx);
}

.login-input::placeholder {
  color: #ccc;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 34rpx;
  font-weight: 600;
  margin-top: 40rpx;
  box-shadow: 0 12rpx 30rpx rgba(255, 107, 157, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:active::before {
  left: 100%;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.btn-text {
  line-height: 1;
  vertical-align: middle;
}

.login-btn:disabled {
  background: #ccc !important;
  box-shadow: none !important;
  color: #999 !important;
}

.login-btn:active:not(:disabled) {
  transform: translateY(4rpx);
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 157, 0.5);
}

.login-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  padding: 20rpx;
  background: rgba(255, 235, 59, 0.1);
  border-radius: 12rpx;
  border: 2rpx solid rgba(255, 235, 59, 0.3);
}

.tip-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.back-login-btn {
  background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 60rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 157, 0.4);
  margin-bottom: 20rpx;
}

.back-login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 157, 0.5);
}

/* Flex 布局样式 */
.flex-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row;
  flex-direction: row;
}

.flex-grow-0 {
  min-width: 0;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.flex-grow-1 {
  min-width: 0;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
}

.container {
  min-height: 100vh;
  background: transparent;
  padding: 0;
  margin: 0;
  align-items: unset !important;
  position: relative;
}

/* 为非登录页面添加内容区域样式 */
.content {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-message {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 50rpx;
  max-width: 500rpx;
}

.go-home-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 60rpx;
  font-size: 30rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.go-home-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

/* 卡券信息卡片 */
.coupon-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
  position: relative;
}

.coupon-status-stamp {
  position: absolute;
  top: 26px;
  right: 0px;
  padding: 6px 18px;
  border-radius: 18px;
  font-size: 22px;
  font-weight: 900;
  color: #fff;
  background: rgba(255, 152, 0, 0.28);
  opacity: 0.4;
  z-index: 2;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18), 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: rotate(31deg);
  letter-spacing: 2px;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18), 0 0 2px #fff;
}

/* 状态标签样式 */
.coupon-status-stamp.status--3 {
  background: rgba(158, 158, 158, 0.78); /* 超时自动退款 - 灰色 */
}

.coupon-status-stamp.status--2 {
  background: rgba(244, 67, 54, 0.78); /* 已取消 - 红色 */
}

.coupon-status-stamp.status--1 {
  background: rgba(255, 193, 7, 0.78); /* 待支付 - 黄色 */
}

.coupon-status-stamp.status-0 {
  background: rgba(255, 152, 0, 0.78); /* 待领取 - 橙色 */
}

.coupon-status-stamp.status-1 {
  background: rgba(76, 175, 80, 0.78); /* 已领取 - 绿色 */
}

.coupon-status-stamp.status-2 {
  background: rgba(189, 189, 189, 0.78); /* 已过期 - 灰色 */
}

.coupon-header {
  margin-bottom: 30rpx;
}

.coupon-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.coupon-type {
  font-size: 24rpx;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.coupon-body {
  line-height: 1.6;
}

.coupon-code,
.coupon-value,
.coupon-status,
.coupon-available {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.label {
  font-size: 28rpx;
  opacity: 0.9;
  width: 140rpx;
}

.code-text {
  font-size: 32rpx;
  font-weight: bold;
  flex: 1;
  font-family: "Courier New", monospace;
}

.copy-btn {
  font-size: 24rpx;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

.value-text,
.available-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffe066;
}

.status-text {
  font-size: 28rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  background: rgba(255, 255, 255, 0.2);
}

.status-0 {
  background: rgba(76, 175, 80, 0.3);
}

.status-1 {
  background: rgba(158, 158, 158, 0.3);
}

/* 操作区域 */
.action-section {
  text-align: center;
  margin-bottom: 40rpx;
}

.send-gift-btn, .confirm-send-btn {
  width: 80%;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 0;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-gift-btn:active, .confirm-send-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.4);
}

.send-gift-btn .btn-text, .confirm-send-btn .btn-text {
  line-height: 1;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 复制按钮 */
.copy-btn {
  margin-left: 10rpx;
  padding: 4rpx 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 状态提示区域 */
.status-section {
  margin: 40rpx 0;
  text-align: center;
}

.status-tip {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

/* 状态提示样式 */
.status-tip.refunded {
  background: linear-gradient(135deg, #9e9e9e, #757575);
  color: #fff;
}

.status-tip.cancelled {
  background: linear-gradient(135deg, #f44336, #e53935);
  color: #fff;
}

.status-tip.pending {
  background: linear-gradient(135deg, #ffc107, #ffb300);
  color: #fff;
}

.status-tip.success {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  color: #fff;
}

.status-tip.expired {
  background: linear-gradient(135deg, #bdbdbd, #9e9e9e);
  color: #fff;
}

.tip-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

/* 礼品详情区域 */
.gift-details {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.gift-details-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.gift-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.gift-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.gift-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.gift-info {
  padding-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.gift-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.gift-specs {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}

.gift-quantity {
  font-size: 24rpx;
  color: #666;
}

/* 礼品和记录区域 */
.pending-gifts-section,
.gift-records-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.gift-item,
.record-item {
  display: flex;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.gift-item:last-child,
.record-item:last-child {
  border-bottom: none;
}

.gift-info,
.record-info {
  flex: 1;
}

.gift-message,
.record-message {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.gift-time,
.gift-expire,
.record-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.claim-btn {
  background: #007aff;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
}

.record-status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  display: inline-block;
  margin-top: 10rpx;
}

.record-status.status-0 {
  background: #e8f5e8;
  color: #4caf50;
}

.record-status.status-1 {
  background: #f0f0f0;
  color: #999;
}

.record-status.status-2 {
  background: #ffebee;
  color: #f44336;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.gift-detail {
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.address-input {
  margin-top: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.address-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}

.address-textarea:focus {
  border-color: #007aff;
}

.modal-footer {
  display: flex;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 20rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 25rpx 0;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #007aff;
  color: white;
}

/* 收礼界面样式 */
.recipient-section {
  margin-top: 40rpx;
  padding: 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.thank-message {
  text-align: center;
  margin-bottom: 40rpx;
}

.thank-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.gift-message-display {
  background: #f8f9ff;
  border: 2rpx solid #e8ecff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gift-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 16rpx;
}

.gift-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #fff5f5;
  border-radius: 12rpx;
}

.status-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: 500;
}

.status-value.pending {
  color: #ff6b35;
}

/* 地址选择器样式 */
.address-picker-section {
  margin-bottom: 40rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  overflow: hidden;
}

.address-picker-content {
  display: flex;
  align-items: center;
  padding: 30rpx;
  min-height: 120rpx;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.receiver-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 24rpx;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
}

.address-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.address-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.arrow-right {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 20rpx;
}

.claim-gift-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 0;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.claim-gift-btn .btn-text {
  line-height: 1;
}

.claim-gift-btn:disabled {
  background: #ccc !important;
  box-shadow: none !important;
  color: #999 !important;
}

.claim-gift-btn:active:not(:disabled) {
  background: linear-gradient(135deg, #e55a2b 0%, #de7f1a 100%);
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.4);
}

/* 选中商品预览区域 */
.selected-goods-section {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.selected-goods-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #eee;
}

.selected-goods-title {
  flex: 1;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.count-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 10rpx;
}

.selected-goods-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b9d;
  margin-right: 20rpx;
}

.toggle-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.selected-goods-list {
  padding: 20rpx 30rpx;
}

.selected-goods-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.selected-goods-item:last-child {
  border-bottom: none;
}

.goods-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.goods-sku {
  font-size: 22rpx;
  color: #888;
  background: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  display: inline-block;
  margin-bottom: 8rpx;
}

/* SKU规格容器样式 */
.goods-specs-container {
  margin-bottom: 8rpx;
}

.goods-specs-item {
  font-size: 22rpx;
  color: #666;
  background: #EDF1FC;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  display: inline-block;
  margin-right: 10rpx;
  margin-bottom: 5rpx;
}

.goods-price {
  font-size: 26rpx;
  color: #ff6b9d;
  font-weight: bold;
}

.selected-goods-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

.clear-btn, .reselect-btn {
  flex: 1;
  padding: 20rpx 0;
  border-radius: 30rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.clear-btn {
  background: #f5f5f5;
  color: #666;
}

.reselect-btn {
  color: #ff6b9d;
  border: 2rpx solid #ff6b9d;
}

/* 送礼配置区域 - 平级布局 */
.gift-config-section {
  margin: 20rpx 0;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 确认送礼按钮区域 - 平级布局 */
.send-action-section {
  margin: 20rpx 40rpx 40rpx;
}

.confirm-send-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 0;
  height: 96rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.config-item {
  margin-bottom: 40rpx;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.config-label-with-tip {
  display: flex;
  align-items: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.config-label-with-tip .config-label {
  margin-bottom: 0;
}

.config-tip {
  font-size: 22rpx;
  color: #999;
  font-weight: normal;
}

.config-input-wrapper {
  position: relative;
}

.config-input {
  width: 100%;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 80rpx;
  text-align: left;
}

.config-input:focus {
  border-color: #ff6b9d;
  background: white;
}

.config-textarea {
  width: 100%;
  height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
  resize: none;
  line-height: 1.5;
}

.config-textarea:focus {
  border-color: #ff6b9d;
  background: white;
}

.char-count {
  position: absolute;
  bottom: 10rpx;
  right: 15rpx;
  font-size: 22rpx;
  color: #999;
}

.input-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}


/* 有效期平铺选择 */
.expire-options {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.expire-option {
  flex: 1;
  min-width: 120rpx;
  padding: 20rpx 15rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.expire-option:active {
  transform: scale(0.98);
}

.expire-option.active {
  border-color: #ff6b9d;
  background: #ff6b9d;
  color: white;
  font-weight: bold;
}

/* 收礼口令输入区域 */
.gift-password-section {
  margin: 30rpx 0;
  padding: 30rpx;
  background: #fff5f5;
  border-radius: 16rpx;
  border: 2rpx solid #ffe0e6;
}

.password-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b9d;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.password-input-wrapper {
  position: relative;
  margin-bottom: 15rpx;
}

.password-input {
  width: 100%;
  padding: 0 20rpx;
  border: 2rpx solid #ff6b9d;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
  box-sizing: border-box;
  height: 80rpx;
  line-height: 80rpx;
  text-align: left;
}

.password-input:focus {
  border-color: #ff4757;
  box-shadow: 0 0 0 4rpx rgba(255, 107, 157, 0.1);
}

.password-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
