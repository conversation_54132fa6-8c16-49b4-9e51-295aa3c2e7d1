// pages/index/index.js
var app = getApp(); //获取应用实例
Page({

  /**
   * 页面的初始数据
   */
  data: {
    config: {},
    code: '',
  },
  bindchange: function (e) {
    this.setData({
      current: e.detail.current
    })
  },
  scan: function (e) {
    console.log('index - scan')
    var that = this;
    wx.scanCode({
      success(res) {
        app.tools.parse_qrcode(that, res.result);
        that.setData({
          // code: res.result,
          password_focus: true,
        });
      },
      fail(res) {
        console.log(res);
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('index - onLoad')
    console.log(options)
    app.pageOnLoad(this, options);
    if (options.code !== undefined) {
      this.setData({
        code: options.code
      });
      this.auto_check(options)
    }
    if (options.q !== undefined) {
      app.tools.parse_qrcode(this, decodeURIComponent(options.q));
      this.auto_check(decodeURIComponent(options.q))
    }
    var that = this;
    app.editTabBar();
    app.request({
      url: app.api.code.config,
      data: options,
      success: function (res) {
        console.log(res);
        that.setData({
          config: res.data
        });
      },
    });
  },
  auto_check: function (data) {
    app.request({
      url: app.api.code.get_code_info,
      data: data,
      success: function (res) {
        if (res.data.code && res.data.status == 0) {
          wx.redirectTo({
            url: '/pages/code/index?code=' + res.data.code,
          })
        }
      },
    });
  },
  active: function (e) {
    var that = this;
    let data = e.detail.value;
    console.log(data);
    if (!data.code) {
      wx.showModal({
        title: '系统提示',
        content: '请输入卡号!',
        showCancel: false,
      });
      return false;
    }
    app.request({
      url: app.api.code.active,
      data: data,
      success: function (result) {
        console.log(result);
        wx.redirectTo({
          url: '/pages/pay/submit?bid=' + app.ext_config.bid + '&order_guid=' + result.data.order_guid + '&bill_number=' + result.data.bill_number + '&type=code_active',
        })
      },
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.log('index - onReady')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('index - onShow')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.log('index - onHide')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.log('index - onUnload')
  },


  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    console.log('index - onPullDownRefresh');
    return app.pageOnPullDownRefresh(this)
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    console.log('index - onReachBottom')
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (options) {
    return app.pageOnShareAppMessage(this, options)
  },
  /**
   * 页面滚动触发事件的处理函数
   */
  // onPageScroll: function(res) {
  //   //console.log(res)
  //   console.log('index - onPageScroll')
  // },
  /**
   * 页面尺寸改变时触发
   */
  onResize: function (res) {
    console.log(res)
    console.log('index - onResize')
  },
  /**
   * 当前是 tab 页时，点击 tab 时触发
   */
  onTabItemTap: function (res) {
    console.log(res)
    console.log('index - onTabItemTap')
  },
})