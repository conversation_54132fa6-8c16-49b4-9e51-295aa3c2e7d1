/* 全局重置 - 确保全屏贴合 */
page {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background-color: #f5f5f5;
}

/* 重置主要容器的默认边距 */
view, text, image, button {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 整体容器样式 - 全屏贴合 */
.detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  padding-bottom: 120rpx; /* 为底部购物车留出空间 */
}

/* 区块通用样式 */
.section {
  margin-bottom: 0;
}

/* 需要边距的内容区块 */
.section-card,
.goods-grid-section,
.goods-list-section {
  margin:0x;
}

/* 提示区域样式 */
.section-hint {
  margin: 0;
}

.access-mode-hint {
  padding: 16rpx 24rpx;
  border-radius: 8rpx;
  margin: 16rpx 16rpx 0 16rpx;
}

.access-mode-hint.success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.access-mode-hint.info {
  background-color: #e3f2fd;
  color: #1565c0;
}

.access-mode-hint.warning {
  background-color: #fff3e0;
  color: #ef6c00;
}

.hint-content {
  display: flex;
  align-items: center;
}

.hint-icon {
  margin-right: 12rpx;
  font-weight: bold;
}

/* 绿色主题公告栏样式 - 固定在顶部，完全贴合屏幕 */
.section-notice {
  position: sticky;
  top: 0;
  z-index: 100;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  /* 确保在小程序中正确固定 */
  -webkit-position: sticky;
  -webkit-top: 0;
  /* 强制贴合顶部 */
  margin-top: 0 !important;
}

/* 备选方案：如果sticky不工作，可以使用fixed定位 */
.section-notice.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

/* 强制贴合顶部的备选方案 */
.section-notice.absolute-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
}

.notice-bar {
  background: #ff9500;
  border-radius: 0;
  padding: 16rpx 20rpx;
  margin: 0;
  color: #ffffff;
  display: flex;
  align-items: center;
  min-height: 80rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.2);
  transition: box-shadow 0.3s ease;
}

.notice-a {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.notice-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.notice-box {
  flex: 1;
  overflow: hidden;
  margin: 0 16rpx;
}

.notice-content {
  width: 100%;
  overflow: hidden;
}

.notice-text {
  display: inline-block;
  white-space: nowrap;
  animation: scroll-left 15s linear infinite;
  padding-left: 100%;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.notice-b {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.notice-arrow {
  width: 12rpx;
  height: 20rpx;
}

/* 轮播图区域 - 全屏贴合 */
.section-banner {
  margin: 0;
  padding: 0;
}

/* 卡片区域样式 */
.section-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 卡券信息样式 - 扁平科技风格 */
.card-info-block {
  margin: 20rpx;
  background: linear-gradient(135deg, #00ae66 0%, #00c975 50%, #00d982 100%);
  border-radius: 24rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.card-info-block:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.card-info-cell {
  padding: 32rpx 28rpx;
  position: relative;
  z-index: 2;
  min-height: 160rpx;
}

/* 科技感背景圆圈 */
.card-info-block::before {
  content: '';
  position: absolute;
  top: -60rpx;
  right: -60rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.card-info-block::after {
  content: '';
  position: absolute;
  bottom: -40rpx;
  left: -40rpx;
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  z-index: 0;
}

/* 卡片装饰条纹 */
.card-stripe {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

/* 卡片主要内容 */
.card-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  height: 100%;
}

/* 卡片标题区域 */
.card-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  letter-spacing: 1rpx;
}

.card-chip {
  width: 52rpx;
  height: 28rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 14rpx;
  position: relative;
  overflow: hidden;
}

.card-chip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16rpx;
  height: 16rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 卡片数值区域 */
.card-value-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-value-line {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
  white-space: nowrap;
}

.card-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.card-unit {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

.highlight-value {
  color: #ffffff;
  font-weight: 700;
  font-size: 52rpx;
  margin: 0 8rpx;
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 卡片有效期区域 */
.card-expire-section {
  display: flex;
  justify-content: flex-end;
}

.card-expire {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 科技感装饰圆点 */
.card-dots {
  position: absolute;
  bottom: 20rpx;
  left: 28rpx;
  display: flex;
  gap: 12rpx;
  z-index: 1;
}

.card-dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  position: relative;
}

.card-dot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6rpx;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.card-dot:nth-child(2) {
  background: rgba(255, 255, 255, 0.6);
}

.card-dot:nth-child(3) {
  background: rgba(255, 255, 255, 0.3);
}

/* 卡券信息响应式样式 */
@media (max-width: 750rpx) {
  .card-info-block {
    margin: 16rpx;
  }

  .card-info-cell {
    padding: 28rpx 24rpx;
    min-height: 140rpx;
  }

  .card-content {
    gap: 20rpx;
  }

  .card-title {
    font-size: 22rpx;
  }

  .card-chip {
    width: 40rpx;
    height: 28rpx;
  }

  .card-label,
  .card-unit {
    font-size: 26rpx;
  }

  .highlight-value {
    font-size: 48rpx;
  }

  .card-expire {
    font-size: 18rpx;
    padding: 4rpx 10rpx;
  }

  .card-dots {
    bottom: 12rpx;
    left: 24rpx;
  }

  .card-dot {
    width: 6rpx;
    height: 6rpx;
  }
}

/* 卡券简介样式 */
.card-intro-block {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-intro-cell {
  padding: 20rpx;
}

.intro-richtext {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 选择提示样式 */
.selection-hint-block {
  padding: 10rpx 0;
}

.selection-header {
  padding: 20rpx;
  text-align: center;
  font-size: 30rpx;
  color: #333;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin: 0 20rpx;
}

.selection-text {
  font-weight: 500;
}

/* 分类标签样式 */
.category-tabs-block {
  background-color: #fff;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.custom-category-tabs {
  padding: 10rpx 20rpx;
}

.tab-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding-bottom: 10rpx;
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  transition: all 0.3s;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-item.active {
  color: #fff;
  background-color: #ff6b6b;
}

/* 商品列表样式 - 网格布局 */
.goods-grid-section {
  background-color: #f5f5f5;
  /* padding: 16rpx; */
}

/* 商品列表样式 - 列表布局（模板1） */
.goods-list-section {
  background-color: #f5f5f5;
  padding: 16rpx;
}

.goods-list-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.goods-list-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #eeeeee;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 24rpx;
}

.goods-list-image {
  flex-shrink: 0;
  width: 220rpx;
  height: 220rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.goods-thumb {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-list-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 220rpx;
}

.goods-list-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.goods-list-price {
  margin-bottom: 12rpx;
}

.goods-list-price .price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #e21323;
}

.goods-list-price .unit-text {
  font-size: 24rpx;
  color: #666666;
  margin-left: 4rpx;
}

.goods-list-price .original-price {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 12rpx;
}

.goods-list-specs {
  margin-bottom: 16rpx;
}

.specs-tag {
  display: inline-block;
  background-color: #f5f5f5;
  color: #666666;
  font-size: 24rpx;
  padding: 6rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 8rpx;
}

.stock-info {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
}

.view-detail-link {
  font-size: 26rpx;
  color: #00ae66;
  margin-bottom: 16rpx;
  text-decoration: underline;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16rpx;
}

.quantity-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  filter: grayscale(1) brightness(0.7);
}

.quantity-btn.active {
  background-color: #ff6b35;
  filter: none;
}

.quantity-btn.disabled {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.quantity-btn.minus {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.quantity-btn.plus.active {
  background-color: #ff6b35;
  filter: none;
}

.quantity-btn:active {
  transform: scale(0.95);
}

/* 模板1：只有加号按钮的样式 */
.quantity-btn-add-only {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  filter: grayscale(1) brightness(0.7);
}

.quantity-btn-add-only.active {
  background-color: #ff6b35;
  filter: none;
}

.quantity-btn-add-only.disabled {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.quantity-btn-add-only:active {
  transform: scale(0.95);
}

/* 模板1：完整数量控制容器 */
.full-quantity-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* SVG图标通用样式 */
.btn-icon {
  width: 38rpx;
  height: 38rpx;
  transition: filter 0.2s ease;
}

/* SVG图标颜色状态 */
.quantity-btn.active .btn-icon,
.quantity-btn-add-only.active .btn-icon,
.cart-btn.active .btn-icon {
  filter: brightness(0) saturate(100%) invert(100%); /* 白色，在橙色背景上更清晰 */
  /* 如果想要橙红色图标，使用下面这行替换上面的 */
  /* filter: brightness(0) saturate(100%) invert(55%) sepia(93%) saturate(1352%) hue-rotate(4deg) brightness(108%) contrast(101%); */
}

.quantity-btn.disabled .btn-icon,
.quantity-btn-add-only.disabled .btn-icon,
.cart-btn.disabled .btn-icon {
  filter: brightness(0) saturate(100%) invert(40%); /* 深灰色 */
}

.quantity-btn.minus .btn-icon,
.cart-btn.minus .btn-icon {
  filter: brightness(0) saturate(100%) invert(40%); /* 深灰色 */
}

.quantity-num {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.quantity-input {
  min-width: 60rpx;
  height: 48rpx;
  text-align: center;
  font-size: 28rpx;
  border: 1rpx solid #eeeeee;
  border-radius: 4rpx;
  background-color: #ffffff;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
}

.goods-list-cols-2 {
  margin: 0 -8rpx;
}

.goods-grid-item {
  width: 50%; /* 确保一行两个 */
  box-sizing: border-box;
  padding: 8rpx;
  margin-bottom: 8rpx;
}

.goods-item {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #eeeeee;
  height: 100%;
  display: flex;
  flex-direction: column;
}



.goods-image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* 降低高度比例，使图片不那么高 */
  overflow: hidden;
}

.out-of-stock {
  background-color: #000;
  border-radius: 12rpx 12rpx 0 0;
}

.replenish-badge {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
}

.replenish-image {
  width: 120rpx;
}

.goods-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-name-container {
  padding: 10rpx 16rpx 6rpx;
}

.goods-name {
  font-size: 26rpx;
  line-height: 1.3;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 68rpx; /* 固定高度，避免不同长度的名称导致卡片高度不一 */
}

.goods-specs-container {
  padding: 0 16rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 36rpx;
}

.goods-specs-tag {
  font-size: 22rpx;
  background-color: #EDF1FC;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  margin-bottom: 6rpx;
  color: #666;
  display: inline-block;
}

.goods-stock-info {
  font-size: 22rpx;
  color: #999;
  margin-left: auto;
}

.goods-price-container {
  padding: 6rpx 16rpx;
}

.goods-price {
  color: #e21323;
}

.price-symbol {
  font-size: 22rpx;
}

.price-value {
  font-size: 32rpx;
  font-weight: 500;
}

.price-original {
  font-size: 22rpx;
  color: #999999;
  text-decoration: line-through;
  margin-left: 6rpx;
}

.goods-cart-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 6rpx 16rpx 16rpx;
  gap: 16rpx;
}

.cart-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  filter: grayscale(1) brightness(0.7);
}

.cart-btn.active {
  background-color: #ff6b35;
  filter: none;
}

.cart-btn.disabled {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.cart-btn.minus {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.cart-btn.plus.active {
  background-color: #ff6b35;
  filter: none;
}

.cart-btn:active {
  transform: scale(0.95);
}

/* 只有加号按钮的容器 */
.add-only-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cart-btn-add-only {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  filter: grayscale(1) brightness(0.7);
}

.cart-btn-add-only.active {
  background-color: #ff6b35;
  filter: none;
}

.cart-btn-add-only.disabled {
  background-color: #f5f5f5;
  filter: grayscale(1) brightness(0.7);
}

.cart-btn-add-only:active {
  transform: scale(0.95);
}

/* 完整数量控制容器 */
.full-cart-controls {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.cart-num {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.cart-num-input {
  min-width: 60rpx;
  height: 48rpx;
  text-align: center;
  font-size: 28rpx;
  border: 1rpx solid #eeeeee;
  border-radius: 4rpx;
  background-color: #ffffff;
}

/* 可点击数量样式 */
.cart-num-clickable,
.quantity-num-clickable {
  cursor: pointer;
  position: relative;
}

.cart-num-clickable::after,
.quantity-num-clickable::after {
  content: '';
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border-radius: 4rpx;
  background-color: transparent;
  transition: background-color 0.2s;
}

.cart-num-clickable:active::after,
.quantity-num-clickable:active::after {
  background-color: rgba(0, 174, 102, 0.1);
}

/* 数量输入弹框 */
.quantity-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.quantity-modal-content {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.quantity-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.quantity-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.quantity-modal-close {
  width: 32rpx;
  height: 32rpx;
}

.quantity-modal-body {
  padding: 32rpx;
}

.quantity-input-wrapper {
  margin-bottom: 24rpx;
}

.quantity-modal-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #eeeeee;
  border-radius: 8rpx;
  font-size: 32rpx;
  text-align: center;
  background-color: #ffffff;
}

.quantity-modal-input:focus {
  border-color: #00ae66;
}

.quantity-tips {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999999;
}

.quantity-modal-footer {
  display: flex;
  border-top: 1rpx solid #eeeeee;
}

.quantity-modal-cancel,
.quantity-modal-confirm {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  border: none;
  border-radius: 0;
  background-color: #ffffff;
}

.quantity-modal-cancel {
  color: #666666;
  border-right: 1rpx solid #eeeeee;
}

.quantity-modal-confirm {
  color: #00ae66;
  font-weight: 600;
}

.quantity-modal-cancel:active {
  background-color: #f5f5f5;
}

.quantity-modal-confirm:active {
  background-color: rgba(0, 174, 102, 0.1);
}

/* 兼容旧样式 */
.weui-media-box_appmsg {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.weui-media-box__hd {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}

.weui-media-box__thumb {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  object-fit: cover;
}

.weui-media-box__title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
}

.weui-media-box__desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

/* 数量选择器样式 */
.flex-row {
  display: flex;
  align-items: center;
}

/* Flex辅助类 */
.flex-grow-0 {
  flex-grow: 0;
  flex-shrink: 0;
}

.flex-grow-1 {
  flex-grow: 1;
  flex-shrink: 1;
}

.flex-y-center {
  display: flex;
  align-items: center;
}

.flex-x-center {
  display: flex;
  justify-content: center;
}

.images {
  width: 48rpx;
  height: 48rpx;
}

.row-data {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 文本溢出处理 */
.text-more-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 购物车容器样式 */
.cart-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 弹窗样式优化 */
.weui-dialog {
  border-radius: 16rpx;
  overflow: hidden;
  max-width: 80%;
  max-height: 80vh;
}

.weui-dialog__title {
  font-size: 32rpx;
  color: #333;
  padding: 24rpx 0;
}

.weui-dialog__bd {
  padding: 30rpx 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.weui-dialog__ft {
  border-top: 1rpx solid #f0f0f0;
}

.weui-dialog__btn {
  font-size: 30rpx;
}

.weui-dialog__btn_primary {
  color: #ff6b6b;
}

/* 验证弹窗样式 */
.verify-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-modal {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.verify-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #f9f9f9;
}

.verify-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.verify-modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-modal-content {
  padding: 30rpx 24rpx;
}

.verify-form-item {
  margin-bottom: 24rpx;
}

.verify-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.verify-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  transition: border-color 0.3s;
}

.verify-input:focus {
  border-color: #ff6b6b;
}

.verify-submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #ff6b6b;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
  transition: background-color 0.3s;
}

.verify-submit-btn:active {
  background-color: #ff5252;
}

/* 商品详情弹窗样式 */
.goods-detail-dialog {
  width: 90%;
  max-width: 650rpx;
}

.goods-detail-content {
  padding: 0 !important;
  position: relative;
}

.goods-detail-image {
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.detail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-detail-description {
  padding: 20rpx 24rpx;
  padding-bottom: 120rpx;
  text-align: left;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

.goods-detail-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.button-wrapper {
  padding: 0 10rpx;
  width: 40%;
}

.detail-btn {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
}

.detail-btn-default {
  background-color: #f5f5f5;
  color: #666;
}

.detail-btn-primary {
  background-color: #ff6b6b;
  color: #fff;
}

/* 绿色主题公告弹窗样式 */
.notice-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notice-container {
  width: 85%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.3s ease;
}

/* 绿色主题头部 */
.notice-header {
  background: linear-gradient(135deg, #00ae66 0%, #00c975 100%);
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.notice-header-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.notice-header-title {
  flex: 1;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
}

.notice-close-btn {
  color: #ffffff;
  font-size: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  transition: background-color 0.2s;
}

.notice-close-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

.notice-body {
  padding: 32rpx 32rpx 40rpx;
}

.notice-content-scroll {
  color: #333333;
  max-height: 400rpx;
  line-height: 1.6;
  font-size: 28rpx;
}

.notice-btn {
  width: 70%;
  height: 88rpx;
  background: linear-gradient(135deg, #00ae66 0%, #00c975 100%);
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 174, 102, 0.3);
  transition: all 0.2s;
}

.notice-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 174, 102, 0.3);
}

.hidden {
  display: none;
}

/* 动画效果 */
.fadeIn {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
