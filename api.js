var api = {
  member: {
    info: "passport/get_member_info",
    pay_qrcode: "member/pay_qrcode",
  },
  common: {
    area: "common/area",
    get_copyright: "common/get_copyright",
  },
  banner: {
    list: "banner/list",
  },
  topic: {
    list: "topic/list",
    detail: "topic/detail",
  },
  industry: {
    list: "industry/list",
    detail: "industry/detail",
  },
  brand: {
    list: "brand/list",
    detail: "brand/detail",
  },
  store: {
    list: "store/list",
    detail: "store/detail",
  },
  code: {
    active: "code/active",
    share_qrcode: "code/share_qrcode",
    bind_code: "code/bind_code",
    bind_code_by_mobile: "code/bind_code_by_mobile",
    config: "code/get_config",
    get_choose_goods_config: "code/get_choose_goods_config",
    code_list: "code/code_list",
    code_detail: "code/code_detail",
    code_note_detail: "code/code_note_detail",
    get_share_note_guid: "code/get_share_note_guid",
    receive_share_code: "code/receive_share_code",
    online_free_receive_code: "code/online_free_receive_code",
    get_verify_qrcode: "code/get_verify_qrcode",
    get_share_note_info: "code/get_share_note_info",
    member_code_list: "code/member_code_list",
    submit_buy_code_order: "code/submit_buy_code_order",
    get_disable_date_list: "code/get_disable_date_list",
    modify_order: "code/modify_order",
    submit_preview: "code/submit_preview",
    get_submit_order_config: "code/get_submit_order_config",
    get_after_submit_order_config: "code/get_after_submit_order_config",
    verify_code: "code/verify_code",
    verify_coupon_send_note_guid: "code/verify_coupon_send_note_guid",
    get_goods_list_by_token: "code/get_goods_list_by_token",
    submit_order: "code/submit_order",
    code_to_member_money: "code/code_to_member_money",
    parse_qrcode: "code/parse_qrcode",
    get_code_info: "code/get_code_info",
    get_goods_list_by_coupon_guid: "code/get_goods_list_by_coupon_guid",
  },
  pay: {
    apply: "pay/apply",
    query: "pay/query",
  },
  order: {
    detail: "code/order_detail",
    list: "code/order_list",
  },
  file: {
    upload: "file/upload",
  },
  config: {
    get: "config/get",
  },
  login: {
    get_access_token: "login/get_access_token",
  },
  passport: {
    login: "passport/login",
    get_user_info: "passport/get_user_info",
    get_phone_number: "passport/get_phone_number",
    on_login: "passport/on_login",
    get_config: "passport/get_config",
  },
  address: {
    list: "address/list",
    detail: "address/detail",
    add: "address/add",
    edit: "address/edit",
    del: "address/del",
    set_default: "address/set_default",
  },
  user: {
    address_detail: "address/get",
    address_save: "address/auto_save",
    address_set_setting: "user/address_set_setting",
    address_delete: "address/del",
    save_form_id: "user/save_form_id",
    favorite_add: "favorites/add",
    favorite_remove: "favorites/remove",
    favorite_list: "favorites/list",
    index: "user/index",
    get_area_id_from_wechat: "address/get_area_id_from_wechat",
    add_wechat_address: "address/add_wechat_address",
    topic_favorite: "user/topic_favorite",
    topic_favorite_list: "user/topic_favorite_list",
    member: "user/member",
    card: "user/card",
    card_qrcode: "user/card_qrcode",
    card_clerk: "user/card_clerk",
    sms_setting: "user/sms_setting",
    send_sms_code: "user/send_sms_code",
    edit: "member/edit",
    index: "user/index",
    info: "member/info",
    get_weapp_toolbar: "user/get_weapp_toolbar",
    update_mobile: "user/update_mobile",
  },
  home: {
    store: "home/store",
    index: "home/index",
    goods_list: "goods/goods_list",
    cat_list: "goods_category/list",
    goods: "goods/detail",
    district: "common/district",
    upload_image: "home/upload_image",
    comment_list: "home/comment_list",
    article_list: "home/article_list",
    article_detail: "article/detail",
    video_list: "home/video_list",
    goods_qrcode: "goods/goods_qrcode",
    coupon_list: "home/coupon_list",
    topic_list: "home/topic_list",
    topic: "home/topic",
    navbar: "home/navbar",
    navigation_bar_color: "home/navigation_bar_color",
    shop_list: "home/shop_list",
    shop_detail: "home/shop_detail",
  },
  cart: {
    list: "cart/get",
    add_cart: "cart/update",
    update: "cart/update",
    delete: "cart/remove",
  },
  live: {
    list: "live/list",
  },
  goods: {
    goods_attr_info: "goods/goods_attr_info",
  },
  order: {
    submit_preview: "goods_order/submit_preview",
    submit: "goods_order/submit",
    pay_data: "goods_order/pay",
    list: "goods_order/list",
    get_pickup_qrcode_url: "goods_order/get_pickup_qrcode_url",
    revoke: "goods_order/close",
    confirm: "goods_order/confirm",
    count_data: "goods_order/count_data",
    detail: "goods_order/detail",
    refund_preview: "goods_order/refund_preview",
    refund: "goods_order/refund",
    apply_refund: "goods_order/apply_refund",
    refund_detail: "goods_order/refund_detail",
    comment_preview: "goods_order/comment_preview",
    comment: "goods_order/comment",
    express_detail: "goods_order/express_detail",
    clerk: "order/clerk",
    clerk_detail: "goods_order/clerk_detail",
    get_qrcode: "goods_order/get_qrcode",
    location: "goods_order/location",
  },
  share: {
    join: "share/join",
    check: "share/check",
    get_info: "share/get_info",
    get_cash_info: "share/get_cash_info",
    apply: "share/apply",
    get_cash_note: "share/get_cash_note",
    get_qrcode: "share/get_qrcode",
    shop_share: "share/shop_share",
    bind_parent: "share/bind_parent",
    get_team: "share/get_team",
    get_order: "share/get_order",
    get_share_setting: "share/get_share_setting",
  },
  coupon: {
    index: "coupon/index",
    share_send: "coupon/share_send",
    receive: "coupon/receive",
  },
  seckill: {
    list: "seckill/list",
    goods_list: "seckill/goods_list",
  },
  group: {
    index: "group/index/index",
    list: "group/index/good_list",
    details: "group/index/good_details",
    goods_attr_info: "group/index/goods_attr_info",
    submit_preview: "group/order/submit_preview",
    submit: "group/order/submit",
    pay_data: "group/order/pay_data",
    order: {
      list: "group/order/list",
      detail: "group/order/detail",
      express_detail: "group/order/express_detail",
      comment_preview: "group/order/comment_preview",
      comment: "group/order/comment",
      confirm: "group/order/confirm",
      goods_qrcode: "group/order/goods_qrcode",
      get_qrcode: "group/order/get_qrcode",
      clerk: "group/order/clerk",
      clerk_order_details: "group/order/clerk_order_details",
    },
    group_info: "group/order/group",
    comment: "group/index/goods_comment",
    goods_qrcode: "group/index/goods_qrcode",
  },
  book: {
    index: "book/index/index",
    list: "book/index/good_list",
    details: "book/index/good_details",
    submit_preview: "book/order/submit_preview",
    submit: "book/order/submit",
    order_list: "book/order/list",
    order_cancel: "book/order/cancel",
    order_pay: "book/order/pay_data",
    order_details: "book/order/order_details",
    shop_list: "book/index/shop_list",
    get_qrcode: "book/order/get_qrcode",
    clerk: "book/order/clerk",
    apply_refund: "book/order/apply_refund",
    comment_preview: "book/order/comment_preview",
    submit_comment: "book/order/comment",
    goods_comment: "book/index/goods_comment",
    goods_qrcode: "book/index/goods_qrcode",
    clerk_order_details: "book/order/clerk_order_details",
  },
  quick: {
    quick: "goods/quick",
  },
  member_money_note: {
    index: "member_money_note/index",
    detail: "member_money_note/detail",
  },
  member_point_note: {
    index: "member_point_note/index",
    detail: "member_point_note/detail",
  },
  recharge: {
    list: "recharge/list",
    submit: "recharge/submit",
  },
  gift: {
    info: "coupon_gift_order_note/info",
    get_pending_gifts: "coupon_gift_order_note/get_pending_gifts",
    claim_gift: "coupon_gift_order_note/claim_gift",
  },
};
module.exports = api;
