# GEMINI.md

## Project Overview

This is a PHP project built on the ThinkPHP 8 framework. It serves as a multi-functional backend system with features like user management, order processing, and various integrations with third-party services. The project is structured in a modular way, with a clear separation of concerns between controllers, models, and services.

**Key Technologies:**

*   **Framework:** ThinkPHP 8
*   **Language:** PHP >= 7.4
*   **Database:** MySQL (inferred from the use of a PHP ORM)
*   **Real-time Communication:** Workerman (for WebSockets)
*   **Image Processing:** Intervention/Image
*   **Task Queues:** think-queue
*   **APIs & SDKs:** WeChat, Alibaba Cloud, Baidu, and more.

**Architecture:**

The application follows a typical MVC (Model-View-Controller) pattern.

*   **`app/controller`:** Contains the controller classes that handle incoming HTTP requests.
*   **`app/model`:**  Contains the database models.
*   **`app/common`:**  Contains common services and helper functions.
*   **`route`:** Defines the application's URL routes.
*   **`config`:** Holds the application's configuration files.
*   **`public`:** The web server's document root.

## Building and Running

1.  **Install Dependencies:**
    ```bash
    composer install
    ```

2.  **Run the Application:**
    *   **Development Server:**
        ```bash
        php think run
        ```
    *   **Production:** Configure a web server (like Nginx or Apache) to point to the `public` directory.

3.  **Running Tests:**
    *   TODO: The testing setup is not immediately clear from the file analysis.

## Development Conventions

This project has a very specific and strict set of development conventions, documented in the `.augment/rules` directory. The key principles are:

*   **AI-Driven Development:** The rules are designed to be used by an AI assistant, with a strong emphasis on automation and consistency.
*   **Step-by-Step, Confirmation-Driven Workflow:** When developing new features, especially for single-table admin modules, a strict, sequential process must be followed, with user confirmation required at each step.
*   **Minimalism:** Only implement features that are explicitly requested by the user.
*   **Convention over Configuration:** The project relies heavily on conventions for things like controller/validator mapping and API routing.
*   **Multi-Tenancy:** All database queries must be scoped to the current business ID (`bid`).

**Key Development Guidelines:**

*   **`develop.md`:** Outlines the development process and workflow.
*   **`augment-rule.md`:** Provides detailed technical specifications for implementation.
*   **`single-table-admin-development.md`:**  A specific guide for creating new single-table admin modules.

**Coding Style:** The code generally follows PSR standards.

**Controllers:** Controllers inherit from `app\BaseController`, which provides common functionality like request parameter handling and validation.

**Helper Functions:** A rich set of helper functions is available in `app/common.php`, covering everything from sending notifications to interacting with various services.

**Routing:** The application uses a combination of explicit and automatic routing. The automatic routing feature maps URLs like `admin_api/v1/controller/action` to the corresponding controller and method.

**Configuration:** Configuration is managed through files in the `config` directory. The `get_config_by_bid` function provides a way to fetch configuration settings based on a business ID (`bid`).