/**app.wxss**/
@import "/style/app.wxss";
@import '/style/weui.wxss';
@import '/common/tabbar/tabbar.wxss';

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

page {
  height: 100%;
  font-size: 11pt;
  color: #555;
  background-color: #f0f2f5;
  overflow-x: hidden;

  font-size: 16px;
  font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}

.page__hd {
  padding: 40px;
}

.page__bd {
  padding-bottom: 40px;
}

.page__bd_spacing {
  padding-left: 15px;
  padding-right: 15px;
}

.page__ft {
  text-align: center;
  padding: 0 0 10px;
  padding: 0 0 calc(10px + constant(safe-area-inset-bottom));
  padding: 0 0 calc(10px + env(safe-area-inset-bottom));
}

.page__title {
  text-align: left;
  font-size: 20px;
  font-weight: 400;
}

.page__desc {
  margin-top: 5px;
  color: #888;
  text-align: left;
  font-size: 14px;
}

.weui-cell_example:before {
  left: 52px;
}

.rich_img {
  max-width: 100% !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block;
}