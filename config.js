var config = {
  path: '/member_api/v1/',
  tabbar: {
    'color': '#a9b7b7',
    'selectedColor': '#FF4544',
    'borderStyle': 'white',
    'position': 'bottom',
    'list': [{
        'pagePath': '/pages/index/index',
        'selectedIconPath': '/images/appnavbar/nav-icon-index.active.png',
        'iconPath': '/images/appnavbar/nav-icon-index.png',
        'text': '首页',
        //'selected': true
      },
      //  {
      //   'pagePath': '/pages/index/index',
      //   'selectedIconPath': '/images/appnavbar/nav-icon-cart.active.png',
      //   'iconPath': '/images/appnavbar/nav-icon-cart.png',
      //   'text': '商城',
      //   //'selected': true
      // },
      // {
      //   'pagePath': '/pages/order/list',
      //   'selectedIconPath': '/images/appnavbar/nav-icon-cat.active.png',
      //   'iconPath': '/images/appnavbar/nav-icon-cat.png',
      //   'text': '订单',
      //   //'selected': false
      // },
      // {
      //   'pagePath': '/pages/pay/pay',
      //   'selectedIconPath': '/images/appnavbar/nav-icon-cat.active.png',
      //   'iconPath': '/images/appnavbar/nav-icon-cat.png',
      //   'text': '支付',
      //   //'selected': false
      // },
      {
        'pagePath': '/pages/user/user',
        'selectedIconPath': '/images/appnavbar/nav-icon-user.active.png',
        'iconPath': '/images/appnavbar/nav-icon-user.png',
        'text': '我的',
        //'selected': false
      }
    ]
  }
};
module.exports = config;